FROM python:3.11-slim

ENV PYTHONUNBUFFERED=1
ENV ANUBIS_ENV=production

RUN groupadd -r anubis && useradd -r -g anubis anubis

WORKDIR /app
RUN mkdir -p /app/data /app/configs /app/logs
RUN chown -R anubis:anubis /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .
RUN chown -R anubis:anubis /app

USER anubis
EXPOSE 8000
VOLUME ["/app/data", "/app/configs", "/app/logs"]

CMD ["python", "-m", "anubis_main_system.core.anubis.main"]
