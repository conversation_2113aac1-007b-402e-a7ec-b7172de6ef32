{"default_provider": "ollama", "providers": {"ollama": {"enabled": true, "model": "llama3.2", "host": "localhost", "port": 11434, "security": {"api_key_env_var": "OLLAMA_API_KEY", "encrypted_storage": true, "rate_limiting": true}}, "gemini": {"enabled": false, "model": "gemini-pro", "api_key": "", "security": {"api_key_env_var": "GEMINI_API_KEY", "encrypted_storage": true, "rate_limiting": true}, "api_key_env": "GEMINI_API_KEY"}, "openai": {"enabled": false, "model": "gpt-3.5-turbo", "api_key": "", "security": {"api_key_env_var": "OPENAI_API_KEY", "encrypted_storage": true, "rate_limiting": true}, "api_key_env": "OPENAI_API_KEY"}}, "security": {"api_keys_encrypted": true, "environment_variables_only": true, "rotation_enabled": true, "access_logging": true}, "isolation": {"container_secrets": true, "network_policies": true, "resource_limits": {"cpu": "500m", "memory": "1Gi"}}}