{"cleanup_date": "2025-07-20T14:03:00.642019", "total_actions": 216, "actions": [{"timestamp": "2025-07-20T14:02:58.416249", "action": "بدء تنظيف", "details": "مل<PERSON><PERSON><PERSON> Docker المكررة"}, {"timestamp": "2025-07-20T14:02:58.416600", "action": "بدء تنظيف", "details": "ملفات التحليل القديمة"}, {"timestamp": "2025-07-20T14:02:58.422756", "action": "أرشفة ملف تحليل قديم", "details": "anubis_services_catalog_20250719_180343.json -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\old_analysis\\anubis_services_catalog_20250719_180343.json"}, {"timestamp": "2025-07-20T14:02:58.424044", "action": "أرشفة ملف تحليل قديم", "details": "workflow_anubis_task_20250720_101017.json -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\old_analysis\\workflow_anubis_task_20250720_101017.json"}, {"timestamp": "2025-07-20T14:02:58.424170", "action": "بدء تنظيف", "details": "ملفات Python في الجذر"}, {"timestamp": "2025-07-20T14:02:58.427606", "action": "أرشفة ملف Python", "details": "anubis_agents_cline_analyzer.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_agents_cline_analyzer.py"}, {"timestamp": "2025-07-20T14:02:58.428878", "action": "أرشفة ملف Python", "details": "anubis_ai_collaboration_helper.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_ai_collaboration_helper.py"}, {"timestamp": "2025-07-20T14:02:58.430498", "action": "أرشفة ملف Python", "details": "anubis_ai_team_collaboration_system.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_ai_team_collaboration_system.py"}, {"timestamp": "2025-07-20T14:02:58.432034", "action": "أرشفة ملف Python", "details": "anubis_api_comprehensive_test.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_api_comprehensive_test.py"}, {"timestamp": "2025-07-20T14:02:58.433703", "action": "أرشفة ملف Python", "details": "anubis_cline_conversation_analyzer.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_cline_conversation_analyzer.py"}, {"timestamp": "2025-07-20T14:02:58.435439", "action": "أرشفة ملف Python", "details": "anubis_complete_system_test.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_complete_system_test.py"}, {"timestamp": "2025-07-20T14:02:58.436705", "action": "أرشفة ملف Python", "details": "anubis_comprehensive_organizer.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_comprehensive_organizer.py"}, {"timestamp": "2025-07-20T14:02:58.437971", "action": "أرشفة ملف Python", "details": "anubis_comprehensive_system_tester.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_comprehensive_system_tester.py"}, {"timestamp": "2025-07-20T14:02:58.439317", "action": "أرشفة ملف Python", "details": "anubis_docker_isolation_launcher.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_docker_isolation_launcher.py"}, {"timestamp": "2025-07-20T14:02:58.440601", "action": "أرشفة ملف Python", "details": "anubis_docker_isolation_system.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_docker_isolation_system.py"}, {"timestamp": "2025-07-20T14:02:58.442559", "action": "أرشفة ملف Python", "details": "anubis_gemini_assistant_request.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_gemini_assistant_request.py"}, {"timestamp": "2025-07-20T14:02:58.466780", "action": "أرشفة ملف Python", "details": "anubis_gemini_cli_helper.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_gemini_cli_helper.py"}, {"timestamp": "2025-07-20T14:02:58.468655", "action": "أرشفة ملف Python", "details": "anubis_isolation_status_checker.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_isolation_status_checker.py"}, {"timestamp": "2025-07-20T14:02:58.470120", "action": "أرشفة ملف Python", "details": "anubis_isolation_system_manager.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_isolation_system_manager.py"}, {"timestamp": "2025-07-20T14:02:58.471471", "action": "أرشفة ملف Python", "details": "anubis_n8n_monitor.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_n8n_monitor.py"}, {"timestamp": "2025-07-20T14:02:58.472857", "action": "أرشفة ملف Python", "details": "anubis_n8n_quick_start.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_n8n_quick_start.py"}, {"timestamp": "2025-07-20T14:02:58.474036", "action": "أرشفة ملف Python", "details": "anubis_project_analyzer.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_project_analyzer.py"}, {"timestamp": "2025-07-20T14:02:58.475458", "action": "أرشفة ملف Python", "details": "anubis_project_cleaner.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_project_cleaner.py"}, {"timestamp": "2025-07-20T14:02:58.477221", "action": "أرشفة ملف Python", "details": "anubis_quick_docker_diagnosis.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_quick_docker_diagnosis.py"}, {"timestamp": "2025-07-20T14:02:58.478473", "action": "أرشفة ملف Python", "details": "anubis_services_comprehensive_guide.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_services_comprehensive_guide.py"}, {"timestamp": "2025-07-20T14:02:58.479717", "action": "أرشفة ملف Python", "details": "anubis_simple_system_tester.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\anubis_simple_system_tester.py"}, {"timestamp": "2025-07-20T14:02:58.481007", "action": "أرشفة ملف Python", "details": "final_anubis_organizer_with_gemini.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\final_anubis_organizer_with_gemini.py"}, {"timestamp": "2025-07-20T14:02:58.482750", "action": "أرشفة ملف Python", "details": "organize_ai_team.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\organize_ai_team.py"}, {"timestamp": "2025-07-20T14:02:58.484290", "action": "أرشفة ملف Python", "details": "quick_access_shortcuts.py -> C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\cleanup_archive\\root_python_files\\quick_access_shortcuts.py"}, {"timestamp": "2025-07-20T14:02:58.484523", "action": "بدء تنظيف", "details": "ملفات __pycache__"}, {"timestamp": "2025-07-20T14:02:58.768800", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": "__pycache__"}, {"timestamp": "2025-07-20T14:02:58.769903", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": "tests\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.771104", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": "src\\cli\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.772174", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": "src\\core\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.773589", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": "src\\core\\core\\anubis\\database\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.775230", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Scripts\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.778006", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.782692", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\adodbapi\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.786584", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\aiofiles\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.788208", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\annotated_types\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.797375", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\anyio\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.799988", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\certifi\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.805879", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\charset_normalizer\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.813659", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\click\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.816574", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\colorama\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.822158", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\dotenv\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.831137", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\fastapi\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.832205", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\greenlet\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.836582", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\h11\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.840305", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpcore\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.848097", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpx\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.851809", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\idna\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.854228", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\iniconfig\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.856790", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\isapi\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.869726", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\jinja2\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.871119", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\markupsafe\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.873993", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\multipart\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.875890", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.884180", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\packaging\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.885592", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.891085", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pluggy\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.894434", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\psutil\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.911010", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.915371", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic_core\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.927738", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pygments\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.930168", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pytest\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.935092", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\python_multipart\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.945589", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\requests\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.947711", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sniffio\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.952510", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.966019", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\starlette\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.968646", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\typing_inspection\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.975992", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\urllib3\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.984878", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\__pycache__"}, {"timestamp": "2025-07-20T14:02:58.995772", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\yaml\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.027204", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.028755", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_yaml\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.040026", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\assertion\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.045702", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\config\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.048476", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\mark\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.056977", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\_code\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.063086", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\_io\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.067148", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\_pytest\\_py\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.070273", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\win32\\lib\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.073202", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\lifespan\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.076661", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\loops\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.082988", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\middleware\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.087785", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\protocols\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.090560", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\supervisors\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.095353", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.101264", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.104066", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\urllib3\\contrib\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.106520", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\urllib3\\http2\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.114603", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\urllib3\\util\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.117883", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.122809", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\starlette\\middleware\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.125730", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.127333", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.129142", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.138339", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\engine\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.149132", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\event\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.157501", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\ext\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.161042", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\future\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.182746", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\orm\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.186029", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\pool\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.206610", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\sql\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.217095", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\testing\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.224788", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\util\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.228236", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\testing\\fixtures\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.231248", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\testing\\plugin\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.238464", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.245796", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.247552", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\ext\\declarative\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.252876", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\ext\\mypy\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.257373", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.268906", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.273809", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.286956", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.293159", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.294967", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\sniffio\\_tests\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.302215", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.306729", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos\\app\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.308379", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos\\ocx\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.310107", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pygments\\filters\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.320529", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pygments\\formatters\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.498772", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pygments\\lexers\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.532373", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pygments\\styles\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.543163", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\deprecated\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.548798", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\experimental\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.553051", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\plugin\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.577795", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\v1\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.616077", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pydantic\\_internal\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.629811", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.633775", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.649003", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.657825", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.673684", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.683723", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.693713", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.698441", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.719179", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.723825", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.741975", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.746077", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.755518", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.760589", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.869668", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.876236", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.885805", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.898903", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.910094", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.929131", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.950440", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.973880", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.983092", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.987901", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__pycache__"}, {"timestamp": "2025-07-20T14:02:59.990592", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.022498", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.059140", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.064051", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.070744", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.078939", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.093006", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.109647", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.118309", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.130383", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.134342", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.140356", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.142991", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.161185", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.166376", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.174628", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.183136", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.187480", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.190941", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.195486", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\packaging\\licenses\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.215509", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.222882", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\aio\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.230622", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\django\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.233430", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\locales\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.239405", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\opentelemetry\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.253982", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\plugins\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.256867", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\locales\\eng\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.267242", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\mysql\\connector\\aio\\plugins\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.273055", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\isapi\\samples\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.275049", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\isapi\\test\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.279588", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpx\\_transports\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.287320", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpcore\\_async\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.294290", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpcore\\_backends\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.300054", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\httpcore\\_sync\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.302181", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\greenlet\\platform\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.314968", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\greenlet\\tests\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.318051", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\fastapi\\dependencies\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.322628", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\fastapi\\middleware\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.326363", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\fastapi\\openapi\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.331171", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\fastapi\\security\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.336079", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\colorama\\tests\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.337896", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\charset_normalizer\\cli\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.349565", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\anyio\\abc\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.357127", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\anyio\\streams\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.361416", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\anyio\\_backends\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.377449", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\anyio\\_core\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.379313", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\aiofiles\\tempfile\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.381866", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\aiofiles\\threadpool\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.384811", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\adodbapi\\examples\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.388727", "action": "<PERSON><PERSON><PERSON> مج<PERSON> cache", "details": ".venv\\Lib\\site-packages\\adodbapi\\test\\__pycache__"}, {"timestamp": "2025-07-20T14:03:00.389028", "action": "بدء تنظيف", "details": "المجلدات الفارغة"}, {"timestamp": "2025-07-20T14:03:00.637603", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "isolation"}, {"timestamp": "2025-07-20T14:03:00.638169", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "config\\security\\networks"}, {"timestamp": "2025-07-20T14:03:00.638560", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "config\\security\\scripts"}, {"timestamp": "2025-07-20T14:03:00.639067", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "reports_and_analysis\\analysis_data"}, {"timestamp": "2025-07-20T14:03:00.639524", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "reports_and_analysis\\logs"}, {"timestamp": "2025-07-20T14:03:00.639933", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "reports_and_analysis\\scan_reports"}, {"timestamp": "2025-07-20T14:03:00.640367", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "reports_and_analysis\\test_reports"}, {"timestamp": "2025-07-20T14:03:00.640845", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "scripts\\analyzers"}, {"timestamp": "2025-07-20T14:03:00.641690", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "src\\security\\systems\\docs"}], "summary": {"archived_files": 26, "deleted_dirs": 185, "errors": 0}}