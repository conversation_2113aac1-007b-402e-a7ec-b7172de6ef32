#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 نظام استدعاء نماذج الذكاء الاصطناعي
AI Models Caller System

نظام متقدم لاستدعاء نماذج الذكاء الاصطناعي من جميع المنصات المكتشفة
Advanced system for calling AI models from all discovered platforms
"""

import os
import json
import asyncio
import aiohttp
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_models_caller.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AIModelsCaller:
    """🤖 نظام استدعاء نماذج الذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.vault_dir = Path(__file__).parent / "vault"
        self.models_dir = self.vault_dir / "ai_models"
        self.models_dir.mkdir(exist_ok=True)
        
        self.available_keys = {}
        self.supported_platforms = {
            "google_gemini": {
                "name": "Google Gemini",
                "models": ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro"],
                "api_url": "https://generativelanguage.googleapis.com/v1/models",
                "key_format": "AIza"
            },
            "openrouter": {
                "name": "OpenRouter",
                "models": ["gpt-4", "claude-3", "llama-2", "mistral-7b"],
                "api_url": "https://openrouter.ai/api/v1/chat/completions",
                "key_format": "sk-or-v1-"
            },
            "anthropic": {
                "name": "Anthropic Claude",
                "models": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
                "api_url": "https://api.anthropic.com/v1/messages",
                "key_format": "sk-ant-"
            },
            "deepseek": {
                "name": "DeepSeek",
                "models": ["deepseek-chat", "deepseek-coder", "deepseek-math"],
                "api_url": "https://api.deepseek.com/v1/chat/completions",
                "key_format": "sk-"
            },
            "mistral": {
                "name": "Mistral AI",
                "models": ["mistral-tiny", "mistral-small", "mistral-medium", "mistral-large"],
                "api_url": "https://api.mistral.ai/v1/chat/completions",
                "key_format": "xr"
            },
            "huggingface": {
                "name": "Hugging Face",
                "models": ["gpt2", "bert-base", "t5-base", "llama-7b"],
                "api_url": "https://api-inference.huggingface.co/models",
                "key_format": "hf_"
            }
        }
        
        logger.info("🤖 تم تهيئة نظام استدعاء نماذج الذكاء الاصطناعي")
    
    def load_encrypted_keys(self) -> bool:
        """تحميل المفاتيح المشفرة"""
        try:
            # البحث عن أحدث ملف مفاتيح مشفرة
            secure_dir = self.vault_dir / "secure"
            encrypted_files = list(secure_dir.glob("encrypted_keys_*.json"))
            
            if not encrypted_files:
                logger.error("❌ لم يتم العثور على ملفات المفاتيح المشفرة")
                return False
            
            latest_file = max(encrypted_files, key=os.path.getctime)
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                encrypted_data = json.load(f)
            
            # استخراج المفاتيح المتاحة (بدون فك التشفير للعرض)
            for platform, platform_data in encrypted_data.get("encrypted_keys", {}).items():
                if platform in self.supported_platforms:
                    self.available_keys[platform] = {
                        "count": len(platform_data.get("keys", [])),
                        "platform_name": platform_data.get("platform", ""),
                        "keys": platform_data.get("keys", [])
                    }
            
            logger.info(f"✅ تم تحميل مفاتيح من {len(self.available_keys)} منصة")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المفاتيح: {e}")
            return False
    
    def get_available_models(self) -> dict:
        """الحصول على النماذج المتاحة"""
        available_models = {}
        
        for platform, keys_info in self.available_keys.items():
            if platform in self.supported_platforms:
                platform_info = self.supported_platforms[platform]
                available_models[platform] = {
                    "platform_name": platform_info["name"],
                    "models": platform_info["models"],
                    "keys_count": keys_info["count"],
                    "status": "available" if keys_info["count"] > 0 else "no_keys"
                }
        
        return available_models
    
    async def call_google_gemini(self, prompt: str, model: str = "gemini-pro") -> dict:
        """استدعاء Google Gemini"""
        try:
            # محاكاة استدعاء (في التطبيق الحقيقي سيستخدم المفتاح المفكوك)
            response = {
                "platform": "Google Gemini",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🤖 استجابة محاكية من {model}: تم معالجة طلبك بنجاح. هذا مثال على الاستجابة من Google Gemini.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 20,
                "cost_estimate": "$0.002"
            }
            
            logger.info(f"✅ تم استدعاء Google Gemini بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Google Gemini: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_openrouter(self, prompt: str, model: str = "gpt-4") -> dict:
        """استدعاء OpenRouter"""
        try:
            response = {
                "platform": "OpenRouter",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🚀 استجابة محاكية من {model} عبر OpenRouter: تم تحليل طلبك وإنتاج استجابة ذكية ومفيدة.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 25,
                "cost_estimate": "$0.005"
            }
            
            logger.info(f"✅ تم استدعاء OpenRouter بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء OpenRouter: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_anthropic_claude(self, prompt: str, model: str = "claude-3-sonnet") -> dict:
        """استدعاء Anthropic Claude"""
        try:
            response = {
                "platform": "Anthropic Claude",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🧠 استجابة محاكية من {model}: أنا Claude، مساعد ذكي من Anthropic. تم فهم طلبك وإنتاج استجابة مدروسة.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 30,
                "cost_estimate": "$0.008"
            }
            
            logger.info(f"✅ تم استدعاء Anthropic Claude بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Anthropic Claude: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_deepseek(self, prompt: str, model: str = "deepseek-chat") -> dict:
        """استدعاء DeepSeek"""
        try:
            response = {
                "platform": "DeepSeek",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🔍 استجابة محاكية من {model}: DeepSeek يقدم تحليلاً عميقاً ومفصلاً لطلبك مع حلول مبتكرة.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 22,
                "cost_estimate": "$0.003"
            }
            
            logger.info(f"✅ تم استدعاء DeepSeek بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء DeepSeek: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_mistral(self, prompt: str, model: str = "mistral-large") -> dict:
        """استدعاء Mistral AI"""
        try:
            response = {
                "platform": "Mistral AI",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🌪️ استجابة محاكية من {model}: Mistral AI يوفر استجابات سريعة وذكية مع فهم عميق للسياق.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 18,
                "cost_estimate": "$0.004"
            }
            
            logger.info(f"✅ تم استدعاء Mistral AI بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Mistral AI: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_huggingface(self, prompt: str, model: str = "gpt2") -> dict:
        """استدعاء Hugging Face"""
        try:
            response = {
                "platform": "Hugging Face",
                "model": model,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "response": f"🤗 استجابة محاكية من {model}: Hugging Face يقدم مجموعة واسعة من النماذج مفتوحة المصدر.",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "tokens_used": len(prompt.split()) + 15,
                "cost_estimate": "$0.001"
            }
            
            logger.info(f"✅ تم استدعاء Hugging Face بنجاح")
            return response
            
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Hugging Face: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_multiple_models(self, prompt: str, platforms: List[str] = None) -> dict:
        """استدعاء عدة نماذج في نفس الوقت"""
        if not platforms:
            platforms = list(self.available_keys.keys())
        
        results = {
            "prompt": prompt,
            "timestamp": datetime.now().isoformat(),
            "platforms_called": len(platforms),
            "responses": {}
        }
        
        # استدعاء النماذج بشكل متوازي
        tasks = []
        for platform in platforms:
            if platform in self.available_keys and self.available_keys[platform]["count"] > 0:
                if platform == "google_gemini":
                    tasks.append(self.call_google_gemini(prompt))
                elif platform == "openrouter":
                    tasks.append(self.call_openrouter(prompt))
                elif platform == "anthropic":
                    tasks.append(self.call_anthropic_claude(prompt))
                elif platform == "deepseek":
                    tasks.append(self.call_deepseek(prompt))
                elif platform == "mistral":
                    tasks.append(self.call_mistral(prompt))
                elif platform == "huggingface":
                    tasks.append(self.call_huggingface(prompt))
        
        # تنفيذ جميع المهام
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # تنظيم النتائج
        for i, response in enumerate(responses):
            if isinstance(response, dict) and "platform" in response:
                platform_name = response["platform"]
                results["responses"][platform_name] = response
        
        return results
    
    def create_models_interface(self) -> str:
        """إنشاء واجهة استدعاء النماذج"""
        interface_html = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 واجهة استدعاء نماذج الذكاء الاصطناعي</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .model-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .model-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        
        .model-card.available {
            border-left: 5px solid #27ae60;
        }
        
        .model-card.unavailable {
            border-left: 5px solid #e74c3c;
            opacity: 0.7;
        }
        
        .prompt-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .prompt-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            font-family: inherit;
        }
        
        .prompt-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .buttons-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .btn-primary { background: linear-gradient(135deg, #667eea, #764ba2); }
        .btn-success { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .btn-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .btn-info { background: linear-gradient(135deg, #3498db, #2980b9); }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .results-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .response-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-available { background: #27ae60; }
        .status-unavailable { background: #e74c3c; }
        
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 واجهة استدعاء نماذج الذكاء الاصطناعي</h1>
            <p>استدعِ نماذج الذكاء الاصطناعي من جميع المنصات المتاحة</p>
        </div>
        
        <div class="models-grid">
            <div class="model-card available">
                <h3>🤖 Google Gemini <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> gemini-pro, gemini-pro-vision</p>
                <p><strong>المفاتيح المتاحة:</strong> 4 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.002 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🚀 OpenRouter <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> GPT-4, Claude-3, Llama-2</p>
                <p><strong>المفاتيح المتاحة:</strong> 4 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.005 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🧠 Anthropic Claude <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> Claude-3-Opus, Sonnet, Haiku</p>
                <p><strong>المفاتيح المتاحة:</strong> 1 مفتاح</p>
                <p><strong>التكلفة:</strong> ~$0.008 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🔍 DeepSeek <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> deepseek-chat, deepseek-coder</p>
                <p><strong>المفاتيح المتاحة:</strong> 3 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.003 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🌪️ Mistral AI <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> mistral-large, mistral-medium</p>
                <p><strong>المفاتيح المتاحة:</strong> 1 مفتاح</p>
                <p><strong>التكلفة:</strong> ~$0.004 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🤗 Hugging Face <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> GPT-2, BERT, T5, Llama</p>
                <p><strong>المفاتيح المتاحة:</strong> 5 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.001 لكل استعلام</p>
            </div>
        </div>
        
        <div class="prompt-section">
            <h3>💬 اكتب طلبك هنا:</h3>
            <textarea id="promptInput" class="prompt-input" placeholder="مثال: اكتب لي قصة قصيرة عن الذكاء الاصطناعي..."></textarea>
            
            <div class="buttons-section">
                <button class="btn btn-primary" onclick="callSingleModel('google_gemini')">
                    🤖 Google Gemini
                </button>
                <button class="btn btn-success" onclick="callSingleModel('openrouter')">
                    🚀 OpenRouter
                </button>
                <button class="btn btn-warning" onclick="callSingleModel('anthropic')">
                    🧠 Claude
                </button>
                <button class="btn btn-info" onclick="callSingleModel('deepseek')">
                    🔍 DeepSeek
                </button>
                <button class="btn btn-primary" onclick="callSingleModel('mistral')">
                    🌪️ Mistral
                </button>
                <button class="btn btn-success" onclick="callSingleModel('huggingface')">
                    🤗 Hugging Face
                </button>
                <button class="btn btn-warning" onclick="callAllModels()" style="font-size: 18px; padding: 15px 30px;">
                    🚀 استدعاء جميع النماذج
                </button>
            </div>
        </div>
        
        <div id="resultsSection" class="results-section">
            <h3>📊 النتائج:</h3>
            <div id="resultsContainer"></div>
        </div>
    </div>
    
    <script>
        function callSingleModel(platform) {
            const prompt = document.getElementById('promptInput').value;
            if (!prompt.trim()) {
                alert('يرجى كتابة طلبك أولاً');
                return;
            }
            
            showLoading();
            
            // محاكاة استدعاء النموذج
            setTimeout(() => {
                const response = generateMockResponse(platform, prompt);
                displayResults([response]);
            }, 2000);
        }
        
        function callAllModels() {
            const prompt = document.getElementById('promptInput').value;
            if (!prompt.trim()) {
                alert('يرجى كتابة طلبك أولاً');
                return;
            }
            
            showLoading();
            
            // محاكاة استدعاء جميع النماذج
            setTimeout(() => {
                const platforms = ['google_gemini', 'openrouter', 'anthropic', 'deepseek', 'mistral', 'huggingface'];
                const responses = platforms.map(platform => generateMockResponse(platform, prompt));
                displayResults(responses);
            }, 4000);
        }
        
        function generateMockResponse(platform, prompt) {
            const responses = {
                google_gemini: {
                    platform: "🤖 Google Gemini",
                    response: "استجابة ذكية ومفصلة من Google Gemini مع تحليل عميق للطلب المقدم.",
                    tokens: 45,
                    cost: "$0.002"
                },
                openrouter: {
                    platform: "🚀 OpenRouter",
                    response: "استجابة متقدمة من OpenRouter باستخدام أفضل النماذج المتاحة.",
                    tokens: 52,
                    cost: "$0.005"
                },
                anthropic: {
                    platform: "🧠 Anthropic Claude",
                    response: "استجابة مدروسة ومتوازنة من Claude مع مراعاة الأخلاقيات والدقة.",
                    tokens: 48,
                    cost: "$0.008"
                },
                deepseek: {
                    platform: "🔍 DeepSeek",
                    response: "تحليل عميق ومفصل من DeepSeek مع حلول مبتكرة ومدروسة.",
                    tokens: 41,
                    cost: "$0.003"
                },
                mistral: {
                    platform: "🌪️ Mistral AI",
                    response: "استجابة سريعة وذكية من Mistral مع فهم ممتاز للسياق.",
                    tokens: 38,
                    cost: "$0.004"
                },
                huggingface: {
                    platform: "🤗 Hugging Face",
                    response: "استجابة من نماذج مفتوحة المصدر عبر Hugging Face.",
                    tokens: 35,
                    cost: "$0.001"
                }
            };
            
            return {
                ...responses[platform],
                prompt: prompt.substring(0, 100) + (prompt.length > 100 ? "..." : ""),
                timestamp: new Date().toLocaleString('ar-SA')
            };
        }
        
        function showLoading() {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');
            
            resultsSection.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading">🤖 جاري استدعاء النماذج... يرجى الانتظار</div>';
        }
        
        function displayResults(responses) {
            const resultsContainer = document.getElementById('resultsContainer');
            
            let html = '';
            responses.forEach(response => {
                html += `
                    <div class="response-card">
                        <h4>${response.platform}</h4>
                        <p><strong>الطلب:</strong> ${response.prompt}</p>
                        <p><strong>الاستجابة:</strong> ${response.response}</p>
                        <div style="margin-top: 10px; font-size: 14px; color: #666;">
                            <span>⏱️ ${response.timestamp}</span> |
                            <span>🔤 ${response.tokens} رمز</span> |
                            <span>💰 ${response.cost}</span>
                        </div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = html;
        }
        
        // إضافة مثال تلقائي
        document.getElementById('promptInput').value = 'اكتب لي قصة قصيرة عن مستقبل الذكاء الاصطناعي';
    </script>
</body>
</html>'''
        
        interface_file = self.models_dir / "models_interface.html"
        with open(interface_file, 'w', encoding='utf-8') as f:
            f.write(interface_html)
        
        logger.info(f"🌐 تم إنشاء واجهة استدعاء النماذج: {interface_file}")
        return str(interface_file)
    
    def start_models_system(self) -> dict:
        """بدء نظام استدعاء النماذج"""
        logger.info("🚀 بدء نظام استدعاء نماذج الذكاء الاصطناعي")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "actions_completed": [],
            "files_created": [],
            "available_platforms": {}
        }
        
        try:
            # تحميل المفاتيح المشفرة
            if self.load_encrypted_keys():
                results["actions_completed"].append("🔑 تم تحميل المفاتيح المشفرة")
            
            # الحصول على النماذج المتاحة
            available_models = self.get_available_models()
            results["available_platforms"] = available_models
            results["actions_completed"].append(f"🤖 تم اكتشاف {len(available_models)} منصة ذكاء اصطناعي")
            
            # إنشاء واجهة استدعاء النماذج
            interface_file = self.create_models_interface()
            results["files_created"].append(interface_file)
            results["actions_completed"].append("🌐 تم إنشاء واجهة استدعاء النماذج")
            
            # حفظ تقرير النماذج
            models_report = {
                "models_system_status": results,
                "available_platforms": available_models,
                "total_keys": sum(platform["keys_count"] for platform in available_models.values()),
                "supported_models": sum(len(platform["models"]) for platform in available_models.values())
            }
            
            report_file = self.models_dir / f"models_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(models_report, f, indent=2, ensure_ascii=False)
            
            results["files_created"].append(str(report_file))
            results["actions_completed"].append("📊 تم إنشاء تقرير النماذج")
            
            logger.info("✅ تم بدء نظام استدعاء النماذج بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء نظام النماذج: {e}")
            results["status"] = "error"
            results["error"] = str(e)
        
        return results

async def main():
    """الدالة الرئيسية"""
    print("🤖 نظام استدعاء نماذج الذكاء الاصطناعي")
    print("=" * 70)
    
    models_caller = AIModelsCaller()
    results = models_caller.start_models_system()
    
    print("\n✅ تم بدء نظام استدعاء النماذج!")
    print("\n✅ الإجراءات المكتملة:")
    for action in results.get("actions_completed", []):
        print(f"   {action}")
    
    print("\n🤖 المنصات المتاحة:")
    for platform, info in results.get("available_platforms", {}).items():
        status = "✅" if info["status"] == "available" else "❌"
        print(f"   {status} {info['platform_name']}: {info['keys_count']} مفتاح، {len(info['models'])} نموذج")
    
    print("\n📁 الملفات المنشأة:")
    for file_path in results.get("files_created", []):
        print(f"   📄 {file_path}")
    
    print("\n🌐 للوصول لواجهة النماذج:")
    print("   📊 افتح: models_interface.html")
    
    # مثال على استدعاء نموذج واحد
    print("\n🧪 مثال على استدعاء نموذج:")
    response = await models_caller.call_google_gemini("مرحباً، كيف يمكنني استخدام الذكاء الاصطناعي؟")
    print(f"   🤖 {response.get('platform', 'Unknown')}: {response.get('response', 'No response')}")

if __name__ == "__main__":
    asyncio.run(main())
