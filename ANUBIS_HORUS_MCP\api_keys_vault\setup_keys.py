#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 إعداد مفاتيح API لأنوبيس وحورس MCP
API Keys Setup for Anubis and Horus MCP

أداة تفاعلية لإعداد وإدارة جميع مفاتيح API بشكل آمن
Interactive tool for setting up and managing all API keys securely
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import getpass
import sys

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from api_keys_vault.keys_manager import APIKeysManager

class APIKeysSetup:
    """🔐 إعداد مفاتيح API"""
    
    def __init__(self):
        """تهيئة إعداد المفاتيح"""
        self.keys_manager = APIKeysManager()
        self.setup_complete = False
        
        # مفاتيح مطلوبة للنظام
        self.required_keys = {
            "github": {
                "name": "GitHub API Key",
                "description": "مفتاح GitHub للوصول للمستودعات والـ MCP",
                "required": True,
                "example": "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "validation_url": "https://api.github.com/user"
            },
            "openai": {
                "name": "OpenAI API Key",
                "description": "مفتاح OpenAI لنماذج GPT",
                "required": False,
                "example": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "validation_url": "https://api.openai.com/v1/models"
            },
            "anthropic": {
                "name": "Anthropic API Key",
                "description": "مفتاح Anthropic لنماذج Claude",
                "required": False,
                "example": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "validation_url": "https://api.anthropic.com/v1/messages"
            },
            "google": {
                "name": "Google AI API Key",
                "description": "مفتاح Google AI لنماذج Gemini",
                "required": False,
                "example": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "validation_url": "https://generativelanguage.googleapis.com/v1/models"
            },
            "langsmith": {
                "name": "LangSmith API Key",
                "description": "مفتاح LangSmith للمراقبة والتتبع",
                "required": False,
                "example": "lsv2_pt_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "validation_url": "https://api.smith.langchain.com/info"
            }
        }
        
        print("🔐 أداة إعداد مفاتيح API لأنوبيس وحورس MCP")
        print("=" * 60)
    
    async def run_setup(self):
        """تشغيل عملية الإعداد"""
        try:
            print("🚀 بدء إعداد مفاتيح API...")
            
            # تهيئة مدير المفاتيح
            await self.keys_manager.initialize()
            
            # عرض المقدمة
            self._show_introduction()
            
            # فحص المفاتيح الموجودة
            existing_keys = await self._check_existing_keys()
            
            # إعداد المفاتيح
            await self._setup_api_keys(existing_keys)
            
            # إعداد مفتاح GitHub المقدم
            await self._setup_provided_github_key()
            
            # التحقق من المفاتيح
            await self._validate_keys()
            
            # عرض الملخص النهائي
            await self._show_final_summary()
            
            self.setup_complete = True
            print("🎉 تم إكمال إعداد مفاتيح API بنجاح!")
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الإعداد بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في الإعداد: {e}")
            raise
    
    def _show_introduction(self):
        """عرض المقدمة"""
        print("\n📋 مرحباً بك في إعداد مفاتيح API!")
        print("=" * 50)
        print("سيتم إعداد المفاتيح التالية:")
        print()
        
        for provider, info in self.required_keys.items():
            status = "مطلوب" if info["required"] else "اختياري"
            print(f"  🔑 {info['name']} ({status})")
            print(f"     {info['description']}")
            print()
        
        print("💡 ملاحظات مهمة:")
        print("  • جميع المفاتيح سيتم تشفيرها بـ AES-256")
        print("  • يمكنك تخطي المفاتيح الاختيارية")
        print("  • يمكن إضافة المفاتيح لاحقاً")
        print("  • سيتم التحقق من صحة المفاتيح")
        print()
        
        input("اضغط Enter للمتابعة...")
    
    async def _check_existing_keys(self) -> Dict[str, bool]:
        """فحص المفاتيح الموجودة"""
        print("\n🔍 فحص المفاتيح الموجودة...")
        
        existing_keys = {}
        all_keys = await self.keys_manager.list_api_keys()
        
        for provider in self.required_keys.keys():
            provider_keys = [k for k in all_keys if k["provider"] == provider]
            existing_keys[provider] = len(provider_keys) > 0
            
            if existing_keys[provider]:
                print(f"  ✅ {provider}: موجود ({len(provider_keys)} مفتاح)")
            else:
                print(f"  ❌ {provider}: غير موجود")
        
        return existing_keys
    
    async def _setup_api_keys(self, existing_keys: Dict[str, bool]):
        """إعداد مفاتيح API"""
        print("\n🔧 إعداد مفاتيح API...")
        print("=" * 40)
        
        for provider, info in self.required_keys.items():
            if provider == "github":
                continue  # سيتم التعامل معه بشكل منفصل
                
            print(f"\n🔑 {info['name']}")
            print(f"📝 {info['description']}")
            
            if existing_keys.get(provider, False):
                print("✅ المفتاح موجود بالفعل")
                update = input("هل تريد تحديثه؟ (y/N): ").lower()
                if update != 'y':
                    continue
            
            if not info["required"]:
                skip = input("هل تريد تخطي هذا المفتاح؟ (Y/n): ").lower()
                if skip != 'n':
                    print("⏭️ تم تخطي المفتاح")
                    continue
            
            # طلب المفتاح
            print(f"📋 مثال: {info['example']}")
            api_key = getpass.getpass("🔐 أدخل المفتاح (سيتم إخفاؤه): ")
            
            if not api_key.strip():
                print("⚠️ لم يتم إدخال مفتاح، تم التخطي")
                continue
            
            # حفظ المفتاح
            success = await self.keys_manager.store_api_key(
                provider=provider,
                key_value=api_key.strip(),
                metadata={
                    "setup_date": "2025-07-23",
                    "setup_method": "interactive"
                }
            )
            
            if success:
                print("✅ تم حفظ المفتاح بنجاح")
            else:
                print("❌ فشل في حفظ المفتاح")
    
    async def _setup_provided_github_key(self):
        """إعداد مفتاح GitHub المقدم"""
        print("\n🔑 إعداد مفتاح GitHub المقدم")
        print("=" * 40)
        
        # هنا يجب إدراج المفتاح المقدم من المستخدم
        # لأغراض الأمان، سأطلب من المستخدم إدخاله
        
        print("📝 تم ذكر أن لديك مفتاح GitHub API للـ MCP")
        print("🔐 من فضلك أدخل مفتاح GitHub الخاص بك:")
        print("📋 يجب أن يبدأ بـ: ghp_")
        
        github_key = getpass.getpass("🔐 مفتاح GitHub: ")
        
        if github_key.strip():
            if not github_key.startswith("ghp_"):
                print("⚠️ تحذير: المفتاح لا يبدأ بـ ghp_")
                confirm = input("هل تريد المتابعة؟ (y/N): ").lower()
                if confirm != 'y':
                    print("⏭️ تم تخطي مفتاح GitHub")
                    return
            
            # حفظ مفتاح GitHub
            success = await self.keys_manager.store_api_key(
                provider="github",
                key_value=github_key.strip(),
                key_name="github_mcp_main",
                metadata={
                    "setup_date": "2025-07-23",
                    "setup_method": "provided_by_user",
                    "purpose": "MCP integration",
                    "permissions": "repo, read:user"
                }
            )
            
            if success:
                print("✅ تم حفظ مفتاح GitHub بنجاح")
                print("🔗 سيتم استخدامه للتكامل مع MCP")
            else:
                print("❌ فشل في حفظ مفتاح GitHub")
        else:
            print("⚠️ لم يتم إدخال مفتاح GitHub")
    
    async def _validate_keys(self):
        """التحقق من صحة المفاتيح"""
        print("\n🔍 التحقق من صحة المفاتيح...")
        print("=" * 40)
        
        all_keys = await self.keys_manager.list_api_keys()
        
        for key_info in all_keys:
            provider = key_info["provider"]
            key_id = key_info["key_id"]
            
            print(f"\n🔑 التحقق من {provider}...")
            
            # الحصول على المفتاح
            api_key = await self.keys_manager.get_api_key(provider, key_id)
            
            if api_key:
                # التحقق من صحة المفتاح
                is_valid = await self.keys_manager.validate_api_key(provider, api_key)
                
                if is_valid:
                    print(f"  ✅ {provider}: صحيح")
                else:
                    print(f"  ⚠️ {provider}: قد يكون غير صحيح (لم يتم اختبار الاتصال)")
            else:
                print(f"  ❌ {provider}: خطأ في قراءة المفتاح")
    
    async def _show_final_summary(self):
        """عرض الملخص النهائي"""
        print("\n📊 ملخص إعداد مفاتيح API")
        print("=" * 50)
        
        all_keys = await self.keys_manager.list_api_keys()
        stats = await self.keys_manager.get_usage_stats()
        
        print(f"📦 إجمالي المفاتيح المحفوظة: {len(all_keys)}")
        print(f"🔐 المفاتيح المشفرة: {stats['usage_statistics']['keys_stored']}")
        print(f"📁 مسار الخزنة: {stats['vault_path']}")
        
        print("\n🔑 المفاتيح المحفوظة:")
        for key_info in all_keys:
            provider = key_info["provider"]
            key_id = key_info["key_id"]
            created_at = key_info["created_at"][:10]  # التاريخ فقط
            
            print(f"  • {provider}: {key_id} (تم الإنشاء: {created_at})")
        
        print("\n🛠️ الخطوات التالية:")
        print("  1. تشغيل خادم MCP: python core/mcp_server.py")
        print("  2. اختبار الاتصالات: python tests/test_api_keys.py")
        print("  3. تفعيل فريق حورس: python horus_integration/team_connector.py")
        print("  4. استكشاف الأدوات: python tools/registry.py")
        
        print("\n🔒 ملاحظات الأمان:")
        print("  • جميع المفاتيح مشفرة بـ AES-256")
        print("  • يتم تسجيل جميع عمليات الوصول")
        print("  • يمكن تدوير المفاتيح دورياً")
        print("  • النسخ الاحتياطية محفوظة في مجلد backups/")

def main():
    """الدالة الرئيسية"""
    print("🔐 أداة إعداد مفاتيح API لأنوبيس وحورس MCP")
    print("=" * 60)
    
    setup = APIKeysSetup()
    
    try:
        asyncio.run(setup.run_setup())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
