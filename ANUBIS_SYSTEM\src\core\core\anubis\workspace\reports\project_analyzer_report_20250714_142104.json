{"project_info": {"path": ".", "name": "", "type": "streamlit", "structure": {"project_exists": true, "is_directory": true, "files_count": 620, "directories_count": 93, "python_files": ["anubis_error_fix.py", "main.py", "organize_project.py", "agents\\database_agent.py", "agents\\error_detector_agent.py", "agents\\file_organizer_agent.py", "agents\\memory_agent.py", "agents\\project_analyzer_agent.py", "agents\\__init__.py", "core\\assistant_system.py", "core\\base_agent.py", "core\\config_manager.py", "core\\logger.py", "core\\__init__.py", "database\\anubis_database.py", "database\\comprehensive_test.py", "database\\direct_setup.py", "database\\final_validation.py", "database\\mysql_connector.py", "database\\mysql_manager.py", "database\\run_all_tests.py", "database\\setup_database.py", "database\\simple_validation.py", "database\\stress_test.py", "database\\test_connection.py", "plugins\\base_plugin.py", "plugins\\example_plugin.py", "plugins\\plugin_manager.py", "plugins\\__init__.py", "scripts\\quick_start.py", "scripts\\__init__.py", "tests\\ask_anubis.py", "tests\\run_all_tests.py", "tests\\test_agents.py", "tests\\test_anubis_system.py", "tests\\test_error_detector.py", "tests\\test_jewelry_database.py", "tests\\test_jewelry_logic.py", "tests\\test_plugins.py", "tests\\test_project_analyzer.py", "tests\\test_system.py", "tests\\__init__.py", "templates\\streamlit_template\\main.py", ".venv\\Lib\\site-packages\\pip\\__init__.py", ".venv\\Lib\\site-packages\\pip\\__main__.py", ".venv\\Lib\\site-packages\\pip\\__pip-runner__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\build_env.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cache.py", ".venv\\Lib\\site-packages\\pip\\_internal\\configuration.py", ".venv\\Lib\\site-packages\\pip\\_internal\\exceptions.py", ".venv\\Lib\\site-packages\\pip\\_internal\\main.py", ".venv\\Lib\\site-packages\\pip\\_internal\\pyproject.py", ".venv\\Lib\\site-packages\\pip\\_internal\\self_outdated_check.py", ".venv\\Lib\\site-packages\\pip\\_internal\\wheel_builder.py", ".venv\\Lib\\site-packages\\pip\\_internal\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\adapter.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\cache.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\controller.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\filewrapper.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\heuristics.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\serialize.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\wrapper.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\_cmd.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\core.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_implementation.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_lint_dependency_groups.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_pip_wrapper.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_toml_compat.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\compat.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\database.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\index.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\locators.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\markers.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\metadata.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\resources.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\version.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\codec.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\compat.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\core.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\idnadata.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\intranges.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\package_data.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\uts46data.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\exceptions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\ext.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\fallback.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\markers.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\metadata.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\requirements.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\specifiers.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\tags.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\utils.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\version.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_elffile.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_manylinux.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_musllinux.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_parser.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_structures.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_tokenizer.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\android.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\api.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\macos.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\unix.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\version.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\windows.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\console.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filter.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatter.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexer.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\modeline.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\plugin.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\regexopt.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\scanner.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\sphinxext.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\style.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\token.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\unistring.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\util.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_impl.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\adapters.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\api.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\auth.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\certs.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\compat.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\cookies.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\exceptions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\help.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\hooks.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\models.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\packages.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\sessions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\status_codes.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\structures.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\utils.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\_internal_utils.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__version__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\providers.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\reporters.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\structs.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\abc.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\align.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\ansi.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\bar.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\box.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\cells.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color_triplet.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\columns.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\constrain.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\containers.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\default_styles.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\diagnose.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\emoji.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\errors.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\filesize.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\file_proxy.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\highlighter.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\json.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\jupyter.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\layout.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live_render.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\logging.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\measure.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\padding.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pager.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\palette.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\panel.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pretty.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\prompt.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\protocol.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\region.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\repr.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\rule.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\scope.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\screen.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\segment.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\spinner.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\status.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\style.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\styled.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\syntax.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\table.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\terminal_theme.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\theme.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\themes.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\traceback.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\tree.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_cell_widths.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_emoji_codes.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_emoji_replace.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_export_format.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_extension.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_fileno.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_inspect.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_log_render.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_loop.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_null_file.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_palettes.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_pick.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_ratio.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_spinners.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_stack.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_timer.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows_renderer.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_wrap.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__main__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_parser.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_re.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_types.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\_writer.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_api.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_macos.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_openssl.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_ssl_constants.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_windows.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connection.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\exceptions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\fields.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\filepost.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\poolmanager.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\request.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\response.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\_collections.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\_version.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\appengine.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\ntlmpool.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\pyopenssl.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\securetransport.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\socks.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_appengine_environ.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\six.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\connection.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\proxy.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\queue.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\request.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\response.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssltransport.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_match_hostname.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\timeout.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\url.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\wait.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\makefile.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\weakref_finalize.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\bindings.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\low_level.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\abstract.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\criterion.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\exceptions.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\resolution.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\_in_process.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\_mapping.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\python.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\_mapping.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\_mapping.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\_spdx.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\file_cache.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\redis_cache.py", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\autocompletion.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\base_command.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\cmdoptions.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\command_context.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\index_command.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\main.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\main_parser.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\parser.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\progress_bars.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\req_command.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\spinners.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\status_codes.py", ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\cache.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\check.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\completion.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\configuration.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\debug.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\download.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\freeze.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\hash.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\help.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\index.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\inspect.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\install.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\list.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\lock.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\search.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\show.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\uninstall.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\base.py", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\installed.py", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\sdist.py", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\index\\collector.py", ".venv\\Lib\\site-packages\\pip\\_internal\\index\\package_finder.py", ".venv\\Lib\\site-packages\\pip\\_internal\\index\\sources.py", ".venv\\Lib\\site-packages\\pip\\_internal\\index\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\base.py", ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_distutils.py", ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_sysconfig.py", ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\base.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\pkg_resources.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\_json.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\candidate.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\direct_url.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\format_control.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\index.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\installation_report.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\link.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\pylock.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\scheme.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\search_scope.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\selection_prefs.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\target_python.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\models\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\auth.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\cache.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\download.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\lazy_wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\session.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\utils.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\xmlrpc.py", ".venv\\Lib\\site-packages\\pip\\_internal\\network\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\check.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\freeze.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\prepare.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\constructors.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_dependency_group.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_file.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_install.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_set.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py", ".venv\\Lib\\site-packages\\pip\\_internal\\req\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\base.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\appdirs.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\compat.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\compatibility_tags.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\datetime.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\deprecation.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\direct_url_helpers.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\egg_link.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\entrypoints.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\filesystem.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\filetypes.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\glibc.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\hashes.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\logging.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\packaging.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\retry.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\setuptools_build.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\subprocess.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\unpacking.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\urls.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\virtualenv.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\_jaraco_text.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\_log.py", ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\bazaar.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\git.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\mercurial.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\subversion.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\versioncontrol.py", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\resolver.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\base.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\candidates.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\factory.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\found_candidates.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\provider.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\reporter.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\requirements.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\resolver.py", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\build_tracker.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_editable.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_legacy.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel_editable.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel_legacy.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\editable_legacy.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__init__.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_compat.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_dists.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_envs.py", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__init__.py"], "config_files": [".venv\\pyvenv.cfg", "archive\\organization_log_20250714_140701.json", "configs\\database_config.json", "configs\\default_config.json", "database\\final_validation_report_20250714_141600.json", "database\\simple_validation_report_20250714_135840.json", "database\\simple_validation_report_20250714_141135.json", "workspace\\knowledge_base\\knowledge_base.json", "workspace\\reports\\assistant_report_20250712_150556.json", "workspace\\reports\\assistant_report_20250712_150648.json", "workspace\\reports\\assistant_report_20250712_150742.json", "workspace\\reports\\assistant_report_20250712_150924.json", "workspace\\reports\\assistant_report_20250712_151038.json", "workspace\\reports\\assistant_report_20250712_151119.json", "workspace\\reports\\assistant_report_20250712_151309.json", "workspace\\reports\\assistant_report_20250712_152601.json", "workspace\\reports\\assistant_report_20250712_153602.json", "workspace\\reports\\assistant_report_20250712_154134.json", "workspace\\reports\\error_detector_report_20250714_123543.json", "workspace\\reports\\error_detector_report_20250714_130858.json", "workspace\\reports\\error_detector_report_20250714_131152.json", "workspace\\reports\\error_detector_report_20250714_142101.json", "workspace\\reports\\project_analyzer_report_20250714_123543.json", "workspace\\reports\\project_analyzer_report_20250714_123544.json", "workspace\\reports\\project_analyzer_report_20250714_130859.json", "workspace\\reports\\project_analyzer_report_20250714_131152.json", "workspace\\shared_memory\\session_20250712.json", "workspace\\shared_memory\\session_20250714.json", "tests\\configs\\default_config.json", "tests\\workspace\\knowledge_base\\knowledge_base.json", "tests\\workspace\\reports\\error_detector_report_20250714_124719.json", "tests\\workspace\\reports\\error_detector_report_20250714_124920.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124719.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124720.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124920.json", "tests\\workspace\\shared_memory\\session_20250714.json", "archive\\duplicate_reports\\all_tests_report_20250714_135032.json", "archive\\duplicate_reports\\final_validation_report_20250714_134540.json", "archive\\duplicate_reports\\final_validation_report_20250714_134549.json", "archive\\duplicate_reports\\final_validation_report_20250714_134653.json", "archive\\duplicate_reports\\simple_validation_report_20250714_134926.json", "archive\\duplicate_reports\\simple_validation_report_20250714_135030.json", "archive\\duplicate_reports\\stress_test_report_20250714_134422.json", "archive\\duplicate_reports\\stress_test_report_20250714_135031.json", "archive\\duplicate_reports\\test_report_20250714_134314.json", "archive\\duplicate_reports\\test_report_20250714_135031.json"], "data_files": ["workspace\\backups\\anubis_backup_20250714_130707.db", "workspace\\backups\\anubis_backup_20250714_130746.db", "workspace\\backups\\anubis_backup_20250714_130858.db", "archive\\old_databases\\anubis.db", "archive\\old_databases\\project_db.db"]}, "agent_type": "project_analyzer", "analysis_time": "2025-07-14T14:21:01.826386"}, "project_size_analysis": {"total_files": 620, "total_lines": 143657, "code_lines": 115187, "comment_lines": 7495, "blank_lines": 20975, "file_types": {"": 8, ".py": 454, ".md": 20, ".txt": 11, ".cfg": 1, ".json": 45, ".html": 1, ".sql": 1, ".db": 5, ".log": 32, ".pyc": 12, ".bat": 2, ".fish": 1, ".ps1": 1, ".exe": 11, ".typed": 14, ".pem": 1}, "largest_files": [{"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\uts46data.py", "lines": 8682}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", "lines": 4585}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\idnadata.py", "lines": 4244}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py", "lines": 3677}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_emoji_codes.py", "lines": 3611}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "lines": 2676}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", "lines": 1985}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "lines": 1716}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", "lines": 1404}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", "lines": 1362}], "directory_structure": {}}, "complexity_analysis": {"cyclomatic_complexity": 0, "cognitive_complexity": 0, "function_complexity": [], "class_complexity": [], "module_complexity": [{"file": "anubis_error_fix.py", "complexity": 67}, {"file": "main.py", "complexity": 25}, {"file": "organize_project.py", "complexity": 37}, {"file": "agents\\database_agent.py", "complexity": 90}, {"file": "agents\\error_detector_agent.py", "complexity": 72}, {"file": "agents\\file_organizer_agent.py", "complexity": 85}, {"file": "agents\\memory_agent.py", "complexity": 67}, {"file": "agents\\project_analyzer_agent.py", "complexity": 129}, {"file": "agents\\__init__.py", "complexity": 14}, {"file": "core\\assistant_system.py", "complexity": 74}, {"file": "core\\base_agent.py", "complexity": 50}, {"file": "core\\config_manager.py", "complexity": 29}, {"file": "core\\logger.py", "complexity": 20}, {"file": "core\\__init__.py", "complexity": 3}, {"file": "database\\anubis_database.py", "complexity": 54}, {"file": "database\\comprehensive_test.py", "complexity": 61}, {"file": "database\\direct_setup.py", "complexity": 21}, {"file": "database\\final_validation.py", "complexity": 66}, {"file": "database\\mysql_connector.py", "complexity": 46}, {"file": "database\\mysql_manager.py", "complexity": 34}, {"file": "database\\run_all_tests.py", "complexity": 39}, {"file": "database\\setup_database.py", "complexity": 37}, {"file": "database\\simple_validation.py", "complexity": 53}, {"file": "database\\stress_test.py", "complexity": 55}, {"file": "database\\test_connection.py", "complexity": 12}, {"file": "plugins\\base_plugin.py", "complexity": 11}, {"file": "plugins\\example_plugin.py", "complexity": 18}, {"file": "plugins\\plugin_manager.py", "complexity": 32}, {"file": "plugins\\__init__.py", "complexity": 3}, {"file": "scripts\\quick_start.py", "complexity": 5}, {"file": "scripts\\__init__.py", "complexity": 3}, {"file": "tests\\ask_anubis.py", "complexity": 58}, {"file": "tests\\run_all_tests.py", "complexity": 46}, {"file": "tests\\test_agents.py", "complexity": 17}, {"file": "tests\\test_anubis_system.py", "complexity": 28}, {"file": "tests\\test_error_detector.py", "complexity": 24}, {"file": "tests\\test_jewelry_database.py", "complexity": 29}, {"file": "tests\\test_jewelry_logic.py", "complexity": 9}, {"file": "tests\\test_plugins.py", "complexity": 12}, {"file": "tests\\test_project_analyzer.py", "complexity": 23}, {"file": "tests\\test_system.py", "complexity": 53}, {"file": "tests\\__init__.py", "complexity": 3}, {"file": "templates\\streamlit_template\\main.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\__init__.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\__main__.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\__pip-runner__.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\build_env.py", "complexity": 65}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cache.py", "complexity": 73}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\configuration.py", "complexity": 84}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\exceptions.py", "complexity": 137}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\main.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\pyproject.py", "complexity": 57}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\self_outdated_check.py", "complexity": 37}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\wheel_builder.py", "complexity": 74}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\__init__.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", "complexity": 1251}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\__init__.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\adapter.py", "complexity": 38}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\cache.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\controller.py", "complexity": 124}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\filewrapper.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\heuristics.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\serialize.py", "complexity": 33}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\wrapper.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\_cmd.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\core.py", "complexity": 15}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__main__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_implementation.py", "complexity": 57}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_lint_dependency_groups.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_pip_wrapper.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_toml_compat.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__main__.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\compat.py", "complexity": 356}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\database.py", "complexity": 403}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\index.py", "complexity": 139}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\locators.py", "complexity": 407}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "complexity": 122}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\markers.py", "complexity": 38}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\metadata.py", "complexity": 370}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\resources.py", "complexity": 70}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", "complexity": 137}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", "complexity": 602}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\version.py", "complexity": 184}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\wheel.py", "complexity": 311}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__init__.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", "complexity": 330}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__main__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\codec.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\compat.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\core.py", "complexity": 164}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\idnadata.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\intranges.py", "complexity": 13}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\package_data.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\uts46data.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\exceptions.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\ext.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\fallback.py", "complexity": 270}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__init__.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\markers.py", "complexity": 85}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\metadata.py", "complexity": 213}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\requirements.py", "complexity": 22}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\specifiers.py", "complexity": 263}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\tags.py", "complexity": 237}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\utils.py", "complexity": 28}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\version.py", "complexity": 96}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_elffile.py", "complexity": 25}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_manylinux.py", "complexity": 89}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_musllinux.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_parser.py", "complexity": 40}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_structures.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_tokenizer.py", "complexity": 21}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__init__.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py", "complexity": 863}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\android.py", "complexity": 52}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\api.py", "complexity": 36}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\macos.py", "complexity": 12}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\unix.py", "complexity": 50}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\version.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\windows.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__init__.py", "complexity": 36}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__main__.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\console.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filter.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatter.py", "complexity": 27}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexer.py", "complexity": 261}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\modeline.py", "complexity": 15}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\plugin.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\regexopt.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\scanner.py", "complexity": 19}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\sphinxext.py", "complexity": 70}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\style.py", "complexity": 94}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\token.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\unistring.py", "complexity": 32}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\util.py", "complexity": 101}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__init__.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__main__.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_impl.py", "complexity": 68}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\adapters.py", "complexity": 188}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\api.py", "complexity": 49}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\auth.py", "complexity": 52}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\certs.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\compat.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\cookies.py", "complexity": 152}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\exceptions.py", "complexity": 34}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\help.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\hooks.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\models.py", "complexity": 313}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\packages.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\sessions.py", "complexity": 203}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\status_codes.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\structures.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\utils.py", "complexity": 290}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\_internal_utils.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__init__.py", "complexity": 39}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__version__.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\providers.py", "complexity": 40}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\reporters.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\structs.py", "complexity": 36}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\abc.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\align.py", "complexity": 63}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\ansi.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\bar.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\box.py", "complexity": 39}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\cells.py", "complexity": 37}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color.py", "complexity": 124}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color_triplet.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\columns.py", "complexity": 59}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "complexity": 566}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\constrain.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\containers.py", "complexity": 43}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\default_styles.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\diagnose.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\emoji.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\errors.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\filesize.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\file_proxy.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\highlighter.py", "complexity": 46}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\json.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\jupyter.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\layout.py", "complexity": 70}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", "complexity": 90}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live_render.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\logging.py", "complexity": 61}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", "complexity": 51}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\measure.py", "complexity": 28}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\padding.py", "complexity": 34}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pager.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\palette.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\panel.py", "complexity": 38}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pretty.py", "complexity": 254}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "complexity": 283}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", "complexity": 57}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\prompt.py", "complexity": 55}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\protocol.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\region.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\repr.py", "complexity": 25}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\rule.py", "complexity": 20}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\scope.py", "complexity": 12}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\screen.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\segment.py", "complexity": 133}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\spinner.py", "complexity": 33}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\status.py", "complexity": 32}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\style.py", "complexity": 265}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\styled.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\syntax.py", "complexity": 194}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\table.py", "complexity": 304}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\terminal_theme.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", "complexity": 319}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\theme.py", "complexity": 22}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\themes.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\traceback.py", "complexity": 164}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\tree.py", "complexity": 51}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_cell_widths.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_emoji_codes.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_emoji_replace.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_export_format.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_extension.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_fileno.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_inspect.py", "complexity": 82}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_log_render.py", "complexity": 20}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_loop.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_null_file.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_palettes.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_pick.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_ratio.py", "complexity": 61}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_spinners.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_stack.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_timer.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", "complexity": 89}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows_renderer.py", "complexity": 35}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_wrap.py", "complexity": 24}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__init__.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__main__.py", "complexity": 28}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_parser.py", "complexity": 198}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_re.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_types.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\_writer.py", "complexity": 73}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_api.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_macos.py", "complexity": 106}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_openssl.py", "complexity": 18}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_ssl_constants.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_windows.py", "complexity": 50}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__init__.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connection.py", "complexity": 129}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py", "complexity": 258}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\exceptions.py", "complexity": 40}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\fields.py", "complexity": 59}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\filepost.py", "complexity": 22}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\poolmanager.py", "complexity": 119}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\request.py", "complexity": 39}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\response.py", "complexity": 232}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\_collections.py", "complexity": 95}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\_version.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__init__.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\appengine.py", "complexity": 65}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\ntlmpool.py", "complexity": 13}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\pyopenssl.py", "complexity": 124}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\securetransport.py", "complexity": 206}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\socks.py", "complexity": 40}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_appengine_environ.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\six.py", "complexity": 191}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\connection.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\proxy.py", "complexity": 15}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\queue.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\request.py", "complexity": 39}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\response.py", "complexity": 24}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py", "complexity": 146}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssltransport.py", "complexity": 50}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py", "complexity": 155}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_match_hostname.py", "complexity": 48}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\timeout.py", "complexity": 81}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\url.py", "complexity": 112}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\wait.py", "complexity": 68}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\makefile.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\weakref_finalize.py", "complexity": 46}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\bindings.py", "complexity": 36}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\low_level.py", "complexity": 90}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\abstract.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\criterion.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\exceptions.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\resolution.py", "complexity": 131}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\_in_process.py", "complexity": 70}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__init__.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__init__.py", "complexity": 110}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\_mapping.py", "complexity": 31}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__init__.py", "complexity": 54}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\python.py", "complexity": 83}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\_mapping.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", "complexity": 135}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\_mapping.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__init__.py", "complexity": 25}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\_spdx.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__init__.py", "complexity": 37}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\file_cache.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\redis_cache.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\autocompletion.py", "complexity": 103}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\base_command.py", "complexity": 42}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\cmdoptions.py", "complexity": 164}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\command_context.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\index_command.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\main.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\main_parser.py", "complexity": 39}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\parser.py", "complexity": 83}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\progress_bars.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\req_command.py", "complexity": 57}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\spinners.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\status_codes.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\cache.py", "complexity": 33}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\check.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\completion.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\configuration.py", "complexity": 81}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\debug.py", "complexity": 50}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\download.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\freeze.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\hash.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\help.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\index.py", "complexity": 24}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\inspect.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\install.py", "complexity": 161}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\list.py", "complexity": 93}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\lock.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\search.py", "complexity": 32}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\show.py", "complexity": 60}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\uninstall.py", "complexity": 20}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\wheel.py", "complexity": 24}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\__init__.py", "complexity": 24}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\base.py", "complexity": 12}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\installed.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\sdist.py", "complexity": 37}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\wheel.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__init__.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\collector.py", "complexity": 109}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\package_finder.py", "complexity": 219}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\sources.py", "complexity": 44}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\base.py", "complexity": 21}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_distutils.py", "complexity": 56}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_sysconfig.py", "complexity": 78}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\__init__.py", "complexity": 116}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\base.py", "complexity": 160}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\pkg_resources.py", "complexity": 57}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\_json.py", "complexity": 22}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__init__.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\candidate.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\direct_url.py", "complexity": 32}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\format_control.py", "complexity": 22}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\index.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\installation_report.py", "complexity": 23}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\link.py", "complexity": 129}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\pylock.py", "complexity": 19}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\scheme.py", "complexity": 4}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\search_scope.py", "complexity": 32}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\selection_prefs.py", "complexity": 11}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\target_python.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\wheel.py", "complexity": 27}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\auth.py", "complexity": 168}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\cache.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\download.py", "complexity": 71}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\lazy_wheel.py", "complexity": 29}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\session.py", "complexity": 108}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\utils.py", "complexity": 40}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\xmlrpc.py", "complexity": 7}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\check.py", "complexity": 36}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\freeze.py", "complexity": 55}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\prepare.py", "complexity": 159}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\constructors.py", "complexity": 104}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_dependency_group.py", "complexity": 30}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_file.py", "complexity": 133}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_install.py", "complexity": 228}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_set.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py", "complexity": 194}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\__init__.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\base.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\appdirs.py", "complexity": 13}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\compat.py", "complexity": 17}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\compatibility_tags.py", "complexity": 46}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\datetime.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\deprecation.py", "complexity": 25}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\direct_url_helpers.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\egg_link.py", "complexity": 26}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\entrypoints.py", "complexity": 30}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\filesystem.py", "complexity": 37}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\filetypes.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\glibc.py", "complexity": 27}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\hashes.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\logging.py", "complexity": 59}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", "complexity": 146}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\packaging.py", "complexity": 5}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\retry.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\setuptools_build.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\subprocess.py", "complexity": 79}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py", "complexity": 62}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\unpacking.py", "complexity": 108}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\urls.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\virtualenv.py", "complexity": 25}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\wheel.py", "complexity": 47}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\_jaraco_text.py", "complexity": 14}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\_log.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\bazaar.py", "complexity": 12}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\git.py", "complexity": 95}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\mercurial.py", "complexity": 16}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\subversion.py", "complexity": 63}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\versioncontrol.py", "complexity": 116}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\resolver.py", "complexity": 167}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\base.py", "complexity": 21}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\candidates.py", "complexity": 93}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\factory.py", "complexity": 196}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\found_candidates.py", "complexity": 58}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\provider.py", "complexity": 89}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\reporter.py", "complexity": 9}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\requirements.py", "complexity": 33}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\resolver.py", "complexity": 81}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\build_tracker.py", "complexity": 20}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_editable.py", "complexity": 6}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_legacy.py", "complexity": 13}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel.py", "complexity": 8}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel_editable.py", "complexity": 10}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel_legacy.py", "complexity": 28}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__init__.py", "complexity": 1}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\editable_legacy.py", "complexity": 3}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py", "complexity": 163}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__init__.py", "complexity": 2}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_compat.py", "complexity": 15}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_dists.py", "complexity": 51}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_envs.py", "complexity": 41}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__init__.py", "complexity": 1}], "complexity_distribution": {"low": 120, "medium": 64, "high": 270}, "average_complexity": 59.42511013215859}, "dependency_analysis": {"total_dependencies": 27, "external_dependencies": ["pathlib2>=2.3.7", "typing-extensions>=4.0.0", "mysql-connector-python>=8.0.0  # لـ MySQL - for MySQL", "psycopg2-binary>=2.9.0          # لـ PostgreSQL - for PostgreSQL", "chardet>=5.0.0", "pathspec>=0.10.0", "pyyaml>=6.0", "python-dateutil>=2.8.0", "colorlog>=6.7.0", "black>=22.0.0      # تنسيق الكود - Code formatting", "flake8>=5.0.0      # فحص جودة الكود - Code quality checking", "pylint>=2.17.0     # تحليل الكود - Code analysis", "pytest>=7.0.0     # الاختبارات - Testing framework", "pytest-cov>=4.0.0 # تغطية الاختبارات - Test coverage", "streamlit>=1.28.0  # لمشاريع Streamlit - for Streamlit projects", "django>=4.0.0      # لمشاريع Django - for Django projects", "fastapi>=0.100.0   # لمشاريع FastAPI - for FastAPI projects", "flask>=2.3.0       # لمشاريع Flask - for Flask projects", "tqdm>=4.64.0        # شريط التقدم - Progress bars", "click>=8.0.0        # واجهة سطر الأوامر - CLI interface", "bandit>=1.7.0       # فحص الأمان - Security linting", "safety>=2.3.0       # فحص الثغرات - Vulnerability checking", "radon>=5.1.0        # قياس التعقيد - Complexity metrics", "pandas>=2.0.0       # معالجة البيانات - Data manipulation", "numpy>=1.24.0       # العمليات الرياضية - Numerical operations", "psutil>=5.9.0       # معلومات النظام - System information", "gitpython>=3.1.0    # التعامل مع Git - Git integration"], "internal_dependencies": ["", "<PERSON><PERSON><PERSON><PERSON>", "collections", "ipywidgets", "ipaddress", "ctypes", "bz2", "jewelry_workshop_business_logic", "linecache", "config_manager", "json", "<PERSON><PERSON><PERSON>", "zlib", "sysconfig", "pty", "pkgu<PERSON>", "dummy_threading", "keyring", "sphinx", "mimetypes", "StringIO", "<PERSON><PERSON><PERSON><PERSON>", "string", "io", "types", "android", "copy", "attr", "optparse", "dummy_thread", "__future__", "keyword", "plugins", "winreg", "pathlib", "itertools", "inspect", "encodings", "py_compile", "fnmatch", "base_agent", "xmlrpc", "platform", "shutil", "mysql", "base64", "__builtin__", "file_organizer_agent", "site", "cgi", "builtins", "ssl", "compileall", "time", "urlparse", "urllib3_secure_extra", "plistlib", "locale", "__pypy__", "requests", "google", "cryptography", "pandas", "math", "sqlite3", "streamlit", "urllib2", "ast", "xmlrpclib", "bisect", "marshal", "datetime", "logging", "_manylinux", "hmac", "subprocess", "unicodedata", "shlex", "thread", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "docutils", "csv", "runpy", "_abcoll", "<PERSON><PERSON><PERSON><PERSON>", "imp", "zipimport", "redis", "weakref", "select", "httplib", "numpy", "__main__", "warnings", "tokenize", "os", "core", "difflib", "java", "database_agent", "_socket", "plotly", "setuptools", "functools", "getpass", "asyncio", "codecs", "_typeshed", "HTMLParser", "ntpath", "_frozen_importlib", "errno", "html", "distutils", "logger", "urllib", "IPython", "OpenSSL", "array", "jewelry_database_models", "http", "mmap", "operator", "threading", "unittest", "Queue", "_frozen_importlib_external", "typing", "filelock", "os,", "tarfile", "contextlib", "database_manager", "_aix_support", "email", "tempfile", "socket", "random", "gc", "%(module)s", "agents", "dataclasses", "uuid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zipfile", "posixpath", "fractions", "stat", "memory_agent", "_ssl", "configparser", "psycopg2", "textwrap", "_imp", "calendar", "re", "abc", "htmlentitydefs", "netrc", "enum", "colorsys", "lzma", "ntlm", "pip", "importlib", "struct", "traceback", "socks", "reprlib", "queue", "sys", "glob", "decimal", "atexit", "j<PERSON>", "_osx_support", "gzip"], "dependency_tree": {}, "outdated_dependencies": [], "security_vulnerabilities": []}, "architecture_analysis": {"project_type": "streamlit", "directory_structure": {"depth": 8, "directories": [".venv", "agents", "archive", "configs", "core", "database", "docs", "plugins", "scripts", "templates", "tests", "workspace", ".venv\\Lib", ".venv\\Scripts", "agents\\__pycache__", "archive\\cache_files", "archive\\duplicate_reports", "archive\\old_databases", "archive\\old_files", "archive\\temp_files", "archive\\unused_files", "core\\__pycache__", "database\\__pycache__", "templates\\streamlit_template", "tests\\configs", "tests\\workspace", "workspace\\backups", "workspace\\collaboration_logs", "workspace\\knowledge_base", "workspace\\logs", "workspace\\reports", "workspace\\shared_memory", "tests\\workspace\\collaboration_logs", "tests\\workspace\\knowledge_base", "tests\\workspace\\logs", "tests\\workspace\\reports", "tests\\workspace\\shared_memory", ".venv\\Lib\\site-packages", ".venv\\Lib\\site-packages\\pip", ".venv\\Lib\\site-packages\\pip-25.1.1.dist-info", ".venv\\Lib\\site-packages\\pip\\_internal", ".venv\\Lib\\site-packages\\pip\\_vendor", ".venv\\Lib\\site-packages\\pip-25.1.1.dist-info\\licenses", ".venv\\Lib\\site-packages\\pip\\_internal\\cli", ".venv\\Lib\\site-packages\\pip\\_internal\\commands", ".venv\\Lib\\site-packages\\pip\\_internal\\distributions", ".venv\\Lib\\site-packages\\pip\\_internal\\index", ".venv\\Lib\\site-packages\\pip\\_internal\\locations", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata", ".venv\\Lib\\site-packages\\pip\\_internal\\models", ".venv\\Lib\\site-packages\\pip\\_internal\\network", ".venv\\Lib\\site-packages\\pip\\_internal\\operations", ".venv\\Lib\\site-packages\\pip\\_internal\\req", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution", ".venv\\Lib\\site-packages\\pip\\_internal\\utils", ".venv\\Lib\\site-packages\\pip\\_internal\\vcs", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol", ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi", ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups", ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib", ".venv\\Lib\\site-packages\\pip\\_vendor\\distro", ".venv\\Lib\\site-packages\\pip\\_vendor\\idna", ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging", ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources", ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks", ".venv\\Lib\\site-packages\\pip\\_vendor\\requests", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib", ".venv\\Lib\\site-packages\\pip\\_vendor\\rich", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli", ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w", ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3", ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches", ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers", ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles", ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process", ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport", ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports", ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build", ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy", ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib"], "organization_score": 71.42857142857143}, "design_patterns": [], "architectural_style": "MVC", "modularity_score": 50, "coupling_analysis": {}, "cohesion_analysis": {}}, "performance_analysis": {"potential_bottlenecks": [{"file": "anubis_error_fix.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "organize_project.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\database_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\error_detector_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\file_organizer_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\memory_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\project_analyzer_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\project_analyzer_agent.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": "core\\assistant_system.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "core\\config_manager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "database\\comprehensive_test.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "database\\final_validation.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "database\\run_all_tests.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "database\\stress_test.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "plugins\\example_plugin.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "plugins\\plugin_manager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\ask_anubis.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\run_all_tests.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_error_detector.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_project_analyzer.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": "tests\\test_system.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\build_env.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cache.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\configuration.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\exceptions.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\pyproject.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\wheel_builder.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\controller.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\serialize.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\_implementation.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\compat.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\database.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\index.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\locators.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\metadata.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\resources.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\version.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\core.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\ext.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\fallback.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\markers.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\metadata.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\specifiers.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\tags.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\version.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_elffile.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_manylinux.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_musllinux.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\android.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\api.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\unix.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\windows.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__main__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatter.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexer.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\modeline.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\plugin.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\regexopt.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\sphinxext.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\style.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\token.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\unistring.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\util.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_impl.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\adapters.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\api.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\cookies.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\models.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\packages.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\sessions.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\status_codes.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\utils.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\providers.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\structs.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\align.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\ansi.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\bar.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\box.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\cells.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\columns.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\containers.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\filesize.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\highlighter.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\json.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\jupyter.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\layout.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\logging.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\measure.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\padding.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pretty.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\prompt.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\segment.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\spinner.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\status.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\style.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\syntax.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\table.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\theme.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\traceback.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\tree.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_inspect.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_loop.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_ratio.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_wrap.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__main__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\_parser.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\_writer.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_api.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_macos.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\_windows.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connection.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\exceptions.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\fields.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\filepost.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\poolmanager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\request.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\response.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\_collections.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\appengine.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\pyopenssl.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\securetransport.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\six.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\connection.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\request.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_match_hostname.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\timeout.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\url.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\wait.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\weakref_finalize.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\bindings.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\low_level.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\resolution.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\_in_process.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\_mapping.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\python.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\autocompletion.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\cmdoptions.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\index_command.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\main_parser.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\parser.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\req_command.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\check.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\completion.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\configuration.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\debug.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\index.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\install.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\list.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\search.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\show.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\uninstall.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\base.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\sdist.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\collector.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\package_finder.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\sources.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_distutils.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\_sysconfig.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\base.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\pkg_resources.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\_json.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__init__.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\installation_report.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\link.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\search_scope.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\target_python.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\auth.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\cache.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\download.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\lazy_wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\session.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\utils.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\check.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\freeze.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\prepare.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\constructors.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_dependency_group.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_file.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_install.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_set.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\compatibility_tags.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\egg_link.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\entrypoints.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\filesystem.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\hashes.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\logging.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\subprocess.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\unpacking.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\virtualenv.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\git.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\subversion.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\versioncontrol.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\resolver.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\candidates.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\factory.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\found_candidates.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\provider.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\requirements.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\resolver.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\build_tracker.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\wheel_legacy.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_dists.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\_envs.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}], "optimization_opportunities": [], "resource_usage_patterns": {}, "scalability_assessment": {}}, "security_analysis": {"security_issues": [{"file": "agents\\project_analyzer_agent.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "agents\\project_analyzer_agent.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": "tests\\test_error_detector.py", "issue": "كلمة مرور مكشوفة في الكود", "severity": "high"}, {"file": "tests\\test_plugins.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "كلمة مرور مكشوفة في الكود", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\typing_extensions.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\_parser.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__init__.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__init__.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__init__.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", "issue": "كلمة مرور مكشوفة في الكود", "severity": "high"}, {"file": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\setuptools_build.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}], "vulnerability_assessment": {}, "security_score": 0, "recommendations": []}, "maintainability_analysis": {"maintainability_index": 25.555690420721156, "documentation_coverage": 60.254864818322716, "test_coverage_estimate": 2.4229074889867843, "code_duplication": 0, "technical_debt": []}, "recommendations": [{"category": "architecture", "priority": "high", "title": "تحسين الهيكل المعماري", "description": "فصل الاهتمامات وتطبيق أنماط التصميم", "action": "إعادة تنظيم الكود حسب المسؤوليات"}, {"category": "performance", "priority": "medium", "title": "تحسين الأداء", "description": "تحسين الخوارزميات وتقليل التعقيد", "action": "مراجعة الحلقات والاستعلامات"}, {"category": "security", "priority": "high", "title": "تعزيز الأمان", "description": "إضافة طبقات حماية وتشفير البيانات الحساسة", "action": "مراجعة نقاط الضعف الأمنية"}, {"category": "maintainability", "priority": "medium", "title": "تحسين قابلية الصيانة", "description": "إضافة توثيق واختبارات شاملة", "action": "كتابة docstrings واختبارات وحدة"}], "summary": {"overall_score": 17.67, "project_size": "620 ملف", "code_lines": 115187, "complexity_level": "عالي", "security_status": "يحتاج تحسين", "maintainability_level": "ضعيف", "recommendations_count": 4, "analysis_time": "2025-07-14T14:21:04.352367"}}