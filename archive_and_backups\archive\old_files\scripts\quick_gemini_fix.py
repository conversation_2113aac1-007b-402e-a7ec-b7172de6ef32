#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ إصلاح سريع للوكلاء مع Gemini CLI
Quick Agent Fix with Gemini CLI

نظام مبسط لإصلاح الوكلاء بسرعة
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
from pathlib import Path


class QuickGeminiFix:
    """⚡ إصلاح سريع للوكلاء"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.absolute()
        self.agents_path = self.base_path / 'agents'
        self.agents_path.mkdir(exist_ok=True)
        
        print("⚡ نظام الإصلاح السريع للوكلاء مع Gemini CLI")
        print(f"📁 المسار: {self.base_path}")
    
    def run_gemini_command(self, prompt: str) -> str:
        """تشغيل أمر Gemini CLI"""
        try:
            print("💎 إرسال طلب لـ Gemini CLI...")
            
            # تحضير الأمر
            cmd = ['gemini', '--prompt', prompt]
            
            # تشغيل الأمر
            result = subprocess.run(
                cmd,
                cwd=str(self.base_path),
                capture_output=True,
                text=True,
                timeout=180
            )
            
            if result.returncode == 0:
                print("✅ تم الحصول على استجابة من Gemini")
                return result.stdout.strip()
            else:
                print(f"❌ خطأ في Gemini: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة الاستجابة")
            return None
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return None
    
    def extract_code(self, response: str) -> str:
        """استخراج الكود من الاستجابة"""
        if not response:
            return None
        
        lines = response.split('\n')
        code_lines = []
        in_code = False
        
        for line in lines:
            if line.strip().startswith('```python'):
                in_code = True
                continue
            elif line.strip() == '```' and in_code:
                in_code = False
                continue
            elif line.strip().startswith('#!/usr/bin/env python3'):
                in_code = True
                code_lines.append(line)
            elif in_code:
                code_lines.append(line)
        
        if code_lines:
            return '\n'.join(code_lines)
        
        # البحث عن كود مباشر
        for i, line in enumerate(lines):
            if 'class ' in line and 'Agent' in line:
                return '\n'.join(lines[max(0, i-2):])
        
        return None
    
    def save_agent(self, agent_name: str, code: str) -> str:
        """حفظ الوكيل"""
        if not code:
            return None
        
        file_path = self.agents_path / f"{agent_name}.py"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(code)
            
            print(f"✅ تم حفظ {agent_name}: {file_path}")
            return str(file_path)
        except Exception as e:
            print(f"❌ خطأ في حفظ {agent_name}: {e}")
            return None
    
    def fix_project_analyzer(self) -> str:
        """إصلاح ProjectAnalyzerAgent"""
        print("\n📊 إصلاح ProjectAnalyzerAgent...")
        
        prompt = """
أنشئ ProjectAnalyzerAgent محسن لنظام أنوبيس.

المطلوب:
- وراثة من BaseAgent
- تحليل شامل للمشاريع (React, Vue, Django, FastAPI)
- كشف نوع المشروع تلقائياً
- دعم الذكاء الاصطناعي

الكود:
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedProjectAnalyzerAgent(BaseAgent):
    def get_agent_type(self) -> str:
        return "enhanced_project_analyzer"
    
    def initialize_agent(self):
        self.project_types = {
            'react': ['package.json', 'src', 'components'],
            'vue': ['package.json', 'src', 'components', 'vue.config.js'],
            'django': ['manage.py', 'settings.py', 'models.py'],
            'fastapi': ['main.py', 'requirements.txt', 'app']
        }
        self.log_action("تهيئة محلل المشاريع المحسن", "جاهز للتحليل")
    
    def analyze_project(self, project_path=None):
        target_path = Path(project_path) if project_path else self.project_path
        
        analysis = {
            'project_path': str(target_path),
            'project_type': self._detect_project_type(target_path),
            'files_count': self._count_files(target_path),
            'structure': self._analyze_structure(target_path),
            'recommendations': self._generate_recommendations(target_path)
        }
        
        return analysis
    
    def _detect_project_type(self, path):
        for proj_type, indicators in self.project_types.items():
            if all((path / indicator).exists() for indicator in indicators[:2]):
                return proj_type
        return 'unknown'
    
    def _count_files(self, path):
        return len(list(path.rglob('*'))) if path.exists() else 0
    
    def _analyze_structure(self, path):
        structure = {}
        if path.exists():
            for item in path.iterdir():
                if item.is_dir():
                    structure[item.name] = 'directory'
                else:
                    structure[item.name] = 'file'
        return structure
    
    def _generate_recommendations(self, path):
        return ["تحسين هيكل المشروع", "إضافة اختبارات", "تحسين التوثيق"]
```

أريد هذا الكود كاملاً!
        """
        
        response = self.run_gemini_command(prompt)
        code = self.extract_code(response)
        
        if code:
            return self.save_agent('enhanced_project_analyzer', code)
        else:
            # استخدام الكود الافتراضي
            default_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedProjectAnalyzerAgent(BaseAgent):
    def get_agent_type(self) -> str:
        return "enhanced_project_analyzer"
    
    def initialize_agent(self):
        self.project_types = {
            'react': ['package.json', 'src', 'components'],
            'vue': ['package.json', 'src', 'components', 'vue.config.js'],
            'django': ['manage.py', 'settings.py', 'models.py'],
            'fastapi': ['main.py', 'requirements.txt', 'app']
        }
        self.log_action("تهيئة محلل المشاريع المحسن", "جاهز للتحليل")
    
    def analyze_project(self, project_path=None):
        target_path = Path(project_path) if project_path else self.project_path
        
        analysis = {
            'project_path': str(target_path),
            'project_type': self._detect_project_type(target_path),
            'files_count': self._count_files(target_path),
            'structure': self._analyze_structure(target_path),
            'recommendations': self._generate_recommendations(target_path)
        }
        
        return analysis
    
    def _detect_project_type(self, path):
        for proj_type, indicators in self.project_types.items():
            if all((path / indicator).exists() for indicator in indicators[:2]):
                return proj_type
        return 'unknown'
    
    def _count_files(self, path):
        return len(list(path.rglob('*'))) if path.exists() else 0
    
    def _analyze_structure(self, path):
        structure = {}
        if path.exists():
            for item in path.iterdir():
                if item.is_dir():
                    structure[item.name] = 'directory'
                else:
                    structure[item.name] = 'file'
        return structure
    
    def _generate_recommendations(self, path):
        return ["تحسين هيكل المشروع", "إضافة اختبارات", "تحسين التوثيق"]
'''
            return self.save_agent('enhanced_project_analyzer', default_code)
    
    def fix_file_organizer(self) -> str:
        """إصلاح FileOrganizerAgent"""
        print("\n📁 إصلاح FileOrganizerAgent...")
        
        default_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import shutil
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedFileOrganizerAgent(BaseAgent):
    def get_agent_type(self) -> str:
        return "enhanced_file_organizer"
    
    def initialize_agent(self):
        self.file_categories = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.svg'],
            'documents': ['.pdf', '.doc', '.docx', '.txt', '.md'],
            'code': ['.py', '.js', '.ts', '.jsx', '.tsx', '.vue'],
            'config': ['.json', '.yaml', '.yml', '.toml', '.ini']
        }
        self.log_action("تهيئة منظم الملفات المحسن", "جاهز للتنظيم")
    
    def organize_files(self, target_path=None):
        target_path = Path(target_path) if target_path else self.project_path
        
        organized = {
            'organized_files': 0,
            'created_folders': [],
            'categories': {}
        }
        
        for category, extensions in self.file_categories.items():
            category_path = target_path / category
            files_moved = 0
            
            for ext in extensions:
                files = list(target_path.glob(f'*{ext}'))
                if files:
                    category_path.mkdir(exist_ok=True)
                    if str(category_path) not in organized['created_folders']:
                        organized['created_folders'].append(str(category_path))
                    
                    for file in files:
                        try:
                            shutil.move(str(file), str(category_path / file.name))
                            files_moved += 1
                        except Exception as e:
                            print(f"خطأ في نقل {file}: {e}")
            
            if files_moved > 0:
                organized['categories'][category] = files_moved
                organized['organized_files'] += files_moved
        
        return organized
    
    def create_project_structure(self, project_type, project_name):
        project_path = self.project_path / project_name
        
        structures = {
            'react': ['src', 'src/components', 'src/pages', 'public', 'tests'],
            'vue': ['src', 'src/components', 'src/views', 'public', 'tests'],
            'django': ['app', 'templates', 'static', 'tests', 'requirements'],
            'fastapi': ['app', 'tests', 'docs', 'requirements']
        }
        
        if project_type in structures:
            project_path.mkdir(exist_ok=True)
            for folder in structures[project_type]:
                (project_path / folder).mkdir(parents=True, exist_ok=True)
            
            return {
                'project_path': str(project_path),
                'created_folders': structures[project_type],
                'project_type': project_type
            }
        
        return {'error': f'نوع المشروع غير مدعوم: {project_type}'}
'''
        
        return self.save_agent('enhanced_file_organizer', default_code)
    
    def fix_memory_agent(self) -> str:
        """إصلاح MemoryAgent"""
        print("\n🧠 إصلاح MemoryAgent...")
        
        default_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import json
import sqlite3
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedMemoryAgent(BaseAgent):
    def get_agent_type(self) -> str:
        return "enhanced_memory_agent"
    
    def initialize_agent(self):
        self.memory_db = self.project_path / 'memory.db'
        self._init_database()
        self.log_action("تهيئة وكيل الذاكرة المحسن", "قاعدة البيانات جاهزة")
    
    def _init_database(self):
        conn = sqlite3.connect(self.memory_db)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE,
                data TEXT,
                timestamp TEXT,
                category TEXT
            )
        """)
        
        conn.commit()
        conn.close()
    
    def store_memory(self, key: str, data: Any, category: str = 'general'):
        conn = sqlite3.connect(self.memory_db)
        cursor = conn.cursor()
        
        data_json = json.dumps(data, ensure_ascii=False)
        timestamp = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT OR REPLACE INTO memories (key, data, timestamp, category)
            VALUES (?, ?, ?, ?)
        """, (key, data_json, timestamp, category))
        
        conn.commit()
        conn.close()
        
        return {'status': 'stored', 'key': key, 'timestamp': timestamp}
    
    def retrieve_memory(self, key: str):
        conn = sqlite3.connect(self.memory_db)
        cursor = conn.cursor()
        
        cursor.execute('SELECT data, timestamp, category FROM memories WHERE key = ?', (key,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            data, timestamp, category = result
            return {
                'key': key,
                'data': json.loads(data),
                'timestamp': timestamp,
                'category': category
            }
        
        return None
    
    def search_memory(self, query: str):
        conn = sqlite3.connect(self.memory_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT key, data, timestamp, category FROM memories 
            WHERE key LIKE ? OR data LIKE ?
        ''', (f'%{query}%', f'%{query}%'))
        
        results = cursor.fetchall()
        conn.close()
        
        memories = []
        for key, data, timestamp, category in results:
            memories.append({
                'key': key,
                'data': json.loads(data),
                'timestamp': timestamp,
                'category': category
            })
        
        return memories
    
    def get_all_memories(self):
        conn = sqlite3.connect(self.memory_db)
        cursor = conn.cursor()
        
        cursor.execute('SELECT key, timestamp, category FROM memories ORDER BY timestamp DESC')
        results = cursor.fetchall()
        
        conn.close()
        
        return [{'key': key, 'timestamp': timestamp, 'category': category} for key, timestamp, category in results]
'''
        
        return self.save_agent('enhanced_memory_agent', default_code)
    
    def fix_all_agents(self) -> Dict[str, str]:
        """إصلاح جميع الوكلاء"""
        print("🚀 إصلاح سريع لجميع الوكلاء")
        print("=" * 50)
        
        results = {}
        
        # إصلاح الوكلاء
        agents = [
            ('ProjectAnalyzerAgent', self.fix_project_analyzer),
            ('FileOrganizerAgent', self.fix_file_organizer),
            ('MemoryAgent', self.fix_memory_agent)
        ]
        
        for agent_name, fix_func in agents:
            try:
                file_path = fix_func()
                if file_path:
                    results[agent_name] = file_path
                    print(f"✅ {agent_name}: تم الإصلاح")
                else:
                    print(f"❌ {agent_name}: فشل الإصلاح")
            except Exception as e:
                print(f"❌ خطأ في {agent_name}: {e}")
        
        return results


def main():
    """الدالة الرئيسية"""
    print("⚡ إصلاح سريع للوكلاء مع Gemini CLI")
    print("=" * 50)
    
    fixer = QuickGeminiFix()
    results = fixer.fix_all_agents()
    
    print(f"\n🏆 النتائج:")
    for agent, path in results.items():
        print(f"   ✅ {agent}: {path}")
    
    print(f"\n⚡ تم إصلاح {len(results)} وكيل بنجاح!")
    return 0 if len(results) > 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
