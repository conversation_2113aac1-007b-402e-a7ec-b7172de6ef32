#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌟 فاحص النظام الشامل واسع المدى
Ultra Comprehensive System Scanner

فحص شامل وواسع المدى لجميع جوانب النظام مع فريق حورس
Ultra comprehensive and wide-range scan of all system aspects with Horus team
"""

import os
import sys
import json
import subprocess
import winreg
import psutil
import socket
import platform
import glob
import shutil
from pathlib import Path
from datetime import datetime
import threading
import time

class UltraComprehensiveSystemScanner:
    def __init__(self):
        self.scan_results = {
            'scan_metadata': {
                'scan_date': datetime.now().isoformat(),
                'scan_type': 'Ultra Comprehensive Wide-Range Scan',
                'horus_team_consultation': True,
                'scan_duration': None
            },
            'system_overview': {},
            'drives_analysis': {},
            'windows_services': {},
            'registry_analysis': {},
            'network_analysis': {},
            'processes_analysis': {},
            'environment_variables': {},
            'installed_programs': {},
            'hidden_tools': {},
            'security_analysis': {},
            'cloud_tools': {},
            'virtualization': {},
            'development_environments': {},
            'statistics': {}
        }
        
        self.start_time = time.time()

    def get_system_overview(self):
        """جمع نظرة عامة شاملة عن النظام"""
        print("🖥️ جمع نظرة عامة شاملة عن النظام...")
        
        try:
            # معلومات النظام الأساسية
            system_info = {
                'os': platform.system(),
                'os_version': platform.version(),
                'os_release': platform.release(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node_name': platform.node(),
                'python_version': platform.python_version(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
            
            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            system_info['memory'] = {
                'total': f"{memory.total / (1024**3):.2f} GB",
                'available': f"{memory.available / (1024**3):.2f} GB",
                'used': f"{memory.used / (1024**3):.2f} GB",
                'percentage': f"{memory.percent}%"
            }
            
            # معلومات المعالج
            system_info['cpu'] = {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'current_frequency': f"{psutil.cpu_freq().current:.2f} MHz" if psutil.cpu_freq() else "Unknown",
                'usage_percent': f"{psutil.cpu_percent(interval=1)}%"
            }
            
            self.scan_results['system_overview'] = system_info
            print(f"✅ تم جمع معلومات النظام: {system_info['os']} {system_info['os_version']}")
            
        except Exception as e:
            print(f"⚠️ خطأ في جمع معلومات النظام: {e}")

    def analyze_all_drives(self):
        """تحليل جميع الأقراص المتاحة"""
        print("💾 تحليل جميع الأقراص المتاحة...")
        
        drives_info = {}
        
        try:
            # الحصول على جميع الأقراص
            partitions = psutil.disk_partitions()
            
            for partition in partitions:
                drive_letter = partition.device
                print(f"   🔍 فحص القرص: {drive_letter}")
                
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    
                    drive_info = {
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'file_system': partition.fstype,
                        'total_size': f"{usage.total / (1024**3):.2f} GB",
                        'used_size': f"{usage.used / (1024**3):.2f} GB",
                        'free_size': f"{usage.free / (1024**3):.2f} GB",
                        'usage_percent': f"{(usage.used / usage.total) * 100:.1f}%",
                        'development_tools': []
                    }
                    
                    # البحث عن أدوات التطوير في كل قرص
                    if os.path.exists(partition.mountpoint):
                        dev_tools = self.scan_drive_for_dev_tools(partition.mountpoint)
                        drive_info['development_tools'] = dev_tools
                    
                    drives_info[drive_letter] = drive_info
                    
                except PermissionError:
                    drives_info[drive_letter] = {
                        'device': partition.device,
                        'status': 'Access Denied',
                        'file_system': partition.fstype
                    }
                    
            self.scan_results['drives_analysis'] = drives_info
            print(f"✅ تم تحليل {len(drives_info)} قرص")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل الأقراص: {e}")

    def scan_drive_for_dev_tools(self, drive_path):
        """فحص قرص محدد للبحث عن أدوات التطوير"""
        dev_tools = []
        
        # مجلدات شائعة لأدوات التطوير
        common_dev_folders = [
            'Program Files', 'Program Files (x86)', 'ProgramData',
            'tools', 'dev', 'development', 'sdk', 'bin', 'usr'
        ]
        
        try:
            for folder in common_dev_folders:
                folder_path = os.path.join(drive_path, folder)
                if os.path.exists(folder_path):
                    # البحث عن أدوات محددة
                    for root, dirs, files in os.walk(folder_path):
                        # تحديد عمق البحث لتجنب البحث المفرط
                        level = root.replace(folder_path, '').count(os.sep)
                        if level > 3:
                            dirs.clear()
                            continue
                        
                        # البحث عن ملفات تنفيذية مهمة
                        for file in files:
                            if file.lower() in [
                                'python.exe', 'node.exe', 'java.exe', 'git.exe',
                                'docker.exe', 'go.exe', 'rustc.exe', 'dotnet.exe'
                            ]:
                                dev_tools.append({
                                    'tool': file,
                                    'path': os.path.join(root, file)
                                })
                                
        except Exception as e:
            pass  # تجاهل الأخطاء في الوصول للملفات
        
        return dev_tools[:20]  # تحديد العدد لتجنب القوائم الطويلة

    def analyze_windows_services(self):
        """تحليل خدمات Windows"""
        print("⚙️ تحليل خدمات Windows...")
        
        services_info = {
            'total_services': 0,
            'running_services': 0,
            'stopped_services': 0,
            'development_related': [],
            'security_related': [],
            'cloud_related': []
        }
        
        try:
            services = psutil.win_service_iter()
            
            dev_keywords = ['docker', 'git', 'node', 'python', 'java', 'mysql', 'postgres', 'mongodb']
            security_keywords = ['antivirus', 'firewall', 'security', 'defender', 'malware']
            cloud_keywords = ['aws', 'azure', 'google', 'cloud', 'dropbox', 'onedrive']
            
            for service in services:
                try:
                    service_info = service.as_dict()
                    services_info['total_services'] += 1
                    
                    if service_info['status'] == 'running':
                        services_info['running_services'] += 1
                    else:
                        services_info['stopped_services'] += 1
                    
                    service_name = service_info['name'].lower()
                    service_display = service_info.get('display_name', '').lower()
                    
                    # تصنيف الخدمات
                    if any(keyword in service_name or keyword in service_display for keyword in dev_keywords):
                        services_info['development_related'].append({
                            'name': service_info['name'],
                            'display_name': service_info.get('display_name', ''),
                            'status': service_info['status']
                        })
                    
                    if any(keyword in service_name or keyword in service_display for keyword in security_keywords):
                        services_info['security_related'].append({
                            'name': service_info['name'],
                            'display_name': service_info.get('display_name', ''),
                            'status': service_info['status']
                        })
                    
                    if any(keyword in service_name or keyword in service_display for keyword in cloud_keywords):
                        services_info['cloud_related'].append({
                            'name': service_info['name'],
                            'display_name': service_info.get('display_name', ''),
                            'status': service_info['status']
                        })
                        
                except Exception:
                    continue
            
            self.scan_results['windows_services'] = services_info
            print(f"✅ تم تحليل {services_info['total_services']} خدمة")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل الخدمات: {e}")

    def deep_registry_analysis(self):
        """تحليل عميق لسجل Windows"""
        print("📋 تحليل عميق لسجل Windows...")
        
        registry_info = {
            'installed_programs': {},
            'development_tools': {},
            'startup_programs': {},
            'environment_variables': {},
            'uninstall_entries': 0
        }
        
        try:
            # فحص البرامج المثبتة
            uninstall_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
            ]
            
            dev_keywords = [
                'visual studio', 'intellij', 'eclipse', 'android studio', 'unity',
                'python', 'node', 'java', 'git', 'docker', 'mysql', 'postgres',
                'mongodb', 'redis', 'nginx', 'apache', 'xampp', 'wamp'
            ]
            
            for hkey, subkey_path in uninstall_keys:
                try:
                    with winreg.OpenKey(hkey, subkey_path) as key:
                        for i in range(winreg.QueryInfoKey(key)[0]):
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                with winreg.OpenKey(key, subkey_name) as subkey:
                                    try:
                                        display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                        registry_info['uninstall_entries'] += 1
                                        
                                        # البحث عن أدوات التطوير
                                        if any(keyword in display_name.lower() for keyword in dev_keywords):
                                            try:
                                                version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                            except:
                                                version = "Unknown"
                                            
                                            try:
                                                install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                            except:
                                                install_location = "Unknown"
                                            
                                            registry_info['development_tools'][display_name] = {
                                                'version': version,
                                                'install_location': install_location,
                                                'registry_key': subkey_name
                                            }
                                            
                                    except:
                                        continue
                            except:
                                continue
                except Exception:
                    continue
            
            self.scan_results['registry_analysis'] = registry_info
            print(f"✅ تم تحليل {registry_info['uninstall_entries']} إدخال في السجل")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل السجل: {e}")

    def analyze_network_and_connections(self):
        """تحليل الشبكة والاتصالات"""
        print("🌐 تحليل الشبكة والاتصالات...")
        
        network_info = {
            'network_interfaces': {},
            'active_connections': [],
            'listening_ports': [],
            'network_stats': {}
        }
        
        try:
            # معلومات واجهات الشبكة
            interfaces = psutil.net_if_addrs()
            for interface_name, addresses in interfaces.items():
                interface_info = []
                for addr in addresses:
                    interface_info.append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
                network_info['network_interfaces'][interface_name] = interface_info
            
            # الاتصالات النشطة
            connections = psutil.net_connections(kind='inet')
            for conn in connections[:50]:  # تحديد العدد
                if conn.status == 'ESTABLISHED':
                    network_info['active_connections'].append({
                        'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
                        'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A",
                        'status': conn.status,
                        'pid': conn.pid
                    })
            
            # المنافذ المستمعة
            for conn in connections:
                if conn.status == 'LISTEN':
                    network_info['listening_ports'].append({
                        'address': f"{conn.laddr.ip}:{conn.laddr.port}",
                        'pid': conn.pid
                    })
            
            # إحصائيات الشبكة
            net_stats = psutil.net_io_counters()
            network_info['network_stats'] = {
                'bytes_sent': f"{net_stats.bytes_sent / (1024**2):.2f} MB",
                'bytes_received': f"{net_stats.bytes_recv / (1024**2):.2f} MB",
                'packets_sent': net_stats.packets_sent,
                'packets_received': net_stats.packets_recv
            }
            
            self.scan_results['network_analysis'] = network_info
            print(f"✅ تم تحليل {len(network_info['network_interfaces'])} واجهة شبكة")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل الشبكة: {e}")

    def analyze_running_processes(self):
        """تحليل العمليات النشطة"""
        print("⚡ تحليل العمليات النشطة...")
        
        processes_info = {
            'total_processes': 0,
            'development_processes': [],
            'high_cpu_processes': [],
            'high_memory_processes': [],
            'suspicious_processes': []
        }
        
        try:
            dev_keywords = [
                'python', 'node', 'java', 'git', 'docker', 'code', 'studio',
                'mysql', 'postgres', 'mongo', 'redis', 'nginx', 'apache'
            ]
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'cmdline']):
                try:
                    processes_info['total_processes'] += 1
                    proc_info = proc.info
                    
                    # العمليات المتعلقة بالتطوير
                    if any(keyword in proc_info['name'].lower() for keyword in dev_keywords):
                        processes_info['development_processes'].append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cpu_percent': proc_info['cpu_percent'],
                            'memory_percent': proc_info['memory_percent']
                        })
                    
                    # العمليات عالية استهلاك المعالج
                    if proc_info['cpu_percent'] and proc_info['cpu_percent'] > 5.0:
                        processes_info['high_cpu_processes'].append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cpu_percent': proc_info['cpu_percent']
                        })
                    
                    # العمليات عالية استهلاك الذاكرة
                    if proc_info['memory_percent'] and proc_info['memory_percent'] > 2.0:
                        processes_info['high_memory_processes'].append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'memory_percent': proc_info['memory_percent']
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # ترتيب القوائم
            processes_info['high_cpu_processes'].sort(key=lambda x: x['cpu_percent'], reverse=True)
            processes_info['high_memory_processes'].sort(key=lambda x: x['memory_percent'], reverse=True)
            
            # تحديد العدد
            processes_info['high_cpu_processes'] = processes_info['high_cpu_processes'][:10]
            processes_info['high_memory_processes'] = processes_info['high_memory_processes'][:10]
            
            self.scan_results['processes_analysis'] = processes_info
            print(f"✅ تم تحليل {processes_info['total_processes']} عملية")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل العمليات: {e}")

    def comprehensive_environment_scan(self):
        """فحص شامل لمتغيرات البيئة"""
        print("🌍 فحص شامل لمتغيرات البيئة...")
        
        env_info = {
            'total_variables': 0,
            'development_variables': {},
            'path_analysis': {
                'total_paths': 0,
                'development_paths': [],
                'duplicate_paths': [],
                'invalid_paths': []
            },
            'system_variables': {},
            'user_variables': {}
        }
        
        try:
            # جميع متغيرات البيئة
            all_env = dict(os.environ)
            env_info['total_variables'] = len(all_env)
            
            # متغيرات التطوير المهمة
            dev_vars = [
                'JAVA_HOME', 'PYTHON_HOME', 'NODE_PATH', 'GOPATH', 'GOROOT',
                'CARGO_HOME', 'RUSTUP_HOME', 'COMPOSER_HOME', 'GEM_HOME',
                'ANDROID_HOME', 'ANDROID_SDK_ROOT', 'FLUTTER_ROOT'
            ]
            
            for var in dev_vars:
                if var in all_env:
                    env_info['development_variables'][var] = all_env[var]
            
            # تحليل PATH
            path_env = all_env.get('PATH', '')
            paths = [p.strip() for p in path_env.split(';') if p.strip()]
            env_info['path_analysis']['total_paths'] = len(paths)
            
            # مسارات التطوير
            dev_keywords = [
                'python', 'node', 'java', 'git', 'docker', 'go', 'rust',
                'mysql', 'postgres', 'mongo', 'redis', 'nginx', 'apache'
            ]
            
            seen_paths = set()
            for path in paths:
                # التحقق من صحة المسار
                if not os.path.exists(path):
                    env_info['path_analysis']['invalid_paths'].append(path)
                
                # البحث عن مسارات التطوير
                if any(keyword in path.lower() for keyword in dev_keywords):
                    env_info['path_analysis']['development_paths'].append(path)
                
                # البحث عن المسارات المكررة
                if path.lower() in seen_paths:
                    env_info['path_analysis']['duplicate_paths'].append(path)
                else:
                    seen_paths.add(path.lower())
            
            self.scan_results['environment_variables'] = env_info
            print(f"✅ تم تحليل {env_info['total_variables']} متغير بيئة")
            
        except Exception as e:
            print(f"⚠️ خطأ في تحليل متغيرات البيئة: {e}")

    def detect_virtualization_and_containers(self):
        """اكتشاف المحاكاة الافتراضية والحاويات"""
        print("🔍 اكتشاف المحاكاة الافتراضية والحاويات...")
        
        virt_info = {
            'virtualization_detected': False,
            'virtualization_type': 'Unknown',
            'docker_info': {},
            'wsl_info': {},
            'hyperv_info': {},
            'vmware_info': {}
        }
        
        try:
            # فحص Docker
            try:
                result = subprocess.run(['docker', '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    virt_info['docker_info']['installed'] = True
                    virt_info['docker_info']['version'] = result.stdout.strip()
                    
                    # فحص الحاويات النشطة
                    result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        containers = result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                        virt_info['docker_info']['running_containers'] = len(containers)
                else:
                    virt_info['docker_info']['installed'] = False
            except:
                virt_info['docker_info']['installed'] = False
            
            # فحص WSL
            if os.path.exists('C:\\Windows\\System32\\wsl.exe'):
                virt_info['wsl_info']['installed'] = True
                try:
                    result = subprocess.run(['wsl', '--list'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        virt_info['wsl_info']['distributions'] = result.stdout.strip()
                except:
                    pass
            else:
                virt_info['wsl_info']['installed'] = False
            
            # فحص Hyper-V
            try:
                result = subprocess.run(['powershell', 'Get-WindowsOptionalFeature', '-Online', '-FeatureName', 'Microsoft-Hyper-V-All'], 
                                      capture_output=True, text=True, timeout=10)
                if 'Enabled' in result.stdout:
                    virt_info['hyperv_info']['enabled'] = True
                else:
                    virt_info['hyperv_info']['enabled'] = False
            except:
                virt_info['hyperv_info']['enabled'] = False
            
            self.scan_results['virtualization'] = virt_info
            print("✅ تم فحص أدوات المحاكاة الافتراضية")
            
        except Exception as e:
            print(f"⚠️ خطأ في فحص المحاكاة الافتراضية: {e}")

    def generate_comprehensive_statistics(self):
        """إنشاء إحصائيات شاملة"""
        print("📊 إنشاء إحصائيات شاملة...")
        
        end_time = time.time()
        scan_duration = end_time - self.start_time
        
        stats = {
            'scan_duration_seconds': round(scan_duration, 2),
            'scan_duration_formatted': f"{int(scan_duration // 60)}m {int(scan_duration % 60)}s",
            'total_drives_scanned': len(self.scan_results.get('drives_analysis', {})),
            'total_services_analyzed': self.scan_results.get('windows_services', {}).get('total_services', 0),
            'total_processes_analyzed': self.scan_results.get('processes_analysis', {}).get('total_processes', 0),
            'total_registry_entries': self.scan_results.get('registry_analysis', {}).get('uninstall_entries', 0),
            'total_environment_variables': self.scan_results.get('environment_variables', {}).get('total_variables', 0),
            'development_tools_found': len(self.scan_results.get('registry_analysis', {}).get('development_tools', {})),
            'network_interfaces': len(self.scan_results.get('network_analysis', {}).get('network_interfaces', {}))
        }
        
        self.scan_results['statistics'] = stats
        self.scan_results['scan_metadata']['scan_duration'] = scan_duration
        
        print(f"✅ تم إنشاء الإحصائيات - مدة الفحص: {stats['scan_duration_formatted']}")

    def save_ultra_comprehensive_results(self, output_dir='SHARED_REQUIREMENTS'):
        """حفظ النتائج الشاملة"""
        print("💾 حفظ النتائج الشاملة...")
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # حفظ النتائج الكاملة
        results_file = output_path / 'ultra_comprehensive_system_scan.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        # إنشاء تقرير مختصر
        summary_file = output_path / 'ultra_scan_summary.txt'
        with open(summary_file, 'w', encoding='utf-8') as f:
            self.write_summary_report(f)
        
        print(f"✅ تم حفظ النتائج في: {output_path}")

    def write_summary_report(self, f):
        """كتابة تقرير مختصر"""
        stats = self.scan_results['statistics']
        
        f.write("🌟 تقرير الفحص الشامل واسع المدى\n")
        f.write("=" * 60 + "\n")
        f.write(f"تاريخ الفحص: {self.scan_results['scan_metadata']['scan_date']}\n")
        f.write(f"مدة الفحص: {stats['scan_duration_formatted']}\n\n")
        
        f.write("📊 الإحصائيات الرئيسية:\n")
        f.write(f"   💾 أقراص مفحوصة: {stats['total_drives_scanned']}\n")
        f.write(f"   ⚙️ خدمات محللة: {stats['total_services_analyzed']}\n")
        f.write(f"   ⚡ عمليات محللة: {stats['total_processes_analyzed']}\n")
        f.write(f"   📋 إدخالات السجل: {stats['total_registry_entries']}\n")
        f.write(f"   🌍 متغيرات البيئة: {stats['total_environment_variables']}\n")
        f.write(f"   🛠️ أدوات التطوير: {stats['development_tools_found']}\n")
        f.write(f"   🌐 واجهات الشبكة: {stats['network_interfaces']}\n")

def main():
    """الدالة الرئيسية"""
    print("🌟 فاحص النظام الشامل واسع المدى")
    print("=" * 60)
    print("👁️ بالتعاون مع فريق حورس")
    print("=" * 60)
    
    scanner = UltraComprehensiveSystemScanner()
    
    # تنفيذ الفحص الشامل
    scanner.get_system_overview()
    scanner.analyze_all_drives()
    scanner.analyze_windows_services()
    scanner.deep_registry_analysis()
    scanner.analyze_network_and_connections()
    scanner.analyze_running_processes()
    scanner.comprehensive_environment_scan()
    scanner.detect_virtualization_and_containers()
    
    # إنشاء الإحصائيات وحفظ النتائج
    scanner.generate_comprehensive_statistics()
    scanner.save_ultra_comprehensive_results()
    
    # عرض النتائج النهائية
    stats = scanner.scan_results['statistics']
    print("\n🎯 نتائج الفحص الشامل:")
    print(f"   ⏱️ مدة الفحص: {stats['scan_duration_formatted']}")
    print(f"   💾 أقراص مفحوصة: {stats['total_drives_scanned']}")
    print(f"   ⚙️ خدمات محللة: {stats['total_services_analyzed']}")
    print(f"   ⚡ عمليات محللة: {stats['total_processes_analyzed']}")
    print(f"   🛠️ أدوات التطوير: {stats['development_tools_found']}")
    
    print("\n🌟 تم إكمال الفحص الشامل واسع المدى بنجاح!")
    return scanner.scan_results

if __name__ == "__main__":
    main()
