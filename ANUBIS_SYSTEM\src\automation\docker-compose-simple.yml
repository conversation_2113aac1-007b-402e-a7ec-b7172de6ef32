version: '3.8'

services:
  anubis-n8n-simple:
    image: n8nio/n8n:latest
    container_name: anubis-n8n-simple
    restart: unless-stopped
    
    # المنافذ
    ports:
      - "5678:5678"
    
    # الأحجام
    volumes:
      - anubis-n8n-data:/home/<USER>/.n8n
      - ./n8n_1/workflows:/home/<USER>/.n8n/workflows
    
    # متغيرات البيئة
    environment:
      - N8N_ENCRYPTION_KEY=anubis_secure_key_2024_n8n_workflows
      - N8N_USER_MANAGEMENT_DISABLED=false
      - N8N_SECURE_COOKIE=false
      - N8N_PROTOCOL=http
      - N8N_PORT=5678
      - N8N_LISTEN_ADDRESS=0.0.0.0
      - WEBHOOK_URL=http://localhost:5678
      - N8N_METRICS=true
      - N8N_LOG_LEVEL=info
      - DB_TYPE=sqlite
      - GENERIC_TIMEZONE=UTC
    
    # فحص الصحة
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# الأحجام
volumes:
  anubis-n8n-data:
    driver: local
