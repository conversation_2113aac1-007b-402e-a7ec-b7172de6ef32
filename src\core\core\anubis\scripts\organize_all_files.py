#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ منظم الملفات الشامل - نظام أنوبيس
Comprehensive File Organizer - Anubis System

ينظم جميع الملفات وينقلها إلى المجلدات المناسبة
"""

import shutil
from datetime import datetime
from pathlib import Path


class AnubisFileOrganizer:
    """🗂️ منظم ملفات نظام أنوبيس"""

    def __init__(self):
        self.base_path = Path(".")
        self.moved_files = []
        self.errors = []

        print("🗂️ منظم الملفات الشامل - نظام أنوبيس")
        print("=" * 50)

    def organize_files(self):
        """تنظيم جميع الملفات"""
        print("🚀 بدء تنظيم الملفات...")

        # قائمة الملفات والمجلدات المستهدفة للنقل
        file_moves = [
            # ملفات قاعدة البيانات
            ("test_db_connection.py", "database/tests/"),
            ("final_db_test.py", "database/tests/"),
            # ملفات التقارير الرئيسية
            ("FINAL_PROJECT_REPORT.md", "docs/"),
            ("PROJECT_STRUCTURE_README.md", "docs/"),
            ("README_NEW.md", "docs/"),
            # ملفات التكوين والسكريبتات
            ("create_all_readmes.py", "scripts/"),
            ("readme_generation_report.json", "reports/"),
            ("setup_langsmith_env.ps1", "scripts/"),
            # ملفات مجلد scripts المتناثرة
            ("scripts/agents/", "archive/old_files/"),
            ("scripts/backup/", "archive/old_files/"),
            ("scripts/configs/", "archive/old_files/"),
            ("scripts/core/", "archive/old_files/"),
            ("scripts/docs/", "archive/old_files/"),
            ("scripts/examples/", "archive/old_files/"),
            ("scripts/logs/", "archive/old_files/"),
            ("scripts/reports/", "archive/old_files/"),
            ("scripts/scripts/", "archive/old_files/"),
            ("scripts/temp/", "archive/old_files/"),
            ("scripts/tests/", "archive/old_files/"),
            ("scripts/tools/", "archive/old_files/"),
        ]

        # تنفيذ عمليات النقل
        for source, destination in file_moves:
            self.move_file_or_folder(source, destination)

        # تنظيف المجلدات الفارغة
        self.cleanup_empty_folders()

        # إنشاء تقرير
        self.create_organization_report()

        print(f"\n✅ تم تنظيم {len(self.moved_files)} عنصر")
        if self.errors:
            print(f"⚠️ {len(self.errors)} خطأ حدث")

    def move_file_or_folder(self, source_path, dest_folder):
        """نقل ملف أو مجلد"""
        source = self.base_path / source_path
        dest_dir = self.base_path / dest_folder

        if not source.exists():
            print(f"   ⏭️ تجاهل: {source_path} (غير موجود)")
            return

        # إنشاء المجلد المستهدف إذا لم يكن موجوداً
        dest_dir.mkdir(parents=True, exist_ok=True)

        # تحديد المسار النهائي
        dest_path = dest_dir / source.name

        try:
            if source.is_file():
                # نقل ملف
                if dest_path.exists():
                    # إنشاء نسخة احتياطية
                    backup_path = dest_path.with_suffix(
                        f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}{dest_path.suffix}"
                    )
                    shutil.move(str(dest_path), str(backup_path))
                    print(f"   📋 نسخة احتياطية: {backup_path.name}")

                shutil.move(str(source), str(dest_path))
                print(f"   ✅ نُقل ملف: {source_path} → {dest_folder}")

            elif source.is_dir():
                # نقل مجلد
                if dest_path.exists():
                    # دمج المحتويات
                    self.merge_directories(source, dest_path)
                    shutil.rmtree(str(source))
                    print(f"   ✅ دُمج مجلد: {source_path} → {dest_folder}")
                else:
                    shutil.move(str(source), str(dest_path))
                    print(f"   ✅ نُقل مجلد: {source_path} → {dest_folder}")

            self.moved_files.append((source_path, dest_folder))

        except Exception as e:
            error_msg = f"خطأ في نقل {source_path}: {e}"
            print(f"   ❌ {error_msg}")
            self.errors.append(error_msg)

    def merge_directories(self, source_dir, dest_dir):
        """دمج محتويات مجلدين"""
        for item in source_dir.iterdir():
            dest_item = dest_dir / item.name

            if item.is_file():
                if dest_item.exists():
                    # إنشاء نسخة احتياطية
                    backup_name = f"{item.stem}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}{item.suffix}"
                    backup_path = dest_dir / backup_name
                    shutil.copy2(str(dest_item), str(backup_path))

                shutil.copy2(str(item), str(dest_item))

            elif item.is_dir():
                if dest_item.exists():
                    self.merge_directories(item, dest_item)
                else:
                    shutil.copytree(str(item), str(dest_item))

    def cleanup_empty_folders(self):
        """تنظيف المجلدات الفارغة"""
        print("\n🧹 تنظيف المجلدات الفارغة...")

        def remove_empty_dirs(path):
            """إزالة المجلدات الفارغة بشكل تكراري"""
            if not path.is_dir():
                return

            # تنظيف المجلدات الفرعية أولاً
            for subdir in path.iterdir():
                if subdir.is_dir():
                    remove_empty_dirs(subdir)

            # إزالة المجلد إذا كان فارغاً
            try:
                if not any(path.iterdir()):
                    path.rmdir()
                    print(f"   🗑️ تم حذف مجلد فارغ: {path}")
            except OSError:
                pass  # المجلد غير فارغ أو لا يمكن حذفه

        # تنظيف مجلد scripts من المجلدات الفارغة
        scripts_path = self.base_path / "scripts"
        if scripts_path.exists():
            remove_empty_dirs(scripts_path)

    def organize_readme_files(self):
        """تنظيم ملفات README المحسنة"""
        print("\n📚 تنظيم ملفات README...")

        readme_moves = [
            # نقل ملفات README المحسنة إلى مكانها الصحيح
            ("tests/README_COMPREHENSIVE.md", "tests/README.md"),
            ("scripts/README_ENHANCED.md", "scripts/README.md"),
        ]

        for source_path, dest_path in readme_moves:
            source = self.base_path / source_path
            dest = self.base_path / dest_path

            if source.exists():
                try:
                    # إنشاء نسخة احتياطية من الملف الأصلي
                    if dest.exists():
                        backup_path = dest.with_suffix(
                            f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
                        )
                        shutil.move(str(dest), str(backup_path))
                        print(f"   📋 نسخة احتياطية: {backup_path.name}")

                    # نقل الملف المحسن
                    shutil.move(str(source), str(dest))
                    print(f"   ✅ تم تحديث: {dest_path}")

                except Exception as e:
                    print(f"   ❌ خطأ في تحديث {dest_path}: {e}")

    def create_organization_report(self):
        """إنشاء تقرير التنظيم"""
        print("\n📊 إنشاء تقرير التنظيم...")

        report = {
            "timestamp": datetime.now().isoformat(),
            "moved_files": self.moved_files,
            "errors": self.errors,
            "summary": {
                "total_moved": len(self.moved_files),
                "total_errors": len(self.errors),
                "success_rate": (
                    len(self.moved_files) / (len(self.moved_files) + len(self.errors)) * 100
                    if (len(self.moved_files) + len(self.errors)) > 0
                    else 100
                ),
            },
        }

        # حفظ التقرير
        report_path = (
            self.base_path
            / "reports"
            / f"file_organization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )

        try:
            import json

            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"   ✅ تم حفظ التقرير: {report_path}")
        except Exception as e:
            print(f"   ❌ فشل حفظ التقرير: {e}")

    def create_final_structure_summary(self):
        """إنشاء ملخص الهيكل النهائي"""
        print("\n📋 إنشاء ملخص الهيكل النهائي...")

        structure = {}

        # فحص المجلدات الرئيسية
        main_folders = [
            "core",
            "agents",
            "tests",
            "scripts",
            "docs",
            "configs",
            "reports",
            "logs",
            "backup",
            "database",
            "temp",
            "examples",
            "tools",
            "workspace",
            "templates",
            "plugins",
            "archive",
        ]

        for folder in main_folders:
            folder_path = self.base_path / folder
            if folder_path.exists():
                file_count = len(list(folder_path.rglob("*")))
                structure[folder] = {
                    "exists": True,
                    "total_items": file_count,
                    "has_readme": (folder_path / "README.md").exists(),
                }
            else:
                structure[folder] = {"exists": False}

        # طباعة الملخص
        print("\n📊 هيكل المشروع النهائي:")
        print("=" * 40)

        for folder, info in structure.items():
            if info["exists"]:
                readme_status = "✅" if info.get("has_readme") else "❌"
                print(f"📁 {folder:15} | {info['total_items']:3} عنصر | README: {readme_status}")
            else:
                print(f"❌ {folder:15} | غير موجود")

        return structure


def main():
    """الدالة الرئيسية"""
    organizer = AnubisFileOrganizer()

    # تنظيم الملفات
    organizer.organize_files()

    # تنظيم ملفات README
    organizer.organize_readme_files()

    # إنشاء ملخص الهيكل النهائي
    structure = organizer.create_final_structure_summary()

    print("\n" + "=" * 50)
    print("🏆 تم تنظيم الملفات بنجاح!")
    print("=" * 50)

    print(f"✅ الملفات المنقولة: {len(organizer.moved_files)}")
    print(f"❌ الأخطاء: {len(organizer.errors)}")

    if organizer.errors:
        print("\n⚠️ الأخطاء التي حدثت:")
        for error in organizer.errors:
            print(f"   - {error}")

    print("\n🏺 تنظيم ملفات نظام أنوبيس مكتمل!")

    return len(organizer.errors) == 0


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
