{"timestamp": "2025-07-16T06:49:02.287198", "test_type": "comprehensive_system_test", "summary": {"total_agents_tested": 5, "working_agents": 1, "ai_enabled_agents": 1, "total_models_tested": 3, "working_models": 3, "system_health": "يحتاج تحسين"}, "detailed_results": {"agents": {"error_detector": {"status": "error", "error": "No module named 'agents.error_detector'", "working": false}, "project_analyzer": {"status": "error", "error": "No module named 'agents.project_analyzer'", "working": false}, "file_organizer": {"status": "error", "error": "No module named 'agents.file_organizer'", "working": false}, "memory_agent": {"status": "error", "error": "'MemoryAgent' object has no attribute 'store_memory'", "working": false}, "smart_analyzer": {"status": "success", "ai_enabled": true, "analysis_working": true, "working": true}}, "models": {"llama3:8b": {"available": true, "working": true, "response_time": 5.95, "response_length": 29, "performance": "<PERSON>ي<PERSON>"}, "mistral:7b": {"available": true, "working": true, "response_time": 26.59, "response_length": 190, "performance": "بطيء"}, "phi3:mini": {"available": true, "working": true, "response_time": 9.45, "response_length": 7, "performance": "<PERSON>ي<PERSON>"}}, "collaboration": {"multi_agent_analysis": true, "ai_recommendations": true, "data_sharing": true, "collaborative_intelligence": true}}, "recommendations": ["⚠️ 4 وكلاء تحتاج إصلاح", "🧠 1 وكلاء مدعومين بالذكاء الاصطناعي", "🤖 3 نماذج ذكاء اصطناعي تعمل"]}