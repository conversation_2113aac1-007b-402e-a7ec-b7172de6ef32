alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093
global:
  evaluation_interval: 15s
  scrape_interval: 15s
rule_files:
- rules/*.yml
scrape_configs:
- job_name: anubis_main
  metrics_path: /metrics
  scrape_interval: 10s
  static_configs:
  - targets:
    - anubis_main:8000
- job_name: universal_ai
  metrics_path: /metrics
  scrape_interval: 10s
  static_configs:
  - targets:
    - universal_ai:8001
- job_name: tools_suite
  metrics_path: /metrics
  scrape_interval: 15s
  static_configs:
  - targets:
    - tools_suite:8002
