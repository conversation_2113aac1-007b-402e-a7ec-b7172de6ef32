#!/usr/bin/env python3
# اختبار الوكلاء المحسنة
import sys

sys.path.append("agents")

try:
    from enhanced_file_organizer import EnhancedFileOrganizerAgent
    from enhanced_memory_agent import EnhancedMemoryAgent
    from enhanced_project_analyzer import EnhancedProjectAnalyzerAgent

    print("✅ جميع الوكلاء تم استيرادها بنجاح!")

    # اختبار بسيط
    analyzer = EnhancedProjectAnalyzerAgent(".", {}, False)
    print("✅ ProjectAnalyzerAgent يعمل")

    organizer = EnhancedFileOrganizerAgent(".", {}, False)
    print("✅ FileOrganizerAgent يعمل")

    memory = EnhancedMemoryAgent(".", {}, False)
    print("✅ MemoryAgent يعمل")

except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
