#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 نظام النسخ الاحتياطي الموحد لأنوبيس
Unified Backup System for Anubis
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

class AnubisBackupSystem:
    def __init__(self):
        self.base_path = Path(".")
        self.backup_dir = self.base_path / "archive_and_backups" / "unified_backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # المجلدات المهمة للنسخ الاحتياطي
        self.important_dirs = [
            "anubis_main_system",
            "configs", 
            "database",
            "universal_ai_system",
            "workflows_and_automation",
            "workspace",
            "scripts",
            "reports",
            "isolation_configs"
        ]
    
    def create_full_backup(self):
        """إنشاء نسخة احتياطية كاملة"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"anubis_full_backup_{timestamp}.zip"
        backup_path = self.backup_dir / backup_filename
        
        print(f"💾 إنشاء نسخة احتياطية كاملة: {backup_filename}")
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # نسخ المجلدات المهمة
            for dir_name in self.important_dirs:
                dir_path = self.base_path / dir_name
                if dir_path.exists():
                    print(f"📁 نسخ مجلد: {dir_name}")
                    for item in dir_path.rglob("*"):
                        if item.is_file():
                            arcname = item.relative_to(self.base_path)
                            zipf.write(item, arcname)
            
            # نسخ الملفات المهمة في المجلد الرئيسي
            important_files = [
                ".env.template", ".gitignore", "docker-compose.yml", 
                "Dockerfile", "start_anubis_isolated.sh"
            ]
            
            for filename in important_files:
                file_path = self.base_path / filename
                if file_path.exists():
                    zipf.write(file_path, filename)
        
        backup_size = backup_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_filename} ({backup_size:.1f} MB)")
        
        return backup_path
    
    def create_configs_backup(self):
        """إنشاء نسخة احتياطية للإعدادات فقط"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"anubis_configs_backup_{timestamp}.zip"
        backup_path = self.backup_dir / backup_filename
        
        print(f"⚙️ إنشاء نسخة احتياطية للإعدادات: {backup_filename}")
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            config_dirs = ["configs", "isolation_configs"]
            
            for dir_name in config_dirs:
                dir_path = self.base_path / dir_name
                if dir_path.exists():
                    for item in dir_path.rglob("*"):
                        if item.is_file():
                            arcname = item.relative_to(self.base_path)
                            zipf.write(item, arcname)
        
        print(f"✅ تم إنشاء نسخة احتياطية للإعدادات: {backup_filename}")
        return backup_path
    
    def cleanup_old_backups(self, days_to_keep=30):
        """تنظيف النسخ الاحتياطية القديمة"""
        print(f"🧹 تنظيف النسخ الاحتياطية الأقدم من {days_to_keep} يوم...")
        
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
        removed_count = 0
        
        for backup_file in self.backup_dir.glob("*.zip"):
            if backup_file.stat().st_mtime < cutoff_time:
                backup_file.unlink()
                removed_count += 1
                print(f"🗑️ تم حذف: {backup_file.name}")
        
        print(f"✅ تم حذف {removed_count} نسخة احتياطية قديمة")

def main():
    backup_system = AnubisBackupSystem()
    
    print("💾 نظام النسخ الاحتياطي الموحد لأنوبيس")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية كاملة
    backup_system.create_full_backup()
    
    # إنشاء نسخة احتياطية للإعدادات
    backup_system.create_configs_backup()
    
    # تنظيف النسخ القديمة
    backup_system.cleanup_old_backups()
    
    print("✅ انتهى نظام النسخ الاحتياطي الموحد")

if __name__ == "__main__":
    main()
