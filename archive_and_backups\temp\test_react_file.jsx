
import React, { useState, useEffect } from 'react';

function BadComponent() {
    const [data, setData] = useState([]);
    
    useEffect(() => {
        fetchData();
    }); // useEffect بدون dependencies
    
    const handleClick = () => {
        eval("console.log('clicked')"); // استخدام eval
        document.getElementById('test').innerHTML = userInput; // XSS محتمل
    };
    
    return (
        <div>
            {data.map((item, index) => (
                <div key={index} onClick={handleClick()}> // key بـ index + استدعاء مباشر
                    <span dangerouslySetInnerHTML={{__html: item.content}} />
                </div>
            ))}
        </div>
    );
}

var oldVar = "استخدام var"; // استخدام var
console.log("في الإنتاج"); // console.log

export default BadComponent;
