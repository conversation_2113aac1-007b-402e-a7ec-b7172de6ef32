{"timestamp": "2025-07-16T16:22:46.896588", "system_stats": {"timestamp": "2025-07-16T16:22:46.284449", "cpu_usage": 15.6, "memory_total": 16963534848, "memory_used": 11536617472, "memory_percent": 68.0, "disk_total": 378090287104, "disk_used": 350259748864, "disk_percent": 92.63918191256134, "platform": "Windows"}, "vscode_analysis": {"total_vscode_processes": 56, "total_memory_usage": 21.85987258685767, "total_cpu_usage": 0.0, "process_breakdown": {"Main Process": {"count": 16, "memory_usage": 11.500649042083424, "cpu_usage": 0.0}, "Unknown VS Code Process": {"count": 39, "memory_usage": 10.29878632993745, "cpu_usage": 0.0}, "Tunnel Process": {"count": 1, "memory_usage": 0.060437214836792964, "cpu_usage": 0.0}}, "performance_issues": [], "recommendations": []}, "vscode_processes": [{"pid": 1816, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.6499596280370726, "memory_rss": 110256128, "memory_vms": 86388736, "create_time": 1752672082.0963163, "num_threads": 20, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --video-capture-use-gpu-memory-buffer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=3900 /prefetch:14", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 2640, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.5849829819620388, "memory_rss": 99233792, "memory_vms": 148840448, "create_time": 1752671797.9313457, "num_threads": 14, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y server-perplexity-ask", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 2644, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.1462276360573784, "memory_rss": 24805376, "memory_vms": 12599296, "create_time": 1752672164.9580584, "num_threads": 7, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe scripts/vscode_process_monitor.py", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 3000, "name": "powershell.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.0402512333731258, "memory_rss": 6828032, "memory_vms": 70377472, "create_time": 1752671751.6026921, "num_threads": 10, "cmdline": "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -noexit -command try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}", "exe": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "cwd": "C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.11.2025071501-win32-x64\\python_files\\deactivate\\powershell", "process_type": "Unknown VS Code Process"}, {"pid": 3052, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.0605820903018432, "memory_rss": 10276864, "memory_vms": 148185088, "create_time": 1752671772.2532022, "num_threads": 14, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @playwright/mcp@latest", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 6300, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 1.590491147143249, "memory_rss": 269803520, "memory_vms": 338493440, "create_time": 1752671685.6401608, "num_threads": 25, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=renderer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --app-user-model-id=Microsoft.VisualStudioCode --app-path=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app --enable-sandbox --enable-blink-features=HighlightAPI --disable-blink-features=FontMatchingCTMigration,StandardizedBrowserZoom, --video-capture-use-gpu-memory-buffer --lang=en-US --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=4 --time-ticks-at-unix-epoch=-1752671529202847 --launch-time-ticks=156423839 --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=3120 --vscode-window-config=vscode:87bf36db-abba-421c-9a92-c1c9f7c9db4d /prefetch:1", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 7400, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.03262112554714634, "memory_rss": 5533696, "memory_vms": 54734848, "create_time": 1752671627.033609, "num_threads": 18, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=gpu-process --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.WebExperience_cw5n1h2txyewy\\LocalState\\EBWebView --webview-exe-name=Widgets.exe --webview-exe-version=525.13900.0.0 --embedded-browser-webview=1 --no-pre-read-main-dll --gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --always-read-main-dll --field-trial-handle=1848,i,1389710903226530420,563322084036540388,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=1852 /prefetch:2 /pfhostedapp:32e256e6622ab5612e5cd9ef29802f1b22d67aa6", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 7976, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.05971283751154175, "memory_rss": 10129408, "memory_vms": 13336576, "create_time": 1752671627.0361667, "num_threads": 21, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=utility --utility-sub-type=network.mojom.NetworkService --lang=en-US --service-sandbox-type=none --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.WebExperience_cw5n1h2txyewy\\LocalState\\EBWebView --webview-exe-name=Widgets.exe --webview-exe-version=525.13900.0.0 --embedded-browser-webview=1 --no-pre-read-main-dll --always-read-main-dll --field-trial-handle=1848,i,1389710903226530420,563322084036540388,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=2032 /prefetch:11 /pfhostedapp:32e256e6622ab5612e5cd9ef29802f1b22d67aa6", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 8404, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.015574112492901103, "memory_rss": 2641920, "memory_vms": 107298816, "create_time": 1752671627.2140512, "num_threads": 22, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=renderer --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.WebExperience_cw5n1h2txyewy\\LocalState\\EBWebView --webview-exe-name=Widgets.exe --webview-exe-version=525.13900.0.0 --embedded-browser-webview=1 --no-pre-read-main-dll --video-capture-use-gpu-memory-buffer --lang=en-US --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --js-flags=--expose-gc --ms-user-locale= --time-ticks-at-unix-epoch=-**************** --launch-time-ticks=98003042 --always-read-main-dll --field-trial-handle=1848,i,1389710903226530420,563322084036540388,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=3836 /pfhostedapp:32e256e6622ab5612e5cd9ef29802f1b22d67aa6 /prefetch:1", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 8988, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.5920335879278172, "memory_rss": 100429824, "memory_vms": 150630400, "create_time": 1752671787.3089488, "num_threads": 14, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @modelcontextprotocol/server-google-maps", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 9336, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.012266122707587226, "memory_rss": 2080768, "memory_vms": 56344576, "create_time": 1752671797.4338322, "num_threads": 15, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\2dedcb6c0f67fe6a\\node_modules\\.bin\\\\..\\@modelcontextprotocol\\server-google-maps\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 9648, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.7017043149708511, "memory_rss": 119033856, "memory_vms": 143908864, "create_time": 1752671685.1605504, "num_threads": 15, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=gpu-process --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=1564 /prefetch:2", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 11096, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.3144763428023937, "memory_rss": 53346304, "memory_vms": 78393344, "create_time": 1752671593.6871722, "num_threads": 21, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=gpu-process --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\PC Manager Store\\webview2\\EBWebView --webview-exe-name=MSPCManager.exe --webview-exe-version=******** --embedded-browser-webview=1 --embedded-browser-webview-dpi-awareness=2 --no-pre-read-main-dll --gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --always-read-main-dll --field-trial-handle=1796,i,12904574066887253880,6791644923351000616,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msSingleSignOnOSForPrimaryAccountIsShared,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=1432 /prefetch:2 /pfhostedapp:2842c789b161ca7d1b0b2bbe1fbbf7af4afe23a9", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 11228, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.10619371588182797, "memory_rss": 18014208, "memory_vms": 12075008, "create_time": 1752671593.6891365, "num_threads": 23, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=utility --utility-sub-type=network.mojom.NetworkService --lang=en-US --service-sandbox-type=none --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\PC Manager Store\\webview2\\EBWebView --webview-exe-name=MSPCManager.exe --webview-exe-version=******** --embedded-browser-webview=1 --embedded-browser-webview-dpi-awareness=2 --no-pre-read-main-dll --always-read-main-dll --field-trial-handle=1796,i,12904574066887253880,6791644923351000616,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msSingleSignOnOSForPrimaryAccountIsShared,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=1680 /prefetch:11 /pfhostedapp:2842c789b161ca7d1b0b2bbe1fbbf7af4afe23a9", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 11680, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 3.812929072835657, "memory_rss": 646807552, "memory_vms": 1250803712, "create_time": 1752671687.1807864, "num_threads": 25, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --dns-result-order=ipv4first --experimental-network-inspection --inspect-port=0 --video-capture-use-gpu-memory-buffer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=2448 /prefetch:14", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 12072, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.05459390441309983, "memory_rss": 9261056, "memory_vms": 40296448, "create_time": 1752671775.5887115, "num_threads": 3, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.autopep8-2025.3.11921012\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 12324, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.4857191424917807, "memory_rss": 82395136, "memory_vms": 75653120, "create_time": 1752671593.924019, "num_threads": 22, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=renderer --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\PC Manager Store\\webview2\\EBWebView --webview-exe-name=MSPCManager.exe --webview-exe-version=******** --embedded-browser-webview=1 --embedded-browser-webview-dpi-awareness=2 --no-pre-read-main-dll --video-capture-use-gpu-memory-buffer --lang=en-US --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --js-flags=--expose-gc --ms-user-locale= --time-ticks-at-unix-epoch=-**************** --launch-time-ticks=******** --always-read-main-dll --field-trial-handle=1796,i,12904574066887253880,6791644923351000616,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msSingleSignOnOSForPrimaryAccountIsShared,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=3760 /pfhostedapp:2842c789b161ca7d1b0b2bbe1fbbf7af4afe23a9 /prefetch:1", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 12580, "name": "cpptools.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.06731879942667948, "memory_rss": 11419648, "memory_vms": 32530432, "create_time": 1752671775.45418, "num_threads": 20, "cmdline": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.cpptools-1.26.3-win32-x64\\bin\\cpptools.exe", "exe": "C:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.cpptools-1.26.3-win32-x64\\bin\\cpptools.exe", "cwd": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.cpptools-1.26.3-win32-x64\\bin", "process_type": "Unknown VS Code Process"}, {"pid": 14072, "name": "powershell.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.03807810139737215, "memory_rss": 6459392, "memory_vms": 73465856, "create_time": 1752671702.055246, "num_threads": 10, "cmdline": "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -noexit -command try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}", "exe": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 14400, "name": "pet.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.019896230533566678, "memory_rss": 3375104, "memory_vms": 2600960, "create_time": 1752671701.3207192, "num_threads": 1, "cmdline": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.11.2025071501-win32-x64\\python-env-tools\\bin\\pet.exe server", "exe": "C:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.11.2025071501-win32-x64\\python-env-tools\\bin\\pet.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 15272, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.13741437860015532, "memory_rss": 23310336, "memory_vms": 46882816, "create_time": 1752671734.9628808, "num_threads": 3, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.flake8-2025.2.0\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 15328, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.01045517939445919, "memory_rss": 1773568, "memory_vms": 55050240, "create_time": 1752671760.6471243, "num_threads": 13, "cmdline": "node C:\\Users\\<USER>\\AppData\\Roaming\\npm\\\\node_modules\\@modelcontextprotocol\\server-github\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 15980, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.012145393153378689, "memory_rss": 2060288, "memory_vms": 59342848, "create_time": 1752671787.4451945, "num_threads": 14, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\c35ab75beed40a3c\\node_modules\\.bin\\\\..\\@upstash\\context7-mcp\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 16816, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.03018238855213392, "memory_rss": 5120000, "memory_vms": 8491008, "create_time": 1752671593.7484474, "num_threads": 8, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=utility --utility-sub-type=storage.mojom.StorageService --lang=en-US --service-sandbox-type=service --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\PC Manager Store\\webview2\\EBWebView --webview-exe-name=MSPCManager.exe --webview-exe-version=******** --embedded-browser-webview=1 --embedded-browser-webview-dpi-awareness=2 --no-pre-read-main-dll --always-read-main-dll --field-trial-handle=1796,i,12904574066887253880,6791644923351000616,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msSingleSignOnOSForPrimaryAccountIsShared,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=2388 /prefetch:13 /pfhostedapp:2842c789b161ca7d1b0b2bbe1fbbf7af4afe23a9", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 17144, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.012580019548529418, "memory_rss": 2134016, "memory_vms": 75198464, "create_time": 1752671787.3092308, "num_threads": 14, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\9833c18b2d85bc59\\node_modules\\.bin\\\\..\\@playwright\\mcp\\cli.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 17284, "name": "msedgewebview2.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.011952225866645032, "memory_rss": 2027520, "memory_vms": 10936320, "create_time": 1752671627.0760841, "num_threads": 8, "cmdline": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe --type=utility --utility-sub-type=storage.mojom.StorageService --lang=en-US --service-sandbox-type=service --noerrdialogs --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.WebExperience_cw5n1h2txyewy\\LocalState\\EBWebView --webview-exe-name=Widgets.exe --webview-exe-version=525.13900.0.0 --embedded-browser-webview=1 --no-pre-read-main-dll --always-read-main-dll --field-trial-handle=1848,i,1389710903226530420,563322084036540388,262144 --enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msWebView2NoTabForScreenShare,msWindowsTaskManager --disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutofillEnableEdgeSuggestions,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillShowDeployedPassword,msEdgeCaptureSelectionInPDF,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeScreenshotUI,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebCapture,msEdgeWebCaptureUniformExperience,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLlmConsumerDlpPurview,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msNumberOfSitesToPin,msNurturingSitePinningWithWindowsConsent,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSitePinningWithoutUi,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsUserActivities,msZipPayVirtualCard --variations-seed-version --mojo-platform-channel-handle=2100 /prefetch:13 /pfhostedapp:32e256e6622ab5612e5cd9ef29802f1b22d67aa6", "exe": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\msedgewebview2.exe", "cwd": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83", "process_type": "Unknown VS Code Process"}, {"pid": 18280, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.570447143635331, "memory_rss": 96768000, "memory_vms": 148217856, "create_time": 1752671730.402551, "num_threads": 13, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @modelcontextprotocol/server-github", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 18348, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.8680696406702132, "memory_rss": 147255296, "memory_vms": 258379776, "create_time": 1752671684.0659797, "num_threads": 58, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 18480, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.1238685226179576, "memory_rss": 21012480, "memory_vms": 17559552, "create_time": 1752671685.214582, "num_threads": 20, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=utility --utility-sub-type=network.mojom.NetworkService --lang=en-US --service-sandbox-type=none --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=1996 /prefetch:11", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 18560, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.4755778599382638, "memory_rss": 80674816, "memory_vms": 32690176, "create_time": 1752671875.2070603, "num_threads": 8, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\markdown-language-features\\dist\\serverWorkerMain --node-ipc --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Main Process"}, {"pid": 19500, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.013570001893039411, "memory_rss": 2301952, "memory_vms": 11096064, "create_time": 1752671684.9832292, "num_threads": 7, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=crashpad-handler --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code /prefetch:4 --no-rate-limit --monitor-self-annotation=ptype=crashpad-handler --database=C:\\Users\\<USER>\\AppData\\Roaming\\Code\\Crashpad --url=appcenter://code?aid=a4e3233c-699c-46ec-b4f4-9c2a77254662&uid=e5229fe5-9cc0-4bf1-a2f5-f71e789ee67d&iid=e5229fe5-9cc0-4bf1-a2f5-f71e789ee67d&sid=e5229fe5-9cc0-4bf1-a2f5-f71e789ee67d --annotation=_companyName=Microsoft --annotation=_productName=VSCode --annotation=_version=1.102.0 --annotation=plat=Win64 --annotation=prod=Electron --annotation=ver=35.6.0 --initial-client-data=0x480,0x484,0x488,0x47c,0x48c,0x7ff6136597a4,0x7ff6136597b0,0x7ff6136597c0", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 20492, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.2547152134691686, "memory_rss": 43208704, "memory_vms": *********, "create_time": 1752671688.648837, "num_threads": 19, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --video-capture-use-gpu-memory-buffer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=4032 /prefetch:14", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 20864, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.24553976734931984, "memory_rss": 41652224, "memory_vms": 138067968, "create_time": 1752671689.8415835, "num_threads": 29, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --video-capture-use-gpu-memory-buffer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=4276 /prefetch:14", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 20988, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 1.2351840691075284, "memory_rss": 209530880, "memory_vms": 229212160, "create_time": 1752671689.9619975, "num_threads": 25, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe --type=renderer --user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --app-user-model-id=Microsoft.VisualStudioCode --app-path=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app --enable-sandbox --enable-blink-features=HighlightAPI --disable-blink-features=FontMatchingCTMigration,StandardizedBrowserZoom, --video-capture-use-gpu-memory-buffer --lang=en-US --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=9 --time-ticks-at-unix-epoch=-1752671529202847 --launch-time-ticks=160744572 --field-trial-handle=1724,i,1488995577221272814,10867328394819933179,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit --variations-seed-version --mojo-platform-channel-handle=4452 --vscode-window-config=vscode:87bf36db-abba-421c-9a92-c1c9f7c9db4d /prefetch:1", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 21384, "name": "powershell.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.03877833281178166, "memory_rss": 6578176, "memory_vms": 65921024, "create_time": 1752671690.9424105, "num_threads": 9, "cmdline": "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -noexit -command try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}", "exe": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 22332, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.03346623242660609, "memory_rss": 5677056, "memory_vms": 36986880, "create_time": 1752671700.7173965, "num_threads": 8, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\.vscode\\extensions\\mtxr.sqltools-0.28.4\\dist\\languageserver.js --node-ipc --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Main Process"}, {"pid": 22524, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.08996766379620079, "memory_rss": 15261696, "memory_vms": 34807808, "create_time": 1752671701.561257, "num_threads": 8, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\json-language-features\\server\\dist\\node\\jsonServerMain --node-ipc --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Main Process"}, {"pid": 22540, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.004032367110565092, "memory_rss": 684032, "memory_vms": 847872, "create_time": 1752671775.474674, "num_threads": 2, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.autopep8-2025.3.11921012\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 22924, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.6396010322859803, "memory_rss": 108498944, "memory_vms": 110977024, "create_time": 1752671734.9447792, "num_threads": 3, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.pylint-2025.2.0\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 22936, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.059616253868174915, "memory_rss": 10113024, "memory_vms": 149241856, "create_time": 1752671772.3126595, "num_threads": 15, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @modelcontextprotocol/server-sequential-thinking", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 23508, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.5724029624135094, "memory_rss": 97099776, "memory_vms": 147771392, "create_time": 1752671730.0794911, "num_threads": 13, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @modelcontextprotocol/server-filesystem c:/Users/<USER>/Universal-AI-Assistants", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 23728, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.42875893881619365, "memory_rss": 72732672, "memory_vms": 524902400, "create_time": 1752671749.9267657, "num_threads": 14, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\aws\\toolkits\\language-servers\\AmazonQ\\1.20.0\\servers\\node.exe --max-old-space-size=8196 C:\\Users\\<USER>\\AppData\\Local\\aws\\toolkits\\language-servers\\AmazonQ\\1.20.0\\servers\\aws-lsp-codewhisperer.js --nolazy --preserve-symlinks --stdio --pre-init-encryption --set-credentials-encryption-key", "exe": "C:\\Users\\<USER>\\AppData\\Local\\aws\\toolkits\\language-servers\\AmazonQ\\1.20.0\\servers\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 23780, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.010744930324559674, "memory_rss": 1822720, "memory_vms": 53456896, "create_time": 1752671786.808588, "num_threads": 15, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\de2bd410102f5eda\\node_modules\\.bin\\\\..\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 23892, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.010938097611293333, "memory_rss": 1855488, "memory_vms": 53747712, "create_time": 1752671787.262436, "num_threads": 15, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\de2bd410102f5eda\\node_modules\\.bin\\\\..\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 23928, "name": "powershell.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.35798727413915, "memory_rss": 60727296, "memory_vms": 176324608, "create_time": 1752671776.491446, "num_threads": 22, "cmdline": "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.powershell-2025.2.0\\modules\\PowerShellEditorServices\\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.powershell-2025.2.0\\modules' -EnableConsoleRepl -StartupBanner \"PowerShell Extension v2025.2.0\nCopyright (c) Microsoft Corporation.\n\nhttps://aka.ms/vscode-powershell\nType 'help' to get help.\n\" -LogLevel 'Warning' -LogPath 'c:\\Users\\<USER>\\AppData\\Roaming\\Code\\logs\\20250716T161445\\window1\\exthost\\ms-vscode.powershell' -SessionDetailsPath 'c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\ms-vscode.powershell\\sessions\\PSES-VSCode-18348-497798.json' -FeatureFlags @() ", "exe": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 24136, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.060509652569318076, "memory_rss": 10264576, "memory_vms": 148348928, "create_time": 1752671772.158908, "num_threads": 14, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @upstash/context7-mcp@latest", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 24528, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.010213720286042118, "memory_rss": 1732608, "memory_vms": 54431744, "create_time": 1752671761.071818, "num_threads": 13, "cmdline": "node C:\\Users\\<USER>\\AppData\\Roaming\\npm\\\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js c:/Users/<USER>/Universal-AI-Assistants", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 24536, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.05399025664205716, "memory_rss": 9158656, "memory_vms": 27549696, "create_time": 1752671721.3186476, "num_threads": 9, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\.vscode\\extensions\\dbaeumer.vscode-eslint-3.0.10\\server\\out\\eslintServer.js --node-ipc --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Main Process"}, {"pid": 24564, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.08366558106651523, "memory_rss": 14192640, "memory_vms": 64212992, "create_time": 1752671721.358184, "num_threads": 14, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\.vscode\\extensions\\stylelint.vscode-stylelint-1.5.3\\dist\\start-server.js --stdio --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Main Process"}, {"pid": 24708, "name": "code-tunnel.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.060437214836792964, "memory_rss": 10252288, "memory_vms": 5730304, "create_time": **********.9007573, "num_threads": 15, "cmdline": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code-tunnel.exe tunnel --accept-server-license-terms --log info --name ABO-ELDAHB --parent-process-id 20492", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code-tunnel.exe", "cwd": "C:\\Users\\<USER>", "process_type": "Tunnel Process"}, {"pid": 24776, "name": "java.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 4.609647546968626, "memory_rss": 781959168, "memory_vms": 867082240, "create_time": **********.325339, "num_threads": 82, "cmdline": "c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\jre\\21.0.7-win32-x86_64.tar\\bin\\java -jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\server\\sonarlint-ls.jar -stdio -analyzers c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonargo.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarjava.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarjavasymbolicexecution.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarjs.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarphp.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarpython.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarhtml.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarxml.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonartext.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonariac.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\analyzers\\sonarlintomnisharp.jar c:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint_ondemand-analyzers\\sonar-cfamily-plugin\\6.68.0.85760\\sonarcfamily.jar", "exe": "C:\\Users\\<USER>\\.vscode\\extensions\\sonarsource.sonarlint-vscode-4.26.0-win32-x64\\jre\\21.0.7-win32-x86_64.tar\\bin\\java.exe", "cwd": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code", "process_type": "Unknown VS Code Process"}, {"pid": 25068, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.004201388486457042, "memory_rss": 712704, "memory_vms": 819200, "create_time": 1752671734.8717527, "num_threads": 1, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.pylint-2025.2.0\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 25096, "name": "python.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.004177242575615334, "memory_rss": 708608, "memory_vms": 827392, "create_time": 1752671734.9177873, "num_threads": 1, "cmdline": "c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.flake8-2025.2.0\\bundled\\tool\\lsp_server.py", "exe": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 25188, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.010720784413717968, "memory_rss": 1818624, "memory_vms": 52346880, "create_time": 1752671807.1214488, "num_threads": 15, "cmdline": "node C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\38edfeea335e8e67\\node_modules\\.bin\\\\..\\server-perplexity-ask\\dist\\index.js", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}, {"pid": 25288, "name": "Code.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 1.267950070119725, "memory_rss": 215089152, "memory_vms": 435646464, "create_time": 1752671735.252259, "num_threads": 15, "cmdline": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.vscode-pylance-2025.6.2\\dist\\server.bundle.js --cancellationReceive=file:05dfc486054e28d3af46b319cdfc7105c8187fa161 --node-ipc --clientProcessId=11680", "exe": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "cwd": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.vscode-pylance-2025.6.2\\dist", "process_type": "Main Process"}, {"pid": 25424, "name": "node.exe", "status": "running", "cpu_percent": 0.0, "memory_percent": 0.06075111167773515, "memory_rss": 10305536, "memory_vms": 148353024, "create_time": **********.7080016, "num_threads": 15, "cmdline": "C:\\Program Files\\nodejs\\\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npx-cli.js -y @modelcontextprotocol/server-sequential-thinking", "exe": "C:\\Program Files\\nodejs\\node.exe", "cwd": "c:\\Users\\<USER>\\Universal-AI-Assistants", "process_type": "Unknown VS Code Process"}], "extensions": [], "summary": {"total_processes": 0, "vscode_processes": 56, "system_health": "Warning"}}