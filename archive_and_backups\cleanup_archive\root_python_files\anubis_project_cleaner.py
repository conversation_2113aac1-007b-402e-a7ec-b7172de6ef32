#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 منظف مشروع أنوبيس
Anubis Project Cleaner

أداة لتنظيف وترتيب مشروع أنوبيس من الملفات المكررة والغير مستخدمة
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

class AnubisProjectCleaner:
    def __init__(self):
        self.project_root = Path.cwd()
        self.archive_dir = self.project_root / "archive_and_backups" / "cleanup_archive"
        self.cleanup_log = []
        
        # إنشاء مجلد الأرشيف
        self.archive_dir.mkdir(parents=True, exist_ok=True)
        
    def log_action(self, action, details):
        """تسجيل العمليات"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.cleanup_log.append(entry)
        print(f"✅ {action}: {details}")
    
    def identify_duplicate_docker_files(self):
        """تحديد ملفات Docker المكررة"""
        docker_files = {
            "docker-compose.yml": "الملف الأساسي الكامل",
            "docker-compose-basic.yml": "حاوية أساسية",
            "docker-compose-simple.yml": "حاوية بسيطة (العاملة)",
            "docker-compose.simple.yml": "مكرر للبسيطة",
            "docker-compose-anubis-isolation.yml": "نظام العزل"
        }
        
        duplicates_to_remove = [
            "docker-compose-basic.yml",  # مكرر وغير مستخدم
            "docker-compose.simple.yml"  # مكرر لـ docker-compose-simple.yml
        ]
        
        return duplicates_to_remove
    
    def identify_old_analysis_files(self):
        """تحديد ملفات التحليل القديمة"""
        patterns = [
            "anubis_*_report_*.json",
            "anubis_*_analysis_*.json", 
            "anubis_cline_*.json",
            "*_20250719_*.json",
            "*_20250720_*.json"
        ]
        
        old_files = []
        for pattern in patterns:
            old_files.extend(self.project_root.glob(pattern))
        
        return [f for f in old_files if f.is_file()]
    
    def identify_duplicate_python_files(self):
        """تحديد ملفات Python المكررة في الجذر"""
        # ملفات Python في الجذر التي يجب نقلها أو حذفها
        root_python_files = [
            f for f in self.project_root.glob("*.py") 
            if f.name not in ["main.py", "setup.py"]
        ]
        
        # تصنيف الملفات
        to_archive = []
        for file in root_python_files:
            if any(keyword in file.name for keyword in [
                "anubis_", "gemini_", "final_", "organize_", 
                "test", "analyzer", "helper", "quick_"
            ]):
                to_archive.append(file)
        
        return to_archive
    
    def identify_empty_directories(self):
        """تحديد المجلدات الفارغة"""
        empty_dirs = []
        
        for root, dirs, files in os.walk(self.project_root):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                
                # تجاهل مجلدات مهمة
                if any(important in str(dir_path) for important in [
                    ".git", "__pycache__", "node_modules", 
                    "archive_and_backups", ".venv"
                ]):
                    continue
                
                try:
                    if not any(dir_path.iterdir()):
                        empty_dirs.append(dir_path)
                except (PermissionError, OSError):
                    continue
        
        return empty_dirs
    
    def archive_file(self, file_path, category="misc"):
        """أرشفة ملف"""
        category_dir = self.archive_dir / category
        category_dir.mkdir(exist_ok=True)
        
        destination = category_dir / file_path.name
        
        # إذا كان الملف موجود، أضف timestamp
        if destination.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_parts = file_path.name.split('.')
            if len(name_parts) > 1:
                new_name = f"{'.'.join(name_parts[:-1])}_{timestamp}.{name_parts[-1]}"
            else:
                new_name = f"{file_path.name}_{timestamp}"
            destination = category_dir / new_name
        
        shutil.move(str(file_path), str(destination))
        return destination
    
    def clean_duplicate_docker_files(self):
        """تنظيف ملفات Docker المكررة"""
        self.log_action("بدء تنظيف", "ملفات Docker المكررة")
        
        duplicates = self.identify_duplicate_docker_files()
        
        for file_name in duplicates:
            file_path = self.project_root / file_name
            if file_path.exists():
                archived_path = self.archive_file(file_path, "docker_duplicates")
                self.log_action("أرشفة ملف Docker مكرر", f"{file_name} -> {archived_path}")
    
    def clean_old_analysis_files(self):
        """تنظيف ملفات التحليل القديمة"""
        self.log_action("بدء تنظيف", "ملفات التحليل القديمة")
        
        old_files = self.identify_old_analysis_files()
        
        for file_path in old_files:
            archived_path = self.archive_file(file_path, "old_analysis")
            self.log_action("أرشفة ملف تحليل قديم", f"{file_path.name} -> {archived_path}")
    
    def clean_duplicate_python_files(self):
        """تنظيف ملفات Python المكررة"""
        self.log_action("بدء تنظيف", "ملفات Python في الجذر")
        
        duplicate_files = self.identify_duplicate_python_files()
        
        for file_path in duplicate_files:
            archived_path = self.archive_file(file_path, "root_python_files")
            self.log_action("أرشفة ملف Python", f"{file_path.name} -> {archived_path}")
    
    def clean_empty_directories(self):
        """تنظيف المجلدات الفارغة"""
        self.log_action("بدء تنظيف", "المجلدات الفارغة")
        
        empty_dirs = self.identify_empty_directories()
        
        for dir_path in empty_dirs:
            try:
                dir_path.rmdir()
                self.log_action("حذف مجلد فارغ", str(dir_path.relative_to(self.project_root)))
            except OSError as e:
                self.log_action("خطأ في حذف مجلد", f"{dir_path}: {e}")
    
    def clean_pycache_files(self):
        """تنظيف ملفات __pycache__"""
        self.log_action("بدء تنظيف", "ملفات __pycache__")
        
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        
        for cache_dir in pycache_dirs:
            try:
                shutil.rmtree(cache_dir)
                self.log_action("حذف مجلد cache", str(cache_dir.relative_to(self.project_root)))
            except OSError as e:
                self.log_action("خطأ في حذف cache", f"{cache_dir}: {e}")
    
    def save_cleanup_report(self):
        """حفظ تقرير التنظيف"""
        report_file = self.archive_dir / f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "cleanup_date": datetime.now().isoformat(),
            "total_actions": len(self.cleanup_log),
            "actions": self.cleanup_log,
            "summary": {
                "archived_files": len([a for a in self.cleanup_log if "أرشفة" in a["action"]]),
                "deleted_dirs": len([a for a in self.cleanup_log if "حذف" in a["action"]]),
                "errors": len([a for a in self.cleanup_log if "خطأ" in a["action"]])
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log_action("حفظ تقرير", f"تم حفظ التقرير في {report_file}")
        return report
    
    def run_full_cleanup(self):
        """تشغيل التنظيف الكامل"""
        print("🏺 بدء تنظيف مشروع أنوبيس")
        print("=" * 50)
        
        # تنظيف ملفات Docker المكررة
        self.clean_duplicate_docker_files()
        
        # تنظيف ملفات التحليل القديمة
        self.clean_old_analysis_files()
        
        # تنظيف ملفات Python في الجذر
        self.clean_duplicate_python_files()
        
        # تنظيف ملفات cache
        self.clean_pycache_files()
        
        # تنظيف المجلدات الفارغة
        self.clean_empty_directories()
        
        # حفظ التقرير
        report = self.save_cleanup_report()
        
        print("\n🎉 تم الانتهاء من التنظيف!")
        print(f"📊 إجمالي العمليات: {report['summary']['archived_files'] + report['summary']['deleted_dirs']}")
        print(f"📁 ملفات مؤرشفة: {report['summary']['archived_files']}")
        print(f"🗑️ مجلدات محذوفة: {report['summary']['deleted_dirs']}")
        
        return report

if __name__ == "__main__":
    cleaner = AnubisProjectCleaner()
    cleaner.run_full_cleanup()
