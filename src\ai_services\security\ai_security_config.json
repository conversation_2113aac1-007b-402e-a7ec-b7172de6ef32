{"ai_security_policy": {"version": "1.0", "description": "سياسات الأمان المتقدمة لنظام الذكاء الاصطناعي", "last_updated": "2025-07-19T17:27:15.328054"}, "api_security": {"rate_limiting": {"enabled": true, "requests_per_minute": 60, "requests_per_hour": 1000, "burst_limit": 10}, "authentication": {"required": true, "methods": ["api_key", "jwt", "oauth2"], "key_rotation_days": 30}, "input_validation": {"enabled": true, "max_prompt_length": 4096, "forbidden_patterns": ["system prompt injection", "jailbreak attempts", "prompt leakage", "model extraction"], "content_filtering": true}}, "model_security": {"isolation": {"sandboxed_execution": true, "memory_limits": "2GB", "cpu_limits": "1.0", "network_restrictions": true}, "access_control": {"model_permissions": "read_only", "fine_tuning_disabled": true, "model_extraction_protection": true}, "monitoring": {"enabled": true, "log_all_requests": true, "anomaly_detection": true, "performance_monitoring": true}}, "data_security": {"encryption": {"at_rest": true, "in_transit": true, "algorithm": "AES-256-GCM"}, "privacy": {"data_anonymization": true, "pii_detection": true, "gdpr_compliance": true, "data_retention_days": 90}, "backup": {"encrypted_backups": true, "backup_schedule": "daily", "retention_policy": "30_days", "offsite_backups": false}}, "network_security": {"firewall": {"enabled": true, "default_deny": true, "allowed_ports": [8090, 8091], "ip_whitelist": ["127.0.0.1", "**********/16"]}, "ssl_tls": {"enabled": true, "min_version": "TLSv1.2", "certificate_validation": true, "hsts_enabled": true}}, "compliance": {"standards": ["OWASP AI Security", "NIST AI Risk Management", "ISO/IEC 27001", "SOC 2 Type II"], "audit_logging": {"enabled": true, "log_retention_days": 365, "tamper_protection": true}}}