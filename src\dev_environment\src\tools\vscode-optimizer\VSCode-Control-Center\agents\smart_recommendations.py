# -*- coding: utf-8 -*-
"""
💡 وكيل التوصيات الذكية - Smart Recommendations Agent
==================================================

وكيل ذكي متخصص في تقديم توصيات ذكية ومخصصة لتحسين الأداء
"""

import time
import json
from typing import Dict, List, Any, Tuple
from .base_agent import BaseAgent

class SmartRecommendationsAgent(BaseAgent):
    """وكيل التوصيات الذكية"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("SmartRecommendations", config)
        self.user_preferences = config.get('user_preferences', {}) if config else {}
        self.recommendation_history = []
        self.learning_enabled = config.get('learning_enabled', True) if config else True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل شامل وتقديم توصيات ذكية"""
        try:
            analysis = {
                'timestamp': time.time(),
                'smart_recommendations': [],
                'priority_recommendations': [],
                'long_term_suggestions': [],
                'quick_fixes': [],
                'personalized_tips': [],
                'learning_insights': {},
                'recommendation_score': 0
            }
            
            # تحليل البيانات وتقديم توصيات
            analysis['smart_recommendations'] = self._generate_smart_recommendations(data)
            analysis['priority_recommendations'] = self._get_priority_recommendations(data)
            analysis['long_term_suggestions'] = self._get_long_term_suggestions(data)
            analysis['quick_fixes'] = self._get_quick_fixes(data)
            analysis['personalized_tips'] = self._get_personalized_tips(data)
            
            # تعلم من البيانات
            if self.learning_enabled:
                analysis['learning_insights'] = self._learn_from_data(data)
            
            # حساب نقاط التوصيات
            analysis['recommendation_score'] = self._calculate_recommendation_score(analysis)
            
            self.save_analysis(analysis)
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل التوصيات: {e}")
            return {'error': str(e)}
    
    def _generate_smart_recommendations(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """توليد توصيات ذكية بناءً على البيانات"""
        recommendations = []
        
        try:
            # تحليل استهلاك الموارد
            if 'system_health' in data:
                system_health = data['system_health']
                
                # توصيات الذاكرة
                memory_usage = system_health.get('memory_usage', 0)
                if memory_usage > 85:
                    recommendations.append({
                        'type': 'memory_optimization',
                        'title': 'تحسين استخدام الذاكرة',
                        'description': f'الذاكرة مستخدمة بنسبة {memory_usage}%',
                        'actions': [
                            'إغلاق التطبيقات غير المستخدمة',
                            'إعادة تشغيل VS Code',
                            'تعطيل الإضافات الثقيلة'
                        ],
                        'priority': 'عالي',
                        'estimated_impact': 'تحسن 15-25% في الأداء'
                    })
                elif memory_usage > 70:
                    recommendations.append({
                        'type': 'memory_monitoring',
                        'title': 'مراقبة استخدام الذاكرة',
                        'description': f'الذاكرة مستخدمة بنسبة {memory_usage}%',
                        'actions': [
                            'مراقبة العمليات عالية الاستهلاك',
                            'تنظيف ملفات التخزين المؤقت'
                        ],
                        'priority': 'متوسط',
                        'estimated_impact': 'منع مشاكل مستقبلية'
                    })
                
                # توصيات المعالج
                cpu_usage = system_health.get('cpu_usage', 0)
                if cpu_usage > 80:
                    recommendations.append({
                        'type': 'cpu_optimization',
                        'title': 'تحسين استخدام المعالج',
                        'description': f'المعالج مستخدم بنسبة {cpu_usage}%',
                        'actions': [
                            'إيقاف العمليات عالية الاستهلاك',
                            'تقليل عدد النوافذ المفتوحة',
                            'استخدام وضع الطاقة المتوازن'
                        ],
                        'priority': 'عالي',
                        'estimated_impact': 'تحسن 20-30% في الاستجابة'
                    })
            
            # تحليل عمليات VS Code
            if 'vscode_processes' in data:
                vscode_data = data['vscode_processes']
                process_count = vscode_data.get('count', 0)
                
                if process_count > 8:
                    recommendations.append({
                        'type': 'vscode_optimization',
                        'title': 'تحسين عمليات VS Code',
                        'description': f'VS Code يشغل {process_count} عمليات',
                        'actions': [
                            'إعادة تشغيل VS Code',
                            'إغلاق النوافذ غير المستخدمة',
                            'تحديث VS Code لآخر إصدار'
                        ],
                        'priority': 'متوسط',
                        'estimated_impact': 'تحسن في سرعة الاستجابة'
                    })
                elif process_count == 0:
                    recommendations.append({
                        'type': 'vscode_startup',
                        'title': 'تحسين بدء تشغيل VS Code',
                        'description': 'VS Code غير مفتوح حالياً',
                        'actions': [
                            'تحسين إعدادات البدء',
                            'تعطيل الإضافات غير الضرورية عند البدء',
                            'استخدام مساحات العمل المحفوظة'
                        ],
                        'priority': 'منخفض',
                        'estimated_impact': 'بدء تشغيل أسرع'
                    })
            
            # توصيات الأمان
            if 'security_score' in data:
                security_score = data.get('security_score', 100)
                if security_score < 70:
                    recommendations.append({
                        'type': 'security_improvement',
                        'title': 'تحسين الأمان',
                        'description': f'نقاط الأمان: {security_score}/100',
                        'actions': [
                            'فحص العمليات المشبوهة',
                            'تحديث نظام التشغيل',
                            'فحص الاتصالات الشبكية'
                        ],
                        'priority': 'عالي',
                        'estimated_impact': 'تحسين الأمان العام'
                    })
                    
        except Exception as e:
            self.logger.error(f"خطأ في توليد التوصيات: {e}")
        
        return recommendations
    
    def _get_priority_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """الحصول على التوصيات ذات الأولوية العالية"""
        priority_recs = []
        
        try:
            # فحص الحالات الحرجة
            if 'system_health' in data:
                system_health = data['system_health']
                
                if system_health.get('memory_usage', 0) > 90:
                    priority_recs.append('🚨 تحرير الذاكرة فوراً - النظام على وشك التعليق!')
                
                if system_health.get('cpu_usage', 0) > 95:
                    priority_recs.append('🚨 إيقاف العمليات عالية الاستهلاك فوراً!')
                
                if system_health.get('disk_usage', 0) > 95:
                    priority_recs.append('🚨 تنظيف القرص الصلب - مساحة منخفضة جداً!')
            
            # فحص مشاكل VS Code
            if 'vscode_processes' in data:
                vscode_data = data['vscode_processes']
                if vscode_data.get('total_memory_usage', 0) > 40:
                    priority_recs.append('⚡ إعادة تشغيل VS Code - استهلاك ذاكرة عالي!')
                
                if vscode_data.get('count', 0) > 15:
                    priority_recs.append('⚡ تقليل عدد نوافذ VS Code المفتوحة!')
            
            # فحص الأمان
            if data.get('security_score', 100) < 50:
                priority_recs.append('🛡️ فحص أمني فوري مطلوب!')
                
        except Exception as e:
            self.logger.error(f"خطأ في التوصيات ذات الأولوية: {e}")
        
        return priority_recs
    
    def _get_long_term_suggestions(self, data: Dict[str, Any]) -> List[str]:
        """اقتراحات طويلة المدى"""
        suggestions = [
            '📈 إعداد مراقبة دورية للأداء',
            '🔄 جدولة تنظيف أسبوعي للنظام',
            '📊 إنشاء تقارير أداء شهرية',
            '🎯 تحسين إعدادات VS Code للمشاريع الكبيرة',
            '💾 إعداد نسخ احتياطية تلقائية',
            '🔧 تحديث الأدوات والإضافات بانتظام',
            '📚 تعلم اختصارات VS Code لزيادة الإنتاجية',
            '⚙️ تخصيص بيئة العمل حسب نوع المشروع'
        ]
        
        # تخصيص الاقتراحات حسب البيانات
        try:
            if 'system_health' in data:
                memory_usage = data['system_health'].get('memory_usage', 0)
                if memory_usage > 60:
                    suggestions.insert(0, '💾 النظر في ترقية الذاكرة (RAM)')
                
                cpu_usage = data['system_health'].get('cpu_usage', 0)
                if cpu_usage > 70:
                    suggestions.insert(0, '⚡ النظر في ترقية المعالج أو تحسين التبريد')
                    
        except Exception as e:
            self.logger.error(f"خطأ في الاقتراحات طويلة المدى: {e}")
        
        return suggestions[:6]  # أول 6 اقتراحات
    
    def _get_quick_fixes(self, data: Dict[str, Any]) -> List[str]:
        """إصلاحات سريعة"""
        quick_fixes = []
        
        try:
            # إصلاحات سريعة للذاكرة
            if 'system_health' in data:
                memory_usage = data['system_health'].get('memory_usage', 0)
                if memory_usage > 80:
                    quick_fixes.extend([
                        '🧹 تنظيف سلة المحذوفات',
                        '📁 إغلاق المجلدات غير المستخدمة في VS Code',
                        '🔄 إعادة تشغيل Explorer.exe'
                    ])
                
                # إصلاحات سريعة للمعالج
                cpu_usage = data['system_health'].get('cpu_usage', 0)
                if cpu_usage > 70:
                    quick_fixes.extend([
                        '⏸️ إيقاف التطبيقات في الخلفية مؤقتاً',
                        '🔇 تعطيل التأثيرات البصرية',
                        '❄️ تفعيل وضع توفير الطاقة'
                    ])
            
            # إصلاحات VS Code
            if 'vscode_processes' in data:
                vscode_count = data['vscode_processes'].get('count', 0)
                if vscode_count > 5:
                    quick_fixes.extend([
                        '📝 حفظ العمل وإعادة تشغيل VS Code',
                        '🧩 تعطيل الإضافات غير الضرورية مؤقتاً',
                        '🗂️ إغلاق التبويبات غير المستخدمة'
                    ])
                    
        except Exception as e:
            self.logger.error(f"خطأ في الإصلاحات السريعة: {e}")
        
        return quick_fixes[:5]  # أول 5 إصلاحات
    
    def _get_personalized_tips(self, data: Dict[str, Any]) -> List[str]:
        """نصائح مخصصة حسب تفضيلات المستخدم"""
        tips = []
        
        try:
            # نصائح حسب نوع المطور
            dev_type = self.user_preferences.get('developer_type', 'general')
            
            if dev_type == 'web':
                tips.extend([
                    '🌐 استخدم Live Server للتطوير السريع',
                    '🎨 فعل Emmet للكتابة السريعة',
                    '📱 استخدم DevTools المدمجة'
                ])
            elif dev_type == 'python':
                tips.extend([
                    '🐍 فعل Python Linting للكود النظيف',
                    '🧪 استخدم pytest للاختبارات',
                    '📊 فعل Jupyter Notebooks'
                ])
            elif dev_type == 'javascript':
                tips.extend([
                    '⚡ استخدم ESLint للكود النظيف',
                    '📦 فعل npm scripts',
                    '🔧 استخدم Prettier للتنسيق'
                ])
            
            # نصائح حسب مستوى الخبرة
            experience = self.user_preferences.get('experience_level', 'intermediate')
            
            if experience == 'beginner':
                tips.extend([
                    '📚 تعلم اختصارات لوحة المفاتيح الأساسية',
                    '🎯 استخدم Command Palette (Ctrl+Shift+P)',
                    '📁 نظم مشاريعك في مجلدات منفصلة'
                ])
            elif experience == 'advanced':
                tips.extend([
                    '⚙️ خصص إعدادات VS Code المتقدمة',
                    '🔧 أنشئ مهام مخصصة (Tasks)',
                    '🐛 استخدم Debugger المتقدم'
                ])
            
            # نصائح عامة مفيدة
            general_tips = [
                '💡 استخدم الوضع المظلم لتوفير البطارية',
                '🔍 فعل البحث الذكي في الملفات',
                '📋 استخدم Multiple Cursors للتحرير السريع',
                '🔄 فعل Auto Save لحفظ العمل تلقائياً',
                '📊 راقب استهلاك الإضافات في Task Manager'
            ]
            
            tips.extend(general_tips)
            
        except Exception as e:
            self.logger.error(f"خطأ في النصائح المخصصة: {e}")
        
        return tips[:8]  # أول 8 نصائح
    
    def _learn_from_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تعلم من البيانات لتحسين التوصيات المستقبلية"""
        insights = {
            'patterns_detected': [],
            'usage_trends': {},
            'optimization_opportunities': [],
            'learning_score': 0
        }
        
        try:
            # تحليل الأنماط
            if len(self.analysis_history) > 5:
                # تحليل اتجاهات استخدام الذاكرة
                memory_usage_trend = []
                for analysis in self.analysis_history[-5:]:
                    if 'system_health' in analysis:
                        memory_usage_trend.append(analysis['system_health'].get('memory_usage', 0))
                
                if memory_usage_trend:
                    avg_memory = sum(memory_usage_trend) / len(memory_usage_trend)
                    if avg_memory > 75:
                        insights['patterns_detected'].append('استخدام ذاكرة عالي مستمر')
                    
                    # اتجاه الاستخدام
                    if len(memory_usage_trend) >= 3:
                        if memory_usage_trend[-1] > memory_usage_trend[-3]:
                            insights['usage_trends']['memory'] = 'متزايد'
                        else:
                            insights['usage_trends']['memory'] = 'متناقص'
            
            # فرص التحسين المكتشفة
            if 'vscode_processes' in data:
                vscode_count = data['vscode_processes'].get('count', 0)
                if vscode_count > 10:
                    insights['optimization_opportunities'].append(
                        'تحسين إدارة نوافذ VS Code'
                    )
            
            # حساب نقاط التعلم
            insights['learning_score'] = min(len(self.analysis_history) * 10, 100)
            
        except Exception as e:
            self.logger.error(f"خطأ في التعلم من البيانات: {e}")
        
        return insights
    
    def _calculate_recommendation_score(self, analysis: Dict[str, Any]) -> int:
        """حساب نقاط جودة التوصيات"""
        score = 0
        
        try:
            # نقاط حسب عدد التوصيات
            smart_recs = len(analysis.get('smart_recommendations', []))
            score += min(smart_recs * 10, 50)
            
            # نقاط حسب التوصيات ذات الأولوية
            priority_recs = len(analysis.get('priority_recommendations', []))
            score += min(priority_recs * 15, 30)
            
            # نقاط حسب التخصيص
            personalized_tips = len(analysis.get('personalized_tips', []))
            score += min(personalized_tips * 5, 20)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط التوصيات: {e}")
            score = 50  # نقاط افتراضية
        
        return min(score, 100)
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """استخراج جميع التوصيات"""
        if 'error' in analysis:
            return ['❌ خطأ في الحصول على التوصيات الذكية']
        
        all_recommendations = []
        
        # التوصيات ذات الأولوية أولاً
        priority_recs = analysis.get('priority_recommendations', [])
        all_recommendations.extend(priority_recs)
        
        # الإصلاحات السريعة
        quick_fixes = analysis.get('quick_fixes', [])
        if quick_fixes:
            all_recommendations.append('--- إصلاحات سريعة ---')
            all_recommendations.extend(quick_fixes[:3])
        
        # التوصيات الذكية
        smart_recs = analysis.get('smart_recommendations', [])
        for rec in smart_recs[:3]:
            title = rec.get('title', 'توصية')
            priority = rec.get('priority', 'متوسط')
            all_recommendations.append(f"💡 {title} (أولوية: {priority})")
        
        # النصائح المخصصة
        personalized_tips = analysis.get('personalized_tips', [])
        if personalized_tips:
            all_recommendations.append('--- نصائح مخصصة ---')
            all_recommendations.extend(personalized_tips[:2])
        
        return all_recommendations or ['لا توجد توصيات محددة']
