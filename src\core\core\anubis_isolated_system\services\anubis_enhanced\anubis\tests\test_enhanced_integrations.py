#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبارات النظام المحسن للتكاملات الخارجية
Enhanced Integrations System Tests

مطور بالتعاون مع Gemini CLI
"""

import json
import sys
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent.parent))

from core.enhanced_integrations import (
    IntegrationsManager,
    HealthChecker,
    EnhancedOllamaProvider,
    EnhancedLangSmithWrapper,
    IntegrationError,
    ServiceUnavailableError,
    retry_with_backoff
)


class TestHealthChecker(unittest.TestCase):
    """اختبارات فاحص الصحة"""
    
    def setUp(self):
        self.health_checker = HealthChecker()
    
    @patch('requests.get')
    def test_ollama_health_check_success(self, mock_get):
        """اختبار فحص صحة Ollama - نجح"""
        # إعداد الاستجابة المزيفة
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "models": [
                {"name": "llama3:8b"},
                {"name": "mistral:7b"}
            ]
        }
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_get.return_value = mock_response
        
        # تشغيل الاختبار
        result = self.health_checker.check_ollama_health()
        
        # التحقق من النتائج
        self.assertEqual(result["status"], "healthy")
        self.assertEqual(len(result["available_models"]), 2)
        self.assertIn("llama3:8b", result["available_models"])
        self.assertEqual(result["response_time"], 0.5)
    
    @patch('requests.get')
    def test_ollama_health_check_failure(self, mock_get):
        """اختبار فحص صحة Ollama - فشل"""
        # إعداد استثناء
        mock_get.side_effect = ConnectionError("Connection failed")
        
        # تشغيل الاختبار
        result = self.health_checker.check_ollama_health()
        
        # التحقق من النتائج
        self.assertEqual(result["status"], "unavailable")
        self.assertIn("Connection failed", result["error"])
    
    def test_langsmith_health_check_no_library(self):
        """اختبار فحص صحة LangSmith - المكتبة غير متاحة"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'langsmith'")):
            result = self.health_checker.check_langsmith_health()
            
            self.assertEqual(result["status"], "unavailable")
            self.assertIn("not installed", result["error"])
    
    def test_comprehensive_health_check(self):
        """اختبار الفحص الشامل"""
        config = {
            "providers": {
                "ollama": {"enabled": True, "host": "localhost", "port": 11434},
                "gemini": {"enabled": False}
            },
            "langsmith": {"enabled": True}
        }
        
        with patch.object(self.health_checker, 'check_ollama_health') as mock_ollama, \
             patch.object(self.health_checker, 'check_langsmith_health') as mock_langsmith:
            
            mock_ollama.return_value = {"status": "healthy"}
            mock_langsmith.return_value = {"status": "healthy"}
            
            result = self.health_checker.run_comprehensive_health_check(config)
            
            self.assertIn("check_timestamp", result)
            self.assertIn("services", result)
            self.assertEqual(result["overall_health"], "excellent")


class TestRetryDecorator(unittest.TestCase):
    """اختبارات مُزخرف إعادة المحاولة"""
    
    def test_retry_success_on_first_attempt(self):
        """اختبار نجح في المحاولة الأولى"""
        @retry_with_backoff(max_retries=3, base_delay=0.1)
        def successful_function():
            return "success"
        
        result = successful_function()
        self.assertEqual(result, "success")
    
    def test_retry_success_after_failures(self):
        """اختبار نجح بعد فشل"""
        call_count = 0
        
        @retry_with_backoff(max_retries=3, base_delay=0.1)
        def failing_then_success():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError("Temporary failure")
            return "success"
        
        result = failing_then_success()
        self.assertEqual(result, "success")
        self.assertEqual(call_count, 3)
    
    def test_retry_max_attempts_exceeded(self):
        """اختبار تجاوز الحد الأقصى للمحاولات"""
        @retry_with_backoff(max_retries=2, base_delay=0.1)
        def always_failing():
            raise ConnectionError("Always fails")
        
        with self.assertRaises(IntegrationError):
            always_failing()


class TestEnhancedOllamaProvider(unittest.TestCase):
    """اختبارات موفر Ollama المحسن"""
    
    def setUp(self):
        self.config = {
            "host": "localhost",
            "port": 11434,
            "model": "llama3:8b",
            "timeout": 30
        }
        self.provider = EnhancedOllamaProvider(self.config)
    
    @patch('requests.post')
    def test_generate_response_success(self, mock_post):
        """اختبار توليد استجابة ناجحة"""
        # إعداد الاستجابة المزيفة
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"response": "Test response"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # تشغيل الاختبار
        result = self.provider.generate_response("Test prompt")
        
        # التحقق من النتائج
        self.assertEqual(result, "Test response")
        mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_generate_response_with_context(self, mock_post):
        """اختبار توليد استجابة مع سياق"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"response": "Contextual response"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        context = {"temperature": 0.5, "max_tokens": 500}
        result = self.provider.generate_response("Test prompt", context)
        
        self.assertEqual(result, "Contextual response")
        
        # التحقق من أن السياق تم تمريره
        call_args = mock_post.call_args
        sent_data = call_args[1]['json']
        self.assertEqual(sent_data['options']['temperature'], 0.5)
        self.assertEqual(sent_data['options']['max_tokens'], 500)
    
    @patch('requests.post')
    def test_generate_response_failure(self, mock_post):
        """اختبار فشل توليد الاستجابة"""
        mock_post.side_effect = ConnectionError("Connection failed")
        
        with self.assertRaises(IntegrationError):
            self.provider.generate_response("Test prompt")
    
    @patch('requests.get')
    def test_is_available_true(self, mock_get):
        """اختبار توفر الخدمة - متاحة"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        result = self.provider.is_available()
        self.assertTrue(result)
    
    @patch('requests.get')
    def test_is_available_false(self, mock_get):
        """اختبار توفر الخدمة - غير متاحة"""
        mock_get.side_effect = ConnectionError("Connection failed")
        
        result = self.provider.is_available()
        self.assertFalse(result)


class TestEnhancedLangSmithWrapper(unittest.TestCase):
    """اختبارات LangSmith wrapper المحسن"""
    
    def setUp(self):
        self.config = {
            "langsmith": {
                "enabled": True,
                "project_name": "test-project"
            }
        }
    
    @patch('builtins.__import__')
    def test_initialization_success(self, mock_import):
        """اختبار تهيئة ناجحة"""
        mock_client_class = Mock()
        mock_import.return_value.Client = mock_client_class
        
        wrapper = EnhancedLangSmithWrapper(self.config)
        
        self.assertTrue(wrapper.enabled)
        self.assertEqual(wrapper.project_name, "test-project")
    
    @patch('builtins.__import__')
    def test_initialization_import_error(self, mock_import):
        """اختبار فشل التهيئة - مكتبة غير متاحة"""
        mock_import.side_effect = ImportError("No module named 'langsmith'")
        
        wrapper = EnhancedLangSmithWrapper(self.config)
        
        self.assertFalse(wrapper.enabled)
        self.assertIsNone(wrapper.client)
    
    def test_disabled_wrapper(self):
        """اختبار wrapper معطل"""
        config = {"langsmith": {"enabled": False}}
        wrapper = EnhancedLangSmithWrapper(config)
        
        self.assertFalse(wrapper.enabled)
        self.assertIsNone(wrapper.client)


class TestIntegrationsManager(unittest.TestCase):
    """اختبارات مدير التكاملات"""
    
    def setUp(self):
        # إنشاء ملفات تكوين مؤقتة
        self.test_config_dir = Path(__file__).parent / "test_configs"
        self.test_config_dir.mkdir(exist_ok=True)
        
        # إنشاء ملف AI config
        ai_config = {
            "providers": {
                "ollama": {"enabled": True, "host": "localhost", "port": 11434}
            }
        }
        with open(self.test_config_dir / "ai_config.json", 'w') as f:
            json.dump(ai_config, f)
        
        # إنشاء ملف LangSmith config
        langsmith_config = {
            "langsmith": {"enabled": True, "project_name": "test"}
        }
        with open(self.test_config_dir / "langsmith_config.json", 'w') as f:
            json.dump(langsmith_config, f)
    
    def tearDown(self):
        # تنظيف الملفات المؤقتة
        for file in self.test_config_dir.glob("*.json"):
            file.unlink()
        self.test_config_dir.rmdir()
    
    @patch('anubis.core.enhanced_integrations.EnhancedOllamaProvider')
    @patch('anubis.core.enhanced_integrations.EnhancedLangSmithWrapper')
    def test_manager_initialization(self, mock_langsmith, mock_ollama):
        """اختبار تهيئة المدير"""
        manager = IntegrationsManager(str(self.test_config_dir))
        
        # التحقق من تحميل التكوين
        self.assertIn("providers", manager.config)
        self.assertIn("langsmith", manager.config)
        
        # التحقق من تهيئة الموفرين
        mock_ollama.assert_called_once()
        mock_langsmith.assert_called_once()
    
    @patch('anubis.core.enhanced_integrations.HealthChecker')
    def test_startup_health_check(self, mock_health_checker):
        """اختبار فحص الصحة عند البدء"""
        mock_checker_instance = Mock()
        mock_checker_instance.run_comprehensive_health_check.return_value = {
            "overall_health": "excellent",
            "services": {}
        }
        mock_health_checker.return_value = mock_checker_instance
        
        manager = IntegrationsManager(str(self.test_config_dir))
        result = manager.run_startup_health_check()
        
        self.assertEqual(result["overall_health"], "excellent")
        mock_checker_instance.run_comprehensive_health_check.assert_called_once()


def run_integration_tests():
    """تشغيل جميع اختبارات التكامل"""
    print("🧪 تشغيل اختبارات النظام المحسن للتكاملات الخارجية")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("-" * 60)

    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()

    # إضافة اختبارات فاحص الصحة
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestHealthChecker))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRetryDecorator))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestEnhancedOllamaProvider))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestEnhancedLangSmithWrapper))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestIntegrationsManager))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # طباعة النتائج
    print("\n" + "="*60)
    print("📊 نتائج الاختبارات:")
    print(f"✅ نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ فشل: {len(result.failures)}")
    print(f"🔴 أخطاء: {len(result.errors)}")
    print("🤖 تم تطوير هذه الاختبارات بالتعاون مع Gemini CLI")
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
