#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 منظم أنوبيس الشامل
Anubis Comprehensive Organizer

نظام تنظيم شامل يستخدم الوكلاء لـ:
- تنظيم بنية الملفات والمجلدات
- وضع الملفات في مجلداتها الصحيحة
- حذف الملفات غير المستخدمة
- إنشاء README لكل نظام معزول
- تنظيف المشروع بالكامل

مطور بالتعاون مع الوكلاء الذكيين
"""

import json
import os
import shutil
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# إضافة مسار أنوبيس
sys.path.append(str(Path(__file__).parent / "anubis"))

try:
    from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent
    from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent
    AGENTS_AVAILABLE = True
except ImportError:
    print("⚠️ الوكلاء غير متاحين - سيتم استخدام نظام تنظيم أساسي")
    AGENTS_AVAILABLE = False


class AnubisComprehensiveOrganizer:
    """منظم أنوبيس الشامل"""
    
    def __init__(self):
        self.project_root = Path(".").resolve()
        self.organization_timestamp = datetime.now().isoformat()
        
        # إعدادات التنظيم
        self.organization_plan = {
            "timestamp": self.organization_timestamp,
            "version": "5.0",
            "type": "comprehensive_organization",
            "actions_performed": [],
            "files_moved": [],
            "files_deleted": [],
            "directories_created": [],
            "readme_files_created": []
        }
        
        # هيكل المجلدات المطلوب
        self.target_structure = {
            "anubis_main_system": {
                "path": "anubis_main_system",
                "description": "النظام الرئيسي لأنوبيس",
                "subdirs": ["core", "agents", "api", "configs", "docs", "tests"]
            },
            "universal_ai_system": {
                "path": "universal_ai_system", 
                "description": "نظام Universal AI Assistants",
                "subdirs": ["src", "configs", "docs", "tests"]
            },
            "tools_and_utilities": {
                "path": "tools_and_utilities",
                "description": "الأدوات والمرافق المساعدة",
                "subdirs": ["vscode_tools", "optimizers", "scripts", "docs"]
            },
            "workflows_and_automation": {
                "path": "workflows_and_automation",
                "description": "أدوات سير العمل والأتمتة",
                "subdirs": ["n8n", "scripts", "configs", "docs"]
            },
            "isolation_systems": {
                "path": "isolation_systems",
                "description": "أنظمة العزل المتقدمة",
                "subdirs": ["basic_isolation", "advanced_isolation", "configs", "docs"]
            },
            "archive_and_backups": {
                "path": "archive_and_backups",
                "description": "الأرشيف والنسخ الاحتياطية",
                "subdirs": ["old_versions", "backups", "deprecated", "temp_files"]
            },
            "reports_and_analysis": {
                "path": "reports_and_analysis",
                "description": "التقارير والتحليلات",
                "subdirs": ["scan_reports", "test_reports", "analysis_data", "logs"]
            },
            "documentation": {
                "path": "documentation",
                "description": "التوثيق الشامل",
                "subdirs": ["user_guides", "technical_docs", "api_docs", "tutorials"]
            }
        }
        
        # قواعد تصنيف الملفات
        self.file_classification_rules = {
            "anubis_main_system": {
                "patterns": ["anubis/", "main.py", "*anubis*core*", "*anubis*agent*"],
                "extensions": []
            },
            "universal_ai_system": {
                "patterns": ["Universal-AI-Assistants/", "*universal*ai*"],
                "extensions": []
            },
            "tools_and_utilities": {
                "patterns": ["tools/", "*vscode*", "*optimizer*", "*utility*"],
                "extensions": []
            },
            "workflows_and_automation": {
                "patterns": ["n8n/", "*workflow*", "*automation*"],
                "extensions": []
            },
            "isolation_systems": {
                "patterns": ["*isolation*", "*container*", "docker-compose.yml"],
                "extensions": []
            },
            "archive_and_backups": {
                "patterns": ["archive/", "*backup*", "*old*", "*deprecated*", "*temp*"],
                "extensions": []
            },
            "reports_and_analysis": {
                "patterns": ["*report*", "*scan*", "*analysis*", "*test_*"],
                "extensions": [".json", ".log"]
            },
            "documentation": {
                "patterns": ["docs/", "*README*", "*GUIDE*"],
                "extensions": [".md", ".txt", ".rst"]
            }
        }
    
    def run_comprehensive_organization(self):
        """تشغيل التنظيم الشامل"""
        print("🏺 بدء التنظيم الشامل لمشروع أنوبيس")
        print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
        print("📁 تنظيم بنية الملفات والمجلدات")
        print("=" * 70)
        
        # المرحلة 1: تحليل الوضع الحالي
        self.analyze_current_structure()
        
        # المرحلة 2: إنشاء الهيكل المطلوب
        self.create_target_structure()
        
        # المرحلة 3: تصنيف ونقل الملفات
        self.classify_and_move_files()
        
        # المرحلة 4: تنظيف الملفات غير المستخدمة
        self.cleanup_unused_files()
        
        # المرحلة 5: إنشاء README لكل نظام
        self.create_readme_files()
        
        # المرحلة 6: تحديث نظام العزل
        self.update_isolation_systems()
        
        # المرحلة 7: إنشاء التقرير النهائي
        self.generate_final_report()
        
        print("\n🎉 تم إكمال التنظيم الشامل بنجاح!")
        return self.organization_plan
    
    def analyze_current_structure(self):
        """تحليل الهيكل الحالي"""
        print("\n🔍 تحليل الهيكل الحالي...")
        
        current_items = []
        for item in self.project_root.iterdir():
            if not item.name.startswith('.'):
                item_info = {
                    "name": item.name,
                    "type": "directory" if item.is_dir() else "file",
                    "size": self.get_size(item),
                    "classification": self.classify_item(item.name)
                }
                current_items.append(item_info)
        
        self.organization_plan["current_structure"] = current_items
        
        print(f"  ✅ تم تحليل {len(current_items)} عنصر")
        
        # عرض التصنيف
        classifications = {}
        for item in current_items:
            classification = item["classification"]
            classifications[classification] = classifications.get(classification, 0) + 1
        
        for classification, count in classifications.items():
            print(f"    🔹 {classification}: {count} عنصر")
    
    def get_size(self, path: Path) -> str:
        """حساب حجم المجلد أو الملف"""
        try:
            if path.is_file():
                size = path.stat().st_size
            else:
                size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
            
            # تحويل إلى وحدات مناسبة
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} MB"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} GB"
        except:
            return "unknown"
    
    def classify_item(self, item_name: str) -> str:
        """تصنيف العنصر"""
        item_name_lower = item_name.lower()
        
        for category, rules in self.file_classification_rules.items():
            # فحص الأنماط
            for pattern in rules["patterns"]:
                pattern_clean = pattern.replace("*", "").replace("/", "").lower()
                if pattern_clean in item_name_lower:
                    return category
            
            # فحص الامتدادات
            for ext in rules["extensions"]:
                if item_name_lower.endswith(ext):
                    return category
        
        return "unclassified"
    
    def create_target_structure(self):
        """إنشاء الهيكل المطلوب"""
        print("\n📁 إنشاء الهيكل المطلوب...")
        
        directories_created = []
        
        for system_name, system_info in self.target_structure.items():
            system_path = self.project_root / system_info["path"]
            
            # إنشاء المجلد الرئيسي
            system_path.mkdir(exist_ok=True)
            directories_created.append(str(system_path))
            
            # إنشاء المجلدات الفرعية
            for subdir in system_info["subdirs"]:
                subdir_path = system_path / subdir
                subdir_path.mkdir(exist_ok=True)
                directories_created.append(str(subdir_path))
        
        self.organization_plan["directories_created"] = directories_created
        
        print(f"  ✅ تم إنشاء {len(directories_created)} مجلد")
    
    def classify_and_move_files(self):
        """تصنيف ونقل الملفات"""
        print("\n📦 تصنيف ونقل الملفات...")
        
        files_moved = []
        
        # قائمة العناصر للنقل
        items_to_move = []
        for item in self.project_root.iterdir():
            if not item.name.startswith('.') and item.name not in [s["path"] for s in self.target_structure.values()]:
                classification = self.classify_item(item.name)
                if classification != "unclassified":
                    items_to_move.append((item, classification))
        
        # نقل العناصر
        for item, classification in items_to_move:
            try:
                target_system = None
                for system_name, system_info in self.target_structure.items():
                    if system_name == classification:
                        target_system = system_info
                        break
                
                if target_system:
                    target_path = self.project_root / target_system["path"]
                    
                    # تحديد المجلد الفرعي المناسب
                    if item.is_dir():
                        if "anubis" in item.name.lower():
                            target_subdir = target_path / "core"
                        elif "tool" in item.name.lower():
                            target_subdir = target_path / "src"
                        else:
                            target_subdir = target_path
                    else:
                        if item.suffix.lower() == ".md":
                            target_subdir = target_path / "docs"
                        elif item.suffix.lower() in [".json", ".log"]:
                            target_subdir = target_path / "reports" if "report" in item.name.lower() else target_path
                        else:
                            target_subdir = target_path
                    
                    target_subdir.mkdir(exist_ok=True)
                    new_path = target_subdir / item.name
                    
                    # تجنب الكتابة فوق الملفات الموجودة
                    if new_path.exists():
                        counter = 1
                        while new_path.exists():
                            name_parts = item.name.rsplit('.', 1)
                            if len(name_parts) == 2:
                                new_name = f"{name_parts[0]}_{counter}.{name_parts[1]}"
                            else:
                                new_name = f"{item.name}_{counter}"
                            new_path = target_subdir / new_name
                            counter += 1
                    
                    # نقل العنصر
                    if item.is_dir():
                        shutil.move(str(item), str(new_path))
                    else:
                        shutil.move(str(item), str(new_path))
                    
                    files_moved.append({
                        "from": str(item),
                        "to": str(new_path),
                        "classification": classification
                    })
                    
                    print(f"    📦 {item.name} → {target_system['path']}")
                    
            except Exception as e:
                print(f"    ❌ خطأ في نقل {item.name}: {e}")
        
        self.organization_plan["files_moved"] = files_moved
        
        print(f"  ✅ تم نقل {len(files_moved)} عنصر")
    
    def cleanup_unused_files(self):
        """تنظيف الملفات غير المستخدمة"""
        print("\n🧹 تنظيف الملفات غير المستخدمة...")
        
        files_deleted = []
        
        # أنماط الملفات المؤقتة والغير مستخدمة
        cleanup_patterns = [
            "*.tmp", "*.temp", "*.log", "*.cache",
            "*~", "*.bak", "*.old", "*.orig",
            "__pycache__", "*.pyc", "*.pyo",
            ".DS_Store", "Thumbs.db", "desktop.ini"
        ]
        
        # البحث عن الملفات للحذف
        for system_name, system_info in self.target_structure.items():
            system_path = self.project_root / system_info["path"]
            
            if system_path.exists():
                for pattern in cleanup_patterns:
                    for file_path in system_path.rglob(pattern):
                        try:
                            if file_path.is_file():
                                file_path.unlink()
                                files_deleted.append(str(file_path))
                            elif file_path.is_dir() and pattern == "__pycache__":
                                shutil.rmtree(file_path)
                                files_deleted.append(str(file_path))
                        except Exception as e:
                            print(f"    ⚠️ لم يتم حذف {file_path}: {e}")
        
        self.organization_plan["files_deleted"] = files_deleted
        
        print(f"  ✅ تم حذف {len(files_deleted)} ملف مؤقت")
    
    def create_readme_files(self):
        """إنشاء README لكل نظام معزول"""
        print("\n📚 إنشاء README لكل نظام معزول...")
        
        readme_files_created = []
        
        for system_name, system_info in self.target_structure.items():
            system_path = self.project_root / system_info["path"]
            readme_path = system_path / "README.md"
            
            readme_content = self.generate_readme_content(system_name, system_info)
            
            try:
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                readme_files_created.append(str(readme_path))
                print(f"    📄 {system_info['path']}/README.md")
                
            except Exception as e:
                print(f"    ❌ خطأ في إنشاء README لـ {system_name}: {e}")
        
        self.organization_plan["readme_files_created"] = readme_files_created
        
        print(f"  ✅ تم إنشاء {len(readme_files_created)} ملف README")
    
    def generate_readme_content(self, system_name: str, system_info: Dict) -> str:
        """إنشاء محتوى README"""
        
        # عنوان النظام
        title = system_info["description"]
        
        # إحصائيات النظام
        system_path = self.project_root / system_info["path"]
        file_count = len(list(system_path.rglob("*"))) if system_path.exists() else 0
        
        readme_content = f"""# {title}

**تاريخ التنظيم:** {datetime.now().strftime('%Y-%m-%d')}  
**منظم بواسطة:** نظام أنوبيس الشامل  
**الإصدار:** 5.0

---

## 📋 نظرة عامة

{system_info["description"]} - تم تنظيمه كجزء من نظام العزل الشامل لمشروع أنوبيس.

## 📁 هيكل المجلدات

```
{system_info["path"]}/
"""

        # إضافة المجلدات الفرعية
        for subdir in system_info["subdirs"]:
            subdir_path = system_path / subdir
            if subdir_path.exists():
                subdir_file_count = len(list(subdir_path.rglob("*")))
                readme_content += f"├── {subdir}/          # {subdir_file_count} ملف\n"
            else:
                readme_content += f"├── {subdir}/          # فارغ\n"

        readme_content += f"""└── README.md       # هذا الملف
```

## 📊 الإحصائيات

- **إجمالي الملفات:** {file_count}
- **المجلدات الفرعية:** {len(system_info["subdirs"])}
- **تاريخ آخر تنظيم:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🚀 الاستخدام

### للمطورين:
```bash
# الانتقال إلى النظام
cd {system_info["path"]}

# عرض محتويات النظام
ls -la
```

### للعزل:
هذا النظام جزء من نظام العزل الشامل لأنوبيس. يمكن تشغيله بشكل منفصل أو كجزء من النظام الكامل.

## 🔧 التكوين

"""

        # إضافة معلومات خاصة بكل نظام
        if system_name == "anubis_main_system":
            readme_content += """
### النظام الرئيسي لأنوبيس

- **النواة:** `core/` - المكونات الأساسية
- **الوكلاء:** `agents/` - الوكلاء الذكيين
- **API:** `api/` - واجهات التطبيق
- **التكوين:** `configs/` - ملفات التكوين

### التشغيل:
```bash
python core/main.py
```
"""
        elif system_name == "universal_ai_system":
            readme_content += """
### نظام Universal AI Assistants

- **المصدر:** `src/` - الكود المصدري
- **التكوين:** `configs/` - إعدادات النظام
- **الاختبارات:** `tests/` - اختبارات النظام

### التشغيل:
```bash
python src/main.py
```
"""
        elif system_name == "isolation_systems":
            readme_content += """
### أنظمة العزل

- **العزل الأساسي:** `basic_isolation/`
- **العزل المتقدم:** `advanced_isolation/`
- **التكوين:** `configs/` - إعدادات العزل

### التشغيل:
```bash
cd advanced_isolation
./management/scripts/start.sh
```
"""

        readme_content += f"""

## 🛡️ الأمان

- جميع الملفات منظمة ومعزولة
- لا توجد ملفات مؤقتة أو غير مستخدمة
- التكوينات آمنة ومحمية

## 📞 الدعم

للمساعدة أو الاستفسارات حول هذا النظام، راجع التوثيق الرئيسي لمشروع أنوبيس.

---

**🏺 تم تنظيمه بواسطة نظام أنوبيس الشامل**  
**🤖 مطور بالتعاون مع الوكلاء الذكيين**  
**📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}**
"""

        return readme_content
    
    def update_isolation_systems(self):
        """تحديث أنظمة العزل"""
        print("\n🔄 تحديث أنظمة العزل...")
        
        # نقل أنظمة العزل الموجودة
        isolation_systems = [
            "anubis_isolated_system",
            "anubis_advanced_isolation"
        ]
        
        isolation_target = self.project_root / "isolation_systems"
        
        for system in isolation_systems:
            system_path = self.project_root / system
            if system_path.exists():
                try:
                    target_path = isolation_target / system
                    if not target_path.exists():
                        shutil.move(str(system_path), str(target_path))
                        print(f"    📦 {system} → isolation_systems/")
                except Exception as e:
                    print(f"    ❌ خطأ في نقل {system}: {e}")
        
        print("  ✅ تم تحديث أنظمة العزل")
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n📊 إنشاء التقرير النهائي...")
        
        # حفظ خطة التنظيم
        report_file = f"anubis_organization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.organization_plan, f, ensure_ascii=False, indent=2)
        
        # إنشاء ملخص التنظيم
        summary = f"""# 🏺 تقرير التنظيم الشامل لأنوبيس

**تاريخ التنظيم:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**الإصدار:** 5.0

## 📊 ملخص الإنجازات

- **أنظمة منظمة:** {len(self.target_structure)}
- **ملفات منقولة:** {len(self.organization_plan.get('files_moved', []))}
- **ملفات محذوفة:** {len(self.organization_plan.get('files_deleted', []))}
- **مجلدات منشأة:** {len(self.organization_plan.get('directories_created', []))}
- **ملفات README:** {len(self.organization_plan.get('readme_files_created', []))}

## 🎯 الأنظمة المنظمة

"""
        
        for system_name, system_info in self.target_structure.items():
            summary += f"- **{system_info['description']}** (`{system_info['path']}/`)\n"
        
        summary += f"""

## 📄 التقرير المفصل

التقرير الكامل متاح في: `{report_file}`

---

**🏺 تم التنظيم بواسطة نظام أنوبيس الشامل**  
**🤖 مطور بالتعاون مع الوكلاء الذكيين**
"""
        
        with open("ORGANIZATION_SUMMARY.md", 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"  ✅ تم حفظ التقرير في: {report_file}")
        print(f"  📄 ملخص التنظيم في: ORGANIZATION_SUMMARY.md")


def main():
    """الدالة الرئيسية"""
    print("🏺 منظم أنوبيس الشامل")
    print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
    
    # إنشاء المنظم
    organizer = AnubisComprehensiveOrganizer()
    
    # تشغيل التنظيم الشامل
    result = organizer.run_comprehensive_organization()
    
    print("\n" + "="*70)
    print("🎉 تم إكمال التنظيم الشامل بنجاح!")
    print("📁 جميع الملفات منظمة في مجلداتها الصحيحة")
    print("📚 تم إنشاء README لكل نظام معزول")
    print("🧹 تم تنظيف الملفات غير المستخدمة")
    print("="*70)


if __name__ == "__main__":
    main()
