#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار تكامل LangSmith مع نظام أنوبيس
LangSmith Integration Test for Anubis System

اختبار شامل لتكامل LangSmith مع جميع الوكلاء والنماذج
"""

import json
import sys
import time
from datetime import datetime
from pathlib import Path

# إضافة المسارات
sys.path.append(str(Path(__file__).parent.parent))

try:
    from anubis.core.ai_integration import OllamaProvider
    from anubis.core.langsmith_wrapper import langsmith_wrapper
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    sys.exit(1)


class LangSmithIntegrationTest:
    """🧪 اختبار تكامل LangSmith"""

    def __init__(self):
        self.base_path = Path(__file__).parent.parent
        self.test_results = {}
        self.start_time = datetime.now()

        print("🧪 اختبار تكامل LangSmith مع نظام أنوبيس")
        print("=" * 60)

    def test_langsmith_availability(self):
        """اختبار توفر LangSmith"""
        print("\n🔍 اختبار توفر LangSmith...")

        try:
            import langsmith

            print("✅ LangSmith متاح")

            # اختبار الاتصال
            try:
                from langsmith import Client

                client = Client()
                print("✅ تم إنشاء LangSmith Client")
                return True
            except Exception as e:
                print(f"⚠️ فشل إنشاء Client: {e}")
                print("💡 تأكد من تعيين LANGCHAIN_API_KEY")
                return False

        except ImportError:
            print("❌ LangSmith غير متاح")
            return False

    def test_wrapper_functionality(self):
        """اختبار وظائف Wrapper"""
        print("\n🔧 اختبار LangSmith Wrapper...")

        try:
            # اختبار تتبع عملية
            with langsmith_wrapper.trace_agent_operation(
                "test_agent", "test_operation", {"test": True}
            ):
                time.sleep(0.1)  # محاكاة عملية

            # اختبار تسجيل أداء النموذج
            langsmith_wrapper.log_model_performance("test_model", 0.5, 100, 50)

            # الحصول على ملخص
            summary = langsmith_wrapper.get_traces_summary()

            print(f"✅ Wrapper يعمل - {summary['total_traces']} عملية مسجلة")
            return True

        except Exception as e:
            print(f"❌ خطأ في Wrapper: {e}")
            return False

    def test_agents_with_langsmith(self):
        """اختبار الوكلاء مع LangSmith"""
        print("\n🤖 اختبار الوكلاء مع LangSmith...")

        agents_results = {}

        # قائمة الوكلاء للاختبار
        agents_to_test = [
            ("enhanced_error_detector", "EnhancedErrorDetectorAgent"),
            ("enhanced_project_analyzer", "EnhancedProjectAnalyzerAgent"),
            ("enhanced_file_organizer", "EnhancedFileOrganizerAgent"),
            ("enhanced_memory_agent", "EnhancedMemoryAgent"),
            ("smart_code_analyzer", "SmartCodeAnalyzer"),
        ]

        for agent_name, agent_class in agents_to_test:
            try:
                print(f"   🔄 اختبار {agent_name}...")

                # تتبع تحميل الوكيل
                with langsmith_wrapper.trace_agent_operation(agent_name, "load_agent"):
                    # محاولة استيراد الوكيل
                    module_name = f"agents.{agent_name}"
                    module = __import__(module_name, fromlist=[agent_class])
                    agent_cls = getattr(module, agent_class)

                    # إنشاء instance
                    agent = agent_cls(".", {}, False)

                # تتبع عملية اختبار
                with langsmith_wrapper.trace_agent_operation(agent_name, "test_operation"):
                    # اختبار بسيط للوكيل
                    if hasattr(agent, "get_agent_info"):
                        info = agent.get_agent_info()
                    else:
                        info = {"status": "loaded"}

                agents_results[agent_name] = {"status": "success", "info": info}
                print(f"   ✅ {agent_name} - نجح")

            except Exception as e:
                agents_results[agent_name] = {"status": "failed", "error": str(e)}
                print(f"   ❌ {agent_name} - فشل: {e}")

        success_count = sum(1 for r in agents_results.values() if r["status"] == "success")
        total_count = len(agents_results)

        print(f"\n📊 نتائج اختبار الوكلاء: {success_count}/{total_count}")
        return agents_results

    def test_models_with_langsmith(self):
        """اختبار النماذج مع LangSmith"""
        print("\n🔥 اختبار النماذج مع LangSmith...")

        models_results = {}
        test_prompt = "اكتب جملة قصيرة عن الذكاء الاصطناعي"

        models = ["llama3:8b", "mistral:7b", "phi3:mini"]

        for model in models:
            try:
                print(f"   🔄 اختبار {model}...")

                with langsmith_wrapper.trace_agent_operation("ollama_provider", f"test_{model}"):
                    provider = OllamaProvider(model_name=model)

                    if provider.is_available():
                        start_time = time.time()
                        response = provider.generate_response(test_prompt)
                        end_time = time.time()

                        response_time = end_time - start_time

                        # تسجيل الأداء
                        langsmith_wrapper.log_model_performance(
                            model, response_time, len(test_prompt), len(response)
                        )

                        models_results[model] = {
                            "status": "success",
                            "response_time": round(response_time, 2),
                            "response_length": len(response),
                            "available": True,
                        }
                        print(f"   ✅ {model} - {response_time:.2f}ث")
                    else:
                        models_results[model] = {
                            "status": "unavailable",
                            "available": False,
                        }
                        print(f"   ⚠️ {model} - غير متاح")

            except Exception as e:
                models_results[model] = {
                    "status": "failed",
                    "error": str(e),
                    "available": False,
                }
                print(f"   ❌ {model} - فشل: {e}")

        available_count = sum(1 for r in models_results.values() if r.get("available", False))
        total_count = len(models_results)

        print(f"\n📊 نتائج اختبار النماذج: {available_count}/{total_count}")
        return models_results

    def test_multi_agent_coordination(self):
        """اختبار التنسيق بين الوكلاء"""
        print("\n🔗 اختبار التنسيق بين الوكلاء...")

        try:
            with langsmith_wrapper.trace_agent_operation("orchestrator", "multi_agent_test"):
                # محاكاة تنسيق بين وكلاء متعددين
                coordination_steps = [
                    ("analyzer", "تحليل المشروع"),
                    ("detector", "كشف الأخطاء"),
                    ("organizer", "تنظيم النتائج"),
                    ("memory", "حفظ البيانات"),
                ]

                results = {}

                for step_name, step_desc in coordination_steps:
                    with langsmith_wrapper.trace_agent_operation("coordination", step_name):
                        # محاكاة عملية
                        time.sleep(0.1)
                        results[step_name] = f"تم {step_desc}"

                print("✅ التنسيق بين الوكلاء - نجح")
                return {
                    "status": "success",
                    "steps_completed": len(coordination_steps),
                    "results": results,
                }

        except Exception as e:
            print(f"❌ التنسيق بين الوكلاء - فشل: {e}")
            return {"status": "failed", "error": str(e)}

    def generate_test_report(self):
        """إنتاج تقرير الاختبار"""
        print("\n📄 إنتاج تقرير الاختبار...")

        # جمع ملخص التتبع
        traces_summary = langsmith_wrapper.get_traces_summary()

        report = {
            "test_info": {
                "timestamp": self.start_time.isoformat(),
                "duration": (datetime.now() - self.start_time).total_seconds(),
                "test_type": "langsmith_integration",
            },
            "langsmith_status": {
                "available": "langsmith" in sys.modules,
                "wrapper_functional": True,
                "traces_recorded": traces_summary["total_traces"],
            },
            "test_results": self.test_results,
            "traces_summary": traces_summary,
            "recommendations": self.generate_recommendations(),
        }

        # حفظ التقرير
        reports_dir = self.base_path / "reports"
        reports_dir.mkdir(exist_ok=True)

        report_file = (
            reports_dir
            / f'langsmith_integration_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"✅ تم حفظ التقرير: {report_file}")
            return str(report_file)

        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None

    def generate_recommendations(self):
        """إنتاج توصيات التحسين"""
        recommendations = []

        # فحص النتائج وإنتاج توصيات
        if "agents_test" in self.test_results:
            agents_results = self.test_results["agents_test"]
            failed_agents = [
                name for name, result in agents_results.items() if result["status"] == "failed"
            ]

            if failed_agents:
                recommendations.append(
                    {
                        "type": "agents_fix",
                        "priority": "high",
                        "description": f"إصلاح الوكلاء الفاشلين: {', '.join(failed_agents)}",
                    }
                )

        if "models_test" in self.test_results:
            models_results = self.test_results["models_test"]
            unavailable_models = [
                name
                for name, result in models_results.items()
                if not result.get("available", False)
            ]

            if unavailable_models:
                recommendations.append(
                    {
                        "type": "models_setup",
                        "priority": "medium",
                        "description": f"إعداد النماذج غير المتاحة: {', '.join(unavailable_models)}",
                    }
                )

        # توصيات عامة
        recommendations.extend(
            [
                {
                    "type": "api_key",
                    "priority": "high",
                    "description": "تعيين LANGCHAIN_API_KEY للاستفادة الكاملة من LangSmith",
                },
                {
                    "type": "monitoring",
                    "priority": "medium",
                    "description": "إعداد مراقبة مستمرة للأداء",
                },
                {
                    "type": "optimization",
                    "priority": "low",
                    "description": "تحسين prompts والتفاعلات بناءً على البيانات المجمعة",
                },
            ]
        )

        return recommendations

    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🚀 بدء الاختبار الشامل لتكامل LangSmith")

        # تشغيل الاختبارات
        tests = [
            ("langsmith_availability", self.test_langsmith_availability),
            ("wrapper_functionality", self.test_wrapper_functionality),
            ("agents_test", self.test_agents_with_langsmith),
            ("models_test", self.test_models_with_langsmith),
            ("coordination_test", self.test_multi_agent_coordination),
        ]

        for test_name, test_func in tests:
            try:
                result = test_func()
                self.test_results[test_name] = result
            except Exception as e:
                self.test_results[test_name] = {"status": "error", "error": str(e)}
                print(f"❌ خطأ في {test_name}: {e}")

        # إنتاج التقرير
        report_file = self.generate_test_report()

        # عرض النتائج النهائية
        self.display_final_results()

        return self.test_results

    def display_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("🏆 نتائج اختبار تكامل LangSmith")
        print("=" * 60)

        # حساب الإحصائيات
        total_tests = len(self.test_results)
        successful_tests = 0

        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                if result.get("status") == "success" or result is True:
                    successful_tests += 1
                    status = "✅"
                else:
                    status = "❌"
            elif result is True:
                successful_tests += 1
                status = "✅"
            else:
                status = "❌"

            print(f"{status} {test_name}")

        print(f"\n📊 الإحصائيات:")
        print(
            f"   🎯 معدل النجاح: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)"
        )
        print(f"   ⏱️ مدة الاختبار: {(datetime.now() - self.start_time).total_seconds():.1f} ثانية")

        # عرض التوصيات
        recommendations = self.generate_recommendations()
        if recommendations:
            print(f"\n💡 التوصيات:")
            for rec in recommendations[:3]:  # أهم 3 توصيات
                print(f"   🔸 {rec['description']}")

        print(f"\n🔗 للمزيد من المعلومات:")
        print(f"   📚 راجع: docs/LANGSMITH_SETUP_GUIDE.md")
        print(f"   🌐 LangSmith: https://smith.langchain.com/")


def main():
    """الدالة الرئيسية"""
    tester = LangSmithIntegrationTest()
    results = tester.run_comprehensive_test()

    print(f"\n🏺 اختبار تكامل LangSmith مع نظام أنوبيس مكتمل!")

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
