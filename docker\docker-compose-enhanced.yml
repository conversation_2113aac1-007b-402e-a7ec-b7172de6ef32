version: '3.8'

# 🏺 نظام أنوبيس المحسن - مع قاعدة البيانات
# Anubis Enhanced System - With Database

services:
  # ===== قاعدة بيانات MySQL =====
  anubis-mysql:
    image: mysql:8.0
    container_name: anubis-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: **********
      MYSQL_DATABASE: anubis_system
      MYSQL_USER: anubis_user
      MYSQL_PASSWORD: **********
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./data/mysql_init:/docker-entrypoint-initdb.d
      - ./logs/mysql:/var/log/mysql
    networks:
      - anubis-network
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --max_connections=200
      --innodb_buffer_pool_size=256M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p**********"]
      timeout: 20s
      retries: 10
      interval: 30s

  # ===== النظام الأساسي المحسن =====
  anubis-enhanced:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: anubis-enhanced
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - ANUBIS_ENV=production
      - DEBUG=false
      - MYSQL_HOST=anubis-mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=anubis_user
      - MYSQL_PASSWORD=**********
      - MYSQL_DATABASE=anubis_system
    networks:
      - anubis-network
    depends_on:
      anubis-mysql:
        condition: service_healthy
    command: python main.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      timeout: 10s
      retries: 5
      interval: 30s

  # ===== Redis للتخزين المؤقت =====
  anubis-redis:
    image: redis:7-alpine
    container_name: anubis-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - anubis-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 5s
      retries: 5
      interval: 30s

  # ===== Nginx للتوزيع =====
  anubis-nginx:
    image: nginx:alpine
    container_name: anubis-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./logs/nginx:/var/log/nginx
      - ./ssl:/etc/nginx/ssl
    networks:
      - anubis-network
    depends_on:
      - anubis-enhanced
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 10s
      retries: 3
      interval: 30s

  # ===== مراقبة Prometheus =====
  anubis-prometheus:
    image: prom/prometheus:latest
    container_name: anubis-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - anubis-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # ===== لوحة تحكم Grafana =====
  anubis-grafana:
    image: grafana/grafana:latest
    container_name: anubis-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=**********
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - anubis-network
    depends_on:
      - anubis-prometheus

# ===== الشبكات =====
networks:
  anubis-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===== التخزين =====
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
