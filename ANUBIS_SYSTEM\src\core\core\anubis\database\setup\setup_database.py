#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 سكريبت إعداد قاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Database Setup Script

هذا السكريبت يقوم بإعداد قاعدة البيانات وتشغيل الاستعلامات الأولية
"""

import json
import os
import sys
from pathlib import Path

import mysql.connector
from mysql.connector import Error


def load_database_config():
    """تحميل إعدادات قاعدة البيانات"""
    config_path = Path(__file__).parent.parent / "configs" / "database_config.json"

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        return config["database"]["mysql"]
    except FileNotFoundError:
        print(f"❌ ملف الإعدادات غير موجود: {config_path}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"❌ خطأ في تحليل ملف الإعدادات: {config_path}")
        sys.exit(1)


def create_database_connection(config, include_db=True):
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        connection_config = {
            "host": config["host"],
            "port": config["port"],
            "user": config["user"],
            "password": config["password"],
            "charset": config.get("charset", "utf8mb4"),
            "autocommit": True,
        }

        if include_db:
            connection_config["database"] = config["database"]

        connection = mysql.connector.connect(**connection_config)
        return connection
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None


def execute_sql_file(connection, sql_file_path):
    """تنفيذ ملف SQL"""
    try:
        with open(sql_file_path, "r", encoding="utf-8") as f:
            sql_content = f.read()

        # تقسيم الاستعلامات
        statements = sql_content.split(";")

        cursor = connection.cursor()

        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith("--"):
                try:
                    cursor.execute(statement)
                    print(f"✅ تم تنفيذ الاستعلام بنجاح")
                except Error as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠️ تحذير في تنفيذ الاستعلام: {e}")

        cursor.close()
        print(f"✅ تم تنفيذ ملف SQL بنجاح: {sql_file_path}")

    except FileNotFoundError:
        print(f"❌ ملف SQL غير موجود: {sql_file_path}")
    except Error as e:
        print(f"❌ خطأ في تنفيذ ملف SQL: {e}")


def test_database_setup(config):
    """اختبار إعداد قاعدة البيانات"""
    connection = create_database_connection(config)
    if not connection:
        return False

    try:
        cursor = connection.cursor(dictionary=True)

        # اختبار الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        print(f"\n📊 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            table_name = list(table.values())[0]

            # عد الصفوف في كل جدول
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            count = cursor.fetchone()["count"]

            print(f"  📋 {table_name}: {count} صف")

        # اختبار البيانات التجريبية
        cursor.execute("SELECT COUNT(*) as count FROM projects")
        project_count = cursor.fetchone()["count"]

        cursor.execute("SELECT COUNT(*) as count FROM analyses")
        analysis_count = cursor.fetchone()["count"]

        print(f"\n🎯 البيانات التجريبية:")
        print(f"  📁 المشاريع: {project_count}")
        print(f"  🔍 التحليلات: {analysis_count}")

        cursor.close()
        connection.close()

        return True

    except Error as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🏺 مرحباً بك في إعداد قاعدة بيانات نظام أنوبيس")
    print("=" * 50)

    # تحميل الإعدادات
    print("📋 تحميل إعدادات قاعدة البيانات...")
    config = load_database_config()

    print(f"🔗 الاتصال بخادم MySQL: {config['host']}:{config['port']}")
    print(f"👤 المستخدم: {config['user']}")
    print(f"🗄️ قاعدة البيانات: {config['database']}")

    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    print("\n🔧 إنشاء قاعدة البيانات...")
    connection = create_database_connection(config, include_db=False)
    if connection:
        try:
            cursor = connection.cursor()
            cursor.execute(
                f"CREATE DATABASE IF NOT EXISTS {config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
            )
            print(f"✅ تم إنشاء قاعدة البيانات: {config['database']}")
            cursor.close()
            connection.close()
        except Error as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            sys.exit(1)
    else:
        print("❌ فشل الاتصال بخادم MySQL")
        sys.exit(1)

    # تنفيذ ملف إنشاء الجداول
    print("\n📋 إنشاء الجداول والبيانات التجريبية...")
    connection = create_database_connection(config)
    if connection:
        sql_file_path = Path(__file__).parent / "create_mysql_database.sql"
        execute_sql_file(connection, sql_file_path)
        connection.close()
    else:
        print("❌ فشل الاتصال بقاعدة البيانات")
        sys.exit(1)

    # اختبار الإعداد
    print("\n🧪 اختبار إعداد قاعدة البيانات...")
    if test_database_setup(config):
        print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("\n📝 الخطوات التالية:")
        print("  1. تشغيل نظام أنوبيس: python main.py")
        print("  2. اختبار الاتصال: python database/mysql_connector.py")
        print("  3. عرض واجهة الويب: python web_interface/app.py")
    else:
        print("\n❌ فشل في إعداد قاعدة البيانات")
        sys.exit(1)


if __name__ == "__main__":
    main()
