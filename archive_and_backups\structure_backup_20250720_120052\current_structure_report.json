{".env": {"type": "file", "size": 56}, ".env.template": {"type": "file", "size": 253}, ".gitignore": {"type": "file", "size": 458}, "anubis_agents_cline_analysis_20250720_072426.json": {"type": "file", "size": 3635}, "anubis_agents_cline_analyzer.py": {"type": "file", "size": 18986}, "anubis_ai_collaboration_helper.py": {"type": "file", "size": 10049}, "ANUBIS_AI_MODELS_AND_APPLICATIONS_DETAILED_GUIDE.md": {"type": "file", "size": 20543}, "anubis_ai_team": {"type": "directory", "files_count": 8, "subdirs": []}, "anubis_ai_team_collaboration_plan.json": {"type": "file", "size": 4559}, "anubis_ai_team_collaboration_system.py": {"type": "file", "size": 12938}, "anubis_api_comprehensive_test.py": {"type": "file", "size": 6851}, "anubis_api_test_report_20250720_090947.json": {"type": "file", "size": 4971}, "anubis_cline_analysis_report_20250720_072206.json": {"type": "file", "size": 20368}, "ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md": {"type": "file", "size": 7354}, "anubis_cline_conversation_analyzer.py": {"type": "file", "size": 13220}, "anubis_complete_system_test.py": {"type": "file", "size": 9409}, "anubis_complete_system_test_report_20250720_091517.json": {"type": "file", "size": 2130}, "anubis_comprehensive_organizer.py": {"type": "file", "size": 27666}, "anubis_comprehensive_system_tester.py": {"type": "file", "size": 31003}, "anubis_docker_isolation_launcher.py": {"type": "file", "size": 11447}, "anubis_docker_isolation_system.py": {"type": "file", "size": 17274}, "anubis_final_organization_report_20250719_175607.json": {"type": "file", "size": 3569}, "anubis_gemini_assistant_request.py": {"type": "file", "size": 10730}, "anubis_gemini_cli_helper.py": {"type": "file", "size": 9756}, "anubis_gemini_docker_help_request.md": {"type": "file", "size": 7030}, "anubis_gemini_request_20250720_072632.md": {"type": "file", "size": 2692}, "anubis_isolation_status_checker.py": {"type": "file", "size": 6835}, "anubis_isolation_system": {"type": "directory", "files_count": 21, "subdirs": ["api", "isolation", "monitor", "worker"]}, "anubis_isolation_system_manager.py": {"type": "file", "size": 9023}, "anubis_main_system": {"type": "directory", "files_count": 699, "subdirs": ["agents", "api", "configs", "core", "database", "docs", "reports", "tests"]}, "anubis_n8n_monitor.py": {"type": "file", "size": 4646}, "anubis_n8n_quick_start.py": {"type": "file", "size": 5049}, "anubis_navigation_shortcuts.json": {"type": "file", "size": 1837}, "anubis_organization_completion_report.md": {"type": "file", "size": 9237}, "anubis_project_analysis_report_20250720_092451.json": {"type": "file", "size": 14070}, "anubis_project_analysis_task_distribution.md": {"type": "file", "size": 5717}, "anubis_project_analyzer.py": {"type": "file", "size": 12204}, "anubis_project_organization_gemini_request.md": {"type": "file", "size": 7970}, "anubis_project_organization_plan.md": {"type": "file", "size": 8200}, "anubis_project_paths": {"type": "directory", "files_count": 3, "subdirs": []}, "anubis_project_paths_config.json": {"type": "file", "size": 4199}, "anubis_quick_docker_diagnosis.py": {"type": "file", "size": 1421}, "anubis_services_catalog_20250719_180343.json": {"type": "file", "size": 20243}, "ANUBIS_SERVICES_COMPREHENSIVE_GUIDE.md": {"type": "file", "size": 14339}, "anubis_services_comprehensive_guide.py": {"type": "file", "size": 43964}, "anubis_simple_system_tester.py": {"type": "file", "size": 37895}, "anubis_system_test_report_20250720_061919.json": {"type": "file", "size": 7217}, "archive_and_backups": {"type": "directory", "files_count": 104, "subdirs": ["archive", "backup", "backups", "deprecated", "old_versions", "structure_backup_20250720_120052", "temp", "temp_files", "unified_backups"]}, "augment-cht": {"type": "directory", "files_count": 3, "subdirs": []}, "cline_task_jul-20-2025_7-13-06-am.md": {"type": "file", "size": 2318885}, "configs": {"type": "directory", "files_count": 4, "subdirs": []}, "create_new_structure.py": {"type": "file", "size": 9356}, "data": {"type": "directory", "files_count": 0, "subdirs": []}, "database": {"type": "directory", "files_count": 1, "subdirs": []}, "docker-compose-anubis-isolation.yml": {"type": "file", "size": 8546}, "docker-compose.yml": {"type": "file", "size": 767}, "Dockerfile": {"type": "file", "size": 495}, "documentation": {"type": "directory", "files_count": 13, "subdirs": ["api_docs", "docs", "guides", "reports", "technical_docs", "tutorials", "user_guides"]}, "final_anubis_organizer_with_gemini.py": {"type": "file", "size": 31272}, "gemini_coordination_request_anubis_task_20250720_101017.md": {"type": "file", "size": 4228}, "gemini_project_analysis_request.md": {"type": "file", "size": 4258}, "isolation": {"type": "directory", "files_count": 0, "subdirs": []}, "isolation_configs": {"type": "directory", "files_count": 9, "subdirs": ["containers", "networks", "scripts", "security"]}, "isolation_systems": {"type": "directory", "files_count": 14, "subdirs": ["advanced_isolation", "basic_isolation", "configs", "docs"]}, "logs": {"type": "directory", "files_count": 8, "subdirs": ["isolation", "operations"]}, "main.py": {"type": "file", "size": 13467}, "move_isolation_files.py": {"type": "file", "size": 2830}, "organize_ai_team.py": {"type": "file", "size": 1840}, "quick_access_shortcuts.py": {"type": "file", "size": 3841}, "README.md": {"type": "file", "size": 5715}, "reports": {"type": "directory", "files_count": 15, "subdirs": ["analysis", "inspection", "organization", "system"]}, "reports_and_analysis": {"type": "directory", "files_count": 9, "subdirs": ["analysis_data", "logs", "reports", "scan_reports", "test_reports"]}, "requirements.txt": {"type": "file", "size": 1966}, "scripts": {"type": "directory", "files_count": 16, "subdirs": ["analyzers", "checkers", "inspectors", "organizers"]}, "setup_gemini.bat": {"type": "file", "size": 425}, "start_anubis_isolated.sh": {"type": "file", "size": 456}, "start_team_analysis.py": {"type": "file", "size": 2148}, "tools_and_utilities": {"type": "directory", "files_count": 109, "subdirs": ["docs", "optimizers", "scripts", "src", "tests", "vscode_tools"]}, "universal_ai_system": {"type": "directory", "files_count": 17, "subdirs": ["configs", "docs", "monitoring", "security", "src", "tests", "Universal-AI-Assistants"]}, "utilities": {"type": "directory", "files_count": 5, "subdirs": ["helpers", "optimizers"]}, "workflows_and_automation": {"type": "directory", "files_count": 35, "subdirs": ["configs", "data", "docs", "logs", "monitoring", "n8n", "n8n_1", "scripts", "security"]}, "workflow_anubis_task_20250720_101017.json": {"type": "file", "size": 4799}, "workspace": {"type": "directory", "files_count": 28, "subdirs": ["logs", "monitoring", "reports", "security"]}, "__pycache__": {"type": "directory", "files_count": 3, "subdirs": []}}