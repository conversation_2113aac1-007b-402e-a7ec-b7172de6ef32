{"status": "completed", "total_files": 292, "categorized_files": {"documents": 73, "code": 90, "data": 65, "other": 25, "logs": 39}, "duplicate_files": {"bd4d2b86ccd1dccd5ebe3b38bc88ea73": ["__init__.py", "agents\\__init__.py", "api\\__init__.py", "database\\__init__.py"]}, "file_size_analysis": {"total_project_size_bytes": 37846920, "total_project_size_mb": 36.09, "average_file_size_bytes": 129612.7397260274, "largest_files": [{"path": "workspace\\reports\\assistant_report_20250712_151309.json", "size": 10492425}, {"path": "workspace\\reports\\assistant_report_20250712_151038.json", "size": 5440462}, {"path": "workspace\\reports\\assistant_report_20250712_151119.json", "size": 5439107}, {"path": "workspace\\reports\\assistant_report_20250712_150924.json", "size": 5438990}, {"path": "workspace\\reports\\assistant_report_20250712_154134.json", "size": 5438990}, {"path": "workspace\\reports\\anubis_error_analysis_20250714_142104.json", "size": 1034916}, {"path": "workspace\\reports\\error_detector_report_20250714_142101.json", "size": 765717}, {"path": "configs\\openapi.json", "size": 543501}, {"path": "workspace\\reports\\error_detector_report_20250714_131152.json", "size": 320500}, {"path": "workspace\\reports\\error_detector_report_20250714_130858.json", "size": 309855}]}, "recommendations": ["إزالة الملفات المكررة (1 مجموعة) لتوفير المساحة.", "يوجد عدد كبير من الملفات غير المصنفة. قد تحتاج إلى تنظيمها في مجلدات.", "توجد ملفات سجلات كثيرة. قم بأرشفتها أو حذفها بانتظام.", "إنشاء ملف .gitignore لتجاهل الملفات غير الضرورية (مثل .log, .tmp).", "استخدام هيكل مجلدات واضح ومتسق (مثل src, data, docs)."], "timestamp": "2025-07-19T07:25:23.268269"}