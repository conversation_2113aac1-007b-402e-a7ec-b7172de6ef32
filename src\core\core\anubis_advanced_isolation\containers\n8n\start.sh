#!/bin/bash
# Startup script for n8n
# Generated by Anubis Advanced Isolation System

echo "🚀 Starting n8n..."

# Wait for dependencies
echo "⏳ Waiting for dependencies..."
sleep 10

# Start the application
echo "🏺 Starting n8n application..."
if [ -f "main.py" ]; then
    python main.py
elif [ -f "app.py" ]; then
    python app.py
elif [ -f "server.py" ]; then
    python server.py
else
    echo "⚠️ No main application file found"
    sleep infinity
fi
