#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 فاحص نظام الذكاء الاصطناعي الشامل - استراتيجية Gemini
Universal AI System Comprehensive Inspector - Gemini Strategy
"""

import os
import json
from pathlib import Path
from datetime import datetime

class UniversalAISystemInspector:
    def __init__(self):
        self.base_path = Path("universal_ai_system")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "inspector": "Universal AI System Comprehensive Inspector",
            "inspection_type": "advanced_ai_system_analysis_with_gemini_strategy",
            "overall_health": "unknown",
            "components": {},
            "ai_capabilities": {},
            "integration_analysis": {},
            "performance_metrics": {},
            "security_assessment": {},
            "scalability_evaluation": {},
            "gemini_recommendations": [],
            "critical_findings": []
        }
        
        # استراتيجية Gemini لفحص أنظمة الذكاء الاصطناعي
        self.gemini_strategy = {
            "ai_analysis_priorities": [
                "analyze_ai_architecture",
                "evaluate_model_integrations", 
                "assess_agent_capabilities",
                "review_ai_configurations",
                "test_provider_connections",
                "measure_performance_metrics"
            ],
            "security_priorities": [
                "api_key_security_audit",
                "data_privacy_compliance",
                "model_access_controls",
                "prompt_injection_protection",
                "sensitive_data_handling"
            ],
            "performance_priorities": [
                "response_time_analysis",
                "throughput_measurement",
                "resource_utilization",
                "scalability_testing",
                "error_rate_monitoring"
            ],
            "integration_priorities": [
                "provider_compatibility",
                "fallback_mechanisms",
                "load_balancing",
                "cross_model_consistency",
                "api_rate_limiting"
            ]
        }
    
    def analyze_ai_architecture(self):
        """تحليل معمارية نظام الذكاء الاصطناعي (استراتيجية Gemini)"""
        print("🤖 تحليل معمارية نظام الذكاء الاصطناعي...")
        print("🧠 تطبيق استراتيجية Gemini للتحليل المتقدم...")
        
        component_health = {
            "status": "analyzing",
            "architecture_score": 0,
            "directory_structure": {},
            "ai_components": {},
            "design_patterns": [],
            "issues": [],
            "strengths": [],
            "gemini_insights": []
        }
        
        if not self.base_path.exists():
            component_health["issues"].append("❌ مجلد universal_ai_system/ مفقود تماماً")
            component_health["gemini_insights"].append("🚨 Gemini: هذا خطأ حرج - النظام يحتاج نواة الذكاء الاصطناعي")
            self.report["components"]["ai_architecture"] = component_health
            return component_health
        
        # فحص هيكل المجلدات الأساسية
        essential_dirs = {
            "src": {
                "description": "كود المصدر الأساسي",
                "critical": True,
                "expected_subdirs": ["models", "agents", "providers", "core"]
            },
            "configs": {
                "description": "تكوينات النظام",
                "critical": True,
                "expected_files": ["*.json", "*.yaml", "*.yml"]
            },
            "tests": {
                "description": "اختبارات النظام",
                "critical": False,
                "expected_files": ["test_*.py", "*_test.py"]
            },
            "docs": {
                "description": "التوثيق",
                "critical": False,
                "expected_files": ["*.md", "*.rst"]
            }
        }
        
        # تحليل كل مجلد
        for dir_name, dir_info in essential_dirs.items():
            dir_path = self.base_path / dir_name
            dir_analysis = self._analyze_ai_directory(dir_path, dir_info)
            component_health["directory_structure"][dir_name] = dir_analysis
            
            # تطبيق منطق Gemini للتقييم
            if dir_analysis["exists"]:
                if dir_analysis["files_count"] > 0:
                    component_health["strengths"].append(f"✅ {dir_name}/ نشط ({dir_analysis['files_count']} ملف)")
                    component_health["architecture_score"] += 20
                    
                    if dir_info["critical"]:
                        component_health["gemini_insights"].append(f"🎯 Gemini: {dir_name} موجود ونشط - مكون حرج")
                else:
                    component_health["issues"].append(f"⚠️ {dir_name}/ فارغ")
                    if dir_info["critical"]:
                        component_health["gemini_insights"].append(f"⚠️ Gemini: {dir_name} فارغ - يحتاج محتوى")
            else:
                if dir_info["critical"]:
                    component_health["issues"].append(f"🚨 {dir_name}/ مفقود (حرج)")
                    component_health["gemini_insights"].append(f"🚨 Gemini: {dir_name} مفقود - ضروري للنظام")
                else:
                    component_health["issues"].append(f"⚠️ {dir_name}/ مفقود (تحسين)")
        
        # فحص الأنماط المعمارية
        self._detect_design_patterns(component_health)
        
        # تقييم Gemini للمعمارية
        if component_health["architecture_score"] >= 80:
            component_health["status"] = "excellent"
            component_health["gemini_insights"].append("🎯 Gemini: معمارية ممتازة ومتطورة")
        elif component_health["architecture_score"] >= 60:
            component_health["status"] = "good"
            component_health["gemini_insights"].append("✅ Gemini: معمارية جيدة مع إمكانية تحسين")
        elif component_health["architecture_score"] >= 40:
            component_health["status"] = "needs_improvement"
            component_health["gemini_insights"].append("🔧 Gemini: معمارية تحتاج تحسين")
        else:
            component_health["status"] = "critical"
            component_health["gemini_insights"].append("🚨 Gemini: معمارية تحتاج إعادة تصميم")
        
        self.report["components"]["ai_architecture"] = component_health
        return component_health
    
    def _analyze_ai_directory(self, dir_path, dir_info):
        """تحليل مجلد في نظام الذكاء الاصطناعي"""
        analysis = {
            "exists": dir_path.exists(),
            "files_count": 0,
            "subdirs_count": 0,
            "file_types": {},
            "ai_patterns": [],
            "complexity_score": 0
        }
        
        if analysis["exists"]:
            all_items = list(dir_path.rglob("*"))
            files = [item for item in all_items if item.is_file()]
            dirs = [item for item in all_items if item.is_dir()]
            
            analysis["files_count"] = len(files)
            analysis["subdirs_count"] = len(dirs)
            
            # تحليل أنواع الملفات
            for file in files:
                ext = file.suffix.lower()
                if ext not in analysis["file_types"]:
                    analysis["file_types"][ext] = 0
                analysis["file_types"][ext] += 1
                
                # اكتشاف أنماط الذكاء الاصطناعي
                if "model" in file.name.lower():
                    analysis["ai_patterns"].append("model_files")
                elif "agent" in file.name.lower():
                    analysis["ai_patterns"].append("agent_files")
                elif "prompt" in file.name.lower():
                    analysis["ai_patterns"].append("prompt_files")
                elif any(term in file.name.lower() for term in ["llm", "gpt", "gemini", "claude"]):
                    analysis["ai_patterns"].append("llm_integration")
            
            # حساب نقاط التعقيد
            analysis["complexity_score"] = len(files) + (len(dirs) * 2) + len(set(analysis["ai_patterns"]) * 5)
        
        return analysis
    
    def _detect_design_patterns(self, component_health):
        """اكتشاف أنماط التصميم المستخدمة"""
        patterns = []
        
        # فحص وجود مجلدات معينة
        structure = component_health["directory_structure"]
        
        if structure.get("src", {}).get("exists") and structure.get("configs", {}).get("exists"):
            patterns.append("Configuration Management Pattern")
        
        if structure.get("tests", {}).get("exists"):
            patterns.append("Test-Driven Development Pattern")
        
        if structure.get("docs", {}).get("exists"):
            patterns.append("Documentation-First Pattern")
        
        # فحص وجود ملف Universal-AI-Assistants
        if (self.base_path / "Universal-AI-Assistants").exists():
            patterns.append("Modular AI System Pattern")
        
        component_health["design_patterns"] = patterns
        
        if len(patterns) >= 3:
            component_health["strengths"].append(f"✅ أنماط تصميم متقدمة ({len(patterns)} نمط)")
            component_health["architecture_score"] += 15
        elif len(patterns) >= 1:
            component_health["strengths"].append(f"✅ أنماط تصميم أساسية ({len(patterns)} نمط)")
            component_health["architecture_score"] += 5
    
    def evaluate_ai_capabilities(self):
        """تقييم قدرات الذكاء الاصطناعي (استراتيجية Gemini)"""
        print("🧠 تقييم قدرات الذكاء الاصطناعي...")
        print("🤖 تطبيق معايير Gemini لتقييم القدرات...")
        
        capabilities = {
            "overall_score": 0,
            "provider_support": {},
            "model_types": {},
            "agent_capabilities": {},
            "integration_quality": {},
            "gemini_assessment": []
        }
        
        # فحص دعم مقدمي الخدمات
        potential_providers = [
            "gemini", "openai", "claude", "ollama", 
            "huggingface", "cohere", "anthropic"
        ]
        
        src_path = self.base_path / "src"
        if src_path.exists():
            all_files = list(src_path.rglob("*.py"))
            file_contents = self._read_python_files(all_files[:10])  # فحص أول 10 ملفات فقط
            
            for provider in potential_providers:
                provider_mentions = sum(1 for content in file_contents if provider.lower() in content.lower())
                if provider_mentions > 0:
                    capabilities["provider_support"][provider] = {
                        "mentions": provider_mentions,
                        "integration_level": "detected" if provider_mentions < 5 else "integrated"
                    }
        
        # تحليل دعم المقدمين
        providers_count = len(capabilities["provider_support"])
        if providers_count >= 4:
            capabilities["overall_score"] += 30
            capabilities["gemini_assessment"].append("🎯 Gemini: دعم ممتاز لمقدمي الخدمات المتعددين")
        elif providers_count >= 2:
            capabilities["overall_score"] += 20
            capabilities["gemini_assessment"].append("✅ Gemini: دعم جيد لمقدمي الخدمات")
        elif providers_count >= 1:
            capabilities["overall_score"] += 10
            capabilities["gemini_assessment"].append("⚠️ Gemini: دعم محدود لمقدمي الخدمات")
        else:
            capabilities["gemini_assessment"].append("🚨 Gemini: لا يوجد دعم واضح لمقدمي الخدمات")
        
        # فحص أنواع النماذج
        model_indicators = {
            "text_generation": ["generate", "completion", "chat"],
            "embeddings": ["embed", "vector", "similarity"],
            "image_analysis": ["vision", "image", "visual"],
            "code_generation": ["code", "programming", "script"],
            "reasoning": ["reasoning", "logic", "thinking"]
        }
        
        for model_type, indicators in model_indicators.items():
            found_indicators = sum(1 for content in file_contents 
                                 for indicator in indicators 
                                 if indicator in content.lower())
            if found_indicators > 0:
                capabilities["model_types"][model_type] = {
                    "indicators_found": found_indicators,
                    "support_level": "basic" if found_indicators < 3 else "advanced"
                }
        
        # تقييم أنواع النماذج
        model_types_count = len(capabilities["model_types"])
        if model_types_count >= 4:
            capabilities["overall_score"] += 25
            capabilities["gemini_assessment"].append("🎯 Gemini: دعم شامل لأنواع النماذج المختلفة")
        elif model_types_count >= 2:
            capabilities["overall_score"] += 15
            capabilities["gemini_assessment"].append("✅ Gemini: دعم جيد لأنواع النماذج")
        
        # فحص قدرات الوكلاء
        agent_indicators = ["agent", "assistant", "bot", "worker"]
        agent_mentions = sum(1 for content in file_contents 
                           for indicator in agent_indicators 
                           if indicator in content.lower())
        
        if agent_mentions > 10:
            capabilities["agent_capabilities"] = {
                "level": "advanced",
                "mentions": agent_mentions,
                "assessment": "نظام وكلاء متقدم"
            }
            capabilities["overall_score"] += 20
            capabilities["gemini_assessment"].append("🤖 Gemini: نظام وكلاء ذكيين متقدم")
        elif agent_mentions > 5:
            capabilities["agent_capabilities"] = {
                "level": "intermediate",
                "mentions": agent_mentions,
                "assessment": "نظام وكلاء متوسط"
            }
            capabilities["overall_score"] += 10
        
        # تقييم Gemini النهائي للقدرات
        if capabilities["overall_score"] >= 70:
            capabilities["gemini_assessment"].append("🎯 Gemini: قدرات ذكاء اصطناعي متقدمة وشاملة")
        elif capabilities["overall_score"] >= 50:
            capabilities["gemini_assessment"].append("✅ Gemini: قدرات ذكاء اصطناعي جيدة")
        elif capabilities["overall_score"] >= 30:
            capabilities["gemini_assessment"].append("⚠️ Gemini: قدرات ذكاء اصطناعي أساسية")
        else:
            capabilities["gemini_assessment"].append("🚨 Gemini: قدرات ذكاء اصطناعي ضعيفة")
        
        self.report["ai_capabilities"] = capabilities
        return capabilities
    
    def _read_python_files(self, file_paths):
        """قراءة محتوى ملفات Python (محدود للأمان)"""
        contents = []
        for file_path in file_paths:
            try:
                if file_path.stat().st_size < 100000:  # ملفات أصغر من 100KB فقط
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        contents.append(content)
            except Exception:
                continue
        return contents
    
    def assess_integration_quality(self):
        """تقييم جودة التكامل (استراتيجية Gemini)"""
        print("🔗 تقييم جودة التكامل...")
        print("🤖 تطبيق معايير Gemini لتحليل التكامل...")
        
        integration = {
            "overall_score": 0,
            "config_integration": {},
            "system_integration": {},
            "external_integration": {},
            "integration_patterns": [],
            "gemini_evaluation": []
        }
        
        # فحص تكامل التكوينات
        configs_path = self.base_path / "configs"
        if configs_path.exists():
            config_files = list(configs_path.glob("*.json")) + list(configs_path.glob("*.yaml"))
            if config_files:
                integration["config_integration"] = {
                    "config_files_count": len(config_files),
                    "status": "configured",
                    "files": [f.name for f in config_files]
                }
                integration["overall_score"] += 20
                integration["gemini_evaluation"].append("✅ Gemini: تكامل التكوينات موجود")
            else:
                integration["config_integration"] = {
                    "status": "missing",
                    "issue": "لا توجد ملفات تكوين"
                }
                integration["gemini_evaluation"].append("⚠️ Gemini: تكامل التكوينات مفقود")
        
        # فحص التكامل مع النظام الرئيسي
        main_system_indicators = [
            self.base_path.parent / "anubis_main_system",
            self.base_path.parent / "configs",
            self.base_path.parent / "database"
        ]
        
        connected_systems = sum(1 for path in main_system_indicators if path.exists())
        if connected_systems >= 2:
            integration["system_integration"] = {
                "connected_systems": connected_systems,
                "status": "well_integrated",
                "assessment": "تكامل جيد مع النظام الرئيسي"
            }
            integration["overall_score"] += 25
            integration["gemini_evaluation"].append("🔗 Gemini: تكامل ممتاز مع النظام الرئيسي")
        else:
            integration["system_integration"] = {
                "status": "isolated",
                "issue": "تكامل ضعيف مع النظام الرئيسي"
            }
            integration["gemini_evaluation"].append("⚠️ Gemini: نظام معزول - يحتاج تكامل أفضل")
        
        # فحص وجود Universal-AI-Assistants
        universal_ai_path = self.base_path / "Universal-AI-Assistants"
        if universal_ai_path.exists():
            integration["external_integration"]["universal_ai"] = {
                "exists": True,
                "has_reports": (universal_ai_path / "reports").exists(),
                "has_logs": (universal_ai_path / "logs").exists()
            }
            integration["overall_score"] += 15
            integration["integration_patterns"].append("Universal AI Integration Pattern")
            integration["gemini_evaluation"].append("🤖 Gemini: تكامل Universal AI موجود")
        
        # تحليل أنماط التكامل
        if len(integration["integration_patterns"]) > 0:
            integration["overall_score"] += 10
        
        # تقييم Gemini النهائي للتكامل
        if integration["overall_score"] >= 60:
            integration["gemini_evaluation"].append("🎯 Gemini: جودة تكامل ممتازة")
        elif integration["overall_score"] >= 40:
            integration["gemini_evaluation"].append("✅ Gemini: جودة تكامل جيدة")
        elif integration["overall_score"] >= 20:
            integration["gemini_evaluation"].append("⚠️ Gemini: جودة تكامل متوسطة")
        else:
            integration["gemini_evaluation"].append("🚨 Gemini: جودة تكامل ضعيفة")
        
        self.report["integration_analysis"] = integration
        return integration
    
    def generate_gemini_recommendations(self):
        """إنشاء توصيات Gemini المتخصصة"""
        print("🤖 إنشاء توصيات Gemini المتخصصة...")
        
        recommendations = []
        
        # تحليل البيانات المجمعة
        architecture = self.report["components"].get("ai_architecture", {})
        capabilities = self.report.get("ai_capabilities", {})
        integration = self.report.get("integration_analysis", {})
        
        # توصيات بناءً على المعمارية
        arch_score = architecture.get("architecture_score", 0)
        if arch_score < 60:
            recommendations.append("🏗️ Gemini: تحسين معمارية النظام - إضافة مكونات أساسية مفقودة")
        
        if not architecture.get("design_patterns"):
            recommendations.append("🎨 Gemini: تطبيق أنماط تصميم متقدمة للذكاء الاصطناعي")
        
        # توصيات بناءً على القدرات
        capabilities_score = capabilities.get("overall_score", 0)
        if capabilities_score < 50:
            recommendations.append("🧠 Gemini: تعزيز قدرات الذكاء الاصطناعي - إضافة دعم مقدمين جدد")
        
        if len(capabilities.get("provider_support", {})) < 3:
            recommendations.append("🔌 Gemini: تنويع مقدمي خدمات الذكاء الاصطناعي")
        
        # توصيات بناءً على التكامل
        integration_score = integration.get("overall_score", 0)
        if integration_score < 40:
            recommendations.append("🔗 Gemini: تحسين التكامل مع الأنظمة الخارجية")
        
        if not integration.get("config_integration", {}).get("status") == "configured":
            recommendations.append("⚙️ Gemini: إعداد ملفات تكوين شاملة للنظام")
        
        # توصيات متقدمة من Gemini
        recommendations.extend([
            "🔒 Gemini: تنفيذ أمان متقدم لمفاتيح API والبيانات الحساسة",
            "📊 Gemini: إضافة مراقبة أداء النماذج والوكلاء",
            "🧪 Gemini: تطوير اختبارات شاملة لجميع مكونات الذكاء الاصطناعي",
            "📈 Gemini: تحسين قابلية التوسع لدعم حمل عمل أكبر",
            "🔄 Gemini: إضافة آليات fallback ذكية للنماذج",
            "📚 Gemini: توثيق شامل لواجهات برمجة التطبيقات والاستخدام"
        ])
        
        self.report["gemini_recommendations"] = recommendations
        return recommendations
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة (منطق Gemini المتقدم)"""
        print("🏥 تقييم الحالة العامة بمنطق Gemini المتقدم...")
        
        # حساب النقاط المختلفة
        architecture_score = self.report["components"].get("ai_architecture", {}).get("architecture_score", 0)
        capabilities_score = self.report.get("ai_capabilities", {}).get("overall_score", 0)
        integration_score = self.report.get("integration_analysis", {}).get("overall_score", 0)
        
        # وزن النقاط (Gemini Weighting)
        weighted_score = (
            (architecture_score * 0.4) +  # 40% للمعمارية
            (capabilities_score * 0.35) + # 35% للقدرات
            (integration_score * 0.25)    # 25% للتكامل
        )
        
        # تحديد الحالة بناءً على منطق Gemini
        if weighted_score >= 75:
            self.report["overall_health"] = "excellent"
            health_text = "ممتاز"
            emoji = "🟢"
            gemini_verdict = "🎯 Gemini: نظام ذكاء اصطناعي متقدم وجاهز للإنتاج"
        elif weighted_score >= 60:
            self.report["overall_health"] = "good"
            health_text = "جيد"
            emoji = "🟡"
            gemini_verdict = "✅ Gemini: نظام ذكاء اصطناعي جيد مع إمكانيات تحسين"
        elif weighted_score >= 40:
            self.report["overall_health"] = "fair"
            health_text = "متوسط"
            emoji = "🟠"
            gemini_verdict = "⚠️ Gemini: نظام ذكاء اصطناعي يحتاج تطوير"
        else:
            self.report["overall_health"] = "poor"
            health_text = "يحتاج عمل"
            emoji = "🔴"
            gemini_verdict = "🚨 Gemini: نظام ذكاء اصطناعي يحتاج إعادة تطوير"
        
        print(f"\n{emoji} الحالة العامة: {health_text} ({weighted_score:.1f}/100)")
        print(f"🤖 {gemini_verdict}")
        
        # إضافة النقاط التفصيلية للتقرير
        self.report["performance_metrics"] = {
            "architecture_score": architecture_score,
            "capabilities_score": capabilities_score,
            "integration_score": integration_score,
            "weighted_total": weighted_score,
            "gemini_verdict": gemini_verdict
        }
        
        return self.report["overall_health"]
    
    def run_comprehensive_inspection(self):
        """تشغيل الفحص الشامل مع استراتيجية Gemini"""
        print("🤖 بدء فحص نظام الذكاء الاصطناعي الشامل مع استراتيجية Gemini")
        print("=" * 70)
        
        # تشغيل جميع عمليات الفحص
        self.analyze_ai_architecture()
        self.evaluate_ai_capabilities()
        self.assess_integration_quality()
        
        # تطبيق تحليل Gemini المتقدم
        self.generate_gemini_recommendations()
        self.evaluate_overall_health()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*70)
        print("📋 تقرير فحص نظام الذكاء الاصطناعي الشامل (استراتيجية Gemini)")
        print("="*70)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "fair": "🟠",
            "poor": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        metrics = self.report.get("performance_metrics", {})
        weighted_score = metrics.get("weighted_total", 0)
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']} ({weighted_score:.1f}/100)")
        
        # النقاط التفصيلية
        print(f"\n📊 التقييم التفصيلي:")
        print(f"   🏗️ المعمارية: {metrics.get('architecture_score', 0):.1f}/100")
        print(f"   🧠 القدرات: {metrics.get('capabilities_score', 0):.1f}/100")
        print(f"   🔗 التكامل: {metrics.get('integration_score', 0):.1f}/100")
        
        # تفاصيل المعمارية
        print(f"\n🏗️ تفاصيل المعمارية:")
        architecture = self.report["components"].get("ai_architecture", {})
        for dir_name, dir_info in architecture.get("directory_structure", {}).items():
            status = "✅ موجود" if dir_info.get("exists") else "❌ مفقود"
            files_count = dir_info.get("files_count", 0)
            print(f"   📁 {dir_name}: {status} ({files_count} ملف)")
        
        # أنماط التصميم
        patterns = architecture.get("design_patterns", [])
        if patterns:
            print(f"\n🎨 أنماط التصميم:")
            for pattern in patterns:
                print(f"   ✅ {pattern}")
        
        # قدرات الذكاء الاصطناعي
        print(f"\n🧠 قدرات الذكاء الاصطناعي:")
        capabilities = self.report.get("ai_capabilities", {})
        
        # مقدمو الخدمات
        providers = capabilities.get("provider_support", {})
        if providers:
            print(f"   🔌 مقدمو الخدمات:")
            for provider, info in providers.items():
                level = info.get("integration_level", "unknown")
                print(f"      • {provider}: {level}")
        
        # أنواع النماذج
        model_types = capabilities.get("model_types", {})
        if model_types:
            print(f"   🤖 أنواع النماذج:")
            for model_type, info in model_types.items():
                support = info.get("support_level", "unknown")
                print(f"      • {model_type}: {support}")
        
        # قدرات الوكلاء
        agent_caps = capabilities.get("agent_capabilities", {})
        if agent_caps:
            level = agent_caps.get("level", "unknown")
            mentions = agent_caps.get("mentions", 0)
            print(f"   🤖 قدرات الوكلاء: {level} ({mentions} إشارة)")
        
        # تحليل التكامل
        print(f"\n🔗 تحليل التكامل:")
        integration = self.report.get("integration_analysis", {})
        
        config_int = integration.get("config_integration", {})
        if config_int:
            status = config_int.get("status", "unknown")
            print(f"   ⚙️ تكامل التكوينات: {status}")
        
        system_int = integration.get("system_integration", {})
        if system_int:
            status = system_int.get("status", "unknown")
            print(f"   🔗 تكامل النظام: {status}")
        
        # رؤى Gemini
        print(f"\n🤖 رؤى Gemini:")
        for component in ["ai_architecture", "ai_capabilities", "integration_analysis"]:
            comp_data = self.report.get(component) or self.report["components"].get(component)
            if comp_data:
                insights = comp_data.get("gemini_insights") or comp_data.get("gemini_assessment") or comp_data.get("gemini_evaluation")
                if insights:
                    for insight in insights:
                        print(f"   {insight}")
        
        # توصيات Gemini
        print(f"\n🤖 توصيات Gemini:")
        for rec in self.report.get("gemini_recommendations", []):
            print(f"   {rec}")
        
        # حكم Gemini النهائي
        verdict = metrics.get("gemini_verdict", "")
        if verdict:
            print(f"\n{verdict}")
        
        print("\n" + "="*70)
        print("🤖 انتهى فحص نظام الذكاء الاصطناعي الشامل")
        print("="*70)
    
    def save_report(self):
        """حفظ التقرير في ملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"universal_ai_system_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    inspector = UniversalAISystemInspector()
    
    # تشغيل الفحص
    report = inspector.run_comprehensive_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    return report

if __name__ == "__main__":
    main()
