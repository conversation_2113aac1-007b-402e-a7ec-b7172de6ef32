# 🏺 مدير مسارات مشروع أنوبيس

## 📋 نظرة عامة
مجلد مخصص لإدارة جميع مسارات مشروع أنوبيس، يحتوي على أدوات التنقل السريع والوصول المباشر لجميع أجزاء المشروع.

## 🎯 الهدف
توفير نظام مركزي لإدارة مسارات المشروع وتسهيل التنقل بين الأنظمة المختلفة.

## 📁 محتويات المجلد

### 🛠️ الأدوات الأساسية:
- `project_paths_manager.py` - مدير المسارات الرئيسي
- `quick_access_shortcuts.py` - اختصارات الوصول السريع
- `project_navigation_helper.py` - مساعد التنقل (سيتم إنشاؤه)

### 📋 ملفات الإعدادات:
- `anubis_project_paths_config.json` - إعدادات المسارات الكاملة
- `anubis_navigation_shortcuts.json` - اختصارات التنقل
- `README.md` - هذا الملف

## 🗺️ خريطة المشروع

### 🏺 النظام الأساسي:
```
📁 الجذر/
├── main.py                    # النظام الرئيسي
├── README.md                  # التوثيق الأساسي
├── requirements.txt           # المتطلبات
├── docker-compose.yml         # Docker الأساسي
└── anubis_main_system/        # مجلد النظام الأساسي
```

### 🔒 النظام المعزول:
```
📁 anubis_isolation_system/
├── api/                       # خدمة API
├── worker/                    # خدمة Worker
├── monitor/                   # خدمة المراقبة
├── docker-compose-isolation.yml
└── README.md
```

### 🤖 فريق الذكاء الاصطناعي:
```
📁 anubis_ai_team/
├── team_workflow_manager.py   # مدير سير العمل
├── anubis_ai_collaboration_helper.py
├── anubis_gemini_cli_helper.py
└── README.md
```

### 📁 مجلد المسارات:
```
📁 anubis_project_paths/
├── project_paths_manager.py   # هذا المجلد
├── quick_access_shortcuts.py
└── README.md
```

## 🌐 نقاط الخدمات

### 🏺 النظام الأساسي:
- **الرئيسية:** http://localhost:8000
- **الصحة:** http://localhost:8000/health
- **التوثيق:** http://localhost:8000/docs

### 🔒 النظام المعزول:
- **API:** http://localhost:8080
- **التوثيق:** http://localhost:8080/docs
- **الحالة:** http://localhost:8080/status

### 📊 المراقبة:
- **لوحة المراقبة:** http://localhost:9090
- **مراقبة الخدمات:** http://localhost:9090/monitor/services
- **حالة النظام:** http://localhost:9090/monitor/system

## 🚀 الاستخدام

### تشغيل مدير المسارات:
```bash
python anubis_project_paths/project_paths_manager.py
```

### الوصول السريع:
```bash
python anubis_project_paths/quick_access_shortcuts.py
```

### عرض بنية المشروع:
```python
from project_paths_manager import AnubisProjectPathsManager

manager = AnubisProjectPathsManager()
manager.display_project_structure()
```

## 🔗 اختصارات التنقل

### 📂 فتح المجلدات:
- **النظام الأساسي:** `code anubis_main_system`
- **النظام المعزول:** `code anubis_isolation_system`
- **فريق الذكاء الاصطناعي:** `code anubis_ai_team`
- **مجلد المسارات:** `code anubis_project_paths`

### 🌐 فتح الخدمات:
- **النظام الأساسي:** http://localhost:8000
- **النظام المعزول:** http://localhost:8080
- **المراقبة:** http://localhost:9090

### 🧪 تشغيل الاختبارات:
```bash
# اختبار API شامل
python anubis_api_comprehensive_test.py

# اختبار النظام الكامل
python anubis_complete_system_test.py

# فحص حالة النظام المعزول
python anubis_isolation_system/anubis_isolation_status_checker.py
```

## 📊 ميزات مدير المسارات

### 1. 🗺️ خريطة شاملة:
- جميع مسارات المشروع
- حالة وجود الملفات
- نقاط الخدمات
- اختصارات التنقل

### 2. 🔍 فحص المسارات:
- التحقق من وجود الملفات
- عرض المسارات المطلقة
- فحص حالة الخدمات

### 3. 🚀 الوصول السريع:
- قائمة تفاعلية
- فتح الملفات والمجلدات
- تشغيل الاختبارات
- فتح روابط الخدمات

### 4. 📋 إدارة الإعدادات:
- حفظ إعدادات المسارات
- تصدير اختصارات التنقل
- تحديث المسارات تلقائياً

## 🛠️ التخصيص والتطوير

### إضافة مسار جديد:
1. تحديث `generate_paths_config()` في `project_paths_manager.py`
2. إضافة المسار للفئة المناسبة
3. تحديث اختصارات التنقل

### إضافة خدمة جديدة:
1. تحديث `service_endpoints` في الإعدادات
2. إضافة URL الخدمة
3. تحديث سكريبت الوصول السريع

### تحسين الأداء:
- تحسين فحص المسارات
- إضافة ذاكرة تخزين مؤقت
- تحسين واجهة المستخدم

## 🎯 أفضل الممارسات

### 1. 📁 تنظيم المسارات:
- استخدم مسارات نسبية
- حافظ على بنية واضحة
- وثق المسارات الجديدة

### 2. 🔄 التحديث المستمر:
- حدث المسارات عند إضافة ملفات جديدة
- تحقق من صحة المسارات دورياً
- حافظ على تزامن الإعدادات

### 3. 🚀 الاستخدام الفعال:
- استخدم الوصول السريع للمهام المتكررة
- احفظ اختصارات مخصصة
- استفد من أدوات التنقل

## 📈 التطوير المستقبلي

### ميزات مخططة:
- [ ] واجهة ويب للتنقل
- [ ] تكامل مع IDE
- [ ] مراقبة المسارات في الوقت الفعلي
- [ ] نظام إشارات مرجعية متقدم
- [ ] تصدير خريطة المشروع

### تحسينات مقترحة:
- [ ] دعم مسارات متعددة المشاريع
- [ ] نظام بحث متقدم
- [ ] تكامل مع Git
- [ ] إحصائيات الاستخدام

---

**🏺 مدير مسارات أنوبيس - تنقل سهل وسريع في المشروع!**
