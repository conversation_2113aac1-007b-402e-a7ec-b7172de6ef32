# 🏺 طلب مساعدة Gemini CLI - مشكلة النظام المعزول بـ Docker

## 📋 السياق العام:
نحن نطور **نظام أنوبيس للمساعدين الذكيين** ونحاول تشغيل نظام معزول باستخدام Docker Compose. واجهنا مشكلة في خدمة API التي تعيد التشغيل باستمرار.

## 🔧 التقنيات المستخدمة:
- **Docker**: الإصدار 28.3.2
- **Docker Compose**: الإصدار v2.38.2-desktop.1
- **Python**: 3.11-slim
- **FastAPI**: 0.104.1
- **PostgreSQL**: 15-alpine
- **Redis**: 7-alpine

## 📊 الحالة الحالية:

### ✅ **ما يعمل:**
- ✅ قاعدة البيانات PostgreSQL (صحية)
- ✅ خدمة Redis (صحية)
- ✅ الشبكة المعزولة (تم إنشاؤها)
- ✅ الأحجام (تم إنشاؤها)

### ❌ **المشكلة:**
- ❌ خدمة API تعيد التشغيل باستمرار
- ❌ الحالة: `restarting`
- ❌ لا يمكن الوصول للـ API على المنفذ 8080

## 📁 بنية المشروع:

```
Universal-AI-Assistants/
├── docker-compose-anubis-isolation.yml
├── isolation/
│   ├── api/
│   │   ├── Dockerfile
│   │   ├── requirements.txt
│   │   └── main.py
│   ├── worker/
│   │   ├── Dockerfile
│   │   ├── requirements.txt
│   │   └── worker.py
│   └── monitor/
│       ├── Dockerfile
│       ├── requirements.txt
│       └── monitor.py
└── anubis_isolation_status_checker.py
```

## 🐳 ملف Docker Compose:

```yaml
version: '3.8'

services:
  anubis-api-isolated:
    build:
      context: ./isolation/api
      dockerfile: Dockerfile
    container_name: anubis-api-isolated
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-api-data:/app/data
      - anubis-api-logs:/app/logs
      - anubis-api-config:/app/config
    environment:
      - ANUBIS_SERVICE=api
      - ANUBIS_ISOLATED=true
      - PYTHONUNBUFFERED=1
      - API_HOST=0.0.0.0
      - API_PORT=8080
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    mem_limit: 512m
    cpus: 0.5
    pids_limit: 100
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 🐳 Dockerfile للـ API:

```dockerfile
FROM python:3.11-slim

RUN groupadd -r anubis && useradd -r -g anubis anubis

RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

RUN mkdir -p /app/data /app/logs /app/config /app/src
RUN chown -R anubis:anubis /app

COPY requirements.txt /app/
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

COPY . /app/src/
RUN chown -R anubis:anubis /app

USER anubis
WORKDIR /app/src

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["python", "main.py"]
```

## 📦 متطلبات Python:

```
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
requests==2.31.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
aiofiles==23.2.1
httpx==0.25.2
sqlalchemy==2.0.23
alembic==1.13.0
psycopg2-binary==2.9.9
redis==5.0.1
celery==5.3.4
prometheus-client==0.19.0
structlog==23.2.0
```

## 🐍 كود main.py (مبسط):

```python
import os
import uvicorn
from fastapi import FastAPI
from datetime import datetime

app = FastAPI(title="🏺 Anubis API Service")

SERVICE_NAME = os.getenv("ANUBIS_SERVICE", "api")
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8080"))

@app.get("/")
async def root():
    return {"message": "🏺 Anubis API Service - Isolated"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": SERVICE_NAME}

if __name__ == "__main__":
    uvicorn.run(app, host=API_HOST, port=API_PORT)
```

## 🔍 الأوامر المنفذة:

```bash
# بناء الصورة - نجح ✅
docker-compose -f docker-compose-anubis-isolation.yml build anubis-api-isolated

# تشغيل قاعدة البيانات و Redis - نجح ✅
docker-compose -f docker-compose-anubis-isolation.yml up -d anubis-database-isolated anubis-redis-isolated

# تشغيل API - فشل ❌
docker-compose -f docker-compose-anubis-isolation.yml up -d anubis-api-isolated
```

## 📊 حالة الحاويات:

```
NAME                       STATUS                 HEALTH
anubis-api-isolated        restarting            (no health check)
anubis-database-isolated   running               healthy
anubis-redis-isolated      running               healthy
```

## ❓ الأسئلة المحددة لـ Gemini:

### 1. **تشخيص المشكلة:**
- لماذا تعيد خدمة API التشغيل باستمرار؟
- كيف يمكن فحص سجلات الأخطاء بشكل صحيح؟
- ما هي أفضل طريقة لتشخيص مشاكل Docker Compose؟

### 2. **إصلاح Dockerfile:**
- هل هناك مشكلة في بنية Dockerfile؟
- هل إعدادات الأمان تسبب مشاكل؟
- هل المستخدم غير المميز يسبب مشاكل في الصلاحيات؟

### 3. **إصلاح Docker Compose:**
- هل إعدادات الشبكة صحيحة؟
- هل متغيرات البيئة مضبوطة بشكل صحيح؟
- هل هناك تعارض في المنافذ؟

### 4. **إصلاح كود Python:**
- هل هناك مشكلة في كود FastAPI؟
- هل إعدادات uvicorn صحيحة؟
- هل المتطلبات متوافقة؟

### 5. **أفضل الممارسات:**
- ما هي أفضل طريقة لتشغيل FastAPI في Docker؟
- كيف يمكن تحسين أداء النظام المعزول؟
- ما هي إعدادات الأمان المثلى؟

## 🛠️ الحلول المطلوبة:

1. **خطوات تشخيص المشكلة**
2. **إصلاح ملفات Docker**
3. **تحسين إعدادات الأمان**
4. **أوامر فحص وتشخيص مفيدة**
5. **نصائح لتجنب المشاكل المستقبلية**

## 📞 كيفية تشغيل Gemini CLI:

```bash
# إذا كان Gemini CLI مثبت:
gemini chat --file="anubis_gemini_docker_help_request.md"

# أو نسخ المحتوى إلى:
# https://gemini.google.com/
```

## 🎯 الهدف النهائي:

الحصول على نظام معزول يعمل بشكل مثالي مع:
- ✅ API متاح على http://localhost:8080
- ✅ جميع الخدمات صحية
- ✅ أمان عالي ومعزول
- ✅ أداء مثالي

---

**🏺 شكراً لمساعدة نظام أنوبيس! نتطلع لحلولكم الخبيرة.**
