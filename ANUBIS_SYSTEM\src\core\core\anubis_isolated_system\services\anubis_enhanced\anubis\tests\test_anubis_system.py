#!/usr/bin/env python3
"""
🏺 اختبار نظام أنوبيس الشامل
Anubis System Comprehensive Test

اختبار شامل لنظام أنوبيس مع قاعدة البيانات والوكلاء الذكيين
"""

import json
import os
import sys
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "database"))


def test_anubis_database():
    """اختبار قاعدة بيانات أنوبيس"""
    print("🏺 ═══════════════════════════════════════════════════════════════")
    print("   اختبار قاعدة بيانات أنوبيس")
    print("   Anubis Database Test")
    print("═══════════════════════════════════════════════════════════════ 🏺")

    try:
        from database_manager import AnubisDatabaseManager

        # إنشاء مدير قاعدة البيانات
        print("\n🔧 إنشاء مدير قاعدة البيانات...")
        db_manager = AnubisDatabaseManager()

        # تهيئة قاعدة البيانات
        print("🔧 تهيئة قاعدة البيانات...")
        if db_manager.initialize_database():
            print("✅ تم تهيئة قاعدة البيانات بنجاح")

            # اختبار إضافة مشروع
            print("\n🔧 اختبار إضافة مشروع...")
            project_id = db_manager.add_project_to_db(
                "نظام أنوبيس للمساعدين الذكيين",
                str(Path(__file__).parent),
                "python",
                "نظام ذكي شامل للمساعدة في تطوير البرمجيات",
            )

            if project_id > 0:
                print(f"✅ تم إضافة المشروع بنجاح (ID: {project_id})")

                # اختبار حفظ تحليل
                print("🔧 اختبار حفظ نتائج التحليل...")
                analysis_data = {
                    "files_analyzed": 25,
                    "lines_of_code": 2500,
                    "agents_used": ["error_detector", "project_analyzer"],
                    "analysis_duration": "45 seconds",
                }

                results = {
                    "overall_score": 92.5,
                    "issues_found": 5,
                    "recommendations": 8,
                    "code_quality": "excellent",
                    "security_score": 95.0,
                    "performance_score": 88.0,
                }

                analysis_id = db_manager.save_analysis_results(
                    project_id, "anubis_system_test", analysis_data, results, 92.5
                )

                if analysis_id > 0:
                    print(f"✅ تم حفظ التحليل بنجاح (ID: {analysis_id})")

                # اختبار حفظ الأخطاء
                print("🔧 اختبار حفظ الأخطاء...")
                test_errors = [
                    {
                        "file_path": "test_file.py",
                        "line_number": 42,
                        "error_type": "syntax_error",
                        "severity": "high",
                        "message": "خطأ نحوي في السطر 42",
                    },
                    {
                        "file_path": "another_file.py",
                        "line_number": 15,
                        "error_type": "logic_error",
                        "severity": "medium",
                        "message": "خطأ منطقي محتمل",
                    },
                ]

                error_ids = db_manager.save_errors(project_id, test_errors)
                print(f"✅ تم حفظ {len(error_ids)} خطأ")

                # عرض إحصائيات قاعدة البيانات
                print("\n📊 إحصائيات قاعدة البيانات:")
                dashboard_data = db_manager.get_dashboard_data()
                stats = dashboard_data.get("stats", {})

                for key, value in stats.items():
                    print(f"   {key}: {value}")

                # عرض تاريخ المشروع
                print(f"\n📈 تاريخ المشروع {project_id}:")
                history = db_manager.get_project_history(project_id)
                print(f"   إجمالي التحليلات: {history.get('total_analyses', 0)}")
                print(f"   التحليلات بحسب الوكيل: {history.get('analyses_by_agent', {})}")

                # اختبار النسخ الاحتياطي
                print("\n💾 اختبار النسخ الاحتياطي...")
                if db_manager.backup_database():
                    print("✅ تم إنشاء النسخة الاحتياطية بنجاح")

            else:
                print("❌ فشل في إضافة المشروع")
                return False

            # إغلاق الاتصال
            db_manager.close()
            print("\n🏺 تم إغلاق اتصال قاعدة البيانات")
            return True

        else:
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_anubis_system():
    """اختبار النظام الكامل مع قاعدة البيانات"""
    print("\n🏺 ═══════════════════════════════════════════════════════════════")
    print("   اختبار نظام أنوبيس الكامل")
    print("   Complete Anubis System Test")
    print("═══════════════════════════════════════════════════════════════ 🏺")

    try:
        from anubis.core.assistant_system import UniversalAssistantSystem
        from anubis.core.config_manager import ConfigManager

        # تحميل التكوين
        print("\n🔧 تحميل إعدادات النظام...")
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # إنشاء النظام
        print("🔧 إنشاء نظام أنوبيس...")
        system = UniversalAssistantSystem(
            project_path=str(Path(__file__).parent), config=config, verbose=True
        )

        print(f"✅ تم إنشاء النظام: {system.system_name}")
        print(f"📊 نوع المشروع: {system.project_type}")
        print(f"🤖 الوكلاء المتاحين: {len(system.available_agents)}")
        print(f"🏺 قاعدة البيانات: {'متصلة' if system.db_manager else 'غير متاحة'}")

        # تشغيل التحليل الشامل
        print("\n🔍 تشغيل التحليل الشامل...")
        results = system.analyze_project()

        if results.get("success", False):
            print("✅ تم التحليل بنجاح!")

            # عرض ملخص النتائج
            summary = results.get("summary", {})
            print(f"\n📊 ملخص التحليل:")
            print(f"   إجمالي الوكلاء: {summary.get('total_agents', 0)}")
            print(f"   الوكلاء الناجحين: {summary.get('successful_agents', 0)}")
            print(f"   الوكلاء الفاشلين: {summary.get('failed_agents', 0)}")

            # عرض نتائج كل وكيل
            agents_results = results.get("agents_results", {})
            print(f"\n🤖 نتائج الوكلاء:")
            for agent_name, result in agents_results.items():
                status = "✅ نجح" if result.get("success", False) else "❌ فشل"
                print(f"   {agent_name}: {status}")

                if result.get("success", False) and "analysis" in result:
                    analysis = result["analysis"]
                    if "summary" in analysis:
                        agent_summary = analysis["summary"]
                        print(f"      الملفات المحللة: {agent_summary.get('files_analyzed', 0)}")
                        if "score" in agent_summary:
                            print(f"      النتيجة: {agent_summary['score']}")

            # فحص قاعدة البيانات
            if system.db_manager:
                print(f"\n🏺 فحص قاعدة البيانات...")
                dashboard_data = system.db_manager.get_dashboard_data()
                stats = dashboard_data.get("stats", {})
                print(f"   المشاريع في قاعدة البيانات: {stats.get('total_projects', 0)}")
                print(f"   التحليلات المحفوظة: {stats.get('total_analyses', 0)}")
                print(f"   الأخطاء المسجلة: {stats.get('total_errors', 0)}")

            return True

        else:
            print("❌ فشل في التحليل")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_anubis_integration():
    """اختبار التكامل الشامل"""
    print("\n🏺 ═══════════════════════════════════════════════════════════════")
    print("   اختبار التكامل الشامل لنظام أنوبيس")
    print("   Anubis Integration Test")
    print("═══════════════════════════════════════════════════════════════ 🏺")

    # اختبار قاعدة البيانات
    print("🔧 المرحلة 1: اختبار قاعدة البيانات...")
    db_test = test_anubis_database()

    # اختبار النظام الكامل
    print("\n🔧 المرحلة 2: اختبار النظام الكامل...")
    system_test = test_anubis_system()

    # النتيجة النهائية
    print("\n🏺 ═══════════════════════════════════════════════════════════════")
    print("   نتائج اختبار نظام أنوبيس")
    print("   Anubis Test Results")
    print("═══════════════════════════════════════════════════════════════ 🏺")

    print(f"🏺 قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    print(f"🤖 النظام الكامل: {'✅ نجح' if system_test else '❌ فشل'}")

    overall_success = db_test and system_test
    print(
        f"\n🎯 النتيجة الإجمالية: {'✅ نجح جميع الاختبارات' if overall_success else '❌ فشل في بعض الاختبارات'}"
    )

    if overall_success:
        print("\n🎉 مبروك! نظام أنوبيس جاهز للاستخدام!")
        print("🏺 يمكنك الآن استخدام النظام لتحليل مشاريعك")
        print("📊 جميع البيانات ستحفظ في قاعدة البيانات")
        print("🤖 الوكلاء الذكيين جاهزين للعمل")
    else:
        print("\n⚠️ يرجى مراجعة الأخطاء وإصلاحها قبل الاستخدام")

    return overall_success


def main():
    """الدالة الرئيسية"""
    print("🏺 مرحباً بك في نظام أنوبيس للمساعدين الذكيين!")
    print("🏺 Welcome to Anubis AI Assistants System!")
    print("🏺 نظام ذكي شامل لتحليل وتطوير المشاريع البرمجية")

    # تشغيل اختبار التكامل الشامل
    success = test_anubis_integration()

    if success:
        print("\n🏺 شكراً لاستخدام نظام أنوبيس!")
        return 0
    else:
        print("\n❌ فشل في اختبار النظام")
        return 1


if __name__ == "__main__":
    exit(main())
