{"project_info": {"path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp48ro1pjr", "name": "tmp48ro1pjr", "type": "custom", "structure": {"project_exists": true, "is_directory": true, "files_count": 3, "directories_count": 0, "python_files": ["poorly_documented.py", "test_example.py", "well_documented.py"], "config_files": [], "data_files": []}, "agent_type": "project_analyzer", "analysis_time": "2025-07-14T12:47:19.977309"}, "project_size_analysis": {"total_files": 3, "total_lines": 30, "code_lines": 21, "comment_lines": 0, "blank_lines": 9, "file_types": {".py": 3}, "largest_files": [{"file": "well_documented.py", "lines": 18}, {"file": "poorly_documented.py", "lines": 8}, {"file": "test_example.py", "lines": 4}], "directory_structure": {}}, "complexity_analysis": {"cyclomatic_complexity": 0, "cognitive_complexity": 0, "function_complexity": [], "class_complexity": [], "module_complexity": [{"file": "poorly_documented.py", "complexity": 1}, {"file": "test_example.py", "complexity": 1}, {"file": "well_documented.py", "complexity": 1}], "complexity_distribution": {"low": 3, "medium": 0, "high": 0}, "average_complexity": 1.0}, "dependency_analysis": {"total_dependencies": 0, "external_dependencies": [], "internal_dependencies": [], "dependency_tree": {}, "outdated_dependencies": [], "security_vulnerabilities": []}, "architecture_analysis": {"project_type": "custom", "directory_structure": {"depth": 0, "directories": [], "organization_score": 0.0}, "design_patterns": [], "architectural_style": "Monolithic", "modularity_score": 90, "coupling_analysis": {}, "cohesion_analysis": {}}, "performance_analysis": {"potential_bottlenecks": [], "optimization_opportunities": [], "resource_usage_patterns": {}, "scalability_assessment": {}}, "security_analysis": {"security_issues": [], "vulnerability_assessment": {}, "security_score": 100, "recommendations": []}, "maintainability_analysis": {"maintainability_index": 36.0, "documentation_coverage": 40.0, "test_coverage_estimate": 33.33333333333333, "code_duplication": 0, "technical_debt": []}, "recommendations": [{"category": "architecture", "priority": "high", "title": "تحسين الهيكل المعماري", "description": "فصل الاهتمامات وتطبيق أنماط التصميم", "action": "إعادة تنظيم الكود حسب المسؤوليات"}, {"category": "performance", "priority": "medium", "title": "تحسين الأداء", "description": "تحسين الخوارزميات وتقليل التعقيد", "action": "مراجعة الحلقات والاستعلامات"}, {"category": "security", "priority": "high", "title": "تعزيز الأمان", "description": "إضافة طبقات حماية وتشفير البيانات الحساسة", "action": "مراجعة نقاط الضعف الأمنية"}, {"category": "maintainability", "priority": "medium", "title": "تحسين قابلية الصيانة", "description": "إضافة توثيق واختبارات شاملة", "action": "كتابة docstrings واختبارات وحدة"}], "summary": {"overall_score": 69.6, "project_size": "3 ملف", "code_lines": 21, "complexity_level": "من<PERSON><PERSON>ض", "security_status": "<PERSON>ي<PERSON>", "maintainability_level": "ضعيف", "recommendations_count": 4, "analysis_time": "2025-07-14T12:47:19.986023"}}