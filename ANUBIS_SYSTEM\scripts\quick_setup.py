#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 إعداد سريع لنظام أنوبيس
Anubis Quick Setup Script

سكريبت لإعداد النظام بسرعة وسهولة
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import secrets
import string

class AnubisQuickSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / ".venv"
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("🏺" + "=" * 60)
        print("🏺 إعداد سريع لنظام أنوبيس")
        print("🏺 Anubis Quick Setup")
        print("🏺" + "=" * 60)
    
    def generate_secure_password(self, length=32):
        """إنتاج كلمة مرور آمنة"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def check_python_version(self):
        """فحص إصدار Python"""
        print("🐍 فحص إصدار Python...")
        
        if sys.version_info < (3, 8):
            print("❌ يتطلب Python 3.8 أو أحدث")
            return False
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    
    def create_virtual_environment(self):
        """إنشاء بيئة افتراضية"""
        print("📦 إنشاء البيئة الافتراضية...")
        
        if self.venv_path.exists():
            print("✅ البيئة الافتراضية موجودة بالفعل")
            return True
        
        try:
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], check=True)
            print("✅ تم إنشاء البيئة الافتراضية")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء البيئة الافتراضية")
            return False
    
    def install_basic_requirements(self):
        """تثبيت المتطلبات الأساسية"""
        print("📚 تثبيت المتطلبات الأساسية...")
        
        # تحديد مسار pip في البيئة الافتراضية
        if os.name == 'nt':  # Windows
            pip_path = self.venv_path / "Scripts" / "pip.exe"
        else:  # Linux/Mac
            pip_path = self.venv_path / "bin" / "pip"
        
        basic_packages = [
            "fastapi", "uvicorn", "pydantic", "requests", 
            "click", "psutil", "python-dotenv"
        ]
        
        try:
            cmd = [str(pip_path), "install"] + basic_packages
            subprocess.run(cmd, check=True, cwd=self.project_root)
            print("✅ تم تثبيت المتطلبات الأساسية")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المتطلبات")
            return False
    
    def create_env_file(self):
        """إنشاء ملف .env مع كلمات مرور آمنة"""
        print("🔐 إنشاء ملف .env مع كلمات مرور آمنة...")
        
        env_file = self.project_root / ".env"
        
        if env_file.exists():
            print("⚠️ ملف .env موجود بالفعل. سيتم إنشاء .env.new")
            env_file = self.project_root / ".env.new"
        
        # إنتاج كلمات مرور آمنة
        passwords = {
            "SECRET_KEY": self.generate_secure_password(64),
            "MYSQL_ROOT_PASSWORD": self.generate_secure_password(),
            "MYSQL_PASSWORD": self.generate_secure_password(),
            "REDIS_PASSWORD": self.generate_secure_password(),
            "N8N_BASIC_AUTH_PASSWORD": self.generate_secure_password(),
            "GF_SECURITY_ADMIN_PASSWORD": self.generate_secure_password(),
            "JWT_SECRET_KEY": self.generate_secure_password(64),
            "ENCRYPTION_KEY": self.generate_secure_password(32),
            "JUPYTER_TOKEN": self.generate_secure_password()
        }
        
        env_content = f"""# 🏺 نظام أنوبيس - متغيرات البيئة (تم إنتاجها تلقائياً)
# Anubis System - Environment Variables (Auto-generated)

# ===== إعدادات النظام الأساسي =====
ANUBIS_ENV=development
DEBUG=true
SECRET_KEY={passwords['SECRET_KEY']}

# ===== قاعدة البيانات =====
MYSQL_ROOT_PASSWORD={passwords['MYSQL_ROOT_PASSWORD']}
MYSQL_DATABASE=anubis_system
MYSQL_USER=anubis
MYSQL_PASSWORD={passwords['MYSQL_PASSWORD']}
DATABASE_URL=mysql://anubis:{passwords['MYSQL_PASSWORD']}@localhost:3306/anubis_system

# ===== Redis =====
REDIS_PASSWORD={passwords['REDIS_PASSWORD']}
REDIS_URL=redis://:{passwords['REDIS_PASSWORD']}@localhost:6379

# ===== N8N =====
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD={passwords['N8N_BASIC_AUTH_PASSWORD']}

# ===== Grafana =====
GF_SECURITY_ADMIN_USER=admin
GF_SECURITY_ADMIN_PASSWORD={passwords['GF_SECURITY_ADMIN_PASSWORD']}

# ===== الأمان =====
JWT_SECRET_KEY={passwords['JWT_SECRET_KEY']}
ENCRYPTION_KEY={passwords['ENCRYPTION_KEY']}

# ===== التطوير =====
JUPYTER_TOKEN={passwords['JUPYTER_TOKEN']}

# ===== الشبكة =====
ANUBIS_HOST=0.0.0.0
ANUBIS_PORT=8000

# ===== السجلات =====
LOG_LEVEL=INFO
LOG_FILE=logs/anubis.log
"""
        
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)
        
        print(f"✅ تم إنشاء {env_file.name}")
        print("🔐 تم إنتاج كلمات مرور آمنة تلقائياً")
        return True
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء المجلدات المطلوبة...")
        
        required_dirs = [
            "logs", "data/database", "config/mysql", 
            "config/security", "tests"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
        
        print("✅ تم إنشاء المجلدات المطلوبة")
        return True
    
    def test_installation(self):
        """اختبار التثبيت"""
        print("🧪 اختبار التثبيت...")
        
        try:
            # اختبار استيراد النظام الأساسي
            sys.path.insert(0, str(self.project_root / "src"))
            from core.main import app
            print("✅ النظام الأساسي يعمل")
            
            # اختبار CLI
            cli_path = self.project_root / "src" / "cli" / "anubis_cli.py"
            if cli_path.exists():
                print("✅ CLI متوفر")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            return False
    
    def print_next_steps(self):
        """طباعة الخطوات التالية"""
        print("\n🎉 تم إعداد نظام أنوبيس بنجاح!")
        print("=" * 60)
        print("📋 الخطوات التالية:")
        print("1. تفعيل البيئة الافتراضية:")
        
        if os.name == 'nt':  # Windows
            print("   .venv\\Scripts\\activate")
        else:  # Linux/Mac
            print("   source .venv/bin/activate")
        
        print("2. تشغيل النظام:")
        print("   python main.py")
        print("3. أو استخدام CLI:")
        print("   python src/cli/anubis_cli.py system start")
        print("4. فتح المتصفح على:")
        print("   http://localhost:8000")
        print("\n🔐 ملاحظة: تم إنتاج كلمات مرور آمنة في ملف .env")
        print("📚 للمزيد من المعلومات، راجع docs/README.md")
    
    def run_setup(self):
        """تشغيل الإعداد الكامل"""
        self.print_header()
        
        steps = [
            ("فحص Python", self.check_python_version),
            ("إنشاء البيئة الافتراضية", self.create_virtual_environment),
            ("تثبيت المتطلبات", self.install_basic_requirements),
            ("إنشاء ملف .env", self.create_env_file),
            ("إنشاء المجلدات", self.create_directories),
            ("اختبار التثبيت", self.test_installation)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if not step_func():
                print(f"❌ فشل في: {step_name}")
                return False
        
        self.print_next_steps()
        return True

if __name__ == "__main__":
    setup = AnubisQuickSetup()
    success = setup.run_setup()
    
    if not success:
        print("\n❌ فشل في الإعداد. راجع الأخطاء أعلاه.")
        sys.exit(1)
    
    print("\n✅ تم الإعداد بنجاح!")
