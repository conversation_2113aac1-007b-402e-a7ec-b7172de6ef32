#!/bin/bash
# سكريبت تشغيل نظام الذكاء الاصطناعي الشامل المعزول

echo "🤖 بدء تشغيل نظام الذكاء الاصطناعي الشامل المعزول..."

# التحقق من المتطلبات
echo "🔍 فحص المتطلبات..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# التحقق من الموارد المتاحة
echo "💾 فحص الموارد المتاحة..."
AVAILABLE_MEMORY=$(free -m | awk 'NR==2{printf "%.0f", $7}')
if [ "$AVAILABLE_MEMORY" -lt 4096 ]; then
    echo "⚠️ تحذير: الذاكرة المتاحة أقل من 4GB - قد يؤثر على الأداء"
fi

# الانتقال لمجلد الذكاء الاصطناعي
cd universal_ai_system

# إنشاء البنية التحتية للذكاء الاصطناعي
echo "🏗️ إنشاء البنية التحتية للذكاء الاصطناعي..."
mkdir -p models data configs logs cache embeddings monitoring security
mkdir -p data/vectors data/embeddings data/models_cache
mkdir -p logs/api logs/models logs/security
mkdir -p security/certificates security/keys

# تعيين صلاحيات الأمان
echo "🔒 تطبيق صلاحيات الأمان..."
chmod 700 security models
chmod 750 data configs logs cache embeddings
chmod 755 monitoring

# إنشاء ملفات التكوين المطلوبة
echo "⚙️ إنشاء ملفات التكوين..."

# ملف requirements.txt
cat > requirements.txt << EOF
torch>=2.0.0
transformers>=4.30.0
langchain>=0.0.200
openai>=1.0.0
google-generativeai>=0.5.0
anthropic>=0.25.0
chromadb>=0.4.0
faiss-cpu>=1.7.0
sentence-transformers>=2.2.0
tiktoken>=0.5.0
redis>=4.5.0
prometheus-client>=0.16.0
fastapi>=0.100.0
uvicorn>=0.22.0
pydantic>=2.0.0
python-multipart>=0.0.6
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=4.0.0
cryptography>=41.0.0
EOF

# ملف إعدادات المصادقة لقاعدة البيانات المتجهة
cat > security/chroma_auth.txt << EOF
admin:$(openssl rand -base64 32)
EOF

# إنشاء الشبكات المعزولة
echo "🌐 إنشاء الشبكات المعزولة للذكاء الاصطناعي..."
docker network create anubis-ai-net --driver bridge 2>/dev/null || true
docker network create anubis-ai-models-net --driver bridge --internal 2>/dev/null || true
docker network create anubis-ai-data-net --driver bridge --internal 2>/dev/null || true

# بناء النظام
echo "🔨 بناء نظام الذكاء الاصطناعي..."
docker-compose build

# تشغيل خدمات البنية التحتية أولاً
echo "🗄️ تشغيل خدمات البنية التحتية..."
docker-compose up -d anubis-ai-vector-db anubis-ai-redis-cache anubis-ai-monitor

# انتظار تجهيز الخدمات
echo "⏳ انتظار تجهيز خدمات البنية التحتية..."
sleep 30

# تشغيل خادم النماذج
echo "🤖 تشغيل خادم النماذج..."
docker-compose up -d anubis-ai-model-server

# انتظار تجهيز خادم النماذج
echo "⏳ انتظار تجهيز خادم النماذج..."
sleep 20

# تشغيل النظام الرئيسي للذكاء الاصطناعي
echo "🧠 تشغيل النظام الرئيسي للذكاء الاصطناعي..."
docker-compose up -d anubis-universal-ai

# التحقق من الحالة النهائية
echo "📊 فحص حالة النظام..."
sleep 20
docker-compose ps

# عرض معلومات الاتصال
echo ""
echo "✅ تم تشغيل نظام الذكاء الاصطناعي الشامل في بيئة معزولة!"
echo ""
echo "🌐 الخدمات المتاحة:"
echo "   🤖 واجهة الذكاء الاصطناعي الرئيسية: http://localhost:8090"
echo "   🔧 إدارة النماذج: http://localhost:8091"
echo "   📊 مراقبة النظام: http://localhost:9092"
echo "   🗄️ قاعدة البيانات المتجهة: http://localhost:8000"
echo "   🤖 خادم النماذج المحلي: http://localhost:11434"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات الشاملة: docker-compose logs -f"
echo "   سجلات الذكاء الاصطناعي: docker-compose logs -f anubis-universal-ai"
echo "   حالة النظام: docker-compose ps"
echo "   إيقاف النظام: docker-compose down"
echo "   إعادة تشغيل النظام: docker-compose restart"
echo ""
echo "🔒 ملاحظات الأمان:"
echo "   - جميع الخدمات معزولة في شبكات منفصلة"
echo "   - البيانات مشفرة ومحمية"
echo "   - المراقبة نشطة على جميع المكونات"
echo "   - تم تطبيق سياسات الأمان المتقدمة"
