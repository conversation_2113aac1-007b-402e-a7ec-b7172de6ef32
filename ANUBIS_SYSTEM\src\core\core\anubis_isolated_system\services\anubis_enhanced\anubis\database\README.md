# 🗄️ قاعدة بيانات نظام أنوبيس
## Anubis System Database

**آخر تحديث**: 2025-07-16  
**الحالة**: ✅ نشطة ومتصلة  
**النوع**: MySQL 8.0.42  
**قاعدة البيانات**: anubis_system  

---

## 📊 ملخص قاعدة البيانات

### ✅ **الحالة الحالية:**
- **🔗 الاتصال**: نشط ويعمل بكفاءة
- **📋 الجداول**: 6 جداول رئيسية
- **📁 المشاريع**: 9 مشاريع مسجلة
- **🔍 التحليلات**: 9 تحليلات مكتملة
- **❌ الأخطاء**: 9 أخطاء مسجلة للمتابعة
- **📊 إجمالي السجلات**: 42 سجل

### **🌐 معلومات الاتصال:**
- **المضيف**: localhost:3306
- **المستخدم**: root
- **كلمة المرور**: 2452329511
- **قاعدة البيانات**: anubis_system

---

## 📋 هيكل قاعدة البيانات

### **الجداول الرئيسية:**

#### 1. **projects** - جدول المشاريع
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- name (VARCHAR) - اسم المشروع
- path (VARCHAR) - مسار المشروع
- type (VARCHAR) - نوع المشروع (python, javascript, etc.)
- description (TEXT) - وصف المشروع
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ آخر تحديث
```

#### 2. **analyses** - جدول التحليلات
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- project_id (INT, FOREIGN KEY) - معرف المشروع
- analysis_type (VARCHAR) - نوع التحليل
- results (JSON) - نتائج التحليل
- score (DECIMAL) - نقاط التقييم
- created_at (TIMESTAMP) - تاريخ التحليل
```

#### 3. **errors** - جدول الأخطاء
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- project_id (INT, FOREIGN KEY) - معرف المشروع
- error_type (VARCHAR) - نوع الخطأ
- severity (ENUM) - مستوى الخطورة (critical, high, medium, low)
- description (TEXT) - وصف الخطأ
- file_path (VARCHAR) - مسار الملف
- line_number (INT) - رقم السطر
- created_at (TIMESTAMP) - تاريخ الاكتشاف
```

#### 4. **activities** - جدول الأنشطة
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- project_id (INT, FOREIGN KEY) - معرف المشروع
- activity_type (VARCHAR) - نوع النشاط
- description (TEXT) - وصف النشاط
- user_id (INT) - معرف المستخدم
- created_at (TIMESTAMP) - تاريخ النشاط
```

#### 5. **plugins** - جدول الإضافات
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- name (VARCHAR) - اسم الإضافة
- version (VARCHAR) - إصدار الإضافة
- status (ENUM) - الحالة (active, inactive)
- config (JSON) - إعدادات الإضافة
- created_at (TIMESTAMP) - تاريخ التثبيت
```

#### 6. **reports** - جدول التقارير
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- project_id (INT, FOREIGN KEY) - معرف المشروع
- report_type (VARCHAR) - نوع التقرير
- content (JSON) - محتوى التقرير
- generated_at (TIMESTAMP) - تاريخ الإنتاج
```

---

## 🚀 الاستخدام والتشغيل

### **1. اختبار الاتصال:**
```bash
# اختبار سريع
python test_db_connection.py

# اختبار شامل
python final_db_test.py

# اختبار متقدم
python database/tests/test_connection.py
```

### **2. استخدام DatabaseAgent:**
```python
from agents.database_agent import DatabaseAgent

# إنشاء وكيل قاعدة البيانات
db_agent = DatabaseAgent(".", {}, True)

# تشغيل تحليل شامل
result = db_agent.run_analysis()

# عرض النتائج
print(f"حالة الاتصال: {result['connection_test']}")
print(f"عدد الجداول: {len(result['structure_analysis']['tables'])}")
```

### **3. استخدام MySQL Manager:**
```python
from database.core.mysql_manager import MySQLManager

# إنشاء مدير قاعدة البيانات
db = MySQLManager('configs/database_config.json')

# إنشاء مشروع جديد
project_id = db.create_project(
    name="مشروع جديد",
    path="/path/to/project",
    project_type="python",
    description="وصف المشروع"
)

# تسجيل تحليل
analysis_id = db.create_analysis(
    project_id=project_id,
    analysis_type="error_detection",
    results={"errors": 5, "warnings": 10},
    score=85.5
)

# الحصول على إحصائيات
stats = db.get_dashboard_stats()
```

---

## 📁 هيكل مجلد قاعدة البيانات

### **الملفات الأساسية:**
- `anubis_database.py` - فئة قاعدة البيانات الرئيسية
- `README.md` - هذا الملف

### **مجلد core/ - الملفات الأساسية:**
- `mysql_manager.py` - مدير MySQL المتقدم
- `mysql_connector.py` - موصل MySQL الأساسي
- `database_validator.py` - مدقق قاعدة البيانات
- `final_validation_runner.py` - منفذ التحقق النهائي

### **مجلد setup/ - ملفات الإعداد:**
- `create_mysql_database.sql` - سكريبت إنشاء قاعدة البيانات
- `direct_setup.py` - إعداد مباشر لقاعدة البيانات
- `setup_database.py` - سكريبت الإعداد الشامل

### **مجلد tests/ - ملفات الاختبار:**
- `test_connection.py` - اختبار الاتصال
- `comprehensive_test.py` - اختبار شامل
- `stress_test.py` - اختبار الضغط
- `run_all_tests.py` - تشغيل جميع الاختبارات

### **مجلد docs/ - التوثيق:**
- `README.md` - دليل مفصل
- `FIXES_SUMMARY.md` - ملخص الإصلاحات
- `TEST_SUMMARY.md` - ملخص الاختبارات

---

## 📊 الإحصائيات والتقارير

### **البيانات الحالية:**
- **المشاريع النشطة**: 9 مشاريع
  - نظام أنوبيس للمساعدين الذكيين (python)
  - مشروع تجريبي 1 (javascript)
  - مشروع تجريبي 2 (java)
  - وغيرها...

- **التحليلات المكتملة**: 9 تحليلات
  - متوسط النقاط: 88.5/100
  - أنواع التحليل: error_detection, performance_analysis, security_scan

- **الأخطاء المسجلة**: 9 أخطاء
  - Critical: 2 أخطاء
  - High: 3 أخطاء
  - Medium: 2 أخطاء
  - Low: 2 أخطاء

### **تقارير متاحة:**
- تقرير التحقق النهائي: `final_validation_report_*.html`
- تقرير الاختبارات الشاملة: `all_tests_report_*.html`
- تقرير التحقق البسيط: `simple_validation_report_*.json`

---

## 🔧 الصيانة والإدارة

### **النسخ الاحتياطية:**
- **تلقائية**: يومياً في منتصف الليل
- **يدوية**: عبر MySQL Manager
- **الاحتفاظ**: 30 يوم للنسخ الاحتياطية

### **الأمان:**
- **تشفير الاتصالات**: SSL/TLS
- **كلمات مرور قوية**: مطلوبة
- **صلاحيات محدودة**: حسب المستخدم
- **تسجيل العمليات**: شامل

### **الأداء:**
- **فهرسة محسنة**: على الجداول الرئيسية
- **تنظيف دوري**: للبيانات القديمة
- **مراقبة الأداء**: مستمرة

---

## 🛠️ استكشاف الأخطاء

### **مشاكل شائعة:**

#### **خطأ الاتصال:**
```
Error: Access denied for user 'root'@'localhost'
الحل: تحقق من كلمة المرور في configs/database_config.json
```

#### **قاعدة البيانات غير موجودة:**
```
Error: Unknown database 'anubis_system'
الحل: تشغيل python database/setup/direct_setup.py
```

#### **جداول مفقودة:**
```
Error: Table doesn't exist
الحل: إعادة تشغيل سكريبت الإعداد
```

### **أوامر مفيدة:**
```bash
# إعادة إنشاء قاعدة البيانات
python database/setup/direct_setup.py

# اختبار شامل
python database/tests/run_all_tests.py

# تحقق من الحالة
python database/core/mysql_manager.py
```

---

## 📞 الدعم والمساعدة

### **للحصول على المساعدة:**
- **الملفات**: راجع مجلد `docs/`
- **الاختبارات**: شغل ملفات `tests/`
- **الأمثلة**: راجع `core/mysql_manager.py`

### **للإبلاغ عن مشاكل:**
- **السجلات**: راجع ملفات `.log`
- **التقارير**: راجع مجلد التقارير
- **الاختبارات**: شغل الاختبارات الشاملة

---

<div align="center">

**🗄️ قاعدة بيانات نظام أنوبيس**

**نظام إدارة بيانات متقدم ومتكامل**

[![Database](https://img.shields.io/badge/Database-MySQL%208.0.42-blue.svg)](README.md)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)](README.md)
[![Projects](https://img.shields.io/badge/Projects-9%20Active-success.svg)](README.md)
[![Analyses](https://img.shields.io/badge/Analyses-9%20Complete-gold.svg)](README.md)

**🔗 متصلة ونشطة - جاهزة للاستخدام الفوري!**

</div>
