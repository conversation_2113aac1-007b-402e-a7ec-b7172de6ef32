@echo off
echo ========================================
echo    EMERGENCY: 67 VS CODE PROCESSES
echo ========================================
echo.
echo Current situation: 42+ VS Code related processes detected
echo Including 16 zombie Node.js processes!
echo.

echo EMERGENCY OPTIONS:
echo [1] 🔴 FORCE CLOSE ALL (SAVE WORK FIRST!)
echo [2] 🟠 Clean Node.js zombie processes only
echo [3] 🟡 Graceful VS Code restart
echo [4] 🔵 Detailed process analysis
echo [5] ❌ Cancel
echo.

set /p choice="URGENT - Choose action (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔴 WARNING: This will FORCE CLOSE ALL VS Code processes!
    echo 🔴 Make sure you saved all your work!
    set /p confirm="Type FORCE to confirm: "
    if /i "%confirm%"=="FORCE" (
        echo.
        echo 🔴 Force closing all VS Code processes...
        taskkill /IM Code.exe /F /T 2>nul
        taskkill /IM node.exe /F /T 2>nul
        taskkill /IM electron.exe /F /T 2>nul
        echo.
        echo ⏳ Waiting 15 seconds for cleanup...
        timeout /t 15 /nobreak
        echo.
        echo ✅ Emergency cleanup completed!
        echo 💡 Restart VS Code with: code --disable-extensions
    ) else (
        echo ❌ Emergency cleanup cancelled.
    )
    
) else if "%choice%"=="2" (
    echo.
    echo 🟠 Cleaning zombie Node.js processes...
    echo This will close orphaned Node.js processes only.
    
    echo Killing zombie Node.js processes...
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| find "node.exe"') do (
        echo Checking Node.js process %%i...
        taskkill /PID %%i /F 2>nul
    )
    
    echo ✅ Node.js cleanup completed!
    echo 📊 Run comprehensive scan to verify.
    
) else if "%choice%"=="3" (
    echo.
    echo 🟡 Attempting graceful restart...
    echo.
    echo Step 1: Closing VS Code gracefully...
    taskkill /IM Code.exe /T 2>nul
    echo.
    echo Step 2: Waiting 20 seconds for processes to close...
    timeout /t 20 /nobreak
    echo.
    echo Step 3: Cleaning remaining processes...
    taskkill /IM node.exe /F 2>nul
    echo.
    echo Step 4: Starting VS Code with minimal extensions...
    start "" "code" --disable-extensions
    echo.
    echo ✅ Graceful restart completed!
    
) else if "%choice%"=="4" (
    echo.
    echo 🔵 Running detailed analysis...
    powershell -ExecutionPolicy Bypass -File scripts/comprehensive_vscode_scan.ps1
    
) else (
    echo ❌ Operation cancelled.
)

echo.
echo ========================================
echo 💡 PREVENTION TIPS:
echo • Restart VS Code daily
echo • Close unused windows
echo • Disable unnecessary extensions
echo • Monitor process count regularly
echo ========================================
echo.
pause
