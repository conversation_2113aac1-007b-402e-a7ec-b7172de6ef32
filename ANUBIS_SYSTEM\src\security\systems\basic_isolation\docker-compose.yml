version: '3.8'

services:
  anubis-basic:
    build: .
    container_name: anubis-basic-isolation
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # الشبكة المعزولة
    networks:
      - anubis-basic-net
    
    # الأحجام المعزولة
    volumes:
      - anubis-basic-data:/app/data
      - anubis-basic-logs:/app/logs:rw
      - anubis-basic-configs:/app/configs:ro
      - /tmp:/app/temp:rw,noexec,nosuid,nodev
    
    # متغيرات البيئة الآمنة
    environment:
      - ANUBIS_MODE=basic_isolation
      - SECURITY_LEVEL=high
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///app/data/anubis_basic.db
    
    # فحص الصحة
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # حماية النظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
      - /var/tmp:rw,noexec,nosuid,size=50m
    
    # قيود الكيرنل
    sysctls:
      - net.ipv4.ip_unprivileged_port_start=0
    
    # منع الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
    
    # المنافذ المحدودة
    ports:
      - "8001:8000"
    
    # تسميات للمراقبة
    labels:
      - "anubis.isolation.level=basic"
      - "anubis.security.profile=standard"
      - "anubis.monitoring.enabled=true"

# الشبكات المعزولة
networks:
  anubis-basic-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.bridge.host_binding_ipv4: "127.0.0.1"

# الأحجام المعزولة
volumes:
  anubis-basic-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  anubis-basic-logs:
    driver: local
  anubis-basic-configs:
    driver: local
    driver_opts:
      type: none
      o: bind,ro
      device: ./configs
