#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌟 خادم MCP الرئيسي لأنوبيس وحورس
Main MCP Server for Anubis and Horus

خادم MCP متقدم يدعم جميع الأدوات والتكاملات المكتشفة
Advanced MCP server supporting all discovered tools and integrations
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
import sys
import os

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# MCP imports
try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource, Tool, TextContent, ImageContent, EmbeddedResource,
        CallToolRequest, CallToolResult, ListResourcesRequest, ListResourcesResult,
        ListToolsRequest, ListToolsResult, ReadResourceRequest, ReadResourceResult
    )
except ImportError as e:
    print(f"⚠️ MCP library not found: {e}")
    print("📦 Installing MCP library...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "mcp>=1.0.0"], check=True)
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server

# Local imports
from horus_integration.team_connector import HorusTeamConnector
from api_keys_vault.keys_manager import APIKeysManager
from tools.registry import ToolRegistry
from monitoring.performance_monitor import PerformanceMonitor

class AnubisHorusMCPServer:
    """🌟 خادم MCP المتكامل لأنوبيس وحورس"""
    
    def __init__(self):
        """تهيئة خادم MCP"""
        self.server = Server("anubis-horus-mcp")
        self.logger = self._setup_logging()
        self.config = self._load_config()
        
        # المكونات الأساسية
        self.horus_team = None
        self.api_keys_manager = None
        self.tool_registry = None
        self.performance_monitor = None
        
        # حالة الخادم
        self.is_running = False
        self.start_time = None
        self.stats = {
            "tools_called": 0,
            "resources_accessed": 0,
            "horus_consultations": 0,
            "errors": 0
        }
        
        self.logger.info("🌟 تم تهيئة خادم MCP لأنوبيس وحورس")
    
    def _setup_logging(self) -> logging.Logger:
        """إعداد نظام السجلات"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/mcp_server.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger("AnubisHorusMCP")
    
    def _load_config(self) -> Dict[str, Any]:
        """تحميل التكوينات"""
        config_path = project_root / "config" / "mcp_config.json"
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info("✅ تم تحميل التكوينات بنجاح")
            return config
        except FileNotFoundError:
            self.logger.warning("⚠️ ملف التكوين غير موجود، استخدام الإعدادات الافتراضية")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """الحصول على التكوينات الافتراضية"""
        return {
            "server": {
                "name": "anubis-horus-mcp",
                "version": "1.0.0",
                "description": "Integrated MCP Server for Anubis and Horus",
                "max_concurrent_tools": 10,
                "timeout": 300
            },
            "horus_team": {
                "enabled": True,
                "collaborative_mode": True,
                "shared_memory": True,
                "auto_consultation": True
            },
            "security": {
                "encryption": True,
                "api_key_rotation": True,
                "access_logging": True,
                "rate_limiting": True
            },
            "monitoring": {
                "performance_tracking": True,
                "health_checks": True,
                "metrics_collection": True
            }
        }
    
    async def initialize(self):
        """تهيئة المكونات الأساسية"""
        self.logger.info("🚀 بدء تهيئة خادم MCP...")
        
        try:
            # تهيئة مدير API keys
            if self.config.get("security", {}).get("encryption", True):
                self.api_keys_manager = APIKeysManager()
                await self.api_keys_manager.initialize()
                self.logger.info("🔐 تم تهيئة مدير API keys")
            
            # تهيئة سجل الأدوات
            self.tool_registry = ToolRegistry()
            await self.tool_registry.initialize()
            self.logger.info(f"🛠️ تم تسجيل {len(self.tool_registry.tools)} أداة")
            
            # تهيئة فريق حورس
            if self.config.get("horus_team", {}).get("enabled", True):
                self.horus_team = HorusTeamConnector()
                await self.horus_team.initialize()
                self.logger.info("𓅃 تم تفعيل فريق حورس")
            
            # تهيئة مراقب الأداء
            if self.config.get("monitoring", {}).get("performance_tracking", True):
                self.performance_monitor = PerformanceMonitor()
                await self.performance_monitor.start()
                self.logger.info("📊 تم تفعيل مراقب الأداء")
            
            # تسجيل معالجات MCP
            self._register_mcp_handlers()
            
            self.start_time = datetime.now()
            self.is_running = True
            self.logger.info("✅ تم تهيئة خادم MCP بنجاح")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة خادم MCP: {e}")
            raise
    
    def _register_mcp_handlers(self):
        """تسجيل معالجات MCP"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """قائمة الأدوات المتاحة"""
            try:
                tools = []
                if self.tool_registry:
                    for tool_name, tool_info in self.tool_registry.tools.items():
                        tools.append(Tool(
                            name=tool_name,
                            description=tool_info.get("description", ""),
                            inputSchema=tool_info.get("input_schema", {})
                        ))
                
                self.logger.info(f"📋 تم طلب قائمة الأدوات: {len(tools)} أداة")
                return tools
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في قائمة الأدوات: {e}")
                self.stats["errors"] += 1
                return []
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """تنفيذ أداة"""
            try:
                self.stats["tools_called"] += 1
                self.logger.info(f"🛠️ تنفيذ أداة: {name}")
                
                # التحقق من وجود الأداة
                if not self.tool_registry or name not in self.tool_registry.tools:
                    return [TextContent(
                        type="text",
                        text=f"❌ الأداة '{name}' غير موجودة"
                    )]
                
                # استشارة فريق حورس إذا كان مفعلاً
                if (self.horus_team and 
                    self.config.get("horus_team", {}).get("auto_consultation", True)):
                    
                    consultation = await self.horus_team.consult_team(
                        tool_name=name,
                        arguments=arguments
                    )
                    self.stats["horus_consultations"] += 1
                    
                    if consultation.get("recommendation"):
                        self.logger.info(f"𓅃 توصية فريق حورس: {consultation['recommendation']}")
                
                # تنفيذ الأداة
                result = await self.tool_registry.execute_tool(name, arguments)
                
                # مراقبة الأداء
                if self.performance_monitor:
                    await self.performance_monitor.record_tool_execution(name, result)
                
                return [TextContent(
                    type="text",
                    text=str(result)
                )]
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في تنفيذ الأداة {name}: {e}")
                self.stats["errors"] += 1
                return [TextContent(
                    type="text",
                    text=f"❌ خطأ في تنفيذ الأداة: {str(e)}"
                )]
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            """قائمة الموارد المتاحة"""
            try:
                resources = [
                    Resource(
                        uri="anubis://system/info",
                        name="معلومات النظام",
                        description="معلومات شاملة عن النظام المحلي",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="horus://team/status",
                        name="حالة فريق حورس",
                        description="حالة وإحصائيات فريق حورس",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="mcp://server/stats",
                        name="إحصائيات الخادم",
                        description="إحصائيات أداء خادم MCP",
                        mimeType="application/json"
                    )
                ]
                
                self.logger.info(f"📚 تم طلب قائمة الموارد: {len(resources)} مورد")
                return resources
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في قائمة الموارد: {e}")
                self.stats["errors"] += 1
                return []
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """قراءة مورد"""
            try:
                self.stats["resources_accessed"] += 1
                self.logger.info(f"📖 قراءة مورد: {uri}")
                
                if uri == "anubis://system/info":
                    return await self._get_system_info()
                elif uri == "horus://team/status":
                    return await self._get_horus_status()
                elif uri == "mcp://server/stats":
                    return await self._get_server_stats()
                else:
                    return json.dumps({"error": f"مورد غير معروف: {uri}"})
                    
            except Exception as e:
                self.logger.error(f"❌ خطأ في قراءة المورد {uri}: {e}")
                self.stats["errors"] += 1
                return json.dumps({"error": str(e)})
    
    async def _get_system_info(self) -> str:
        """الحصول على معلومات النظام"""
        if self.tool_registry and "system_info_collector" in self.tool_registry.tools:
            result = await self.tool_registry.execute_tool("system_info_collector", {})
            return json.dumps(result, ensure_ascii=False, indent=2)
        return json.dumps({"error": "أداة معلومات النظام غير متاحة"})
    
    async def _get_horus_status(self) -> str:
        """الحصول على حالة فريق حورس"""
        if self.horus_team:
            status = await self.horus_team.get_team_status()
            return json.dumps(status, ensure_ascii=False, indent=2)
        return json.dumps({"error": "فريق حورس غير مفعل"})
    
    async def _get_server_stats(self) -> str:
        """الحصول على إحصائيات الخادم"""
        uptime = None
        if self.start_time:
            uptime = str(datetime.now() - self.start_time)
        
        stats = {
            "server_info": {
                "name": self.config["server"]["name"],
                "version": self.config["server"]["version"],
                "uptime": uptime,
                "is_running": self.is_running
            },
            "statistics": self.stats,
            "components": {
                "horus_team": self.horus_team is not None,
                "api_keys_manager": self.api_keys_manager is not None,
                "tool_registry": self.tool_registry is not None,
                "performance_monitor": self.performance_monitor is not None
            }
        }
        
        return json.dumps(stats, ensure_ascii=False, indent=2)
    
    async def run(self):
        """تشغيل خادم MCP"""
        try:
            await self.initialize()
            self.logger.info("🌟 خادم MCP يعمل الآن...")
            
            # تشغيل خادم MCP
            async with stdio_server() as (read_stream, write_stream):
                await self.server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name=self.config["server"]["name"],
                        server_version=self.config["server"]["version"]
                    )
                )
                
        except KeyboardInterrupt:
            self.logger.info("⏹️ تم إيقاف الخادم بواسطة المستخدم")
        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل الخادم: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """إيقاف الخادم"""
        self.logger.info("🛑 بدء إيقاف خادم MCP...")
        
        self.is_running = False
        
        # إيقاف المكونات
        if self.performance_monitor:
            await self.performance_monitor.stop()
        
        if self.horus_team:
            await self.horus_team.shutdown()
        
        if self.api_keys_manager:
            await self.api_keys_manager.shutdown()
        
        self.logger.info("✅ تم إيقاف خادم MCP بنجاح")

async def main():
    """الدالة الرئيسية"""
    print("🌟 بدء تشغيل خادم MCP المتكامل لأنوبيس وحورس")
    print("=" * 60)
    
    server = AnubisHorusMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
