{"project_info": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants", "name": "Universal-AI-Assistants", "type": "streamlit", "structure": {"project_exists": true, "is_directory": true, "files_count": 130, "directories_count": 30, "python_files": ["main.py", "test_anubis_system.py", "agents\\database_agent.py", "agents\\error_detector_agent.py", "agents\\file_organizer_agent.py", "agents\\memory_agent.py", "agents\\project_analyzer_agent.py", "agents\\__init__.py", "core\\assistant_system.py", "core\\base_agent.py", "core\\config_manager.py", "core\\logger.py", "core\\__init__.py", "database\\anubis_database.py", "database\\database_manager.py", "plugins\\base_plugin.py", "plugins\\example_plugin.py", "plugins\\plugin_manager.py", "plugins\\__init__.py", "scripts\\quick_start.py", "scripts\\__init__.py", "tests\\run_all_tests.py", "tests\\test_agents.py", "tests\\test_error_detector.py", "tests\\test_jewelry_database.py", "tests\\test_jewelry_logic.py", "tests\\test_plugins.py", "tests\\test_project_analyzer.py", "tests\\test_system.py", "tests\\__init__.py", "templates\\streamlit_template\\main.py"], "config_files": ["configs\\database_config.json", "configs\\default_config.json", "workspace\\knowledge_base\\knowledge_base.json", "workspace\\reports\\assistant_report_20250712_150556.json", "workspace\\reports\\assistant_report_20250712_150648.json", "workspace\\reports\\assistant_report_20250712_150742.json", "workspace\\reports\\assistant_report_20250712_150924.json", "workspace\\reports\\assistant_report_20250712_151038.json", "workspace\\reports\\assistant_report_20250712_151119.json", "workspace\\reports\\assistant_report_20250712_151309.json", "workspace\\reports\\assistant_report_20250712_152601.json", "workspace\\reports\\assistant_report_20250712_153602.json", "workspace\\reports\\assistant_report_20250712_154134.json", "workspace\\reports\\error_detector_report_20250714_123543.json", "workspace\\reports\\project_analyzer_report_20250714_123543.json", "workspace\\reports\\project_analyzer_report_20250714_123544.json", "workspace\\shared_memory\\session_20250712.json", "workspace\\shared_memory\\session_20250714.json", "tests\\configs\\default_config.json", "tests\\workspace\\knowledge_base\\knowledge_base.json", "tests\\workspace\\reports\\error_detector_report_20250714_124719.json", "tests\\workspace\\reports\\error_detector_report_20250714_124920.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124719.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124720.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124920.json", "tests\\workspace\\shared_memory\\session_20250714.json"], "data_files": ["project_db.db", "database\\anubis.db", "workspace\\backups\\anubis_backup_20250714_130707.db", "workspace\\backups\\anubis_backup_20250714_130746.db", "workspace\\backups\\anubis_backup_20250714_130858.db"]}, "agent_type": "error_detector", "analysis_time": "2025-07-14T13:08:58.458518"}, "error_analysis": {"syntax_errors": [], "import_errors": [{"file": "tests\\test_error_detector.py", "line": 141, "error": "مكتبة مطلوبة قد تكون مفقودة: numpy", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install numpy"}, {"file": "tests\\test_error_detector.py", "line": 142, "error": "مكتبة مطلوبة قد تكون مفقودة: pandas", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install pandas"}, {"file": "tests\\test_error_detector.py", "line": 143, "error": "مكتبة مطلوبة قد تكون مفقودة: mat<PERSON><PERSON><PERSON><PERSON>", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install matplotlib"}, {"file": "tests\\test_error_detector.py", "line": 144, "error": "مكتبة مطلوبة قد تكون مفقودة: requests", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install requests"}, {"file": "tests\\test_project_analyzer.py", "line": 155, "error": "مكتبة مطلوبة قد تكون مفقودة: numpy", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install numpy"}, {"file": "tests\\test_project_analyzer.py", "line": 156, "error": "مكتبة مطلوبة قد تكون مفقودة: pandas", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install pandas"}, {"file": "tests\\test_project_analyzer.py", "line": 157, "error": "مكتبة مطلوبة قد تكون مفقودة: requests", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install requests"}, {"file": "templates\\streamlit_template\\main.py", "line": 9, "error": "مكتبة مطلوبة قد تكون مفقودة: streamlit", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install streamlit"}, {"file": "templates\\streamlit_template\\main.py", "line": 10, "error": "مكتبة مطلوبة قد تكون مفقودة: pandas", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install pandas"}, {"file": "templates\\streamlit_template\\main.py", "line": 11, "error": "مكتبة مطلوبة قد تكون مفقودة: numpy", "severity": "medium", "type": "missing_dependency", "suggestion": "pip install numpy"}], "style_issues": [{"file": "main.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 62, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 78, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 84, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 90, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 102, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 109, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 129, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 133, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 137, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 150, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 154, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 157, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 161, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 172, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 176, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 187, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 192, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 202, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 230, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "main.py", "line": 232, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 25, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 32, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 37, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 46, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 58, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 67, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 71, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 74, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 86, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 93, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 101, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 104, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 115, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 119, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 124, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 128, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 141, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 145, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 150, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 158, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 163, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 167, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 177, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 184, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 192, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 220, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 224, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 234, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 237, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 240, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 248, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 256, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "test_anubis_system.py", "line": 259, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 50, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 59, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 67, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 70, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 74, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 76, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 85, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 100, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 104, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 107, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 113, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 120, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 122, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 126, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 141, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 145, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 149, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 151, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 159, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 169, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 179, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 187, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 205, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 211, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 222, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 224, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 229, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 231, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 237, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 241, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 248, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 250, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 261, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 266, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 272, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 276, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 279, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 282, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 300, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 312, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 316, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 326, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 329, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 331, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 335, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 346, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 348, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 351, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 353, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 361, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 366, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 368, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 374, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 377, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 383, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 386, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 390, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 392, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 401, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 406, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 409, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 411, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 415, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 419, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 424, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 427, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 431, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\database_agent.py", "line": 435, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 33, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 37, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 45, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 55, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 63, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 65, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 81, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 84, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 87, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 90, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 103, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 107, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 111, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 118, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 121, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 138, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 148, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 157, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 161, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 173, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 179, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 200, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 202, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 206, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 210, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 222, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 233, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 236, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 238, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 243, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 254, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 264, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 266, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 270, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 280, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 282, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 286, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 293, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 305, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 307, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 311, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 321, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 333, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 337, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 341, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 347, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 351, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 354, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 357, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 361, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 363, "error": "السطر طويل جداً (136 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "agents\\error_detector_agent.py", "line": 365, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 370, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 372, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 376, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 385, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 393, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 401, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 403, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 408, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 410, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 420, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 424, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\error_detector_agent.py", "line": 432, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 33, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 37, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 42, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 47, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 51, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 53, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 111, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 120, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 124, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 127, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 133, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 137, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 141, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 158, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 164, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 168, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 174, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 177, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 179, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 187, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 194, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 196, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 200, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 206, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 212, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 217, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 219, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 231, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 233, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 237, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 254, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 256, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 265, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 271, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 279, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 285, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 291, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 293, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 299, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 304, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 306, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 312, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 314, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 326, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 331, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 336, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 341, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 344, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 347, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 351, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 356, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 359, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 361, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 365, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 368, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 373, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 378, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 381, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 384, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 388, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 391, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 393, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 403, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 410, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 413, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 421, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 427, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 431, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 434, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 439, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 442, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 447, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 449, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 456, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 459, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\file_organizer_agent.py", "line": 461, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 31, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 35, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 42, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 47, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 52, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 58, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 61, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 63, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 81, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 119, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 131, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 172, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 191, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 194, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 210, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 219, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 225, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 234, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 241, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 248, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 255, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 260, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 270, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 275, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 290, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 293, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 295, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 303, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 307, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 311, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 319, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 326, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 328, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 332, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 335, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 339, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 342, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 346, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 350, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 352, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 355, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 357, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 365, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 369, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 371, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 374, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 376, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 379, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 388, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 391, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 393, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 396, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 405, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 408, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 413, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 419, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 423, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 428, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 432, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 435, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 440, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 447, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 450, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\memory_agent.py", "line": 452, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 32, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 36, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 43, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 56, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 60, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 63, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 80, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 86, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 99, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 104, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 118, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 128, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 137, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 141, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 154, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 156, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 161, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 166, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 174, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 177, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 182, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 184, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 188, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 198, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 200, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 211, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 217, "error": "السطر طويل جداً (122 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "agents\\project_analyzer_agent.py", "line": 222, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 226, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 240, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 242, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 256, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 259, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 265, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 267, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 275, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 283, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 285, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 290, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 292, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 306, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 312, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 321, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 333, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 342, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 345, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 347, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 351, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 359, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 366, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 369, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 371, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 380, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 383, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 386, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 390, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 398, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 405, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 413, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 416, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 418, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 426, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 428, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 430, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 440, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 444, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 447, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 451, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 455, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 458, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 461, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 465, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 470, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 476, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 478, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 482, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 514, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 516, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 523, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 531, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 538, "error": "السطر طويل جداً (127 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "agents\\project_analyzer_agent.py", "line": 542, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 551, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\project_analyzer_agent.py", "line": 560, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "agents\\__init__.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 40, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 41, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 47, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 56, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 61, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 87, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 91, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 94, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 99, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 103, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 107, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 113, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 114, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 120, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 124, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 129, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 161, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 163, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 208, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 215, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 226, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 229, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 233, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 245, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 252, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 255, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 264, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 267, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 270, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 274, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 277, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 283, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 292, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 300, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 306, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 315, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 325, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 334, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 340, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 343, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 346, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 350, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 353, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 355, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 364, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 368, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 381, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 385, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\assistant_system.py", "line": 389, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 16, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 20, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 29, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 39, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 42, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 45, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 50, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 53, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 59, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 82, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 87, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 94, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 98, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 105, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 118, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 127, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 139, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 142, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 159, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 161, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 168, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 172, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 184, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 190, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 198, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 204, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 206, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 211, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 225, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 243, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\base_agent.py", "line": 245, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 15, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 19, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 26, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 29, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 33, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 48, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 133, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 159, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 162, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 168, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 173, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 178, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 182, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 191, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 195, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 199, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 232, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 236, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 241, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 247, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 255, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 259, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 282, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 306, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 310, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\config_manager.py", "line": 317, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 16, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 17, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 25, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 38, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 41, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 53, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 59, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 70, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 75, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 79, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 81, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 86, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 91, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 98, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 103, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 108, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 114, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 120, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 138, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 140, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 144, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 148, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 156, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 160, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 166, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 184, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 188, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 191, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 195, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 205, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 211, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 219, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 226, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 231, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 235, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 237, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 242, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 247, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\logger.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "core\\__init__.py", "line": 17, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 19, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 23, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 32, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 47, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 58, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 79, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 100, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 114, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 156, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 180, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 185, "error": "السطر طويل جداً (127 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "database\\anubis_database.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 189, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 192, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 210, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 222, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 223, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 233, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 235, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 236, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 242, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 245, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 247, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 252, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 265, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 268, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 272, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 278, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 286, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 290, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 297, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 300, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 306, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 312, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 314, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 316, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 320, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 321, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 331, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 333, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 336, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 339, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 344, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 347, "error": "السطر طويل جداً (127 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "database\\anubis_database.py", "line": 348, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 351, "error": "السطر طويل جداً (127 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "database\\anubis_database.py", "line": 352, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 355, "error": "السطر طويل جداً (125 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "database\\anubis_database.py", "line": 356, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 359, "error": "السطر طويل جداً (126 حرف)", "severity": "low", "type": "line_too_long"}, {"file": "database\\anubis_database.py", "line": 360, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 362, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 366, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 374, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\anubis_database.py", "line": 379, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 26, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 32, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 60, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 65, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 68, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 78, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 88, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 100, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 101, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 107, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 109, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 111, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 117, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 119, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 125, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 138, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 140, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 145, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 151, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 156, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 158, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 167, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 179, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 188, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 193, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 215, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 219, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 226, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 229, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 238, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 242, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 249, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 254, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 258, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 264, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 281, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 296, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 299, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 303, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 307, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 310, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 316, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "database\\database_manager.py", "line": 320, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 13, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 17, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 27, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 30, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 39, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 58, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 62, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 80, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 84, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 88, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\base_plugin.py", "line": 90, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 13, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 36, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 50, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 59, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 67, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 90, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\example_plugin.py", "line": 104, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 19, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 23, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 32, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 36, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 40, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 46, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 55, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 59, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 62, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 78, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 79, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 81, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 85, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 88, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 92, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 95, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 98, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 102, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 106, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 109, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 119, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 127, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 131, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 134, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 141, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 144, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 147, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 150, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 154, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 158, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 167, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 174, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 177, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 182, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 192, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 194, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 198, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 211, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 217, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 225, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "plugins\\plugin_manager.py", "line": 229, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 22, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 25, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 31, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 36, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "scripts\\quick_start.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 45, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 53, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 64, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 82, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 86, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 93, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 103, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 121, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 131, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 139, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 150, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 155, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 164, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 184, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 190, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 194, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 209, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 225, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 233, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 238, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 255, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 264, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 271, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 278, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 283, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 291, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 295, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 299, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 303, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 307, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 310, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\run_all_tests.py", "line": 313, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 29, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 39, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 47, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 50, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 60, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_agents.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 42, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 49, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 64, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 76, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 80, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 88, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 99, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 102, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 107, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 112, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 122, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 125, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 135, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 149, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 159, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 174, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 178, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 199, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 206, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 209, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 217, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 222, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 237, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 241, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 244, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 250, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 255, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 259, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 270, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 281, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 300, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 303, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_error_detector.py", "line": 307, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 24, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 46, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 60, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 65, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 85, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 87, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 95, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 112, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 151, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 155, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 157, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 172, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 195, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 199, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 216, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 224, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 234, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 239, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 245, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 273, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 276, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 285, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 288, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 291, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_database.py", "line": 297, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 13, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 22, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 24, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 40, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 46, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 48, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 52, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 57, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 61, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 64, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 69, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 75, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 80, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 89, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 94, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 97, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 102, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 108, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 111, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 116, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 121, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 126, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 131, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 137, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 140, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 155, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 157, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 164, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 173, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 176, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 191, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 196, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 209, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 212, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 214, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 232, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 235, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 240, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 246, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 253, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 256, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 260, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 270, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 279, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 281, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 284, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 286, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_jewelry_logic.py", "line": 295, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 30, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 38, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 41, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 60, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 65, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 72, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 87, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 91, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 95, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 99, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 103, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 113, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 117, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 128, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 135, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 143, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 147, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 156, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 160, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 170, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 174, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 181, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 185, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 190, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 194, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 201, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 203, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 207, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 213, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 220, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 230, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 234, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 243, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 248, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 252, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 258, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 264, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 269, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 274, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 281, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 286, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 292, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 297, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 300, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 304, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 310, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 317, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 327, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 330, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 336, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 340, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 348, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 356, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 360, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 364, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_plugins.py", "line": 368, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 28, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 34, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 41, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 48, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 53, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 62, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 73, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 77, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 80, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 84, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 89, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 126, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 130, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 135, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 147, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 159, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 162, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 166, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 178, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 190, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 196, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 200, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 218, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 225, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 235, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 251, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 254, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 258, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 262, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 265, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 271, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 287, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 294, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 304, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 311, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 314, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 318, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 323, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 327, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 332, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 336, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 339, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 347, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 356, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 359, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 363, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 373, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 377, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 382, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 385, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 394, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 397, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_project_analyzer.py", "line": 401, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 31, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 35, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 38, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 44, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 46, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 54, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 57, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 61, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 66, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 68, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 76, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 83, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 86, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 89, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 92, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 98, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 104, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 110, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 112, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 120, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 123, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 127, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 135, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 142, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 147, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 154, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 158, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 173, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 183, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 186, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 189, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 202, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 204, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 208, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 210, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 229, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 231, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 236, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 240, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 245, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 249, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 254, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 257, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 261, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\test_system.py", "line": 263, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "tests\\__init__.py", "line": 14, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 58, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 66, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 74, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 82, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 93, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 96, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 97, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 108, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 111, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 114, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 115, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 122, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 125, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 126, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 138, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 146, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 153, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 160, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 165, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 168, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 171, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 175, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 180, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 185, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 189, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 191, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 195, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 197, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 202, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 221, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 226, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 228, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}, {"file": "templates\\streamlit_template\\main.py", "line": 235, "error": "مسافات زائدة في نهاية السطر", "severity": "low", "type": "trailing_whitespace"}], "logic_warnings": [{"file": "main.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "test_anubis_system.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\database_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\error_detector_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\file_organizer_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\memory_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\project_analyzer_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "agents\\__init__.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "core\\assistant_system.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "core\\base_agent.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "core\\config_manager.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "core\\logger.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "core\\__init__.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "database\\anubis_database.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "database\\database_manager.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "plugins\\base_plugin.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "plugins\\example_plugin.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "plugins\\plugin_manager.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "plugins\\__init__.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "scripts\\quick_start.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\run_all_tests.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_agents.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_error_detector.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_jewelry_database.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_jewelry_logic.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_plugins.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_project_analyzer.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "tests\\test_system.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}, {"file": "templates\\streamlit_template\\main.py", "line": 0, "error": "قد توجد استيرادات غير مستخدمة", "severity": "low", "type": "unused_imports"}], "security_issues": [{"file": "tests\\test_error_detector.py", "line": 118, "error": "كلمة مرور أو مفتاح API مكشوف في الكود", "severity": "high", "type": "exposed_credentials"}, {"file": "tests\\test_error_detector.py", "line": 119, "error": "كلمة مرور أو مفتاح API مكشوف في الكود", "severity": "high", "type": "exposed_credentials"}, {"file": "tests\\test_error_detector.py", "line": 120, "error": "كلمة مرور أو مفتاح API مكشوف في الكود", "severity": "high", "type": "exposed_credentials"}, {"file": "tests\\test_error_detector.py", "line": 276, "error": "كلمة مرور أو مفتاح API مكشوف في الكود", "severity": "high", "type": "exposed_credentials"}, {"file": "tests\\test_project_analyzer.py", "line": 245, "error": "كلمة مرور أو مفتاح API مكشوف في الكود", "severity": "high", "type": "exposed_credentials"}], "performance_issues": [{"file": "agents\\database_agent.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "agents\\error_detector_agent.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "agents\\file_organizer_agent.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "agents\\memory_agent.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "agents\\project_analyzer_agent.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "core\\assistant_system.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "core\\config_manager.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "database\\database_manager.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "plugins\\example_plugin.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "plugins\\plugin_manager.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "tests\\run_all_tests.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "tests\\test_error_detector.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "tests\\test_project_analyzer.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}, {"file": "tests\\test_system.py", "line": 0, "error": "عدد كبير من الحلقات - قد يؤثر على الأداء", "severity": "medium", "type": "multiple_loops"}], "total_files_analyzed": 31, "files_with_errors": 77}, "quality_analysis": {"complexity_score": 73.81935483870967, "maintainability_score": 86.75391417947944, "readability_score": 86.75391417947944, "documentation_score": 99.68847352024922, "test_coverage_estimate": 25.806451612903224}, "recommendations": [{"category": "general", "priority": "medium", "title": "استخدام أدوات فحص الكود", "description": "استخدم أدوات مثل flake8 أو pylint لفحص جودة الكود", "action": "pip install flake8 pylint"}, {"category": "testing", "priority": "high", "title": "إضافة اختبارات وحدة", "description": "أض<PERSON> اختبارات شاملة لتحسين جودة الكود", "action": "pip install pytest"}, {"category": "documentation", "priority": "medium", "title": "تحسين التوثيق", "description": "أض<PERSON> docstrings للدوال والفئات", "action": "إضافة توثيق للكود"}], "summary": {"total_files_analyzed": 31, "files_with_errors": 77, "total_errors_found": 1340, "quality_score": 74.56, "severity_breakdown": {"high": 5, "medium": 24, "low": 1311}, "status": "completed", "analysis_time": "2025-07-14T13:08:58.646533"}}