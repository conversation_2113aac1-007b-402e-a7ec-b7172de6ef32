# 🏺 نظام أنوبيس للذكاء الاصطناعي
## Anubis AI Assistant System

نظام متطور للمساعدين الذكيين مع وكلاء متخصصين وقاعدة بيانات متكاملة.

## الهيكل الجديد

```
anubis/                 # النظام الأساسي
├── core/              # النواة الأساسية
├── agents/            # الوكلاء الذكيين
├── api/               # واجهة برمجة التطبيقات
├── database/          # قاعدة البيانات
├── plugins/           # نظام الإضافات
├── configs/           # ملفات التكوين
├── tests/             # الاختبارات
├── scripts/           # النصوص المساعدة
├── docs/              # التوثيق
├── workspace/         # مساحة العمل
├── templates/         # قوالب المشاريع
└── examples/          # أمثلة الاستخدام

tools/                 # الأدوات المساعدة
├── vscode-optimizer/  # أدوات تحسين VS Code
├── emergency/         # أدوات الطوارئ
└── monitoring/        # أدوات المراقبة

archive/               # الأرشيف
├── old_versions/      # الإصدارات القديمة
├── backups/           # النسخ الاحتياطية
└── deprecated/        # الملفات المهجورة
```

## التشغيل السريع

```bash
# تشغيل النظام
python anubis/main.py

# تشغيل الاختبارات
python anubis/tests/run_all_tests.py

# تشغيل واجهة المساعدة
python anubis/scripts/ask_anubis.py
```

## المزيد من المعلومات

راجع مجلد `anubis/docs/` للحصول على التوثيق الكامل.
