#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار الاتصال بقاعدة بيانات نظام أنوبيس
"""

import json
from pathlib import Path

import mysql.connector
from mysql.connector import Error


def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""

    # تحميل الإعدادات
    config_path = Path(__file__).parent.parent / "configs" / "database_config.json"
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)["database"]["mysql"]

    print("🏺 اختبار الاتصال بقاعدة بيانات نظام أنوبيس")
    print("=" * 50)
    print(f"🔗 الخادم: {config['host']}:{config['port']}")
    print(f"👤 المستخدم: {config['user']}")
    print(f"🗄️ قاعدة البيانات: {config['database']}")

    try:
        # إنشاء الاتصال
        connection = mysql.connector.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            database=config["database"],
            charset=config.get("charset", "utf8mb4"),
        )

        if connection.is_connected():
            print("✅ تم الاتصال بنجاح!")

            # معلومات الخادم
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📊 إصدار MySQL: {version}")

            # عرض الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 عدد الجداول: {len(tables)}")

            # إحصائيات سريعة
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  📄 {table_name}: {count} صف")

            # اختبار استعلام بسيط
            cursor.execute("SELECT name, type FROM projects LIMIT 3")
            projects = cursor.fetchall()
            print(f"\n📁 المشاريع:")
            for project in projects:
                print(f"  - {project[0]} ({project[1]})")

            cursor.close()
            connection.close()

            print("\n🎉 جميع الاختبارات نجحت!")
            return True

    except Error as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False


if __name__ == "__main__":
    test_connection()
