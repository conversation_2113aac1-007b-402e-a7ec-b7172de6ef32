#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 حاوية الوكلاء المخصصة لنظام أنوبيس
Anubis Agent Container System

نظام عزل وإدارة الوكلاء الذكيين مع حماية وأمان متقدم
مطور بالتعاون مع Gemini CLI
"""

import json
import logging
import multiprocessing
import os
import signal
import sys
import threading
import time
import traceback
from abc import ABC, abstractmethod
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import subprocess
import tempfile
import uuid


class AgentContainerError(Exception):
    """استثناء مخصص لأخطاء الحاوية"""
    pass


class AgentTimeoutError(AgentContainerError):
    """استثناء انتهاء مهلة الوكيل"""
    pass


class AgentSecurityError(AgentContainerError):
    """استثناء أمان الوكيل"""
    pass


class ResourceLimits:
    """حدود الموارد للوكلاء"""
    
    def __init__(self, 
                 max_memory_mb: int = 512,
                 max_cpu_percent: float = 50.0,
                 max_execution_time: int = 300,
                 max_file_operations: int = 1000,
                 allowed_paths: List[str] = None):
        self.max_memory_mb = max_memory_mb
        self.max_cpu_percent = max_cpu_percent
        self.max_execution_time = max_execution_time
        self.max_file_operations = max_file_operations
        self.allowed_paths = allowed_paths or []


class AgentSandbox:
    """صندوق رمل للوكلاء"""
    
    def __init__(self, agent_id: str, limits: ResourceLimits):
        self.agent_id = agent_id
        self.limits = limits
        self.start_time = None
        self.file_operations_count = 0
        self.temp_dir = None
        self.logger = logging.getLogger(f"sandbox_{agent_id}")
        
    def __enter__(self):
        """دخول صندوق الرمل"""
        self.start_time = datetime.now()
        
        # إنشاء مجلد مؤقت معزول
        self.temp_dir = tempfile.mkdtemp(prefix=f"anubis_agent_{self.agent_id}_")
        
        # تسجيل بداية التشغيل
        self.logger.info(f"🏺 بدء تشغيل الوكيل {self.agent_id} في صندوق رمل")
        self.logger.info(f"📁 مجلد مؤقت: {self.temp_dir}")
        
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """خروج من صندوق الرمل"""
        # تنظيف المجلد المؤقت
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                self.logger.info(f"🧹 تم تنظيف المجلد المؤقت: {self.temp_dir}")
            except Exception as e:
                self.logger.warning(f"⚠️ فشل في تنظيف المجلد المؤقت: {e}")
        
        # تسجيل انتهاء التشغيل
        if self.start_time:
            duration = datetime.now() - self.start_time
            self.logger.info(f"⏱️ انتهى تشغيل الوكيل {self.agent_id} - المدة: {duration.total_seconds():.2f}s")
    
    def check_execution_time(self):
        """فحص وقت التنفيذ"""
        if self.start_time:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            if elapsed > self.limits.max_execution_time:
                raise AgentTimeoutError(f"تجاوز الوكيل {self.agent_id} الحد الأقصى للوقت: {elapsed:.2f}s")
    
    def check_file_operation(self, operation: str, path: str):
        """فحص عملية ملف"""
        self.file_operations_count += 1
        
        # فحص عدد العمليات
        if self.file_operations_count > self.limits.max_file_operations:
            raise AgentSecurityError(f"تجاوز الوكيل {self.agent_id} الحد الأقصى لعمليات الملفات")
        
        # فحص المسارات المسموحة
        if self.limits.allowed_paths:
            path_obj = Path(path).resolve()
            allowed = any(str(path_obj).startswith(str(Path(allowed_path).resolve())) 
                         for allowed_path in self.limits.allowed_paths)
            if not allowed:
                raise AgentSecurityError(f"الوكيل {self.agent_id} يحاول الوصول لمسار غير مسموح: {path}")


class IsolatedAgent:
    """وكيل معزول في حاوية"""
    
    def __init__(self, agent_class, agent_id: str, project_path: str, 
                 config: Dict[str, Any], limits: ResourceLimits = None):
        self.agent_class = agent_class
        self.agent_id = agent_id
        self.project_path = project_path
        self.config = config
        self.limits = limits or ResourceLimits()
        self.sandbox = None
        self.logger = logging.getLogger(f"isolated_agent_{agent_id}")
        
    def run_in_container(self) -> Dict[str, Any]:
        """تشغيل الوكيل في حاوية معزولة"""
        try:
            with AgentSandbox(self.agent_id, self.limits) as sandbox:
                self.sandbox = sandbox
                
                # إنشاء الوكيل
                agent = self.agent_class(self.project_path, self.config)
                
                # تهيئة الوكيل
                agent.initialize_agent()
                
                # تشغيل التحليل مع مراقبة
                return self._run_with_monitoring(agent)
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل الوكيل {self.agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.agent_id,
                "execution_time": 0
            }
    
    def _run_with_monitoring(self, agent) -> Dict[str, Any]:
        """تشغيل مع مراقبة الموارد"""
        start_time = time.time()
        
        try:
            # تشغيل التحليل في thread منفصل للمراقبة
            result_queue = []
            error_queue = []
            
            def run_analysis():
                try:
                    result = agent.run_analysis()
                    result_queue.append(result)
                except Exception as e:
                    error_queue.append(e)
            
            # بدء thread التحليل
            analysis_thread = threading.Thread(target=run_analysis)
            analysis_thread.daemon = True
            analysis_thread.start()
            
            # مراقبة التنفيذ
            while analysis_thread.is_alive():
                # فحص وقت التنفيذ
                if self.sandbox:
                    self.sandbox.check_execution_time()
                
                # انتظار قصير
                analysis_thread.join(timeout=1.0)
            
            # جمع النتائج
            if error_queue:
                raise error_queue[0]
            
            if result_queue:
                result = result_queue[0]
            else:
                result = {"message": "لا توجد نتائج"}
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "result": result,
                "agent_id": self.agent_id,
                "execution_time": execution_time,
                "file_operations": self.sandbox.file_operations_count if self.sandbox else 0
            }
            
        except AgentTimeoutError as e:
            return {
                "success": False,
                "error": f"انتهت مهلة الوكيل: {str(e)}",
                "agent_id": self.agent_id,
                "execution_time": time.time() - start_time,
                "timeout": True
            }
        except AgentSecurityError as e:
            return {
                "success": False,
                "error": f"خطأ أمان: {str(e)}",
                "agent_id": self.agent_id,
                "execution_time": time.time() - start_time,
                "security_violation": True
            }


class AgentContainer:
    """حاوية الوكلاء الرئيسية"""
    
    def __init__(self, max_concurrent_agents: int = 3):
        self.max_concurrent_agents = max_concurrent_agents
        self.running_agents = {}
        self.agent_results = {}
        self.logger = logging.getLogger("agent_container")
        self.default_limits = ResourceLimits()
        
    def register_agent(self, agent_class, agent_id: str, project_path: str, 
                      config: Dict[str, Any], limits: ResourceLimits = None) -> str:
        """تسجيل وكيل جديد"""
        if agent_id in self.running_agents:
            raise AgentContainerError(f"الوكيل {agent_id} مسجل بالفعل")
        
        isolated_agent = IsolatedAgent(
            agent_class, agent_id, project_path, config, 
            limits or self.default_limits
        )
        
        self.running_agents[agent_id] = isolated_agent
        self.logger.info(f"📝 تم تسجيل الوكيل: {agent_id}")
        
        return agent_id
    
    def run_agent(self, agent_id: str) -> Dict[str, Any]:
        """تشغيل وكيل محدد"""
        if agent_id not in self.running_agents:
            raise AgentContainerError(f"الوكيل {agent_id} غير مسجل")
        
        self.logger.info(f"🚀 بدء تشغيل الوكيل: {agent_id}")
        
        isolated_agent = self.running_agents[agent_id]
        result = isolated_agent.run_in_container()
        
        # حفظ النتائج
        self.agent_results[agent_id] = result
        
        # إزالة الوكيل من قائمة التشغيل
        del self.running_agents[agent_id]
        
        self.logger.info(f"✅ انتهى تشغيل الوكيل: {agent_id}")
        
        return result
    
    def run_all_agents(self) -> Dict[str, Any]:
        """تشغيل جميع الوكلاء المسجلين"""
        self.logger.info(f"🏺 بدء تشغيل {len(self.running_agents)} وكيل")
        
        all_results = {}
        
        # تشغيل الوكلاء بشكل متتالي (يمكن تحسينه للتشغيل المتوازي)
        agent_ids = list(self.running_agents.keys())
        
        for agent_id in agent_ids:
            try:
                result = self.run_agent(agent_id)
                all_results[agent_id] = result
            except Exception as e:
                self.logger.error(f"❌ فشل في تشغيل الوكيل {agent_id}: {e}")
                all_results[agent_id] = {
                    "success": False,
                    "error": str(e),
                    "agent_id": agent_id
                }
        
        return all_results
    
    def get_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على حالة وكيل"""
        if agent_id in self.running_agents:
            return {"status": "running", "agent_id": agent_id}
        elif agent_id in self.agent_results:
            return {"status": "completed", "result": self.agent_results[agent_id]}
        else:
            return {"status": "not_found", "agent_id": agent_id}
    
    def stop_agent(self, agent_id: str) -> bool:
        """إيقاف وكيل"""
        if agent_id in self.running_agents:
            del self.running_agents[agent_id]
            self.logger.info(f"🛑 تم إيقاف الوكيل: {agent_id}")
            return True
        return False
    
    def get_container_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الحاوية"""
        return {
            "running_agents": len(self.running_agents),
            "completed_agents": len(self.agent_results),
            "max_concurrent": self.max_concurrent_agents,
            "agent_list": {
                "running": list(self.running_agents.keys()),
                "completed": list(self.agent_results.keys())
            }
        }


# دالة مساعدة لإنشاء حاوية وكلاء
def create_agent_container(max_concurrent: int = 3) -> AgentContainer:
    """إنشاء حاوية وكلاء جديدة"""
    container = AgentContainer(max_concurrent)
    
    # إعداد نظام السجلات
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    return container


# مثال على الاستخدام
if __name__ == "__main__":
    print("🏺 نظام حاوية الوكلاء لأنوبيس")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("🔒 عزل وحماية متقدمة للوكلاء")
    
    # إنشاء حاوية
    container = create_agent_container()
    
    # طباعة الإحصائيات
    stats = container.get_container_stats()
    print(f"📊 إحصائيات الحاوية: {json.dumps(stats, ensure_ascii=False, indent=2)}")
