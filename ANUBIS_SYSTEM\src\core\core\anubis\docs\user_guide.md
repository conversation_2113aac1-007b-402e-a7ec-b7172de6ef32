# 📖 دليل المستخدم الشامل
# Complete User Guide

## 🎯 مرحباً بك في نظام المساعدين الذكيين العالمي

هذا الدليل الشامل سيأخذك خطوة بخطوة لتعلم كيفية استخدام نظام المساعدين الذكيين العالمي بكفاءة وفعالية.

## 📚 فهرس الدليل

1. [البدء السريع](#البدء-السريع)
2. [الواجهة الأساسية](#الواجهة-الأساسية)
3. [الوكلاء الذكيين](#الوكلاء-الذكيين)
4. [نظام الإضافات](#نظام-الإضافات)
5. [التقارير والتحليلات](#التقارير-والتحليلات)
6. [الإعدادات المتقدمة](#الإعدادات-المتقدمة)
7. [استكشاف الأخطاء](#استكشاف-الأخطاء)
8. [أمثلة عملية](#أمثلة-عملية)

---

## 🚀 البدء السريع

### الخطوة 1: التثبيت
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/macOS
# أو
venv\Scripts\activate     # Windows

# تثبيت التبعيات
pip install -r requirements.txt
```

### الخطوة 2: أول تشغيل
```bash
# عرض المساعدة
python main.py --help

# تحليل مشروع بسيط
python main.py --project "path/to/your/project" --analyze
```

### الخطوة 3: عرض النتائج
```bash
# عرض التقارير
ls workspace/reports/

# عرض السجلات
ls workspace/logs/
```

---

## 🖥️ الواجهة الأساسية

### أوامر سطر الأوامر الأساسية

#### الأمر الرئيسي:
```bash
python main.py [OPTIONS]
```

#### المعاملات الأساسية:
- `--project, -p`: مسار المشروع (مطلوب)
- `--config, -c`: ملف التكوين (افتراضي: configs/default_config.json)
- `--verbose, -v`: عرض تفاصيل أكثر
- `--output, -o`: مجلد حفظ التقارير

#### أنواع العمليات:
- `--analyze`: تحليل شامل للمشروع
- `--organize`: تنظيم ملفات المشروع
- `--health-check`: فحص صحة المشروع
- `--fix-issues`: إصلاح المشاكل المكتشفة

#### اختيار الوكلاء:
- `--agent database`: تشغيل وكيل قاعدة البيانات فقط
- `--agent organizer`: تشغيل وكيل تنظيم الملفات فقط
- `--agent memory`: تشغيل وكيل الذاكرة فقط
- `--agent error`: تشغيل وكيل كشف الأخطاء فقط
- `--agent analyzer`: تشغيل وكيل تحليل المشاريع فقط
- `--agent all`: تشغيل جميع الوكلاء (افتراضي)

### أمثلة الاستخدام:

#### تحليل شامل مع تفاصيل:
```bash
python main.py --project "my_project" --analyze --verbose
```

#### تنظيم ملفات مشروع Django:
```bash
python main.py --project "django_app" --organize --project-type django
```

#### فحص أمان مشروع FastAPI:
```bash
python main.py --project "api_project" --agent error --project-type fastapi
```

#### تحليل مشروع Streamlit مع حفظ في مجلد مخصص:
```bash
python main.py --project "streamlit_app" --analyze --output "custom_reports"
```

---

## 🤖 الوكلاء الذكيين

### 🗄️ Database Agent - وكيل قاعدة البيانات

**الوظائف:**
- فحص اتصال قاعدة البيانات
- تحليل هيكل الجداول
- فحص الأداء والاستعلامات
- إنشاء نسخ احتياطية
- فحص الأمان

**الاستخدام:**
```bash
python main.py --project "my_project" --agent database
```

**التكوين:**
```json
{
  "database_agent": {
    "enabled": true,
    "database": {
      "type": "sqlite",
      "name": "project_db",
      "host": "localhost"
    },
    "auto_backup": true,
    "check_performance": true,
    "check_security": true
  }
}
```

### 📁 File Organizer Agent - وكيل تنظيم الملفات

**الوظائف:**
- تنظيم الملفات حسب النوع
- إنشاء هيكل مجلدات منطقي
- نقل الملفات للمواقع المناسبة
- تنظيف الملفات المؤقتة

**الاستخدام:**
```bash
python main.py --project "messy_project" --organize
```

**قواعد التنظيم:**
- `*.py` → `src/`
- `test_*.py` → `tests/`
- `*.md, *.txt` → `docs/`
- `*.json, *.yaml` → `config/`
- `*.csv, *.xlsx` → `data/`

### 🧠 Memory Agent - وكيل الذاكرة

**الوظائف:**
- حفظ سياق المشروع
- توثيق التغييرات
- إدارة المعرفة المشتركة
- تتبع التقدم

**الاستخدام:**
```bash
python main.py --project "my_project" --agent memory
```

**الملفات المُنتجة:**
- `workspace/shared_memory/shared_memory.md`
- `workspace/collaboration_logs/collaboration_log.md`
- `workspace/knowledge_base/knowledge_base.json`

### 🔍 Error Detection Agent - وكيل كشف الأخطاء

**الوظائف:**
- كشف الأخطاء النحوية
- فحص جودة الكود
- اكتشاف المشاكل الأمنية
- تحليل الأداء

**الاستخدام:**
```bash
python main.py --project "my_project" --agent error
```

**أنواع الأخطاء المكتشفة:**
- أخطاء نحوية (Syntax Errors)
- أخطاء الاستيراد (Import Errors)
- مشاكل الأسلوب (Style Issues)
- مشاكل أمنية (Security Issues)
- مشاكل الأداء (Performance Issues)

### 📊 Project Analyzer Agent - وكيل تحليل المشاريع

**الوظائف:**
- تحليل حجم وتعقيد المشروع
- تقييم جودة الكود
- تحليل التبعيات
- تقييم الهيكل المعماري

**الاستخدام:**
```bash
python main.py --project "my_project" --agent analyzer
```

**المقاييس المُحللة:**
- حجم المشروع (عدد الملفات والأسطر)
- تعقيد الكود (Cyclomatic Complexity)
- جودة التوثيق
- تغطية الاختبارات
- الأمان والأداء

---

## 🔌 نظام الإضافات

### إنشاء إضافة جديدة

#### الخطوة 1: إنشاء ملف الإضافة
```python
# plugins/my_custom_plugin.py
from plugins.base_plugin import BasePlugin

class MyCustomPlugin(BasePlugin):
    def get_plugin_info(self):
        return {
            'name': 'My Custom Plugin',
            'description': 'إضافة مخصصة للمشروع',
            'version': '1.0.0',
            'author': 'Your Name'
        }
    
    def initialize_plugin(self):
        self.custom_setting = self.config.get('custom_setting', 'default')
    
    def execute(self, action='default', data=None):
        if action == 'process':
            return {
                'success': True,
                'result': f'Processed: {data}',
                'setting': self.custom_setting
            }
        return {'success': False, 'error': 'Unknown action'}
```

#### الخطوة 2: تكوين الإضافة
```json
{
  "plugins": {
    "enabled": true,
    "auto_load": true,
    "MyCustomPlugin": {
      "custom_setting": "my_value"
    }
  }
}
```

#### الخطوة 3: استخدام الإضافة
```python
from core.assistant_system import UniversalAssistantSystem

system = UniversalAssistantSystem("my_project")
result = system.run_plugin('MyCustomPlugin', action='process', data='test')
```

### إدارة الإضافات

#### عرض الإضافات المتاحة:
```python
system = UniversalAssistantSystem("my_project")
plugins = system.plugin_manager.list_plugins()
print(plugins)
```

#### تفعيل/إلغاء تفعيل إضافة:
```python
# تفعيل
system.plugin_manager.enable_plugin('MyCustomPlugin')

# إلغاء تفعيل
system.plugin_manager.disable_plugin('MyCustomPlugin')
```

---

## 📊 التقارير والتحليلات

### أنواع التقارير

#### 1. تقرير التحليل الشامل
```json
{
  "project_info": {
    "name": "My Project",
    "type": "streamlit",
    "analysis_time": "2025-07-14T12:00:00"
  },
  "agents_results": {
    "database_agent": {...},
    "file_organizer_agent": {...},
    "memory_agent": {...},
    "error_detector_agent": {...},
    "project_analyzer_agent": {...}
  },
  "summary": {
    "overall_score": 85.5,
    "issues_found": 12,
    "recommendations": 8
  }
}
```

#### 2. تقرير كشف الأخطاء
```json
{
  "error_analysis": {
    "syntax_errors": [...],
    "security_issues": [...],
    "style_issues": [...],
    "total_files_analyzed": 25,
    "files_with_errors": 3
  },
  "quality_analysis": {
    "complexity_score": 75,
    "documentation_score": 60,
    "maintainability_score": 70
  }
}
```

#### 3. تقرير تحليل المشروع
```json
{
  "project_size_analysis": {
    "total_files": 50,
    "total_lines": 5000,
    "code_lines": 3500
  },
  "complexity_analysis": {
    "average_complexity": 12.5,
    "complexity_distribution": {
      "low": 30,
      "medium": 15,
      "high": 5
    }
  },
  "security_analysis": {
    "security_score": 90,
    "security_issues": []
  }
}
```

### قراءة وتفسير التقارير

#### نقاط الجودة:
- **90-100**: ممتاز
- **80-89**: جيد جداً
- **70-79**: جيد
- **60-69**: مقبول
- **أقل من 60**: يحتاج تحسين

#### مستويات الخطورة:
- **High**: يجب إصلاحها فوراً
- **Medium**: يُفضل إصلاحها قريباً
- **Low**: يمكن إصلاحها لاحقاً

---

## ⚙️ الإعدادات المتقدمة

### تخصيص ملف التكوين

#### إعدادات النظام:
```json
{
  "system": {
    "name": "My Custom System",
    "language": "ar",
    "encoding": "utf-8"
  }
}
```

#### إعدادات الوكلاء:
```json
{
  "agents": {
    "database_agent": {
      "enabled": true,
      "auto_backup": true,
      "backup_frequency": "daily"
    },
    "error_detector_agent": {
      "enabled": true,
      "check_syntax": true,
      "check_security": true,
      "auto_fix": false
    }
  }
}
```

#### إعدادات مساحة العمل:
```json
{
  "workspace": {
    "base_dir": "custom_workspace",
    "auto_cleanup": true,
    "max_log_size_mb": 100
  }
}
```

### متغيرات البيئة

```bash
# إعداد مجلد العمل
export UNIVERSAL_AI_WORKSPACE=/path/to/workspace

# إعداد مستوى السجلات
export UNIVERSAL_AI_LOG_LEVEL=DEBUG

# إعداد قاعدة البيانات
export DATABASE_URL=sqlite:///project.db
```

---

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة والحلول

#### 1. خطأ في تحميل الوكلاء
```
❌ خطأ في تحميل النواة الأساسية: No module named 'agents'
```
**الحل:**
```bash
# التأكد من تفعيل البيئة الافتراضية
source venv/bin/activate

# إعادة تثبيت التبعيات
pip install -r requirements.txt --force-reinstall
```

#### 2. مشكلة في الأذونات
```
❌ Permission denied: 'workspace/logs/agent.log'
```
**الحل:**
```bash
# إعطاء أذونات للمجلد
chmod -R 755 workspace/

# أو تشغيل بصلاحيات المستخدم
python main.py --project . --analyze
```

#### 3. نفاد الذاكرة
```
❌ MemoryError: Unable to allocate array
```
**الحل:**
```bash
# تقليل حجم التحليل
python main.py --project . --agent memory --verbose

# أو تحليل ملفات محددة فقط
python main.py --project "src/" --analyze
```

#### 4. مشكلة في قاعدة البيانات
```
❌ Database connection failed
```
**الحل:**
```json
{
  "database_agent": {
    "database": {
      "type": "sqlite",
      "name": "fallback.db"
    }
  }
}
```

### تشخيص المشاكل

#### تشغيل الاختبارات:
```bash
# اختبار النظام الأساسي
python test_system.py

# اختبار الوكلاء
python test_agents.py

# اختبار شامل
python run_all_tests.py
```

#### فحص السجلات:
```bash
# عرض آخر السجلات
tail -f workspace/logs/universalassistants.log

# البحث عن أخطاء
grep "ERROR" workspace/logs/*.log
```

---

## 💡 أمثلة عملية

### مثال 1: تحليل مشروع Streamlit

```bash
# إنشاء مشروع Streamlit جديد
mkdir my_streamlit_app
cd my_streamlit_app

# إنشاء ملف التطبيق
cat > app.py << EOF
import streamlit as st
import pandas as pd

st.title("My Streamlit App")
data = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
st.dataframe(data)
EOF

# تحليل المشروع
cd ../Universal-AI-Assistants
python main.py --project "../my_streamlit_app" --analyze --project-type streamlit
```

### مثال 2: تنظيم مشروع فوضوي

```bash
# مشروع غير منظم
mkdir messy_project
cd messy_project

# ملفات متناثرة
touch main.py utils.py config.json data.csv README.md
touch test_main.py test_utils.py
mkdir temp && touch temp/cache.tmp

# تنظيم المشروع
cd ../Universal-AI-Assistants
python main.py --project "../messy_project" --organize --verbose
```

### مثال 3: فحص أمان مشروع

```bash
# إنشاء ملف بمشاكل أمنية
cat > security_test.py << EOF
password = "secret123"
user_input = input("Enter command: ")
eval(user_input)  # خطر أمني!
EOF

# فحص الأمان
python main.py --project "." --agent error --verbose
```

### مثال 4: استخدام الإضافات

```python
# استخدام برمجي للنظام
from core.assistant_system import UniversalAssistantSystem

# تهيئة النظام
system = UniversalAssistantSystem("my_project")

# تشغيل تحليل شامل
analysis = system.analyze_project()
print(f"نقاط الجودة: {analysis['summary']['overall_score']}")

# تشغيل إضافة
plugin_result = system.run_plugin('ExamplePlugin', action='greet')
print(plugin_result['message'])

# إغلاق النظام
system.shutdown()
```

---

## 📞 الحصول على المساعدة

### الموارد المتاحة:
- **التوثيق الكامل**: `docs/`
- **أمثلة عملية**: `templates/`
- **الأسئلة الشائعة**: `docs/faq.md`
- **دليل المطور**: `docs/developer_guide.md`

### طلب المساعدة:
1. راجع هذا الدليل أولاً
2. ابحث في الأسئلة الشائعة
3. شغل الاختبارات للتأكد من سلامة النظام
4. أنشئ مشكلة جديدة في GitHub مع تفاصيل المشكلة

### معلومات مفيدة عند طلب المساعدة:
- إصدار Python المستخدم
- نظام التشغيل
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
- ملف التكوين المستخدم

---

---

## 🎓 خلاصة الدليل

تهانينا! لقد أكملت دليل المستخدم الشامل لنظام المساعدين الذكيين العالمي. الآن أصبحت قادراً على:

### ما تعلمته:
- ✅ تثبيت وتشغيل النظام
- ✅ استخدام جميع الوكلاء الذكيين
- ✅ إنشاء وإدارة الإضافات
- ✅ قراءة وتفسير التقارير
- ✅ تخصيص الإعدادات
- ✅ حل المشاكل الشائعة

### الخطوات التالية:
1. **جرب النظام** مع مشاريعك الحقيقية
2. **أنشئ إضافات مخصصة** لاحتياجاتك
3. **شارك تجربتك** مع المجتمع
4. **ساهم في التطوير** بأفكارك واقتراحاتك

### نصائح للاستخدام الأمثل:
- ✅ ابدأ بمشاريع صغيرة للتعلم
- ✅ استخدم الوضع المفصل (-v) للتشخيص
- ✅ راجع التقارير بانتظام
- ✅ احتفظ بنسخ احتياطية من إعداداتك
- ✅ حدث النظام بانتظام للحصول على أحدث الميزات

**مع تمنياتنا لك بتجربة رائعة مع نظام المساعدين الذكيين العالمي! 🚀**
