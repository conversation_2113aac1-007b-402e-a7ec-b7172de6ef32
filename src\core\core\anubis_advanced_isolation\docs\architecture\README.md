# 🏺 معمارية نظام العزل المتقدم لأنوبيس

## نظرة عامة
نظام عزل متقدم يستخدم Docker لعزل كل مكون في حاوية منفصلة مع شبكات وأحجام معزولة.

## المكونات الرئيسية

### 🐳 الحاويات
- **anubis_main**: النظام الرئيسي لأنوبيس
- **universal_ai**: نظام Universal AI Assistants
- **tools_suite**: مجموعة الأدوات المساعدة
- **n8n_workflows**: أدوات سير العمل
- **prometheus**: مراقبة المقاييس
- **grafana**: لوحات التحكم

### 🌐 الشبكات
- **anubis_main_network**: الشبكة الرئيسية (172.31.0.0/16)
- **anubis_monitoring_network**: شبكة المراقبة (172.32.0.0/16)
- **anubis_security_network**: شبكة الأمان (172.33.0.0/16)

### 💾 الأحجام
- **anubis_main_data**: بيانات النظام الرئيسي
- **universal_ai_data**: بيانات Universal AI
- **tools_data**: بيانات الأدوات
- **database_data**: بيانات قاعدة البيانات (مشفرة)
- **monitoring_data**: بيانات المراقبة
- **shared_logs**: السجلات المشتركة

## الأمان
- مستخدمين غير جذر في جميع الحاويات
- شبكات معزولة مع سياسات وصول محددة
- أسرار مشفرة لكلمات المرور والمفاتيح
- نظام ملفات للقراءة فقط حيث أمكن

## المراقبة
- Prometheus لجمع المقاييس
- Grafana للوحات التحكم
- فحص صحة تلقائي لجميع الخدمات
- تنبيهات للمشاكل الحرجة
