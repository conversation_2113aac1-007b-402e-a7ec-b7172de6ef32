{"timestamp": "2025-07-18T16:21:11.123623", "project_path": "anubis", "checks": {"black": {"tool": "black", "exit_code": 0, "stdout": "", "stderr": "reformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\langsmith_wrapper.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\final_validation_runner.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\logger.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_connector.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\ai_integration.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\anubis_database.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\assistant_system.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\direct_setup.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_manager.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api\\anubis_api_server.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\setup_database.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\database_validator.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\config_manager.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_connection.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\base_agent.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\base_plugin.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_db_connection.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\example_plugin.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ready.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\simple_validation.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\run_all_tests.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\plugin_manager.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\main.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\agents_cleanup.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\comprehensive_test.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\emergency_vscode_check.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\fix_agents_with_gemini.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ollama.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\stress_test.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_cli_helper.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\create_all_readmes.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\activate_real_langsmith.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\code_quality_checker.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\complete_file_organizer.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_integration_system.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_vscode_check.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_ollama_langsmith.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\run_vscode_monitor.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_all_files.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\process_monitor.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_project_files.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\setup_langsmith.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\langsmith_integration_demo.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\start_anubis_n8n_system.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\quick_ai_test.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates\\streamlit_template\\main.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_agents.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_langsmith_integration.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_alerts.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\smart_workflow_demo.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_heavy_load_analyzer.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_monitor.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\run_all_tests.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_system_test.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_agents_test.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_error_detector.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_anubis_system.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_fixed.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_error_detector.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_integration.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_smart_analyzer.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_logic.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_database.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_system.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py\nreformatted C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_plugins.py\n\nAll done! ✨ 🍰 ✨\n69 files reformatted, 11 files left unchanged.\n"}, "flake8": {"tool": "flake8", "exit_code": 1, "stdout": "", "stderr": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe: No module named flake8\n", "issues_count": 0}, "pylint": {"tool": "pylint", "exit_code": 30, "stdout": "************* Module anubis.main\nanubis\\main.py:22:4: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\main.py:28:4: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\main.py:136:8: W0707: Consider explicitly re-raising using 'raise SystemInitializationError(f'فشل في إنشاء مجلد التقارير: {e}') from e' (raise-missing-from)\nanubis\\main.py:160:8: W0707: Consider explicitly re-raising using 'raise ConfigurationError(f'خطأ في التكوين: {e}') from e' (raise-missing-from)\nanubis\\main.py:162:8: W0707: Consider explicitly re-raising using 'raise SystemInitializationError(f'خطأ في تهيئة النظام: {e}') from e' (raise-missing-from)\nanubis\\main.py:201:8: W0707: Consider explicitly re-raising using 'raise RuntimeError(f'خطأ أثناء تشغيل العمليات: {e}') from e' (raise-missing-from)\nanubis\\main.py:219:21: E1101: Module 'json' has no 'JSONEncodeError' member (no-member)\nanubis\\main.py:284:11: W0718: Catching too general exception Exception (broad-exception-caught)\n************* Module anubis.api.anubis_api_server\nanubis\\api\\anubis_api_server.py:360:0: C0301: Line too long (126/100) (line-too-long)\nanubis\\api\\anubis_api_server.py:19:0: C0413: Import \"import uvicorn\" should be placed at the top of the module (wrong-import-position)\nanubis\\api\\anubis_api_server.py:20:0: C0413: Import \"from fastapi import Depends, FastAPI, HTTPException, Security\" should be placed at the top of the module (wrong-import-position)\nanubis\\api\\anubis_api_server.py:21:0: C0413: Import \"from fastapi.middleware.cors import CORSMiddleware\" should be placed at the top of the module (wrong-import-position)\nanubis\\api\\anubis_api_server.py:22:0: C0413: Import \"from fastapi.security import APIKeyHeader\" should be placed at the top of the module (wrong-import-position)\nanubis\\api\\anubis_api_server.py:23:0: C0413: Import \"from pydantic import BaseModel, Field\" should be placed at the top of the module (wrong-import-position)\nanubis\\api\\anubis_api_server.py:146:4: W0602: Using global for 'ollama_providers' but no assignment is done (global-variable-not-assigned)\nanubis\\api\\anubis_api_server.py:156:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\api\\anubis_api_server.py:163:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\api\\anubis_api_server.py:219:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:227:8: C0415: Import outside toplevel (subprocess) (import-outside-toplevel)\nanubis\\api\\anubis_api_server.py:232:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\api\\anubis_api_server.py:242:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\api\\anubis_api_server.py:253:8: W0707: Consider explicitly re-raising using 'except Exception as exc' and 'raise HTTPException(status_code=408, detail='Gemini request timeout') from exc' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:255:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:305:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:348:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\api\\anubis_api_server.py:375:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:386:19: E1101: Instance of 'AnubisDatabase' has no 'get_all_projects' member (no-member)\nanubis\\api\\anubis_api_server.py:390:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:410:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:418:8: C0415: Import outside toplevel (langsmith.traceable) (import-outside-toplevel)\nanubis\\api\\anubis_api_server.py:433:8: W0707: Consider explicitly re-raising using 'raise HTTPException(status_code=500, detail=str(e)) from e' (raise-missing-from)\nanubis\\api\\anubis_api_server.py:424:8: W0612: Unused variable 'result' (unused-variable)\nanubis\\api\\anubis_api_server.py:8:0: W0611: Unused import json (unused-import)\n************* Module anubis.core.ai_integration\nanubis\\core\\ai_integration.py:26:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\core\\ai_integration.py:31:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\core\\ai_integration.py:66:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\ai_integration.py:74:8: W0702: No exception type(s) specified (bare-except)\nanubis\\core\\ai_integration.py:122:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\ai_integration.py:183:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\ai_integration.py:232:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\ai_integration.py:242:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\ai_integration.py:12:0: W0611: Unused import subprocess (unused-import)\nanubis\\core\\ai_integration.py:15:0: W0611: Unused Optional imported from typing (unused-import)\nanubis\\core\\ai_integration.py:15:0: W0611: Unused Union imported from typing (unused-import)\n************* Module anubis.core.assistant_system\nanubis\\core\\assistant_system.py:40:0: R0902: Too many instance attributes (14/7) (too-many-instance-attributes)\nanubis\\core\\assistant_system.py:78:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:144:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:135:16: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\assistant_system.py:148:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\assistant_system.py:159:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:153:16: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\assistant_system.py:125:4: R0911: Too many return statements (9/6) (too-many-return-statements)\nanubis\\core\\assistant_system.py:205:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:208:20: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\core\\assistant_system.py:230:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:259:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:312:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:319:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\core\\assistant_system.py:342:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\assistant_system.py:13:0: W0611: Unused List imported from typing (unused-import)\nanubis\\core\\assistant_system.py:13:0: W0611: Unused Optional imported from typing (unused-import)\nanubis\\core\\assistant_system.py:19:4: W0611: Unused BaseAgent imported from base_agent (unused-import)\nanubis\\core\\assistant_system.py:20:4: W0611: Unused ConfigManager imported from config_manager (unused-import)\nanubis\\core\\assistant_system.py:21:4: W0611: Unused SessionLogger imported from logger (unused-import)\n************* Module anubis.core.base_agent\nanubis\\core\\base_agent.py:312:0: C0301: Line too long (124/100) (line-too-long)\nanubis\\core\\base_agent.py:21:0: R0902: Too many instance attributes (14/7) (too-many-instance-attributes)\nanubis\\core\\base_agent.py:86:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\core\\base_agent.py:91:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\core\\base_agent.py:117:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:157:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:220:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:244:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:235:16: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\base_agent.py:248:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\base_agent.py:259:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:253:16: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\core\\base_agent.py:225:4: R0911: Too many return statements (9/6) (too-many-return-statements)\nanubis\\core\\base_agent.py:303:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\base_agent.py:8:0: W0611: Unused import os (unused-import)\nanubis\\core\\base_agent.py:12:0: W0611: Unused Optional imported from typing (unused-import)\n************* Module anubis.core.config_manager\nanubis\\core\\config_manager.py:275:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\config_manager.py:8:0: W0611: Unused import os (unused-import)\nanubis\\core\\config_manager.py:11:0: W0611: Unused Optional imported from typing (unused-import)\n************* Module anubis.core.langsmith_wrapper\nanubis\\core\\langsmith_wrapper.py:36:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\langsmith_wrapper.py:50:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\core\\langsmith_wrapper.py:58:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\core\\langsmith_wrapper.py:59:19: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\core\\langsmith_wrapper.py:81:8: W0612: Unused variable 'performance_data' (unused-variable)\nanubis\\core\\langsmith_wrapper.py:11:0: W0611: Unused Optional imported from typing (unused-import)\nanubis\\core\\langsmith_wrapper.py:16:4: W0611: Unused tracing_context imported from langsmith.utils (unused-import)\n************* Module anubis.core.logger\nanubis\\core\\logger.py:18:4: R0917: Too many positional arguments (6/5) (too-many-positional-arguments)\nanubis\\core\\logger.py:138:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\core\\logger.py:8:0: W0611: Unused import os (unused-import)\nanubis\\core\\logger.py:12:0: W0611: Unused Optional imported from typing (unused-import)\n************* Module anubis.database.anubis_database\nanubis\\database\\anubis_database.py:83:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:202:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:230:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:234:4: R0917: Too many positional arguments (6/5) (too-many-positional-arguments)\nanubis\\database\\anubis_database.py:268:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:272:4: R0917: Too many positional arguments (7/5) (too-many-positional-arguments)\nanubis\\database\\anubis_database.py:298:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:316:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:346:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:350:4: R0917: Too many positional arguments (6/5) (too-many-positional-arguments)\nanubis\\database\\anubis_database.py:374:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:416:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\anubis_database.py:10:0: W0611: Unused import os (unused-import)\nanubis\\database\\anubis_database.py:13:0: W0611: Unused Optional imported from typing (unused-import)\nanubis\\database\\anubis_database.py:13:0: W0611: Unused Union imported from typing (unused-import)\n************* Module anubis.database.simple_validation\nanubis\\database\\simple_validation.py:37:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\database\\simple_validation.py:352:4: W0621: Redefining name 'exit_code' from outer scope (line 357) (redefined-outer-name)\nanubis\\database\\simple_validation.py:348:11: W0718: Catching too general exception Exception (broad-exception-caught)\n************* Module anubis.plugins.base_plugin\nanubis\\plugins\\base_plugin.py:37:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\plugins\\base_plugin.py:44:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\plugins\\base_plugin.py:49:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\plugins\\base_plugin.py:54:8: W0107: Unnecessary pass statement (unnecessary-pass)\nanubis\\plugins\\base_plugin.py:92:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\plugins\\base_plugin.py:9:0: W0611: Unused Optional imported from typing (unused-import)\n************* Module anubis.plugins.example_plugin\nanubis\\plugins\\example_plugin.py:35:4: W0221: Variadics removed in overriding 'ExamplePlugin.execute' method (arguments-differ)\nanubis\\plugins\\example_plugin.py:37:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\plugins\\example_plugin.py:30:8: W0201: Attribute 'greeting_message' defined outside __init__ (attribute-defined-outside-init)\nanubis\\plugins\\example_plugin.py:31:8: W0201: Attribute 'enable_analysis' defined outside __init__ (attribute-defined-outside-init)\n************* Module anubis.plugins.plugin_manager\nanubis\\plugins\\plugin_manager.py:51:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\plugins\\plugin_manager.py:93:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\plugins\\plugin_manager.py:114:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\plugins\\plugin_manager.py:181:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\plugins\\plugin_manager.py:215:69: C0201: Consider iterating the dictionary directly instead of calling .keys() (consider-iterating-dictionary)\nanubis\\plugins\\plugin_manager.py:10:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.activate_real_langsmith\nanubis\\scripts\\activate_real_langsmith.py:360:0: C0301: Line too long (118/100) (line-too-long)\nanubis\\scripts\\activate_real_langsmith.py:82:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:77:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\activate_real_langsmith.py:78:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\activate_real_langsmith.py:121:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:92:17: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:99:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:103:20: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\activate_real_langsmith.py:107:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:177:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:139:17: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:148:25: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:153:24: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\activate_real_langsmith.py:237:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:188:17: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:197:25: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:203:24: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\activate_real_langsmith.py:311:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:246:17: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:253:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:256:20: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\activate_real_langsmith.py:266:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:277:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:289:21: E1101: Instance of 'Client' has no 'trace' member (no-member)\nanubis\\scripts\\activate_real_langsmith.py:338:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:379:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:404:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\activate_real_langsmith.py:409:8: W0612: Unused variable 'report_file' (unused-variable)\nanubis\\scripts\\activate_real_langsmith.py:442:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\n************* Module anubis.scripts.agents_cleanup\nanubis\\scripts\\agents_cleanup.py:71:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\agents_cleanup.py:90:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\agents_cleanup.py:164:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\agents_cleanup.py:162:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\agents_cleanup.py:397:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\agents_cleanup.py:395:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\agents_cleanup.py:438:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\agents_cleanup.py:463:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\agents_cleanup.py:491:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\agents_cleanup.py:492:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\agents_cleanup.py:489:4: W0612: Unused variable 'result' (unused-variable)\n************* Module anubis.scripts.check_ollama\nanubis\\scripts\\check_ollama.py:32:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\check_ollama.py:23:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\check_ollama.py:87:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\check_ollama.py:42:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\check_ollama.py:44:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\check_ollama.py:121:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\check_ollama.py:105:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\check_ollama.py:161:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\check_ollama.py:131:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\check_ollama.py:133:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\check_ollama.py:192:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\check_ollama.py:204:21: W0612: Unused variable 'api_data' (unused-variable)\nanubis\\scripts\\check_ollama.py:235:4: W0612: Unused variable 'report' (unused-variable)\n************* Module anubis.scripts.check_ready\nanubis\\scripts\\check_ready.py:10:0: C0413: Import \"import sys\" should be placed at the top of the module (wrong-import-position)\nanubis\\scripts\\check_ready.py:30:0: C0413: Import \"import os\" should be placed at the top of the module (wrong-import-position)\nanubis\\scripts\\check_ready.py:16:4: W0611: Unused import tkinter (unused-import)\n************* Module anubis.scripts.code_quality_checker\nanubis\\scripts\\code_quality_checker.py:68:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\code_quality_checker.py:36:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\code_quality_checker.py:103:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\code_quality_checker.py:76:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\code_quality_checker.py:113:8: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\code_quality_checker.py:153:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\code_quality_checker.py:122:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\code_quality_checker.py:200:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\code_quality_checker.py:259:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\code_quality_checker.py:229:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\code_quality_checker.py:268:12: W0612: Unused variable 'tool' (unused-variable)\nanubis\\scripts\\code_quality_checker.py:351:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\code_quality_checker.py:10:0: W0611: Unused import os (unused-import)\nanubis\\scripts\\code_quality_checker.py:15:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.complete_file_organizer\nanubis\\scripts\\complete_file_organizer.py:159:0: C0301: Line too long (102/100) (line-too-long)\nanubis\\scripts\\complete_file_organizer.py:135:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\complete_file_organizer.py:174:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\complete_file_organizer.py:224:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\complete_file_organizer.py:245:31: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\complete_file_organizer.py:255:31: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\complete_file_organizer.py:290:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\complete_file_organizer.py:312:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\complete_file_organizer.py:320:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\complete_file_organizer.py:338:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\complete_file_organizer.py:339:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\complete_file_organizer.py:336:4: W0612: Unused variable 'result' (unused-variable)\nanubis\\scripts\\complete_file_organizer.py:15:0: W0611: Unused Any imported from typing (unused-import)\nanubis\\scripts\\complete_file_organizer.py:15:0: W0611: Unused Dict imported from typing (unused-import)\nanubis\\scripts\\complete_file_organizer.py:15:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.create_all_readmes\nanubis\\scripts\\create_all_readmes.py:49:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\create_all_readmes.py:40:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\create_all_readmes.py:43:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\create_all_readmes.py:95:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\create_all_readmes.py:78:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\create_all_readmes.py:85:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\create_all_readmes.py:151:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\create_all_readmes.py:231:25: W0612: Unused variable 'file_count' (unused-variable)\nanubis\\scripts\\create_all_readmes.py:274:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\create_all_readmes.py:290:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\create_all_readmes.py:295:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\create_all_readmes.py:322:4: W0612: Unused variable 'report' (unused-variable)\n************* Module anubis.scripts.emergency_vscode_check\nanubis\\scripts\\emergency_vscode_check.py:42:8: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\emergency_vscode_check.py:52:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:84:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:94:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:114:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:167:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\emergency_vscode_check.py:145:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:161:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\emergency_vscode_check.py:141:8: W0612: Unused variable 'danger_level' (unused-variable)\n************* Module anubis.scripts.fix_agents_with_gemini\nanubis\\scripts\\fix_agents_with_gemini.py:190:24: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:190:27: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:190:34: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:191:23: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:191:26: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:192:23: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:192:26: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:193:25: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:193:28: W1401: Anomalous backslash in string: '\\*'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:194:25: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:197:22: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:198:23: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:198:26: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:199:28: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:200:27: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:201:21: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:205:28: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:205:31: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:205:33: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:205:41: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:205:44: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:206:27: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:206:30: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:206:37: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:206:39: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:206:44: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:212:23: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:212:32: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:212:37: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:213:22: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:213:25: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:214:28: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:214:31: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:23: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:28: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:31: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:38: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:40: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:46: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:49: W1401: Anomalous backslash in string: '\\w'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:52: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:55: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:217:62: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:218:26: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:218:32: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:218:34: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:22: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:27: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:30: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:38: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:41: W1401: Anomalous backslash in string: '\\('. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:48: W1401: Anomalous backslash in string: '\\)'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:221:50: W1401: Anomalous backslash in string: '\\s'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:222:22: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:222:29: W1401: Anomalous backslash in string: '\\.'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:222:31: W1401: Anomalous backslash in string: '\\w'. String constant might be missing an r prefix. (anomalous-backslash-in-string)\nanubis\\scripts\\fix_agents_with_gemini.py:521:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\fix_agents_with_gemini.py:10:0: W0611: Unused import json (unused-import)\nanubis\\scripts\\fix_agents_with_gemini.py:13:0: W0611: Unused datetime imported from datetime (unused-import)\n************* Module anubis.scripts.gemini_cli_helper\nanubis\\scripts\\gemini_cli_helper.py:39:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\gemini_cli_helper.py:96:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_cli_helper.py:70:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\gemini_cli_helper.py:77:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\gemini_cli_helper.py:235:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_cli_helper.py:11:0: W0611: Unused import os (unused-import)\nanubis\\scripts\\gemini_cli_helper.py:13:0: W0611: Unused import sys (unused-import)\nanubis\\scripts\\gemini_cli_helper.py:15:0: W0611: Unused Path imported from pathlib (unused-import)\nanubis\\scripts\\gemini_cli_helper.py:16:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.gemini_integration_system\nanubis\\scripts\\gemini_integration_system.py:40:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\gemini_integration_system.py:155:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_integration_system.py:124:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\gemini_integration_system.py:127:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\gemini_integration_system.py:134:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\gemini_integration_system.py:184:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\gemini_integration_system.py:217:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\gemini_integration_system.py:249:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\gemini_integration_system.py:271:12: R1724: Unnecessary \"elif\" after \"continue\", remove the leading \"el\" from \"elif\" (no-else-continue)\nanubis\\scripts\\gemini_integration_system.py:313:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_integration_system.py:321:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_integration_system.py:329:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_integration_system.py:355:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\gemini_integration_system.py:13:0: W0611: Unused import time (unused-import)\nanubis\\scripts\\gemini_integration_system.py:15:0: W0611: Unused Path imported from pathlib (unused-import)\nanubis\\scripts\\gemini_integration_system.py:16:0: W0611: Unused List imported from typing (unused-import)\nanubis\\scripts\\gemini_integration_system.py:16:0: W0611: Unused Optional imported from typing (unused-import)\n************* Module anubis.scripts.langsmith_integration_demo\nanubis\\scripts\\langsmith_integration_demo.py:343:0: C0301: Line too long (101/100) (line-too-long)\nanubis\\scripts\\langsmith_integration_demo.py:72:36: E0601: Using variable 'MockLangSmithClient' before assignment (used-before-assignment)\nanubis\\scripts\\langsmith_integration_demo.py:90:12: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\langsmith_integration_demo.py:101:68: W0612: Unused variable 'trace' (unused-variable)\nanubis\\scripts\\langsmith_integration_demo.py:181:27: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\langsmith_integration_demo.py:156:24: C0415: Import outside toplevel (anubis.core.ai_integration.OllamaProvider) (import-outside-toplevel)\nanubis\\scripts\\langsmith_integration_demo.py:229:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\langsmith_integration_demo.py:265:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\langsmith_integration_demo.py:260:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\langsmith_integration_demo.py:289:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\langsmith_integration_demo.py:303:4: W0621: Redefining name 'orchestrator' from outer scope (line 358) (redefined-outer-name)\nanubis\\scripts\\langsmith_integration_demo.py:313:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\langsmith_integration_demo.py:307:8: E0401: Unable to import 'anubis.agents.smart_code_analyzer' (import-error)\nanubis\\scripts\\langsmith_integration_demo.py:307:8: C0415: Import outside toplevel (anubis.agents.smart_code_analyzer.SmartCodeAnalyzer) (import-outside-toplevel)\nanubis\\scripts\\langsmith_integration_demo.py:307:8: E0611: No name 'smart_code_analyzer' in module 'anubis.agents' (no-name-in-module)\nanubis\\scripts\\langsmith_integration_demo.py:10:0: W0611: Unused import json (unused-import)\nanubis\\scripts\\langsmith_integration_demo.py:15:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.organize_all_files\nanubis\\scripts\\organize_all_files.py:127:0: C0301: Line too long (111/100) (line-too-long)\nanubis\\scripts\\organize_all_files.py:114:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_all_files.py:194:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_all_files.py:229:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_all_files.py:224:12: C0415: Import outside toplevel (json) (import-outside-toplevel)\nanubis\\scripts\\organize_all_files.py:296:4: W0612: Unused variable 'structure' (unused-variable)\nanubis\\scripts\\organize_all_files.py:317:4: R1722: Consider using 'sys.exit' instead (consider-using-sys-exit)\n************* Module anubis.scripts.organize_project_files\nanubis\\scripts\\organize_project_files.py:115:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:138:33: C0207: Use __file__.rsplit('/', maxsplit=1)[-1] instead (use-maxsplit-arg)\nanubis\\scripts\\organize_project_files.py:166:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:188:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:259:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:277:8: C0206: Consider iterating with .items() (consider-using-dict-items)\nanubis\\scripts\\organize_project_files.py:277:27: C0201: Consider iterating the dictionary directly instead of calling .keys() (consider-iterating-dictionary)\nanubis\\scripts\\organize_project_files.py:303:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:345:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\organize_project_files.py:358:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\organize_project_files.py:11:0: W0611: Unused import os (unused-import)\nanubis\\scripts\\organize_project_files.py:16:0: W0611: Unused Any imported from typing (unused-import)\nanubis\\scripts\\organize_project_files.py:16:0: W0611: Unused Dict imported from typing (unused-import)\nanubis\\scripts\\organize_project_files.py:16:0: W0611: Unused List imported from typing (unused-import)\n************* Module anubis.scripts.process_monitor\nanubis\\scripts\\process_monitor.py:59:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\process_monitor.py:60:12: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\process_monitor.py:204:8: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\process_monitor.py:208:8: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\process_monitor.py:219:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\process_monitor.py:220:16: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\process_monitor.py:272:4: C0415: Import outside toplevel (argparse) (import-outside-toplevel)\n************* Module anubis.scripts.quick_start\nanubis\\scripts\\quick_start.py:72:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\quick_start.py:73:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\quick_start.py:94:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\quick_start.py:113:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\quick_start.py:110:8: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\quick_start.py:159:11: W0718: Catching too general exception Exception (broad-exception-caught)\n************* Module anubis.scripts.quick_vscode_check\nanubis\\scripts\\quick_vscode_check.py:85:0: C0301: Line too long (115/100) (line-too-long)\nanubis\\scripts\\quick_vscode_check.py:25:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:39:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:70:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:76:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:89:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:68:0: R0912: Too many branches (14/12) (too-many-branches)\nanubis\\scripts\\quick_vscode_check.py:168:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:172:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:198:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\quick_vscode_check.py:193:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:197:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:200:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\quick_vscode_check.py:201:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\n************* Module anubis.scripts.run_vscode_monitor\nanubis\\scripts\\run_vscode_monitor.py:18:8: C0415: Import outside toplevel (psutil) (import-outside-toplevel)\nanubis\\scripts\\run_vscode_monitor.py:18:8: W0611: Unused import psutil (unused-import)\nanubis\\scripts\\run_vscode_monitor.py:43:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\run_vscode_monitor.py:34:8: E0401: Unable to import 'vscode_process_monitor' (import-error)\nanubis\\scripts\\run_vscode_monitor.py:34:8: C0415: Import outside toplevel (vscode_process_monitor.VSCodeProcessMonitor) (import-outside-toplevel)\nanubis\\scripts\\run_vscode_monitor.py:39:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\run_vscode_monitor.py:37:8: W0612: Unused variable 'report' (unused-variable)\nanubis\\scripts\\run_vscode_monitor.py:62:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\run_vscode_monitor.py:55:8: E0401: Unable to import 'vscode_process_alerts' (import-error)\nanubis\\scripts\\run_vscode_monitor.py:55:8: C0415: Import outside toplevel (vscode_process_alerts.VSCodeProcessAlerts) (import-outside-toplevel)\nanubis\\scripts\\run_vscode_monitor.py:133:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\run_vscode_monitor.py:109:8: C0415: Import outside toplevel (platform) (import-outside-toplevel)\nanubis\\scripts\\run_vscode_monitor.py:111:8: C0415: Import outside toplevel (psutil) (import-outside-toplevel)\nanubis\\scripts\\run_vscode_monitor.py:127:12: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\run_vscode_monitor.py:204:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\run_vscode_monitor.py:248:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\run_vscode_monitor.py:221:12: R1723: Unnecessary \"elif\" after \"break\", remove the leading \"el\" from \"elif\" (no-else-break)\nanubis\\scripts\\run_vscode_monitor.py:208:0: R0912: Too many branches (16/12) (too-many-branches)\n************* Module anubis.scripts.setup_langsmith\nanubis\\scripts\\setup_langsmith.py:33:12: C0415: Import outside toplevel (langsmith) (import-outside-toplevel)\nanubis\\scripts\\setup_langsmith.py:33:12: W0611: Unused import langsmith (unused-import)\nanubis\\scripts\\setup_langsmith.py:65:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:51:25: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\setup_langsmith.py:139:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:203:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:334:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:466:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:495:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\setup_langsmith.py:500:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\setup_langsmith.py:519:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\setup_langsmith.py:11:0: W0611: Unused import os (unused-import)\n************* Module anubis.scripts.simple_langsmith_test\nanubis\\scripts\\simple_langsmith_test.py:53:7: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\simple_langsmith_test.py:80:7: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\simple_langsmith_test.py:66:8: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\simple_langsmith_test.py:128:7: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\simple_langsmith_test.py:93:12: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\simple_langsmith_test.py:100:12: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\simple_langsmith_test.py:107:12: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\simple_langsmith_test.py:186:7: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\simple_langsmith_test.py:136:33: W0621: Redefining name 'agent_name' from outer scope (line 180) (redefined-outer-name)\nanubis\\scripts\\simple_langsmith_test.py:136:45: W0621: Redefining name 'operation' from outer scope (line 180) (redefined-outer-name)\nanubis\\scripts\\simple_langsmith_test.py:136:56: W0621: Redefining name 'data' from outer scope (line 180) (redefined-outer-name)\nanubis\\scripts\\simple_langsmith_test.py:138:8: C0415: Import outside toplevel (time) (import-outside-toplevel)\nanubis\\scripts\\simple_langsmith_test.py:204:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:205:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:208:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:209:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:210:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:211:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:212:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\simple_langsmith_test.py:214:6: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\n************* Module anubis.scripts.smart_workflow_demo\nanubis\\scripts\\smart_workflow_demo.py:66:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\smart_workflow_demo.py:94:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\smart_workflow_demo.py:146:31: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\smart_workflow_demo.py:178:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\smart_workflow_demo.py:69:4: R0915: Too many statements (58/50) (too-many-statements)\nanubis\\scripts\\smart_workflow_demo.py:76:13: W0612: Unused variable 'main_trace' (unused-variable)\nanubis\\scripts\\smart_workflow_demo.py:204:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\smart_workflow_demo.py:353:12: W0612: Unused variable 'file_path' (unused-variable)\nanubis\\scripts\\smart_workflow_demo.py:410:4: W0621: Redefining name 'orchestrator' from outer scope (line 436) (redefined-outer-name)\nanubis\\scripts\\smart_workflow_demo.py:413:4: W0612: Unused variable 'analysis_result' (unused-variable)\nanubis\\scripts\\smart_workflow_demo.py:416:4: W0612: Unused variable 'fixing_result' (unused-variable)\nanubis\\scripts\\smart_workflow_demo.py:438:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\smart_workflow_demo.py:10:0: W0611: Unused import json (unused-import)\nanubis\\scripts\\smart_workflow_demo.py:11:0: W0611: Unused import os (unused-import)\n************* Module anubis.scripts.start_anubis_n8n_system\nanubis\\scripts\\start_anubis_n8n_system.py:63:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\start_anubis_n8n_system.py:53:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\start_anubis_n8n_system.py:82:4: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\start_anubis_n8n_system.py:78:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\start_anubis_n8n_system.py:90:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\start_anubis_n8n_system.py:146:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\start_anubis_n8n_system.py:138:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\start_anubis_n8n_system.py:127:18: R1732: Consider using 'with' for resource-allocating operations (consider-using-with)\nanubis\\scripts\\start_anubis_n8n_system.py:142:12: W0612: Unused variable 'stdout' (unused-variable)\nanubis\\scripts\\start_anubis_n8n_system.py:173:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\start_anubis_n8n_system.py:163:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\start_anubis_n8n_system.py:156:18: R1732: Consider using 'with' for resource-allocating operations (consider-using-with)\nanubis\\scripts\\start_anubis_n8n_system.py:169:12: W0612: Unused variable 'stdout' (unused-variable)\nanubis\\scripts\\start_anubis_n8n_system.py:182:4: C0415: Import outside toplevel (requests) (import-outside-toplevel)\nanubis\\scripts\\start_anubis_n8n_system.py:225:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\start_anubis_n8n_system.py:269:4: W0612: Unused variable 'requirements' (unused-variable)\nanubis\\scripts\\start_anubis_n8n_system.py:297:4: W0612: Unused variable 'test_results' (unused-variable)\nanubis\\scripts\\start_anubis_n8n_system.py:300:4: W0612: Unused variable 'report' (unused-variable)\nanubis\\scripts\\start_anubis_n8n_system.py:12:0: W0611: Unused import threading (unused-import)\n************* Module anubis.scripts.test_langsmith_integration\nanubis\\scripts\\test_langsmith_integration.py:302:0: C0301: Line too long (101/100) (line-too-long)\nanubis\\scripts\\test_langsmith_integration.py:385:0: C0301: Line too long (107/100) (line-too-long)\nanubis\\scripts\\test_langsmith_integration.py:43:12: C0415: Import outside toplevel (langsmith) (import-outside-toplevel)\nanubis\\scripts\\test_langsmith_integration.py:54:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:49:16: C0415: Import outside toplevel (langsmith.Client) (import-outside-toplevel)\nanubis\\scripts\\test_langsmith_integration.py:43:12: W0611: Unused import langsmith (unused-import)\nanubis\\scripts\\test_langsmith_integration.py:51:16: W0612: Unused variable 'client' (unused-variable)\nanubis\\scripts\\test_langsmith_integration.py:83:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:127:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:179:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:222:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:265:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:346:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_langsmith_integration.py:351:8: W0612: Unused variable 'report_file' (unused-variable)\nanubis\\scripts\\test_langsmith_integration.py:383:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:392:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:396:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:397:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:398:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:406:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\test_langsmith_integration.py:404:4: W0612: Unused variable 'results' (unused-variable)\n************* Module anubis.scripts.test_ollama_langsmith\nanubis\\scripts\\test_ollama_langsmith.py:18:0: C0413: Import \"import time\" should be placed at the top of the module (wrong-import-position)\nanubis\\scripts\\test_ollama_langsmith.py:20:0: C0413: Import \"from langsmith import traceable\" should be placed at the top of the module (wrong-import-position)\nanubis\\scripts\\test_ollama_langsmith.py:22:0: C0413: Import \"from anubis.core.ai_integration import OllamaProvider\" should be placed at the top of the module (wrong-import-position)\nanubis\\scripts\\test_ollama_langsmith.py:61:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\test_ollama_langsmith.py:57:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\n************* Module anubis.scripts.vscode_emergency_cleanup\nanubis\\scripts\\vscode_emergency_cleanup.py:140:0: C0301: Line too long (109/100) (line-too-long)\nanubis\\scripts\\vscode_emergency_cleanup.py:145:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_emergency_cleanup.py:157:16: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\vscode_emergency_cleanup.py:194:12: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\vscode_emergency_cleanup.py:199:16: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\vscode_emergency_cleanup.py:233:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_emergency_cleanup.py:213:16: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\vscode_emergency_cleanup.py:215:16: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\vscode_emergency_cleanup.py:225:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\vscode_emergency_cleanup.py:270:12: W0702: No exception type(s) specified (bare-except)\nanubis\\scripts\\vscode_emergency_cleanup.py:297:8: C0415: Import outside toplevel (json) (import-outside-toplevel)\nanubis\\scripts\\vscode_emergency_cleanup.py:325:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_emergency_cleanup.py:330:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\scripts\\vscode_emergency_cleanup.py:331:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_emergency_cleanup.py:334:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_emergency_cleanup.py:305:4: R1710: Either all return statements in a function should return an expression, or none of them should. (inconsistent-return-statements)\nanubis\\scripts\\vscode_emergency_cleanup.py:347:11: W0718: Catching too general exception Exception (broad-exception-caught)\n************* Module anubis.scripts.vscode_heavy_load_analyzer\nanubis\\scripts\\vscode_heavy_load_analyzer.py:215:0: C0301: Line too long (109/100) (line-too-long)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:239:0: C0301: Line too long (102/100) (line-too-long)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:325:0: C0301: Line too long (109/100) (line-too-long)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:365:0: C0301: Line too long (115/100) (line-too-long)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:87:12: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:98:12: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:82:4: R0911: Too many return statements (14/6) (too-many-return-statements)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:82:4: R0912: Too many branches (16/12) (too-many-branches)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:312:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:318:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:328:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:403:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:389:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:12:0: W0611: Unused import subprocess (unused-import)\nanubis\\scripts\\vscode_heavy_load_analyzer.py:13:0: W0611: Unused import time (unused-import)\n************* Module anubis.scripts.vscode_process_alerts\nanubis\\scripts\\vscode_process_alerts.py:153:0: C0301: Line too long (105/100) (line-too-long)\nanubis\\scripts\\vscode_process_alerts.py:367:0: C0301: Line too long (102/100) (line-too-long)\nanubis\\scripts\\vscode_process_alerts.py:18:0: E0401: Unable to import 'vscode_process_monitor' (import-error)\nanubis\\scripts\\vscode_process_alerts.py:67:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_alerts.py:250:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_alerts.py:296:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_alerts.py:346:4: C0415: Import outside toplevel (argparse) (import-outside-toplevel)\nanubis\\scripts\\vscode_process_alerts.py:12:0: W0611: Unused import threading (unused-import)\n************* Module anubis.scripts.vscode_process_monitor\nanubis\\scripts\\vscode_process_monitor.py:334:0: C0301: Line too long (108/100) (line-too-long)\nanubis\\scripts\\vscode_process_monitor.py:68:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_monitor.py:69:12: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\vscode_process_monitor.py:142:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\vscode_process_monitor.py:174:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_monitor.py:158:21: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\scripts\\vscode_process_monitor.py:175:12: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\vscode_process_monitor.py:261:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\scripts\\vscode_process_monitor.py:279:8: W1203: Use lazy % formatting in logging functions (logging-fstring-interpolation)\nanubis\\scripts\\vscode_process_monitor.py:359:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\scripts\\vscode_process_monitor.py:354:8: W0612: Unused variable 'report' (unused-variable)\nanubis\\scripts\\vscode_process_monitor.py:15:0: W0611: Unused import time (unused-import)\n************* Module anubis.tests.ask_anubis\nanubis\\tests\\ask_anubis.py:72:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:47:12: R1723: Unnecessary \"elif\" after \"break\", remove the leading \"el\" from \"elif\" (no-else-break)\nanubis\\tests\\ask_anubis.py:76:0: R0914: Too many local variables (18/15) (too-many-locals)\nanubis\\tests\\ask_anubis.py:147:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:90:8: C0415: Import outside toplevel (anubis.core.assistant_system.UniversalAssistantSystem) (import-outside-toplevel)\nanubis\\tests\\ask_anubis.py:91:8: C0415: Import outside toplevel (anubis.core.config_manager.ConfigManager) (import-outside-toplevel)\nanubis\\tests\\ask_anubis.py:89:4: R1702: Too many nested blocks (6/5) (too-many-nested-blocks)\nanubis\\tests\\ask_anubis.py:188:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:161:8: E0401: Unable to import 'anubis.agents.error_detector_agent' (import-error)\nanubis\\tests\\ask_anubis.py:161:8: C0415: Import outside toplevel (anubis.agents.error_detector_agent.ErrorDetectorAgent) (import-outside-toplevel)\nanubis\\tests\\ask_anubis.py:161:8: E0611: No name 'error_detector_agent' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\ask_anubis.py:229:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:202:8: E0401: Unable to import 'anubis.agents.project_analyzer_agent' (import-error)\nanubis\\tests\\ask_anubis.py:202:8: C0415: Import outside toplevel (anubis.agents.project_analyzer_agent.ProjectAnalyzerAgent) (import-outside-toplevel)\nanubis\\tests\\ask_anubis.py:202:8: E0611: No name 'project_analyzer_agent' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\ask_anubis.py:290:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:253:8: E0401: Unable to import 'database_manager' (import-error)\nanubis\\tests\\ask_anubis.py:253:8: C0415: Import outside toplevel (database_manager.AnubisDatabaseManager) (import-outside-toplevel)\nanubis\\tests\\ask_anubis.py:304:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:312:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\ask_anubis.py:320:4: R1722: Consider using 'sys.exit' instead (consider-using-sys-exit)\nanubis\\tests\\ask_anubis.py:9:0: W0611: Unused import json (unused-import)\n************* Module anubis.tests.comprehensive_agents_test\nanubis\\tests\\comprehensive_agents_test.py:65:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:115:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:97:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\comprehensive_agents_test.py:154:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:130:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\comprehensive_agents_test.py:134:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:207:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:179:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\comprehensive_agents_test.py:183:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:194:16: C0415: Import outside toplevel (shutil) (import-outside-toplevel)\nanubis\\tests\\comprehensive_agents_test.py:272:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:229:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:234:16: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\comprehensive_agents_test.py:235:26: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:258:26: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:265:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:304:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:287:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\comprehensive_agents_test.py:288:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:293:16: C0415: Import outside toplevel (shutil) (import-outside-toplevel)\nanubis\\tests\\comprehensive_agents_test.py:326:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:364:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_agents_test.py:377:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:382:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:390:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:391:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_agents_test.py:11:0: W0611: Unused import os (unused-import)\n************* Module anubis.tests.comprehensive_system_test\nanubis\\tests\\comprehensive_system_test.py:241:0: C0301: Line too long (117/100) (line-too-long)\nanubis\\tests\\comprehensive_system_test.py:395:0: C0301: Line too long (113/100) (line-too-long)\nanubis\\tests\\comprehensive_system_test.py:398:0: C0301: Line too long (115/100) (line-too-long)\nanubis\\tests\\comprehensive_system_test.py:401:0: C0301: Line too long (113/100) (line-too-long)\nanubis\\tests\\comprehensive_system_test.py:22:0: R0914: Too many local variables (19/15) (too-many-locals)\nanubis\\tests\\comprehensive_system_test.py:62:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:32:8: E0401: Unable to import 'anubis.agents.error_detector' (import-error)\nanubis\\tests\\comprehensive_system_test.py:32:8: C0415: Import outside toplevel (anubis.agents.error_detector.ErrorDetectorAgent) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:32:8: E0611: No name 'error_detector' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:92:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:73:8: E0401: Unable to import 'anubis.agents.project_analyzer' (import-error)\nanubis\\tests\\comprehensive_system_test.py:73:8: C0415: Import outside toplevel (anubis.agents.project_analyzer.ProjectAnalyzerAgent) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:73:8: E0611: No name 'project_analyzer' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:122:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:103:8: E0401: Unable to import 'anubis.agents.file_organizer' (import-error)\nanubis\\tests\\comprehensive_system_test.py:103:8: C0415: Import outside toplevel (anubis.agents.file_organizer.FileOrganizerAgent) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:103:8: E0611: No name 'file_organizer' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:154:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:133:8: E0401: Unable to import 'anubis.agents.memory_agent' (import-error)\nanubis\\tests\\comprehensive_system_test.py:133:8: C0415: Import outside toplevel (anubis.agents.memory_agent.MemoryAgent) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:133:8: E0611: No name 'memory_agent' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:193:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:165:8: E0401: Unable to import 'anubis.agents.smart_code_analyzer' (import-error)\nanubis\\tests\\comprehensive_system_test.py:165:8: C0415: Import outside toplevel (anubis.agents.smart_code_analyzer.SmartCodeAnalyzer) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:165:8: E0611: No name 'smart_code_analyzer' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:22:0: R0915: Too many statements (70/50) (too-many-statements)\nanubis\\tests\\comprehensive_system_test.py:257:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:209:8: C0415: Import outside toplevel (anubis.core.ai_integration.AIIntegrationManager, anubis.core.ai_integration.OllamaProvider) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:247:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:245:26: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_system_test.py:209:8: W0611: Unused AIIntegrationManager imported from anubis.core.ai_integration (unused-import)\nanubis\\tests\\comprehensive_system_test.py:301:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:270:8: E0401: Unable to import 'anubis.agents.smart_code_analyzer' (import-error)\nanubis\\tests\\comprehensive_system_test.py:270:8: C0415: Import outside toplevel (anubis.agents.smart_code_analyzer.SmartCodeAnalyzer) (import-outside-toplevel)\nanubis\\tests\\comprehensive_system_test.py:270:8: E0611: No name 'smart_code_analyzer' in module 'anubis.agents' (no-name-in-module)\nanubis\\tests\\comprehensive_system_test.py:368:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\comprehensive_system_test.py:409:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\comprehensive_system_test.py:412:4: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\tests\\comprehensive_system_test.py:15:0: W0611: Unused Path imported from pathlib (unused-import)\n************* Module anubis.tests.quick_ai_test\nanubis\\tests\\quick_ai_test.py:44:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\quick_ai_test.py:23:8: C0415: Import outside toplevel (anubis.core.ai_integration.OllamaProvider) (import-outside-toplevel)\nanubis\\tests\\quick_ai_test.py:34:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\quick_ai_test.py:78:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\quick_ai_test.py:54:8: C0415: Import outside toplevel (anubis.core.ai_integration.AIIntegrationManager) (import-outside-toplevel)\nanubis\\tests\\quick_ai_test.py:68:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\quick_ai_test.py:59:8: W0612: Unused variable 'status' (unused-variable)\nanubis\\tests\\quick_ai_test.py:119:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\quick_ai_test.py:88:8: C0415: Import outside toplevel (anubis.core.base_agent.BaseAgent) (import-outside-toplevel)\nanubis\\tests\\quick_ai_test.py:109:12: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\quick_ai_test.py:142:4: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\tests\\quick_ai_test.py:10:0: W0611: Unused import json (unused-import)\n************* Module anubis.tests.run_all_tests\nanubis\\tests\\run_all_tests.py:20:0: C0413: Import \"import os\" should be placed at the top of the module (wrong-import-position)\nanubis\\tests\\run_all_tests.py:64:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\run_all_tests.py:44:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\tests\\run_all_tests.py:53:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\run_all_tests.py:153:12: W0621: Redefining name 'success' from outer scope (line 319) (redefined-outer-name)\nanubis\\tests\\run_all_tests.py:153:31: W0612: Unused variable 'output' (unused-variable)\nanubis\\tests\\run_all_tests.py:171:12: W0621: Redefining name 'success' from outer scope (line 319) (redefined-outer-name)\nanubis\\tests\\run_all_tests.py:171:31: W0612: Unused variable 'output' (unused-variable)\nanubis\\tests\\run_all_tests.py:189:12: W0621: Redefining name 'success' from outer scope (line 319) (redefined-outer-name)\nanubis\\tests\\run_all_tests.py:189:31: W0612: Unused variable 'output' (unused-variable)\nanubis\\tests\\run_all_tests.py:218:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\run_all_tests.py:204:17: W1510: 'subprocess.run' used without explicitly defining the value for 'check'. (subprocess-run-check)\nanubis\\tests\\run_all_tests.py:211:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\run_all_tests.py:241:23: W0621: Redefining name 'success' from outer scope (line 319) (redefined-outer-name)\nanubis\\tests\\run_all_tests.py:252:16: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\run_all_tests.py:305:4: W0621: Redefining name 'success' from outer scope (line 319) (redefined-outer-name)\nanubis\\tests\\run_all_tests.py:310:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\n************* Module anubis.tests.test_agents\nanubis\\tests\\test_agents.py:24:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_agents.py:20:8: C0415: Import outside toplevel (anubis.core.base_agent.BaseAgent) (import-outside-toplevel)\nanubis\\tests\\test_agents.py:20:8: W0611: Unused BaseAgent imported from anubis.core.base_agent (unused-import)\nanubis\\tests\\test_agents.py:68:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_agents.py:63:23: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_agents.py:70:12: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\n************* Module anubis.tests.test_ai_fixed\nanubis\\tests\\test_ai_fixed.py:321:0: C0301: Line too long (114/100) (line-too-long)\nanubis\\tests\\test_ai_fixed.py:324:0: C0301: Line too long (108/100) (line-too-long)\nanubis\\tests\\test_ai_fixed.py:327:0: C0301: Line too long (134/100) (line-too-long)\nanubis\\tests\\test_ai_fixed.py:36:8: W0201: Attribute 'test_results' defined outside __init__ (attribute-defined-outside-init)\nanubis\\tests\\test_ai_fixed.py:90:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_fixed.py:142:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_fixed.py:114:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_ai_fixed.py:191:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_fixed.py:159:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_ai_fixed.py:242:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_fixed.py:289:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_fixed.py:14:0: W0611: Unused Path imported from pathlib (unused-import)\n************* Module anubis.tests.test_ai_integration\nanubis\\tests\\test_ai_integration.py:194:0: C0301: Line too long (102/100) (line-too-long)\nanubis\\tests\\test_ai_integration.py:331:0: C0301: Line too long (114/100) (line-too-long)\nanubis\\tests\\test_ai_integration.py:334:0: C0301: Line too long (108/100) (line-too-long)\nanubis\\tests\\test_ai_integration.py:337:0: C0301: Line too long (134/100) (line-too-long)\nanubis\\tests\\test_ai_integration.py:35:8: W0201: Attribute 'test_results' defined outside __init__ (attribute-defined-outside-init)\nanubis\\tests\\test_ai_integration.py:84:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_integration.py:129:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_integration.py:108:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_ai_integration.py:178:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_integration.py:146:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_ai_integration.py:242:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_integration.py:299:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_ai_integration.py:14:0: W0611: Unused Path imported from pathlib (unused-import)\n************* Module anubis.tests.test_anubis_system\nanubis\\tests\\test_anubis_system.py:242:0: C0301: Line too long (109/100) (line-too-long)\nanubis\\tests\\test_anubis_system.py:129:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_anubis_system.py:27:8: C0415: Import outside toplevel (database_manager.AnubisDatabaseManager) (import-outside-toplevel)\nanubis\\tests\\test_anubis_system.py:35:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_anubis_system.py:131:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\tests\\test_anubis_system.py:137:0: R0914: Too many local variables (17/15) (too-many-locals)\nanubis\\tests\\test_anubis_system.py:208:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_anubis_system.py:145:8: C0415: Import outside toplevel (anubis.core.assistant_system.UniversalAssistantSystem) (import-outside-toplevel)\nanubis\\tests\\test_anubis_system.py:146:8: C0415: Import outside toplevel (anubis.core.config_manager.ConfigManager) (import-outside-toplevel)\nanubis\\tests\\test_anubis_system.py:168:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_anubis_system.py:173:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_anubis_system.py:180:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_anubis_system.py:144:4: R1702: Too many nested blocks (6/5) (too-many-nested-blocks)\nanubis\\tests\\test_anubis_system.py:195:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_anubis_system.py:210:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\tests\\test_anubis_system.py:265:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_anubis_system.py:274:4: R1722: Consider using 'sys.exit' instead (consider-using-sys-exit)\nanubis\\tests\\test_anubis_system.py:9:0: W0611: Unused import json (unused-import)\n************* Module anubis.tests.test_enhanced_error_detector\nanubis\\tests\\test_enhanced_error_detector.py:40:0: C0301: Line too long (109/100) (line-too-long)\nanubis\\tests\\test_enhanced_error_detector.py:83:0: C0301: Line too long (103/100) (line-too-long)\nanubis\\tests\\test_enhanced_error_detector.py:88:0: C0301: Line too long (101/100) (line-too-long)\nanubis\\tests\\test_enhanced_error_detector.py:142:0: C0301: Line too long (113/100) (line-too-long)\nanubis\\tests\\test_enhanced_error_detector.py:147:0: C0301: Line too long (125/100) (line-too-long)\nanubis\\tests\\test_enhanced_error_detector.py:43:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_enhanced_error_detector.py:73:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_enhanced_error_detector.py:134:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_enhanced_error_detector.py:175:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_enhanced_error_detector.py:181:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_enhanced_error_detector.py:26:0: R0915: Too many statements (67/50) (too-many-statements)\nanubis\\tests\\test_enhanced_error_detector.py:215:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_enhanced_error_detector.py:238:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_enhanced_error_detector.py:256:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_enhanced_error_detector.py:247:8: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\n************* Module anubis.tests.test_error_detector\nanubis\\tests\\test_error_detector.py:325:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\n************* Module anubis.tests.test_fixed_agents\nanubis\\tests\\test_fixed_agents.py:24:7: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_fixed_agents.py:8:4: E0401: Unable to import 'enhanced_file_organizer' (import-error)\nanubis\\tests\\test_fixed_agents.py:9:4: E0401: Unable to import 'enhanced_memory_agent' (import-error)\nanubis\\tests\\test_fixed_agents.py:10:4: E0401: Unable to import 'enhanced_project_analyzer' (import-error)\n************* Module anubis.tests.test_jewelry_database\nanubis\\tests\\test_jewelry_database.py:36:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_jewelry_database.py:45:21: E0601: Using variable 'SimpleDatabaseManager' before assignment (used-before-assignment)\nanubis\\tests\\test_jewelry_database.py:93:0: R1710: Either all return statements in a function should return an expression, or none of them should. (inconsistent-return-statements)\nanubis\\tests\\test_jewelry_database.py:250:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_jewelry_database.py:276:19: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_jewelry_database.py:309:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_jewelry_database.py:311:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\tests\\test_jewelry_database.py:11:0: W0611: Unused datetime imported from datetime (unused-import)\nanubis\\tests\\test_jewelry_database.py:15:4: W0611: Unused ExchangeRate imported from jewelry_database_models (unused-import)\nanubis\\tests\\test_jewelry_database.py:15:4: W0611: Unused GoldPrice imported from jewelry_database_models (unused-import)\n************* Module anubis.tests.test_jewelry_logic\nanubis\\tests\\test_jewelry_logic.py:269:0: C0301: Line too long (101/100) (line-too-long)\nanubis\\tests\\test_jewelry_logic.py:12:0: E0401: Unable to import 'jewelry_workshop_business_logic' (import-error)\nanubis\\tests\\test_jewelry_logic.py:53:11: C0121: Comparison 'gold_manager.add_gold_stock(100.0, 18) == True' should be 'gold_manager.add_gold_stock(100.0, 18) is True' if checking for the singleton value True, or 'gold_manager.add_gold_stock(100.0, 18)' if testing for truthiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:54:11: C0121: Comparison 'gold_manager.add_gold_stock(50.0, 21) == True' should be 'gold_manager.add_gold_stock(50.0, 21) is True' if checking for the singleton value True, or 'gold_manager.add_gold_stock(50.0, 21)' if testing for truthiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:62:11: C0121: Comparison 'gold_manager.remove_gold_stock(20.0, 18) == True' should be 'gold_manager.remove_gold_stock(20.0, 18) is True' if checking for the singleton value True, or 'gold_manager.remove_gold_stock(20.0, 18)' if testing for truthiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:66:11: C0121: Comparison 'gold_manager.remove_gold_stock(100.0, 18) == False' should be 'gold_manager.remove_gold_stock(100.0, 18) is False' if checking for the singleton value False, or 'not gold_manager.remove_gold_stock(100.0, 18)' if testing for falsiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:84:8: C0121: Comparison 'diamond_manager.add_diamond_stock(weight=2.5, count=25, size='medium', diamond_type='round', source='ours', price_per_carat=700.0) == True' should be 'diamond_manager.add_diamond_stock(weight=2.5, count=25, size='medium', diamond_type='round', source='ours', price_per_carat=700.0) is True' if checking for the singleton value True, or 'diamond_manager.add_diamond_stock(weight=2.5, count=25, size='medium', diamond_type='round', source='ours', price_per_carat=700.0)' if testing for truthiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:97:8: C0121: Comparison 'diamond_manager.remove_diamond_stock(weight=0.5, count=5, size='medium', diamond_type='round', source='ours') == True' should be 'diamond_manager.remove_diamond_stock(weight=0.5, count=5, size='medium', diamond_type='round', source='ours') is True' if checking for the singleton value True, or 'diamond_manager.remove_diamond_stock(weight=0.5, count=5, size='medium', diamond_type='round', source='ours')' if testing for truthiness (singleton-comparison)\nanubis\\tests\\test_jewelry_logic.py:265:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_jewelry_logic.py:272:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_jewelry_logic.py:279:10: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_jewelry_logic.py:316:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_jewelry_logic.py:318:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\n************* Module anubis.tests.test_plugins\nanubis\\tests\\test_plugins.py:43:4: W0221: Variadics removed in overriding 'TestPlugin.execute' method (arguments-differ)\nanubis\\tests\\test_plugins.py:44:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\tests\\test_plugins.py:52:12: W0719: Raising too general exception: Exception (broad-exception-raised)\nanubis\\tests\\test_plugins.py:41:8: W0201: Attribute 'test_value' defined outside __init__ (attribute-defined-outside-init)\nanubis\\tests\\test_plugins.py:369:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\n************* Module anubis.tests.test_project_analyzer\nanubis\\tests\\test_project_analyzer.py:420:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\n************* Module anubis.tests.test_smart_analyzer\nanubis\\tests\\test_smart_analyzer.py:35:0: C0301: Line too long (101/100) (line-too-long)\nanubis\\tests\\test_smart_analyzer.py:54:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:62:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:71:22: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:77:26: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:95:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:104:18: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_smart_analyzer.py:26:0: R0912: Too many branches (14/12) (too-many-branches)\nanubis\\tests\\test_smart_analyzer.py:26:0: R0915: Too many statements (64/50) (too-many-statements)\nanubis\\tests\\test_smart_analyzer.py:155:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_smart_analyzer.py:185:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_smart_analyzer.py:174:8: R1705: Unnecessary \"elif\" after \"return\", remove the leading \"el\" from \"elif\" (no-else-return)\nanubis\\tests\\test_smart_analyzer.py:168:8: W0612: Unused variable 'report' (unused-variable)\n************* Module anubis.tests.test_system\nanubis\\tests\\test_system.py:49:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:43:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_system.py:72:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:120:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:98:12: C0415: Import outside toplevel (database_agent.DatabaseAgent) (import-outside-toplevel)\nanubis\\tests\\test_system.py:105:12: C0415: Import outside toplevel (file_organizer_agent.FileOrganizerAgent) (import-outside-toplevel)\nanubis\\tests\\test_system.py:112:12: C0415: Import outside toplevel (memory_agent.MemoryAgent) (import-outside-toplevel)\nanubis\\tests\\test_system.py:98:12: W0611: Unused DatabaseAgent imported from database_agent (unused-import)\nanubis\\tests\\test_system.py:105:12: W0611: Unused FileOrganizerAgent imported from file_organizer_agent (unused-import)\nanubis\\tests\\test_system.py:112:12: W0611: Unused MemoryAgent imported from memory_agent (unused-import)\nanubis\\tests\\test_system.py:180:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:167:14: W1309: Using an f-string that does not have any interpolated variables (f-string-without-interpolation)\nanubis\\tests\\test_system.py:182:8: C0415: Import outside toplevel (traceback) (import-outside-toplevel)\nanubis\\tests\\test_system.py:225:11: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:261:15: W0718: Catching too general exception Exception (broad-exception-caught)\nanubis\\tests\\test_system.py:279:4: R1705: Unnecessary \"else\" after \"return\", remove the \"else\" and de-indent the code inside it (no-else-return)\nanubis\\tests\\test_system.py:9:0: W0611: Unused import json (unused-import)\n************* Module anubis.tests.__init__\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.core.assistant_system:[127:165]\n==anubis.core.base_agent:[227:265]\n        files = list(self.project_path.glob(\"*.py\"))\n\n        # البحث عن مؤشرات نوع المشروع\n        for file in files:\n            try:\n                content = file.read_text(encoding=\"utf-8\")\n\n                if \"streamlit\" in content.lower():\n                    return \"streamlit\"\n                elif \"django\" in content.lower():\n                    return \"django\"\n                elif \"fastapi\" in content.lower():\n                    return \"fastapi\"\n                elif \"flask\" in content.lower():\n                    return \"flask\"\n\n            except Exception:\n                continue\n\n        # فحص ملفات التكوين\n        if (self.project_path / \"manage.py\").exists():\n            return \"django\"\n        elif (self.project_path / \"requirements.txt\").exists():\n            try:\n                req_content = (self.project_path / \"requirements.txt\").read_text()\n                if \"streamlit\" in req_content:\n                    return \"streamlit\"\n                elif \"fastapi\" in req_content:\n                    return \"fastapi\"\n                elif \"flask\" in req_content:\n                    return \"flask\"\n            except Exception:\n                pass\n\n        return \"custom\"\n\n    def get_project_info(self) -> Dict[str, Any]:\n        \"\"\"الحصول على معلومات المشروع\"\"\" (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.run_vscode_monitor:[74:97]\n==anubis.scripts.vscode_process_alerts:[31:54]\n        \"thresholds\": {\n            \"cpu_warning\": 70,\n            \"cpu_critical\": 90,\n            \"memory_warning\": 70,\n            \"memory_critical\": 90,\n            \"vscode_memory_warning\": 20,\n            \"vscode_memory_critical\": 40,\n        },\n        \"monitoring\": {\n            \"interval\": 60,\n            \"alert_cooldown\": 300,\n            \"max_alerts_per_hour\": 10,\n        },\n        \"notifications\": {\"console\": True, \"file\": True, \"email\": False},\n        \"email\": {\n            \"smtp_server\": \"smtp.gmail.com\",\n            \"smtp_port\": 587,\n            \"sender_email\": \"\",\n            \"sender_password\": \"\",\n            \"recipient_email\": \"\",\n        },\n    }\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[254:270]\n==anubis.tests.test_ai_integration:[253:269]\n        \"test_summary\": {\n            \"ollama_models_tested\": len(ollama_results),\n            \"working_models\": len([r for r in ollama_results.values() if r.get(\"working\", False)]),\n            \"ai_manager_working\": manager_results.get(\"manager_working\", False),\n            \"agent_integration_working\": agent_results.get(\"integration_successful\", False),\n            \"use_cases_tested\": len(use_case_results),\n            \"successful_use_cases\": len(\n                [r for r in use_case_results.values() if r.get(\"success\", False)]\n            ),\n        },\n        \"detailed_results\": {\n            \"ollama_providers\": ollama_results,\n            \"ai_manager\": manager_results,\n            \"agent_integration\": agent_results,\n            \"use_cases\": use_case_results,\n        }, (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.core.assistant_system:[181:199]\n==anubis.tests.test_agents:[38:55]\n            spec = importlib.util.spec_from_file_location(agent_name, agent_file)\n            module = importlib.util.module_from_spec(spec)\n            spec.loader.exec_module(module)\n\n            # البحث عن فئة الوكيل\n            agent_class = None\n            for attr_name in dir(module):\n                attr = getattr(module, attr_name)\n                if (\n                    isinstance(attr, type)\n                    and attr_name.endswith(\"Agent\")\n                    and attr_name != \"BaseAgent\"\n                ):\n                    agent_class = attr\n                    break\n\n            if agent_class: (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[183:201]\n==anubis.tests.test_ai_integration:[170:188]\n                \"suggestions_count\": len(suggestions),\n                \"integration_successful\": True,\n            }\n        else:\n            print(\"   ⚠️ الذكاء الاصطناعي غير مفعل في الوكيل\")\n            return {\"agent_ai_enabled\": False, \"integration_successful\": False}\n\n    except Exception as e:\n        print(f\"   ❌ خطأ في اختبار دمج الوكيل: {e}\")\n        return {\n            \"agent_ai_enabled\": False,\n            \"integration_successful\": False,\n            \"error\": str(e),\n        }\n\n\ndef test_simple_use_cases():\n    \"\"\"اختبار حالات استخدام بسيطة\"\"\" (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_error_detector:[320:336]\n==anubis.tests.test_project_analyzer:[415:431]\n    runner = unittest.TextTestRunner(verbosity=2)\n    result = runner.run(test_suite)\n\n    # عرض النتائج\n    if result.wasSuccessful():\n        print(\"✅ جميع الاختبارات نجحت!\")\n        return True\n    else:\n        print(f\"❌ فشل {len(result.failures)} اختبار\")\n        print(f\"⚠️ خطأ في {len(result.errors)} اختبار\")\n        return False\n\n\nif __name__ == \"__main__\":\n    success = run_tests()\n    sys.exit(0 if success else 1) (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[131:148]\n==anubis.tests.test_ai_integration:[118:135]\n                \"response_length\": len(response),\n            }\n        else:\n            print(\"   ⚠️ لا توجد موفرين متاحين\")\n            return {\n                \"manager_working\": True,\n                \"available_providers\": [],\n                \"response_test\": False,\n            }\n\n    except Exception as e:\n        print(f\"   ❌ خطأ في مدير الذكاء الاصطناعي: {e}\")\n        return {\"manager_working\": False, \"error\": str(e)}\n\n\ndef test_agent_ai_integration():\n    \"\"\"اختبار دمج الذكاء الاصطناعي مع الوكلاء\"\"\" (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_error_detector:[264:284]\n==anubis.tests.test_project_analyzer:[344:364]\n        result = self.agent.run()\n\n        # التحقق من وجود التوصيات\n        self.assertTrue(result[\"success\"])\n        self.assertIn(\"recommendations\", result)\n\n        recommendations = result[\"recommendations\"]\n        self.assertGreater(len(recommendations), 0)\n\n        # التحقق من بنية التوصية\n        recommendation = recommendations[0]\n        self.assertIn(\"category\", recommendation)\n        self.assertIn(\"priority\", recommendation)\n        self.assertIn(\"title\", recommendation)\n        self.assertIn(\"description\", recommendation)\n        self.assertIn(\"action\", recommendation)\n\n    def test_summary_creation(self):\n        \"\"\"اختبار إنشاء الملخص\"\"\"\n        # إنشاء ملف بأخطاء متنوعة (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[317:329]\n==anubis.tests.test_ai_integration:[327:339]\n    print(f\"   🤖 نماذج Ollama المختبرة: {report['test_summary']['ollama_models_tested']}\")\n    print(f\"   ✅ النماذج التي تعمل: {report['test_summary']['working_models']}\")\n    print(\n        f\"   🧠 مدير الذكاء الاصطناعي: {'✅ يعمل' if report['test_summary']['ai_manager_working'] else '❌ لا يعمل'}\"\n    )\n    print(\n        f\"   🔗 دمج الوكلاء: {'✅ ناجح' if report['test_summary']['agent_integration_working'] else '❌ فاشل'}\"\n    )\n    print(\n        f\"   🎯 حالات الاستخدام الناجحة: {report['test_summary']['successful_use_cases']}/{report['test_summary']['use_cases_tested']}\"\n    )\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[167:182]\n==anubis.tests.test_ai_integration:[154:169]\n            print(f\"   📤 اختبار التحليل: {analysis_prompt}\")\n\n            analysis = agent.get_ai_analysis(analysis_prompt, test_context)\n            print(f\"   📥 التحليل: {analysis[:150]}...\")\n\n            # اختبار الاقتراحات الذكية\n            test_data = {\"code_quality\": 75, \"test_coverage\": 60, \"documentation\": 40}\n\n            suggestions = agent.get_smart_suggestions(test_data)\n            print(f\"   💡 الاقتراحات ({len(suggestions)}):\")\n            for i, suggestion in enumerate(suggestions[:3], 1):\n                print(f\"      {i}. {suggestion}\")\n\n            return {\n                \"agent_ai_enabled\": True, (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[77:90]\n==anubis.scripts.vscode_process_monitor:[89:102]\n                }\n        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):\n            return None\n\n    def find_vscode_processes(self) -> List[Dict]:\n        \"\"\"البحث عن عمليات VS Code\"\"\"\n        vscode_keywords = [\n            \"code\",\n            \"Code.exe\",\n            \"electron\",\n            \"extensionHost\",\n            \"Microsoft VS Code\",\n            \"Visual Studio Code\", (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[46:57]\n==anubis.scripts.vscode_process_monitor:[54:65]\n            disk = psutil.disk_usage(\"/\")\n\n            return {\n                \"timestamp\": datetime.now().isoformat(),\n                \"cpu_usage\": cpu_percent,\n                \"memory_total\": memory.total,\n                \"memory_used\": memory.used,\n                \"memory_percent\": memory.percent,\n                \"disk_total\": disk.total,\n                \"disk_used\": disk.used,\n                \"disk_percent\": (disk.used / disk.total) * 100, (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[237:253]\n==anubis.tests.test_ai_integration:[237:253]\n            else:\n                results[test_case[\"name\"]] = {\"success\": False, \"error\": response}\n                print(f\"   ❌ فشل: {response}\")\n\n    except Exception as e:\n        print(f\"   ❌ خطأ في اختبار حالات الاستخدام: {e}\")\n        results[\"error\"] = str(e)\n\n    return results\n\n\ndef generate_fixed_report(ollama_results, manager_results, agent_results, use_case_results):\n    \"\"\"إنتاج تقرير مُصحح\"\"\"\n\n    report = {\n        \"timestamp\": datetime.now().isoformat(), (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[52:67]\n==anubis.tests.test_ai_integration:[43:59]\n    models_to_test = [\"llama3:8b\", \"mistral:7b\", \"phi3:mini\"]\n    results = {}\n\n    for model in models_to_test:\n        print(f\"\\n   🔍 اختبار النموذج: {model}\")\n\n        try:\n            provider = OllamaProvider(model_name=model)\n\n            # فحص التوفر\n            is_available = provider.is_available()\n            print(f\"   📡 متاح: {'✅ نعم' if is_available else '❌ لا'}\")\n\n            if is_available:\n                # اختبار بسيط (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.complete_file_organizer:[185:194]\n==anubis.scripts.organize_all_files:[239:248]\n            \"core\",\n            \"agents\",\n            \"tests\",\n            \"scripts\",\n            \"docs\",\n            \"configs\",\n            \"reports\",\n            \"logs\",\n            \"backup\", (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.complete_file_organizer:[188:197]\n==anubis.scripts.create_all_readmes:[21:30]\n                \"scripts\",\n                \"docs\",\n                \"configs\",\n                \"reports\",\n                \"logs\",\n                \"backup\",\n                \"temp\",\n                \"examples\",\n                \"tools\", (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_error_detector:[326:336]\n==anubis.tests.test_plugins:[370:380]\n        return True\n    else:\n        print(f\"❌ فشل {len(result.failures)} اختبار\")\n        print(f\"⚠️ خطأ في {len(result.errors)} اختبار\")\n        return False\n\n\nif __name__ == \"__main__\":\n    success = run_tests()\n    sys.exit(0 if success else 1) (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[155:166]\n==anubis.tests.test_ai_integration:[142:153]\n        ai_status = agent.get_ai_status()\n        print(f\"   📊 حالة الذكاء الاصطناعي في الوكيل: {ai_status['available']}\")\n\n        if agent.is_ai_enabled():\n            # اختبار التحليل الذكي\n            test_context = {\n                \"project_type\": \"Python\",\n                \"files_count\": 50,\n                \"main_language\": \"Python\",\n            }\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[81:90]\n==anubis.tests.test_ai_integration:[75:84]\n                    results[model] = {\n                        \"available\": True,\n                        \"working\": False,\n                        \"error\": response,\n                    }\n            else:\n                results[model] = {\"available\": False, \"working\": False}\n\n        except Exception as e: (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[68:78]\n==anubis.tests.test_ai_integration:[61:71]\n                print(f\"   📤 إرسال: {test_prompt}\")\n\n                response = provider.generate_response(test_prompt)\n\n                if response and not response.startswith(\"خطأ\"):\n                    print(f\"   📥 الاستجابة: {response[:100]}...\")\n                    results[model] = {\n                        \"available\": True,\n                        \"working\": True,\n                        \"response_length\": len(response), (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.gemini_cli_helper:[37:48]\n==anubis.scripts.gemini_integration_system:[38:49]\n        try:\n            result = subprocess.run(\n                [\"gemini\", \"--version\"], capture_output=True, text=True, timeout=10\n            )\n            return result.returncode == 0\n        except (subprocess.TimeoutExpired, FileNotFoundError):\n            return False\n\n    def ask_gemini_for_agent_fix(self, agent_name: str, current_issues: str) -> str:\n        \"\"\"طلب مساعدة Gemini لإصلاح وكيل محدد\"\"\"\n        if not self.gemini_available: (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_integration:[293:306]\n==anubis.tests.test_smart_analyzer:[149:162]\n    try:\n        with open(report_file, \"w\", encoding=\"utf-8\") as f:\n            json.dump(report, f, ensure_ascii=False, indent=2)\n\n        print(f\"\\n📄 تم حفظ تقرير الاختبار في: {report_file}\")\n    except Exception as e:\n        print(f\"\\n❌ خطأ في حفظ التقرير: {e}\")\n\n    return report\n\n\ndef main():\n    \"\"\"الدالة الرئيسية\"\"\" (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.ask_anubis:[122:130]\n==anubis.tests.test_anubis_system:[180:188]\n            for agent_name, result in agents_results.items():\n                status = \"✅ نجح\" if result.get(\"success\", False) else \"❌ فشل\"\n                print(f\"   {agent_name}: {status}\")\n\n                if result.get(\"success\", False) and \"analysis\" in result:\n                    analysis = result[\"analysis\"]\n                    if \"summary\" in analysis:\n                        agent_summary = analysis[\"summary\"] (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[90:101]\n==anubis.scripts.vscode_process_monitor:[105:116]\n        ]\n\n        vscode_procs = []\n\n        for proc in psutil.process_iter([\"pid\", \"name\", \"cmdline\"]):\n            try:\n                proc_info = proc.info\n                proc_name = proc_info[\"name\"].lower()\n                cmdline = \" \".join(proc_info[\"cmdline\"] or []).lower()\n\n                # التحقق من كلمات VS Code المفتاحية (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[36:46]\n==anubis.scripts.vscode_process_monitor:[39:51]\n                logging.StreamHandler(),\n            ],\n        )\n        self.logger = logging.getLogger(__name__)\n\n    def get_system_stats(self) -> Dict:\n        \"\"\"الحصول على إحصائيات النظام العامة\"\"\"\n        try:\n            cpu_percent = psutil.cpu_percent(interval=1)\n            memory = psutil.virtual_memory()\n\n            # تحديد مسار القرص حسب نظام التشغيل (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.core.langsmith_wrapper:[32:39]\n==anubis.scripts.setup_langsmith:[241:248]\n            try:\n                self.client = Client()\n                print(\"✅ تم تهيئة LangSmith Client\")\n            except Exception as e:\n                print(f\"⚠️ فشل تهيئة LangSmith: {e}\")\n                self.client = None\n        else: (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_jewelry_database:[312:321]\n==anubis.tests.test_jewelry_logic:[319:328]\n        traceback.print_exc()\n        return 1\n\n    return 0\n\n\nif __name__ == \"__main__\":\n    exit_code = main()\n    sys.exit(exit_code) (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_error_detector:[246:257]\n==anubis.tests.test_project_analyzer:[70:81]\n        }\n\n        for filename, content in files_data.items():\n            test_file = self.test_project / filename\n            test_file.write_text(content)\n\n        # تشغيل التحليل\n        result = self.agent.run()\n\n        # التحقق من النتائج\n        self.assertTrue(result[\"success\"]) (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[122:130]\n==anubis.tests.test_ai_integration:[109:117]\n            print(f\"   📤 اختبار التوليد: {test_prompt}\")\n\n            response = ai_manager.generate_ai_response(test_prompt)\n            print(f\"   📥 الاستجابة: {response[:150]}...\")\n\n            return {\n                \"manager_working\": True,\n                \"available_providers\": available_providers, (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.tests.test_ai_fixed:[16:27]\n==anubis.tests.test_ai_integration:[16:27]\nsys.path.append(os.path.join(os.path.dirname(__file__), \"core\"))\nsys.path.append(os.path.join(os.path.dirname(__file__), \"agents\"))\n\ntry:\n    from anubis.core.ai_integration import AIIntegrationManager, OllamaProvider\n    from anubis.core.base_agent import BaseAgent\nexcept ImportError as e:\n    print(f\"❌ خطأ في استيراد الوحدات: {e}\")\n    sys.exit(1)\n\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[66:72]\n==anubis.scripts.vscode_process_monitor:[76:82]\n                return {\n                    \"pid\": process.pid,\n                    \"name\": process.name(),\n                    \"status\": process.status(),\n                    \"cpu_percent\": process.cpu_percent(),\n                    \"memory_percent\": process.memory_percent(), (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[57:66]\n==anubis.scripts.vscode_process_monitor:[66:75]\n            }\n        except Exception as e:\n            self.logger.error(f\"Error getting system stats: {e}\")\n            return {}\n\n    def get_process_info(self, process) -> Optional[Dict]:\n        \"\"\"الحصول على معلومات العملية\"\"\"\n        try:\n            with process.oneshot(): (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.process_monitor:[28:35]\n==anubis.scripts.vscode_process_monitor:[31:38]\n        log_dir = \"Universal-AI-Assistants/logs\"\n        os.makedirs(log_dir, exist_ok=True)\n\n        logging.basicConfig(\n            level=logging.INFO,\n            format=\"%(asctime)s - %(levelname)s - %(message)s\",\n            handlers=[ (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.create_all_readmes:[269:276]\n==anubis.scripts.organize_all_files:[222:233]\n        try:\n            import json\n\n            with open(report_path, \"w\", encoding=\"utf-8\") as f:\n                json.dump(report, f, ensure_ascii=False, indent=2)\n            print(f\"   ✅ تم حفظ التقرير: {report_path}\")\n        except Exception as e:\n            print(f\"   ❌ فشل حفظ التقرير: {e}\")\n\n    def create_final_structure_summary(self):\n        \"\"\"إنشاء ملخص الهيكل النهائي\"\"\" (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.create_all_readmes:[21:27]\n==anubis.scripts.organize_all_files:[242:248]\n            \"scripts\",\n            \"docs\",\n            \"configs\",\n            \"reports\",\n            \"logs\",\n            \"backup\", (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.smart_workflow_demo:[18:32]\n==anubis.scripts.test_langsmith_integration:[16:26]\nsys.path.append(str(Path(__file__).parent.parent))\n\ntry:\n    from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent\n    from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent\n    from anubis.agents.enhanced_memory_agent import EnhancedMemoryAgent\n    from anubis.agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent\n    from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer\n    from anubis.core.langsmith_wrapper import langsmith_wrapper\nexcept ImportError as e:\n    print(f\"❌ خطأ في الاستيراد: {e}\")\n    sys.exit(1)\n\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.scripts.langsmith_integration_demo:[157:164]\n==anubis.scripts.test_langsmith_integration:[150:157]\n                        provider = OllamaProvider(model_name=model)\n\n                        if provider.is_available():\n                            start_time = time.time()\n                            response = provider.generate_response(test_prompt)\n                            end_time = time.time()\n (duplicate-code)\nanubis\\tests\\__init__.py:1:0: R0801: Similar lines in 2 files\n==anubis.core.langsmith_wrapper:[55:62]\n==anubis.scripts.setup_langsmith:[264:271]\n        trace_name = f\"{agent_name}_{operation}\"\n\n        if self.client:\n            return self.client.trace(trace_name, inputs=inputs)\n        else:\n            # محاكاة\n            trace_data = { (duplicate-code)\n\n------------------------------------------------------------------\nYour code has been rated at 8.78/10 (previous run: 8.65/10, +0.13)\n\n", "stderr": "", "score": 8.78}, "mypy": {"tool": "mypy", "exit_code": 1, "stdout": "", "stderr": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe: No module named mypy\n", "issues_count": 0}}, "fixes_applied": ["إصلاح تنسيق anubis\\main.py", "إصلاح تنسيق anubis\\api\\anubis_api_server.py", "إصلاح تنسيق anubis\\core\\ai_integration.py", "إصلاح تنسيق anubis\\core\\assistant_system.py", "إصلاح تنسيق anubis\\core\\base_agent.py", "إصلاح تنسيق anubis\\core\\config_manager.py", "إصلاح تنسيق anubis\\core\\langsmith_wrapper.py", "إصلاح تنسيق anubis\\core\\logger.py", "إصلاح تنسيق anubis\\database\\anubis_database.py", "إصلاح تنسيق anubis\\database\\simple_validation.py", "إصلاح تنسيق anubis\\plugins\\base_plugin.py", "إصلاح تنسيق anubis\\plugins\\example_plugin.py", "إصلاح تنسيق anubis\\plugins\\plugin_manager.py", "إصلاح تنسيق anubis\\scripts\\activate_real_langsmith.py", "إصلاح تنسيق anubis\\scripts\\agents_cleanup.py", "إصلاح تنسيق anubis\\scripts\\check_ollama.py", "إصلاح تنسيق anubis\\scripts\\code_quality_checker.py", "إصلاح تنسيق anubis\\scripts\\complete_file_organizer.py", "إصلاح تنسيق anubis\\scripts\\create_all_readmes.py", "إصلاح تنسيق anubis\\scripts\\emergency_vscode_check.py", "إصلاح تنسيق anubis\\scripts\\fix_agents_with_gemini.py", "إصلاح تنسيق anubis\\scripts\\gemini_cli_helper.py", "إصلاح تنسيق anubis\\scripts\\gemini_integration_system.py", "إصلاح تنسيق anubis\\scripts\\langsmith_integration_demo.py", "إصلاح تنسيق anubis\\scripts\\organize_all_files.py", "إصلاح تنسيق anubis\\scripts\\organize_project_files.py", "إصلاح تنسيق anubis\\scripts\\process_monitor.py", "إصلاح تنسيق anubis\\scripts\\quick_start.py", "إصلاح تنسيق anubis\\scripts\\quick_vscode_check.py", "إصلاح تنسيق anubis\\scripts\\run_vscode_monitor.py", "إصلاح تنسيق anubis\\scripts\\setup_langsmith.py", "إصلاح تنسيق anubis\\scripts\\smart_workflow_demo.py", "إصلاح تنسيق anubis\\scripts\\start_anubis_n8n_system.py", "إصلاح تنسيق anubis\\scripts\\test_langsmith_integration.py", "إصلاح تنسيق anubis\\scripts\\test_ollama_langsmith.py", "إصلاح تنسيق anubis\\scripts\\vscode_emergency_cleanup.py", "إصلاح تنسيق anubis\\scripts\\vscode_heavy_load_analyzer.py", "إصلاح تنسيق anubis\\scripts\\vscode_process_alerts.py", "إصلاح تنسيق anubis\\scripts\\vscode_process_monitor.py", "إصلاح تنسيق anubis\\tests\\ask_anubis.py", "إصلاح تنسيق anubis\\tests\\comprehensive_agents_test.py", "إصلاح تنسيق anubis\\tests\\comprehensive_system_test.py", "إصلاح تنسيق anubis\\tests\\quick_ai_test.py", "إصلاح تنسيق anubis\\tests\\run_all_tests.py", "إصلاح تنسيق anubis\\tests\\test_agents.py", "إصلاح تنسيق anubis\\tests\\test_ai_fixed.py", "إصلاح تنسيق anubis\\tests\\test_ai_integration.py", "إصلاح تنسيق anubis\\tests\\test_anubis_system.py", "إصلاح تنسيق anubis\\tests\\test_enhanced_error_detector.py", "إصلاح تنسيق anubis\\tests\\test_error_detector.py", "إصلاح تنسيق anubis\\tests\\test_jewelry_database.py", "إصلاح تنسيق anubis\\tests\\test_jewelry_logic.py", "إصلاح تنسيق anubis\\tests\\test_plugins.py", "إصلاح تنسيق anubis\\tests\\test_project_analyzer.py", "إصلاح تنسيق anubis\\tests\\test_smart_analyzer.py", "إصلاح تنسيق anubis\\tests\\test_system.py", "إصلاح تنسيق anubis\\templates\\streamlit_template\\main.py", "إصلاح تنسيق anubis\\database\\core\\database_validator.py", "إصلاح تنسيق anubis\\database\\core\\final_validation_runner.py", "إصلاح تنسيق anubis\\database\\core\\mysql_connector.py", "إصلاح تنسيق anubis\\database\\core\\mysql_manager.py", "إصلاح تنسيق anubis\\database\\setup\\direct_setup.py", "إصلاح تنسيق anubis\\database\\setup\\setup_database.py", "إصلاح تنسيق anubis\\database\\tests\\comprehensive_test.py", "إصلاح تنسيق anubis\\database\\tests\\run_all_tests.py", "إصلاح تنسيق anubis\\database\\tests\\stress_test.py", "إصلاح تنسيق anubis\\database\\tests\\test_connection.py"], "summary": {"total_issues": 0, "tools_run": 4, "fixes_applied": 67, "overall_status": "<PERSON>ي<PERSON>"}}