# 🏺 قاعدة بيانات نظام أنوبيس
# Anubis AI Assistants System Database

## 📋 نظرة عامة

تم إعداد قاعدة بيانات MySQL شاملة لنظام أنوبيس للمساعدين الذكيين. تدعم القاعدة جميع العمليات الأساسية لإدارة المشاريع والتحليلات والأخطاء والتقارير.

## 🔧 الإعداد والتكوين

### متطلبات النظام
- MySQL Server 8.0 أو أحدث
- Python 3.8+
- مكتبة `mysql-connector-python`

### إعدادات الاتصال
```json
{
  "host": "localhost",
  "port": 3306,
  "user": "root",
  "password": "2452329511",
  "database": "anubis_system",
  "charset": "utf8mb4"
}
```

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية

#### 1. جدول المشاريع (projects)
```sql
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    path TEXT NOT NULL,
    type VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. جدول التحليلات (analyses)
```sql
CREATE TABLE analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    agent_type VARCHAR(100) NOT NULL,
    analysis_data JSON,
    results JSON,
    score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

#### 3. جدول الأخطاء (errors)
```sql
CREATE TABLE errors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    file_path TEXT,
    line_number INT DEFAULT 0,
    error_type VARCHAR(100),
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

#### 4. جدول التقارير (reports)
```sql
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    report_type VARCHAR(100),
    title VARCHAR(255),
    content LONGTEXT,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

#### 5. جدول الإضافات (plugins)
```sql
CREATE TABLE plugins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50),
    description TEXT,
    config JSON,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 6. جدول الأنشطة (activities)
```sql
CREATE TABLE activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    activity_type VARCHAR(100),
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);
```

## 🚀 التشغيل والاستخدام

### 1. إعداد قاعدة البيانات
```bash
# تثبيت المتطلبات
pip install mysql-connector-python

# إعداد قاعدة البيانات
python database/direct_setup.py
```

### 2. اختبار الاتصال
```bash
python database/test_connection.py
```

### 3. استخدام مدير قاعدة البيانات
```bash
python database/mysql_manager.py
```

## 💻 أمثلة على الاستخدام

### إنشاء مشروع جديد
```python
from database.mysql_manager import MySQLManager

db = MySQLManager()
project_id = db.create_project(
    name="مشروع جديد",
    path="/path/to/project",
    project_type="python",
    description="وصف المشروع"
)
```

### الحصول على إحصائيات النظام
```python
stats = db.get_dashboard_stats()
print(f"عدد المشاريع: {stats['projects']['total_projects']}")
print(f"عدد التحليلات: {stats['analyses']['total_analyses']}")
```

### البحث في المشاريع
```python
projects = db.search_projects("python")
for project in projects:
    print(f"- {project['name']} ({project['type']})")
```

## 📈 الإحصائيات الحالية

### البيانات التجريبية المدرجة:
- **المشاريع**: 6 مشاريع تجريبية
- **التحليلات**: 6 تحليلات بمتوسط نقاط 88.5
- **الأخطاء**: 6 أخطاء بمستويات مختلفة
- **الإضافات**: 4 إضافات نشطة
- **الأنشطة**: 6 أنشطة مسجلة

## 🔧 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `create_mysql_database.sql` | سكريبت إنشاء قاعدة البيانات |
| `direct_setup.py` | إعداد مباشر لقاعدة البيانات |
| `mysql_connector.py` | موصل قاعدة البيانات الأساسي |
| `mysql_manager.py` | مدير قاعدة البيانات المتقدم |
| `test_connection.py` | اختبار الاتصال |
| `setup_database.py` | سكريبت الإعداد الشامل |

## 🛡️ الأمان والصيانة

### إعدادات الأمان
- استخدام كلمات مرور قوية
- تشفير الاتصالات
- تحديد صلاحيات المستخدمين
- تسجيل العمليات الحساسة

### النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية
mysqldump -u root -p anubis_system > backup_$(date +%Y%m%d).sql

# استعادة النسخة الاحتياطية
mysql -u root -p anubis_system < backup_20231214.sql
```

### تنظيف البيانات القديمة
```python
# حذف البيانات الأقدم من سنة
cleanup_result = db.cleanup_old_data(days=365)
print(f"تم حذف {cleanup_result['analyses_deleted']} تحليل قديم")
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في الاتصال**
   ```
   Error: Can't connect to MySQL server
   ```
   - تأكد من تشغيل خادم MySQL
   - تحقق من إعدادات الاتصال
   - تأكد من صحة كلمة المرور

2. **خطأ في قاعدة البيانات غير موجودة**
   ```
   Error: Unknown database 'anubis_system'
   ```
   - قم بتشغيل `direct_setup.py` لإنشاء قاعدة البيانات

3. **خطأ في الصلاحيات**
   ```
   Error: Access denied for user
   ```
   - تحقق من صلاحيات المستخدم في MySQL

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملفات السجل
2. قم بتشغيل اختبار الاتصال
3. راجع إعدادات قاعدة البيانات
4. تأكد من تشغيل خادم MySQL

---

## 🎯 الخطوات التالية

1. **تطوير واجهة الويب**: إنشاء واجهة ويب لإدارة قاعدة البيانات
2. **تحسين الأداء**: إضافة فهارس وتحسين الاستعلامات
3. **النسخ الاحتياطي التلقائي**: إعداد نظام نسخ احتياطي تلقائي
4. **مراقبة الأداء**: إضافة أدوات مراقبة الأداء
5. **التوثيق المتقدم**: إنشاء دليل مطور شامل

---

**تم إنشاء قاعدة البيانات بنجاح! 🎉**

جميع الجداول والبيانات التجريبية جاهزة للاستخدام مع نظام أنوبيس للمساعدين الذكيين.
