events {
    worker_connections 1024;
}

http {
    upstream anubis_enhanced {
        server anubis_enhanced_container:8000;
    }
    
    upstream universal_ai {
        server universal_ai_container:8001;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/anubis/ {
            proxy_pass http://anubis_enhanced/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/universal/ {
            proxy_pass http://universal_ai/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
