#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبارات نظام حاوية الوكلاء
Agent Container System Tests

مطور بالتعاون مع Gemini CLI لاختبار العزل والأمان
"""

import json
import os
import sys
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent.parent))

from core.agent_container import (
    AgentContainer,
    AgentSandbox,
    IsolatedAgent,
    ResourceLimits,
    AgentContainerError,
    AgentTimeoutError,
    AgentSecurityError,
    create_agent_container
)


class MockAgent:
    """وكيل وهمي للاختبار"""
    
    def __init__(self, project_path: str, config: dict):
        self.project_path = project_path
        self.config = config
        self.initialized = False
        
    def initialize_agent(self):
        """تهيئة الوكيل الوهمي"""
        self.initialized = True
        
    def run_analysis(self):
        """تشغيل تحليل وهمي"""
        if not self.initialized:
            raise RuntimeError("الوكيل غير مهيأ")
            
        return {
            "status": "completed",
            "files_analyzed": 5,
            "issues_found": 2,
            "analysis_time": 0.1
        }


class SlowMockAgent(MockAgent):
    """وكيل وهمي بطيء للاختبار"""
    
    def run_analysis(self):
        """تحليل بطيء"""
        time.sleep(2)  # محاكاة عملية بطيئة
        return super().run_analysis()


class ErrorMockAgent(MockAgent):
    """وكيل وهمي يرمي خطأ"""
    
    def run_analysis(self):
        """تحليل يرمي خطأ"""
        raise RuntimeError("خطأ محاكى في التحليل")


class TestResourceLimits(unittest.TestCase):
    """اختبارات حدود الموارد"""
    
    def test_default_limits(self):
        """اختبار الحدود الافتراضية"""
        limits = ResourceLimits()
        
        self.assertEqual(limits.max_memory_mb, 512)
        self.assertEqual(limits.max_cpu_percent, 50.0)
        self.assertEqual(limits.max_execution_time, 300)
        self.assertEqual(limits.max_file_operations, 1000)
        self.assertEqual(limits.allowed_paths, [])
    
    def test_custom_limits(self):
        """اختبار حدود مخصصة"""
        limits = ResourceLimits(
            max_memory_mb=256,
            max_cpu_percent=25.0,
            max_execution_time=60,
            max_file_operations=500,
            allowed_paths=["/tmp", "/var/tmp"]
        )
        
        self.assertEqual(limits.max_memory_mb, 256)
        self.assertEqual(limits.max_cpu_percent, 25.0)
        self.assertEqual(limits.max_execution_time, 60)
        self.assertEqual(limits.max_file_operations, 500)
        self.assertEqual(len(limits.allowed_paths), 2)


class TestAgentSandbox(unittest.TestCase):
    """اختبارات صندوق الرمل"""
    
    def test_sandbox_creation(self):
        """اختبار إنشاء صندوق الرمل"""
        limits = ResourceLimits()
        
        with AgentSandbox("test_agent", limits) as sandbox:
            self.assertEqual(sandbox.agent_id, "test_agent")
            self.assertIsNotNone(sandbox.start_time)
            self.assertIsNotNone(sandbox.temp_dir)
            self.assertTrue(os.path.exists(sandbox.temp_dir))
        
        # التحقق من تنظيف المجلد المؤقت
        # ملاحظة: قد لا يعمل هذا الاختبار دائماً بسبب التوقيت
    
    def test_file_operations_limit(self):
        """اختبار حد عمليات الملفات"""
        limits = ResourceLimits(max_file_operations=2)
        
        with AgentSandbox("test_agent", limits) as sandbox:
            # عمليات ملفات عادية
            sandbox.check_file_operation("read", "/tmp/test1.txt")
            sandbox.check_file_operation("write", "/tmp/test2.txt")
            
            # تجاوز الحد
            with self.assertRaises(AgentSecurityError):
                sandbox.check_file_operation("read", "/tmp/test3.txt")
    
    def test_execution_time_check(self):
        """اختبار فحص وقت التنفيذ"""
        limits = ResourceLimits(max_execution_time=1)  # ثانية واحدة
        
        with AgentSandbox("test_agent", limits) as sandbox:
            # محاكاة مرور الوقت
            sandbox.start_time = sandbox.start_time.replace(
                year=sandbox.start_time.year - 1
            )
            
            with self.assertRaises(AgentTimeoutError):
                sandbox.check_execution_time()


class TestIsolatedAgent(unittest.TestCase):
    """اختبارات الوكيل المعزول"""
    
    def setUp(self):
        self.test_project_path = str(Path(__file__).parent.parent)
        self.test_config = {"enabled": True, "verbose": False}
        self.test_limits = ResourceLimits(max_execution_time=10)
    
    def test_successful_agent_run(self):
        """اختبار تشغيل وكيل ناجح"""
        isolated_agent = IsolatedAgent(
            MockAgent, "test_agent", self.test_project_path, 
            self.test_config, self.test_limits
        )
        
        result = isolated_agent.run_in_container()
        
        self.assertTrue(result["success"])
        self.assertEqual(result["agent_id"], "test_agent")
        self.assertIn("result", result)
        self.assertGreater(result["execution_time"], 0)
    
    def test_agent_timeout(self):
        """اختبار انتهاء مهلة الوكيل"""
        # حدود صارمة جداً
        strict_limits = ResourceLimits(max_execution_time=1)
        
        isolated_agent = IsolatedAgent(
            SlowMockAgent, "slow_agent", self.test_project_path,
            self.test_config, strict_limits
        )
        
        result = isolated_agent.run_in_container()
        
        self.assertFalse(result["success"])
        self.assertIn("انتهت مهلة الوكيل", result["error"])
        self.assertTrue(result.get("timeout", False))
    
    def test_agent_error_handling(self):
        """اختبار معالجة أخطاء الوكيل"""
        isolated_agent = IsolatedAgent(
            ErrorMockAgent, "error_agent", self.test_project_path,
            self.test_config, self.test_limits
        )
        
        result = isolated_agent.run_in_container()
        
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertEqual(result["agent_id"], "error_agent")


class TestAgentContainer(unittest.TestCase):
    """اختبارات حاوية الوكلاء"""
    
    def setUp(self):
        self.container = AgentContainer(max_concurrent_agents=2)
        self.test_project_path = str(Path(__file__).parent.parent)
        self.test_config = {"enabled": True, "verbose": False}
    
    def test_agent_registration(self):
        """اختبار تسجيل الوكلاء"""
        agent_id = self.container.register_agent(
            MockAgent, "test_agent_1", self.test_project_path, self.test_config
        )
        
        self.assertEqual(agent_id, "test_agent_1")
        self.assertIn("test_agent_1", self.container.running_agents)
        
        # اختبار تسجيل وكيل مكرر
        with self.assertRaises(AgentContainerError):
            self.container.register_agent(
                MockAgent, "test_agent_1", self.test_project_path, self.test_config
            )
    
    def test_single_agent_run(self):
        """اختبار تشغيل وكيل واحد"""
        # تسجيل الوكيل
        agent_id = self.container.register_agent(
            MockAgent, "single_agent", self.test_project_path, self.test_config
        )
        
        # تشغيل الوكيل
        result = self.container.run_agent(agent_id)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["agent_id"], "single_agent")
        
        # التحقق من أن الوكيل تم إزالته من قائمة التشغيل
        self.assertNotIn(agent_id, self.container.running_agents)
        self.assertIn(agent_id, self.container.agent_results)
    
    def test_multiple_agents_run(self):
        """اختبار تشغيل عدة وكلاء"""
        # تسجيل عدة وكلاء
        agent_ids = []
        for i in range(3):
            agent_id = f"agent_{i}"
            self.container.register_agent(
                MockAgent, agent_id, self.test_project_path, self.test_config
            )
            agent_ids.append(agent_id)
        
        # تشغيل جميع الوكلاء
        results = self.container.run_all_agents()
        
        self.assertEqual(len(results), 3)
        
        for agent_id in agent_ids:
            self.assertIn(agent_id, results)
            self.assertTrue(results[agent_id]["success"])
    
    def test_agent_status_tracking(self):
        """اختبار تتبع حالة الوكلاء"""
        # تسجيل وكيل
        agent_id = self.container.register_agent(
            MockAgent, "status_agent", self.test_project_path, self.test_config
        )
        
        # فحص الحالة قبل التشغيل
        status = self.container.get_agent_status(agent_id)
        self.assertEqual(status["status"], "running")
        
        # تشغيل الوكيل
        self.container.run_agent(agent_id)
        
        # فحص الحالة بعد التشغيل
        status = self.container.get_agent_status(agent_id)
        self.assertEqual(status["status"], "completed")
        
        # فحص وكيل غير موجود
        status = self.container.get_agent_status("non_existent")
        self.assertEqual(status["status"], "not_found")
    
    def test_container_stats(self):
        """اختبار إحصائيات الحاوية"""
        # إحصائيات فارغة
        stats = self.container.get_container_stats()
        self.assertEqual(stats["running_agents"], 0)
        self.assertEqual(stats["completed_agents"], 0)
        
        # تسجيل وكيل
        self.container.register_agent(
            MockAgent, "stats_agent", self.test_project_path, self.test_config
        )
        
        stats = self.container.get_container_stats()
        self.assertEqual(stats["running_agents"], 1)
        self.assertEqual(stats["completed_agents"], 0)
        
        # تشغيل الوكيل
        self.container.run_agent("stats_agent")
        
        stats = self.container.get_container_stats()
        self.assertEqual(stats["running_agents"], 0)
        self.assertEqual(stats["completed_agents"], 1)


class TestContainerIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def test_create_agent_container_function(self):
        """اختبار دالة إنشاء الحاوية"""
        container = create_agent_container(max_concurrent=5)
        
        self.assertIsInstance(container, AgentContainer)
        self.assertEqual(container.max_concurrent_agents, 5)
    
    def test_full_workflow(self):
        """اختبار سير عمل كامل"""
        # إنشاء حاوية
        container = create_agent_container()
        
        # تسجيل وكلاء مختلفين
        container.register_agent(
            MockAgent, "normal_agent", 
            str(Path(__file__).parent.parent), {"enabled": True}
        )
        
        container.register_agent(
            ErrorMockAgent, "error_agent",
            str(Path(__file__).parent.parent), {"enabled": True}
        )
        
        # تشغيل جميع الوكلاء
        results = container.run_all_agents()
        
        # التحقق من النتائج
        self.assertEqual(len(results), 2)
        self.assertTrue(results["normal_agent"]["success"])
        self.assertFalse(results["error_agent"]["success"])
        
        # فحص الإحصائيات النهائية
        stats = container.get_container_stats()
        self.assertEqual(stats["completed_agents"], 2)
        self.assertEqual(stats["running_agents"], 0)


def run_container_tests():
    """تشغيل جميع اختبارات الحاوية"""
    print("🧪 تشغيل اختبارات نظام حاوية الوكلاء")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("-" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestResourceLimits))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestAgentSandbox))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestIsolatedAgent))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestAgentContainer))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestContainerIntegration))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # طباعة النتائج
    print("\n" + "="*60)
    print("📊 نتائج اختبارات نظام حاوية الوكلاء:")
    print(f"✅ نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ فشل: {len(result.failures)}")
    print(f"🔴 أخطاء: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print("\n🔴 الأخطاء:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    print("🤖 تم تطوير هذه الاختبارات بالتعاون مع Gemini CLI")
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_container_tests()
    sys.exit(0 if success else 1)
