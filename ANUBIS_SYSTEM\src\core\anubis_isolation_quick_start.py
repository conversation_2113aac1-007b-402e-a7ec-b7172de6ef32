#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 بدء سريع لنظام العزل الشامل لأنوبيس
Anubis Isolation System Quick Start

سكريبت تشغيل سريع مع:
- فحص متطلبات النظام
- إعداد البيئة
- تشغيل الخدمات
- مراقبة الحالة

مطور بالتعاون مع Gemini CLI و Ollama
"""

import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any


class AnubisIsolationQuickStart:
    """مدير البدء السريع لنظام العزل"""
    
    def __init__(self, isolation_dir: str = "anubis_isolated_system"):
        self.isolation_dir = Path(isolation_dir)
        self.status = {
            "start_time": datetime.now().isoformat(),
            "system_checks": {},
            "service_status": {},
            "health_checks": {},
            "ready": False
        }
        
    def run_quick_start(self):
        """تشغيل البدء السريع"""
        print("🚀 بدء سريع لنظام العزل الشامل")
        print("🏺 نظام أنوبيس المعزول")
        print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
        print("=" * 60)
        
        # فحص متطلبات النظام
        if not self.check_system_requirements():
            print("❌ فشل في فحص متطلبات النظام")
            return False
        
        # فحص وجود نظام العزل
        if not self.check_isolation_system():
            print("❌ نظام العزل غير موجود")
            return False
        
        # إعداد البيئة
        if not self.setup_environment():
            print("❌ فشل في إعداد البيئة")
            return False
        
        # تشغيل الخدمات
        if not self.start_services():
            print("❌ فشل في تشغيل الخدمات")
            return False
        
        # فحص صحة الخدمات
        if not self.health_check():
            print("⚠️ بعض الخدمات قد لا تعمل بشكل صحيح")
        
        # عرض معلومات الوصول
        self.show_access_info()
        
        self.status["ready"] = True
        return True
    
    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        print("\n🔍 فحص متطلبات النظام...")
        
        requirements = {
            "docker": self.check_docker(),
            "docker_compose": self.check_docker_compose(),
            "python": self.check_python(),
            "disk_space": self.check_disk_space()
        }
        
        self.status["system_checks"] = requirements
        
        all_passed = all(requirements.values())
        
        for req, status in requirements.items():
            icon = "✅" if status else "❌"
            print(f"  {icon} {req}")
        
        return all_passed
    
    def check_docker(self):
        """فحص Docker"""
        try:
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            return result.returncode == 0
        except:
            return False
    
    def check_docker_compose(self):
        """فحص Docker Compose"""
        try:
            result = subprocess.run(
                ["docker-compose", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            return result.returncode == 0
        except:
            # محاولة docker compose (الإصدار الجديد)
            try:
                result = subprocess.run(
                    ["docker", "compose", "version"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                return result.returncode == 0
            except:
                return False
    
    def check_python(self):
        """فحص Python"""
        try:
            version = sys.version_info
            return version.major >= 3 and version.minor >= 8
        except:
            return False
    
    def check_disk_space(self):
        """فحص مساحة القرص"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            # نحتاج على الأقل 5 GB مساحة فارغة
            return free > 5 * 1024 * 1024 * 1024
        except:
            return False
    
    def check_isolation_system(self):
        """فحص وجود نظام العزل"""
        print("\n📁 فحص نظام العزل...")
        
        if not self.isolation_dir.exists():
            print(f"  ❌ مجلد العزل غير موجود: {self.isolation_dir}")
            print("  💡 قم بتشغيل: python anubis_isolation_system.py")
            return False
        
        # فحص الملفات الأساسية
        required_files = [
            "docker-compose.yml",
            "scripts/start.sh",
            "scripts/stop.sh"
        ]
        
        for file_path in required_files:
            file_full_path = self.isolation_dir / file_path
            if not file_full_path.exists():
                print(f"  ❌ ملف مفقود: {file_path}")
                return False
        
        print("  ✅ نظام العزل موجود ومكتمل")
        return True
    
    def setup_environment(self):
        """إعداد البيئة"""
        print("\n⚙️ إعداد البيئة...")
        
        try:
            # إنشاء ملفات requirements.txt للخدمات
            self.create_requirements_files()
            
            # إنشاء ملفات main.py للخدمات
            self.create_main_files()
            
            print("  ✅ تم إعداد البيئة")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في إعداد البيئة: {e}")
            return False
    
    def create_requirements_files(self):
        """إنشاء ملفات requirements.txt"""
        
        # متطلبات أنوبيس المحسن
        anubis_requirements = """fastapi==0.104.1
uvicorn==0.24.0
mysql-connector-python==8.2.0
pydantic==2.5.0
python-multipart==0.0.6
requests==2.31.0
aiofiles==23.2.1
prometheus-client==0.19.0
"""
        
        anubis_req_file = self.isolation_dir / "services/anubis_enhanced/requirements.txt"
        with open(anubis_req_file, 'w') as f:
            f.write(anubis_requirements)
        
        # متطلبات Universal AI
        universal_requirements = """fastapi==0.104.1
uvicorn==0.24.0
mysql-connector-python==8.2.0
pydantic==2.5.0
requests==2.31.0
prometheus-client==0.19.0
"""
        
        universal_req_file = self.isolation_dir / "services/universal_ai/requirements.txt"
        with open(universal_req_file, 'w') as f:
            f.write(universal_requirements)
    
    def create_main_files(self):
        """إنشاء ملفات main.py للخدمات"""
        
        # main.py لأنوبيس المحسن
        anubis_main = '''#!/usr/bin/env python3
import uvicorn
from fastapi import FastAPI
from prometheus_client import Counter, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

app = FastAPI(title="Anubis Enhanced Service", version="1.0.0")

# مقاييس Prometheus
REQUEST_COUNT = Counter('anubis_requests_total', 'Total requests')

@app.get("/")
async def root():
    REQUEST_COUNT.inc()
    return {"service": "Anubis Enhanced", "status": "running", "version": "1.0.0"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "anubis_enhanced"}

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
        
        anubis_main_file = self.isolation_dir / "services/anubis_enhanced/main.py"
        with open(anubis_main_file, 'w', encoding='utf-8') as f:
            f.write(anubis_main)
        
        # main.py لـ Universal AI
        universal_main = '''#!/usr/bin/env python3
import uvicorn
from fastapi import FastAPI
from prometheus_client import Counter, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

app = FastAPI(title="Universal AI Service", version="1.0.0")

# مقاييس Prometheus
REQUEST_COUNT = Counter('universal_ai_requests_total', 'Total requests')

@app.get("/")
async def root():
    REQUEST_COUNT.inc()
    return {"service": "Universal AI", "status": "running", "version": "1.0.0"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "universal_ai"}

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
'''
        
        universal_main_file = self.isolation_dir / "services/universal_ai/main.py"
        with open(universal_main_file, 'w', encoding='utf-8') as f:
            f.write(universal_main)
    
    def start_services(self):
        """تشغيل الخدمات"""
        print("\n🚀 تشغيل الخدمات...")
        
        try:
            # الانتقال إلى مجلد العزل
            os.chdir(self.isolation_dir)
            
            # تشغيل docker-compose
            print("  🐳 بدء تشغيل الحاويات...")
            result = subprocess.run(
                ["docker-compose", "up", "-d"],
                capture_output=True,
                text=True,
                timeout=300  # 5 دقائق
            )
            
            if result.returncode != 0:
                # محاولة مع docker compose (الإصدار الجديد)
                result = subprocess.run(
                    ["docker", "compose", "up", "-d"],
                    capture_output=True,
                    text=True,
                    timeout=300
                )
            
            if result.returncode == 0:
                print("  ✅ تم تشغيل جميع الخدمات")
                
                # انتظار قليل للخدمات لتبدأ
                print("  ⏳ انتظار بدء الخدمات...")
                time.sleep(30)
                
                return True
            else:
                print(f"  ❌ فشل في تشغيل الخدمات: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"  ❌ خطأ في تشغيل الخدمات: {e}")
            return False
        finally:
            # العودة إلى المجلد الأصلي
            os.chdir("..")
    
    def health_check(self):
        """فحص صحة الخدمات"""
        print("\n🏥 فحص صحة الخدمات...")
        
        services = {
            "anubis_enhanced": "http://localhost:8000/health",
            "universal_ai": "http://localhost:8001/health",
            "nginx": "http://localhost/health",
            "prometheus": "http://localhost:9090/-/healthy",
            "grafana": "http://localhost:3000/api/health"
        }
        
        health_status = {}
        
        for service_name, health_url in services.items():
            try:
                import requests
                response = requests.get(health_url, timeout=10)
                is_healthy = response.status_code == 200
                health_status[service_name] = is_healthy
                
                icon = "✅" if is_healthy else "❌"
                print(f"  {icon} {service_name}")
                
            except Exception as e:
                health_status[service_name] = False
                print(f"  ❌ {service_name} (خطأ: {str(e)[:50]})")
        
        self.status["health_checks"] = health_status
        
        # إذا كان 70% من الخدمات تعمل، نعتبر النظام صحي
        healthy_count = sum(health_status.values())
        total_count = len(health_status)
        
        return (healthy_count / total_count) >= 0.7
    
    def show_access_info(self):
        """عرض معلومات الوصول"""
        print("\n🌐 معلومات الوصول للخدمات:")
        print("=" * 40)
        
        services_info = [
            ("🏺 أنوبيس المحسن", "http://localhost/api/anubis/"),
            ("🤖 Universal AI", "http://localhost/api/universal/"),
            ("🔍 Ollama API", "http://localhost:11434"),
            ("🗄️ قاعدة البيانات MySQL", "localhost:3306"),
            ("📊 Grafana", "http://localhost:3000 (admin/anubis2024)"),
            ("📈 Prometheus", "http://localhost:9090"),
            ("🌐 API Gateway", "http://localhost")
        ]
        
        for service_name, url in services_info:
            print(f"  {service_name}: {url}")
        
        print("\n🛠️ أوامر الإدارة:")
        print(f"  📊 مراقبة: cd {self.isolation_dir} && ./scripts/monitor.sh")
        print(f"  🛑 إيقاف: cd {self.isolation_dir} && ./scripts/stop.sh")
        print(f"  📋 السجلات: cd {self.isolation_dir} && docker-compose logs -f")
    
    def save_status_report(self, filename: str = None) -> str:
        """حفظ تقرير الحالة"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_isolation_status_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.status, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الحالة في: {filename}")
        return filename


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء سريع لنظام العزل الشامل لأنوبيس")
    print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
    
    # إنشاء مدير البدء السريع
    quick_start = AnubisIsolationQuickStart()
    
    # تشغيل البدء السريع
    success = quick_start.run_quick_start()
    
    # حفظ تقرير الحالة
    quick_start.save_status_report()
    
    if success:
        print("\n🎉 تم تشغيل نظام العزل بنجاح!")
        print("🏺 نظام أنوبيس جاهز للاستخدام")
        sys.exit(0)
    else:
        print("\n❌ فشل في تشغيل نظام العزل")
        print("💡 تحقق من الأخطاء أعلاه وحاول مرة أخرى")
        sys.exit(1)


if __name__ == "__main__":
    main()
