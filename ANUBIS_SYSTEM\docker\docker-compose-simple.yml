version: '3.8'

# 🏺 نظام أنوبيس - Docker Compose المبسط
# Anubis System - Simplified Docker Compose
# يحتوي فقط على الخدمات الأساسية التي تعمل بسرعة

networks:
  anubis-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:

services:
  # ===== النظام الأساسي - Core System =====
  anubis-core:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: anubis-core
    restart: unless-stopped
    command: python main.py
    volumes:
      - ./data:/app/data
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./src:/app/src
    environment:
      - ANUBIS_ENV=development
      - DATABASE_URL=mysql://anubis:anubis_secure_password@anubis-mysql:3306/anubis_system
      - REDIS_URL=redis://anubis-redis:6379
    ports:
      - "8000:8000"
    depends_on:
      - anubis-mysql
      - anubis-redis
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== قاعدة البيانات - Database =====
  anubis-mysql:
    image: mysql:8.0
    container_name: anubis-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: **********
      MYSQL_DATABASE: anubis_system
      MYSQL_USER: anubis
      MYSQL_PASSWORD: anubis_secure_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ===== Redis للتخزين المؤقت - Redis Cache =====
  anubis-redis:
    image: redis:7-alpine
    container_name: anubis-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
