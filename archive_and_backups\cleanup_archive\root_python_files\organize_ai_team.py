#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيم مجلد فريق الذكاء الاصطناعي
"""

import shutil
import os
from pathlib import Path

def organize_ai_team():
    """تنظيم مجلد فريق الذكاء الاصطناعي"""
    
    # إنشاء المجلدات
    team_dir = Path("anubis_ai_team")
    paths_dir = Path("anubis_project_paths")
    
    team_dir.mkdir(exist_ok=True)
    paths_dir.mkdir(exist_ok=True)
    
    print("🏺 تنظيم مجلد فريق الذكاء الاصطناعي")
    print("=" * 50)
    
    # ملفات الفريق للنقل
    team_files = [
        "anubis_ai_team_collaboration_system.py",
        "anubis_ai_collaboration_helper.py", 
        "anubis_ai_team_collaboration_plan.json"
    ]
    
    # نقل ملفات الفريق
    print("\n📁 نقل ملفات الفريق:")
    for file_name in team_files:
        source = Path(file_name)
        if source.exists():
            dest = team_dir / file_name
            shutil.copy2(source, dest)
            print(f"✅ نقل: {file_name}")
        else:
            print(f"⚠️  غير موجود: {file_name}")
    
    # ملفات Gemini للنقل
    gemini_files = [
        "anubis_gemini_cli_helper.py",
        "anubis_project_organization_gemini_request.md",
        "anubis_gemini_docker_help_request.md"
    ]
    
    print("\n📁 نقل ملفات Gemini:")
    for file_name in gemini_files:
        source = Path(file_name)
        if source.exists():
            dest = team_dir / file_name
            shutil.copy2(source, dest)
            print(f"✅ نقل: {file_name}")
    
    print(f"\n📋 مجلد الفريق يحتوي على {len(list(team_dir.iterdir()))} ملف")
    
    return team_dir, paths_dir

if __name__ == "__main__":
    organize_ai_team()
