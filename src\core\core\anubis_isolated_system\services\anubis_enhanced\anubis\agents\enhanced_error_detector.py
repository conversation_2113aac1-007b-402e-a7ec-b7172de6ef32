#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Enhanced Error Detector Agent - وكيل كشف الأخطاء المحسن
يكتشف ويحلل الأخطاء في الكود بطريقة متقدمة
"""

import ast
import json
import re
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent


class EnhancedErrorDetectorAgent(BaseAgent):
    """وكيل كشف الأخطاء المحسن"""

    def get_agent_type(self) -> str:
        return "enhanced_error_detector"

    def initialize_agent(self):
        """تهيئة وكيل كشف الأخطاء"""
        self.supported_extensions = ['.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c']
        self.error_patterns = {
            'syntax_errors': [],
            'logic_errors': [],
            'style_errors': [],
            'security_issues': [],
            'performance_issues': []
        }
        self.total_files_scanned = 0
        self.total_errors_found = 0
        
        self.log_action("تم تهيئة وكيل كشف الأخطاء المحسن")

    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل كشف الأخطاء"""
        try:
            self.log_action("بدء تحليل كشف الأخطاء المحسن")
            
            # البحث عن ملفات الكود
            code_files = self.find_code_files()
            
            # تحليل كل ملف
            file_results = {}
            total_errors = 0
            
            for file_path in code_files:
                file_analysis = self.analyze_file(file_path)
                file_results[str(file_path)] = file_analysis
                total_errors += file_analysis.get('total_errors', 0)
            
            # تجميع النتائج
            error_summary = self.summarize_errors(file_results)
            
            result = {
                "status": "completed",
                "files_scanned": len(code_files),
                "total_errors": total_errors,
                "error_summary": error_summary,
                "file_analysis": file_results,
                "recommendations": self.get_error_recommendations(file_results),
                "critical_issues": self.identify_critical_issues(file_results),
                "timestamp": datetime.now().isoformat()
            }
            
            # حفظ التقرير
            self.save_report(result, f"error_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            self.log_action(f"تم إكمال تحليل كشف الأخطاء - تم العثور على {total_errors} خطأ")
            return result
            
        except Exception as e:
            self.log_action("خطأ في تحليل كشف الأخطاء", str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def find_code_files(self) -> List[Path]:
        """البحث عن ملفات الكود"""
        code_files = []
        
        for ext in self.supported_extensions:
            code_files.extend(self.project_path.rglob(f"*{ext}"))
        
        # تصفية الملفات غير المرغوب فيها
        filtered_files = []
        for file_path in code_files:
            if not any(exclude in str(file_path) for exclude in ['__pycache__', '.git', 'node_modules', '.venv']):
                filtered_files.append(file_path)
        
        self.log_action(f"تم العثور على {len(filtered_files)} ملف كود")
        return filtered_files

    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """تحليل ملف واحد"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            analysis = {
                "file_path": str(file_path),
                "file_size": len(content),
                "line_count": len(content.splitlines()),
                "extension": file_path.suffix,
                "errors": {
                    "syntax_errors": [],
                    "logic_errors": [],
                    "style_errors": [],
                    "security_issues": [],
                    "performance_issues": []
                },
                "total_errors": 0,
                "status": "success"
            }
            
            # تحليل حسب نوع الملف
            if file_path.suffix == '.py':
                analysis = self.analyze_python_file(file_path, content, analysis)
            elif file_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
                analysis = self.analyze_javascript_file(file_path, content, analysis)
            else:
                analysis = self.analyze_generic_file(file_path, content, analysis)
            
            # حساب إجمالي الأخطاء
            analysis["total_errors"] = sum(len(errors) for errors in analysis["errors"].values())
            
            return analysis
            
        except Exception as e:
            return {
                "file_path": str(file_path),
                "status": "error",
                "error": str(e),
                "total_errors": 0
            }

    def analyze_python_file(self, file_path: Path, content: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل ملف Python"""
        
        # فحص الأخطاء النحوية
        try:
            ast.parse(content)
        except SyntaxError as e:
            analysis["errors"]["syntax_errors"].append({
                "type": "SyntaxError",
                "message": str(e),
                "line": e.lineno,
                "column": e.offset
            })
        
        # فحص أخطاء الأسلوب
        style_errors = self.check_python_style(content)
        analysis["errors"]["style_errors"].extend(style_errors)
        
        # فحص المشاكل الأمنية
        security_issues = self.check_python_security(content)
        analysis["errors"]["security_issues"].extend(security_issues)
        
        # فحص مشاكل الأداء
        performance_issues = self.check_python_performance(content)
        analysis["errors"]["performance_issues"].extend(performance_issues)
        
        # فحص الأخطاء المنطقية
        logic_errors = self.check_python_logic(content)
        analysis["errors"]["logic_errors"].extend(logic_errors)
        
        return analysis

    def check_python_style(self, content: str) -> List[Dict[str, Any]]:
        """فحص أخطاء الأسلوب في Python"""
        errors = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # خطوط طويلة جداً
            if len(line) > 120:
                errors.append({
                    "type": "LineLength",
                    "message": f"السطر طويل جداً ({len(line)} حرف)",
                    "line": i,
                    "severity": "warning"
                })
            
            # استخدام tabs بدلاً من spaces
            if '\t' in line:
                errors.append({
                    "type": "TabUsage",
                    "message": "استخدام Tab بدلاً من المسافات",
                    "line": i,
                    "severity": "warning"
                })
            
            # مسافات زائدة في نهاية السطر
            if line.endswith(' '):
                errors.append({
                    "type": "TrailingWhitespace",
                    "message": "مسافات زائدة في نهاية السطر",
                    "line": i,
                    "severity": "info"
                })
        
        return errors

    def check_python_security(self, content: str) -> List[Dict[str, Any]]:
        """فحص المشاكل الأمنية في Python"""
        errors = []
        lines = content.splitlines()
        
        dangerous_patterns = [
            (r'eval\s*\(', "استخدام eval() خطير أمنياً"),
            (r'exec\s*\(', "استخدام exec() خطير أمنياً"),
            (r'subprocess\.call\s*\(.*shell\s*=\s*True', "استخدام shell=True خطير"),
            (r'pickle\.loads?\s*\(', "استخدام pickle مع بيانات غير موثوقة خطير"),
            (r'input\s*\(.*\)', "استخدام input() قد يكون خطير"),
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern, message in dangerous_patterns:
                if re.search(pattern, line):
                    errors.append({
                        "type": "SecurityIssue",
                        "message": message,
                        "line": i,
                        "severity": "critical",
                        "code": line.strip()
                    })
        
        return errors

    def check_python_performance(self, content: str) -> List[Dict[str, Any]]:
        """فحص مشاكل الأداء في Python"""
        errors = []
        lines = content.splitlines()
        
        performance_patterns = [
            (r'for\s+\w+\s+in\s+range\s*\(\s*len\s*\(', "استخدم enumerate بدلاً من range(len())"),
            (r'\+\s*=.*\[.*\]', "استخدم list.extend() بدلاً من += للقوائم"),
            (r'\.keys\s*\(\s*\).*in\s+', "استخدم 'in dict' بدلاً من 'in dict.keys()'"),
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern, message in performance_patterns:
                if re.search(pattern, line):
                    errors.append({
                        "type": "PerformanceIssue",
                        "message": message,
                        "line": i,
                        "severity": "warning",
                        "code": line.strip()
                    })
        
        return errors

    def check_python_logic(self, content: str) -> List[Dict[str, Any]]:
        """فحص الأخطاء المنطقية في Python"""
        errors = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # متغيرات غير مستخدمة
            if re.search(r'^\s*\w+\s*=.*', line) and not re.search(r'self\.\w+', line):
                var_name = re.search(r'^\s*(\w+)\s*=', line)
                if var_name:
                    var_name = var_name.group(1)
                    # البحث عن استخدام المتغير في باقي الكود
                    if var_name not in content[content.find(line) + len(line):]:
                        errors.append({
                            "type": "UnusedVariable",
                            "message": f"المتغير '{var_name}' غير مستخدم",
                            "line": i,
                            "severity": "warning"
                        })
            
            # مقارنات خاطئة
            if '==' in line and 'None' in line:
                errors.append({
                    "type": "NoneComparison",
                    "message": "استخدم 'is None' بدلاً من '== None'",
                    "line": i,
                    "severity": "warning"
                })
        
        return errors

    def analyze_javascript_file(self, file_path: Path, content: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل ملف JavaScript/TypeScript"""
        
        # فحص أخطاء الأسلوب الأساسية
        style_errors = self.check_javascript_style(content)
        analysis["errors"]["style_errors"].extend(style_errors)
        
        # فحص المشاكل الأمنية
        security_issues = self.check_javascript_security(content)
        analysis["errors"]["security_issues"].extend(security_issues)
        
        return analysis

    def check_javascript_style(self, content: str) -> List[Dict[str, Any]]:
        """فحص أخطاء الأسلوب في JavaScript"""
        errors = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # استخدام var بدلاً من let/const
            if re.search(r'\bvar\s+', line):
                errors.append({
                    "type": "VarUsage",
                    "message": "استخدم let أو const بدلاً من var",
                    "line": i,
                    "severity": "warning"
                })
            
            # مقارنات غير صارمة
            if '==' in line and '===' not in line:
                errors.append({
                    "type": "LooseEquality",
                    "message": "استخدم === بدلاً من ==",
                    "line": i,
                    "severity": "warning"
                })
        
        return errors

    def check_javascript_security(self, content: str) -> List[Dict[str, Any]]:
        """فحص المشاكل الأمنية في JavaScript"""
        errors = []
        lines = content.splitlines()
        
        dangerous_patterns = [
            (r'eval\s*\(', "استخدام eval() خطير أمنياً"),
            (r'innerHTML\s*=', "استخدام innerHTML قد يؤدي لـ XSS"),
            (r'document\.write\s*\(', "استخدام document.write خطير"),
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern, message in dangerous_patterns:
                if re.search(pattern, line):
                    errors.append({
                        "type": "SecurityIssue",
                        "message": message,
                        "line": i,
                        "severity": "critical"
                    })
        
        return errors

    def analyze_generic_file(self, file_path: Path, content: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل عام للملفات الأخرى"""
        
        # فحص أساسي للأخطاء الشائعة
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # خطوط فارغة زائدة
            if not line.strip() and i > 1 and not lines[i-2].strip():
                analysis["errors"]["style_errors"].append({
                    "type": "ExtraBlankLines",
                    "message": "خطوط فارغة زائدة",
                    "line": i,
                    "severity": "info"
                })
        
        return analysis

    def summarize_errors(self, file_results: Dict[str, Any]) -> Dict[str, Any]:
        """تلخيص الأخطاء"""
        summary = {
            "syntax_errors": 0,
            "logic_errors": 0,
            "style_errors": 0,
            "security_issues": 0,
            "performance_issues": 0,
            "by_severity": {
                "critical": 0,
                "warning": 0,
                "info": 0
            }
        }
        
        for file_analysis in file_results.values():
            if file_analysis.get("status") == "success":
                errors = file_analysis.get("errors", {})
                for error_type, error_list in errors.items():
                    summary[error_type] += len(error_list)
                    
                    # تصنيف حسب الخطورة
                    for error in error_list:
                        severity = error.get("severity", "warning")
                        if severity in summary["by_severity"]:
                            summary["by_severity"][severity] += 1
        
        return summary

    def get_error_recommendations(self, file_results: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات لإصلاح الأخطاء"""
        recommendations = []
        
        # تحليل أنواع الأخطاء الشائعة
        error_counts = {"syntax_errors": 0, "security_issues": 0, "style_errors": 0}
        
        for file_analysis in file_results.values():
            if file_analysis.get("status") == "success":
                errors = file_analysis.get("errors", {})
                for error_type, error_list in errors.items():
                    if error_type in error_counts:
                        error_counts[error_type] += len(error_list)
        
        # توصيات بناءً على الأخطاء
        if error_counts["syntax_errors"] > 0:
            recommendations.append("إصلاح الأخطاء النحوية أولاً قبل تشغيل الكود")
        
        if error_counts["security_issues"] > 0:
            recommendations.append("مراجعة المشاكل الأمنية فوراً - أولوية عالية")
        
        if error_counts["style_errors"] > 10:
            recommendations.append("استخدام أدوات تنسيق الكود مثل Black أو Prettier")
        
        recommendations.append("إجراء مراجعة دورية للكود")
        recommendations.append("استخدام أدوات التحليل الثابت مثل pylint أو ESLint")
        
        return recommendations

    def identify_critical_issues(self, file_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تحديد المشاكل الحرجة"""
        critical_issues = []
        
        for file_path, file_analysis in file_results.items():
            if file_analysis.get("status") == "success":
                errors = file_analysis.get("errors", {})
                
                # البحث عن الأخطاء الحرجة
                for error_type, error_list in errors.items():
                    for error in error_list:
                        if error.get("severity") == "critical":
                            critical_issues.append({
                                "file": file_path,
                                "type": error.get("type"),
                                "message": error.get("message"),
                                "line": error.get("line"),
                                "code": error.get("code", "")
                            })
        
        return critical_issues