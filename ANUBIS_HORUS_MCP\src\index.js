#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';

class AnubisMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'anubis-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'anubis_status',
            description: 'Get Anubis system status',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'anubis_info',
            description: 'Get Anubis system information',
            inputSchema: {
              type: 'object',
              properties: {
                component: {
                  type: 'string',
                  description: 'Component to get info about',
                  enum: ['database', 'agents', 'api', 'all'],
                },
              },
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'anubis_status':
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    status: 'running',
                    timestamp: new Date().toISOString(),
                    components: {
                      database: 'connected',
                      agents: 'active',
                      api: 'running',
                    },
                  }, null, 2),
                },
              ],
            };

          case 'anubis_info':
            const component = args?.component || 'all';
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    component,
                    info: `Information about ${component} component`,
                    version: '2.0.0',
                    last_updated: new Date().toISOString(),
                  }, null, 2),
                },
              ],
            };

          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${name}`
            );
        }
      } catch (error) {
        throw new McpError(
          ErrorCode.InternalError,
          `Error executing tool ${name}: ${error.message}`
        );
      }
    });
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Anubis MCP Server running on stdio');
  }
}

const server = new AnubisMCPServer();
server.run().catch(console.error);
