#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎛️ VS Code Process Control Dashboard
===================================
واجهة تحكم شاملة لإدارة العمليات والإضافات

المميزات:
- مراقبة العمليات في الوقت الفعلي
- إدارة إضافات VS Code
- إغلاق العمليات المعلقة
- تحسين الأداء
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import psutil
import json
import os
import threading
import time
from datetime import datetime
import webbrowser

class ProcessControlDashboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ VS Code Process Control Dashboard")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2d2d2d')
        
        # متغيرات التحكم
        self.monitoring = False
        self.process_data = []
        self.extensions_data = []
        
        self.setup_ui()
        self.refresh_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط العنوان
        title_frame = tk.Frame(self.root, bg='#1e1e1e', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🎛️ VS Code Process Control Dashboard", 
                              font=('Arial', 16, 'bold'),
                              fg='#ffffff', bg='#1e1e1e')
        title_label.pack(pady=15)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.root, bg='#2d2d2d')
        stats_frame.pack(fill='x', padx=5, pady=5)
        
        self.stats_labels = {}
        stats = ['العمليات النشطة', 'استهلاك الذاكرة', 'استهلاك المعالج', 'الإضافات المفعلة']
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']
        
        for i, (stat, color) in enumerate(zip(stats, colors)):
            frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            frame.pack(side='left', fill='both', expand=True, padx=2)
            
            tk.Label(frame, text=stat, font=('Arial', 10, 'bold'), 
                    bg=color, fg='white').pack(pady=2)
            
            self.stats_labels[stat] = tk.Label(frame, text="0", 
                                             font=('Arial', 14, 'bold'),
                                             bg=color, fg='white')
            self.stats_labels[stat].pack(pady=2)
        
        # إطار التحكم الرئيسي
        main_frame = tk.Frame(self.root, bg='#2d2d2d')
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - العمليات
        left_frame = tk.LabelFrame(main_frame, text="🔄 إدارة العمليات", 
                                  font=('Arial', 12, 'bold'),
                                  fg='#ffffff', bg='#2d2d2d')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 2))
        
        # أزرار التحكم في العمليات
        process_buttons_frame = tk.Frame(left_frame, bg='#2d2d2d')
        process_buttons_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(process_buttons_frame, text="🔄 تحديث", 
                 command=self.refresh_processes,
                 bg='#4ecdc4', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        tk.Button(process_buttons_frame, text="🚫 إغلاق المحدد",
                 command=self.kill_selected_process,
                 bg='#ff6b6b', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        tk.Button(process_buttons_frame, text="🧹 تنظيف شامل",
                 command=self.cleanup_all,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        # جدول العمليات
        self.process_tree = ttk.Treeview(left_frame, 
                                        columns=('PID', 'Name', 'CPU', 'Memory', 'Status'),
                                        show='tree headings')
        
        self.process_tree.heading('#0', text='العملية')
        self.process_tree.heading('PID', text='PID')
        self.process_tree.heading('Name', text='الاسم')
        self.process_tree.heading('CPU', text='المعالج %')
        self.process_tree.heading('Memory', text='الذاكرة MB')
        self.process_tree.heading('Status', text='الحالة')
        
        # تحديد عرض الأعمدة
        self.process_tree.column('#0', width=150)
        self.process_tree.column('PID', width=80)
        self.process_tree.column('Name', width=200)
        self.process_tree.column('CPU', width=80)
        self.process_tree.column('Memory', width=100)
        self.process_tree.column('Status', width=100)
        
        self.process_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # الجانب الأيمن - الإضافات
        right_frame = tk.LabelFrame(main_frame, text="🧩 إدارة الإضافات",
                                   font=('Arial', 12, 'bold'),
                                   fg='#ffffff', bg='#2d2d2d')
        right_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))
        
        # أزرار التحكم في الإضافات
        ext_buttons_frame = tk.Frame(right_frame, bg='#2d2d2d')
        ext_buttons_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(ext_buttons_frame, text="🔄 تحديث الإضافات",
                 command=self.refresh_extensions,
                 bg='#4ecdc4', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        tk.Button(ext_buttons_frame, text="❌ تعطيل المحدد",
                 command=self.disable_selected_extension,
                 bg='#f39c12', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        tk.Button(ext_buttons_frame, text="✅ تفعيل المحدد",
                 command=self.enable_selected_extension,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=2)
        
        # جدول الإضافات
        self.extensions_tree = ttk.Treeview(right_frame,
                                           columns=('ID', 'Version', 'Status', 'Publisher'),
                                           show='tree headings')
        
        self.extensions_tree.heading('#0', text='الإضافة')
        self.extensions_tree.heading('ID', text='المعرف')
        self.extensions_tree.heading('Version', text='الإصدار')
        self.extensions_tree.heading('Status', text='الحالة')
        self.extensions_tree.heading('Publisher', text='الناشر')
        
        self.extensions_tree.column('#0', width=200)
        self.extensions_tree.column('ID', width=150)
        self.extensions_tree.column('Version', width=80)
        self.extensions_tree.column('Status', width=80)
        self.extensions_tree.column('Publisher', width=120)
        
        self.extensions_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إطار السجل
        log_frame = tk.LabelFrame(self.root, text="📋 سجل العمليات",
                                 font=('Arial', 12, 'bold'),
                                 fg='#ffffff', bg='#2d2d2d')
        log_frame.pack(fill='x', padx=5, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8,
                                                 bg='#1e1e1e', fg='#ffffff',
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إطار التحكم السفلي
        control_frame = tk.Frame(self.root, bg='#2d2d2d')
        control_frame.pack(fill='x', padx=5, pady=5)
        
        # مراقبة تلقائية
        self.monitor_var = tk.BooleanVar()
        tk.Checkbutton(control_frame, text="مراقبة تلقائية (كل 5 ثواني)",
                      variable=self.monitor_var,
                      command=self.toggle_monitoring,
                      bg='#2d2d2d', fg='#ffffff',
                      font=('Arial', 10)).pack(side='left', padx=5)
        
        # أزرار إضافية
        tk.Button(control_frame, text="🚀 إعادة تشغيل VS Code",
                 command=self.restart_vscode,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold')).pack(side='right', padx=2)
        
        tk.Button(control_frame, text="⚡ تحسين الأداء",
                 command=self.optimize_performance,
                 bg='#9b59b6', fg='white', font=('Arial', 10, 'bold')).pack(side='right', padx=2)
        
        tk.Button(control_frame, text="💾 حفظ التقرير",
                 command=self.save_report,
                 bg='#34495e', fg='white', font=('Arial', 10, 'bold')).pack(side='right', padx=2)
    
    def log_message(self, message):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def refresh_processes(self):
        """تحديث قائمة العمليات"""
        self.log_message("🔄 تحديث قائمة العمليات...")
        
        # مسح البيانات السابقة
        for item in self.process_tree.get_children():
            self.process_tree.delete(item)
        
        self.process_data = []
        vscode_processes = 0
        total_memory = 0
        total_cpu = 0
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status']):
                try:
                    pinfo = proc.info
                    if any(keyword in pinfo['name'].lower() for keyword in 
                          ['code', 'node', 'electron', 'typescript', 'eslint']):
                        
                        memory_mb = pinfo['memory_info'].rss / 1024 / 1024
                        cpu_percent = pinfo['cpu_percent'] or 0
                        
                        self.process_data.append(pinfo)
                        vscode_processes += 1
                        total_memory += memory_mb
                        total_cpu += cpu_percent
                        
                        # إضافة إلى الجدول
                        self.process_tree.insert('', 'end',
                                               text=pinfo['name'],
                                               values=(pinfo['pid'],
                                                      pinfo['name'],
                                                      f"{cpu_percent:.1f}%",
                                                      f"{memory_mb:.1f}",
                                                      pinfo['status']))
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث العمليات: {e}")
        
        # تحديث الإحصائيات
        self.stats_labels['العمليات النشطة'].config(text=str(vscode_processes))
        self.stats_labels['استهلاك الذاكرة'].config(text=f"{total_memory:.0f} MB")
        self.stats_labels['استهلاك المعالج'].config(text=f"{total_cpu:.1f}%")
        
        self.log_message(f"✅ تم العثور على {vscode_processes} عملية")
    
    def refresh_extensions(self):
        """تحديث قائمة الإضافات"""
        self.log_message("🔄 تحديث قائمة الإضافات...")
        
        # مسح البيانات السابقة
        for item in self.extensions_tree.get_children():
            self.extensions_tree.delete(item)
        
        try:
            # الحصول على قائمة الإضافات
            result = subprocess.run(['code', '--list-extensions', '--show-versions'],
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                extensions = result.stdout.strip().split('\n')
                enabled_count = 0
                
                for ext in extensions:
                    if '@' in ext:
                        name, version = ext.rsplit('@', 1)
                        publisher = name.split('.')[0] if '.' in name else 'Unknown'
                        
                        self.extensions_tree.insert('', 'end',
                                                   text=name,
                                                   values=(name, version, 'مفعل', publisher))
                        enabled_count += 1
                
                self.stats_labels['الإضافات المفعلة'].config(text=str(enabled_count))
                self.log_message(f"✅ تم العثور على {enabled_count} إضافة مفعلة")
            else:
                self.log_message("❌ فشل في الحصول على قائمة الإضافات")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تحديث الإضافات: {e}")
    
    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.refresh_processes()
        self.refresh_extensions()
    
    def kill_selected_process(self):
        """إغلاق العملية المحددة"""
        selection = self.process_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية لإغلاقها")
            return
        
        item = self.process_tree.item(selection[0])
        pid = item['values'][0]
        name = item['values'][1]
        
        if messagebox.askyesno("تأكيد", f"هل تريد إغلاق العملية {name} (PID: {pid})؟"):
            try:
                proc = psutil.Process(pid)
                proc.terminate()
                self.log_message(f"✅ تم إغلاق العملية {name} (PID: {pid})")
                self.refresh_processes()
            except Exception as e:
                self.log_message(f"❌ فشل في إغلاق العملية: {e}")
    
    def cleanup_all(self):
        """تنظيف شامل لجميع العمليات"""
        if messagebox.askyesno("تأكيد", "هل تريد إغلاق جميع عمليات VS Code؟\n⚠️ تأكد من حفظ عملك!"):
            self.log_message("🧹 بدء التنظيف الشامل...")
            
            killed_count = 0
            for pinfo in self.process_data:
                try:
                    proc = psutil.Process(pinfo['pid'])
                    proc.terminate()
                    killed_count += 1
                except:
                    pass
            
            time.sleep(2)  # انتظار قصير
            self.refresh_processes()
            self.log_message(f"✅ تم إغلاق {killed_count} عملية")
    
    def disable_selected_extension(self):
        """تعطيل الإضافة المحددة"""
        selection = self.extensions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد إضافة لتعطيلها")
            return
        
        item = self.extensions_tree.item(selection[0])
        ext_id = item['values'][0]
        
        try:
            subprocess.run(['code', '--disable-extension', ext_id], check=True)
            self.log_message(f"✅ تم تعطيل الإضافة {ext_id}")
            self.refresh_extensions()
        except Exception as e:
            self.log_message(f"❌ فشل في تعطيل الإضافة: {e}")
    
    def enable_selected_extension(self):
        """تفعيل الإضافة المحددة"""
        selection = self.extensions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد إضافة لتفعيلها")
            return
        
        item = self.extensions_tree.item(selection[0])
        ext_id = item['values'][0]
        
        try:
            subprocess.run(['code', '--enable-extension', ext_id], check=True)
            self.log_message(f"✅ تم تفعيل الإضافة {ext_id}")
            self.refresh_extensions()
        except Exception as e:
            self.log_message(f"❌ فشل في تفعيل الإضافة: {e}")
    
    def restart_vscode(self):
        """إعادة تشغيل VS Code"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تشغيل VS Code؟"):
            self.log_message("🚀 إعادة تشغيل VS Code...")
            
            # إغلاق VS Code
            try:
                subprocess.run(['taskkill', '/IM', 'Code.exe', '/F'], check=False)
                time.sleep(3)
                
                # إعادة تشغيل
                subprocess.Popen(['code'])
                self.log_message("✅ تم إعادة تشغيل VS Code")
            except Exception as e:
                self.log_message(f"❌ فشل في إعادة التشغيل: {e}")
    
    def optimize_performance(self):
        """تحسين الأداء"""
        self.log_message("⚡ بدء تحسين الأداء...")

        optimization_steps = [
            "🧹 تنظيف العمليات المعلقة",
            "💾 تنظيف ذاكرة التخزين المؤقت",
            "🔄 إعادة تشغيل VS Code مع إعدادات محسنة",
            "⚡ تطبيق إعدادات الأداء"
        ]

        for step in optimization_steps:
            self.log_message(step)
            time.sleep(1)

        # تنظيف العمليات المعلقة
        killed_count = 0
        for pinfo in self.process_data:
            try:
                proc = psutil.Process(pinfo['pid'])
                if proc.cpu_percent() < 1.0:  # العمليات الخاملة
                    proc.terminate()
                    killed_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        self.log_message(f"🧹 تم إغلاق {killed_count} عملية خاملة")

        # إعادة تشغيل VS Code مع إعدادات محسنة
        try:
            subprocess.run(['taskkill', '/IM', 'Code.exe', '/F'], check=False)
            time.sleep(3)

            # تشغيل مع إعدادات محسنة
            subprocess.Popen(['code',
                             '--max-memory=4096',
                             '--disable-gpu',
                             '--disable-extensions'])
            self.log_message("✅ تم تحسين الأداء بنجاح")
        except Exception as e:
            self.log_message(f"❌ فشل في التحسين: {e}")

    def create_performance_profile(self):
        """إنشاء ملف تعريف الأداء"""
        try:
            # حساب إجمالي الذاكرة
            total_memory = 0
            for p in self.process_data:
                memory_info = p.get('memory_info', {})
                if hasattr(memory_info, 'rss'):
                    total_memory += memory_info.rss
                else:
                    total_memory += memory_info.get('rss', 0)

            profile_data = {
                "timestamp": datetime.now().isoformat(),
                "processes": len(self.process_data),
                "total_memory": total_memory / 1024 / 1024,
                "extensions_count": len(self.extensions_data),
                "recommendations": []
            }

            # توصيات الأداء
            if profile_data["processes"] > 20:
                profile_data["recommendations"].append(
                    "تقليل عدد العمليات النشطة")

            if profile_data["total_memory"] > 2048:
                profile_data["recommendations"].append(
                    "تقليل استهلاك الذاكرة")

            if profile_data["extensions_count"] > 30:
                profile_data["recommendations"].append(
                    "تعطيل الإضافات غير المستخدمة")

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_profile_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=2, ensure_ascii=False)

            self.log_message(f"📊 تم إنشاء ملف تعريف الأداء: {filename}")
            return filename

        except Exception as e:
            self.log_message(f"❌ فشل في إنشاء ملف تعريف الأداء: {e}")
            return None

    def save_report(self):
        """حفظ تقرير الحالة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vscode_report_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("📊 تقرير حالة VS Code\n")
                f.write("=" * 50 + "\n")
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"التاريخ: {current_time}\n\n")

                f.write("📈 الإحصائيات:\n")
                for stat, label in self.stats_labels.items():
                    f.write(f"  {stat}: {label.cget('text')}\n")

                f.write("\n🔄 العمليات النشطة:\n")
                for pinfo in self.process_data:
                    f.write(f"  PID: {pinfo['pid']}, Name: {pinfo['name']}\n")

                f.write("\n📋 السجل:\n")
                f.write(self.log_text.get(1.0, tk.END))

            self.log_message(f"💾 تم حفظ التقرير: {filename}")

        except Exception as e:
            self.log_message(f"❌ فشل في حفظ التقرير: {e}")

    def toggle_monitoring(self):
        """تفعيل/إلغاء المراقبة التلقائية"""
        if self.monitor_var.get():
            self.monitoring = True
            self.log_message("🔄 تم تفعيل المراقبة التلقائية")
            self.start_monitoring()
        else:
            self.monitoring = False
            self.log_message("⏹️ تم إيقاف المراقبة التلقائية")

    def start_monitoring(self):
        """بدء المراقبة التلقائية"""
        def monitor():
            while self.monitoring:
                self.refresh_data()
                time.sleep(5)

        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

    def run(self):
        """تشغيل التطبيق"""
        self.log_message("🎛️ تم تشغيل لوحة التحكم")
        self.root.mainloop()


if __name__ == "__main__":
    app = ProcessControlDashboard()
    app.run()
