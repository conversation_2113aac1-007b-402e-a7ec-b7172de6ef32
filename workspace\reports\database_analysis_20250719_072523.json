{"status": "completed", "databases_found": 3, "total_tables": 21, "total_records": 42, "database_analysis": {"C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 9, "table_analysis": {"projects": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 14, "table_analysis": {"projects": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 4, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 19, "table_analysis": {"projects": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 6, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}}, "recommendations": ["جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db"], "timestamp": "2025-07-19T07:25:23.358550"}