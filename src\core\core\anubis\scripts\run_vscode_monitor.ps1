# VS Code Process Monitor PowerShell Script
# سكريبت PowerShell لمراقبة عمليات VS Code

param(
    [string]$Action = "menu",
    [int]$Interval = 60,
    [switch]$Install,
    [switch]$Check,
    [switch]$Monitor,
    [switch]$Info
)

# الألوان للإخراج
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Show-Header {
    Write-ColorOutput "=" * 60 -Color $Colors.Header
    Write-ColorOutput "VS CODE PROCESS MONITOR - PowerShell Edition" -Color $Colors.Header
    Write-ColorOutput "=" * 60 -Color $Colors.Header
}

function Test-Dependencies {
    Write-ColorOutput "Checking dependencies..." -Color $Colors.Info
    
    # فحص Python
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Python found: $pythonVersion" -Color $Colors.Success
        } else {
            Write-ColorOutput "✗ Python not found" -Color $Colors.Error
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Python not found" -Color $Colors.Error
        return $false
    }
    
    # فحص psutil
    try {
        python -c "import psutil; print('psutil version:', psutil.__version__)" 2>&1 | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ psutil library found" -Color $Colors.Success
        } else {
            Write-ColorOutput "✗ psutil library not found" -Color $Colors.Error
            return $false
        }
    } catch {
        Write-ColorOutput "✗ psutil library not found" -Color $Colors.Error
        return $false
    }
    
    return $true
}

function Install-Dependencies {
    Write-ColorOutput "Installing dependencies..." -Color $Colors.Info
    
    try {
        python -m pip install psutil
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Dependencies installed successfully" -Color $Colors.Success
            return $true
        } else {
            Write-ColorOutput "✗ Failed to install dependencies" -Color $Colors.Error
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Error installing dependencies: $_" -Color $Colors.Error
        return $false
    }
}

function Get-SystemInfo {
    Write-ColorOutput "System Information:" -Color $Colors.Header
    Write-ColorOutput "-" * 30 -Color $Colors.Header
    
    # معلومات النظام الأساسية
    $os = Get-CimInstance Win32_OperatingSystem
    $cpu = Get-CimInstance Win32_Processor
    $memory = Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property Capacity -Sum
    
    Write-ColorOutput "OS: $($os.Caption) $($os.Version)" -Color $Colors.Info
    Write-ColorOutput "CPU: $($cpu.Name)" -Color $Colors.Info
    Write-ColorOutput "Memory: $([math]::Round($memory.Sum / 1GB, 2)) GB" -Color $Colors.Info
    
    # استهلاك الموارد الحالي
    $cpuUsage = (Get-Counter "\Processor(_Total)\% Processor Time").CounterSamples.CookedValue
    $memoryUsage = (Get-Counter "\Memory\% Committed Bytes In Use").CounterSamples.CookedValue
    
    Write-ColorOutput "Current CPU Usage: $([math]::Round($cpuUsage, 1))%" -Color $Colors.Info
    Write-ColorOutput "Current Memory Usage: $([math]::Round($memoryUsage, 1))%" -Color $Colors.Info
    
    # عمليات VS Code
    $vscodeProcesses = Get-Process | Where-Object { $_.ProcessName -like "*code*" -or $_.ProcessName -like "*electron*" }
    Write-ColorOutput "VS Code Processes: $($vscodeProcesses.Count)" -Color $Colors.Info
    
    if ($vscodeProcesses.Count -gt 0) {
        Write-ColorOutput "`nVS Code Process Details:" -Color $Colors.Header
        foreach ($proc in $vscodeProcesses | Select-Object -First 10) {
            $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
            Write-ColorOutput "  $($proc.ProcessName) (PID: $($proc.Id)) - Memory: $memoryMB MB" -Color $Colors.Info
        }
    }
    
    Write-ColorOutput "-" * 30 -Color $Colors.Header
}

function Start-SingleCheck {
    Write-ColorOutput "Running single VS Code process check..." -Color $Colors.Info
    
    if (-not (Test-Dependencies)) {
        Write-ColorOutput "Dependencies not met. Please install them first." -Color $Colors.Error
        return
    }
    
    try {
        $scriptPath = Join-Path $PSScriptRoot "vscode_process_monitor.py"
        if (Test-Path $scriptPath) {
            python $scriptPath
            Write-ColorOutput "✓ Check completed successfully!" -Color $Colors.Success
        } else {
            Write-ColorOutput "✗ Monitor script not found: $scriptPath" -Color $Colors.Error
        }
    } catch {
        Write-ColorOutput "✗ Error running check: $_" -Color $Colors.Error
    }
}

function Start-ContinuousMonitoring {
    Write-ColorOutput "Starting continuous monitoring..." -Color $Colors.Info
    Write-ColorOutput "Press Ctrl+C to stop" -Color $Colors.Warning
    
    if (-not (Test-Dependencies)) {
        Write-ColorOutput "Dependencies not met. Please install them first." -Color $Colors.Error
        return
    }
    
    try {
        $scriptPath = Join-Path $PSScriptRoot "vscode_process_alerts.py"
        if (Test-Path $scriptPath) {
            python $scriptPath --monitor
        } else {
            Write-ColorOutput "✗ Alerts script not found: $scriptPath" -Color $Colors.Error
        }
    } catch {
        Write-ColorOutput "✗ Error during monitoring: $_" -Color $Colors.Error
    }
}

function Show-RecentReports {
    $reportsDir = "Universal-AI-Assistants\reports"
    
    if (-not (Test-Path $reportsDir)) {
        Write-ColorOutput "No reports directory found" -Color $Colors.Warning
        return
    }
    
    $reportFiles = Get-ChildItem $reportsDir -Filter "vscode_monitor_report_*.json" | Sort-Object LastWriteTime -Descending
    
    if ($reportFiles.Count -eq 0) {
        Write-ColorOutput "No VS Code monitor reports found" -Color $Colors.Warning
        return
    }
    
    Write-ColorOutput "`nRecent VS Code Monitor Reports ($($reportFiles.Count) found):" -Color $Colors.Header
    Write-ColorOutput "-" * 50 -Color $Colors.Header
    
    foreach ($file in $reportFiles | Select-Object -First 10) {
        try {
            $content = Get-Content $file.FullName -Raw | ConvertFrom-Json
            $timestamp = $content.timestamp
            $vscodeProcesses = $content.summary.vscode_processes
            $systemHealth = $content.summary.system_health
            
            Write-ColorOutput "$($file.Name)" -Color $Colors.Info
            Write-ColorOutput "  Time: $timestamp" -Color $Colors.Info
            Write-ColorOutput "  VS Code Processes: $vscodeProcesses" -Color $Colors.Info
            Write-ColorOutput "  System Health: $systemHealth" -Color $Colors.Info
            Write-ColorOutput ""
        } catch {
            Write-ColorOutput "$($file.Name) (Error reading file)" -Color $Colors.Error
        }
    }
}

function Show-Menu {
    Write-ColorOutput "`n1. Run single check" -Color $Colors.Info
    Write-ColorOutput "2. Start continuous monitoring" -Color $Colors.Info
    Write-ColorOutput "3. Show system information" -Color $Colors.Info
    Write-ColorOutput "4. Install dependencies" -Color $Colors.Info
    Write-ColorOutput "5. View recent reports" -Color $Colors.Info
    Write-ColorOutput "0. Exit" -Color $Colors.Info
    Write-ColorOutput "=" * 60 -Color $Colors.Header
}

function Start-InteractiveMenu {
    while ($true) {
        Show-Header
        Show-Menu
        
        $choice = Read-Host "Enter your choice (0-5)"
        
        switch ($choice) {
            "0" {
                Write-ColorOutput "Goodbye!" -Color $Colors.Success
                break
            }
            "1" {
                Start-SingleCheck
            }
            "2" {
                Start-ContinuousMonitoring
            }
            "3" {
                Get-SystemInfo
            }
            "4" {
                Install-Dependencies
            }
            "5" {
                Show-RecentReports
            }
            default {
                Write-ColorOutput "Invalid choice. Please try again." -Color $Colors.Warning
            }
        }
        
        if ($choice -eq "0") { break }
        
        Write-ColorOutput "`nPress Enter to continue..." -Color $Colors.Info
        Read-Host
    }
}

# التحقق من المسار الحالي
if (-not (Test-Path "Universal-AI-Assistants")) {
    Write-ColorOutput "Warning: Not in the correct directory" -Color $Colors.Warning
    Write-ColorOutput "Please run this script from the project root directory" -Color $Colors.Warning
}

# تنفيذ الإجراء المطلوب
switch ($Action.ToLower()) {
    "install" {
        Show-Header
        Install-Dependencies
    }
    "check" {
        Show-Header
        Start-SingleCheck
    }
    "monitor" {
        Show-Header
        Start-ContinuousMonitoring
    }
    "info" {
        Show-Header
        Get-SystemInfo
    }
    "menu" {
        Start-InteractiveMenu
    }
    default {
        Start-InteractiveMenu
    }
}

# معالجة المعاملات المباشرة
if ($Install) {
    Show-Header
    Install-Dependencies
} elseif ($Check) {
    Show-Header
    Start-SingleCheck
} elseif ($Monitor) {
    Show-Header
    Start-ContinuousMonitoring
} elseif ($Info) {
    Show-Header
    Get-SystemInfo
}
