@echo off
color 0C
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █               🚨 EMERGENCY VS CODE KILLER 🚨                █
echo █                                                              █
echo █  WARNING: This will FORCE CLOSE ALL VS Code processes!      █
echo █  Make sure you SAVED ALL YOUR WORK before proceeding!       █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.
echo Current situation detected:
echo • Multiple Console Window Host processes
echo • Multiple Node.js JavaScript Runtime processes  
echo • VS Code consuming 26%% memory
echo • System performance severely impacted
echo.
echo ⚠️  CRITICAL: Save all your work before proceeding!
echo.
pause
echo.

echo 🔴 EMERGENCY CLEANUP STARTING...
echo ================================
echo.

echo Step 1: Killing VS Code main processes...
taskkill /IM "Code.exe" /F /T 2>nul
if %errorlevel%==0 (
    echo ✅ VS Code main processes terminated
) else (
    echo ⚠️  No VS Code main processes found
)
echo.

echo Step 2: Killing Node.js processes...
taskkill /IM "node.exe" /F /T 2>nul
if %errorlevel%==0 (
    echo ✅ Node.js processes terminated
) else (
    echo ⚠️  No Node.js processes found
)
echo.

echo Step 3: Killing Console Window Host processes...
taskkill /IM "conhost.exe" /F 2>nul
if %errorlevel%==0 (
    echo ✅ Console Window Host processes terminated
) else (
    echo ⚠️  No Console Window Host processes found
)
echo.

echo Step 4: Killing Electron processes...
taskkill /IM "electron.exe" /F /T 2>nul
if %errorlevel%==0 (
    echo ✅ Electron processes terminated
) else (
    echo ⚠️  No Electron processes found
)
echo.

echo Step 5: Killing any remaining VS Code related processes...
for /f "tokens=1" %%i in ('tasklist /fi "imagename eq Code*" /fo csv /nh 2^>nul') do (
    if not "%%i"=="" (
        taskkill /IM %%i /F 2>nul
        echo ✅ Killed remaining process: %%i
    )
)
echo.

echo Step 6: Waiting for system cleanup...
echo ⏳ Waiting 15 seconds for system to stabilize...
timeout /t 15 /nobreak >nul
echo.

echo Step 7: Verifying cleanup...
echo 🔍 Checking for remaining VS Code processes...
tasklist /fi "imagename eq Code.exe" /fo csv 2>nul | find /i "code.exe" >nul
if %errorlevel%==0 (
    echo ⚠️  Some VS Code processes still running - attempting force kill...
    wmic process where "name='Code.exe'" delete 2>nul
) else (
    echo ✅ No VS Code processes detected
)

tasklist /fi "imagename eq node.exe" /fo csv 2>nul | find /i "node.exe" >nul
if %errorlevel%==0 (
    echo ⚠️  Some Node.js processes still running - attempting force kill...
    wmic process where "name='node.exe'" delete 2>nul
) else (
    echo ✅ No Node.js processes detected
)
echo.

echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                  🎉 EMERGENCY CLEANUP COMPLETED! 🎉         █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.
echo 📊 NEXT STEPS:
echo ===============
echo 1. ✅ Emergency cleanup completed
echo 2. 🔄 System should be responsive now
echo 3. ⚡ Restart VS Code safely with: code --disable-extensions
echo 4. 🔧 Enable extensions one by one to identify problematic ones
echo 5. 📊 Monitor process count regularly
echo.
echo 💡 PREVENTION TIPS:
echo ===================
echo • Restart VS Code daily
echo • Close unused windows/tabs
echo • Disable unnecessary extensions
echo • Use smaller workspaces
echo • Monitor Task Manager regularly
echo.

echo 🚀 Ready to restart VS Code safely?
echo.
set /p restart="Start VS Code with minimal extensions? (Y/N): "
if /i "%restart%"=="Y" (
    echo.
    echo 🚀 Starting VS Code with disabled extensions...
    start "" "code" --disable-extensions
    echo ✅ VS Code started safely!
    echo 💡 You can now enable extensions one by one in Extensions panel
) else (
    echo.
    echo 👍 VS Code not started automatically.
    echo 💡 When ready, use: code --disable-extensions
)

echo.
echo ████████████████████████████████████████████████████████████████
echo █  Emergency cleanup completed at %date% %time%  █
echo ████████████████████████████████████████████████████████████████
echo.
pause
