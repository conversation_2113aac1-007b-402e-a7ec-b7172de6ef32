#!/bin/bash
# سكريبت تشغيل بيئة العمل المعزولة

echo "💼 بدء تشغيل بيئة العمل المعزولة..."

# التحقق من المتطلبات
echo "🔍 فحص المتطلبات..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# التحقق من الموارد
echo "💾 فحص الموارد المتاحة..."
AVAILABLE_MEMORY=$(free -m | awk 'NR==2{printf "%.0f", $7}')
if [ "$AVAILABLE_MEMORY" -lt 2048 ]; then
    echo "⚠️ تحذير: الذاكرة المتاحة أقل من 2GB"
fi

# الانتقال لمجلد بيئة العمل
cd workspace

# إنشاء البنية التحتية
echo "🏗️ إنشاء البنية التحتية لبيئة العمل..."
mkdir -p data logs reports projects notebooks configs temp scripts monitoring security
mkdir -p data/raw data/processed data/external
mkdir -p projects/active projects/archived projects/templates
mkdir -p notebooks/analysis notebooks/experiments notebooks/tutorials
mkdir -p logs/jupyter logs/application logs/system
mkdir -p security/certificates security/keys

# تعيين الصلاحيات
echo "🔒 تطبيق صلاحيات الأمان..."
chmod 700 security
chmod 750 data logs reports projects notebooks
chmod 755 configs scripts monitoring

# إنشاء ملف متغيرات البيئة
echo "⚙️ إنشاء ملف متغيرات البيئة..."
cat > .env << EOF
# متغيرات البيئة الآمنة لبيئة العمل
JUPYTER_TOKEN=$(openssl rand -hex 32)
WORKSPACE_DB_PASSWORD=$(openssl rand -base64 24)
WORKSPACE_SECRET_KEY=$(openssl rand -base64 32)
POSTGRES_PASSWORD=$(openssl rand -base64 20)
EOF

# إنشاء ملفات التكوين الأساسية
echo "📝 إنشاء ملفات التكوين..."

# إنشاء requirements.txt
cat > requirements.txt << EOF
# Data Science and Analysis
pandas>=2.0.0
numpy>=1.24.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Web Development
fastapi>=0.100.0
uvicorn>=0.22.0
streamlit>=1.24.0
requests>=2.31.0

# Development Tools
jupyter>=1.0.0
jupyterlab>=4.0.0
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Database
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
redis>=4.6.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
loguru>=0.7.0
EOF

# إنشاء الشبكات المعزولة
echo "🌐 إنشاء الشبكات المعزولة لبيئة العمل..."
docker network create anubis-workspace-net --driver bridge 2>/dev/null || true
docker network create anubis-workspace-data-net --driver bridge --internal 2>/dev/null || true

# بناء النظام
echo "🔨 بناء بيئة العمل..."
docker-compose build

# تشغيل خدمات البنية التحتية
echo "🗄️ تشغيل خدمات البنية التحتية..."
docker-compose up -d anubis-workspace-db anubis-workspace-redis anubis-workspace-monitor

# انتظار تجهيز الخدمات
echo "⏳ انتظار تجهيز خدمات البنية التحتية..."
sleep 30

# تشغيل بيئة العمل الرئيسية
echo "💼 تشغيل بيئة العمل الرئيسية..."
docker-compose up -d anubis-workspace

# تشغيل نظام النسخ الاحتياطي
echo "💾 تشغيل نظام النسخ الاحتياطي..."
docker-compose up -d anubis-workspace-backup

# التحقق من الحالة النهائية
echo "📊 فحص حالة النظام..."
sleep 20
docker-compose ps

# عرض معلومات الاتصال
echo ""
echo "✅ تم تشغيل بيئة العمل المعزولة بنجاح!"
echo ""
echo "🌐 الخدمات المتاحة:"
echo "   💼 Jupyter Lab: http://localhost:8888"
echo "   📊 Streamlit: http://localhost:8501" 
echo "   🚀 FastAPI: http://localhost:8000"
echo "   📈 مراقبة بيئة العمل: http://localhost:9094"
echo ""
echo "🔑 معلومات الوصول:"
JUPYTER_TOKEN=$(grep JUPYTER_TOKEN .env | cut -d'=' -f2)
echo "   🎫 Jupyter Token: $JUPYTER_TOKEN"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات الشاملة: docker-compose logs -f"
echo "   سجلات بيئة العمل: docker-compose logs -f anubis-workspace"
echo "   حالة النظام: docker-compose ps"
echo "   إيقاف النظام: docker-compose down"
echo ""
echo "🔒 ملاحظات الأمان:"
echo "   - جميع الخدمات معزولة في شبكات منفصلة"
echo "   - البيانات محمية ومشفرة"
echo "   - النسخ الاحتياطي يعمل يومياً"
echo "   - المراقبة نشطة على جميع المكونات"
echo ""
echo "📚 للبدء:"
echo "   1. افتح Jupyter Lab على المنفذ 8888"
echo "   2. استخدم الرمز المميز المعروض أعلاه"
echo "   3. ابدأ العمل في مجلد notebooks/"
