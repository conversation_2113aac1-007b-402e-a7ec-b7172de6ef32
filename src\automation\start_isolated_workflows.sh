#!/bin/bash
# سكريبت تشغيل نظام سير العمل والأتمتة المعزول

echo "🔄 بدء تشغيل نظام سير العمل والأتمتة المعزول..."

# التحقق من المتطلبات
echo "🔍 فحص المتطلبات..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# الانتقال لمجلد الأتمتة
cd workflows_and_automation

# إنشاء البنية التحتية
echo "🏗️ إنشاء البنية التحتية للأتمتة..."
mkdir -p data workflows credentials logs monitoring security
mkdir -p data/n8n data/database data/vault
mkdir -p logs/workflows logs/executions logs/security
mkdir -p security/certificates security/vault

# تعيين الصلاحيات الآمنة
echo "🔒 تطبيق صلاحيات الأمان..."
chmod 700 security credentials
chmod 750 data workflows logs
chmod 755 monitoring

# إنشاء ملف متغيرات البيئة
echo "⚙️ إنشاء ملف متغيرات البيئة..."
cat > .env << EOF
# متغيرات البيئة الآمنة لنظام الأتمتة
N8N_ENCRYPTION_KEY=$(openssl rand -base64 32)
WORKFLOWS_DB_PASSWORD=$(openssl rand -base64 24)
VAULT_ROOT_TOKEN=anubis-workflows-$(openssl rand -hex 16)
POSTGRES_PASSWORD=$(openssl rand -base64 20)
EOF

# إنشاء الشبكات المعزولة
echo "🌐 إنشاء الشبكات المعزولة للأتمتة..."
docker network create anubis-workflows-net --driver bridge 2>/dev/null || true
docker network create anubis-automation-secure-net --driver bridge --internal 2>/dev/null || true

# بناء النظام
echo "🔨 بناء نظام الأتمتة..."
docker-compose build

# تشغيل خدمات البنية التحتية
echo "🗄️ تشغيل خدمات البنية التحتية..."
docker-compose up -d anubis-workflows-db anubis-workflows-vault anubis-automation-monitor

# انتظار تجهيز الخدمات
echo "⏳ انتظار تجهيز خدمات البنية التحتية..."
sleep 30

# تشغيل نظام n8n الرئيسي
echo "🔄 تشغيل نظام n8n الرئيسي..."
docker-compose up -d anubis-n8n

# تشغيل مجدول المهام
echo "⏰ تشغيل مجدول المهام..."
docker-compose up -d anubis-automation-scheduler

# التحقق من الحالة النهائية
echo "📊 فحص حالة النظام..."
sleep 20
docker-compose ps

# عرض معلومات الاتصال
echo ""
echo "✅ تم تشغيل نظام سير العمل والأتمتة في بيئة معزولة!"
echo ""
echo "🌐 الخدمات المتاحة:"
echo "   🔄 واجهة n8n: http://localhost:5678"
echo "   📊 مراقبة الأتمتة: http://localhost:9093"
echo "   🔐 مخزن الأسرار: http://localhost:8201"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات الشاملة: docker-compose logs -f"
echo "   سجلات n8n: docker-compose logs -f anubis-n8n"
echo "   حالة النظام: docker-compose ps"
echo "   إيقاف النظام: docker-compose down"
echo ""
echo "🔒 ملاحظات الأمان:"
echo "   - جميع الخدمات معزولة في شبكات منفصلة"
echo "   - بيانات الاعتماد مشفرة في Vault"
echo "   - المراقبة نشطة على جميع المكونات"
echo "   - سجلات الأمان محفوظة ومراقبة"
echo ""
echo "📚 للمزيد من المعلومات:"
echo "   - دليل الاستخدام: README.md"
echo "   - إعدادات الأمان: security/"
echo "   - مراقبة الأداء: monitoring/"
