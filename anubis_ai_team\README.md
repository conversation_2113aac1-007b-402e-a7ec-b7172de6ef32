# 🏺 فريق الذكاء الاصطناعي - مشروع أنوبيس

## 📋 نظرة عامة
مجلد مخصص لإدارة فريق الذكاء الاصطناعي المساعد في مشروع أنوبيس، يحتوي على جميع الأدوات والآليات اللازمة للتعاون مع النماذج المحلية و Gemini CLI.

## 🤖 أعضاء الفريق

### 🚀 النماذج المحلية (Ollama):
1. **phi3:mini** - المحلل السريع ⚡
   - التخصص: تحليل سريع وإجابات مباشرة
   - الاستخدام: فحص الأخطاء، اقتراحات سريعة

2. **mistral:7b** - المطور الخبير 🔧
   - التخصص: البرمجة والتطوير المتقدم
   - الاستخدام: كتابة الكود، حل المشاكل التقنية

3. **llama3:8b** - المستشار الاستراتيجي 🎯
   - التخصص: التخطيط والاستراتيجية
   - الاستخدام: تخطيط المشاريع، اتخاذ القرارات

4. **strikegpt-r1-zero-8b** - المبدع والمبتكر 💡
   - التخصص: الحلول الإبداعية والابتكار
   - الاستخدام: حلول غير تقليدية، أفكار جديدة

5. **Qwen2.5-VL-7B** - المحلل البصري 👁️
   - التخصص: تحليل الصور والمحتوى البصري
   - الاستخدام: تحليل الواجهات، فهم الرسوم البيانية

### 🌟 المنسق الرئيسي:
- **Gemini CLI** - المنسق والمشرف العام
  - التخصص: التنسيق والإشراف العام
  - الاستخدام: إدارة المشروع، المراجعة النهائية

## 📁 محتويات المجلد

### 🛠️ الأدوات الأساسية:
- `team_workflow_manager.py` - مدير سير العمل الرئيسي (مع تكامل الذاكرة)
- `anubis_ai_team_collaboration_system.py` - نظام إدارة الفريق
- `anubis_ai_collaboration_helper.py` - مساعد التعاون المباشر

### 🧠 نظام الذاكرة والعقل:
- `anubis_team_memory/` - مجلد نظام الذاكرة الجماعية
- `anubis_team_memory/anubis_team_brain.py` - العقل المتكامل للفريق
- `anubis_team_memory/anubis_team_memory_manager.py` - مدير الذاكرة الأساسي
- `anubis_team_memory/anubis_pattern_analyzer.py` - محلل الأنماط
- `anubis_team_memory/anubis_adaptive_learning.py` - نظام التعلم التكيفي
- `anubis_team_memory/anubis_knowledge_search.py` - محرك البحث الذكي

### 📁 إدارة المسارات:
- `anubis_project_paths/` - مجلد إدارة مسارات المشروع (منقول من الجذر)
- `anubis_project_paths/project_paths_manager.py` - مدير المسارات الرئيسي
- `anubis_project_paths/project_navigation_helper.py` - مساعد التنقل

### 📋 الإعدادات والتخطيط:
- `anubis_ai_team_collaboration_plan.json` - خطة التعاون المحفوظة
- `README.md` - هذا الملف

### 🌟 أدوات Gemini:
- `anubis_gemini_cli_helper.py` - مساعد Gemini CLI
- `anubis_project_organization_gemini_request.md` - طلب تنظيم المشروع
- `anubis_gemini_docker_help_request.md` - طلب مساعدة Docker

## 🔄 آلية العمل

### 1. 🎯 تحديد المهمة:
```python
# استخدام مدير سير العمل
manager = AnubisTeamWorkflowManager()
workflow = manager.execute_full_workflow(
    task_type="development",  # أو analysis, planning, innovation, review
    task_description="وصف المهمة المطلوبة",
    priority="high"  # أو medium, low
)
```

### 2. 🤖 تنفيذ المراحل:
- **المرحلة الأساسية:** النموذج الرئيسي يحلل المهمة
- **المراحل الداعمة:** النماذج المساعدة تقدم الدعم
- **التنسيق:** Gemini CLI يراجع ويوجه
- **التنفيذ:** Augment Agent ينفذ الحل النهائي

### 3. 📊 أنواع المهام:

#### 🔍 **Analysis** (التحليل):
- **القائد:** phi3:mini
- **الداعمون:** mistral:7b
- **الاستخدام:** تحليل سريع، فحص الأخطاء

#### 🔧 **Development** (التطوير):
- **القائد:** mistral:7b
- **الداعمون:** phi3:mini, strikegpt-r1-zero-8b
- **الاستخدام:** كتابة الكود، تطوير الميزات

#### 🎯 **Planning** (التخطيط):
- **القائد:** llama3:8b
- **الداعمون:** gemini_cli
- **الاستخدام:** التخطيط الاستراتيجي، اتخاذ القرارات

#### 💡 **Innovation** (الابتكار):
- **القائد:** strikegpt-r1-zero-8b
- **الداعمون:** llama3:8b, Qwen2.5-VL-7B
- **الاستخدام:** حلول إبداعية، أفكار جديدة

#### 📋 **Review** (المراجعة):
- **القائد:** gemini_cli
- **الداعمون:** llama3:8b, mistral:7b
- **الاستخدام:** مراجعة شاملة، ضمان الجودة

## 🚀 الاستخدام السريع

### تشغيل سير عمل كامل:
```bash
cd anubis_ai_team
python team_workflow_manager.py
```

### تعاون مباشر:
```bash
python anubis_ai_collaboration_helper.py
```

### إدارة الفريق:
```bash
python anubis_ai_team_collaboration_system.py
```

## 📊 المخرجات

### 📄 ملفات سير العمل:
- `workflow_[task_id].json` - سجل كامل لسير العمل
- `gemini_coordination_request_[task_id].md` - طلب التنسيق لـ Gemini

### 📋 التقارير:
- سجل تنفيذ كل مرحلة
- أوقات الاستجابة
- معدلات النجاح
- التوصيات النهائية

## 🎯 أفضل الممارسات

### 1. 📋 اختيار نوع المهمة المناسب:
- استخدم "analysis" للفحص السريع
- استخدم "development" للبرمجة
- استخدم "planning" للتخطيط
- استخدم "innovation" للحلول الإبداعية
- استخدم "review" للمراجعة النهائية

### 2. 🎯 كتابة وصف واضح للمهمة:
- كن محدداً ومباشراً
- اذكر السياق والهدف
- حدد المتطلبات والقيود

### 3. 🔄 متابعة سير العمل:
- راجع ملفات JSON المُنشأة
- استخدم طلبات Gemini للتوجيه
- وثق النتائج والتعلم

## 🛠️ الصيانة والتطوير

### إضافة نموذج جديد:
1. تحديث `anubis_ai_team_collaboration_plan.json`
2. إضافة النموذج في `team_workflow_manager.py`
3. تحديث قوالب سير العمل

### تحسين الأداء:
- مراقبة أوقات الاستجابة
- تحليل معدلات النجاح
- تحسين prompts

---

**🏺 فريق الذكاء الاصطناعي - قوة جماعية لتطوير أنوبيس!**
