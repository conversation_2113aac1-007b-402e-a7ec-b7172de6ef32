#!/bin/bash
# سكريبت إيقاف نظام الذكاء الاصطناعي المعزول

echo "🛑 إيقاف نظام الذكاء الاصطناعي الشامل المعزول..."

cd universal_ai_system

# إيقاف جميع الخدمات
echo "📱 إيقاف جميع خدمات الذكاء الاصطناعي..."
docker-compose down

# إزالة الشبكات المؤقتة (اختياري)
echo "🌐 تنظيف الشبكات (اختياري)..."
read -p "هل تريد إزالة الشبكات المعزولة؟ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker network rm anubis-ai-net anubis-ai-models-net anubis-ai-data-net 2>/dev/null || true
    echo "✅ تم تنظيف الشبكات"
fi

echo "✅ تم إيقاف نظام الذكاء الاصطناعي بنجاح"
