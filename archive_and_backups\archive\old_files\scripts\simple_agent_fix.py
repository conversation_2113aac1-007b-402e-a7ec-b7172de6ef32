#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ إصلاح بسيط للوكلاء
Simple Agent Fix

إصلاح سريع ومباشر للوكلاء الثلاثة
"""

import os
import sys
from pathlib import Path


def create_enhanced_project_analyzer():
    """إنشاء ProjectAnalyzerAgent محسن"""
    code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 وكيل تحليل المشاريع المحسن
Enhanced Project Analyzer Agent
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedProjectAnalyzerAgent(BaseAgent):
    """📊 وكيل تحليل المشاريع المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_project_analyzer"
    
    def initialize_agent(self):
        """تهيئة وكيل تحليل المشاريع"""
        self.project_types = {
            'react': {
                'files': ['package.json', 'src', 'components'],
                'description': 'مشروع React'
            },
            'vue': {
                'files': ['package.json', 'src', 'components', 'vue.config.js'],
                'description': 'مشروع Vue.js'
            },
            'django': {
                'files': ['manage.py', 'settings.py', 'models.py'],
                'description': 'مشروع Django'
            },
            'fastapi': {
                'files': ['main.py', 'requirements.txt', 'app'],
                'description': 'مشروع FastAPI'
            },
            'nextjs': {
                'files': ['package.json', 'next.config.js', 'pages'],
                'description': 'مشروع Next.js'
            }
        }
        
        self.log_action("تهيئة محلل المشاريع المحسن", "جاهز للتحليل الشامل")
    
    def analyze_project(self, project_path=None):
        """تحليل شامل للمشروع"""
        target_path = Path(project_path) if project_path else self.project_path
        
        if not target_path.exists():
            return {'error': f'المشروع غير موجود: {target_path}'}
        
        analysis = {
            'project_path': str(target_path),
            'project_type': self._detect_project_type(target_path),
            'files_count': self._count_files(target_path),
            'structure': self._analyze_structure(target_path),
            'technologies': self._detect_technologies(target_path),
            'quality_score': self._calculate_quality_score(target_path),
            'recommendations': self._generate_recommendations(target_path)
        }
        
        self.log_action("تحليل المشروع", f"نوع المشروع: {analysis['project_type']}")
        return analysis
    
    def _detect_project_type(self, path):
        """كشف نوع المشروع"""
        for proj_type, config in self.project_types.items():
            indicators = config['files']
            matches = sum(1 for indicator in indicators if (path / indicator).exists())
            if matches >= len(indicators) // 2:  # نصف المؤشرات على الأقل
                return proj_type
        return 'unknown'
    
    def _count_files(self, path):
        """عد الملفات"""
        try:
            return len(list(path.rglob('*'))) if path.exists() else 0
        except:
            return 0
    
    def _analyze_structure(self, path):
        """تحليل هيكل المشروع"""
        structure = {}
        try:
            for item in path.iterdir():
                if item.name.startswith('.'):
                    continue
                if item.is_dir():
                    structure[item.name] = {
                        'type': 'directory',
                        'files_count': len(list(item.rglob('*')))
                    }
                else:
                    structure[item.name] = {
                        'type': 'file',
                        'size': item.stat().st_size
                    }
        except:
            pass
        return structure
    
    def _detect_technologies(self, path):
        """كشف التقنيات المستخدمة"""
        technologies = []
        
        # فحص ملفات التكوين
        config_files = {
            'package.json': 'Node.js/JavaScript',
            'requirements.txt': 'Python',
            'Dockerfile': 'Docker',
            'docker-compose.yml': 'Docker Compose'
        }
        
        for file_name, tech in config_files.items():
            if (path / file_name).exists():
                technologies.append(tech)
        
        return technologies
    
    def _calculate_quality_score(self, path):
        """حساب نقاط الجودة"""
        score = 0
        
        # فحص وجود ملفات مهمة
        important_files = ['README.md', 'LICENSE', '.gitignore']
        for file_name in important_files:
            if (path / file_name).exists():
                score += 20
        
        # فحص وجود مجلدات الاختبار
        test_folders = ['test', 'tests', '__tests__']
        for folder in test_folders:
            if (path / folder).exists():
                score += 30
                break
        
        return min(score, 100)
    
    def _generate_recommendations(self, path):
        """إنتاج توصيات التحسين"""
        recommendations = []
        
        if not (path / 'README.md').exists():
            recommendations.append("إضافة ملف README.md للتوثيق")
        
        if not (path / '.gitignore').exists():
            recommendations.append("إضافة ملف .gitignore")
        
        test_folders = ['test', 'tests', '__tests__']
        if not any((path / folder).exists() for folder in test_folders):
            recommendations.append("إضافة مجلد للاختبارات")
        
        if not recommendations:
            recommendations.append("المشروع منظم بشكل جيد!")
        
        return recommendations
'''
    
    return code


def create_enhanced_file_organizer():
    """إنشاء FileOrganizerAgent محسن"""
    code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📁 وكيل تنظيم الملفات المحسن
Enhanced File Organizer Agent
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedFileOrganizerAgent(BaseAgent):
    """📁 وكيل تنظيم الملفات المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_file_organizer"
    
    def initialize_agent(self):
        """تهيئة وكيل تنظيم الملفات"""
        self.file_categories = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
            'documents': ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
            'code': ['.py', '.js', '.ts', '.jsx', '.tsx', '.vue', '.html', '.css'],
            'config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.env'],
            'archives': ['.zip', '.rar', '.tar', '.gz', '.7z'],
            'videos': ['.mp4', '.avi', '.mkv', '.mov', '.wmv']
        }
        
        self.project_structures = {
            'react': {
                'folders': ['src', 'src/components', 'src/pages', 'src/hooks', 'public', 'tests'],
                'files': {
                    'package.json': '{"name": "react-app", "version": "1.0.0"}',
                    'src/App.js': 'import React from "react";\\n\\nfunction App() {\\n  return <div>Hello React!</div>;\\n}\\n\\nexport default App;'
                }
            },
            'vue': {
                'folders': ['src', 'src/components', 'src/views', 'src/router', 'public', 'tests'],
                'files': {
                    'package.json': '{"name": "vue-app", "version": "1.0.0"}',
                    'src/App.vue': '<template>\\n  <div>Hello Vue!</div>\\n</template>'
                }
            },
            'django': {
                'folders': ['app', 'templates', 'static', 'tests', 'media'],
                'files': {
                    'requirements.txt': 'Django>=4.0\\ndjango-cors-headers',
                    'manage.py': '#!/usr/bin/env python\\nimport os\\nimport sys'
                }
            },
            'fastapi': {
                'folders': ['app', 'tests', 'docs', 'static'],
                'files': {
                    'requirements.txt': 'fastapi>=0.68.0\\nuvicorn[standard]',
                    'main.py': 'from fastapi import FastAPI\\n\\napp = FastAPI()\\n\\<EMAIL>("/")\\ndef read_root():\\n    return {"Hello": "World"}'
                }
            }
        }
        
        self.log_action("تهيئة منظم الملفات المحسن", "جاهز للتنظيم الذكي")
    
    def organize_files(self, target_path=None):
        """تنظيم الملفات حسب النوع"""
        target_path = Path(target_path) if target_path else self.project_path
        
        if not target_path.exists():
            return {'error': f'المسار غير موجود: {target_path}'}
        
        organized = {
            'organized_files': 0,
            'created_folders': [],
            'categories': {},
            'skipped_files': []
        }
        
        for category, extensions in self.file_categories.items():
            category_path = target_path / category
            files_moved = 0
            
            for ext in extensions:
                files = list(target_path.glob(f'*{ext}'))
                if files:
                    category_path.mkdir(exist_ok=True)
                    if str(category_path) not in organized['created_folders']:
                        organized['created_folders'].append(str(category_path))
                    
                    for file in files:
                        try:
                            destination = category_path / file.name
                            if not destination.exists():
                                shutil.move(str(file), str(destination))
                                files_moved += 1
                            else:
                                organized['skipped_files'].append(str(file))
                        except Exception as e:
                            organized['skipped_files'].append(f"{file}: {e}")
            
            if files_moved > 0:
                organized['categories'][category] = files_moved
                organized['organized_files'] += files_moved
        
        self.log_action("تنظيم الملفات", f"تم تنظيم {organized['organized_files']} ملف")
        return organized
    
    def create_project_structure(self, project_type, project_name):
        """إنشاء هيكل مشروع جديد"""
        if project_type not in self.project_structures:
            return {'error': f'نوع المشروع غير مدعوم: {project_type}'}
        
        project_path = self.project_path / project_name
        structure = self.project_structures[project_type]
        
        try:
            # إنشاء المجلد الرئيسي
            project_path.mkdir(exist_ok=True)
            
            # إنشاء المجلدات
            created_folders = []
            for folder in structure['folders']:
                folder_path = project_path / folder
                folder_path.mkdir(parents=True, exist_ok=True)
                created_folders.append(str(folder_path))
            
            # إنشاء الملفات
            created_files = []
            for file_name, content in structure.get('files', {}).items():
                file_path = project_path / file_name
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content.replace('\\n', '\\n'))
                created_files.append(str(file_path))
            
            result = {
                'project_path': str(project_path),
                'project_type': project_type,
                'created_folders': created_folders,
                'created_files': created_files,
                'status': 'success'
            }
            
            self.log_action("إنشاء مشروع جديد", f"{project_type}: {project_name}")
            return result
            
        except Exception as e:
            return {'error': f'خطأ في إنشاء المشروع: {e}'}
    
    def clean_empty_folders(self, target_path=None):
        """تنظيف المجلدات الفارغة"""
        target_path = Path(target_path) if target_path else self.project_path
        
        removed_folders = []
        
        for folder in target_path.rglob('*'):
            if folder.is_dir():
                try:
                    if not any(folder.iterdir()):  # مجلد فارغ
                        folder.rmdir()
                        removed_folders.append(str(folder))
                except:
                    pass
        
        return {
            'removed_folders': removed_folders,
            'count': len(removed_folders)
        }
'''
    
    return code


def create_enhanced_memory_agent():
    """إنشاء MemoryAgent محسن"""
    code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 وكيل الذاكرة المحسن
Enhanced Memory Agent
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedMemoryAgent(BaseAgent):
    """🧠 وكيل الذاكرة المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_memory_agent"
    
    def initialize_agent(self):
        """تهيئة وكيل الذاكرة"""
        self.memory_file = self.project_path / 'memory.json'
        self.memory_data = self._load_memory()
        
        self.log_action("تهيئة وكيل الذاكرة المحسن", "الذاكرة جاهزة للاستخدام")
    
    def _load_memory(self):
        """تحميل الذاكرة من الملف"""
        if self.memory_file.exists():
            try:
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_memory(self):
        """حفظ الذاكرة في الملف"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory_data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def store_memory(self, key: str, data: Any, category: str = 'general'):
        """تخزين ذكرى جديدة"""
        memory_entry = {
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'category': category
        }
        
        self.memory_data[key] = memory_entry
        
        if self._save_memory():
            self.log_action("تخزين ذكرى", f"المفتاح: {key}")
            return {
                'status': 'stored',
                'key': key,
                'timestamp': memory_entry['timestamp']
            }
        else:
            return {'status': 'error', 'message': 'فشل في حفظ الذاكرة'}
    
    def retrieve_memory(self, key: str):
        """استرجاع ذكرى محددة"""
        if key in self.memory_data:
            memory = self.memory_data[key]
            self.log_action("استرجاع ذكرى", f"المفتاح: {key}")
            return {
                'key': key,
                'data': memory['data'],
                'timestamp': memory['timestamp'],
                'category': memory['category']
            }
        
        return None
    
    def search_memory(self, query: str):
        """البحث في الذاكرة"""
        results = []
        query_lower = query.lower()
        
        for key, memory in self.memory_data.items():
            # البحث في المفتاح
            if query_lower in key.lower():
                results.append({
                    'key': key,
                    'data': memory['data'],
                    'timestamp': memory['timestamp'],
                    'category': memory['category'],
                    'match_type': 'key'
                })
                continue
            
            # البحث في البيانات
            data_str = str(memory['data']).lower()
            if query_lower in data_str:
                results.append({
                    'key': key,
                    'data': memory['data'],
                    'timestamp': memory['timestamp'],
                    'category': memory['category'],
                    'match_type': 'data'
                })
        
        self.log_action("البحث في الذاكرة", f"الاستعلام: {query}, النتائج: {len(results)}")
        return results
    
    def get_all_memories(self):
        """الحصول على جميع الذكريات"""
        memories = []
        for key, memory in self.memory_data.items():
            memories.append({
                'key': key,
                'timestamp': memory['timestamp'],
                'category': memory['category'],
                'data_preview': str(memory['data'])[:100] + "..." if len(str(memory['data'])) > 100 else str(memory['data'])
            })
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        memories.sort(key=lambda x: x['timestamp'], reverse=True)
        return memories
    
    def delete_memory(self, key: str):
        """حذف ذكرى محددة"""
        if key in self.memory_data:
            del self.memory_data[key]
            if self._save_memory():
                self.log_action("حذف ذكرى", f"المفتاح: {key}")
                return {'status': 'deleted', 'key': key}
            else:
                return {'status': 'error', 'message': 'فشل في حفظ التغييرات'}
        
        return {'status': 'not_found', 'key': key}
    
    def get_memory_stats(self):
        """إحصائيات الذاكرة"""
        categories = {}
        for memory in self.memory_data.values():
            category = memory['category']
            categories[category] = categories.get(category, 0) + 1
        
        return {
            'total_memories': len(self.memory_data),
            'categories': categories,
            'memory_file': str(self.memory_file),
            'file_exists': self.memory_file.exists()
        }
'''
    
    return code


def main():
    """الدالة الرئيسية"""
    print("⚡ إصلاح بسيط للوكلاء الثلاثة")
    print("=" * 40)
    
    base_path = Path(__file__).parent.absolute()
    agents_path = base_path / 'agents'
    agents_path.mkdir(exist_ok=True)
    
    # إنشاء الوكلاء
    agents = [
        ('enhanced_project_analyzer', create_enhanced_project_analyzer()),
        ('enhanced_file_organizer', create_enhanced_file_organizer()),
        ('enhanced_memory_agent', create_enhanced_memory_agent())
    ]
    
    results = {}
    
    for agent_name, code in agents:
        try:
            file_path = agents_path / f"{agent_name}.py"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(code)
            
            results[agent_name] = str(file_path)
            print(f"✅ {agent_name}: {file_path}")
            
        except Exception as e:
            print(f"❌ خطأ في {agent_name}: {e}")
    
    print(f"\n🏆 تم إنشاء {len(results)} وكيل بنجاح!")
    
    # إنشاء ملف اختبار
    test_code = f'''#!/usr/bin/env python3
# اختبار الوكلاء المحسنة
import sys
sys.path.append('agents')

try:
    from enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
    from enhanced_file_organizer import EnhancedFileOrganizerAgent  
    from enhanced_memory_agent import EnhancedMemoryAgent
    
    print("✅ جميع الوكلاء تم استيرادها بنجاح!")
    
    # اختبار بسيط
    analyzer = EnhancedProjectAnalyzerAgent(".", {{}}, False)
    print("✅ ProjectAnalyzerAgent يعمل")
    
    organizer = EnhancedFileOrganizerAgent(".", {{}}, False)
    print("✅ FileOrganizerAgent يعمل")
    
    memory = EnhancedMemoryAgent(".", {{}}, False)
    print("✅ MemoryAgent يعمل")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {{e}}")
'''
    
    test_file = base_path / 'test_fixed_agents.py'
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"📝 تم إنشاء ملف الاختبار: {test_file}")
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
