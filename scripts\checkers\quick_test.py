#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار سريع لنظام أنوبيس المحسن
Quick Test for Anubis Enhanced System
"""

import requests
import json
from datetime import datetime

def test_endpoint(method, url, data=None, description=""):
    """اختبار endpoint واحد"""
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        status = "✅" if response.status_code == 200 else "❌"
        print(f"{status} {method} {url}")
        print(f"   Status: {response.status_code}")
        
        if description:
            print(f"   {description}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if isinstance(result, dict) and len(result) <= 5:
                    print(f"   Response: {result}")
                else:
                    print(f"   Response: {type(result)} with {len(result) if hasattr(result, '__len__') else 'unknown'} items")
            except:
                print(f"   Response: {response.text[:100]}...")
        else:
            print(f"   Error: {response.text[:100]}")
        
        print()
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ {method} {url}")
        print(f"   Error: {e}")
        print()
        return False

def main():
    """الاختبار الرئيسي"""
    base_url = "http://localhost:8000"
    
    print("🧪 اختبار سريع لنظام أنوبيس المحسن")
    print("=" * 50)
    print(f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        # اختبارات أساسية
        ("GET", f"{base_url}/health", None, "فحص الصحة"),
        ("GET", f"{base_url}/status", None, "حالة النظام"),
        
        # اختبارات قاعدة البيانات
        ("GET", f"{base_url}/api/v2/database/status", None, "حالة قاعدة البيانات"),
        ("POST", f"{base_url}/api/v2/database/initialize?database=sqlite", {}, "تهيئة SQLite"),
        
        # اختبارات المستخدمين
        ("GET", f"{base_url}/api/v2/users", None, "قائمة المستخدمين"),
        ("POST", f"{base_url}/api/v2/users/register", {
            "username": f"test_user_{int(datetime.now().timestamp())}",
            "email": f"test_{int(datetime.now().timestamp())}@example.com",
            "password": "testpassword123"
        }, "تسجيل مستخدم جديد"),
        
        # اختبارات الذكاء الاصطناعي
        ("POST", f"{base_url}/api/v2/ai/generate", {
            "prompt": "مرحبا، كيف حالك؟",
            "model": "gpt-3.5-turbo",
            "max_tokens": 50
        }, "إنتاج نص بالذكاء الاصطناعي"),
        
        ("GET", f"{base_url}/api/v2/ai/sessions", None, "جلسات الذكاء الاصطناعي"),
        
        # اختبارات النظام
        ("GET", f"{base_url}/api/v2/system/stats", None, "إحصائيات النظام"),
        ("GET", f"{base_url}/api/v2/monitoring/health", None, "فحص صحة مفصل"),
    ]
    
    successful_tests = 0
    total_tests = len(tests)
    
    for method, url, data, description in tests:
        if test_endpoint(method, url, data, description):
            successful_tests += 1
    
    print("=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   إجمالي الاختبارات: {total_tests}")
    print(f"   ✅ نجح: {successful_tests}")
    print(f"   ❌ فشل: {total_tests - successful_tests}")
    print(f"   📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("🚀 نظام أنوبيس المحسن يعمل بشكل مثالي!")
    else:
        print(f"\n⚠️ {total_tests - successful_tests} اختبار فشل")
        print("🔧 قد تحتاج لمراجعة النظام")

if __name__ == "__main__":
    main()
