#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗄️ Database Agent - وكيل قاعدة البيانات
يدير عمليات قاعدة البيانات والتحليل
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent


class DatabaseAgent(BaseAgent):
    """وكيل قاعدة البيانات"""

    def get_agent_type(self) -> str:
        return "database_agent"

    def initialize_agent(self):
        """تهيئة وكيل قاعدة البيانات"""
        self.db_path = self.project_path / "database.db"
        self.supported_formats = ['.db', '.sqlite', '.sqlite3']
        self.analysis_results = {}
        
        self.log_action("تم تهيئة وكيل قاعدة البيانات")

    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل قاعدة البيانات"""
        try:
            self.log_action("بدء تحليل قاعدة البيانات")
            
            # البحث عن ملفات قاعدة البيانات
            db_files = self.find_database_files()
            
            # تحليل كل قاعدة بيانات
            analysis_results = {}
            for db_file in db_files:
                analysis_results[str(db_file)] = self.analyze_database(db_file)
            
            # إحصائيات عامة
            total_tables = sum(len(result.get('tables', [])) for result in analysis_results.values())
            total_records = sum(result.get('total_records', 0) for result in analysis_results.values())
            
            result = {
                "status": "completed",
                "databases_found": len(db_files),
                "total_tables": total_tables,
                "total_records": total_records,
                "database_analysis": analysis_results,
                "recommendations": self.get_database_recommendations(analysis_results),
                "timestamp": datetime.now().isoformat()
            }
            
            # حفظ التقرير
            self.save_report(result, f"database_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            self.log_action("تم إكمال تحليل قاعدة البيانات")
            return result
            
        except Exception as e:
            self.log_action("خطأ في تحليل قاعدة البيانات", str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def find_database_files(self) -> List[Path]:
        """البحث عن ملفات قاعدة البيانات"""
        db_files = []
        
        for ext in self.supported_formats:
            db_files.extend(self.project_path.rglob(f"*{ext}"))
        
        self.log_action(f"تم العثور على {len(db_files)} ملف قاعدة بيانات")
        return db_files

    def analyze_database(self, db_path: Path) -> Dict[str, Any]:
        """تحليل قاعدة بيانات واحدة"""
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            # تحليل كل جدول
            table_analysis = {}
            total_records = 0
            
            for table in tables:
                # عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                record_count = cursor.fetchone()[0]
                total_records += record_count
                
                # هيكل الجدول
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                table_analysis[table] = {
                    "record_count": record_count,
                    "columns": [{"name": col[1], "type": col[2], "not_null": bool(col[3])} for col in columns],
                    "column_count": len(columns)
                }
            
            conn.close()
            
            return {
                "file_path": str(db_path),
                "file_size": db_path.stat().st_size,
                "tables": tables,
                "table_count": len(tables),
                "total_records": total_records,
                "table_analysis": table_analysis,
                "status": "success"
            }
            
        except Exception as e:
            return {
                "file_path": str(db_path),
                "status": "error",
                "error": str(e)
            }

    def get_database_recommendations(self, analysis_results: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات لتحسين قاعدة البيانات"""
        recommendations = []
        
        for db_path, analysis in analysis_results.items():
            if analysis.get("status") == "error":
                recommendations.append(f"إصلاح قاعدة البيانات: {db_path}")
                continue
                
            # توصيات بناءً على التحليل
            if analysis.get("table_count", 0) == 0:
                recommendations.append(f"قاعدة البيانات فارغة: {db_path}")
            
            if analysis.get("total_records", 0) > 100000:
                recommendations.append(f"قاعدة بيانات كبيرة تحتاج تحسين: {db_path}")
            
            # فحص الجداول
            for table_name, table_info in analysis.get("table_analysis", {}).items():
                if table_info.get("record_count", 0) == 0:
                    recommendations.append(f"جدول فارغ: {table_name} في {db_path}")
                
                if table_info.get("column_count", 0) > 20:
                    recommendations.append(f"جدول معقد: {table_name} يحتوي على {table_info['column_count']} عمود")
        
        return recommendations

    def create_database_backup(self, db_path: Path) -> str:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            backup_dir = self.workspace_dir / "backups"
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"{db_path.stem}_backup_{timestamp}.db"
            
            # نسخ قاعدة البيانات
            import shutil
            shutil.copy2(db_path, backup_path)
            
            self.log_action(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.log_action("خطأ في إنشاء النسخة الاحتياطية", str(e))
            raise

    def optimize_database(self, db_path: Path) -> Dict[str, Any]:
        """تحسين قاعدة البيانات"""
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # تنفيذ VACUUM لتحسين قاعدة البيانات
            cursor.execute("VACUUM")
            
            # إعادة تحليل الإحصائيات
            cursor.execute("ANALYZE")
            
            conn.close()
            
            self.log_action(f"تم تحسين قاعدة البيانات: {db_path}")
            return {"status": "success", "message": "تم تحسين قاعدة البيانات"}
            
        except Exception as e:
            self.log_action("خطأ في تحسين قاعدة البيانات", str(e))
            return {"status": "error", "error": str(e)}

    def validate_database_integrity(self, db_path: Path) -> Dict[str, Any]:
        """التحقق من سلامة قاعدة البيانات"""
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # فحص سلامة قاعدة البيانات
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchall()
            
            conn.close()
            
            is_valid = len(integrity_result) == 1 and integrity_result[0][0] == "ok"
            
            return {
                "status": "success",
                "is_valid": is_valid,
                "integrity_check": integrity_result,
                "message": "قاعدة البيانات سليمة" if is_valid else "توجد مشاكل في قاعدة البيانات"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "is_valid": False
            }