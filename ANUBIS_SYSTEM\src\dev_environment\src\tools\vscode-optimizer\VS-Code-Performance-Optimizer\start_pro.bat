@echo off
chcp 65001 >nul
color 0A
title VS Code Control Center Pro - Task Manager Advanced

cls
echo.
echo ================================================================
echo                VS Code Control Center Pro                      
echo                Task Manager Advanced                           
echo ================================================================
echo.
echo Starting advanced interface...
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed!
    echo Please download and install Python from: https://python.org
    pause
    exit /b 1
)

echo Python OK

REM Check basic libraries
echo Checking required libraries...
python -c "import tkinter, psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing basic libraries...
    pip install psutil
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install basic libraries!
        echo Try manually: pip install psutil
        pause
        exit /b 1
    )
)

REM Check AI agent libraries (optional)
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing AI agent libraries...
    pip install requests >nul 2>&1
    if %errorlevel% neq 0 (
        echo WARNING: Will run in basic mode (without AI agents)
    ) else (
        echo AI agent libraries installed
    )
) else (
    echo AI agent libraries available
)

echo.
echo Advanced features available:
echo    - Process display like real Task Manager
echo    - Interactive chat with system
echo    - Full process control (stop/start/kill)
echo    - Advanced search and filtering
echo    - Real-time network and security monitoring
echo    - Advanced cleaning and optimization tools
echo    - Detailed reports and data export

python -c "import sys; sys.path.append('agents'); from agents.agent_coordinator import AgentCoordinator" >nul 2>&1
if %errorlevel% equ 0 (
    echo    - 6 intelligent agents for advanced analysis
    echo    - Chat with Gemini and Ollama
    echo    - Custom and intelligent recommendations
    echo    - Cross-analysis from multiple agents
)

echo.
echo Starting VS Code Control Center Pro...
echo.

REM Run the advanced application
python vscode_control_center_pro.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start application!
    echo Make sure file exists: vscode_control_center_pro.py
    echo Or check if required libraries are installed
    echo You can try the basic interface: run.bat
    pause
    exit /b 1
)

echo.
echo VS Code Control Center Pro closed
echo Thank you for using the advanced interface!
pause
