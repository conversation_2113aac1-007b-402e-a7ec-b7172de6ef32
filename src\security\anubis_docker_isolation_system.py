#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام العزل المتقدم بـ Docker - أنوبيس
Anubis Advanced Docker Isolation System
"""

import docker
import json
import subprocess
import time
from pathlib import Path
from datetime import datetime
import yaml

class AnubisDockerIsolation:
    """نظام العزل المتقدم بـ Docker"""
    
    def __init__(self):
        self.client = None
        self.containers = {}
        self.networks = {}
        self.volumes = {}
        self.config = self.load_config()
        
    def load_config(self):
        """تحميل إعدادات النظام"""
        config = {
            "isolation_network": "anubis-isolation-net",
            "base_image": "python:3.11-slim",
            "security_opts": [
                "no-new-privileges:true",
                "seccomp=unconfined"
            ],
            "resource_limits": {
                "memory": "512m",
                "cpus": "0.5",
                "pids_limit": 100
            },
            "volumes": {
                "anubis-data": "/app/data",
                "anubis-logs": "/app/logs",
                "anubis-config": "/app/config"
            }
        }
        return config
    
    def check_docker(self):
        """فحص توفر Docker"""
        try:
            self.client = docker.from_env()
            info = self.client.info()
            print(f"✅ Docker متصل - الإصدار: {info.get('ServerVersion', 'غير معروف')}")
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ Docker: {e}")
            return False
    
    def create_isolation_network(self):
        """إنشاء شبكة العزل"""
        try:
            network_name = self.config["isolation_network"]
            
            # فحص وجود الشبكة
            try:
                network = self.client.networks.get(network_name)
                print(f"✅ شبكة العزل موجودة: {network_name}")
                return network
            except docker.errors.NotFound:
                pass
            
            # إنشاء شبكة جديدة
            network = self.client.networks.create(
                name=network_name,
                driver="bridge",
                options={
                    "com.docker.network.bridge.enable_icc": "false",
                    "com.docker.network.bridge.enable_ip_masquerade": "true"
                },
                labels={
                    "anubis.system": "isolation",
                    "anubis.created": datetime.now().isoformat()
                }
            )
            
            print(f"✅ تم إنشاء شبكة العزل: {network_name}")
            self.networks[network_name] = network
            return network
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء شبكة العزل: {e}")
            return None
    
    def create_volumes(self):
        """إنشاء الأحجام المطلوبة"""
        created_volumes = {}
        
        for volume_name, mount_point in self.config["volumes"].items():
            try:
                # فحص وجود الحجم
                try:
                    volume = self.client.volumes.get(volume_name)
                    print(f"✅ الحجم موجود: {volume_name}")
                except docker.errors.NotFound:
                    # إنشاء حجم جديد
                    volume = self.client.volumes.create(
                        name=volume_name,
                        labels={
                            "anubis.system": "isolation",
                            "anubis.mount": mount_point,
                            "anubis.created": datetime.now().isoformat()
                        }
                    )
                    print(f"✅ تم إنشاء الحجم: {volume_name}")
                
                created_volumes[volume_name] = volume
                self.volumes[volume_name] = volume
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء الحجم {volume_name}: {e}")
        
        return created_volumes
    
    def create_dockerfile(self, service_name, requirements=None):
        """إنشاء Dockerfile مخصص"""
        dockerfile_content = f"""
# 🏺 Anubis Isolated Service: {service_name}
FROM {self.config["base_image"]}

# إعداد المستخدم غير المميز
RUN groupadd -r anubis && useradd -r -g anubis anubis

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \\
    curl \\
    wget \\
    git \\
    && rm -rf /var/lib/apt/lists/*

# إنشاء مجلدات العمل
RUN mkdir -p /app/data /app/logs /app/config
RUN chown -R anubis:anubis /app

# تثبيت متطلبات Python
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r /app/requirements.txt

# نسخ الكود
COPY . /app/
RUN chown -R anubis:anubis /app

# التبديل للمستخدم غير المميز
USER anubis

# مجلد العمل
WORKDIR /app

# المنفذ الافتراضي
EXPOSE 8080

# نقطة الدخول
ENTRYPOINT ["python", "main.py"]
"""
        
        # إنشاء ملف requirements.txt
        if requirements is None:
            requirements = [
                "fastapi==0.104.1",
                "uvicorn==0.24.0",
                "requests==2.31.0",
                "pydantic==2.5.0",
                "python-multipart==0.0.6"
            ]
        
        requirements_content = "\n".join(requirements)
        
        return dockerfile_content, requirements_content
    
    def build_isolated_image(self, service_name, dockerfile_content, requirements_content):
        """بناء صورة معزولة"""
        try:
            # إنشاء مجلد مؤقت
            build_dir = Path(f"temp_build_{service_name}")
            build_dir.mkdir(exist_ok=True)
            
            # كتابة الملفات
            (build_dir / "Dockerfile").write_text(dockerfile_content)
            (build_dir / "requirements.txt").write_text(requirements_content)
            
            # إنشاء main.py بسيط
            main_py = """
import uvicorn
from fastapi import FastAPI

app = FastAPI(title="Anubis Isolated Service")

@app.get("/")
async def root():
    return {"message": "Anubis Isolated Service Running", "status": "healthy"}

@app.get("/health")
async def health():
    return {"status": "ok", "service": "anubis-isolated"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
"""
            (build_dir / "main.py").write_text(main_py)
            
            # بناء الصورة
            image_tag = f"anubis-{service_name}:latest"
            print(f"🔨 بناء الصورة: {image_tag}")
            
            image, logs = self.client.images.build(
                path=str(build_dir),
                tag=image_tag,
                rm=True,
                labels={
                    "anubis.system": "isolation",
                    "anubis.service": service_name,
                    "anubis.created": datetime.now().isoformat()
                }
            )
            
            print(f"✅ تم بناء الصورة: {image_tag}")
            
            # تنظيف المجلد المؤقت
            import shutil
            shutil.rmtree(build_dir)
            
            return image
            
        except Exception as e:
            print(f"❌ خطأ في بناء الصورة: {e}")
            return None
    
    def create_isolated_container(self, service_name, image, port=8080):
        """إنشاء حاوية معزولة"""
        try:
            container_name = f"anubis-{service_name}-isolated"
            
            # إعداد الأحجام
            volumes = {}
            for volume_name, mount_point in self.config["volumes"].items():
                volumes[volume_name] = {"bind": mount_point, "mode": "rw"}
            
            # إعداد الشبكة
            network_name = self.config["isolation_network"]
            
            # إنشاء الحاوية
            container = self.client.containers.run(
                image=image.id,
                name=container_name,
                detach=True,
                ports={f"{port}/tcp": port},
                volumes=volumes,
                network=network_name,
                security_opt=self.config["security_opts"],
                mem_limit=self.config["resource_limits"]["memory"],
                cpuset_cpus="0",
                pids_limit=self.config["resource_limits"]["pids_limit"],
                read_only=False,
                tmpfs={"/tmp": "noexec,nosuid,size=100m"},
                labels={
                    "anubis.system": "isolation",
                    "anubis.service": service_name,
                    "anubis.created": datetime.now().isoformat(),
                    "anubis.port": str(port)
                },
                environment={
                    "ANUBIS_SERVICE": service_name,
                    "ANUBIS_ISOLATED": "true",
                    "PYTHONUNBUFFERED": "1"
                },
                restart_policy={"Name": "unless-stopped"}
            )
            
            print(f"✅ تم إنشاء الحاوية المعزولة: {container_name}")
            self.containers[service_name] = container
            
            return container
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الحاوية: {e}")
            return None
    
    def monitor_container(self, container):
        """مراقبة الحاوية"""
        try:
            # انتظار بدء التشغيل
            print("⏳ انتظار بدء الخدمة...")
            time.sleep(5)
            
            # فحص الحالة
            container.reload()
            status = container.status
            
            if status == "running":
                print(f"✅ الحاوية تعمل: {container.name}")
                
                # عرض السجلات
                logs = container.logs(tail=10).decode('utf-8')
                if logs:
                    print("📋 آخر السجلات:")
                    for line in logs.split('\n')[-5:]:
                        if line.strip():
                            print(f"   {line}")
                
                # عرض معلومات الشبكة
                container.reload()
                networks = container.attrs['NetworkSettings']['Networks']
                for net_name, net_info in networks.items():
                    ip = net_info.get('IPAddress', 'غير محدد')
                    print(f"🌐 الشبكة {net_name}: {ip}")
                
                return True
            else:
                print(f"❌ الحاوية لا تعمل: {status}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في مراقبة الحاوية: {e}")
            return False
    
    def create_docker_compose(self, services):
        """إنشاء ملف docker-compose للنظام المعزول"""
        compose_config = {
            "version": "3.8",
            "services": {},
            "networks": {
                self.config["isolation_network"]: {
                    "driver": "bridge",
                    "driver_opts": {
                        "com.docker.network.bridge.enable_icc": "false"
                    }
                }
            },
            "volumes": {}
        }
        
        # إضافة الأحجام
        for volume_name in self.config["volumes"].keys():
            compose_config["volumes"][volume_name] = {
                "driver": "local"
            }
        
        # إضافة الخدمات
        for service_name in services:
            compose_config["services"][f"anubis-{service_name}"] = {
                "build": {
                    "context": f"./services/{service_name}",
                    "dockerfile": "Dockerfile"
                },
                "container_name": f"anubis-{service_name}-isolated",
                "restart": "unless-stopped",
                "networks": [self.config["isolation_network"]],
                "volumes": [
                    f"{vol}:{mount}" for vol, mount in self.config["volumes"].items()
                ],
                "security_opt": self.config["security_opts"],
                "mem_limit": self.config["resource_limits"]["memory"],
                "cpus": self.config["resource_limits"]["cpus"],
                "pids_limit": self.config["resource_limits"]["pids_limit"],
                "read_only": False,
                "tmpfs": ["/tmp:noexec,nosuid,size=100m"],
                "environment": {
                    "ANUBIS_SERVICE": service_name,
                    "ANUBIS_ISOLATED": "true",
                    "PYTHONUNBUFFERED": "1"
                },
                "labels": {
                    "anubis.system": "isolation",
                    "anubis.service": service_name
                }
            }
        
        return compose_config
    
    def save_docker_compose(self, compose_config, filename="docker-compose-isolation.yml"):
        """حفظ ملف docker-compose"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                yaml.dump(compose_config, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ تم حفظ ملف Docker Compose: {filename}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ ملف Docker Compose: {e}")
            return False
    
    def cleanup_isolation(self):
        """تنظيف نظام العزل"""
        print("🧹 تنظيف نظام العزل...")
        
        # إيقاف وحذف الحاويات
        for service_name, container in self.containers.items():
            try:
                container.stop()
                container.remove()
                print(f"✅ تم حذف الحاوية: {service_name}")
            except Exception as e:
                print(f"⚠️  خطأ في حذف الحاوية {service_name}: {e}")
        
        # حذف الشبكة
        try:
            network = self.client.networks.get(self.config["isolation_network"])
            network.remove()
            print(f"✅ تم حذف الشبكة: {self.config['isolation_network']}")
        except Exception as e:
            print(f"⚠️  خطأ في حذف الشبكة: {e}")
        
        print("✅ تم تنظيف نظام العزل")
    
    def run_isolation_system(self, services=None):
        """تشغيل نظام العزل الكامل"""
        if services is None:
            services = ["api", "worker", "monitor"]
        
        print("🏺 بدء نظام العزل المتقدم - أنوبيس")
        print("=" * 50)
        
        # فحص Docker
        if not self.check_docker():
            return False
        
        # إنشاء الشبكة
        if not self.create_isolation_network():
            return False
        
        # إنشاء الأحجام
        self.create_volumes()
        
        # إنشاء وتشغيل الخدمات
        for service_name in services:
            print(f"\n🔧 إعداد خدمة: {service_name}")
            
            # إنشاء Dockerfile
            dockerfile, requirements = self.create_dockerfile(service_name)
            
            # بناء الصورة
            image = self.build_isolated_image(service_name, dockerfile, requirements)
            if not image:
                continue
            
            # إنشاء الحاوية
            container = self.create_isolated_container(service_name, image)
            if not container:
                continue
            
            # مراقبة الحاوية
            self.monitor_container(container)
        
        # إنشاء ملف docker-compose
        compose_config = self.create_docker_compose(services)
        self.save_docker_compose(compose_config)
        
        print(f"\n🎉 تم تشغيل نظام العزل بنجاح!")
        print(f"📊 الخدمات النشطة: {len(self.containers)}")
        print(f"🌐 الشبكة: {self.config['isolation_network']}")
        print(f"💾 الأحجام: {len(self.volumes)}")
        
        return True

def main():
    """الدالة الرئيسية"""
    isolation = AnubisDockerIsolation()
    
    try:
        # تشغيل النظام
        success = isolation.run_isolation_system()
        
        if success:
            print("\n⚠️  اضغط Ctrl+C لإيقاف النظام وتنظيفه")
            while True:
                time.sleep(10)
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        isolation.cleanup_isolation()
    except Exception as e:
        print(f"❌ خطأ في النظام: {e}")
        isolation.cleanup_isolation()

if __name__ == "__main__":
    main()
