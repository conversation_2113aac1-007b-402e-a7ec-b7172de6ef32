#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نقاط النهاية المحسنة لنظام أنوبيس
Anubis Enhanced Endpoints

endpoints جديدة مع دعم قاعدة البيانات والذكاء الاصطناعي
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import json
import asyncio
import uuid
import logging

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from data_management.simple_database_manager import simple_db_manager as db_manager

# إعداد الراوتر
router = APIRouter(prefix="/api/v2", tags=["Enhanced API"])

# إعداد السجلات
logger = logging.getLogger("anubis_enhanced_api")

# نماذج البيانات
class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=8)

class AIRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=5000)
    model: str = Field(default="gpt-3.5-turbo")
    max_tokens: Optional[int] = Field(default=1000, ge=1, le=4000)
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0)
    user_id: Optional[int] = None

class DatabaseQuery(BaseModel):
    query: str = Field(..., min_length=1)
    params: Optional[List] = Field(default=[])
    database: str = Field(default="sqlite", pattern="^(mysql|sqlite)$")

class SystemCommand(BaseModel):
    command: str = Field(..., min_length=1)
    parameters: Optional[Dict[str, Any]] = Field(default={})
    async_execution: bool = Field(default=False)

# ===== نقاط النهاية للمستخدمين =====

@router.post("/users/register")
async def register_user(user: UserCreate):
    """تسجيل مستخدم جديد"""
    try:
        # فحص وجود المستخدم
        existing_user = await db_manager.execute_query(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            (user.username, user.email)
        )
        
        if existing_user:
            raise HTTPException(
                status_code=400, 
                detail="اسم المستخدم أو البريد الإلكتروني موجود بالفعل"
            )
        
        # تشفير كلمة المرور (مبسط للتجربة)
        import hashlib
        password_hash = hashlib.sha256(user.password.encode()).hexdigest()
        
        # إدراج المستخدم الجديد
        await db_manager.execute_non_query(
            """INSERT INTO users (username, email, password_hash)
               VALUES (?, ?, ?)""",
            (user.username, user.email, password_hash)
        )
        
        return {
            "status": "success",
            "message": "تم تسجيل المستخدم بنجاح",
            "username": user.username,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تسجيل المستخدم: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

@router.get("/users/{user_id}")
async def get_user(user_id: int):
    """الحصول على معلومات المستخدم"""
    try:
        user = await db_manager.execute_query(
            "SELECT id, username, email, created_at, is_active FROM users WHERE id = ?",
            (user_id,)
        )
        
        if not user:
            raise HTTPException(status_code=404, detail="المستخدم غير موجود")
        
        return {
            "status": "success",
            "user": user[0],
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في جلب المستخدم: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

@router.get("/users")
async def list_users(limit: int = 10, offset: int = 0):
    """قائمة المستخدمين"""
    try:
        users = await db_manager.execute_query(
            """SELECT id, username, email, created_at, is_active
               FROM users
               ORDER BY created_at DESC
               LIMIT ? OFFSET ?""",
            (limit, offset)
        )

        # عدد المستخدمين الإجمالي
        total_count = await db_manager.execute_query(
            "SELECT COUNT(*) as count FROM users"
        )
        
        return {
            "status": "success",
            "users": users,
            "total_count": total_count[0]["count"] if total_count else 0,
            "limit": limit,
            "offset": offset,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في جلب قائمة المستخدمين: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

# ===== نقاط النهاية للذكاء الاصطناعي =====

@router.post("/ai/generate")
async def generate_ai_response(request: AIRequest, background_tasks: BackgroundTasks):
    """إنتاج استجابة ذكية"""
    try:
        session_id = str(uuid.uuid4())
        
        # محاكاة استجابة الذكاء الاصطناعي
        ai_response = await simulate_ai_response(request.prompt, request.model)
        
        # حفظ الجلسة في قاعدة البيانات (في الخلفية)
        background_tasks.add_task(
            save_ai_session,
            request.user_id,
            session_id,
            request.model,
            request.prompt,
            ai_response["response"],
            ai_response["tokens_used"]
        )
        
        return {
            "status": "success",
            "session_id": session_id,
            "model": request.model,
            "prompt": request.prompt,
            "response": ai_response["response"],
            "tokens_used": ai_response["tokens_used"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في إنتاج الاستجابة: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

async def simulate_ai_response(prompt: str, model: str) -> Dict[str, Any]:
    """محاكاة استجابة الذكاء الاصطناعي"""
    # محاكاة تأخير المعالجة
    await asyncio.sleep(0.5)
    
    responses = {
        "gpt-4": f"استجابة متقدمة من GPT-4 للسؤال: {prompt}",
        "gpt-3.5-turbo": f"استجابة سريعة من GPT-3.5 للسؤال: {prompt}",
        "gemini-pro": f"استجابة ذكية من Gemini Pro للسؤال: {prompt}",
        "claude-3": f"استجابة تحليلية من Claude-3 للسؤال: {prompt}"
    }
    
    response_text = responses.get(model, f"استجابة عامة للسؤال: {prompt}")
    tokens_used = len(prompt.split()) + len(response_text.split())
    
    return {
        "response": response_text,
        "tokens_used": tokens_used
    }

async def save_ai_session(user_id: Optional[int], session_id: str, model: str, 
                         prompt: str, response: str, tokens_used: int):
    """حفظ جلسة الذكاء الاصطناعي"""
    try:
        await db_manager.execute_non_query(
            """INSERT INTO ai_sessions
               (user_id, session_id, model_name, prompt, response, tokens_used)
               VALUES (?, ?, ?, ?, ?, ?)""",
            (user_id, session_id, model, prompt, response, tokens_used)
        )
        logger.info(f"تم حفظ جلسة AI: {session_id}")
        
    except Exception as e:
        logger.error(f"خطأ في حفظ جلسة AI: {e}")

@router.get("/ai/sessions")
async def get_ai_sessions(user_id: Optional[int] = None, limit: int = 10):
    """الحصول على جلسات الذكاء الاصطناعي"""
    try:
        if user_id:
            sessions = await db_manager.execute_query(
                """SELECT session_id, model_name, prompt, response, tokens_used, created_at
                   FROM ai_sessions
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )
        else:
            sessions = await db_manager.execute_query(
                """SELECT session_id, model_name, prompt, response, tokens_used, created_at
                   FROM ai_sessions
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (limit,)
            )
        
        return {
            "status": "success",
            "sessions": sessions,
            "count": len(sessions),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في جلب جلسات AI: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

# ===== نقاط النهاية لقاعدة البيانات =====

@router.post("/database/query")
async def execute_database_query(query_request: DatabaseQuery):
    """تنفيذ استعلام قاعدة بيانات"""
    try:
        # فحص أمان الاستعلام (مبسط)
        dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER']
        if any(keyword in query_request.query.upper() for keyword in dangerous_keywords):
            raise HTTPException(
                status_code=403, 
                detail="استعلامات خطيرة غير مسموحة"
            )
        
        results = await db_manager.execute_query(
            query_request.query,
            tuple(query_request.params) if query_request.params else (),
            query_request.database
        )
        
        return {
            "status": "success",
            "database": query_request.database,
            "query": query_request.query,
            "results": results,
            "count": len(results),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في قاعدة البيانات: {str(e)}")

@router.get("/database/status")
async def get_database_status():
    """حالة قواعد البيانات"""
    try:
        status = await db_manager.get_connection_status()
        
        return {
            "status": "success",
            "databases": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في فحص حالة قاعدة البيانات: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

@router.post("/database/initialize")
async def initialize_database(database: str = "mysql"):
    """تهيئة قاعدة البيانات"""
    try:
        if database == "mysql":
            success = await db_manager.initialize_mysql()
        elif database == "sqlite":
            success = await db_manager.initialize_sqlite()
        else:
            raise HTTPException(status_code=400, detail="نوع قاعدة بيانات غير مدعوم")
        
        if success:
            # إنشاء الجداول
            await db_manager.create_tables(database)
            
            return {
                "status": "success",
                "message": f"تم تهيئة قاعدة البيانات {database} بنجاح",
                "database": database,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail=f"فشل في تهيئة قاعدة البيانات {database}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

# ===== نقاط النهاية للنظام =====

@router.post("/system/command")
async def execute_system_command(command: SystemCommand, background_tasks: BackgroundTasks):
    """تنفيذ أوامر النظام"""
    try:
        if command.async_execution:
            # تنفيذ غير متزامن
            background_tasks.add_task(process_system_command, command)
            return {
                "status": "accepted",
                "message": "تم قبول الأمر للتنفيذ في الخلفية",
                "command": command.command,
                "timestamp": datetime.now().isoformat()
            }
        else:
            # تنفيذ متزامن
            result = await process_system_command(command)
            return {
                "status": "success",
                "command": command.command,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        
    except Exception as e:
        logger.error(f"خطأ في تنفيذ أمر النظام: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

async def process_system_command(command: SystemCommand) -> Dict[str, Any]:
    """معالجة أوامر النظام"""
    try:
        if command.command == "health_check":
            return {"status": "healthy", "uptime": "running"}
        elif command.command == "clear_cache":
            return {"status": "cache_cleared", "items_removed": 0}
        elif command.command == "backup_database":
            return {"status": "backup_completed", "backup_file": "backup_" + datetime.now().strftime("%Y%m%d_%H%M%S")}
        else:
            return {"status": "unknown_command", "message": "أمر غير معروف"}
            
    except Exception as e:
        logger.error(f"خطأ في معالجة الأمر: {e}")
        return {"status": "error", "message": str(e)}

@router.get("/system/stats")
async def get_system_stats():
    """إحصائيات النظام"""
    try:
        # إحصائيات المستخدمين
        user_stats = await db_manager.execute_query(
            "SELECT COUNT(*) as total_users FROM users"
        )
        
        # إحصائيات جلسات AI
        ai_stats = await db_manager.execute_query(
            "SELECT COUNT(*) as total_sessions, SUM(tokens_used) as total_tokens FROM ai_sessions"
        )
        
        # إحصائيات السجلات
        log_stats = await db_manager.execute_query(
            "SELECT COUNT(*) as total_logs FROM system_logs"
        )
        
        return {
            "status": "success",
            "stats": {
                "users": user_stats[0] if user_stats else {"total_users": 0},
                "ai_sessions": ai_stats[0] if ai_stats else {"total_sessions": 0, "total_tokens": 0},
                "system_logs": log_stats[0] if log_stats else {"total_logs": 0}
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"خطأ في جلب إحصائيات النظام: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في النظام: {str(e)}")

# ===== نقاط النهاية للمراقبة =====

@router.get("/monitoring/health")
async def detailed_health_check():
    """فحص صحة مفصل"""
    try:
        db_status = await db_manager.get_connection_status()
        
        health_status = {
            "overall": "healthy",
            "components": {
                "database": {
                    "mysql": "healthy" if db_status["mysql"]["connected"] else "unhealthy",
                    "sqlite": "healthy" if db_status["sqlite"]["connected"] else "unhealthy"
                },
                "api": "healthy",
                "memory": "normal",
                "disk": "normal"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # تحديد الحالة العامة
        if not any([db_status["mysql"]["connected"], db_status["sqlite"]["connected"]]):
            health_status["overall"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"خطأ في فحص الصحة: {e}")
        return {
            "overall": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
