#!/usr/bin/env python3
"""
Quick VS Code Process Check
فحص سريع لعمليات VS Code

A simple, fast script to check VS Code processes without complex setup.
"""

import json
import os
from datetime import datetime

import psutil


def quick_system_check():
    """فحص سريع للنظام"""
    print("🔍 Quick VS Code Process Check")
    print("=" * 40)

    # معلومات النظام الأساسية
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()

    print(f"💻 System Status:")
    print(f"   CPU Usage: {cpu_percent:.1f}%")
    print(f"   Memory Usage: {memory.percent:.1f}%")
    print(f"   Available Memory: {memory.available / (1024**3):.1f} GB")

    return cpu_percent, memory.percent


def find_vscode_processes():
    """البحث عن عمليات VS Code"""
    vscode_processes = []
    total_memory = 0
    total_cpu = 0

    print(f"\n🔎 Searching for VS Code processes...")

    for proc in psutil.process_iter(["pid", "name", "memory_percent", "cpu_percent"]):
        try:
            proc_info = proc.info
            proc_name = proc_info["name"].lower()

            # البحث عن عمليات VS Code
            if any(keyword in proc_name for keyword in ["code", "electron"]):
                memory_mb = proc.memory_info().rss / (1024 * 1024)

                process_data = {
                    "pid": proc_info["pid"],
                    "name": proc_info["name"],
                    "memory_percent": proc_info["memory_percent"],
                    "memory_mb": memory_mb,
                    "cpu_percent": proc_info["cpu_percent"],
                }

                vscode_processes.append(process_data)
                total_memory += proc_info["memory_percent"]
                total_cpu += proc_info["cpu_percent"]

        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    return vscode_processes, total_memory, total_cpu


def display_results(processes, total_memory, total_cpu, system_cpu, system_memory):
    """عرض النتائج"""
    print(f"\n📊 Results:")
    print(f"   VS Code Processes Found: {len(processes)}")
    print(f"   Total VS Code Memory: {total_memory:.1f}%")
    print(f"   Total VS Code CPU: {total_cpu:.1f}%")

    if processes:
        print(f"\n📋 Process Details:")
        print(f"{'PID':<8} {'Name':<20} {'Memory':<10} {'CPU':<8}")
        print("-" * 50)

        # ترتيب حسب استهلاك الذاكرة
        sorted_processes = sorted(processes, key=lambda x: x["memory_mb"], reverse=True)

        for proc in sorted_processes[:10]:  # أعلى 10 عمليات
            print(
                f"{proc['pid']:<8} {proc['name']:<20} {proc['memory_mb']:.1f} MB{'':<2} {proc['cpu_percent']:.1f}%"
            )

    # تحليل الحالة
    print(f"\n🎯 Analysis:")

    if system_cpu > 80:
        print("   ⚠️  High system CPU usage detected!")
    elif system_cpu > 60:
        print("   ⚡ Moderate system CPU usage")
    else:
        print("   ✅ System CPU usage is normal")

    if system_memory > 80:
        print("   ⚠️  High system memory usage detected!")
    elif system_memory > 60:
        print("   ⚡ Moderate system memory usage")
    else:
        print("   ✅ System memory usage is normal")

    if total_memory > 30:
        print("   ⚠️  VS Code using significant memory!")
    elif total_memory > 15:
        print("   ⚡ VS Code memory usage is moderate")
    else:
        print("   ✅ VS Code memory usage is normal")

    if len(processes) > 15:
        print("   ⚠️  Many VS Code processes running!")
    elif len(processes) > 8:
        print("   ⚡ Several VS Code processes running")
    else:
        print("   ✅ Normal number of VS Code processes")


def save_quick_report(processes, total_memory, total_cpu, system_cpu, system_memory):
    """حفظ تقرير سريع"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "system": {"cpu_percent": system_cpu, "memory_percent": system_memory},
        "vscode": {
            "process_count": len(processes),
            "total_memory_percent": total_memory,
            "total_cpu_percent": total_cpu,
            "processes": processes,
        },
    }

    # إنشاء مجلد التقارير إذا لم يكن موجوداً
    reports_dir = "Universal-AI-Assistants/reports"
    os.makedirs(reports_dir, exist_ok=True)

    # حفظ التقرير
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{reports_dir}/quick_vscode_check_{timestamp}.json"

    with open(filename, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"\n💾 Report saved: {filename}")
    return filename


def get_recommendations(processes, total_memory, total_cpu, system_cpu, system_memory):
    """الحصول على توصيات"""
    recommendations = []

    if system_cpu > 80:
        recommendations.append("Consider closing some applications to reduce CPU load")

    if system_memory > 80:
        recommendations.append("Consider closing some applications to free memory")

    if total_memory > 30:
        recommendations.append("VS Code is using a lot of memory - consider closing unused windows")

    if len(processes) > 15:
        recommendations.append("Many VS Code processes detected - restart VS Code to consolidate")

    if total_cpu > 20:
        recommendations.append("VS Code is using significant CPU - check for heavy extensions")

    if recommendations:
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    else:
        print(f"\n✨ Everything looks good! No recommendations needed.")


def main():
    """الدالة الرئيسية"""
    try:
        # فحص النظام
        system_cpu, system_memory = quick_system_check()

        # البحث عن عمليات VS Code
        processes, total_memory, total_cpu = find_vscode_processes()

        # عرض النتائج
        display_results(processes, total_memory, total_cpu, system_cpu, system_memory)

        # حفظ التقرير
        filename = save_quick_report(processes, total_memory, total_cpu, system_cpu, system_memory)

        # التوصيات
        get_recommendations(processes, total_memory, total_cpu, system_cpu, system_memory)

        print(f"\n🎉 Quick check completed!")
        print(f"📄 Full report: {filename}")

    except KeyboardInterrupt:
        print(f"\n👋 Check cancelled by user")
    except Exception as e:
        print(f"\n❌ Error during check: {e}")
        print(f"Make sure you have the required permissions and psutil is installed")
        print(f"Install with: pip install psutil")


if __name__ == "__main__":
    main()
