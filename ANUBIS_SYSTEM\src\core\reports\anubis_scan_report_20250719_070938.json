{"scan_timestamp": "2025-07-19T07:09:38.161246", "project_root": "anubis", "components": {"agents": {"directory_exists": true, "agents_found": ["database_agent", "enhanced_error_detector", "enhanced_file_organizer"], "code_quality_issues": [], "error_handling_check": [], "performance_issues": [], "score": 75}, "database": {"directory_exists": true, "config_files": [], "security_issues": [], "connection_files": [], "schema_files": [], "score": 60}, "api": {"directory_exists": true, "api_files": ["anubis_api_server.py", "__init__.py"], "openapi_spec": "مو<PERSON><PERSON><PERSON>", "endpoints_found": ["/api/v1/models/ollama/generate", "/api/v1/models/gemini/generate", "/api/v1/agents/run", "/api/v1/collaboration/analyze", "/api/v1/database/projects", "/api/v1/langsmith/trace"], "security_features": [], "documentation_issues": [], "score": 90}, "plugins": {"directory_exists": true, "plugin_manager_exists": true, "base_plugin_exists": true, "plugins_found": ["example_plugin"], "isolation_issues": [], "security_concerns": [], "score": 85}, "tests": {"directory_exists": true, "test_files": ["test_agents", "test_ai_fixed", "test_ai_integration", "test_anubis_system", "test_enhanced_error_detector", "test_error_detector", "test_fixed_agents", "test_jewelry_database", "test_jewelry_logic", "test_plugins", "test_project_analyzer", "test_smart_analyzer", "test_system", "comprehensive_agents_test", "comprehensive_system_test", "quick_ai_test"], "test_runner_exists": true, "coverage_estimate": 100, "test_quality_issues": [], "environment_isolation": [], "score": 70}, "integrations": {"langsmith_config": true, "ollama_integration": false, "gemini_integration": false, "config_security": [], "connectivity_issues": [], "score": 50}, "main_file": {"file_exists": true, "imports_check": ["استيراد النظام الأساسي: ✓"], "error_handling": ["معالجة الأخطاء: ✓"], "configuration": [], "score": 60}}, "issues": [], "recommendations": ["🟢 agents: حالة جيدة (النقاط: 75)", "🟡 database: يحتاج تحسين (النقاط: 60)", "🟢 api: حالة جيدة (النقاط: 90)", "🟢 plugins: حالة جيدة (النقاط: 85)", "🟡 tests: يحتاج تحسين (النقاط: 70)", "🟡 integrations: يحتاج تحسين (النقاط: 50)", "🟡 main_file: يحتا<PERSON> تحسين (النقاط: 60)"], "overall_score": 70, "gemini_analysis": "تم الفحص بالتعاون مع Gemini CLI"}