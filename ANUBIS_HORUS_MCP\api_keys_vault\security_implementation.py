#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 نظام تطبيق التوصيات الأمنية الفوري
Immediate Security Recommendations Implementation System

تطبيق شامل لجميع التوصيات الأمنية مع فريق حورس
Comprehensive implementation of all security recommendations with Horus team
"""

import os
import json
import hashlib
import secrets
import base64
from pathlib import Path
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('security_implementation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HorusSecurityImplementation:
    """🔐 نظام تطبيق الأمان مع فريق حورس"""
    
    def __init__(self):
        """تهيئة نظام الأمان"""
        self.vault_dir = Path(__file__).parent / "vault"
        self.vault_dir.mkdir(exist_ok=True)
        
        self.secure_dir = self.vault_dir / "secure"
        self.secure_dir.mkdir(exist_ok=True)
        
        self.backup_dir = self.vault_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        self.master_key = None
        self.encryption_key = None
        
        logger.info("🔐 تم تهيئة نظام الأمان مع فريق حورس")
    
    def generate_master_key(self, password: str = None) -> str:
        """إنشاء المفتاح الرئيسي"""
        if not password:
            password = secrets.token_urlsafe(32)
        
        # إنشاء salt عشوائي
        salt = os.urandom(16)
        
        # إنشاء مفتاح التشفير
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        
        # حفظ المفتاح بشكل آمن
        master_key_file = self.secure_dir / "master.key"
        with open(master_key_file, 'wb') as f:
            f.write(salt + key)
        
        # تعيين صلاحيات محدودة
        os.chmod(master_key_file, 0o600)
        
        self.master_key = key
        self.encryption_key = Fernet(key)
        
        logger.info("🔑 تم إنشاء المفتاح الرئيسي بنجاح")
        return password
    
    def load_master_key(self, password: str) -> bool:
        """تحميل المفتاح الرئيسي"""
        try:
            master_key_file = self.secure_dir / "master.key"
            if not master_key_file.exists():
                return False
            
            with open(master_key_file, 'rb') as f:
                data = f.read()
            
            salt = data[:16]
            stored_key = data[16:]
            
            # إعادة إنشاء المفتاح
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            
            if key == stored_key:
                self.master_key = key
                self.encryption_key = Fernet(key)
                logger.info("✅ تم تحميل المفتاح الرئيسي بنجاح")
                return True
            else:
                logger.error("❌ كلمة مرور خاطئة")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المفتاح: {e}")
            return False
    
    def encrypt_api_key(self, api_key: str, metadata: dict = None) -> dict:
        """تشفير مفتاح API"""
        if not self.encryption_key:
            raise ValueError("لم يتم تحميل مفتاح التشفير")
        
        # تشفير المفتاح
        encrypted_key = self.encryption_key.encrypt(api_key.encode())
        
        # إنشاء hash للتحقق
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # إنشاء معرف فريد
        key_id = secrets.token_hex(16)
        
        encrypted_data = {
            "key_id": key_id,
            "encrypted_key": base64.b64encode(encrypted_key).decode(),
            "key_hash": key_hash,
            "created_at": datetime.now().isoformat(),
            "metadata": metadata or {},
            "encryption_version": "1.0"
        }
        
        logger.info(f"🔐 تم تشفير المفتاح: {key_id}")
        return encrypted_data
    
    def decrypt_api_key(self, encrypted_data: dict) -> str:
        """فك تشفير مفتاح API"""
        if not self.encryption_key:
            raise ValueError("لم يتم تحميل مفتاح التشفير")
        
        try:
            encrypted_key = base64.b64decode(encrypted_data["encrypted_key"])
            decrypted_key = self.encryption_key.decrypt(encrypted_key).decode()
            
            # التحقق من صحة المفتاح
            key_hash = hashlib.sha256(decrypted_key.encode()).hexdigest()
            if key_hash != encrypted_data["key_hash"]:
                raise ValueError("فشل في التحقق من صحة المفتاح")
            
            logger.info(f"🔓 تم فك تشفير المفتاح: {encrypted_data['key_id']}")
            return decrypted_key
            
        except Exception as e:
            logger.error(f"❌ خطأ في فك التشفير: {e}")
            raise
    
    def secure_key_storage(self, keys_collection: dict) -> str:
        """تخزين آمن لمجموعة المفاتيح"""
        if not self.encryption_key:
            raise ValueError("لم يتم تحميل مفتاح التشفير")
        
        # تشفير كل مفتاح في المجموعة
        secured_collection = {
            "metadata": keys_collection.get("metadata", {}),
            "encrypted_keys": {},
            "security_info": {
                "encryption_method": "AES-256",
                "created_at": datetime.now().isoformat(),
                "total_keys": 0
            }
        }
        
        total_keys = 0
        
        for platform, platform_data in keys_collection.get("api_keys_collection", {}).items():
            if platform in ["metadata", "security_notes", "usage_instructions"]:
                continue
            
            secured_collection["encrypted_keys"][platform] = {
                "platform": platform_data.get("platform", ""),
                "description": platform_data.get("description", ""),
                "keys": []
            }
            
            for key_info in platform_data.get("keys", []):
                encrypted_key_data = self.encrypt_api_key(
                    key_info["key"],
                    {
                        "name": key_info.get("name", ""),
                        "usage": key_info.get("usage", ""),
                        "platform": platform,
                        "status": key_info.get("status", "active")
                    }
                )
                
                secured_collection["encrypted_keys"][platform]["keys"].append({
                    "key_id": encrypted_key_data["key_id"],
                    "name": key_info.get("name", ""),
                    "usage": key_info.get("usage", ""),
                    "encrypted_data": encrypted_key_data,
                    "status": key_info.get("status", "active")
                })
                
                total_keys += 1
        
        secured_collection["security_info"]["total_keys"] = total_keys
        
        # حفظ المجموعة المشفرة
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        secure_file = self.secure_dir / f"encrypted_keys_{timestamp}.json"
        
        with open(secure_file, 'w', encoding='utf-8') as f:
            json.dump(secured_collection, f, indent=2, ensure_ascii=False)
        
        # تعيين صلاحيات محدودة
        os.chmod(secure_file, 0o600)
        
        logger.info(f"🔐 تم حفظ {total_keys} مفتاح مشفر في: {secure_file}")
        return str(secure_file)
    
    def create_environment_template(self, keys_collection: dict) -> str:
        """إنشاء قالب متغيرات البيئة"""
        env_template = []
        env_template.append("# 🔐 متغيرات البيئة الآمنة لمفاتيح API")
        env_template.append("# Secure Environment Variables for API Keys")
        env_template.append(f"# Generated: {datetime.now().isoformat()}")
        env_template.append("")
        
        for platform, platform_data in keys_collection.get("api_keys_collection", {}).items():
            if platform in ["metadata", "security_notes", "usage_instructions"]:
                continue
            
            env_template.append(f"# {platform_data.get('platform', platform)} Keys")
            env_template.append(f"# {platform_data.get('description', '')}")
            
            for i, key_info in enumerate(platform_data.get("keys", []), 1):
                key_name = key_info.get("name", f"{platform}_key_{i}")
                env_name = f"{platform.upper()}_{key_name.upper()}".replace("-", "_").replace(" ", "_")
                env_template.append(f"{env_name}=YOUR_ENCRYPTED_KEY_HERE")
            
            env_template.append("")
        
        # حفظ القالب
        env_file = self.secure_dir / "environment_template.env"
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(env_template))
        
        logger.info(f"📄 تم إنشاء قالب متغيرات البيئة: {env_file}")
        return str(env_file)
    
    def setup_access_monitoring(self) -> str:
        """إعداد مراقبة الوصول"""
        monitor_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
👁️ نظام مراقبة الوصول لمفاتيح API
API Keys Access Monitoring System
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

class AccessMonitor:
    def __init__(self):
        self.log_file = Path(__file__).parent / "access_log.json"
        self.alerts_file = Path(__file__).parent / "security_alerts.json"
        
    def log_access(self, key_id: str, action: str, user: str = None, ip: str = None):
        """تسجيل محاولة الوصول"""
        access_log = {
            "timestamp": datetime.now().isoformat(),
            "key_id": key_id,
            "action": action,
            "user": user or os.getenv("USER", "unknown"),
            "ip": ip or "localhost",
            "success": True
        }
        
        # حفظ السجل
        logs = []
        if self.log_file.exists():
            with open(self.log_file, 'r') as f:
                logs = json.load(f)
        
        logs.append(access_log)
        
        # الاحتفاظ بآخر 1000 سجل فقط
        if len(logs) > 1000:
            logs = logs[-1000:]
        
        with open(self.log_file, 'w') as f:
            json.dump(logs, f, indent=2)
    
    def check_suspicious_activity(self):
        """فحص النشاط المشبوه"""
        # يمكن إضافة منطق كشف النشاط المشبوه هنا
        pass

if __name__ == "__main__":
    monitor = AccessMonitor()
    print("👁️ نظام مراقبة الوصول جاهز")
'''
        
        monitor_file = self.secure_dir / "access_monitor.py"
        with open(monitor_file, 'w', encoding='utf-8') as f:
            f.write(monitor_script)
        
        os.chmod(monitor_file, 0o755)
        
        logger.info(f"👁️ تم إعداد نظام مراقبة الوصول: {monitor_file}")
        return str(monitor_file)
    
    def implement_immediate_security(self, keys_collection_file: str, master_password: str = None) -> dict:
        """تطبيق الأمان الفوري"""
        logger.info("🚀 بدء تطبيق التوصيات الأمنية الفورية")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "actions_completed": [],
            "files_created": [],
            "security_level": "high"
        }
        
        try:
            # 1. إنشاء المفتاح الرئيسي
            if not master_password:
                master_password = self.generate_master_key()
                results["actions_completed"].append("✅ تم إنشاء المفتاح الرئيسي")
            else:
                if not self.load_master_key(master_password):
                    master_password = self.generate_master_key(master_password)
                results["actions_completed"].append("✅ تم تحميل المفتاح الرئيسي")
            
            # 2. تحميل مجموعة المفاتيح
            with open(keys_collection_file, 'r', encoding='utf-8') as f:
                keys_collection = json.load(f)
            
            # 3. تشفير وحفظ المفاتيح
            secure_file = self.secure_key_storage(keys_collection)
            results["files_created"].append(secure_file)
            results["actions_completed"].append("🔐 تم تشفير جميع المفاتيح بـ AES-256")
            
            # 4. إنشاء قالب متغيرات البيئة
            env_file = self.create_environment_template(keys_collection)
            results["files_created"].append(env_file)
            results["actions_completed"].append("📄 تم إنشاء قالب متغيرات البيئة")
            
            # 5. إعداد مراقبة الوصول
            monitor_file = self.setup_access_monitoring()
            results["files_created"].append(monitor_file)
            results["actions_completed"].append("👁️ تم إعداد نظام مراقبة الوصول")
            
            # 6. إنشاء تقرير الأمان
            security_report = {
                "security_implementation": {
                    "timestamp": datetime.now().isoformat(),
                    "encryption_method": "AES-256",
                    "total_keys_secured": keys_collection.get("api_keys_collection", {}).get("metadata", {}).get("total_keys", 0),
                    "master_password": master_password,
                    "security_level": "maximum",
                    "recommendations_applied": [
                        "AES-256 encryption for all keys",
                        "Secure file permissions (600)",
                        "Environment variables template",
                        "Access monitoring system",
                        "Master key protection"
                    ]
                }
            }
            
            report_file = self.secure_dir / f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(security_report, f, indent=2, ensure_ascii=False)
            
            results["files_created"].append(str(report_file))
            results["actions_completed"].append("📊 تم إنشاء تقرير الأمان")
            results["master_password"] = master_password
            
            logger.info("✅ تم تطبيق جميع التوصيات الأمنية بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق الأمان: {e}")
            results["status"] = "error"
            results["error"] = str(e)
        
        return results

def main():
    """الدالة الرئيسية"""
    print("🔐 نظام تطبيق التوصيات الأمنية الفوري")
    print("=" * 60)
    
    security_system = HorusSecurityImplementation()
    
    # تطبيق الأمان على مجموعة المفاتيح
    keys_file = Path(__file__).parent / "api_keys_collection.json"
    
    if keys_file.exists():
        results = security_system.implement_immediate_security(str(keys_file))
        
        print("\n✅ تم تطبيق الأمان بنجاح!")
        print(f"🔑 كلمة المرور الرئيسية: {results.get('master_password', 'N/A')}")
        print("\n📁 الملفات المنشأة:")
        for file_path in results.get("files_created", []):
            print(f"   📄 {file_path}")
        
        print("\n✅ الإجراءات المكتملة:")
        for action in results.get("actions_completed", []):
            print(f"   {action}")
    else:
        print(f"❌ لم يتم العثور على ملف المفاتيح: {keys_file}")

if __name__ == "__main__":
    main()
