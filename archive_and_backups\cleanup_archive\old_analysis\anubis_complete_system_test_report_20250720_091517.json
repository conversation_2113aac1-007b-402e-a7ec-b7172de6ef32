{"test_results": {"api": {"/": {"message": "🏺 Anubis API Service - Isolated", "status": "running", "service": "api", "isolated": true, "timestamp": "2025-07-20T06:15:17.426046"}, "/health": {"status": "healthy", "service": "api", "timestamp": "2025-07-20T06:15:17.432255", "version": "1.0.0", "isolated": true}, "/info": {"name": "Anubis API Service", "version": "1.0.0", "description": "خدمة API معزولة لنظام أنوبيس", "endpoints": ["/", "/health", "/info", "/status", "/metrics", "/docs", "/redoc"]}, "/docs": {"status": "ok"}, "/redoc": {"status": "ok"}}, "worker": {"scan_task": {"status": "available", "simulated": true}, "analysis_task": {"status": "available", "simulated": true}, "health_check": {"status": "available", "simulated": true}}, "database": {"status": "connected", "type": "PostgreSQL", "version": "15-alpine", "simulated": true}, "redis": {"status": "connected", "type": "Redis", "version": "7-alpine", "simulated": true}, "performance": {"api_response_time_ms": 16.3, "status": "excellent"}, "security": {"Network Isolation": {"status": "secure", "simulated": true}, "Container Security": {"status": "secure", "simulated": true}, "User Privileges": {"status": "secure", "simulated": true}, "Volume Isolation": {"status": "secure", "simulated": true}}}, "metadata": {"test_timestamp": "20250720_091517", "system": "Anubis Isolated System", "version": "1.0.0"}}