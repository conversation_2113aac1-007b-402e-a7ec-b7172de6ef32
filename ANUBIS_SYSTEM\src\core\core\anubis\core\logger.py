#!/usr/bin/env python3
"""
📝 نظام السجلات لنظام المساعدين الذكيين العالمي
Logging System for Universal AI Assistants
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Optional


class SystemLogger:
    """نظام السجلات المتقدم"""

    def __init__(
        self,
        name: str = "UniversalAssistants",
        log_dir: str = "workspace/logs",
        level: str = "INFO",
        max_file_size_mb: int = 10,
        backup_count: int = 5,
    ):
        """
        تهيئة نظام السجلات

        Args:
            name: اسم السجل
            log_dir: مجلد السجلات
            level: مستوى السجل
            max_file_size_mb: الحد الأقصى لحجم ملف السجل
            backup_count: عدد النسخ الاحتياطية
        """
        self.name = name
        self.log_dir = Path(log_dir)
        self.level = getattr(logging, level.upper(), logging.INFO)
        self.max_file_size = max_file_size_mb * 1024 * 1024  # تحويل إلى bytes
        self.backup_count = backup_count

        # إنشاء مجلد السجلات
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # إعداد السجل
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """إعداد نظام السجلات"""
        logger = logging.getLogger(self.name)
        logger.setLevel(self.level)

        # تجنب إضافة handlers متعددة
        if logger.handlers:
            return logger

        # تنسيق السجل
        formatter = logging.Formatter(
            "[%(asctime)s] %(name)s - %(levelname)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # معالج ملف السجل مع التدوير
        log_file = self.log_dir / f"{self.name.lower()}.log"
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding="utf-8",
        )
        file_handler.setLevel(self.level)
        file_handler.setFormatter(formatter)

        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # عرض التحذيرات والأخطاء فقط
        console_handler.setFormatter(formatter)

        # إضافة المعالجات
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def info(self, message: str, agent: str = None):
        """تسجيل معلومة"""
        formatted_message = self._format_message(message, agent)
        self.logger.info(formatted_message)

    def warning(self, message: str, agent: str = None):
        """تسجيل تحذير"""
        formatted_message = self._format_message(message, agent)
        self.logger.warning(formatted_message)

    def error(self, message: str, agent: str = None, exception: Exception = None):
        """تسجيل خطأ"""
        formatted_message = self._format_message(message, agent)
        if exception:
            formatted_message += f" - Exception: {str(exception)}"
        self.logger.error(formatted_message)

    def debug(self, message: str, agent: str = None):
        """تسجيل معلومة تطوير"""
        formatted_message = self._format_message(message, agent)
        self.logger.debug(formatted_message)

    def critical(self, message: str, agent: str = None):
        """تسجيل خطأ حرج"""
        formatted_message = self._format_message(message, agent)
        self.logger.critical(formatted_message)

    def _format_message(self, message: str, agent: str = None) -> str:
        """تنسيق الرسالة"""
        if agent:
            return f"[{agent}] {message}"
        return message

    def log_agent_action(
        self, agent_name: str, action: str, details: str = "", level: str = "INFO"
    ):
        """تسجيل عمل وكيل محدد"""
        message = f"{action}"
        if details:
            message += f" - {details}"

        level_method = getattr(self, level.lower(), self.info)
        level_method(message, agent_name)

    def log_system_event(self, event: str, details: str = ""):
        """تسجيل حدث نظام"""
        message = f"SYSTEM EVENT: {event}"
        if details:
            message += f" - {details}"
        self.info(message)

    def log_error_with_traceback(self, message: str, exception: Exception, agent: str = None):
        """تسجيل خطأ مع تفاصيل التتبع"""
        import traceback

        formatted_message = self._format_message(message, agent)
        formatted_message += f"\nException: {str(exception)}"
        formatted_message += f"\nTraceback:\n{traceback.format_exc()}"

        self.logger.error(formatted_message)

    def create_session_log(self, session_id: str) -> "SessionLogger":
        """إنشاء سجل جلسة منفصل"""
        return SessionLogger(session_id, self.log_dir, self.level)

    def get_log_stats(self) -> dict:
        """إحصائيات السجلات"""
        log_file = self.log_dir / f"{self.name.lower()}.log"

        stats = {
            "log_file": str(log_file),
            "file_exists": log_file.exists(),
            "file_size_mb": 0,
            "last_modified": None,
            "backup_files": [],
        }

        if log_file.exists():
            stats["file_size_mb"] = round(log_file.stat().st_size / (1024 * 1024), 2)
            stats["last_modified"] = datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()

            # البحث عن ملفات النسخ الاحتياطية
            for i in range(1, self.backup_count + 1):
                backup_file = Path(f"{log_file}.{i}")
                if backup_file.exists():
                    stats["backup_files"].append(str(backup_file))

        return stats


class SessionLogger:
    """سجل جلسة منفصل"""

    def __init__(self, session_id: str, log_dir: Path, level: int):
        """
        تهيئة سجل الجلسة

        Args:
            session_id: معرف الجلسة
            log_dir: مجلد السجلات
            level: مستوى السجل
        """
        self.session_id = session_id
        self.log_dir = log_dir
        self.level = level

        # ملف سجل الجلسة
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = log_dir / f"session_{session_id}_{timestamp}.log"

        # إعداد السجل
        self.logger = self._setup_session_logger()

        # بدء الجلسة
        self.start_time = datetime.now()
        self.log("بدء جلسة جديدة", f"معرف الجلسة: {session_id}")

    def _setup_session_logger(self) -> logging.Logger:
        """إعداد سجل الجلسة"""
        logger_name = f"Session_{self.session_id}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(self.level)

        # تجنب إضافة handlers متعددة
        if logger.handlers:
            return logger

        # تنسيق السجل
        formatter = logging.Formatter("[%(asctime)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")

        # معالج ملف السجل
        file_handler = logging.FileHandler(self.log_file, encoding="utf-8")
        file_handler.setLevel(self.level)
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)
        return logger

    def log(self, action: str, details: str = ""):
        """تسجيل عمل في الجلسة"""
        message = action
        if details:
            message += f" - {details}"
        self.logger.info(message)

    def log_agent_result(self, agent_name: str, result: dict):
        """تسجيل نتيجة وكيل"""
        success = result.get("success", False)
        status = "نجح" if success else "فشل"

        message = f"الوكيل {agent_name}: {status}"
        if not success and "error" in result:
            message += f" - خطأ: {result['error']}"

        self.log(message)

    def end_session(self):
        """إنهاء الجلسة"""
        duration = datetime.now() - self.start_time
        self.log("إنهاء الجلسة", f"المدة: {duration}")

        # إغلاق معالجات السجل
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler)

    def get_session_summary(self) -> dict:
        """ملخص الجلسة"""
        duration = datetime.now() - self.start_time

        return {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "duration_seconds": duration.total_seconds(),
            "log_file": str(self.log_file),
            "file_size_bytes": (self.log_file.stat().st_size if self.log_file.exists() else 0),
        }


# إنشاء سجل النظام الافتراضي
default_logger = SystemLogger()


# دوال مساعدة للاستخدام السريع
def log_info(message: str, agent: str = None):
    """تسجيل معلومة"""
    default_logger.info(message, agent)


def log_warning(message: str, agent: str = None):
    """تسجيل تحذير"""
    default_logger.warning(message, agent)


def log_error(message: str, agent: str = None, exception: Exception = None):
    """تسجيل خطأ"""
    default_logger.error(message, agent, exception)


def log_agent_action(agent_name: str, action: str, details: str = ""):
    """تسجيل عمل وكيل"""
    default_logger.log_agent_action(agent_name, action, details)
