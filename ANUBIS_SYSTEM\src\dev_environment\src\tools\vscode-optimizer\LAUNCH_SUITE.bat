@echo off
chcp 65001 >nul
color 0B
title Universal AI Assistant Suite - Main Launcher

cls
echo.
echo ████████████████████████████████████████████████████████████████████████
echo █                                                                      █
echo █           🚀 Universal AI Assistant Suite 🚀                        █
echo █                                                                      █
echo █        مجموعة شاملة من أدوات الذكاء الاصطناعي ومراقبة الأداء        █
echo █                                                                      █
echo █     📊 النتائج المحققة:                                              █
echo █     • تحسن المعالج: 95.9%% → 11.1%% (تحسن 84.8%%!)                █
echo █     • تحسن الذاكرة: 89.0%% → 62.2%% (تحسن 26.8%%!)                █
echo █     • تحسن VS Code: 56.2%% → 26.0%% (تحسن 30.2%%!)               █
echo █                                                                      █
echo █     🤖 6 وكلاء ذكيين | 🎛️ 3 واجهات متقدمة | 📊 مراقبة شاملة     █
echo █                                                                      █
echo ████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 اختر التطبيق المناسب لاحتياجاتك:
echo.
echo ═══════════════════════════════════════════════════════════════════════
echo 🚀 VS Code Performance Optimizer (موصى به للبداية)
echo ═══════════════════════════════════════════════════════════════════════
echo 1️⃣  التشغيل السريع - محسن الأداء (للمبتدئين)
echo 2️⃣  الواجهة المتقدمة - Task Manager كامل
echo 3️⃣  النسخة المستقرة - معالجة محسنة للأخطاء
echo 4️⃣  تطبيق تحسينات VS Code فقط
echo.
echo ═══════════════════════════════════════════════════════════════════════
echo 🎛️ VSCode Control Center (للمراقبة المتقدمة)
echo ═══════════════════════════════════════════════════════════════════════
echo 5️⃣  مركز التحكم المتقدم
echo 6️⃣  مركز التحكم المستقر
echo 7️⃣  مركز التحكم الموحد
echo.
echo ═══════════════════════════════════════════════════════════════════════
echo 🤖 AI Agents System (للتحليل الذكي)
echo ═══════════════════════════════════════════════════════════════════════
echo 8️⃣  تشغيل نظام الوكلاء الذكيين
echo 9️⃣  اختبار الوكلاء المتاحين
echo.
echo ═══════════════════════════════════════════════════════════════════════
echo 📚 أدوات إضافية
echo ═══════════════════════════════════════════════════════════════════════
echo A️⃣  عرض التوثيق والأدلة
echo B️⃣  تحليل النظام الشامل
echo C️⃣  معلومات المجموعة
echo 0️⃣  خروج
echo.

set /p choice=اختر الخيار المناسب: 

if "%choice%"=="1" goto perf_quick
if "%choice%"=="2" goto perf_pro
if "%choice%"=="3" goto perf_stable
if "%choice%"=="4" goto perf_optimize
if "%choice%"=="5" goto control_pro
if "%choice%"=="6" goto control_stable
if "%choice%"=="7" goto control_unified
if "%choice%"=="8" goto agents_system
if "%choice%"=="9" goto agents_test
if /i "%choice%"=="A" goto show_docs
if /i "%choice%"=="B" goto system_analysis
if /i "%choice%"=="C" goto suite_info
if "%choice%"=="0" goto exit
goto invalid_choice

:perf_quick
echo.
echo 🚀 تشغيل محسن الأداء - التشغيل السريع...
cd VS-Code-Performance-Optimizer
call quick_start.bat
cd ..
goto end

:perf_pro
echo.
echo 🚀 تشغيل محسن الأداء - الواجهة المتقدمة...
cd VS-Code-Performance-Optimizer
call start_pro.bat
cd ..
goto end

:perf_stable
echo.
echo 🚀 تشغيل محسن الأداء - النسخة المستقرة...
cd VS-Code-Performance-Optimizer
call start_stable.bat
cd ..
goto end

:perf_optimize
echo.
echo 🔧 تطبيق تحسينات VS Code...
cd VS-Code-Performance-Optimizer
call apply_vscode_optimizations.bat
cd ..
goto menu

:control_pro
echo.
echo 🎛️ تشغيل مركز التحكم المتقدم...
cd VSCode-Control-Center
call start_pro.bat
cd ..
goto end

:control_stable
echo.
echo 🎛️ تشغيل مركز التحكم المستقر...
cd VSCode-Control-Center
call start_stable.bat
cd ..
goto end

:control_unified
echo.
echo 🎛️ تشغيل مركز التحكم الموحد...
cd VSCode-Control-Center
call start.bat
cd ..
goto end

:agents_system
echo.
echo 🤖 تشغيل نظام الوكلاء الذكيين...
cd agents
python agent_coordinator.py
cd ..
goto menu

:agents_test
echo.
echo 🧪 اختبار الوكلاء المتاحين...
cd agents
python -c "
import os
print('🤖 الوكلاء المتاحين:')
agents = [f for f in os.listdir('.') if f.endswith('_agent.py') or f.endswith('_analyzer.py') or f.endswith('_optimizer.py') or f.endswith('_monitor.py') or f.endswith('_recommendations.py')]
for i, agent in enumerate(agents, 1):
    print(f'{i}. {agent}')
print(f'\\n📊 إجمالي الوكلاء: {len(agents)}')
"
cd ..
echo.
pause
goto menu

:show_docs
echo.
echo 📚 التوثيق والأدلة المتاحة:
echo.
echo 📁 VS Code Performance Optimizer:
echo    📄 README_MAIN.md - الدليل الرئيسي
echo    📄 README_PRO.md - دليل الواجهة المتقدمة
echo    📄 PROJECT_INFO.md - معلومات المشروع
echo.
echo 📁 VSCode Control Center:
echo    📄 README.md - دليل مركز التحكم
echo    📄 FINAL_SOLUTION.md - الحل النهائي
echo    📄 HOW_TO_RUN.md - دليل التشغيل السريع
echo.
echo 📁 AI Agents System:
echo    📄 ملفات الوكلاء في مجلد agents/
echo.
echo 📁 المجموعة:
echo    📄 README.md - دليل المجموعة الشامل
echo.
echo 💡 افتح أي ملف .md بـ Notepad أو VS Code لقراءته
echo.
pause
goto menu

:system_analysis
echo.
echo 🔍 تحليل النظام الشامل...
cd VS-Code-Performance-Optimizer
python test_system.py
cd ..
echo.
pause
goto menu

:suite_info
echo.
echo 📊 معلومات Universal AI Assistant Suite:
echo.
echo 🎯 المجموعة تحتوي على:
echo    📁 VS Code Performance Optimizer (39 ملف - 433.2 KB)
echo    📁 VSCode Control Center (34 ملف - 453.2 KB)
echo    📁 AI Agents System (17 ملف - 195.2 KB)
echo.
echo 🚀 الإجمالي: 90+ ملف (~1.1 MB)
echo.
echo 🏆 النتائج المحققة:
echo    ⚡ تحسن المعالج: 84.8%%
echo    💾 تحسن الذاكرة: 26.8%%
echo    🧩 تحسن VS Code: 30.2%%
echo.
echo 🤖 الوكلاء الذكيين: 6 وكلاء متخصصين
echo 🎛️ الواجهات: 3 واجهات مختلفة
echo 📊 المراقبة: شاملة ومتقدمة
echo.
pause
goto menu

:invalid_choice
echo.
echo ❌ خيار غير صحيح! يرجى اختيار من الخيارات المتاحة
echo.
pause
goto menu

:menu
cls
echo.
echo 🔄 العودة للقائمة الرئيسية...
timeout /t 2 >nul
goto start

:exit
echo.
echo 👋 شكراً لاستخدام Universal AI Assistant Suite!
echo.
echo 💡 نصائح للحصول على أفضل أداء:
echo    • استخدم محسن الأداء بانتظام
echo    • راقب النظام باستمرار
echo    • استفد من الوكلاء الذكيين
echo    • طبق التحسينات المقترحة
echo.
echo 🚀 نتمنى لك تجربة ممتازة مع أدواتنا!
echo.
pause
exit

:end
echo.
echo ✅ انتهى التشغيل
echo.
echo 🔄 هل تريد العودة للقائمة الرئيسية؟ (Y/N)
set /p return_choice=

if /i "%return_choice%"=="Y" goto menu
if /i "%return_choice%"=="y" goto menu

echo.
echo 👋 شكراً لاستخدام Universal AI Assistant Suite!
echo 🎯 لا تنس تطبيق التحسينات للحصول على أفضل أداء
pause
exit

:start
goto menu
