#!/usr/bin/env python3
"""
🧪 اختبارات نظام الإضافات
Plugins System Tests

اختبارات شاملة لنظام الإضافات والمدير
"""

import os
import shutil
import sys
import tempfile
import unittest
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "plugins"))

try:
    from anubis.plugins.base_plugin import BasePlugin
    from anubis.plugins.example_plugin import ExamplePlugin
    from anubis.plugins.plugin_manager import PluginManager
except ImportError as e:
    print(f"❌ خطأ في استيراد نظام الإضافات: {e}")
    sys.exit(1)


class TestPlugin(BasePlugin):
    """إضافة اختبار للاختبارات"""

    def get_plugin_info(self):
        return {
            "name": "Test Plugin",
            "description": "إضافة للاختبار",
            "version": "1.0.0",
            "author": "Test Author",
        }

    def initialize_plugin(self):
        self.test_value = "initialized"

    def execute(self, action="test", data=None):
        if action == "test":
            return {
                "success": True,
                "message": "Test plugin executed successfully",
                "test_value": self.test_value,
                "data": data,
            }
        elif action == "error":
            raise Exception("Test error")
        else:
            return {"success": False, "error": f"Unknown action: {action}"}


class TestBasePlugin(unittest.TestCase):
    """اختبارات الفئة الأساسية للإضافات"""

    def test_plugin_initialization(self):
        """اختبار تهيئة الإضافة"""
        config = {"test_setting": "test_value"}
        plugin = TestPlugin(config)

        self.assertEqual(plugin.plugin_name, "TestPlugin")
        self.assertEqual(plugin.version, "1.0.0")
        self.assertTrue(plugin.is_enabled)
        self.assertTrue(plugin.is_initialized)
        self.assertEqual(plugin.config, config)
        self.assertEqual(plugin.test_value, "initialized")

    def test_plugin_status(self):
        """اختبار حالة الإضافة"""
        plugin = TestPlugin()
        status = plugin.get_status()

        self.assertIn("plugin_name", status)
        self.assertIn("version", status)
        self.assertIn("is_enabled", status)
        self.assertIn("is_initialized", status)
        self.assertIn("last_run", status)

        self.assertEqual(status["plugin_name"], "TestPlugin")
        self.assertTrue(status["is_enabled"])
        self.assertTrue(status["is_initialized"])

    def test_plugin_enable_disable(self):
        """اختبار تفعيل وإلغاء تفعيل الإضافة"""
        plugin = TestPlugin()

        # تفعيل
        plugin.enable()
        self.assertTrue(plugin.is_enabled)

        # إلغاء تفعيل
        plugin.disable()
        self.assertFalse(plugin.is_enabled)

    def test_plugin_execution(self):
        """اختبار تنفيذ الإضافة"""
        plugin = TestPlugin()

        # تنفيذ ناجح
        result = plugin.run(action="test", data="test_data")
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "Test plugin executed successfully")
        self.assertEqual(result["data"], "test_data")
        self.assertIn("plugin_info", result)

        # التحقق من تحديث last_run
        self.assertIsNotNone(plugin.last_run)

    def test_plugin_error_handling(self):
        """اختبار معالجة الأخطاء"""
        plugin = TestPlugin()

        # تنفيذ مع خطأ
        result = plugin.run(action="error")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertIn("plugin_info", result)

    def test_disabled_plugin_execution(self):
        """اختبار تنفيذ إضافة معطلة"""
        plugin = TestPlugin()
        plugin.disable()

        result = plugin.run()
        self.assertFalse(result["success"])
        self.assertIn("error", result)


class TestExamplePlugin(unittest.TestCase):
    """اختبارات الإضافة المثال"""

    def setUp(self):
        """إعداد الاختبارات"""
        self.config = {
            "greeting_message": "مرحباً من الاختبار!",
            "enable_analysis": True,
        }
        self.plugin = ExamplePlugin(self.config)

    def test_plugin_info(self):
        """اختبار معلومات الإضافة"""
        info = self.plugin.get_plugin_info()

        self.assertIn("name", info)
        self.assertIn("description", info)
        self.assertIn("version", info)
        self.assertIn("author", info)
        self.assertIn("capabilities", info)

        self.assertEqual(info["name"], "Example Plugin")
        self.assertIsInstance(info["capabilities"], list)

    def test_greet_action(self):
        """اختبار عملية الترحيب"""
        result = self.plugin.run(action="greet")

        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "مرحباً من الاختبار!")
        self.assertEqual(result["action"], "greet")
        self.assertIn("timestamp", result)

    def test_analyze_action(self):
        """اختبار عملية التحليل"""
        test_text = "هذا نص تجريبي للتحليل مع أرقام 123 وكلمات English"
        result = self.plugin.run(action="analyze", data=test_text)

        self.assertTrue(result["success"])
        self.assertIn("analysis", result)
        self.assertEqual(result["action"], "analyze")

        analysis = result["analysis"]
        self.assertIn("character_count", analysis)
        self.assertIn("word_count", analysis)
        self.assertIn("has_arabic", analysis)
        self.assertIn("has_english", analysis)
        self.assertIn("has_numbers", analysis)

        self.assertTrue(analysis["has_arabic"])
        self.assertTrue(analysis["has_english"])
        self.assertTrue(analysis["has_numbers"])

    def test_report_action(self):
        """اختبار عملية إنشاء التقرير"""
        test_data = {"key": "value", "number": 42}
        result = self.plugin.run(action="report", data=test_data)

        self.assertTrue(result["success"])
        self.assertIn("report", result)
        self.assertEqual(result["action"], "report")

        report = result["report"]
        self.assertIn("report_title", report)
        self.assertIn("generated_at", report)
        self.assertIn("plugin_info", report)
        self.assertIn("input_data", report)
        self.assertIn("summary", report)

        self.assertEqual(report["input_data"], test_data)

    def test_invalid_action(self):
        """اختبار عملية غير صالحة"""
        result = self.plugin.run(action="invalid_action")

        self.assertFalse(result["success"])
        self.assertIn("error", result)


class TestPluginManager(unittest.TestCase):
    """اختبارات مدير الإضافات"""

    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء مجلد مؤقت للإضافات
        self.test_dir = tempfile.mkdtemp()
        self.plugin_dir = Path(self.test_dir) / "test_plugins"
        self.plugin_dir.mkdir()

        # إنشاء مدير الإضافات
        self.config = {
            "auto_load": False,  # تعطيل التحميل التلقائي للاختبار
            "auto_enable": True,
        }
        self.manager = PluginManager(plugin_dir=str(self.plugin_dir), config=self.config)

    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_manager_initialization(self):
        """اختبار تهيئة مدير الإضافات"""
        self.assertEqual(str(self.manager.plugin_dir), str(self.plugin_dir))
        self.assertEqual(self.manager.config, self.config)
        self.assertFalse(self.manager.auto_load)
        self.assertTrue(self.manager.auto_enable)
        self.assertEqual(len(self.manager.plugins), 0)
        self.assertEqual(len(self.manager.enabled_plugins), 0)

    def test_manual_plugin_registration(self):
        """اختبار تسجيل الإضافات يدوياً"""
        # إنشاء إضافة اختبار
        plugin = TestPlugin()

        # تسجيل الإضافة يدوياً
        self.manager.plugins["TestPlugin"] = plugin
        self.manager.enabled_plugins["TestPlugin"] = plugin

        # التحقق من التسجيل
        self.assertEqual(len(self.manager.plugins), 1)
        self.assertEqual(len(self.manager.enabled_plugins), 1)
        self.assertIn("TestPlugin", self.manager.plugins)
        self.assertIn("TestPlugin", self.manager.enabled_plugins)

    def test_plugin_enable_disable(self):
        """اختبار تفعيل وإلغاء تفعيل الإضافات"""
        # تسجيل إضافة
        plugin = TestPlugin()
        self.manager.plugins["TestPlugin"] = plugin

        # تفعيل الإضافة
        result = self.manager.enable_plugin("TestPlugin")
        self.assertTrue(result)
        self.assertIn("TestPlugin", self.manager.enabled_plugins)

        # إلغاء تفعيل الإضافة
        result = self.manager.disable_plugin("TestPlugin")
        self.assertTrue(result)
        self.assertNotIn("TestPlugin", self.manager.enabled_plugins)

    def test_plugin_execution(self):
        """اختبار تنفيذ الإضافات"""
        # تسجيل وتفعيل إضافة
        plugin = TestPlugin()
        self.manager.plugins["TestPlugin"] = plugin
        self.manager.enabled_plugins["TestPlugin"] = plugin

        # تنفيذ الإضافة
        result = self.manager.run_plugin("TestPlugin", action="test", data="test_data")
        self.assertTrue(result["success"])
        self.assertEqual(result["data"], "test_data")

    def test_run_all_plugins(self):
        """اختبار تنفيذ جميع الإضافات"""
        # تسجيل عدة إضافات
        plugin1 = TestPlugin()
        plugin2 = ExamplePlugin()

        self.manager.plugins["TestPlugin"] = plugin1
        self.manager.plugins["ExamplePlugin"] = plugin2
        self.manager.enabled_plugins["TestPlugin"] = plugin1
        self.manager.enabled_plugins["ExamplePlugin"] = plugin2

        # تنفيذ جميع الإضافات
        results = self.manager.run_all_plugins(action="test")

        self.assertEqual(len(results), 2)
        self.assertIn("TestPlugin", results)
        self.assertIn("ExamplePlugin", results)

    def test_plugin_info_retrieval(self):
        """اختبار الحصول على معلومات الإضافة"""
        # تسجيل إضافة
        plugin = TestPlugin()
        self.manager.plugins["TestPlugin"] = plugin

        # الحصول على المعلومات
        info = self.manager.get_plugin_info("TestPlugin")
        self.assertIsNotNone(info)
        self.assertIn("name", info)
        self.assertIn("plugin_name", info)
        self.assertIn("is_enabled", info)

    def test_list_plugins(self):
        """اختبار قائمة الإضافات"""
        # تسجيل إضافات
        plugin1 = TestPlugin()
        plugin2 = ExamplePlugin()

        self.manager.plugins["TestPlugin"] = plugin1
        self.manager.plugins["ExamplePlugin"] = plugin2
        self.manager.enabled_plugins["TestPlugin"] = plugin1

        # الحصول على القائمة
        plugin_list = self.manager.list_plugins()

        self.assertEqual(plugin_list["total_plugins"], 2)
        self.assertEqual(plugin_list["enabled_plugins"], 1)
        self.assertIn("plugins", plugin_list)
        self.assertIn("TestPlugin", plugin_list["plugins"])
        self.assertIn("ExamplePlugin", plugin_list["plugins"])

    def test_manager_status(self):
        """اختبار حالة مدير الإضافات"""
        status = self.manager.get_status()

        self.assertIn("plugin_dir", status)
        self.assertIn("total_plugins", status)
        self.assertIn("enabled_plugins", status)
        self.assertIn("auto_load", status)
        self.assertIn("auto_enable", status)
        self.assertIn("plugin_names", status)
        self.assertIn("enabled_plugin_names", status)

        self.assertEqual(status["plugin_dir"], str(self.plugin_dir))
        self.assertFalse(status["auto_load"])
        self.assertTrue(status["auto_enable"])


def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات نظام الإضافات...")

    # إنشاء مجموعة الاختبارات
    test_classes = [TestBasePlugin, TestExamplePlugin, TestPluginManager]
    test_suite = unittest.TestSuite()

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # عرض النتائج
    if result.wasSuccessful():
        print("✅ جميع اختبارات نظام الإضافات نجحت!")
        return True
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
