#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕵️ كاشف الأدوات المخفية والمتقدمة
Advanced Hidden Tools Detector

يكتشف الأدوات المخفية والمتقدمة التي قد تفوتها الفحوصات العادية
Detects hidden and advanced tools that might be missed by regular scans
"""

import os
import sys
import json
import subprocess
import winreg
import glob
from pathlib import Path
from datetime import datetime
import re

class AdvancedHiddenToolsDetector:
    def __init__(self):
        self.scan_results = {
            'scan_date': datetime.now().isoformat(),
            'hidden_development_tools': {},
            'portable_tools': {},
            'embedded_tools': {},
            'cloud_cli_tools': {},
            'package_managers': {},
            'ide_extensions': {},
            'browser_dev_tools': {},
            'wsl_tools': {},
            'advanced_statistics': {}
        }

    def detect_portable_tools(self):
        """اكتشاف الأدوات المحمولة"""
        print("🎒 اكتشاف الأدوات المحمولة...")
        
        portable_tools = {}
        
        # مسارات شائعة للأدوات المحمولة
        portable_paths = [
            'C:/PortableApps',
            'C:/Portable',
            'C:/Tools',
            'C:/Dev',
            'C:/Development',
            'D:/PortableApps',
            'D:/Portable',
            'D:/Tools',
            'F:/PortableApps',
            'F:/Portable',
            'F:/Tools'
        ]
        
        for path in portable_paths:
            if os.path.exists(path):
                print(f"   🔍 فحص: {path}")
                try:
                    for root, dirs, files in os.walk(path):
                        # تحديد عمق البحث
                        level = root.replace(path, '').count(os.sep)
                        if level > 4:
                            dirs.clear()
                            continue
                        
                        # البحث عن أدوات محمولة
                        for file in files:
                            if file.lower().endswith('.exe'):
                                file_path = os.path.join(root, file)
                                if any(keyword in file.lower() for keyword in [
                                    'portable', 'python', 'node', 'git', 'code', 'studio',
                                    'editor', 'ide', 'compiler', 'interpreter'
                                ]):
                                    portable_tools[file] = {
                                        'path': file_path,
                                        'directory': root,
                                        'size': self.get_file_size(file_path)
                                    }
                except Exception as e:
                    continue
        
        self.scan_results['portable_tools'] = portable_tools
        print(f"✅ تم اكتشاف {len(portable_tools)} أداة محمولة")

    def detect_embedded_tools(self):
        """اكتشاف الأدوات المدمجة"""
        print("🔧 اكتشاف الأدوات المدمجة...")
        
        embedded_tools = {}
        
        # البحث في مجلدات التطبيقات
        app_folders = [
            'C:/Program Files',
            'C:/Program Files (x86)',
            'C:/Users/<USER>/AppData/Local',
            'C:/Users/<USER>/AppData/Roaming'
        ]
        
        embedded_patterns = [
            '**/python.exe',
            '**/node.exe',
            '**/java.exe',
            '**/git.exe',
            '**/npm.cmd',
            '**/pip.exe',
            '**/composer.phar',
            '**/mvn.cmd',
            '**/gradle.bat'
        ]
        
        for folder_pattern in app_folders:
            try:
                folders = glob.glob(folder_pattern)
                for folder in folders:
                    if os.path.exists(folder):
                        for pattern in embedded_patterns:
                            search_pattern = os.path.join(folder, pattern)
                            matches = glob.glob(search_pattern, recursive=True)
                            
                            for match in matches:
                                if os.path.exists(match):
                                    tool_name = os.path.basename(match)
                                    parent_app = self.identify_parent_application(match)
                                    
                                    if parent_app and parent_app not in ['Windows', 'System']:
                                        embedded_tools[f"{parent_app}_{tool_name}"] = {
                                            'tool': tool_name,
                                            'path': match,
                                            'parent_application': parent_app,
                                            'version': self.get_tool_version(match)
                                        }
            except Exception:
                continue
        
        self.scan_results['embedded_tools'] = embedded_tools
        print(f"✅ تم اكتشاف {len(embedded_tools)} أداة مدمجة")

    def detect_cloud_cli_tools(self):
        """اكتشاف أدوات سطر الأوامر السحابية"""
        print("☁️ اكتشاف أدوات سطر الأوامر السحابية...")
        
        cloud_tools = {}
        
        # أدوات سحابية شائعة
        cloud_cli_tools = {
            'aws': ['aws.exe', 'aws.cmd'],
            'azure': ['az.exe', 'az.cmd'],
            'gcloud': ['gcloud.exe', 'gcloud.cmd'],
            'kubectl': ['kubectl.exe'],
            'helm': ['helm.exe'],
            'terraform': ['terraform.exe'],
            'ansible': ['ansible.exe', 'ansible-playbook.exe'],
            'docker': ['docker.exe', 'docker-compose.exe'],
            'heroku': ['heroku.exe', 'heroku.cmd'],
            'firebase': ['firebase.exe', 'firebase.cmd']
        }
        
        # البحث في PATH وأماكن أخرى
        search_paths = os.environ.get('PATH', '').split(';')
        search_paths.extend([
            'C:/Program Files/Amazon/AWSCLIV2',
            'C:/Program Files/Microsoft SDKs/Azure',
            'C:/Program Files/Google/Cloud SDK',
            'C:/Users/<USER>/AppData/Local/Google/Cloud SDK',
            'C:/HashiCorp/Terraform',
            'C:/tools'
        ])
        
        for tool_name, executables in cloud_cli_tools.items():
            for executable in executables:
                # البحث في PATH
                tool_path = self.find_in_path(executable)
                if tool_path:
                    cloud_tools[tool_name] = {
                        'executable': executable,
                        'path': tool_path,
                        'version': self.get_tool_version(tool_path),
                        'found_in': 'PATH'
                    }
                    continue
                
                # البحث في مسارات أخرى
                for search_path in search_paths:
                    if search_path and os.path.exists(search_path):
                        tool_path = os.path.join(search_path, executable)
                        if os.path.exists(tool_path):
                            cloud_tools[tool_name] = {
                                'executable': executable,
                                'path': tool_path,
                                'version': self.get_tool_version(tool_path),
                                'found_in': search_path
                            }
                            break
        
        self.scan_results['cloud_cli_tools'] = cloud_tools
        print(f"✅ تم اكتشاف {len(cloud_tools)} أداة سحابية")

    def detect_advanced_package_managers(self):
        """اكتشاف مدراء الحزم المتقدمة"""
        print("📦 اكتشاف مدراء الحزم المتقدمة...")
        
        package_managers = {}
        
        # مدراء حزم متقدمة
        advanced_managers = {
            'uv': ['uv.exe'],
            'pipx': ['pipx.exe'],
            'poetry': ['poetry.exe'],
            'conda': ['conda.exe'],
            'mamba': ['mamba.exe'],
            'vcpkg': ['vcpkg.exe'],
            'conan': ['conan.exe'],
            'nuget': ['nuget.exe'],
            'chocolatey': ['choco.exe'],
            'scoop': ['scoop.cmd', 'scoop.ps1'],
            'winget': ['winget.exe'],
            'brew': ['brew.exe']  # Windows Subsystem for Linux
        }
        
        # مسارات بحث إضافية
        additional_paths = [
            'C:/Users/<USER>/AppData/Local/Programs/Python/*/Scripts',
            'C:/Users/<USER>/AppData/Roaming/Python/*/Scripts',
            'C:/Users/<USER>/.cargo/bin',
            'C:/Users/<USER>/.local/bin',
            'C:/ProgramData/chocolatey/bin',
            'C:/Users/<USER>/scoop/shims',
            'C:/tools/vcpkg',
            'C:/conan'
        ]
        
        for manager_name, executables in advanced_managers.items():
            for executable in executables:
                # البحث في PATH
                tool_path = self.find_in_path(executable)
                if tool_path:
                    package_managers[manager_name] = {
                        'executable': executable,
                        'path': tool_path,
                        'version': self.get_tool_version(tool_path),
                        'packages_info': self.get_package_manager_info(manager_name, tool_path)
                    }
                    continue
                
                # البحث في مسارات إضافية
                for path_pattern in additional_paths:
                    try:
                        paths = glob.glob(path_pattern)
                        for path in paths:
                            if os.path.exists(path):
                                tool_path = os.path.join(path, executable)
                                if os.path.exists(tool_path):
                                    package_managers[manager_name] = {
                                        'executable': executable,
                                        'path': tool_path,
                                        'version': self.get_tool_version(tool_path),
                                        'packages_info': self.get_package_manager_info(manager_name, tool_path)
                                    }
                                    break
                    except Exception:
                        continue
        
        self.scan_results['package_managers'] = package_managers
        print(f"✅ تم اكتشاف {len(package_managers)} مدير حزم متقدم")

    def detect_ide_extensions(self):
        """اكتشاف إضافات بيئات التطوير"""
        print("🔌 اكتشاف إضافات بيئات التطوير...")
        
        ide_extensions = {}
        
        # مسارات إضافات VS Code
        vscode_extensions_path = os.path.expanduser('~/.vscode/extensions')
        if os.path.exists(vscode_extensions_path):
            try:
                extensions = [d for d in os.listdir(vscode_extensions_path) if os.path.isdir(os.path.join(vscode_extensions_path, d))]
                ide_extensions['vscode'] = {
                    'total_extensions': len(extensions),
                    'extensions_path': vscode_extensions_path,
                    'sample_extensions': extensions[:10]  # عينة من الإضافات
                }
            except Exception:
                pass
        
        # مسارات إضافات Visual Studio
        vs_extensions_paths = [
            'C:/Users/<USER>/AppData/Local/Microsoft/VisualStudio/*/Extensions',
            'C:/Users/<USER>/AppData/Roaming/Microsoft/VisualStudio/*/Extensions'
        ]
        
        for path_pattern in vs_extensions_paths:
            try:
                paths = glob.glob(path_pattern)
                for path in paths:
                    if os.path.exists(path):
                        extensions = [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
                        if extensions:
                            ide_extensions['visual_studio'] = {
                                'total_extensions': len(extensions),
                                'extensions_path': path,
                                'sample_extensions': extensions[:5]
                            }
                            break
            except Exception:
                continue
        
        self.scan_results['ide_extensions'] = ide_extensions
        print(f"✅ تم اكتشاف إضافات {len(ide_extensions)} بيئة تطوير")

    def detect_wsl_tools(self):
        """اكتشاف أدوات Windows Subsystem for Linux"""
        print("🐧 اكتشاف أدوات Windows Subsystem for Linux...")
        
        wsl_info = {}
        
        try:
            # فحص وجود WSL
            if os.path.exists('C:/Windows/System32/wsl.exe'):
                wsl_info['wsl_installed'] = True
                
                # قائمة التوزيعات
                try:
                    result = subprocess.run(['wsl', '--list', '--verbose'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        wsl_info['distributions'] = result.stdout.strip()
                        
                        # عدد التوزيعات
                        lines = result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                        wsl_info['distribution_count'] = len([line for line in lines if line.strip()])
                except Exception:
                    wsl_info['distributions'] = "Unable to retrieve"
                
                # فحص أدوات Linux المثبتة
                try:
                    linux_tools = ['python3', 'node', 'npm', 'git', 'docker', 'kubectl']
                    installed_tools = {}
                    
                    for tool in linux_tools:
                        try:
                            result = subprocess.run(['wsl', 'which', tool], 
                                                  capture_output=True, text=True, timeout=5)
                            if result.returncode == 0:
                                installed_tools[tool] = result.stdout.strip()
                        except Exception:
                            continue
                    
                    wsl_info['linux_tools'] = installed_tools
                except Exception:
                    wsl_info['linux_tools'] = {}
            else:
                wsl_info['wsl_installed'] = False
        
        except Exception as e:
            wsl_info['error'] = str(e)
        
        self.scan_results['wsl_tools'] = wsl_info
        print(f"✅ تم فحص أدوات WSL")

    def detect_browser_dev_tools(self):
        """اكتشاف أدوات التطوير في المتصفحات"""
        print("🌐 اكتشاف أدوات التطوير في المتصفحات...")
        
        browser_tools = {}
        
        # مسارات إضافات Chrome
        chrome_extensions_path = os.path.expanduser('~/AppData/Local/Google/Chrome/User Data/Default/Extensions')
        if os.path.exists(chrome_extensions_path):
            try:
                extensions = [d for d in os.listdir(chrome_extensions_path) if os.path.isdir(os.path.join(chrome_extensions_path, d))]
                browser_tools['chrome'] = {
                    'extensions_count': len(extensions),
                    'extensions_path': chrome_extensions_path
                }
            except Exception:
                pass
        
        # مسارات إضافات Firefox
        firefox_profiles_path = os.path.expanduser('~/AppData/Roaming/Mozilla/Firefox/Profiles')
        if os.path.exists(firefox_profiles_path):
            try:
                profiles = [d for d in os.listdir(firefox_profiles_path) if os.path.isdir(os.path.join(firefox_profiles_path, d))]
                browser_tools['firefox'] = {
                    'profiles_count': len(profiles),
                    'profiles_path': firefox_profiles_path
                }
            except Exception:
                pass
        
        # مسارات إضافات Edge
        edge_extensions_path = os.path.expanduser('~/AppData/Local/Microsoft/Edge/User Data/Default/Extensions')
        if os.path.exists(edge_extensions_path):
            try:
                extensions = [d for d in os.listdir(edge_extensions_path) if os.path.isdir(os.path.join(edge_extensions_path, d))]
                browser_tools['edge'] = {
                    'extensions_count': len(extensions),
                    'extensions_path': edge_extensions_path
                }
            except Exception:
                pass
        
        self.scan_results['browser_dev_tools'] = browser_tools
        print(f"✅ تم فحص أدوات {len(browser_tools)} متصفح")

    # دوال مساعدة
    def get_file_size(self, file_path):
        """الحصول على حجم الملف"""
        try:
            size = os.path.getsize(file_path)
            if size > 1024**3:
                return f"{size / (1024**3):.2f} GB"
            elif size > 1024**2:
                return f"{size / (1024**2):.2f} MB"
            elif size > 1024:
                return f"{size / 1024:.2f} KB"
            else:
                return f"{size} bytes"
        except:
            return "Unknown"

    def identify_parent_application(self, tool_path):
        """تحديد التطبيق الأب للأداة"""
        path_parts = tool_path.lower().split(os.sep)
        
        # أنماط شائعة للتطبيقات
        app_patterns = {
            'mysql': 'MySQL',
            'postgresql': 'PostgreSQL',
            'pgadmin': 'pgAdmin',
            'adobe': 'Adobe',
            'microsoft': 'Microsoft',
            'google': 'Google',
            'jetbrains': 'JetBrains',
            'visual studio': 'Visual Studio',
            'anaconda': 'Anaconda',
            'miniconda': 'Miniconda'
        }
        
        for pattern, app_name in app_patterns.items():
            if any(pattern in part for part in path_parts):
                return app_name
        
        # محاولة استخراج اسم التطبيق من المسار
        for part in path_parts:
            if 'program files' in part or 'appdata' in part:
                continue
            if len(part) > 3 and not part.isdigit():
                return part.title()
        
        return "Unknown"

    def find_in_path(self, executable):
        """البحث عن ملف تنفيذي في PATH"""
        try:
            result = subprocess.run(['where', executable], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split('\n')[0]
        except:
            pass
        return None

    def get_tool_version(self, tool_path):
        """الحصول على إصدار الأداة"""
        try:
            # محاولات مختلفة للحصول على الإصدار
            version_commands = ['--version', '-version', '-V', '/version']
            
            for cmd in version_commands:
                try:
                    result = subprocess.run([tool_path, cmd], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        return result.stdout.strip()[:100]
                except:
                    continue
            
            return "Unknown"
        except:
            return "Unknown"

    def get_package_manager_info(self, manager_name, tool_path):
        """الحصول على معلومات مدير الحزم"""
        try:
            if manager_name == 'pip':
                result = subprocess.run([tool_path, 'list'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    packages = result.stdout.strip().split('\n')[2:]  # تجاهل العناوين
                    return {'installed_packages': len(packages)}
            
            elif manager_name == 'npm':
                result = subprocess.run([tool_path, 'list', '-g', '--depth=0'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return {'global_packages': 'Available'}
            
            return {'status': 'Available'}
        except:
            return {'status': 'Unknown'}

    def generate_advanced_statistics(self):
        """إنشاء إحصائيات متقدمة"""
        print("📊 إنشاء إحصائيات متقدمة...")
        
        stats = {
            'total_portable_tools': len(self.scan_results.get('portable_tools', {})),
            'total_embedded_tools': len(self.scan_results.get('embedded_tools', {})),
            'total_cloud_tools': len(self.scan_results.get('cloud_cli_tools', {})),
            'total_package_managers': len(self.scan_results.get('package_managers', {})),
            'total_ide_environments': len(self.scan_results.get('ide_extensions', {})),
            'total_browser_environments': len(self.scan_results.get('browser_dev_tools', {})),
            'wsl_available': self.scan_results.get('wsl_tools', {}).get('wsl_installed', False),
            'total_hidden_tools': 0
        }
        
        # حساب إجمالي الأدوات المخفية
        stats['total_hidden_tools'] = (
            stats['total_portable_tools'] +
            stats['total_embedded_tools'] +
            stats['total_cloud_tools'] +
            stats['total_package_managers']
        )
        
        self.scan_results['advanced_statistics'] = stats
        print(f"✅ تم إنشاء الإحصائيات المتقدمة")

    def save_advanced_results(self, output_dir='SHARED_REQUIREMENTS'):
        """حفظ النتائج المتقدمة"""
        print("💾 حفظ النتائج المتقدمة...")
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # حفظ النتائج الكاملة
        results_file = output_path / 'advanced_hidden_tools_scan.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        # إنشاء تقرير نصي
        report_file = output_path / 'hidden_tools_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            self.write_advanced_report(f)
        
        print(f"✅ تم حفظ النتائج في: {output_path}")

    def write_advanced_report(self, f):
        """كتابة تقرير متقدم"""
        stats = self.scan_results['advanced_statistics']
        
        f.write("🕵️ تقرير الأدوات المخفية والمتقدمة\n")
        f.write("=" * 60 + "\n")
        f.write(f"تاريخ الفحص: {self.scan_results['scan_date']}\n\n")
        
        f.write("📊 الإحصائيات المتقدمة:\n")
        f.write(f"   🎒 أدوات محمولة: {stats['total_portable_tools']}\n")
        f.write(f"   🔧 أدوات مدمجة: {stats['total_embedded_tools']}\n")
        f.write(f"   ☁️ أدوات سحابية: {stats['total_cloud_tools']}\n")
        f.write(f"   📦 مدراء حزم متقدمة: {stats['total_package_managers']}\n")
        f.write(f"   🔌 بيئات تطوير: {stats['total_ide_environments']}\n")
        f.write(f"   🌐 بيئات متصفحات: {stats['total_browser_environments']}\n")
        f.write(f"   🐧 WSL متاح: {'نعم' if stats['wsl_available'] else 'لا'}\n")
        f.write(f"   🕵️ إجمالي الأدوات المخفية: {stats['total_hidden_tools']}\n")

def main():
    """الدالة الرئيسية"""
    print("🕵️ كاشف الأدوات المخفية والمتقدمة")
    print("=" * 60)
    
    detector = AdvancedHiddenToolsDetector()
    
    # تنفيذ الكشف المتقدم
    detector.detect_portable_tools()
    detector.detect_embedded_tools()
    detector.detect_cloud_cli_tools()
    detector.detect_advanced_package_managers()
    detector.detect_ide_extensions()
    detector.detect_wsl_tools()
    detector.detect_browser_dev_tools()
    
    # إنشاء الإحصائيات وحفظ النتائج
    detector.generate_advanced_statistics()
    detector.save_advanced_results()
    
    # عرض النتائج النهائية
    stats = detector.scan_results['advanced_statistics']
    print("\n🎯 نتائج الكشف المتقدم:")
    print(f"   🎒 أدوات محمولة: {stats['total_portable_tools']}")
    print(f"   🔧 أدوات مدمجة: {stats['total_embedded_tools']}")
    print(f"   ☁️ أدوات سحابية: {stats['total_cloud_tools']}")
    print(f"   📦 مدراء حزم متقدمة: {stats['total_package_managers']}")
    print(f"   🕵️ إجمالي الأدوات المخفية: {stats['total_hidden_tools']}")
    
    print("\n🕵️ تم إكمال الكشف المتقدم للأدوات المخفية بنجاح!")
    return detector.scan_results

if __name__ == "__main__":
    main()
