# 🏺 نظام أنوبيس - متغيرات البيئة النموذجية المحسنة
# Anubis System - Enhanced Environment Variables Template
#
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب بيئتك
# Copy this file to .env and modify values according to your environment

# ===== إعدادات النظام الأساسية - Core System Settings =====
ANUBIS_ENV=development
ANUBIS_VERSION=2.0.0
ANUBIS_DEBUG=true
ANUBIS_LOG_LEVEL=info
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production

# ===== إعدادات الخادم - Server Settings =====
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=true

# ===== قاعدة البيانات - Database Settings =====
# MySQL
MYSQL_ROOT_PASSWORD=your-mysql-root-password
MYSQL_DATABASE=anubis_system
MYSQL_USER=anubis
MYSQL_PASSWORD=your-mysql-password

# Database URL
DATABASE_URL=mysql://anubis:your-mysql-password@localhost:3306/anubis_system

# ===== Redis =====
REDIS_PASSWORD=your-redis-password
REDIS_URL=redis://:your-redis-password@localhost:6379

# ===== N8N Automation =====
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your-n8n-password
N8N_ENCRYPTION_KEY=your-n8n-encryption-key

# ===== Grafana Monitoring =====
GF_SECURITY_ADMIN_USER=admin
GF_SECURITY_ADMIN_PASSWORD=your-grafana-password

# ===== خدمات الذكاء الاصطناعي - AI Services =====
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Google Gemini
GOOGLE_API_KEY=your-google-api-key

# Anthropic Claude
ANTHROPIC_API_KEY=your-anthropic-api-key

# ===== LangSmith (اختياري) - LangSmith (Optional) =====
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your-langsmith-api-key
LANGCHAIN_PROJECT=anubis-system

# ===== إعدادات الأمان - Security Settings =====
JWT_SECRET_KEY=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# ===== إعدادات التطوير - Development Settings =====
# Jupyter
JUPYTER_TOKEN=your-jupyter-token

# Streamlit
STREAMLIT_SERVER_PORT=8501

# ===== إعدادات الشبكة - Network Settings =====
ANUBIS_HOST=0.0.0.0
ANUBIS_PORT=8000

# ===== إعدادات السجلات - Logging Settings =====
LOG_LEVEL=INFO
LOG_FILE=logs/anubis.log

# ===== إعدادات Docker - Docker Settings =====
COMPOSE_PROJECT_NAME=anubis
DOCKER_BUILDKIT=1

# ===== ملاحظات مهمة - Important Notes =====
# 1. لا تشارك هذا الملف مع كلمات المرور الحقيقية
# 2. استخدم كلمات مرور قوية ومعقدة
# 3. قم بتغيير جميع كلمات المرور الافتراضية
# 4. احتفظ بنسخة احتياطية آمنة من كلمات المرور
