#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 طالب مساعدة Gemini CLI لنظام أنوبيس
Anubis Gemini CLI Assistant Request
"""

import json
import subprocess
import sys
from pathlib import Path
from datetime import datetime

class AnubisGeminiAssistant:
    """طالب مساعدة Gemini CLI"""
    
    def __init__(self):
        self.analysis_summary = {}
        self.gemini_available = False
        
    def check_gemini_availability(self):
        """فحص توفر Gemini CLI"""
        try:
            result = subprocess.run(['gemini', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.gemini_available = True
                print("✅ Gemini CLI متوفر")
                return True
            else:
                print("❌ Gemini CLI غير متوفر")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            print("❌ Gemini CLI غير مثبت أو غير متوفر")
            return False
    
    def load_analysis_results(self):
        """تحميل نتائج التحليل السابقة"""
        analysis_files = [
            "anubis_cline_analysis_report_*.json",
            "anubis_agents_cline_analysis_*.json"
        ]
        
        results = {}
        for pattern in analysis_files:
            files = list(Path(".").glob(pattern))
            if files:
                latest_file = max(files, key=lambda x: x.stat().st_mtime)
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        results[latest_file.stem] = data
                        print(f"✅ تم تحميل: {latest_file}")
                except Exception as e:
                    print(f"❌ خطأ في تحميل {latest_file}: {e}")
        
        self.analysis_summary = results
        return results
    
    def create_gemini_prompt(self):
        """إنشاء prompt لـ Gemini"""
        prompt = """
🏺 مساعدة مطلوبة من Gemini CLI لتحليل محادثة Cline

## السياق:
تم تحليل محادثة طويلة (55,263 سطر) بين مستخدم ومساعد Cline AI حول تطوير نظام أنوبيس للمساعدين الذكيين.

## النتائج الأساسية:
- المحادثة استمرت ~15 ساعة
- 159 رسالة من كل طرف
- 70 أمر منفذ
- 857 خطأ مكتشف
- 123 ملف تم إنشاؤه

## التقنيات المستخدمة:
- Python (2,867 ذكر)
- Docker (2,111 ذكر) 
- قواعد البيانات (1,582 ذكر)
- JSON (870 ذكر)
- Markdown (537 ذكر)

## الإنجازات:
✅ نظام قاعدة بيانات متكامل
✅ 15 أداة متخصصة للفحص
✅ أنظمة عزل متقدمة
✅ مجموعة شاملة من الاختبارات
✅ توثيق مفصل

## المطلوب من Gemini:
1. تحليل إضافي للأنماط المخفية في المحادثة
2. اقتراحات لتحسين كفاءة التطوير
3. تحديد نقاط القوة والضعف في سير العمل
4. توصيات لتحسين التعاون بين الإنسان والذكاء الاصطناعي
5. اقتراحات لتطوير النظام مستقبلياً

## الأسئلة المحددة:
- ما هي أكثر الأنماط فعالية في هذا التعاون؟
- كيف يمكن تقليل معدل الأخطاء (857 خطأ)؟
- ما هي أفضل استراتيجيات لإدارة مشاريع بهذا الحجم؟
- كيف يمكن تحسين تجربة المطور؟

يرجى تقديم تحليل عميق ونصائح عملية قابلة للتطبيق.
"""
        return prompt
    
    def request_gemini_analysis(self):
        """طلب تحليل من Gemini CLI"""
        if not self.gemini_available:
            print("⚠️  Gemini CLI غير متوفر - سيتم إنشاء طلب مساعدة بديل")
            return self.create_alternative_request()
        
        prompt = self.create_gemini_prompt()
        
        try:
            # محاولة استخدام Gemini CLI
            result = subprocess.run(['gemini', 'chat', '--prompt', prompt], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ تم الحصول على استجابة من Gemini")
                return result.stdout
            else:
                print(f"❌ خطأ في Gemini CLI: {result.stderr}")
                return self.create_alternative_request()
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل Gemini CLI: {e}")
            return self.create_alternative_request()
    
    def create_alternative_request(self):
        """إنشاء طلب مساعدة بديل"""
        request = f"""
🏺 طلب مساعدة من Gemini CLI - نظام أنوبيس

## 📋 ملخص المشروع:
تم تطوير نظام أنوبيس للمساعدين الذكيين من خلال محادثة مكثفة مع Cline AI.

## 📊 إحصائيات المحادثة:
- الحجم: 55,263 سطر (2M+ حرف)
- المدة: ~15 ساعة
- التبادلات: 159 من كل طرف
- الأوامر: 70 أمر منفذ
- الأخطاء: 857 خطأ مكتشف
- الملفات: 123 ملف منشأ

## 🛠️ التقنيات الرئيسية:
1. Python - 2,867 ذكر
2. Docker - 2,111 ذكر  
3. قواعد البيانات - 1,582 ذكر
4. JSON - 870 ذكر
5. Markdown - 537 ذكر

## ✅ الإنجازات:
- نظام قاعدة بيانات متكامل (MySQL + SQLite)
- 15 أداة متخصصة للفحص والتحليل
- أنظمة عزل متقدمة للأمان
- مجموعة شاملة من الاختبارات
- توثيق مفصل لجميع المكونات

## 🎯 المطلوب من Gemini:

### 1. تحليل الأنماط:
- ما هي أكثر أنماط التعاون فعالية؟
- كيف يمكن تحسين كفاءة حل المشاكل؟
- ما هي العوامل المؤثرة على معدل النجاح؟

### 2. تحسين سير العمل:
- كيف يمكن تقليل معدل الأخطاء من 857؟
- ما هي أفضل استراتيجيات إدارة المشاريع الكبيرة؟
- كيف يمكن تحسين التعاون بين الإنسان والذكاء الاصطناعي؟

### 3. التطوير المستقبلي:
- ما هي أولويات التطوير القادمة؟
- كيف يمكن تحسين تجربة المطور؟
- ما هي التقنيات الجديدة المقترحة؟

### 4. الأمان والجودة:
- كيف يمكن تحسين أنظمة الأمان؟
- ما هي أفضل ممارسات ضمان الجودة؟
- كيف يمكن تحسين أنظمة المراقبة؟

## 📞 طرق التواصل:
إذا كان Gemini CLI متوفراً، يرجى تشغيل:
```bash
gemini chat --file="ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md"
```

أو استخدام واجهة Gemini الويب مع هذا الطلب.

## 📅 معلومات إضافية:
- تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- المحلل: نظام أنوبيس للوكلاء الذكيين
- الملفات المرجعية: 
  * ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md
  * anubis_cline_analysis_report_*.json
  * anubis_agents_cline_analysis_*.json

نتطلع لتحليلكم العميق ونصائحكم القيمة! 🏺✨
"""
        return request
    
    def save_gemini_request(self, content):
        """حفظ طلب Gemini"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_gemini_request_{timestamp}.md"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"💾 تم حفظ طلب Gemini: {filename}")
            return filename
        except Exception as e:
            print(f"❌ خطأ في حفظ طلب Gemini: {e}")
            return None
    
    def generate_summary_report(self):
        """إنشاء تقرير ملخص للمساعدة المطلوبة"""
        print("\n" + "🏺" + "=" * 60)
        print("🤖 طلب مساعدة Gemini CLI - نظام أنوبيس")
        print("=" * 62)
        
        print(f"\n📊 حالة Gemini CLI:")
        print(f"   {'✅ متوفر' if self.gemini_available else '❌ غير متوفر'}")
        
        print(f"\n📋 ملفات التحليل المتوفرة:")
        for filename in self.analysis_summary.keys():
            print(f"   📄 {filename}")
        
        print(f"\n🎯 المساعدة المطلوبة:")
        print("   🔍 تحليل الأنماط المخفية")
        print("   ⚡ تحسين كفاءة التطوير") 
        print("   🚨 تقليل معدل الأخطاء")
        print("   🤝 تحسين التعاون الإنسان-AI")
        print("   🚀 اقتراحات التطوير المستقبلي")
        
        print(f"\n💡 الخطوات التالية:")
        if self.gemini_available:
            print("   1. تشغيل Gemini CLI مع الطلب المحفوظ")
            print("   2. مراجعة النتائج والتوصيات")
            print("   3. تطبيق التحسينات المقترحة")
        else:
            print("   1. تثبيت Gemini CLI أو استخدام الواجهة الويب")
            print("   2. استخدام الطلب المحفوظ للحصول على المساعدة")
            print("   3. تطبيق التوصيات على النظام")
    
    def run_gemini_assistance(self):
        """تشغيل طلب مساعدة Gemini الكامل"""
        print("🏺 بدء طلب مساعدة Gemini CLI...")
        
        # فحص توفر Gemini
        self.check_gemini_availability()
        
        # تحميل نتائج التحليل
        self.load_analysis_results()
        
        # طلب المساعدة
        response = self.request_gemini_analysis()
        
        # حفظ الطلب
        self.save_gemini_request(response)
        
        # إنشاء التقرير
        self.generate_summary_report()
        
        print("\n✅ تم إكمال طلب مساعدة Gemini!")
        return response

def main():
    """الدالة الرئيسية"""
    assistant = AnubisGeminiAssistant()
    assistant.run_gemini_assistance()

if __name__ == "__main__":
    main()
