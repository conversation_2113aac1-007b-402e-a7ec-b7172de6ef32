{"system": {"name": "Universal AI Assistants", "version": "1.0.0", "language": "ar", "encoding": "utf-8", "created_date": "2025-07-14T12:47:18.980369"}, "project": {"auto_detect_type": true, "supported_types": ["streamlit", "django", "<PERSON><PERSON><PERSON>", "flask", "custom"], "default_type": "custom"}, "agents": {"database_agent": {"enabled": true, "database": {"type": "sqlite", "name": "project_db", "host": "localhost", "user": "root", "password": "", "port": 3306, "required_tables": []}, "auto_backup": true, "check_performance": true, "check_security": true, "supported_databases": ["mysql", "postgresql", "sqlite"]}, "file_organizer_agent": {"enabled": true, "auto_organize": false, "backup_before_organize": true, "dry_run": false, "organization_rules": {"core_files": {"target_dir": "", "patterns": ["main.py", "app.py", "requirements.txt", "README.md", "setup.py"], "description": "الملفات الأساسية للمشروع"}, "source_code": {"target_dir": "src", "patterns": ["*.py"], "exclude": ["main.py", "app.py", "setup.py", "test_*.py", "*_test.py"], "description": "ملفات الكود المصدري"}, "tests": {"target_dir": "tests", "patterns": ["test_*.py", "*_test.py", "conftest.py"], "extensions": [".py"], "description": "ملفات الاختبارات"}, "documentation": {"target_dir": "docs", "patterns": ["*.md", "*.txt", "*.rst"], "exclude": ["README.md"], "description": "الوثائق والتقارير"}, "configuration": {"target_dir": "config", "patterns": ["*.json", "*.yaml", "*.yml", "*.ini", "*.cfg", "*.toml"], "description": "ملفات التكوين"}, "data": {"target_dir": "data", "patterns": ["*.csv", "*.json", "*.xlsx", "*.xml", "*.db", "*.sqlite"], "description": "ملفات البيانات"}, "static": {"target_dir": "static", "patterns": ["*.css", "*.js", "*.html", "*.png", "*.jpg", "*.svg", "*.ico"], "description": "الملفات الثابتة"}, "logs": {"target_dir": "logs", "patterns": ["*.log"], "description": "ملفات السجلات"}, "temp": {"target_dir": "temp", "patterns": ["*.tmp", "*.cache", "*.pyc", "__pycache__"], "description": "الملفات المؤقتة"}, "archive": {"target_dir": "archive", "patterns": ["*backup*", "*old*", "*archive*"], "description": "الملفات المؤرشفة"}}}, "memory_agent": {"enabled": true, "auto_save": true, "memory_retention_days": 30, "max_memory_size_mb": 100}, "error_detector_agent": {"enabled": true, "check_syntax": true, "check_imports": true, "check_style": true, "auto_fix": false}, "project_analyzer_agent": {"enabled": true, "deep_analysis": true, "performance_check": true, "security_scan": true}}, "workspace": {"base_dir": "workspace", "subdirs": {"logs": "logs", "reports": "reports", "backups": "backups", "memory": "shared_memory", "collaboration": "collaboration_logs"}, "auto_cleanup": true, "max_log_size_mb": 50}, "database": {"default_type": "sqlite", "connection_timeout": 30, "backup_frequency": "daily", "security_checks": true}, "logging": {"level": "INFO", "format": "[%(asctime)s] %(name)s: %(message)s", "max_file_size_mb": 10, "backup_count": 5}, "plugins": {"enabled": true, "auto_load": true, "plugin_dir": "plugins"}}