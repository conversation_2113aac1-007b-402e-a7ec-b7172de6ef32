version: '3.8'

services:
  anubis-n8n:
    build: .
    container_name: anubis-n8n-isolated
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد للأتمتة
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # الشبكات المعزولة
    networks:
      - anubis-workflows-net
      - anubis-automation-secure-net
    
    # الأحجام المعزولة
    volumes:
      - anubis-n8n-data:/home/<USER>/.n8n
      - anubis-workflows-data:/app/workflows
      - anubis-credentials-vault:/app/credentials:ro
      - anubis-automation-logs:/app/logs:rw
    
    # متغيرات البيئة الآمنة
    environment:
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY:-anubis_secure_key_2024}
      - N8N_USER_MANAGEMENT_DISABLED=false
      - N8N_SECURE_COOKIE=true
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://anubis-workflows.local
      - N8N_METRICS=true
      - DB_TYPE=sqlite
      - N8N_LOG_LEVEL=info
      - GENERIC_TIMEZONE=UTC
    
    # حماية النظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=200m
      - /var/tmp:rw,noexec,nosuid,size=100m
    
    # إزالة الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
    
    # المنافذ المحمية
    ports:
      - "127.0.0.1:5678:5678"
    
    # تسميات للمراقبة
    labels:
      - "anubis.component=workflows"
      - "anubis.isolation.level=advanced"
      - "anubis.automation.enabled=true"
    
    # التبعيات
    depends_on:
      - anubis-workflows-db
      - anubis-automation-monitor
  
  anubis-workflows-db:
    image: postgres:15-alpine
    container_name: anubis-workflows-db
    restart: unless-stopped
    networks:
      - anubis-automation-secure-net
    volumes:
      - anubis-workflows-db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=n8n_workflows
      - POSTGRES_USER=anubis_workflows
      - POSTGRES_PASSWORD=${WORKFLOWS_DB_PASSWORD:-anubis_workflows_secure_2024}
    security_opt:
      - no-new-privileges:true
    
  anubis-automation-monitor:
    image: prom/prometheus:latest
    container_name: anubis-automation-monitor
    restart: unless-stopped
    networks:
      - anubis-workflows-net
    volumes:
      - ./monitoring/prometheus-workflows.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-automation-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9093:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
  
  anubis-workflows-vault:
    image: vault:latest
    container_name: anubis-workflows-vault
    restart: unless-stopped
    networks:
      - anubis-automation-secure-net
    volumes:
      - anubis-workflows-vault-data:/vault/data
      - ./security/vault-workflows-config.hcl:/vault/config/vault.hcl:ro
    ports:
      - "127.0.0.1:8201:8200"
    cap_add:
      - IPC_LOCK
    security_opt:
      - no-new-privileges:true
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=anubis-workflows-token
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
  
  anubis-automation-scheduler:
    build: .
    container_name: anubis-automation-scheduler
    restart: unless-stopped
    networks:
      - anubis-workflows-net
    volumes:
      - anubis-workflows-data:/app/workflows:ro
      - anubis-automation-logs:/app/logs:rw
    environment:
      - SCHEDULER_MODE=isolated
      - WORKFLOWS_PATH=/app/workflows
      - LOG_LEVEL=info
    security_opt:
      - no-new-privileges:true
    command: ["node", "-e", "console.log('Scheduler running...'); setInterval(() => console.log('Checking workflows...'), 60000);"]

# الشبكات المعزولة للأتمتة
networks:
  anubis-workflows-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
  
  anubis-automation-secure-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# الأحجام المعزولة للأتمتة
volumes:
  anubis-n8n-data:
    driver: local
  anubis-workflows-data:
    driver: local
  anubis-credentials-vault:
    driver: local
  anubis-automation-logs:
    driver: local
  anubis-workflows-db-data:
    driver: local
  anubis-automation-monitor-data:
    driver: local
  anubis-workflows-vault-data:
    driver: local
