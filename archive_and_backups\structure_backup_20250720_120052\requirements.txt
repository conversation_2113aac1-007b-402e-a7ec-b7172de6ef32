# 🏺 نظام أنوبيس - المتطلبات الموحدة
# Anubis System - Unified Requirements
# تم توحيد جميع المتطلبات من المجلدات المختلفة

# ===== المتطلبات الأساسية - Core Requirements =====
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
requests==2.31.0
python-multipart==0.0.6
sqlalchemy==2.0.23
python-dotenv==1.0.0
jinja2==3.1.2
aiofiles==23.2.1
httpx==0.25.2

# ===== قواعد البيانات - Database Support =====
mysql-connector-python>=8.0.0
psycopg2-binary>=2.9.0

# ===== معالجة الملفات والنصوص - File Processing =====
chardet>=5.0.0
pathspec>=0.10.0
pyyaml>=6.0
python-dateutil>=2.8.0

# ===== السجلات والمراقبة - Logging & Monitoring =====
colorlog>=6.7.0
prometheus-client>=0.16.0

# ===== أدوات التطوير - Development Tools =====
black>=22.0.0
flake8>=5.0.0
pylint>=2.17.0
pytest>=7.0.0
pytest-cov>=4.0.0
tqdm>=4.64.0
click>=8.0.0

# ===== الذكاء الاصطناعي - AI & ML =====
torch>=2.0.0
transformers>=4.30.0
langchain>=0.0.200
openai>=1.0.0
google-generativeai>=0.5.0
anthropic>=0.25.0
sentence-transformers>=2.2.0
tiktoken>=0.5.0

# ===== قواعد البيانات المتجهة - Vector Databases =====
chromadb>=0.4.0
faiss-cpu>=1.7.0
redis>=4.5.0

# ===== الأمان - Security =====
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=4.0.0
cryptography>=41.0.0

# ===== تحليل البيانات - Data Analysis =====
pandas>=2.0.0
numpy>=1.24.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
scipy>=1.10.0
scikit-learn>=1.3.0

# ===== أدوات النظام - System Tools =====
psutil>=5.9.0

# ===== أدوات إضافية - Additional Tools =====
streamlit>=1.28.0
jupyter>=1.0.0
jupyterlab>=4.0.0

# ===== متطلبات أساسية إضافية - Additional Core =====
pathlib2>=2.3.7
typing-extensions>=4.0.0
