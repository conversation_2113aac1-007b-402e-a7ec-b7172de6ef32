# 🚀 مجلد السكريبتات - نظام أنوبيس
## Scripts Directory - Anubis System

**آخر تحديث**: 2025-07-16  
**الحالة**: ✅ محسن بالتعاون مع Gemini CLI  
**عدد السكريبتات**: 11 سكريبت نشط  

---

## 📜 الوصف

مجلد `scripts` يحتوي على مجموعة شاملة من السكريبتات المساعدة لنظام أنوبيس. هذه السكريبتات تغطي جميع جوانب النظام من الإعداد والاختبار إلى التشغيل والصيانة والتكامل مع الخدمات الخارجية.

## 📁 تصنيف السكريبتات

### **🔗 سكريبتات LangSmith والتتبع:**
- **`activate_real_langsmith.py`**: تفعيل التتبع الحقيقي لـ LangSmith
- **`setup_langsmith.py`**: إعداد وتكوين LangSmith
- **`simple_langsmith_test.py`**: اختبار بسيط لـ LangSmith
- **`test_langsmith_integration.py`**: اختبار تكامل متقدم مع LangSmith
- **`langsmith_integration_demo.py`**: عرض توضيحي لتكامل LangSmith

### **🤖 سكريبتات الوكلاء والذكاء الاصطناعي:**
- **`agents_cleanup.py`**: تنظيف وصيانة الوكلاء
- **`fix_agents_with_gemini.py`**: إصلاح الوكلاء بمساعدة Gemini
- **`check_ollama.py`**: فحص حالة خدمة Ollama
- **`smart_workflow_demo.py`**: عرض سير العمل الذكي

### **🛠️ سكريبتات التنظيم والإدارة:**
- **`complete_file_organizer.py`**: منظم ملفات شامل
- **`organize_project_files.py`**: تنظيم ملفات المشروع

### **🤝 سكريبتات التكامل الخارجي:**
- **`gemini_cli_helper.py`**: مساعد للتكامل مع Gemini CLI
- **`gemini_integration_system.py`**: نظام تكامل شامل مع Gemini

---

## 🚀 كيفية الاستخدام

### **1. سكريبتات LangSmith:**
```bash
# إعداد LangSmith
python scripts/setup_langsmith.py

# تفعيل التتبع الحقيقي
python scripts/activate_real_langsmith.py

# اختبار بسيط
python scripts/simple_langsmith_test.py

# عرض توضيحي
python scripts/langsmith_integration_demo.py
```

### **2. سكريبتات الوكلاء:**
```bash
# فحص Ollama
python scripts/check_ollama.py

# تنظيف الوكلاء
python scripts/agents_cleanup.py

# إصلاح الوكلاء مع Gemini
python scripts/fix_agents_with_gemini.py

# عرض سير العمل الذكي
python scripts/smart_workflow_demo.py
```

### **3. سكريبتات التنظيم:**
```bash
# تنظيم شامل للملفات
python scripts/complete_file_organizer.py

# تنظيم ملفات المشروع
python scripts/organize_project_files.py
```

### **4. سكريبتات Gemini:**
```bash
# مساعد Gemini CLI
python scripts/gemini_cli_helper.py

# نظام تكامل Gemini
python scripts/gemini_integration_system.py
```

---

## 📊 تفاصيل السكريبتات

### **🔗 activate_real_langsmith.py**
- **الوظيفة**: تفعيل التتبع الحقيقي لـ LangSmith
- **الميزات**: 
  - اختبار الاتصال
  - إنشاء traces تجريبية
  - اختبار الوكلاء
  - تقرير شامل
- **الاستخدام**: `python scripts/activate_real_langsmith.py`

### **🧹 agents_cleanup.py**
- **الوظيفة**: تنظيف وصيانة الوكلاء
- **الميزات**:
  - إزالة الملفات المؤقتة
  - تنظيف السجلات القديمة
  - إعادة تنظيم الكود
- **الاستخدام**: `python scripts/agents_cleanup.py`

### **🔍 check_ollama.py**
- **الوظيفة**: فحص حالة خدمة Ollama
- **الميزات**:
  - فحص الاتصال
  - اختبار النماذج
  - تقرير الحالة
- **الاستخدام**: `python scripts/check_ollama.py`

### **📁 complete_file_organizer.py**
- **الوظيفة**: منظم ملفات شامل
- **الميزات**:
  - تصنيف الملفات
  - إنشاء مجلدات
  - نقل وتنظيم
- **الاستخدام**: `python scripts/complete_file_organizer.py`

### **🔧 fix_agents_with_gemini.py**
- **الوظيفة**: إصلاح الوكلاء بمساعدة Gemini
- **الميزات**:
  - تحليل الأخطاء
  - اقتراح الحلول
  - تطبيق الإصلاحات
- **الاستخدام**: `python scripts/fix_agents_with_gemini.py`

### **🤝 gemini_cli_helper.py**
- **الوظيفة**: مساعد للتكامل مع Gemini CLI
- **الميزات**:
  - واجهة موحدة
  - معالجة الأخطاء
  - تنسيق النتائج
- **الاستخدام**: `python scripts/gemini_cli_helper.py`

### **🌐 gemini_integration_system.py**
- **الوظيفة**: نظام تكامل شامل مع Gemini
- **الميزات**:
  - تكامل متقدم
  - إدارة الجلسات
  - تتبع العمليات
- **الاستخدام**: `python scripts/gemini_integration_system.py`

### **📊 langsmith_integration_demo.py**
- **الوظيفة**: عرض توضيحي لتكامل LangSmith
- **الميزات**:
  - أمثلة عملية
  - حالات استخدام
  - أفضل الممارسات
- **الاستخدام**: `python scripts/langsmith_integration_demo.py`

### **📂 organize_project_files.py**
- **الوظيفة**: تنظيم ملفات المشروع
- **الميزات**:
  - هيكلة المجلدات
  - تصنيف الملفات
  - تنظيف المكررات
- **الاستخدام**: `python scripts/organize_project_files.py`

### **⚙️ setup_langsmith.py**
- **الوظيفة**: إعداد وتكوين LangSmith
- **الميزات**:
  - تكوين المتغيرات
  - إنشاء الملفات
  - اختبار الإعداد
- **الاستخدام**: `python scripts/setup_langsmith.py`

### **🧪 simple_langsmith_test.py**
- **الوظيفة**: اختبار بسيط لـ LangSmith
- **الميزات**:
  - اختبار سريع
  - تحقق أساسي
  - تقرير مبسط
- **الاستخدام**: `python scripts/simple_langsmith_test.py`

### **🔗 smart_workflow_demo.py**
- **الوظيفة**: عرض سير العمل الذكي
- **الميزات**:
  - تنسيق الوكلاء
  - سير عمل متقدم
  - تتبع العمليات
- **الاستخدام**: `python scripts/smart_workflow_demo.py`

### **🧪 test_langsmith_integration.py**
- **الوظيفة**: اختبار تكامل متقدم مع LangSmith
- **الميزات**:
  - اختبارات شاملة
  - تحقق متقدم
  - تقرير مفصل
- **الاستخدام**: `python scripts/test_langsmith_integration.py`

---

## 🔧 أفضل الممارسات

### **1. قبل تشغيل السكريبتات:**
- تأكد من تثبيت المتطلبات
- تحقق من متغيرات البيئة
- تأكد من تشغيل الخدمات المطلوبة

### **2. أثناء التشغيل:**
- راقب المخرجات والسجلات
- تحقق من رسائل الخطأ
- استخدم الوضع المفصل عند الحاجة

### **3. بعد التشغيل:**
- راجع التقارير المُنتجة
- تحقق من النتائج
- احفظ السجلات المهمة

---

## 📈 مقاييس الأداء

### **أوقات التنفيذ المتوقعة:**
- **السكريبتات السريعة**: 5-15 ثانية
- **السكريبتات المتوسطة**: 30-60 ثانية
- **السكريبتات الشاملة**: 2-5 دقائق

### **استهلاك الموارد:**
- **الذاكرة**: 100-500 MB
- **المعالج**: 10-50% لفترات قصيرة
- **التخزين**: ملفات مؤقتة < 100 MB

---

## 🛠️ استكشاف الأخطاء

### **مشاكل شائعة:**
1. **خطأ في الاستيراد**: تحقق من PYTHONPATH
2. **خطأ في الاتصال**: تحقق من الخدمات الخارجية
3. **خطأ في الصلاحيات**: تحقق من صلاحيات الملفات

### **حلول سريعة:**
```bash
# إعادة تعيين البيئة
export PYTHONPATH="${PYTHONPATH}:."

# تحقق من Ollama
ollama list

# تحقق من LangSmith
echo $LANGCHAIN_API_KEY
```

---

<div align="center">

**🚀 مجلد السكريبتات - نظام أنوبيس**

**أدوات قوية لإدارة وتشغيل النظام**

[![Scripts](https://img.shields.io/badge/Scripts-13%20Active-blue.svg)](README.md)
[![LangSmith](https://img.shields.io/badge/LangSmith-Integrated-brightgreen.svg)](README.md)
[![Gemini](https://img.shields.io/badge/Gemini-Supported-gold.svg)](README.md)

**محسن بالتعاون مع Gemini CLI**

</div>
