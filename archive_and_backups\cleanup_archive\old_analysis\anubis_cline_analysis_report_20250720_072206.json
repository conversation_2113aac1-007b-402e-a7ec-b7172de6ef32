{"analysis_results": {"basic_stats": {"total_lines": 55263, "total_characters": 2006826, "total_words": 170690, "user_messages": 159, "assistant_messages": 159, "commands": 70, "file_operations": 0, "errors": 857}, "timestamps": {"count": 161, "first": "7/19/2025, 4:03:31 PM", "last": "7/20/2025, 7:12:56 AM"}, "commands": {"total": 70, "types": {"python": 23, "other": 23, "package_management": 1, "docker": 21, "navigation": 2}, "most_common": [["python", 23], ["other", 23], ["docker", 21], ["navigation", 2], ["package_management", 1]]}, "file_operations": {"file_creation": 123, "file_editing": 145, "file_reading": 1621, "file_deletion": 783}, "errors": {"total": 597, "types": {"Error:": 18, "Error: cannot unpack non-iterable bool object": 1, "Error: name 'sqlite3' is n": 1, "Error: name 'sqlite3' is not defined. Did you forg": 1, "Error: unsupported operand type(s) f": 1, "error: error.message,": 3, "Error: No module named 'docker'": 13, "ERROR: Operation cancelled by user": 1, "ERROR: CreateProcessCommon:798: execvpe(/bin/bash)...": 1, "ERROR: Could not find a version that satisfies the...": 6, "ERROR: No matching distribution found for sqlite3": 5, "Error: No mo": 19, "Error: No module named 'anubis')": 10, "Error: (:) [], ParentContainsErrorRecordException": 1, "Exception:": 4, "❌ ملف إعدادات قاعدة البيانات غير موجود\")": 2, "❌ فارغة'}\")": 2, "❌ خطأ في قراءة الإعدادات: {e}\")": 2, "❌ خطأ SQLite: {e}\")": 2, "❌ فشل الاتصال بخادم MySQL\")": 2, "❌ مكتبة mysql-connector-python غير مثبتة\")": 2, "❌ خطأ MySQL: {e}\")": 2, "❌ مشكلة'}\")": 4, "❌ ملف إعدادات قاعدة البيانات غير موجود": 1, "❌ ملف إعدادات قاعدة البيانات غير موجود في المسارات...": 2, "❌ خطأ في تحميل النواة الأساسية: {e}\")": 1, "❌ فشل\"": 1, "❌ خطأ في المسار: {e}\")": 1, "❌ خطأ في التكوين: {e}\")": 1, "❌ خطأ في التهيئة: {e}\")": 1, "❌ خطأ في التنفيذ: {e}\")": 1, "❌ خطأ غير متوقع: {e}\")": 1, "❌ المشكلة: 'datetime.datetime' imported but unused": 1, "❌ المشكلة: blank line contains whitespace": 1, "❌ المشكلة: expected 2 blank lines, found 1": 1, "❌ المشكلة: f-string is missing placeholders": 1, "❌ المشكلة: line too long (>79 characters)": 1, "❌ محذوف": 1, "❌": 1, "❌ ما لا يعمل حال<|im_start|>هم:": 1, "❌ لا يعمل: 4 وكلاء تقليديين (تحتاج إصلاح)": 1, "❌ فشل الاتصال بقاعدة البيانات')": 1, "❌ خطأ: {e}')": 3, "❌ فشل\\\"}')": 1, "❌ {file} مفقود\")": 2, "❌ مفقود\"": 26, "❌ مجلد {subdir}/ مفقود\")": 2, "❌ نظام {system_name} مفقود\")": 2, "❌ {script} مفقود\")": 2, "❌ JSON غير صحيح\"": 2, "❌ {config} - JSON غير صحيح\")": 2, "❌ {config} مفقود\")": 2, "❌ مجلد archive/ مفقود\")": 2, "❌ {subdir}/ مفقود\")": 2, "❌ مجلد backup/ مفقود\")": 2, "❌ فئة {category} مفقودة\")": 2, "❌ مجلد duplicate_reports/ مفقود\")": 2, "❌ مجلد old_databases/ مفقود\")": 2, "❌ المشاكل: {total_issues}\")": 2, "❌ المشاكل: 2": 1, "❌ مجلد configs/ مفقود تماماً\")": 2, "❌ {config_file} - JSON غير صحيح\")": 2, "❌ مجلد database/ مفقود\")": 2, "❌ لا توجد ملفات قاعدة بيانات\")": 2, "❌ {db_file.name} غير قابل للوصول\")": 2, "❌ Gemini: خطأ في تحليل الأداء: {e}\")": 2, "❌ مجلد isolation_systems/ مفقود تماماً\")": 2, "❌ خطأ في قراءة ملف Docker: {e}\")": 2, "❌ غير موجود\")": 2, "❌ **0 ملفات** - المجلد فارغ تماماً": 2, "❌ **لا يوجد تكوين Docker**": 2, "❌ **0 ملفات** - لا توجد إعدادات عزل": 1, "❌ **لا توجد تكوينات أمان**": 1, "❌ **0 ملفات** - لا يوجد توثيق للعزل": 1, "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً.\"": 2, "❌ docker-compose غير مثبت. يرجى تثبيت docker-compo...": 2, "❌ Docker غير مثبت\"": 9, "❌ docker-compose غير مثبت\"": 9, "❌ مجلد tools_and_utilities/ مفقود\")": 2, "❌ مجلد {main_dir}/ مفقود\")": 2, "❌ مجلد vscode-optimizer/ مفقود\")": 2, "❌ مشروع {project} مفقود\")": 2, "❌ أدوات الطوارئ مفقودة\")": 2, "❌ خطأ في المراقبة: {e}\")": 2, "❌ فشل: {failed}\")": 2, "❌ مجلد universal_ai_system/ مفقود تماماً\")": 4, "❌ المشاكل الحرجة:**": 1, "❌ فشل تحميل النماذج": 2, "❌ مجلد workflows_and_automation/ مفقود\")": 2, "❌ نظام n8n غير موجود\")": 2, "❌ مجلد workspace/ مفقود\")": 2, "❌ مفقود (0 ملف)": 3, "❌ مفقود - يحتاج إنشاء": 1, "❌ مفقود - يحتاج إعدادات": 1, "❌ مفقود - مجلد مؤقت": 1, "❌ خطأ في نقل {item.name}: {e}\")": 8, "❌ خطأ في نقل {filename}: {e}\")": 2, "❌ Docker غير متاح\")": 4, "❌ خطأ في اختبار Docker: {e}\")": 6, "❌ Docker Compose غير متاح\")": 6, "❌ خطأ في اختبار Docker Compose: {e}\")": 4, "❌ خطأ في اختبار الاتصال: {e}\")": 4, "❌ خطأ في اختبار {service_name}: {e}\")": 4, "❌ خطأ في قياس استخدام القرص: {e}\")": 4, "❌ فشل: {failed_tests} اختبار\")": 2, "❌ الاختبارات الفاشلة: {summary.get('failed_tests',...": 2, "❌ غير متاح'}\")": 10, "❌ غير متاح\"": 26, "❌ الإخفاقات:\")": 2, "❌ غير متاح\")": 10, "❌ خطأ - {e}\")": 2, "❌ غير متصل\")": 2, "❌ خطأ في قياس مساحة القرص: {e}\")": 2, "❌\"": 20, "❌ العقد المخصصة: غير موجودة\")": 2, "❌ {db_path}\")": 2, "❌ غير متصل'}\")": 2, "❌ المشاكل المكتشفة:\")": 2, "❌ anubis_main_system": 1, "❌ غير متاح النظام الرئيسي": 10, "❌ غير متاح نظام الذكاء الاصطناعي": 10, "❌ غير متاح سير العمل (n8n)": 10, "❌ غير متاح بيئة العمل (Jupyter)": 6, "❌ غير متاح المراقبة (Prometheus)": 1, "❌ غير متاح قاعدة البيانات المتجهة": 1, "❌ غير متاح واجهة n8n": 1, "❌ غير متاح postgresql": 1, "❌ غير متاح redis": 1, "❌ غير متاح chroma_db": 1, "❌ غير متاح": 7, "❌ المشاكل المكتشفة:": 1, "❌ anubis_main_system (مفقود)": 1, "❌ **غير المتاح:**": 1, "❌ **يحتاج تشغيل:**": 1, "❌ **الخدمات المتقدمة (تحتاج تشغيل):**": 1, "خطأ في قراءة الإعدادات: {e}\")": 2, "خطأ SQLite: {e}\")": 2, "خطأ MySQL: {e}\")": 2, "خطأ في تحميل النواة الأساسية: {e}\")": 1, "خطأ في التكوين: {e}\")": 2, "خطأ في تهيئة النظام: {e}\")": 1, "خطأ أثناء تشغيل العمليات: {e}\")": 1, "خطأ في حفظ التقرير: {e}\")": 1, "خطأ في المسار: {e}\")": 1, "خطأ في التهيئة: {e}\")": 1, "خطأ في التنفيذ: {e}\")": 1, "خطأ غير متوقع: {e}\")": 1, "خطأ تم اكتشافها في المشروع": 1, "خطأ؟\"": 1, "خطأ المسافات:": 1, "خطأ في استخدام LangSmith API. دعني أصحح السكريبت:": 1, "خطأ: {e}')": 3, "خطأ وأعيد الاختبار:": 1, "خطأ معقد": 1, "خطأ:": 1, "خطأ تعاوني": 1, "خطأ حرج - النظام يحتاج ملفات التكوين للعمل\")": 2, "خطأ في تحليل الأداء: {e}\")": 2, "خطأ حرج - النظام يحتاج أنظمة عزل للأمان\")": 2, "خطأ في قراءة ملف Docker: {e}\")": 2, "خطأ في المراقبة: {e}\")": 2, "خطأ في استيراد مراقب النظام: {e}\")": 2, "خطأ في نسخ {dir_name}: {e}\")": 2, "خطأ حرج - النظام يحتاج نواة الذكاء الاصطناعي\")": 4, "خطأ حرج في النظام": 1, "خطأ في نقل {item.name}: {e}\")": 8, "خطأ في نقل {filename}: {e}\")": 2, "خطأ في قراءة التقرير: {e}\")": 2, "خطأ في اختبار Docker: {e}\")": 6, "خطأ في اختبار Docker Compose: {e}\")": 4, "خطأ في اختبار الاتصال: {e}\")": 4, "خطأ في اختبار {service_name}: {e}\")": 4, "خطأ في قياس استخدام القرص: {e}\")": 4, "خطأ - {e}\")": 2, "خطأ في قياس مساحة القرص: {e}\")": 2, "خطأ في النظام: {str(e)}\")": 2, "خطأ.": 1, "خطأ في تعريف المسار في Docker**:": 1, "خطأ في ملف `Dockerfile` أو `docker-compose.yml`**:": 1, "failed.": 6, "failed = len([r for r in self.test_results if r[\"s...": 2, "failed\": failed,": 2, "failed}\")": 2, "failed_authentication_attempts\": True,": 2, "failed_attempts\": 3": 2, "Failed requests percentage\"": 2, "failed_tests = 0": 4, "failed_tests += 1": 16, "failed_tests\": failed_tests,": 4, "failed_tests} اختبار\")": 2, "failed_tests', 0)}\")": 2, "failed: No such file or directory": 1, "failed to solve: failed to compute cache key: fail...": 1, "failed to solve: process \"/bin/sh -c pip install -...": 1, "فشل الاتصال بخادم MySQL\")": 2, "فشل في إنشاء مجلد التقارير: {e}\")": 1, "فشل\"": 1, "فشل الاتصال بقاعدة البيانات')": 1, "فشل\\\"}')": 1, "فشل تام": 1, "فشل: {failed}\")": 2, "فشل تحميل النماذج": 2, "فشل العملية إذا فشل حفظ قاعدة البيانات": 1, "فشل: {failed_tests} اختبار\")": 2}}, "conversation_flow": {"total_exchanges": 159, "avg_section_length": 6279, "longest_section": 138230, "shortest_section": 0}, "topics": {"tech_keywords": {"anubis": 4166, "file": 3473, "docker": 2111, "config": 1638, "test": 1454, "isolation": 1330, "database": 981, "python": 915, "agent": 730, "mysql": 398}, "files_mentioned": {"README.md": 236, "anubis_database_checker.py": 129, "main.py": 99, "anubis_comprehensive_system_tester.py": 90, "gemini_assisted_fixes.py": 76, "archive_and_backups_inspection_report.py": 75, "database_config.json": 72, "anubis_simple_system_tester.py": 71, "configs_database_inspection_report.py": 65, "gemini_fixes_completed.py": 52}}}, "conversation_data": {"commands_sample": ["python anubis_database_checker.py", "python anubis_database_checker.py", "gemini --prompt \"أحتاج مساعدتك في فحص مجلد anubis_main_system في مشروع نظام أنوبيس للذكاء الاصطناعي. هذا المجلد يحتوي على النواة الأساسية للنظام. أريد فحص شامل لجميع الملفات والمجلدات بداخله وتحديد: 1) حالة كل مكون 2) المشاكل المحتملة 3) التحسينات المطلوبة 4) التوصيات للخطوات القادمة. ابدأ بفحص الهيكل العام أولاً.\"", "python anubis_main_system_inspection_report.py", "python archive_and_backups_inspection_report.py", "gemini --prompt \"أحتاج مساعدتك في فحص مجلدين مهمين في مشروع نظام أنوبيس: 1) مجلد configs - يحتوي على ملفات التكوين 2) مجلد database - يحتوي على قاعدة البيانات والأدوات المرتبطة بها. أريد خطة شاملة للفحص تشمل: أنواع الفحوصات المطلوبة، النقاط المهمة للتركيز عليها، والمشاكل المحتملة. ابدأ بتحديد أولويات الفحص.\"", "python configs_database_inspection_report.py", "python gemini_assisted_fixes.py", "str_replace", "str_replace"], "errors_sample": ["Error:", "Error: cannot unpack non-iterable bool object", "Error:", "Error:", "Error:", "Error: name 'sqlite3' is n", "Error: name 'sqlite3' is not defined. Did you forg", "Error:", "Error:", "Error:"], "timestamps": ["7/19/2025, 4:03:31 PM", "7/19/2025, 4:04:00 PM", "7/19/2025, 4:04:21 PM", "7/19/2025, 4:04:30 PM", "7/19/2025, 4:04:36 PM", "7/19/2025, 4:04:42 PM", "7/19/2025, 4:04:51 PM", "7/19/2025, 4:05:02 PM", "7/19/2025, 4:05:19 PM", "7/19/2025, 4:05:51 PM", "7/19/2025, 4:06:17 PM", "7/19/2025, 4:06:57 PM", "7/19/2025, 4:07:09 PM", "7/19/2025, 4:07:20 PM", "7/19/2025, 4:08:28 PM", "7/19/2025, 4:11:41 PM", "7/19/2025, 4:11:59 PM", "7/19/2025, 4:12:06 PM", "7/19/2025, 4:12:38 PM", "7/19/2025, 4:14:39 PM", "7/19/2025, 4:15:19 PM", "7/19/2025, 4:15:42 PM", "7/19/2025, 4:16:53 PM", "7/19/2025, 4:17:19 PM", "7/19/2025, 4:19:32 PM", "7/19/2025, 4:20:06 PM", "7/19/2025, 4:21:41 PM", "7/19/2025, 4:22:02 PM", "7/19/2025, 4:24:29 PM", "7/19/2025, 4:24:57 PM", "7/19/2025, 4:25:30 PM", "7/19/2025, 4:26:23 PM", "7/19/2025, 4:28:10 PM", "7/19/2025, 4:28:25 PM", "7/19/2025, 4:32:54 PM", "7/19/2025, 4:35:05 PM", "7/19/2025, 4:35:26 PM", "7/19/2025, 4:35:45 PM", "7/19/2025, 4:36:03 PM", "7/19/2025, 4:36:23 PM", "7/19/2025, 4:36:36 PM", "7/19/2025, 4:37:08 PM", "7/19/2025, 4:37:37 PM", "7/19/2025, 4:37:56 PM", "7/19/2025, 4:38:48 PM", "7/19/2025, 4:39:04 PM", "7/19/2025, 4:41:12 PM", "7/19/2025, 4:41:38 PM", "7/19/2025, 4:41:51 PM", "7/19/2025, 4:43:26 PM", "7/19/2025, 4:43:46 PM", "7/19/2025, 4:46:34 PM", "7/19/2025, 4:49:11 PM", "7/19/2025, 4:49:28 PM", "7/19/2025, 5:04:02 PM", "7/19/2025, 5:04:15 PM", "7/19/2025, 5:05:45 PM", "7/19/2025, 5:06:01 PM", "7/19/2025, 5:09:13 PM", "7/19/2025, 5:11:54 PM", "7/19/2025, 5:12:16 PM", "7/19/2025, 5:14:31 PM", "7/19/2025, 5:14:54 PM", "7/19/2025, 5:15:06 PM", "7/19/2025, 5:17:17 PM", "7/19/2025, 5:17:30 PM", "7/19/2025, 5:17:45 PM", "7/19/2025, 5:18:04 PM", "7/19/2025, 5:18:18 PM", "7/19/2025, 5:20:15 PM", "7/19/2025, 5:20:32 PM", "7/19/2025, 5:23:36 PM", "7/19/2025, 5:27:00 PM", "7/19/2025, 5:27:19 PM", "7/19/2025, 5:28:57 PM", "7/19/2025, 5:29:24 PM", "7/19/2025, 5:29:39 PM", "7/19/2025, 5:33:05 PM", "7/19/2025, 5:33:25 PM", "7/19/2025, 5:37:00 PM", "7/19/2025, 5:37:26 PM", "7/19/2025, 5:37:39 PM", "7/19/2025, 5:41:07 PM", "7/19/2025, 5:41:27 PM", "7/19/2025, 5:46:59 PM", "7/19/2025, 5:47:23 PM", "7/19/2025, 5:47:34 PM", "7/19/2025, 5:49:20 PM", "7/19/2025, 5:49:37 PM", "7/19/2025, 5:52:31 PM", "7/19/2025, 5:52:48 PM", "7/19/2025, 5:53:37 PM", "7/19/2025, 5:55:51 PM", "7/19/2025, 5:56:12 PM", "7/19/2025, 5:56:34 PM", "7/19/2025, 6:00:10 PM", "7/19/2025, 6:03:31 PM", "7/19/2025, 6:04:13 PM", "7/20/2025, 6:01:36 AM", "7/20/2025, 6:01:54 AM", "7/20/2025, 6:02:06 AM", "7/20/2025, 6:02:14 AM", "7/20/2025, 6:02:29 AM", "7/20/2025, 6:02:41 AM", "7/20/2025, 6:04:51 AM", "7/20/2025, 6:05:31 AM", "7/20/2025, 6:13:02 AM", "7/20/2025, 6:15:03 AM", "7/20/2025, 6:15:22 AM", "7/20/2025, 6:15:58 AM", "7/20/2025, 6:18:27 AM", "7/20/2025, 6:18:47 AM", "7/20/2025, 6:19:00 AM", "7/20/2025, 6:19:11 AM", "7/20/2025, 6:19:22 AM", "7/20/2025, 6:32:45 AM", "7/20/2025, 6:33:03 AM", "7/20/2025, 6:33:10 AM", "7/20/2025, 6:33:51 AM", "7/20/2025, 6:34:08 AM", "7/20/2025, 6:35:01 AM", "7/20/2025, 6:35:14 AM", "7/20/2025, 6:35:29 AM", "7/20/2025, 6:36:32 AM", "7/20/2025, 6:36:45 AM", "7/20/2025, 6:37:17 AM", "7/20/2025, 6:38:35 AM", "7/20/2025, 6:41:03 AM", "7/20/2025, 6:41:22 AM", "7/20/2025, 6:41:32 AM", "7/20/2025, 6:42:34 AM", "7/20/2025, 6:43:13 AM", "7/20/2025, 6:43:28 AM", "7/20/2025, 6:43:41 AM", "7/20/2025, 6:43:51 AM", "7/20/2025, 6:44:02 AM", "7/20/2025, 6:44:11 AM", "7/20/2025, 6:44:34 AM", "7/20/2025, 6:44:51 AM", "7/20/2025, 6:45:05 AM", "7/20/2025, 6:45:21 AM", "7/20/2025, 6:45:38 AM", "7/20/2025, 6:45:48 AM", "7/20/2025, 6:46:04 AM", "7/20/2025, 6:46:45 AM", "7/20/2025, 6:47:05 AM", "7/20/2025, 7:04:14 AM", "7/20/2025, 7:04:24 AM", "7/20/2025, 7:05:05 AM", "7/20/2025, 7:05:21 AM", "7/20/2025, 7:05:35 AM", "7/20/2025, 7:06:14 AM", "7/20/2025, 7:07:45 AM", "7/20/2025, 7:08:22 AM", "7/20/2025, 7:09:22 AM", "7/20/2025, 7:09:35 AM", "7/20/2025, 7:11:42 AM", "7/20/2025, 7:12:00 AM", "7/20/2025, 7:12:35 AM", "7/20/2025, 7:12:42 AM", "7/20/2025, 7:12:56 AM"]}, "metadata": {"analyzed_file": "cline_task_jul-20-2025_7-13-06-am.md", "analysis_timestamp": "20250720_072206", "analyzer_version": "1.0.0"}}