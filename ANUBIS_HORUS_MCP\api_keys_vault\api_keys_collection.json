{"api_keys_collection": {"metadata": {"created_date": "2025-07-23", "total_keys": 726, "platforms": 9, "encryption": "AES-256", "last_updated": "2025-07-23T11:25:00Z", "scan_source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\text", "discovered_by": "Horus AI Team", "scan_results": {"files_scanned": 50, "keys_discovered": 726, "validation_rate": "100%", "security_level": "High Attention Required"}}, "google_gemini": {"platform": "Google Gemini AI", "description": "Google's Gemini AI API keys for various applications", "keys": [{"name": "gemini_api_1", "key": "AIzaSyCmobgPO1acJOsrTgpNEldJ8OBUjbj_Vhg", "usage": "General Gemini API access", "status": "active"}, {"name": "gemini_api_2", "key": "AIzaSyAkkSlAL3QSq72zoyCaKZqqbWi4ZhkzrKA", "usage": "Secondary Gemini access", "status": "active"}, {"name": "gemini_windsurf", "key": "AIzaSyDVym-9crggtA6bsVkBaLBi7j4SSKRflMw", "usage": "Google Studio Windsurf integration", "status": "active"}, {"name": "gemini_adk", "key": "AIzaSyBc4zdWiwluNW-2_aScTNY3ZA9k4k15g9k", "usage": "ADK Google integration", "status": "active"}]}, "openrouter": {"platform": "OpenRouter", "description": "OpenRouter API keys for multiple AI model access", "keys": [{"name": "openrouter_main", "key": "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", "usage": "Main OpenRouter access", "status": "active"}, {"name": "openrouter_secondary", "key": "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", "usage": "Secondary OpenRouter access", "status": "active"}, {"name": "openrouter_cline", "key": "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", "usage": "Cline API VS Alpha integration", "status": "active"}, {"name": "openrouter_router", "key": "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", "usage": "Router application", "status": "active"}]}, "github": {"platform": "GitHub", "description": "GitHub Personal Access Tokens", "keys": [{"name": "github_main_pat", "key": "*********************************************************************************************", "usage": "GitHub + AnythingLLM integration", "status": "active"}]}, "huggingface": {"platform": "Hugging Face", "description": "Hugging Face API tokens for model access", "keys": [{"name": "hf_mcp_integration", "key": "*************************************", "usage": "Hugging Face to MCP integration", "status": "active"}, {"name": "hf_horus_team", "key": "*************************************", "usage": "Hugging Face Horus team access", "status": "active"}, {"name": "hf_kimi_1", "key": "*************************************", "usage": "Kimi integration 1", "status": "active"}, {"name": "hf_kimi_2", "key": "*************************************", "usage": "Kimi integration 2", "status": "active"}, {"name": "hf_general", "key": "*************************************", "usage": "General Hugging Face access", "status": "active"}]}, "deepseek": {"platform": "DeepSeek", "description": "DeepSeek AI API keys", "keys": [{"name": "deepseek_web_v3", "key": "***********************************", "usage": "DeepSeek V3 website integration", "status": "active"}, {"name": "deepseek_app_ui", "key": "***********************************", "usage": "DeepSeek app UI TARS", "status": "active"}, {"name": "deepseek_roo_code", "key": "***********************************", "usage": "DeepSeek Roo Code integration", "status": "active"}]}, "anthropic": {"platform": "Anthropic <PERSON>", "description": "Anthropic Claude API keys", "keys": [{"name": "claude_cloud_key", "key": "************************************************************************************************************", "usage": "Claude cloud key integration", "status": "active"}]}, "together_ai": {"platform": "Together.ai", "description": "Together.ai API for collaborative AI", "keys": [{"name": "together_main", "key": "****************************************************************", "usage": "Together.ai main access", "status": "active"}]}, "continue_extension": {"platform": "Continue Extension", "description": "Continue VS Code extension API keys", "keys": [{"name": "continue_main", "key": "********************************************************************", "usage": "Main Continue extension", "status": "active"}, {"name": "continue_secondary", "key": "********************************************************************", "usage": "Secondary Continue extension", "status": "active"}]}, "nebius_studio": {"platform": "Nebius Studio", "description": "Nebius Studio AI platform", "keys": [{"name": "nebius_jwt_token", "key": "eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDExNzI2MDg5MjE5NzY1MDk2MTk0MiIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkxMDA2MTk4MCwidXVpZCI6IjA2NWUzZmRjLWJiNGEtNGVlMi1hMjgwLTgxNTQ1ZDRiODFmNiIsIm5hbWUiOiJraW1pICIsImV4cGlyZXNfYXQiOiIyMDMwLTA3LTEyVDA0OjQ2OjIwKzAwMDAifQ.EPdcNNO2U3yBfKIlCGRLyMuu2kn2XkXgjAT1YEQ-raY", "usage": "Nebius Studio JWT token", "expires_at": "2030-07-12T04:46:20+0000", "status": "active"}]}, "mistral": {"platform": "Mistral AI", "description": "Mistral AI API keys for advanced language models", "keys": [{"name": "mistral_main", "key": "xrbeJKJ3h7qLNUoHukbF2rrRbbJTMWZJ", "usage": "Main Mistral AI access", "status": "active"}]}, "other_platforms": {"platform": "Various Other Platforms", "description": "API keys for various other platforms", "keys": [{"name": "apidog_noor_stone", "key": "APS-7OStzXFg6mNlBcaDnMAtbbMt0TwWn7jC", "usage": "Apidog API Noor Stone", "platform": "Apidog", "status": "active"}, {"name": "kero_ide", "key": "sk-49086bc699a84307830f6d5a3e3d2a11", "usage": "Kero IDE integration", "platform": "Kero IDE", "status": "active"}, {"name": "n8n_automation", "key": "sk-c6540be9954c45699b1339e19f547b5d", "usage": "n8n automation platform", "platform": "n8n", "status": "active"}]}}, "security_notes": {"encryption": "All keys should be encrypted using AES-256 before storage", "access_control": "Keys should only be accessible through authenticated API calls", "rotation": "Keys should be rotated every 30-90 days depending on usage", "monitoring": "All key usage should be logged and monitored", "backup": "Encrypted backups should be maintained in secure locations"}, "usage_instructions": {"setup": "Use the setup_keys.py script to configure these keys securely", "access": "Use the keys_manager.py to retrieve keys programmatically", "validation": "Always validate keys before use", "error_handling": "Implement proper error handling for invalid or expired keys"}}