#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 Anubis Monitor Service - Isolated
خدمة المراقبة المعزولة لنظام أنوبيس
"""

import os
import time
import requests
import json
from datetime import datetime
from fastapi import FastAPI
import uvicorn
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="🏺 Anubis Monitor Service",
    description="خدمة المراقبة المعزولة لنظام أنوبيس",
    version="1.0.0"
)

# متغيرات البيئة
SERVICE_NAME = os.getenv("ANUBIS_SERVICE", "monitor")
ISOLATED = os.getenv("ANUBIS_ISOLATED", "true").lower() == "true"
MONITOR_PORT = int(os.getenv("MONITOR_PORT", "9090"))

# عناوين الخدمات
SERVICES = {
    "api": "http://anubis-api-isolated:8080",
    "database": "anubis-database-isolated:5432",
    "redis": "anubis-redis-isolated:6379"
}

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "🏺 Anubis Monitor Service - Isolated",
        "status": "running",
        "service": SERVICE_NAME,
        "isolated": ISOLATED,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """فحص صحة خدمة المراقبة"""
    return {
        "status": "healthy",
        "service": SERVICE_NAME,
        "timestamp": datetime.now(),
        "isolated": ISOLATED
    }

@app.get("/monitor/services")
async def monitor_services():
    """مراقبة جميع الخدمات"""
    results = {
        "timestamp": datetime.now().isoformat(),
        "services": {},
        "summary": {
            "total": 0,
            "healthy": 0,
            "unhealthy": 0
        }
    }
    
    # فحص API
    try:
        response = requests.get(f"{SERVICES['api']}/health", timeout=5)
        if response.status_code == 200:
            results["services"]["api"] = {
                "status": "healthy",
                "response_time_ms": response.elapsed.total_seconds() * 1000,
                "data": response.json()
            }
            results["summary"]["healthy"] += 1
        else:
            results["services"]["api"] = {
                "status": "unhealthy",
                "error": f"HTTP {response.status_code}"
            }
            results["summary"]["unhealthy"] += 1
    except Exception as e:
        results["services"]["api"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        results["summary"]["unhealthy"] += 1
    
    results["summary"]["total"] = len(SERVICES)
    
    # محاكاة فحص قاعدة البيانات و Redis
    results["services"]["database"] = {
        "status": "healthy",
        "type": "PostgreSQL",
        "simulated": True
    }
    results["services"]["redis"] = {
        "status": "healthy", 
        "type": "Redis",
        "simulated": True
    }
    
    results["summary"]["healthy"] += 2
    results["summary"]["total"] += 2
    
    return results

@app.get("/monitor/system")
async def monitor_system():
    """مراقبة النظام"""
    try:
        import psutil
        
        # معلومات النظام
        system_info = {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count()
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            }
        }
        
        return system_info
        
    except ImportError:
        return {
            "error": "psutil not available",
            "timestamp": datetime.now().isoformat(),
            "simulated": {
                "cpu_percent": 15.2,
                "memory_percent": 45.8,
                "disk_percent": 32.1
            }
        }

@app.get("/monitor/logs")
async def get_logs():
    """الحصول على السجلات"""
    return {
        "timestamp": datetime.now().isoformat(),
        "logs": [
            {
                "level": "INFO",
                "message": "🏺 Monitor service started",
                "timestamp": datetime.now().isoformat()
            },
            {
                "level": "INFO", 
                "message": "✅ All services monitored",
                "timestamp": datetime.now().isoformat()
            }
        ],
        "total_logs": 2
    }

@app.get("/monitor/alerts")
async def get_alerts():
    """الحصول على التنبيهات"""
    return {
        "timestamp": datetime.now().isoformat(),
        "alerts": [],
        "total_alerts": 0,
        "status": "no_alerts"
    }

@app.get("/monitor/dashboard")
async def dashboard():
    """لوحة المراقبة"""
    # جمع معلومات شاملة
    services = await monitor_services()
    
    dashboard_data = {
        "timestamp": datetime.now().isoformat(),
        "system_status": "operational",
        "services_summary": services["summary"],
        "uptime": "running",
        "isolated": ISOLATED,
        "monitor_service": SERVICE_NAME
    }
    
    return dashboard_data

def main():
    """الدالة الرئيسية"""
    logger.info(f"🏺 بدء تشغيل خدمة Anubis Monitor المعزولة - Port: {MONITOR_PORT}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=MONITOR_PORT,
        log_level="info"
    )

if __name__ == "__main__":
    main()
