{"database": {"type": "mysql", "description": "إعدادات قاعدة بيانات نظام أنوبيس", "sqlite": {"db_path": "database/anubis.db", "description": "قاعدة بيانات SQLite محلية", "auto_backup": true, "backup_interval": "daily"}, "mysql": {"host": "localhost", "port": 3306, "user": "root", "password": "2452329511", "database": "anubis_system", "description": "قاعدة بيانات MySQL للإنتاج", "charset": "utf8mb4", "autocommit": true, "pool_size": 10}}, "system": {"name": "Anubis AI Assistants System", "version": "1.0.0", "description": "نظام أنوبيس للمساعدين الذكيين", "database_features": {"project_tracking": true, "analysis_history": true, "error_logging": true, "performance_metrics": true, "user_activities": true, "automated_backups": true}}, "performance": {"connection_timeout": 30, "query_timeout": 60, "max_retries": 3, "batch_size": 100, "cache_enabled": true, "cache_ttl": 3600}, "security": {"encrypt_sensitive_data": true, "log_queries": false, "sanitize_inputs": true, "max_connections": 50}, "maintenance": {"auto_vacuum": true, "optimize_tables": true, "cleanup_old_data": true, "retention_days": 365, "backup_retention_days": 30}}