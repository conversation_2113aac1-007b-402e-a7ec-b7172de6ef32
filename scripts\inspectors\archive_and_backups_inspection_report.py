#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ فاحص مجلد archive_and_backups الشامل
Archive and Backups Comprehensive Inspector
"""

import os
import json
from pathlib import Path
from datetime import datetime

class ArchiveBackupsInspector:
    def __init__(self):
        self.base_path = Path("archive_and_backups")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "system_name": "Archive and Backups System",
            "inspection_type": "comprehensive_analysis",
            "overall_health": "unknown",
            "components": {},
            "statistics": {},
            "issues": [],
            "recommendations": [],
            "cleanup_suggestions": []
        }
    
    def analyze_archive_structure(self):
        """تحليل هيكل مجلد الأرشيف"""
        print("🔍 تحليل هيكل مجلد الأرشيف...")
        
        archive_path = self.base_path / "archive"
        component_health = {
            "status": "active",
            "total_size": 0,
            "subdirectories": {},
            "file_types": {},
            "issues": [],
            "strengths": []
        }
        
        if not archive_path.exists():
            component_health["issues"].append("❌ مجلد archive/ مفقود")
            self.report["components"]["archive"] = component_health
            return component_health
        
        # فحص المجلدات الفرعية
        subdirs = [
            "duplicate_reports", "old_databases", "old_files", 
            "old_versions", "temp_files", "cache_files", 
            "deprecated", "unused_files"
        ]
        
        for subdir in subdirs:
            subdir_path = archive_path / subdir
            if subdir_path.exists():
                files = list(subdir_path.rglob("*"))
                files_count = len([f for f in files if f.is_file()])
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                
                component_health["subdirectories"][subdir] = {
                    "exists": True,
                    "files_count": files_count,
                    "size_bytes": total_size,
                    "size_mb": round(total_size / (1024*1024), 2),
                    "status": "✅ نشط" if files_count > 0 else "⚠️ فارغ"
                }
                
                component_health["total_size"] += total_size
                
                if files_count > 0:
                    component_health["strengths"].append(f"✅ {subdir}/ ({files_count} ملف، {round(total_size/(1024*1024), 2)} MB)")
                else:
                    component_health["issues"].append(f"⚠️ {subdir}/ فارغ")
            else:
                component_health["subdirectories"][subdir] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ {subdir}/ مفقود")
        
        # تحليل أنواع الملفات
        all_files = list(archive_path.rglob("*"))
        for file in all_files:
            if file.is_file():
                ext = file.suffix.lower()
                if ext not in component_health["file_types"]:
                    component_health["file_types"][ext] = 0
                component_health["file_types"][ext] += 1
        
        self.report["components"]["archive"] = component_health
        return component_health
    
    def analyze_backup_system(self):
        """تحليل نظام النسخ الاحتياطية"""
        print("🔍 تحليل نظام النسخ الاحتياطية...")
        
        backup_path = self.base_path / "backup"
        component_health = {
            "status": "active",
            "backup_categories": {},
            "total_backups": 0,
            "issues": [],
            "strengths": []
        }
        
        if not backup_path.exists():
            component_health["issues"].append("❌ مجلد backup/ مفقود")
            self.report["components"]["backup"] = component_health
            return component_health
        
        # فحص فئات النسخ الاحتياطية
        categories = ["old_agents"]
        
        for category in categories:
            category_path = backup_path / category
            if category_path.exists():
                files = list(category_path.glob("*"))
                files_count = len(files)
                
                component_health["backup_categories"][category] = {
                    "exists": True,
                    "files_count": files_count,
                    "files": [f.name for f in files],
                    "status": "✅ متوفر"
                }
                
                component_health["total_backups"] += files_count
                component_health["strengths"].append(f"✅ {category} ({files_count} نسخة احتياطية)")
            else:
                component_health["backup_categories"][category] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ فئة {category} مفقودة")
        
        # فحص الملفات الأساسية في backup
        core_files = backup_path.glob("*.py")
        core_count = len(list(core_files))
        if core_count > 0:
            component_health["strengths"].append(f"✅ {core_count} ملف Python احتياطي")
            component_health["total_backups"] += core_count
        
        self.report["components"]["backup"] = component_health
        return component_health
    
    def analyze_duplicate_reports(self):
        """تحليل التقارير المكررة"""
        print("🔍 تحليل التقارير المكررة...")
        
        duplicates_path = self.base_path / "archive" / "duplicate_reports"
        component_health = {
            "status": "cleanup_needed",
            "total_duplicates": 0,
            "report_types": {},
            "size_analysis": {},
            "issues": [],
            "cleanup_candidates": []
        }
        
        if not duplicates_path.exists():
            component_health["issues"].append("❌ مجلد duplicate_reports/ مفقود")
            self.report["components"]["duplicate_reports"] = component_health
            return component_health
        
        # تحليل التقارير المكررة
        files = list(duplicates_path.glob("*"))
        component_health["total_duplicates"] = len(files)
        
        # تصنيف حسب نوع التقرير
        report_patterns = {
            "final_validation": [],
            "simple_validation": [],
            "stress_test": [],
            "test_report": [],
            "all_tests": []
        }
        
        total_size = 0
        for file in files:
            if file.is_file():
                size = file.stat().st_size
                total_size += size
                
                # تصنيف التقارير
                for pattern, file_list in report_patterns.items():
                    if pattern in file.name:
                        file_list.append({
                            "name": file.name,
                            "size": size,
                            "modified": datetime.fromtimestamp(file.stat().st_mtime).isoformat()
                        })
        
        # إحصائيات التقارير
        for report_type, files_list in report_patterns.items():
            if files_list:
                component_health["report_types"][report_type] = {
                    "count": len(files_list),
                    "files": files_list
                }
                
                if len(files_list) > 3:  # أكثر من 3 نسخ
                    component_health["cleanup_candidates"].append(f"⚠️ {report_type}: {len(files_list)} نسخ")
        
        component_health["size_analysis"] = {
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024*1024), 2),
            "average_file_size": round(total_size / len(files), 2) if files else 0
        }
        
        if total_size > 10 * 1024 * 1024:  # أكثر من 10 MB
            component_health["issues"].append(f"⚠️ حجم التقارير المكررة كبير: {round(total_size/(1024*1024), 2)} MB")
        
        self.report["components"]["duplicate_reports"] = component_health
        return component_health
    
    def analyze_old_databases(self):
        """تحليل قواعد البيانات القديمة"""
        print("🔍 تحليل قواعد البيانات القديمة...")
        
        old_db_path = self.base_path / "archive" / "old_databases"
        component_health = {
            "status": "archived",
            "databases": {},
            "total_size": 0,
            "issues": [],
            "strengths": []
        }
        
        if not old_db_path.exists():
            component_health["issues"].append("❌ مجلد old_databases/ مفقود")
            self.report["components"]["old_databases"] = component_health
            return component_health
        
        # فحص ملفات قواعد البيانات
        db_files = list(old_db_path.glob("*.db"))
        
        for db_file in db_files:
            size = db_file.stat().st_size
            component_health["databases"][db_file.name] = {
                "size_bytes": size,
                "size_kb": round(size / 1024, 2),
                "modified": datetime.fromtimestamp(db_file.stat().st_mtime).isoformat(),
                "status": "✅ محفوظة"
            }
            
            component_health["total_size"] += size
            component_health["strengths"].append(f"✅ {db_file.name} ({round(size/1024, 2)} KB)")
        
        if not db_files:
            component_health["issues"].append("⚠️ لا توجد قواعد بيانات مؤرشفة")
        
        self.report["components"]["old_databases"] = component_health
        return component_health
    
    def generate_statistics(self):
        """إنشاء إحصائيات شاملة"""
        print("📊 إنشاء إحصائيات شاملة...")
        
        stats = {
            "total_files": 0,
            "total_size_mb": 0,
            "categories_summary": {},
            "space_usage": {},
            "file_distribution": {}
        }
        
        # حساب الإحصائيات من المكونات
        for component_name, component in self.report["components"].items():
            if "total_size" in component:
                stats["total_size_mb"] += component["total_size"] / (1024*1024)
            
            if "subdirectories" in component:
                for subdir, info in component["subdirectories"].items():
                    if "files_count" in info:
                        stats["total_files"] += info["files_count"]
            
            if "total_duplicates" in component:
                stats["total_files"] += component["total_duplicates"]
            
            if "total_backups" in component:
                stats["total_files"] += component["total_backups"]
        
        # تصنيف استخدام المساحة
        archive_size = self.report["components"].get("archive", {}).get("total_size", 0) / (1024*1024)
        stats["space_usage"] = {
            "archive_mb": round(archive_size, 2),
            "backups_mb": 0,  # سيتم حسابه لاحقاً
            "largest_category": "archive" if archive_size > 0 else "unknown"
        }
        
        self.report["statistics"] = stats
        return stats
    
    def generate_cleanup_recommendations(self):
        """إنشاء توصيات التنظيف"""
        print("🧹 إنشاء توصيات التنظيف...")
        
        cleanup_suggestions = []
        
        # تحليل التقارير المكررة
        duplicate_reports = self.report["components"].get("duplicate_reports", {})
        if duplicate_reports.get("total_duplicates", 0) > 10:
            cleanup_suggestions.append("🗑️ حذف التقارير المكررة القديمة (الاحتفاظ بالأحدث فقط)")
        
        # تحليل الملفات المؤقتة
        archive_component = self.report["components"].get("archive", {})
        subdirs = archive_component.get("subdirectories", {})
        
        if subdirs.get("temp_files", {}).get("files_count", 0) > 0:
            cleanup_suggestions.append("🧹 تنظيف الملفات المؤقتة في temp_files/")
        
        if subdirs.get("cache_files", {}).get("files_count", 0) > 0:
            cleanup_suggestions.append("💾 مراجعة وحذف ملفات الكاش القديمة")
        
        # تحليل الحجم الإجمالي
        total_size = self.report["statistics"].get("total_size_mb", 0)
        if total_size > 100:  # أكثر من 100 MB
            cleanup_suggestions.append(f"📦 مراجعة الحجم الإجمالي: {round(total_size, 2)} MB")
        
        # توصيات عامة
        cleanup_suggestions.extend([
            "📋 إنشاء سياسة احتفاظ للملفات القديمة",
            "🔄 أتمتة عملية التنظيف الدورية",
            "📊 مراقبة نمو حجم الأرشيف"
        ])
        
        self.report["cleanup_suggestions"] = cleanup_suggestions
        return cleanup_suggestions
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة للأرشيف"""
        print("🏥 تقييم الحالة العامة...")
        
        total_issues = 0
        total_warnings = 0
        
        for component in self.report["components"].values():
            if "issues" in component:
                total_issues += len(component["issues"])
            if "cleanup_candidates" in component:
                total_warnings += len(component["cleanup_candidates"])
        
        # تحديد الحالة
        if total_issues == 0 and total_warnings <= 2:
            self.report["overall_health"] = "excellent"
            health_text = "ممتاز"
            emoji = "🟢"
        elif total_issues <= 2 and total_warnings <= 5:
            self.report["overall_health"] = "good"
            health_text = "جيد"
            emoji = "🟡"
        elif total_issues <= 5 or total_warnings <= 10:
            self.report["overall_health"] = "needs_attention"
            health_text = "يحتاج انتباه"
            emoji = "🟠"
        else:
            self.report["overall_health"] = "needs_cleanup"
            health_text = "يحتاج تنظيف"
            emoji = "🔴"
        
        print(f"\n{emoji} الحالة العامة: {health_text}")
        print(f"❌ المشاكل: {total_issues}")
        print(f"⚠️ التحذيرات: {total_warnings}")
        
        return self.report["overall_health"]
    
    def run_inspection(self):
        """تشغيل الفحص الشامل"""
        print("🗂️ بدء فحص مجلد archive_and_backups الشامل")
        print("=" * 60)
        
        # تشغيل جميع عمليات الفحص
        self.analyze_archive_structure()
        self.analyze_backup_system()
        self.analyze_duplicate_reports()
        self.analyze_old_databases()
        
        # التحليل والتوصيات
        self.generate_statistics()
        self.generate_cleanup_recommendations()
        self.evaluate_overall_health()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("📋 تقرير فحص archive_and_backups المفصل")
        print("="*60)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "needs_attention": "🟠",
            "needs_cleanup": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']}")
        
        # الإحصائيات
        stats = self.report.get("statistics", {})
        print(f"\n📊 الإحصائيات:")
        print(f"   📁 إجمالي الملفات: {stats.get('total_files', 0)}")
        print(f"   💾 إجمالي الحجم: {round(stats.get('total_size_mb', 0), 2)} MB")
        
        # تفاصيل المكونات
        print(f"\n🔍 تفاصيل المكونات:")
        for name, component in self.report["components"].items():
            print(f"\n🔹 {name}:")
            if "strengths" in component:
                for strength in component["strengths"]:
                    print(f"   {strength}")
            if "issues" in component:
                for issue in component["issues"]:
                    print(f"   {issue}")
            if "cleanup_candidates" in component:
                for candidate in component["cleanup_candidates"]:
                    print(f"   {candidate}")
        
        # توصيات التنظيف
        print(f"\n🧹 توصيات التنظيف:")
        for suggestion in self.report.get("cleanup_suggestions", []):
            print(f"   {suggestion}")
        
        print("\n" + "="*60)
        print("🗂️ انتهى فحص archive_and_backups")
        print("="*60)
    
    def save_report(self):
        """حفظ التقرير في ملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"archive_backups_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    inspector = ArchiveBackupsInspector()
    
    # تشغيل الفحص
    report = inspector.run_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    return report

if __name__ == "__main__":
    main()
