#!/usr/bin/env python3
"""
🏺 فاحص جودة الكود لنظام أنوبيس
Code Quality Checker for Anubis System

يقوم بفحص وإصلاح أخطاء الكود باستخدام Flake8 و Pylint
"""

import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List


class CodeQualityChecker:
    """فاحص جودة الكود الشامل"""

    def __init__(self, project_path: str = "anubis"):
        self.project_path = Path(project_path)
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "project_path": str(self.project_path),
            "checks": {},
            "fixes_applied": [],
            "summary": {},
        }

    def run_flake8(self) -> Dict[str, Any]:
        """تشغيل فحص Flake8"""
        print("🔍 تشغيل فحص Flake8...")

        try:
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "flake8",
                    str(self.project_path),
                    "--max-line-length=100",
                    "--ignore=E203,W503,E501",
                    "--exclude=__pycache__,*.backup*,*.pyc",
                ],
                capture_output=True,
                text=True,
                timeout=60,
            )

            flake8_report = {
                "tool": "flake8",
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "issues_count": len(result.stdout.splitlines()) if result.stdout else 0,
            }

            if result.returncode == 0:
                print("✅ Flake8: لا توجد مشاكل")
            else:
                print(f"⚠️ Flake8: وجد {flake8_report['issues_count']} مشكلة")

            return flake8_report

        except subprocess.TimeoutExpired:
            return {"tool": "flake8", "error": "انتهت مهلة التنفيذ"}
        except Exception as e:
            return {"tool": "flake8", "error": str(e)}

    def run_pylint(self) -> Dict[str, Any]:
        """تشغيل فحص Pylint"""
        print("🔍 تشغيل فحص Pylint...")

        try:
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "pylint",
                    str(self.project_path),
                    "--disable=C0103,C0114,C0115,C0116,R0903,R0913,W0613",
                    "--max-line-length=100",
                ],
                capture_output=True,
                text=True,
                timeout=120,
            )

            pylint_report = {
                "tool": "pylint",
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "score": self._extract_pylint_score(result.stdout),
            }

            print(f"📊 Pylint Score: {pylint_report['score']}/10")
            return pylint_report

        except subprocess.TimeoutExpired:
            return {"tool": "pylint", "error": "انتهت مهلة التنفيذ"}
        except Exception as e:
            return {"tool": "pylint", "error": str(e)}

    def _extract_pylint_score(self, output: str) -> float:
        """استخراج نقاط Pylint من المخرجات"""
        try:
            for line in output.splitlines():
                if "Your code has been rated at" in line:
                    score_part = line.split("rated at ")[1].split("/")[0]
                    return float(score_part)
        except:
            pass
        return 0.0

    def run_mypy(self) -> Dict[str, Any]:
        """تشغيل فحص MyPy للأنواع"""
        print("🔍 تشغيل فحص MyPy...")

        try:
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "mypy",
                    str(self.project_path),
                    "--ignore-missing-imports",
                    "--no-strict-optional",
                ],
                capture_output=True,
                text=True,
                timeout=60,
            )

            mypy_report = {
                "tool": "mypy",
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "issues_count": len(result.stdout.splitlines()) if result.stdout else 0,
            }

            if result.returncode == 0:
                print("✅ MyPy: لا توجد مشاكل في الأنواع")
            else:
                print(f"⚠️ MyPy: وجد {mypy_report['issues_count']} مشكلة في الأنواع")

            return mypy_report

        except subprocess.TimeoutExpired:
            return {"tool": "mypy", "error": "انتهت مهلة التنفيذ"}
        except Exception as e:
            return {"tool": "mypy", "error": str(e)}

    def auto_fix_common_issues(self):
        """إصلاح المشاكل الشائعة تلقائياً"""
        print("🔧 إصلاح المشاكل الشائعة...")

        fixes_applied = []

        # البحث عن جميع ملفات Python
        for py_file in self.project_path.rglob("*.py"):
            if "backup" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                content = py_file.read_text(encoding="utf-8")
                original_content = content

                # إصلاح المسافات الفارغة في نهاية الأسطر
                content = "\n".join(line.rstrip() for line in content.splitlines())

                # إضافة سطر فارغ في نهاية الملف
                if content and not content.endswith("\n"):
                    content += "\n"

                # إصلاح الاستيرادات غير المستخدمة الشائعة
                unused_imports = [
                    "import datetime\n",
                    "from datetime import datetime\n",
                    "import os\n",
                    "import sys\n",
                ]

                for unused in unused_imports:
                    if unused in content and not self._is_import_used(content, unused):
                        content = content.replace(unused, "")
                        fixes_applied.append(f"حذف استيراد غير مستخدم في {py_file}")

                # إصلاح الأسطر الفارغة المتعددة
                while "\n\n\n" in content:
                    content = content.replace("\n\n\n", "\n\n")

                # حفظ الملف إذا تم تغييره
                if content != original_content:
                    py_file.write_text(content, encoding="utf-8")
                    fixes_applied.append(f"إصلاح تنسيق {py_file}")

            except Exception as e:
                print(f"⚠️ خطأ في إصلاح {py_file}: {e}")

        self.report["fixes_applied"] = fixes_applied
        print(f"✅ تم تطبيق {len(fixes_applied)} إصلاح")

    def _is_import_used(self, content: str, import_line: str) -> bool:
        """فحص ما إذا كان الاستيراد مستخدم في الكود"""
        # استخراج اسم الوحدة من سطر الاستيراد
        if "import " in import_line:
            module_name = import_line.split("import ")[-1].strip()
            if "from " in import_line:
                module_name = import_line.split("from ")[-1].split(" import")[0].strip()

            # البحث عن استخدام الوحدة في الكود
            lines = content.splitlines()
            for line in lines:
                if line.strip() == import_line.strip():
                    continue
                if module_name in line:
                    return True

        return False

    def run_black_formatter(self) -> Dict[str, Any]:
        """تشغيل Black لتنسيق الكود"""
        print("🎨 تشغيل Black لتنسيق الكود...")

        try:
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "black",
                    str(self.project_path),
                    "--line-length=100",
                    "--exclude=backup",
                ],
                capture_output=True,
                text=True,
                timeout=60,
            )

            black_report = {
                "tool": "black",
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
            }

            if result.returncode == 0:
                print("✅ Black: تم تنسيق الكود بنجاح")
            else:
                print("⚠️ Black: مشاكل في التنسيق")

            return black_report

        except subprocess.TimeoutExpired:
            return {"tool": "black", "error": "انتهت مهلة التنفيذ"}
        except Exception as e:
            return {"tool": "black", "error": str(e)}

    def generate_report(self):
        """إنشاء تقرير شامل لجودة الكود"""
        # حساب الملخص
        total_issues = 0
        tools_run = 0

        for tool, result in self.report["checks"].items():
            if "error" not in result:
                tools_run += 1
                if "issues_count" in result:
                    total_issues += result["issues_count"]

        self.report["summary"] = {
            "total_issues": total_issues,
            "tools_run": tools_run,
            "fixes_applied": len(self.report["fixes_applied"]),
            "overall_status": "جيد" if total_issues < 10 else "يحتاج تحسين",
        }

        # حفظ التقرير
        report_path = self.project_path / "workspace" / "reports" / "code_quality_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)

        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)

        # إنشاء تقرير نصي
        text_report_path = report_path.with_suffix(".md")
        self._generate_text_report(text_report_path)

        print(f"📋 تم حفظ التقرير في: {report_path}")

    def _generate_text_report(self, report_path: Path):
        """إنشاء تقرير نصي"""
        content = f"""# 🏺 تقرير جودة الكود - نظام أنوبيس

**تاريخ الفحص:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## الملخص
- **إجمالي المشاكل:** {self.report['summary']['total_issues']}
- **الأدوات المستخدمة:** {self.report['summary']['tools_run']}
- **الإصلاحات المطبقة:** {self.report['summary']['fixes_applied']}
- **الحالة العامة:** {self.report['summary']['overall_status']}

## نتائج الأدوات

"""

        for tool, result in self.report["checks"].items():
            content += f"### {tool.upper()}\n"
            if "error" in result:
                content += f"❌ **خطأ:** {result['error']}\n\n"
            else:
                if "score" in result:
                    content += f"📊 **النقاط:** {result['score']}/10\n"
                if "issues_count" in result:
                    content += f"⚠️ **المشاكل:** {result['issues_count']}\n"
                content += f"✅ **حالة الخروج:** {result['exit_code']}\n\n"

        if self.report["fixes_applied"]:
            content += "## الإصلاحات المطبقة\n"
            for fix in self.report["fixes_applied"]:
                content += f"- ✅ {fix}\n"

        content += "\n---\n🏺 **تم إنشاء التقرير بواسطة نظام أنوبيس**"

        report_path.write_text(content, encoding="utf-8")

    def run_full_check(self):
        """تشغيل فحص شامل لجودة الكود"""
        print("🏺 بدء فحص جودة الكود الشامل لنظام أنوبيس...")

        # تطبيق الإصلاحات التلقائية أولاً
        self.auto_fix_common_issues()

        # تشغيل Black للتنسيق
        self.report["checks"]["black"] = self.run_black_formatter()

        # تشغيل أدوات الفحص
        self.report["checks"]["flake8"] = self.run_flake8()
        self.report["checks"]["pylint"] = self.run_pylint()
        self.report["checks"]["mypy"] = self.run_mypy()

        # إنشاء التقرير
        self.generate_report()

        print("\n🎉 تم إكمال فحص جودة الكود!")
        print(f"📊 إجمالي المشاكل: {self.report['summary']['total_issues']}")
        print(f"🔧 الإصلاحات المطبقة: {self.report['summary']['fixes_applied']}")
        print(f"📋 التقرير: anubis/workspace/reports/code_quality_report.json")


def main():
    """الدالة الرئيسية"""
    checker = CodeQualityChecker()
    checker.run_full_check()


if __name__ == "__main__":
    main()
