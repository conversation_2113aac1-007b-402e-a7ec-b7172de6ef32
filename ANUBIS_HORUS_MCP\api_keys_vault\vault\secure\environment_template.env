# 🔐 متغيرات البيئة الآمنة لمفاتيح API
# Secure Environment Variables for API Keys
# Generated: 2025-07-23T11:38:09.912730

# Google Gemini AI Keys
# Google's Gemini AI API keys for various applications
GOOGLE_GEMINI_GEMINI_API_1=YOUR_ENCRYPTED_KEY_HERE
GOOGLE_GEMINI_GEMINI_API_2=YOUR_ENCRYPTED_KEY_HERE
GOOGLE_GEMINI_GEMINI_WINDSURF=YOUR_ENCRYPTED_KEY_HERE
GOOGLE_GEMINI_GEMINI_ADK=YOUR_ENCRYPTED_KEY_HERE

# OpenRouter Keys
# OpenRouter API keys for multiple AI model access
OPENROUTER_OPENROUTER_MAIN=YOUR_ENCRYPTED_KEY_HERE
OPENROUTER_OPENROUTER_SECONDARY=YOUR_ENCRYPTED_KEY_HERE
OPENROUTER_OPENROUTER_CLINE=YOUR_ENCRYPTED_KEY_HERE
OPENROUTER_OPENROUTER_ROUTER=YOUR_ENCRYPTED_KEY_HERE

# GitHub Keys
# GitHub Personal Access Tokens
GITHUB_GITHUB_MAIN_PAT=YOUR_ENCRYPTED_KEY_HERE

# Hugging Face Keys
# Hugging Face API tokens for model access
HUGGINGFACE_HF_MCP_INTEGRATION=YOUR_ENCRYPTED_KEY_HERE
HUGGINGFACE_HF_HORUS_TEAM=YOUR_ENCRYPTED_KEY_HERE
HUGGINGFACE_HF_KIMI_1=YOUR_ENCRYPTED_KEY_HERE
HUGGINGFACE_HF_KIMI_2=YOUR_ENCRYPTED_KEY_HERE
HUGGINGFACE_HF_GENERAL=YOUR_ENCRYPTED_KEY_HERE

# DeepSeek Keys
# DeepSeek AI API keys
DEEPSEEK_DEEPSEEK_WEB_V3=YOUR_ENCRYPTED_KEY_HERE
DEEPSEEK_DEEPSEEK_APP_UI=YOUR_ENCRYPTED_KEY_HERE
DEEPSEEK_DEEPSEEK_ROO_CODE=YOUR_ENCRYPTED_KEY_HERE

# Anthropic Claude Keys
# Anthropic Claude API keys
ANTHROPIC_CLAUDE_CLOUD_KEY=YOUR_ENCRYPTED_KEY_HERE

# Together.ai Keys
# Together.ai API for collaborative AI
TOGETHER_AI_TOGETHER_MAIN=YOUR_ENCRYPTED_KEY_HERE

# Continue Extension Keys
# Continue VS Code extension API keys
CONTINUE_EXTENSION_CONTINUE_MAIN=YOUR_ENCRYPTED_KEY_HERE
CONTINUE_EXTENSION_CONTINUE_SECONDARY=YOUR_ENCRYPTED_KEY_HERE

# Nebius Studio Keys
# Nebius Studio AI platform
NEBIUS_STUDIO_NEBIUS_JWT_TOKEN=YOUR_ENCRYPTED_KEY_HERE

# Mistral AI Keys
# Mistral AI API keys for advanced language models
MISTRAL_MISTRAL_MAIN=YOUR_ENCRYPTED_KEY_HERE

# Various Other Platforms Keys
# API keys for various other platforms
OTHER_PLATFORMS_APIDOG_NOOR_STONE=YOUR_ENCRYPTED_KEY_HERE
OTHER_PLATFORMS_KERO_IDE=YOUR_ENCRYPTED_KEY_HERE
OTHER_PLATFORMS_N8N_AUTOMATION=YOUR_ENCRYPTED_KEY_HERE
