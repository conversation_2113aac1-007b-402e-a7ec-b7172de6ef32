version: '3.8'

services:
  anubis-api:
    build:
      context: ./anubis
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    environment:
      - LANGCHAIN_API_KEY=***************************************************
      - LANGCHAIN_TRACING_V2=true
      - LANGCHAIN_PROJECT=anubis-ai-system
      - API_KEY=anubis-api-key-2025
    volumes:
      - ./anubis:/app/anubis
      - ./projects:/app/projects
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - anubis-network

  anubis-database:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=anubis_root_2025
      - MYSQL_DATABASE=anubis_db
      - MYSQL_USER=anubis_user
      - MYSQL_PASSWORD=anubis_pass_2025
    ports:
      - "3306:3306"
    volumes:
      - anubis_db_data:/var/lib/mysql
      - ./anubis/database/setup:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - anubis-network

  anubis-redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - anubis-network

networks:
  anubis-network:
    driver: bridge

volumes:
  anubis_db_data: