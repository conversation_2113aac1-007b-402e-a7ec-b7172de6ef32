#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 اختبار النظام الداخلي لأنوبيس
Anubis Internal System Test

اختبار شامل باستخدام الوكلاء والنماذج الداخلية
مطور بالتعاون مع Gemini CLI
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# إضافة مسار أنوبيس
sys.path.append(str(Path(__file__).parent / "anubis"))

try:
    from core.assistant_system import UniversalAssistantSystem
    from core.config_manager import ConfigManager
    from agents.enhanced_error_detector import EnhancedErrorDetectorAgent
    from agents.enhanced_file_organizer import EnhancedFileOrganizerAgent
    from agents.database_agent import DatabaseAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد مكونات أنوبيس: {e}")
    print("🔧 تأكد من وجود ملفات النواة في مجلد anubis/")
    sys.exit(1)


class AnubisInternalTester:
    """فاحص النظام الداخلي لأنوبيس"""
    
    def __init__(self):
        self.test_results = {
            "test_timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "agent_tests": {},
            "system_tests": {},
            "performance_metrics": {},
            "overall_status": "unknown"
        }
        
        self.test_project_path = Path(__file__).parent / "anubis"
        
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("🏺 بدء الاختبار الشامل لنظام أنوبيس")
        print("🤖 اختبار باستخدام الوكلاء والنماذج الداخلية")
        print("=" * 60)
        
        # اختبار الوكلاء الفردية
        self.test_individual_agents()
        
        # اختبار النظام المتكامل
        self.test_integrated_system()
        
        # اختبار الأداء
        self.test_performance()
        
        # حساب النتائج النهائية
        self.calculate_final_results()
        
        # طباعة التقرير
        self.print_comprehensive_report()
        
        return self.test_results
    
    def test_individual_agents(self):
        """اختبار الوكلاء الفردية"""
        print("\n🤖 اختبار الوكلاء الفردية...")
        
        # اختبار وكيل كشف الأخطاء
        self.test_error_detector_agent()
        
        # اختبار وكيل تنظيم الملفات
        self.test_file_organizer_agent()
        
        # اختبار وكيل قاعدة البيانات
        self.test_database_agent()
    
    def test_error_detector_agent(self):
        """اختبار وكيل كشف الأخطاء"""
        print("  🔍 اختبار وكيل كشف الأخطاء...")

        test_name = "error_detector_agent"
        self.test_results["tests_run"] += 1

        try:
            # إنشاء تكوين افتراضي
            config = {"enabled": True, "verbose": False}

            # إنشاء وكيل كشف الأخطاء
            agent = EnhancedErrorDetectorAgent(str(self.test_project_path), config)
            agent.initialize_agent()
            
            # تشغيل التحليل
            start_time = time.time()
            results = agent.run_analysis()
            end_time = time.time()
            
            # التحقق من النتائج
            assert isinstance(results, dict), "النتائج يجب أن تكون dictionary"
            assert "errors_found" in results, "النتائج يجب أن تحتوي على errors_found"
            assert "files_analyzed" in results, "النتائج يجب أن تحتوي على files_analyzed"
            
            # حفظ نتائج الاختبار
            self.test_results["agent_tests"][test_name] = {
                "status": "passed",
                "execution_time": round(end_time - start_time, 2),
                "files_analyzed": results.get("files_analyzed", 0),
                "errors_found": results.get("errors_found", 0),
                "details": results
            }
            
            self.test_results["tests_passed"] += 1
            print(f"    ✅ نجح - تم تحليل {results.get('files_analyzed', 0)} ملف")
            
        except Exception as e:
            self.test_results["agent_tests"][test_name] = {
                "status": "failed",
                "error": str(e),
                "execution_time": 0
            }
            self.test_results["tests_failed"] += 1
            print(f"    ❌ فشل: {str(e)}")
    
    def test_file_organizer_agent(self):
        """اختبار وكيل تنظيم الملفات"""
        print("  📁 اختبار وكيل تنظيم الملفات...")

        test_name = "file_organizer_agent"
        self.test_results["tests_run"] += 1

        try:
            # إنشاء تكوين افتراضي
            config = {"enabled": True, "verbose": False}

            # إنشاء وكيل تنظيم الملفات
            agent = EnhancedFileOrganizerAgent(str(self.test_project_path), config)
            agent.initialize_agent()
            
            # تشغيل التحليل
            start_time = time.time()
            results = agent.run_analysis()
            end_time = time.time()
            
            # التحقق من النتائج
            assert isinstance(results, dict), "النتائج يجب أن تكون dictionary"
            assert "organization_suggestions" in results, "النتائج يجب أن تحتوي على organization_suggestions"
            
            # حفظ نتائج الاختبار
            self.test_results["agent_tests"][test_name] = {
                "status": "passed",
                "execution_time": round(end_time - start_time, 2),
                "suggestions_count": len(results.get("organization_suggestions", [])),
                "details": results
            }
            
            self.test_results["tests_passed"] += 1
            print(f"    ✅ نجح - تم إنشاء {len(results.get('organization_suggestions', []))} اقتراح")
            
        except Exception as e:
            self.test_results["agent_tests"][test_name] = {
                "status": "failed",
                "error": str(e),
                "execution_time": 0
            }
            self.test_results["tests_failed"] += 1
            print(f"    ❌ فشل: {str(e)}")
    
    def test_database_agent(self):
        """اختبار وكيل قاعدة البيانات"""
        print("  🗄️ اختبار وكيل قاعدة البيانات...")

        test_name = "database_agent"
        self.test_results["tests_run"] += 1

        try:
            # إنشاء تكوين افتراضي
            config = {"enabled": True, "verbose": False}

            # إنشاء وكيل قاعدة البيانات
            agent = DatabaseAgent(str(self.test_project_path), config)
            agent.initialize_agent()
            
            # تشغيل التحليل
            start_time = time.time()
            results = agent.run_analysis()
            end_time = time.time()
            
            # التحقق من النتائج
            assert isinstance(results, dict), "النتائج يجب أن تكون dictionary"
            
            # حفظ نتائج الاختبار
            self.test_results["agent_tests"][test_name] = {
                "status": "passed",
                "execution_time": round(end_time - start_time, 2),
                "details": results
            }
            
            self.test_results["tests_passed"] += 1
            print(f"    ✅ نجح - تم تحليل قاعدة البيانات")
            
        except Exception as e:
            self.test_results["agent_tests"][test_name] = {
                "status": "failed",
                "error": str(e),
                "execution_time": 0
            }
            self.test_results["tests_failed"] += 1
            print(f"    ❌ فشل: {str(e)}")
    
    def test_integrated_system(self):
        """اختبار النظام المتكامل"""
        print("\n🏺 اختبار النظام المتكامل...")
        
        test_name = "integrated_system"
        self.test_results["tests_run"] += 1
        
        try:
            # تحميل التكوين
            config_manager = ConfigManager()
            config = config_manager.load_config()
            
            # إنشاء النظام الأساسي
            system = UniversalAssistantSystem(str(self.test_project_path), config)
            
            # تشغيل التحليل الشامل
            start_time = time.time()
            results = system.analyze_project()
            end_time = time.time()
            
            # التحقق من النتائج
            assert isinstance(results, dict), "النتائج يجب أن تكون dictionary"
            assert "agents_results" in results, "النتائج يجب أن تحتوي على agents_results"
            
            # حفظ نتائج الاختبار
            self.test_results["system_tests"][test_name] = {
                "status": "passed",
                "execution_time": round(end_time - start_time, 2),
                "agents_run": len(results.get("agents_results", {})),
                "details": results
            }
            
            self.test_results["tests_passed"] += 1
            print(f"    ✅ نجح - تم تشغيل {len(results.get('agents_results', {}))} وكيل")
            
        except Exception as e:
            self.test_results["system_tests"][test_name] = {
                "status": "failed",
                "error": str(e),
                "execution_time": 0
            }
            self.test_results["tests_failed"] += 1
            print(f"    ❌ فشل: {str(e)}")
    
    def test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        # جمع مقاييس الأداء من الاختبارات السابقة
        total_execution_time = 0
        agent_times = {}
        
        for test_name, test_data in self.test_results["agent_tests"].items():
            execution_time = test_data.get("execution_time", 0)
            total_execution_time += execution_time
            agent_times[test_name] = execution_time
        
        for test_name, test_data in self.test_results["system_tests"].items():
            execution_time = test_data.get("execution_time", 0)
            total_execution_time += execution_time
        
        # حساب مقاييس الأداء
        self.test_results["performance_metrics"] = {
            "total_execution_time": round(total_execution_time, 2),
            "average_agent_time": round(sum(agent_times.values()) / len(agent_times) if agent_times else 0, 2),
            "fastest_agent": min(agent_times.items(), key=lambda x: x[1]) if agent_times else None,
            "slowest_agent": max(agent_times.items(), key=lambda x: x[1]) if agent_times else None,
            "performance_rating": self.calculate_performance_rating(total_execution_time)
        }
        
        print(f"    ⏱️ إجمالي وقت التنفيذ: {total_execution_time:.2f} ثانية")
        print(f"    📊 تقييم الأداء: {self.test_results['performance_metrics']['performance_rating']}")
    
    def calculate_performance_rating(self, total_time: float) -> str:
        """حساب تقييم الأداء"""
        if total_time < 5:
            return "ممتاز"
        elif total_time < 15:
            return "جيد"
        elif total_time < 30:
            return "مقبول"
        else:
            return "بطيء"
    
    def calculate_final_results(self):
        """حساب النتائج النهائية"""
        if self.test_results["tests_run"] == 0:
            self.test_results["overall_status"] = "no_tests"
        elif self.test_results["tests_failed"] == 0:
            self.test_results["overall_status"] = "all_passed"
        elif self.test_results["tests_passed"] > self.test_results["tests_failed"]:
            self.test_results["overall_status"] = "mostly_passed"
        else:
            self.test_results["overall_status"] = "mostly_failed"
    
    def print_comprehensive_report(self):
        """طباعة التقرير الشامل"""
        print("\n" + "="*60)
        print("🏺 تقرير الاختبار الشامل لنظام أنوبيس")
        print("="*60)
        
        # النتائج العامة
        print(f"📊 إجمالي الاختبارات: {self.test_results['tests_run']}")
        print(f"✅ نجح: {self.test_results['tests_passed']}")
        print(f"❌ فشل: {self.test_results['tests_failed']}")
        print(f"📈 معدل النجاح: {(self.test_results['tests_passed'] / self.test_results['tests_run'] * 100):.1f}%" if self.test_results['tests_run'] > 0 else "0%")
        
        # نتائج الوكلاء
        print(f"\n🤖 نتائج اختبار الوكلاء:")
        for agent_name, agent_data in self.test_results["agent_tests"].items():
            status_icon = "✅" if agent_data["status"] == "passed" else "❌"
            print(f"  {status_icon} {agent_name}: {agent_data['status']} ({agent_data.get('execution_time', 0):.2f}s)")
        
        # نتائج النظام
        print(f"\n🏺 نتائج اختبار النظام:")
        for system_name, system_data in self.test_results["system_tests"].items():
            status_icon = "✅" if system_data["status"] == "passed" else "❌"
            print(f"  {status_icon} {system_name}: {system_data['status']} ({system_data.get('execution_time', 0):.2f}s)")
        
        # مقاييس الأداء
        print(f"\n⚡ مقاييس الأداء:")
        perf = self.test_results["performance_metrics"]
        print(f"  ⏱️ إجمالي وقت التنفيذ: {perf.get('total_execution_time', 0):.2f} ثانية")
        print(f"  📊 تقييم الأداء: {perf.get('performance_rating', 'غير محدد')}")
        
        if perf.get('fastest_agent'):
            print(f"  🚀 أسرع وكيل: {perf['fastest_agent'][0]} ({perf['fastest_agent'][1]:.2f}s)")
        if perf.get('slowest_agent'):
            print(f"  🐌 أبطأ وكيل: {perf['slowest_agent'][0]} ({perf['slowest_agent'][1]:.2f}s)")
        
        # الحالة النهائية
        status_messages = {
            "all_passed": "🎉 جميع الاختبارات نجحت!",
            "mostly_passed": "🟡 معظم الاختبارات نجحت",
            "mostly_failed": "🔴 معظم الاختبارات فشلت",
            "no_tests": "⚠️ لم يتم تشغيل أي اختبارات"
        }
        
        print(f"\n🎯 الحالة النهائية: {status_messages.get(self.test_results['overall_status'], 'غير محدد')}")
        print("🤖 تم تطوير هذا الاختبار بالتعاون مع Gemini CLI")
        print("="*60)
    
    def save_results(self, filename: str = None) -> str:
        """حفظ نتائج الاختبار"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_internal_test_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الاختبار في: {filename}")
        return filename


def main():
    """الدالة الرئيسية"""
    print("🏺 اختبار النظام الداخلي لأنوبيس")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("🔧 اختبار باستخدام الوكلاء والنماذج الداخلية")
    
    # إنشاء الفاحص
    tester = AnubisInternalTester()
    
    # تشغيل الاختبار الشامل
    results = tester.run_comprehensive_test()
    
    # حفظ النتائج
    report_file = tester.save_results()
    
    # تحديد كود الخروج
    if results["overall_status"] in ["all_passed", "mostly_passed"]:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
