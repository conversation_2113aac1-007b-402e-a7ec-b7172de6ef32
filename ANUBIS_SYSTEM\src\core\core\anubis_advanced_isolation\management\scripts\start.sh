#!/bin/bash
# Advanced Anubis Isolation System - Start Script
echo "🏺 بدء تشغيل نظام العزل المتقدم لأنوبيس..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت"
    exit 1
fi

# Create secrets
echo "🔐 إنشاء الأسرار..."
echo "**********" | docker secret create anubis_db_secret - 2>/dev/null || true
echo "anubis_api_key_2024" | docker secret create anubis_api_secrets - 2>/dev/null || true

# Start services
echo "🚀 بدء تشغيل الخدمات..."
docker-compose up -d

# Wait for services
echo "⏳ انتظار بدء الخدمات..."
sleep 30

# Check health
echo "🏥 فحص صحة الخدمات..."
docker-compose ps

echo "✅ تم تشغيل نظام العزل المتقدم بنجاح!"
echo "🌐 الخدمات متاحة على:"
echo "  - Grafana: http://localhost:3000 (admin/anubis2024)"
echo "  - Prometheus: http://localhost:9090"
