#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 منظف مجلد الوكلاء
Agents Folder Cleanup

تنظيف مجلد الوكلاء بناءً على تحليل Gemini CLI
حذف الوكلاء القديمين والمكررين والاحتفاظ بالمحسنين والمفيدين
"""

import json
import shutil
import sys
from datetime import datetime
from pathlib import Path


class AgentsCleanup:
    """🧹 منظف مجلد الوكلاء"""

    def __init__(self):
        self.base_path = Path(__file__).parent.parent.absolute()
        self.agents_path = self.base_path / "agents"
        self.backup_path = self.base_path / "backup" / "old_agents"

        # تصنيف الوكلاء بناءً على تحليل Gemini CLI
        self.agents_classification = {
            "enhanced_agents": [
                "enhanced_error_detector.py",
                "enhanced_file_organizer.py",
                "enhanced_memory_agent.py",
                "enhanced_project_analyzer.py",
            ],
            "useful_agents": [
                "database_agent.py",
                "smart_ai_agent.py",
                "smart_code_analyzer.py",
            ],
            "old_agents_to_remove": [
                "file_organizer_agent.py",
                "memory_agent.py",
                "project_analyzer_agent.py",
                "error_detector.py",
                "error_detector_agent.py",
            ],
            "system_files": ["__init__.py", "README.md"],
        }

        print("🧹 منظف مجلد الوكلاء - بناءً على تحليل Gemini CLI")
        print(f"📁 مجلد الوكلاء: {self.agents_path}")

    def create_backup(self):
        """إنشاء نسخة احتياطية للوكلاء القديمين"""
        print("\n📦 إنشاء نسخة احتياطية للوكلاء القديمين...")

        self.backup_path.mkdir(parents=True, exist_ok=True)
        backed_up_files = []

        for old_agent in self.agents_classification["old_agents_to_remove"]:
            source_file = self.agents_path / old_agent

            if source_file.exists():
                backup_file = (
                    self.backup_path / f"{old_agent}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )

                try:
                    shutil.copy2(source_file, backup_file)
                    backed_up_files.append(old_agent)
                    print(f"   📦 نسخة احتياطية: {old_agent}")
                except Exception as e:
                    print(f"   ❌ خطأ في النسخ الاحتياطي لـ {old_agent}: {e}")

        return backed_up_files

    def remove_old_agents(self):
        """حذف الوكلاء القديمين والمكررين"""
        print("\n🗑️ حذف الوكلاء القديمين والمكررين...")

        removed_files = []

        for old_agent in self.agents_classification["old_agents_to_remove"]:
            agent_file = self.agents_path / old_agent

            if agent_file.exists():
                try:
                    agent_file.unlink()
                    removed_files.append(old_agent)
                    print(f"   🗑️ تم حذف: {old_agent}")
                except Exception as e:
                    print(f"   ❌ خطأ في حذف {old_agent}: {e}")
            else:
                print(f"   ⏭️ غير موجود: {old_agent}")

        return removed_files

    def update_init_file(self):
        """تحديث ملف __init__.py"""
        print("\n🔧 تحديث ملف __init__.py...")

        init_file = self.agents_path / "__init__.py"

        # محتوى __init__.py الجديد
        init_content = '''"""
🤖 مجلد الوكلاء المحسنين - نظام أنوبيس
Enhanced Agents Module - Anubis System

الوكلاء المحسنين الأساسيين:
- EnhancedErrorDetectorAgent: كشف الأخطاء المتقدم
- EnhancedProjectAnalyzerAgent: تحليل المشاريع الذكي
- EnhancedFileOrganizerAgent: تنظيم الملفات المتقدم
- EnhancedMemoryAgent: إدارة الذاكرة الذكية

الوكلاء المتخصصين:
- DatabaseAgent: إدارة قواعد البيانات
- SmartAIAgent: الذكاء الاصطناعي الشامل
- SmartCodeAnalyzer: تحليل الكود الذكي

تم تنظيفه وتحديثه بالتعاون مع Gemini CLI
"""

# استيراد الوكلاء المحسنين الأساسيين
try:
    from .enhanced_error_detector import EnhancedErrorDetectorAgent
    from .enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
    from .enhanced_file_organizer import EnhancedFileOrganizerAgent
    from .enhanced_memory_agent import EnhancedMemoryAgent
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الوكلاء المحسنين: {e}")

# استيراد الوكلاء المتخصصين
try:
    from .database_agent import DatabaseAgent
    from .smart_ai_agent import SmartAIAgent
    from .smart_code_analyzer import SmartCodeAnalyzer
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الوكلاء المتخصصين: {e}")

# قائمة الوكلاء المتاحين
__all__ = [
    # الوكلاء المحسنين
    'EnhancedErrorDetectorAgent',
    'EnhancedProjectAnalyzerAgent',
    'EnhancedFileOrganizerAgent',
    'EnhancedMemoryAgent',

    # الوكلاء المتخصصين
    'DatabaseAgent',
    'SmartAIAgent',
    'SmartCodeAnalyzer'
]

# معلومات الوحدة
__version__ = "2.0"
__author__ = "Amr Ashour"
__description__ = "نظام الوكلاء المحسنين لنظام أنوبيس"
'''

        try:
            with open(init_file, "w", encoding="utf-8") as f:
                f.write(init_content)
            print(f"   ✅ تم تحديث __init__.py")
            return True
        except Exception as e:
            print(f"   ❌ خطأ في تحديث __init__.py: {e}")
            return False

    def create_agents_readme(self):
        """إنشاء README محدث لمجلد الوكلاء"""
        print("\n📚 إنشاء README محدث للوكلاء...")

        readme_content = """# 🤖 مجلد الوكلاء المحسنين - نظام أنوبيس
## Enhanced Agents Directory - Anubis System

**آخر تحديث**: 2025-07-16
**الحالة**: منظف ومحسن بالتعاون مع Gemini CLI

---

## 🎯 الوكلاء المحسنين الأساسيين

### 🔍 EnhancedErrorDetectorAgent
**الملف**: `enhanced_error_detector.py`
**الوظيفة**: كشف الأخطاء المتقدم للباك إند والفرونت إند

**المميزات**:
- دعم 15+ لغة برمجة (Python, JavaScript, TypeScript, React, Vue)
- كشف أخطاء الأمان (XSS, SQL Injection, كلمات المرور المكشوفة)
- تحليل الأداء واقتراحات التحسين
- تكامل مع الذكاء الاصطناعي

**الاستخدام**:
```python
from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent

detector = EnhancedErrorDetectorAgent(".", {}, True)
result = detector.scan_single_file("file.py")
```

### 📊 EnhancedProjectAnalyzerAgent
**الملف**: `enhanced_project_analyzer.py`
**الوظيفة**: تحليل شامل للمشاريع وكشف التقنيات

**المميزات**:
- كشف نوع المشروع تلقائياً (React, Vue, Django, FastAPI)
- تحليل التقنيات والأطر المستخدمة
- تقييم جودة المشروع (نقاط 0-100)
- توصيات ذكية للتحسين

**الاستخدام**:
```python
from anubis.agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent

analyzer = EnhancedProjectAnalyzerAgent(".", {}, True)
result = analyzer.analyze_project()
```

### 📁 EnhancedFileOrganizerAgent
**الملف**: `enhanced_file_organizer.py`
**الوظيفة**: تنظيم ذكي للملفات وإنشاء هياكل المشاريع

**المميزات**:
- تنظيم تلقائي للملفات (6 فئات)
- إنشاء هياكل مشاريع جاهزة (React, Vue, Django, FastAPI)
- تنظيف المجلدات الفارغة
- نسخ احتياطية للملفات المهمة

**الاستخدام**:
```python
from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent

organizer = EnhancedFileOrganizerAgent(".", {}, True)
result = organizer.organize_files()
project = organizer.create_project_structure("react", "my-app")
```

### 🧠 EnhancedMemoryAgent
**الملف**: `enhanced_memory_agent.py`
**الوظيفة**: إدارة الذاكرة الذكية والبحث المتقدم

**المميزات**:
- تخزين ذكي مع تصنيف وتاريخ
- بحث متقدم في المفاتيح والمحتوى
- إحصائيات شاملة للذاكرة
- إدارة الفئات والتصنيفات

**الاستخدام**:
```python
from anubis.agents.enhanced_memory_agent import EnhancedMemoryAgent

memory = EnhancedMemoryAgent(".", {}, True)
memory.store_memory("key", "data", "category")
data = memory.retrieve_memory("key")
```

---

## 🔧 الوكلاء المتخصصين

### 🗄️ DatabaseAgent
**الملف**: `database_agent.py`
**الوظيفة**: إدارة قواعد البيانات المتقدمة

**المميزات**:
- دعم قواعد بيانات متعددة
- عمليات CRUD آمنة
- إدارة الاتصالات
- نسخ احتياطية

### 🧠 SmartAIAgent
**الملف**: `smart_ai_agent.py`
**الوظيفة**: الذكاء الاصطناعي الشامل على مستوى المشروع

**المميزات**:
- تحليل شامل للمشروع
- اقتراحات عامة للتحسين
- تكامل مع مقدمي AI متعددين
- تحليل ذكي للسياق

### 📝 SmartCodeAnalyzer
**الملف**: `smart_code_analyzer.py`
**الوظيفة**: تحليل الكود الذكي للملفات الفردية

**المميزات**:
- تحليل ملفات الكود بالذكاء الاصطناعي
- اقتراحات تحسين محددة
- كشف الأنماط والمشاكل
- تكامل مع SmartAIAgent

---

## 📊 إحصائيات المجلد

| النوع | العدد | الحالة |
|-------|-------|--------|
| **الوكلاء المحسنين** | 4 | ✅ نشط |
| **الوكلاء المتخصصين** | 3 | ✅ نشط |
| **الوكلاء المحذوفين** | 5 | 🗑️ محذوف |
| **المجموع النشط** | **7** | **✅ جاهز** |

---

## 🗑️ الوكلاء المحذوفين

تم حذف الوكلاء التالية لأنها قديمة أو مكررة:

- `file_organizer_agent.py` → استبدل بـ `enhanced_file_organizer.py`
- `memory_agent.py` → استبدل بـ `enhanced_memory_agent.py`
- `project_analyzer_agent.py` → استبدل بـ `enhanced_project_analyzer.py`
- `error_detector.py` → استبدل بـ `enhanced_error_detector.py`
- `error_detector_agent.py` → استبدل بـ `enhanced_error_detector.py`

**النسخ الاحتياطية**: متوفرة في `backup/old_agents/`

---

## 🚀 الاستخدام السريع

### استيراد جميع الوكلاء:
```python
from agents import (
    EnhancedErrorDetectorAgent,
    EnhancedProjectAnalyzerAgent,
    EnhancedFileOrganizerAgent,
    EnhancedMemoryAgent,
    DatabaseAgent,
    SmartAIAgent,
    SmartCodeAnalyzer
)
```

### مثال شامل:
```python
# إنشاء الوكلاء
detector = EnhancedErrorDetectorAgent(".", {}, True)
analyzer = EnhancedProjectAnalyzerAgent(".", {}, True)
organizer = EnhancedFileOrganizerAgent(".", {}, True)
memory = EnhancedMemoryAgent(".", {}, True)

# تشغيل التحليل الشامل
errors = detector.scan_entire_project()
analysis = analyzer.analyze_project()
organized = organizer.organize_files()

# حفظ النتائج في الذاكرة
memory.store_memory("last_scan", {
    "errors": errors,
    "analysis": analysis,
    "organized": organized
})
```

---

## 🔧 الصيانة والتطوير

### إضافة وكيل جديد:
1. إنشاء ملف الوكيل في هذا المجلد
2. الوراثة من `BaseAgent`
3. تحديث `__init__.py`
4. إضافة التوثيق هنا

### تحديث وكيل موجود:
1. تعديل الملف المطلوب
2. تشغيل الاختبارات
3. تحديث التوثيق

---

## 📞 الدعم

**المطور**: Amr Ashour
**التحديث**: بالتعاون مع Gemini CLI
**التاريخ**: 2025-07-16

---

<div align="center">

**🤖 مجلد الوكلاء المحسنين - نظام أنوبيس**

**منظف ومحسن بالتعاون مع Gemini CLI**

[![Agents](https://img.shields.io/badge/Agents-7%20Active-brightgreen.svg)](README.md)
[![Status](https://img.shields.io/badge/Status-Clean-success.svg)](README.md)
[![Version](https://img.shields.io/badge/Version-2.0-blue.svg)](README.md)

</div>"""

        readme_file = self.agents_path / "README.md"

        try:
            with open(readme_file, "w", encoding="utf-8") as f:
                f.write(readme_content)
            print(f"   ✅ تم إنشاء README محدث")
            return True
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء README: {e}")
            return False

    def generate_cleanup_report(self, backed_up_files, removed_files):
        """إنتاج تقرير التنظيف"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "operation": "agents_cleanup",
            "gemini_analysis": True,
            "classification": self.agents_classification,
            "backed_up_files": backed_up_files,
            "removed_files": removed_files,
            "remaining_agents": {
                "enhanced": len(self.agents_classification["enhanced_agents"]),
                "useful": len(self.agents_classification["useful_agents"]),
                "total_active": len(self.agents_classification["enhanced_agents"])
                + len(self.agents_classification["useful_agents"]),
            },
            "summary": {
                "files_backed_up": len(backed_up_files),
                "files_removed": len(removed_files),
                "cleanup_success": len(removed_files) > 0,
            },
        }

        # حفظ التقرير
        reports_dir = self.base_path / "reports"
        reports_dir.mkdir(exist_ok=True)

        report_file = (
            reports_dir / f'agents_cleanup_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"\n📄 تم حفظ تقرير التنظيف: {report_file}")
            return str(report_file)

        except Exception as e:
            print(f"\n❌ خطأ في حفظ التقرير: {e}")
            return None

    def run_cleanup(self):
        """تشغيل عملية التنظيف الكاملة"""
        print("🚀 بدء تنظيف مجلد الوكلاء بناءً على تحليل Gemini CLI")
        print("=" * 60)

        # إنشاء نسخة احتياطية
        backed_up_files = self.create_backup()

        # حذف الوكلاء القديمين
        removed_files = self.remove_old_agents()

        # تحديث __init__.py
        init_updated = self.update_init_file()

        # إنشاء README محدث
        readme_created = self.create_agents_readme()

        # إنتاج التقرير
        report_file = self.generate_cleanup_report(backed_up_files, removed_files)

        # عرض النتائج
        print(f"\n🏆 تم إكمال تنظيف مجلد الوكلاء!")
        print(f"📦 ملفات النسخ الاحتياطية: {len(backed_up_files)}")
        print(f"🗑️ ملفات محذوفة: {len(removed_files)}")
        print(f"🔧 __init__.py: {'✅ محدث' if init_updated else '❌ فشل'}")
        print(f"📚 README.md: {'✅ محدث' if readme_created else '❌ فشل'}")

        # عرض الوكلاء المتبقين
        total_active = len(self.agents_classification["enhanced_agents"]) + len(
            self.agents_classification["useful_agents"]
        )
        print(f"\n🤖 الوكلاء النشطين: {total_active}")
        print(f"   📈 محسنين: {len(self.agents_classification['enhanced_agents'])}")
        print(f"   🔧 متخصصين: {len(self.agents_classification['useful_agents'])}")

        return {
            "backed_up_files": backed_up_files,
            "removed_files": removed_files,
            "init_updated": init_updated,
            "readme_created": readme_created,
            "report_file": report_file,
        }


def main():
    """الدالة الرئيسية"""
    cleanup = AgentsCleanup()
    result = cleanup.run_cleanup()

    print(f"\n✅ تم تنظيف مجلد الوكلاء بنجاح!")
    print(f"🏺 نظام أنوبيس - مجلد وكلاء نظيف ومنظم!")

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
