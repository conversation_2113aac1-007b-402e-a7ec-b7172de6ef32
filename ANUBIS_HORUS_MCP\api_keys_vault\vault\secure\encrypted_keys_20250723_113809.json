{"metadata": {}, "encrypted_keys": {"google_gemini": {"platform": "Google Gemini AI", "description": "Google's Gemini AI API keys for various applications", "keys": [{"key_id": "280c383d9966428bfaf9281aa650389d", "name": "gemini_api_1", "usage": "General Gemini API access", "encrypted_data": {"key_id": "280c383d9966428bfaf9281aa650389d", "encrypted_key": "Z0FBQUFBQm9nSjl4ak1ZZldQejdmUmU1dkxCLURQclF5RFZqS2VyOWJoNDRSWXBjR2F3WDFYb0NxVkREelNfUVJfbkVScmNERFhiOTZkQ2FmWjBRYXBwQ0JnZm92UVF4eFdJd29IN0V5UTh1cXh5dU10UTFaSUNvZjI1ZkJ0TzNiVGlKbG1tMlY1d08=", "key_hash": "f311aec551ab815df61e7051b8a961b4e9f8e313e8348c5d0d78bdaef1b2adbe", "created_at": "2025-07-23T11:38:09.894382", "metadata": {"name": "gemini_api_1", "usage": "General Gemini API access", "platform": "google_gemini", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "a7a4797488ecc71c0f25407096e3cd28", "name": "gemini_api_2", "usage": "Secondary Gemini access", "encrypted_data": {"key_id": "a7a4797488ecc71c0f25407096e3cd28", "encrypted_key": "Z0FBQUFBQm9nSjl4M3lCcnE4WUR1MWVPRHZreXg3Y0FtR2xLQmkzYlJzOUdRVW8yVlM4ZXllODg3WkczRndPNGpnSnVITEpsUnltQ0x1cjJVX053NmJQcWxlU0dPQXNkdFdraGV4MXZyTUYxMUVlNEpJd2dWb0RfekVGcjR1MmNKTmJPcFI3a21YUlI=", "key_hash": "2b21e2ef099202a71a3380c58d817b12d8cc313047ba62e01f8f7ab021931a4d", "created_at": "2025-07-23T11:38:09.895086", "metadata": {"name": "gemini_api_2", "usage": "Secondary Gemini access", "platform": "google_gemini", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "02cbd62f78ae3ca3cfa4e5be2885bd4e", "name": "gemini_windsurf", "usage": "Google Studio Windsurf integration", "encrypted_data": {"key_id": "02cbd62f78ae3ca3cfa4e5be2885bd4e", "encrypted_key": "Z0FBQUFBQm9nSjl4ckdQWGMxanV0cUdWUi1NYXRCeGlyU3luR2o1dElCb051Q0hQVUE1Sk5Zd20tVnNxNmZBZ1Y5eDNHbTVoSHIteHBfNnJTV3BseG1teXpkbGtoQkFyWHB3TFphU2xHYUZvWUlnaFUyU0NPeURPM3E1djZyYzJBbllkdzA2bXVPUkc=", "key_hash": "842c07de3f663a22b752d1d9ebad22c3c238743d7ac7cd7365d8dbb820192be0", "created_at": "2025-07-23T11:38:09.895657", "metadata": {"name": "gemini_windsurf", "usage": "Google Studio Windsurf integration", "platform": "google_gemini", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "07e1a1722f8a945d19a3fd63b23dc5c0", "name": "gemini_adk", "usage": "ADK Google integration", "encrypted_data": {"key_id": "07e1a1722f8a945d19a3fd63b23dc5c0", "encrypted_key": "Z0FBQUFBQm9nSjl4cXF1RGdOS0JkelF3Z2RIbjUtYUhQb0lQWndUY0h5VnVTNXFwLVhVYXQ0b1V3R0lsV2hUVGEzM1FGYmYzWjhhT3JGSi1vcGVFaTQ1SjFWaGNUcUl5MVpEdGJDU252eExLS1FZd2RkMEdOUmctVGpwa2l1VDhTYi12Y3lHZ0RYd1Y=", "key_hash": "903383bc2e4f05c54be9d91d3ddb07596957b0d2dc836384932ecc4b1820e12a", "created_at": "2025-07-23T11:38:09.896381", "metadata": {"name": "gemini_adk", "usage": "ADK Google integration", "platform": "google_gemini", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "openrouter": {"platform": "OpenRouter", "description": "OpenRouter API keys for multiple AI model access", "keys": [{"key_id": "d90e25d1fbf06d936d152a49bc6c66be", "name": "openrouter_main", "usage": "Main OpenRouter access", "encrypted_data": {"key_id": "d90e25d1fbf06d936d152a49bc6c66be", "encrypted_key": "Z0FBQUFBQm9nSjl4OXltV2R2cXhEV0xYYUw3YkprRU9IVzlLb3dQcm95VkpaTldxN3Y2UUdiYWtHNGVHdzVNT1NOS1V0bmtETnZIVGFPQ1lfRHhCYWN3b3dpV1ZPdDd2WDRVNGdmd3ZIR3hjYlE4RnhFenF2RVlKSk5HcVBBbTRvUDZSamoyeDdzSG9ORE9hdktGaXo1TTJndnRic3lNbEw1SWVVZDgtTFl2b2F3akRocFBBSnFjPQ==", "key_hash": "4cec4af19539aa2aea4e58bb2539d6b448bc6bbdb106329fc9586e0dba3ceab9", "created_at": "2025-07-23T11:38:09.896866", "metadata": {"name": "openrouter_main", "usage": "Main OpenRouter access", "platform": "openrouter", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "30e4095f0b78b14ca1a84873a1fa21c4", "name": "openrouter_secondary", "usage": "Secondary OpenRouter access", "encrypted_data": {"key_id": "30e4095f0b78b14ca1a84873a1fa21c4", "encrypted_key": "Z0FBQUFBQm9nSjl4VHBSVWw4eU1Nc2R2QlFIZ19idHh5UVFuOURELU8zVHZ3U1pvbVA2eU9yMjAwMjlyRE9DVy1nUlZmMktKaTZPRGk0RGNsU2JRVVltT0NZcUhHQ2xpWkdGVXd1VVBCMTdtLXZZLXBqSUxOWF9ncG5nNUNOTWx5YlltTGMxaE9ybTRBbE9XcDJQYnc4VUtpcXlrT3JKVEd1Xy1manJROElxOVlwSncwSGRzYk1BPQ==", "key_hash": "0a8774af2dde41d0fded1d017ae5dd548986070a3bce271c05ddf4644d535abe", "created_at": "2025-07-23T11:38:09.897520", "metadata": {"name": "openrouter_secondary", "usage": "Secondary OpenRouter access", "platform": "openrouter", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "471a54c59010731cbb27555bc989c989", "name": "openrouter_cline", "usage": "Cline API VS Alpha integration", "encrypted_data": {"key_id": "471a54c59010731cbb27555bc989c989", "encrypted_key": "Z0FBQUFBQm9nSjl4QkxxYnh1ODNocFBjYkhiZ1ozaHVLYTRwcGI0d2pNbWRwRmg1bnlEejl2S012dW94MVJQY0dHUDFDUnRaUGlXc092cF9qdDhZY0tFMmthOTZmYi1uOW1LYy1UWW1VVUNjLXpIcHNBeng5OTNZS2htcEZCRHRaclN4RVY4em9Na1pqaHJjLXpwYTNGaWRiYUZDQk9oM1NiME1WOG9qQ3lsdzVKRHNMSUV3eThVPQ==", "key_hash": "a8f38b503149c76f7b7820056b42e9bfeb0cda183fea62f6a05221024723ea71", "created_at": "2025-07-23T11:38:09.898145", "metadata": {"name": "openrouter_cline", "usage": "Cline API VS Alpha integration", "platform": "openrouter", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "5f82be6c4ec029efa8e4cbb3a2e1cfd9", "name": "openrouter_router", "usage": "Router application", "encrypted_data": {"key_id": "5f82be6c4ec029efa8e4cbb3a2e1cfd9", "encrypted_key": "Z0FBQUFBQm9nSjl4VFgtdzlRQUVYc1hjOWQzUnJuVFUyZTNTLUlicmczLWVkUGRvUkw1V3N6U1dUQ2NlSEp4alJkZnRLUkxwQkRXVExuN3dDOEg4YkJWdXgzWWZZMnlmc2JpQXlrQ1o1TXNtaWpSbzFWc3BORTZQWlFHS3ZLUHBmX2pxOVhfUDd4ZURHMkNXZXNpNE1XVVVUMEdJNXJLbzJQaVVCc0pJZ1NZSDNxRTVpR1FPS3owPQ==", "key_hash": "aac58ebe02f0f95fd5a406056e8d43d48f0f5f6cc98f5c6b005b94eedbcd13a0", "created_at": "2025-07-23T11:38:09.898590", "metadata": {"name": "openrouter_router", "usage": "Router application", "platform": "openrouter", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "github": {"platform": "GitHub", "description": "GitHub Personal Access Tokens", "keys": [{"key_id": "28ab75bc36eb69065c26cdcc05f39a53", "name": "github_main_pat", "usage": "GitHub + AnythingLLM integration", "encrypted_data": {"key_id": "28ab75bc36eb69065c26cdcc05f39a53", "encrypted_key": "Z0FBQUFBQm9nSjl4M19Db1l3WmQ4LUxLanQ1ZEJPN0MwOGJrb3RBczlHSXY2TU9NWlZOTjNmeXZINDZZcFRyc3hRSXZjVHdzQ0prQXl4X3ZXc1YteEdyMjR2ZzBDNDNMNHFjblBReFpXUHZGOHE4MUhMOFRrOE9LNUlkNVdlOEFwTC1uMjJGZEFGbzJSdlNzYXRzcHZpeGRtUENWQnhpTmNhWDhULUZHNFUxcl9WdDNlWVBxdjZoLWNOQzNULUNuUWI4am5KbVpkZnVY", "key_hash": "cf1155bc18af495868e5b00d3cc96468283b3a1cd7c6fe306fd1453b99bbc67c", "created_at": "2025-07-23T11:38:09.899018", "metadata": {"name": "github_main_pat", "usage": "GitHub + AnythingLLM integration", "platform": "github", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "huggingface": {"platform": "Hugging Face", "description": "Hugging Face API tokens for model access", "keys": [{"key_id": "eac36771021cf0fcbffe5a4fc625f618", "name": "hf_mcp_integration", "usage": "Hugging Face to MCP integration", "encrypted_data": {"key_id": "eac36771021cf0fcbffe5a4fc625f618", "encrypted_key": "Z0FBQUFBQm9nSjl4OU56cl9Ma2RPRkNuUlZGV3JPcE41eWpRMDZEUjFvT01GdG5KMHNBZnFRa1pEUkt1VDJDQXF6MllxbXdQeDBLRExNNkVodEZyLXh5NlpLTHZQTlluUDZMTThfYkZjdHcwVjNjX0ppV2hlQWxYZFlicnBVcGpoWmlXN0QwSkNHQnM=", "key_hash": "2813b8073237caab4303c02d8c0da72a9b226c32c51e9df1bc81cee0254e3c1d", "created_at": "2025-07-23T11:38:09.899504", "metadata": {"name": "hf_mcp_integration", "usage": "Hugging Face to MCP integration", "platform": "huggingface", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "f5b2e19e5d0296a6abbb8d40b273b304", "name": "hf_horus_team", "usage": "Hugging Face Horus team access", "encrypted_data": {"key_id": "f5b2e19e5d0296a6abbb8d40b273b304", "encrypted_key": "Z0FBQUFBQm9nSjl4RVJnQko5T3ZPVXpadDJ1ZDVDVUJzSW9sdzZBV3hIdmFMLUEweV9NbXhTdEJLUDJOQ3hVUk9TeFZVd1BlcWlmTVlNc3luRmY3SnlSTURITVNkbnlQZFNoZGdrMEExeHZ2aGNHUEluTVg0alJpXzNaSEdCRVRFSFFJTTM2cXZWY0g=", "key_hash": "4ba47cc41c7a7e03d6aba9e5c07a0a87c3fc4b5d25f0e0729cc22e1c6cd7170d", "created_at": "2025-07-23T11:38:09.899948", "metadata": {"name": "hf_horus_team", "usage": "Hugging Face Horus team access", "platform": "huggingface", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "a57101fe9c3e1700907b07a411671345", "name": "hf_kimi_1", "usage": "Kimi integration 1", "encrypted_data": {"key_id": "a57101fe9c3e1700907b07a411671345", "encrypted_key": "Z0FBQUFBQm9nSjl4X0JXVk9zMVZYQk5jeGplQ1NTQXZkRVYtNXYyM1ZkeDZ1WEM1c0o5cm9ueXRwRmsydHM4QXNqVXEzeHdhSlhkTkt4Mk42RGtTb2JVMElFU3hyVDJrWTVEb2wyWDRyaGNHSzVRc1Y0MWliWVUtcGljY1J1NjJqc3lwLVdzREhjR1M=", "key_hash": "2cd504e030f3820e8fab4fd879d0174557547146988c57118605ccbd15928880", "created_at": "2025-07-23T11:38:09.900478", "metadata": {"name": "hf_kimi_1", "usage": "Kimi integration 1", "platform": "huggingface", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "b6c2319a57902080f8665e3fd1bbff4c", "name": "hf_kimi_2", "usage": "Kimi integration 2", "encrypted_data": {"key_id": "b6c2319a57902080f8665e3fd1bbff4c", "encrypted_key": "Z0FBQUFBQm9nSjl4WU9MWWxwSGFtQ3dTYVRoLWZKYWFmWUwzMjNCQkUtWmpNWWVqUVZjU3Y0djlpYi16YWltN2JBbzlwamg2R095X3dWWjdWWU5JUm5wT1NzLUNtRE9BX2hZR1V3SUVCdi1CR3VLbEdiQXVVeUZrTDFqcXoxWndMa1U3MFNheUIyVVM=", "key_hash": "4c6d6270773534ffba82cc5115ffdbe85c9aa13d87bd067445c23855a2995d37", "created_at": "2025-07-23T11:38:09.900928", "metadata": {"name": "hf_kimi_2", "usage": "Kimi integration 2", "platform": "huggingface", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "3f245ed23f89237ca3f486004ed9d922", "name": "hf_general", "usage": "General Hugging Face access", "encrypted_data": {"key_id": "3f245ed23f89237ca3f486004ed9d922", "encrypted_key": "Z0FBQUFBQm9nSjl4UEJUbWh1bUxsM3hmb0MwWXd3MGpja2tMaHF2OXJJa1RpUVloRS1UR0NDOW01WXliWTNKWTE4Y1AzeUJpVFR1MkItUXhjSklqQWVzTlZsWTdocTRfYVRoWEp2Vi1iTVFTR1JRVnJrc0FGTFEtTzNfLXhTa3psYVU5cnliNXRhUk8=", "key_hash": "f3ac8dd3f38f8ed9f710eebe300d3b09f4cfa0c4b742be3b0e77549001bf1f4d", "created_at": "2025-07-23T11:38:09.901417", "metadata": {"name": "hf_general", "usage": "General Hugging Face access", "platform": "huggingface", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "deepseek": {"platform": "DeepSeek", "description": "DeepSeek AI API keys", "keys": [{"key_id": "00b504de05a52d7295189bb3782adc42", "name": "deepseek_web_v3", "usage": "DeepSeek V3 website integration", "encrypted_data": {"key_id": "00b504de05a52d7295189bb3782adc42", "encrypted_key": "Z0FBQUFBQm9nSjl4LWsxanF4NUtvSFZ2ZnZSUFpGS1RXUTNrbWI4bG91ZDdILUNZb2ZBREk2a0RkYTZOdXoyR3R3SURnVkJfVU1sa3VLam9Ud0NsUHc3WU5VLTMwMEVZek0yUkhyeFNaVUVfSTkzN0k0OVpmbTdoRzJ1RFIzVUo5Y2hTUVNHMFlFTXE=", "key_hash": "ced85a72a80d3f0dde703bf1332a5f56b97c9d7968168a338c031335d39af7e5", "created_at": "2025-07-23T11:38:09.901873", "metadata": {"name": "deepseek_web_v3", "usage": "DeepSeek V3 website integration", "platform": "deepseek", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "3e4ddbb8ee2f4240651f923d2f70b272", "name": "deepseek_app_ui", "usage": "DeepSeek app UI TARS", "encrypted_data": {"key_id": "3e4ddbb8ee2f4240651f923d2f70b272", "encrypted_key": "Z0FBQUFBQm9nSjl4Ry1GaVBlcUlSM2JWeUZGMS10blVtUS1WSDBXMl9nQm9IdW1OaGVPWVE0NUE1UVVaeUItWkdhc2Z3dXctSnppRWo2dG5IMWpvRGUtM3E3bFFSWW9KTVNoM3FIbmM0ZFNUU3MxeHI1YlIxUlB2a2c2NGhDQ21rcjFEbE9iaHBzcS0=", "key_hash": "7da7e13a1165097b58ec2b2ab6904cc0bc51560b8f4d0d338b3a52dcdd3d85dc", "created_at": "2025-07-23T11:38:09.902349", "metadata": {"name": "deepseek_app_ui", "usage": "DeepSeek app UI TARS", "platform": "deepseek", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "49787d191c0963d10ec15d8afe4805f6", "name": "deepseek_roo_code", "usage": "DeepSeek Roo Code integration", "encrypted_data": {"key_id": "49787d191c0963d10ec15d8afe4805f6", "encrypted_key": "Z0FBQUFBQm9nSjl4WWFoSUFZWElqQkxPSjhJR1Z6SmhULTJpNngyOEUyek80V29acWZfWkJrSHlTODhnaXRzdEQySC16VVc3QW1rVm5PRnB1OERtNnlkOEJqb2owRmlVM0FfbDQ5RjFidmMzQ0kxRG9OTTJEdVZLV0JZZlhBQUhlYVk0T1pFMGoyZEI=", "key_hash": "c0aa63257195f14abeb22b746afdcbcd3131eb43b9dedee781d4f53775434fee", "created_at": "2025-07-23T11:38:09.902819", "metadata": {"name": "deepseek_roo_code", "usage": "DeepSeek Roo Code integration", "platform": "deepseek", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "anthropic": {"platform": "Anthropic <PERSON>", "description": "Anthropic Claude API keys", "keys": [{"key_id": "de4725bcf66c8715cbbde16e5899fdab", "name": "claude_cloud_key", "usage": "Claude cloud key integration", "encrypted_data": {"key_id": "de4725bcf66c8715cbbde16e5899fdab", "encrypted_key": "Z0FBQUFBQm9nSjl4WFkzTHFJaWtwMmE3Z0FwMHVQSmRIaDNYMG45cWJMaU1TeUR4cnJPN253UUhUZHdtZjdsV1l2aEFyYjdzVzlvUDZFYkRFdmhzUHd1UWt1RVVlYlJPVXZMSmRXaXAtVko4aXVnT1FuWEszY2JFd0NLeUdPbllYZ2x3TURzdll6Tm51ZXZfXy1UZkhkY3hUeWl3eVYxSm91clBrNGpiWGw5T3hMaU9iT0lETGFQSUVTakZfRHc4LVpYU1A3R0J2akpFX2dibEo5OU45eWtxOWs2SGNPYWxuQT09", "key_hash": "b2597ff9c595852a4a3f11159c9f7fbf1fed11ad808634e59fa238f17b05b41a", "created_at": "2025-07-23T11:38:09.903392", "metadata": {"name": "claude_cloud_key", "usage": "Claude cloud key integration", "platform": "anthropic", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "together_ai": {"platform": "Together.ai", "description": "Together.ai API for collaborative AI", "keys": [{"key_id": "702f40381e8a2fd3c13aab90cd4de75c", "name": "together_main", "usage": "Together.ai main access", "encrypted_data": {"key_id": "702f40381e8a2fd3c13aab90cd4de75c", "encrypted_key": "Z0FBQUFBQm9nSjl4V3EyclFweXJNOVZHb2dwWUxJbXhxMnlneUhacm1SdmU0Q3hBNmR6NWs4YlVrR2E2MkpTczZIb1dqRXJkM1VQLU5kcFVmU29zNDNoZnVNODRDTk1uTTYzeHJNT19YSHdWdlMxcUh0djJ1XzBwRUJvMjU5aEZiYVRVVnBBWXJlUFJUdmpwTklhYTM3el82NjM5RkZSVlRVbFpsb2k2LXUzLXdWTUswQ1lfVUU0PQ==", "key_hash": "508bf35960bd4ce9c4f7e92fd414fc7656e211f1cf30c9c276e05fb9e2536f54", "created_at": "2025-07-23T11:38:09.903913", "metadata": {"name": "together_main", "usage": "Together.ai main access", "platform": "together_ai", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "continue_extension": {"platform": "Continue Extension", "description": "Continue VS Code extension API keys", "keys": [{"key_id": "fea44c3fb5fcd8137ce2d51a001fb583", "name": "continue_main", "usage": "Main Continue extension", "encrypted_data": {"key_id": "fea44c3fb5fcd8137ce2d51a001fb583", "encrypted_key": "Z0FBQUFBQm9nSjl4eFp6enB2aXdmYlhvRDhQVVlGSTN2ckh0dDZ4Zmo3dXBoSDh1WjdVQlk1X2wtQXl0bnJJXzRLZTZ4RV82MWZrY3VkbFBCZUVHNk1GZEEyYVJaTGJ6d2FQazduM3p2N1NjUHZFWHlVNmpSUFUxWWxYeGU3bllpRzVVd1pFRGFYeUptbjZtVC1QcWdDWlp2bEFyczhpX2FZOWRqWXNCRnNqMzBMc09nNW5jTFpnPQ==", "key_hash": "c2fadca62fe4778aa67fbf12a544b14c162738a204aecc93c1ec2c341babcbb4", "created_at": "2025-07-23T11:38:09.904360", "metadata": {"name": "continue_main", "usage": "Main Continue extension", "platform": "continue_extension", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "********************************", "name": "continue_secondary", "usage": "Secondary Continue extension", "encrypted_data": {"key_id": "********************************", "encrypted_key": "Z0FBQUFBQm9nSjl4TmdzbUNiLWhvdThZeDhnaWlrV1lzUjRwTVV4UjlOYTV4Tm1IM3F6Rjd0RWdrSERFQ1pMRkU1Y1dPZ2NkdTFYbVpEQlo3OFVyX3UxbjlycEpxR0Fla1JITFRncGdWY3NNRV9wOEcyUFhncFZ4LUlDVDBRSGNuV3hsc29kM0M0Sk96SlNnczN2UFBicDhLQXRUZjlCNHBjSWhXRXpzVzNsdnppcGktdlBHN2RnPQ==", "key_hash": "cb800cce3710cdf94cf6cb74ad9d9a1a4f20ce86edb9b84b0cfdd685545b57ee", "created_at": "2025-07-23T11:38:09.904925", "metadata": {"name": "continue_secondary", "usage": "Secondary Continue extension", "platform": "continue_extension", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "nebius_studio": {"platform": "Nebius Studio", "description": "Nebius Studio AI platform", "keys": [{"key_id": "c6b134061ed5d96e4fcfcfc7e69bafe1", "name": "nebius_jwt_token", "usage": "Nebius Studio JWT token", "encrypted_data": {"key_id": "c6b134061ed5d96e4fcfcfc7e69bafe1", "encrypted_key": "Z0FBQUFBQm9nSjl4WkpvamFfMUVwc0V3VW5tWU5URlEwREFBWHVwYXBERVd6OEJZMndxT3h6WFlmSERYT21HYnNMdzA3ci1XaHZvYWpTSzJHQm9QbnAtVTBhVlRNaVh5bXRPTGJqY0pVeEFWRDkzcm9oUTdCSG8yeUVkbHhUNk1WZHItbk1BOV9vc3NfUUhtX2NMaEVXTHkxSnZsQU5pQVBjbTBCSFViU1JaY2tsVUYwUUhoMWIyMzZpbWhvTVpDa0VFSW5pSE1sMHZZUnF3c3E1cUNwQWZ0Y0VvSjdEbWJhLVFZc0V6YnB3eWRnY2R1TXN2U1FyeVUtZlo3c2I4X2wwWkhNX09KX0M4dVYwSFJYZWFDb3gxNXcxRTBvLUcyOVJ2LU8yQzU3UVZiS2c0LUczSkRXdVJZUlgzU2dXZEpxeEtwZmFndmpYYXFtWllRMEYtTG5wTEZVRzlSYzBBLWF6eGZIdHJiNkJGMmZCeFJ1TXhsalZHclVvNEl1QVRSSFFDZVg0S2xiUWdjaEdGYVpOdlNIRHpmOFdfSi1CSjNFVmpTcEhhd2JYVWhtY0oycEdxdm9TNkdvVUdPWlpVSjVNZWN4aE9HYmdwV2tWZW1JV0hPeGFFTXJXamc3cFdFOHoxWHBhVFVKR25Iejl2OGVYMkFaek1pcDZpbDA0UTdhRzU4UDI5YlQ1QjN6TFRIcTV4UWFiWU5STU54OUZvQlM1VG1rbFVLYWt4dmh4cGdKTDZOeG9HT3o4d1FfM3lMMWFOa1RaaktPSEttSGRfb29SUHNBbmQzS1ljNmVXTTBZRE9nc1RfZVBnNnNIRlB4MjVVY1BmczV4TXYxN1hFbkZ4VldzbTQ3VnNOMFlBWUFKY19zVnhpOTRmeHBaQ0Q3aDN2YWpLLTNDa01aNzF5NzFlQmFTMExaZENUYTNfNDN5dWxldk93aVZXRFZYR3NGMkRPVE42dS1qYTZaajI2ejVyLVpIeFJLYnJkUHM4ajh5Rzc1aXRNd3o3TVpwR3k5UlV4NFRJQXdIcFNB", "key_hash": "5c7fa089eb32d7538bcb6e1f8c6c3d8f3d54e31c5221ec8a10b93dd628d406a5", "created_at": "2025-07-23T11:38:09.905626", "metadata": {"name": "nebius_jwt_token", "usage": "Nebius Studio JWT token", "platform": "nebius_studio", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "mistral": {"platform": "Mistral AI", "description": "Mistral AI API keys for advanced language models", "keys": [{"key_id": "8a6ac4a75075d0b8488bc08c565501be", "name": "mistral_main", "usage": "Main Mistral AI access", "encrypted_data": {"key_id": "8a6ac4a75075d0b8488bc08c565501be", "encrypted_key": "Z0FBQUFBQm9nSjl4NUpTT2JMeVdCU0JKNVVzUEh5UWJUNzF5bkNiLUFzejdkREFlbVlmSG40N2ltWHdzWm1nTmNuTVRtTVZRSVAyeXhMMW9jNVQwS1MwUDVPY2dsWlEzb0JTellYMnYyTHByNUg3dlBGVFpXN2RrbnNWLVQ5ajBzZTBwTTZtZ2JHNGw=", "key_hash": "9c12cb826e85a0b011b9ceb29f955036135a93b1db0c648a2f7324773ea83abe", "created_at": "2025-07-23T11:38:09.906075", "metadata": {"name": "mistral_main", "usage": "Main Mistral AI access", "platform": "mistral", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}, "other_platforms": {"platform": "Various Other Platforms", "description": "API keys for various other platforms", "keys": [{"key_id": "86dadaa491201a8e075f61af693b123e", "name": "apidog_noor_stone", "usage": "Apidog API Noor Stone", "encrypted_data": {"key_id": "86dadaa491201a8e075f61af693b123e", "encrypted_key": "Z0FBQUFBQm9nSjl4NUZvNTZsbmw2Q2VLeHFFaGRPSGVkNUxyNkl1T0YtUlBwR3F5ZmhER3QwdTV1aFctLXFEaHRjVjZrWnhidS0zZUNSXy1hcVVRbTloOV9aUFBCZGpib21JSEFNMUdyeGNycWVESjZYbGprVF9tUUc4dHp2aXk4VTNnNFptTmVSLUY=", "key_hash": "5ebe1c06c0a69dc6477426c10c42fedfb0ac8a5bd277a9199902ca02e8f6e443", "created_at": "2025-07-23T11:38:09.906632", "metadata": {"name": "apidog_noor_stone", "usage": "Apidog API Noor Stone", "platform": "other_platforms", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "33af54330c20b32218e8068bf82b862d", "name": "kero_ide", "usage": "Kero IDE integration", "encrypted_data": {"key_id": "33af54330c20b32218e8068bf82b862d", "encrypted_key": "Z0FBQUFBQm9nSjl4TW9VZVVSUFJTS0VFRlZtR19ZMUh5X3pfX1Iyd0xMTjZrS1RPSi0waVE3OXBKRVBobnNhblhocnc2N1pDNVJ2VmU2cGNISHI5Ni1XbXBIb3JhWktXS1FnTGxhM1Rsci1oUUtQbzFQWnN1Vmx0Q01EX25jS0RXNlVkUTJDRnlvRWI=", "key_hash": "fe22b2273c77097658250914d1cc9314e218c8b706b81c3548fcc79e07bc24e0", "created_at": "2025-07-23T11:38:09.907109", "metadata": {"name": "kero_ide", "usage": "Kero IDE integration", "platform": "other_platforms", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}, {"key_id": "b5bb363bf373e670f4f8b0c00846146c", "name": "n8n_automation", "usage": "n8n automation platform", "encrypted_data": {"key_id": "b5bb363bf373e670f4f8b0c00846146c", "encrypted_key": "Z0FBQUFBQm9nSjl4X1gzQlh2bFI2SERoazBwcXNiNE1WdXpSS3NXRmVmQ05wOTA2dU02SlFIY3FUQjluZTU4OWE4TDktRk4xNWNhcUVLTUNPZHdFNlhMUGtyMGk2OUl0RG1mSlBCUXI0NURRUW1LSmg5Z1k2LTZpUERIbEVEZmxWcVhQZ0dDd2FiOXI=", "key_hash": "ac80d34918a9a9074a471c915898266604f8046ab3f044eef80594416b651388", "created_at": "2025-07-23T11:38:09.907539", "metadata": {"name": "n8n_automation", "usage": "n8n automation platform", "platform": "other_platforms", "status": "active"}, "encryption_version": "1.0"}, "status": "active"}]}}, "security_info": {"encryption_method": "AES-256", "created_at": "2025-07-23T11:38:09.892144", "total_keys": 26}}