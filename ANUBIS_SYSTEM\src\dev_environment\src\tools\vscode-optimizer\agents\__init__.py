"""
🤖 مجلد الوكلاء المحسنين - نظام أنوبيس
Enhanced Agents Module - Anubis System

الوكلاء المحسنين الأساسيين:
- EnhancedErrorDetectorAgent: كشف الأخطاء المتقدم
- EnhancedProjectAnalyzerAgent: تحليل المشاريع الذكي
- EnhancedFileOrganizerAgent: تنظيم الملفات المتقدم
- EnhancedMemoryAgent: إدارة الذاكرة الذكية

الوكلاء المتخصصين:
- DatabaseAgent: إدارة قواعد البيانات
- SmartAIAgent: الذكاء الاصطناعي الشامل
- SmartCodeAnalyzer: تحليل الكود الذكي

تم تنظيفه وتحديثه بالتعاون مع Gemini CLI
"""

# استيراد الوكلاء المحسنين الأساسيين
try:
    from .enhanced_error_detector import EnhancedErrorDetectorAgent
    from .enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
    from .enhanced_file_organizer import EnhancedFileOrganizerAgent
    from .enhanced_memory_agent import EnhancedMemoryAgent
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الوكلاء المحسنين: {e}")

# استيراد الوكلاء المتخصصين
try:
    from .database_agent import DatabaseAgent
    from .smart_ai_agent import SmartAIAgent
    from .smart_code_analyzer import SmartCodeAnalyzer
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الوكلاء المتخصصين: {e}")

# قائمة الوكلاء المتاحين
__all__ = [
    # الوكلاء المحسنين
    'EnhancedErrorDetectorAgent',
    'EnhancedProjectAnalyzerAgent', 
    'EnhancedFileOrganizerAgent',
    'EnhancedMemoryAgent',
    
    # الوكلاء المتخصصين
    'DatabaseAgent',
    'SmartAIAgent',
    'SmartCodeAnalyzer'
]

# معلومات الوحدة
__version__ = "2.0"
__author__ = "Amr Ashour"
__description__ = "نظام الوكلاء المحسنين لنظام أنوبيس"
