#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛠️ فاحص مجلد الأدوات والمرافق الشامل
Tools and Utilities Comprehensive Inspector
"""

import os
import json
from pathlib import Path
from datetime import datetime

class ToolsUtilitiesInspector:
    def __init__(self):
        self.base_path = Path("tools_and_utilities")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "inspector": "Tools and Utilities Comprehensive Inspector",
            "inspection_type": "comprehensive_tools_analysis",
            "overall_health": "unknown",
            "components": {},
            "tool_categories": {},
            "functionality_analysis": {},
            "recommendations": [],
            "optimization_opportunities": []
        }
    
    def analyze_structure(self):
        """تحليل هيكل مجلد الأدوات والمرافق"""
        print("🛠️ تحليل هيكل مجلد الأدوات والمرافق...")
        
        component_health = {
            "status": "analyzing",
            "main_directories": {},
            "total_files": 0,
            "tool_types": {},
            "issues": [],
            "strengths": []
        }
        
        if not self.base_path.exists():
            component_health["issues"].append("❌ مجلد tools_and_utilities/ مفقود")
            self.report["components"]["structure"] = component_health
            return component_health
        
        # فحص المجلدات الرئيسية
        main_dirs = ["docs", "optimizers", "scripts", "src", "vscode_tools"]
        
        for main_dir in main_dirs:
            dir_path = self.base_path / main_dir
            if dir_path.exists():
                files = list(dir_path.rglob("*"))
                files_count = len([f for f in files if f.is_file()])
                dirs_count = len([f for f in files if f.is_dir()])
                
                component_health["main_directories"][main_dir] = {
                    "exists": True,
                    "files_count": files_count,
                    "subdirs_count": dirs_count,
                    "status": "✅ نشط" if files_count > 0 else "⚠️ فارغ"
                }
                
                component_health["total_files"] += files_count
                component_health["strengths"].append(f"✅ {main_dir}/ ({files_count} ملف، {dirs_count} مجلد)")
            else:
                component_health["main_directories"][main_dir] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ مجلد {main_dir}/ مفقود")
        
        self.report["components"]["structure"] = component_health
        return component_health
    
    def analyze_vscode_tools(self):
        """تحليل أدوات VSCode المفصل"""
        print("🔧 تحليل أدوات VSCode...")
        
        vscode_path = self.base_path / "src" / "tools" / "vscode-optimizer"
        component_health = {
            "status": "analyzing",
            "optimizer_projects": {},
            "emergency_tools": {},
            "agents": {},
            "total_scripts": 0,
            "issues": [],
            "strengths": []
        }
        
        if not vscode_path.exists():
            component_health["issues"].append("❌ مجلد vscode-optimizer/ مفقود")
            self.report["components"]["vscode_tools"] = component_health
            return component_health
        
        # فحص مشاريع المحسن
        optimizer_projects = [
            "VS-Code-Performance-Optimizer",
            "VSCode-Control-Center"
        ]
        
        for project in optimizer_projects:
            project_path = vscode_path / project
            if project_path.exists():
                project_analysis = self._analyze_vscode_project(project_path, project)
                component_health["optimizer_projects"][project] = project_analysis
                component_health["total_scripts"] += project_analysis.get("scripts_count", 0)
                
                if project_analysis.get("has_agents"):
                    component_health["strengths"].append(f"✅ {project} مع وكلاء ذكيين")
                
                if project_analysis.get("has_config"):
                    component_health["strengths"].append(f"✅ {project} مع ملفات تكوين")
            else:
                component_health["issues"].append(f"❌ مشروع {project} مفقود")
        
        # فحص أدوات الطوارئ
        emergency_path = self.base_path / "src" / "tools" / "emergency"
        if emergency_path.exists():
            emergency_files = list(emergency_path.glob("*.bat"))
            component_health["emergency_tools"] = {
                "exists": True,
                "files_count": len(emergency_files),
                "files": [f.name for f in emergency_files],
                "status": "✅ متوفر"
            }
            component_health["strengths"].append(f"✅ أدوات الطوارئ ({len(emergency_files)} أداة)")
        else:
            component_health["emergency_tools"] = {
                "exists": False,
                "status": "❌ مفقود"
            }
            component_health["issues"].append("❌ أدوات الطوارئ مفقودة")
        
        # فحص الوكلاء
        agents_path = vscode_path / "agents"
        if agents_path.exists():
            agent_files = list(agents_path.glob("*.py"))
            component_health["agents"] = {
                "exists": True,
                "agents_count": len(agent_files),
                "agents": [f.stem for f in agent_files if f.name != "__init__.py"],
                "status": "✅ نشط"
            }
            component_health["strengths"].append(f"✅ وكلاء VSCode ({len(agent_files)} وكيل)")
        else:
            component_health["agents"] = {
                "exists": False,
                "status": "❌ مفقود"
            }
        
        self.report["components"]["vscode_tools"] = component_health
        return component_health
    
    def _analyze_vscode_project(self, project_path, project_name):
        """تحليل مشروع VSCode واحد"""
        analysis = {
            "exists": True,
            "files_count": 0,
            "scripts_count": 0,
            "has_agents": False,
            "has_config": False,
            "has_readme": False,
            "file_types": {}
        }
        
        # فحص الملفات
        all_files = list(project_path.rglob("*"))
        files = [f for f in all_files if f.is_file()]
        analysis["files_count"] = len(files)
        
        # تحليل أنواع الملفات
        for file in files:
            ext = file.suffix.lower()
            if ext not in analysis["file_types"]:
                analysis["file_types"][ext] = 0
            analysis["file_types"][ext] += 1
            
            # عد السكريبتات
            if ext in [".py", ".bat", ".sh", ".ps1"]:
                analysis["scripts_count"] += 1
        
        # فحص وجود المكونات المهمة
        analysis["has_agents"] = (project_path / "agents").exists()
        analysis["has_config"] = any(f.name.endswith(('.json', '.yaml', '.yml')) for f in files)
        analysis["has_readme"] = any(f.name.lower().startswith('readme') for f in files)
        
        return analysis
    
    def analyze_functionality(self):
        """تحليل الوظائف والقدرات"""
        print("⚙️ تحليل الوظائف والقدرات...")
        
        functionality = {
            "categories": {},
            "capabilities": [],
            "automation_level": "unknown",
            "integration_points": [],
            "missing_features": []
        }
        
        # تحليل الفئات الوظيفية
        structure = self.report["components"].get("structure", {})
        vscode_tools = self.report["components"].get("vscode_tools", {})
        
        # فئة التحسين
        if structure.get("main_directories", {}).get("optimizers", {}).get("exists"):
            functionality["categories"]["optimization"] = {
                "status": "available",
                "description": "أدوات تحسين الأداء",
                "level": "basic"
            }
            functionality["capabilities"].append("أدوات تحسين الأداء")
        
        # فئة VSCode
        if vscode_tools.get("optimizer_projects"):
            vscode_score = len(vscode_tools["optimizer_projects"])
            functionality["categories"]["vscode_optimization"] = {
                "status": "advanced",
                "description": "تحسين VSCode متقدم",
                "level": "advanced" if vscode_score >= 2 else "basic",
                "projects_count": vscode_score
            }
            functionality["capabilities"].extend([
                "تحسين أداء VSCode",
                "مراقبة العمليات",
                "إدارة الإضافات"
            ])
        
        # فئة الطوارئ
        if vscode_tools.get("emergency_tools", {}).get("exists"):
            functionality["categories"]["emergency_response"] = {
                "status": "available",
                "description": "أدوات الاستجابة للطوارئ",
                "level": "essential"
            }
            functionality["capabilities"].append("أدوات الطوارئ والإنقاذ")
        
        # فئة الوكلاء الذكيين
        if vscode_tools.get("agents", {}).get("exists"):
            agents_count = vscode_tools["agents"].get("agents_count", 0)
            functionality["categories"]["ai_agents"] = {
                "status": "advanced",
                "description": "وكلاء ذكيين للأتمتة",
                "level": "advanced" if agents_count >= 5 else "basic",
                "agents_count": agents_count
            }
            functionality["capabilities"].append("وكلاء ذكيين للأتمتة")
        
        # تحديد مستوى الأتمتة
        automation_indicators = [
            vscode_tools.get("agents", {}).get("exists", False),
            vscode_tools.get("emergency_tools", {}).get("exists", False),
            len(functionality["categories"]) >= 3
        ]
        
        if sum(automation_indicators) >= 3:
            functionality["automation_level"] = "high"
        elif sum(automation_indicators) >= 2:
            functionality["automation_level"] = "medium"
        else:
            functionality["automation_level"] = "low"
        
        # نقاط التكامل
        functionality["integration_points"] = [
            "نظام أنوبيس الرئيسي",
            "VSCode IDE",
            "نظام العمليات",
            "أدوات المراقبة"
        ]
        
        # الميزات المفقودة
        if not functionality["categories"].get("monitoring"):
            functionality["missing_features"].append("مراقبة شاملة للنظام")
        
        if not functionality["categories"].get("security"):
            functionality["missing_features"].append("أدوات الأمان والحماية")
        
        if functionality["automation_level"] != "high":
            functionality["missing_features"].append("أتمتة متقدمة")
        
        self.report["functionality_analysis"] = functionality
        return functionality
    
    def generate_recommendations(self):
        """إنشاء التوصيات للتحسين"""
        print("💡 إنشاء التوصيات...")
        
        recommendations = []
        optimization_opportunities = []
        
        # تحليل الحالة الحالية
        structure = self.report["components"].get("structure", {})
        vscode_tools = self.report["components"].get("vscode_tools", {})
        functionality = self.report.get("functionality_analysis", {})
        
        # توصيات بناءً على المشاكل
        total_issues = sum(len(comp.get("issues", [])) for comp in self.report["components"].values())
        
        if total_issues > 0:
            recommendations.append("🔧 إصلاح المشاكل المكتشفة في الهيكل")
        
        # توصيات بناءً على الوظائف
        if functionality.get("automation_level") != "high":
            recommendations.append("🤖 تحسين مستوى الأتمتة")
            optimization_opportunities.append("تطوير وكلاء ذكيين إضافيين")
        
        if not structure.get("main_directories", {}).get("docs", {}).get("exists"):
            recommendations.append("📚 إضافة توثيق شامل للأدوات")
        
        # توصيات للميزات المفقودة
        missing_features = functionality.get("missing_features", [])
        if missing_features:
            recommendations.append("➕ إضافة الميزات المفقودة")
            optimization_opportunities.extend(missing_features)
        
        # توصيات عامة
        recommendations.extend([
            "🧪 إضافة اختبارات للأدوات",
            "📊 تحسين المراقبة والتقارير",
            "🔄 أتمتة عمليات الصيانة",
            "🛡️ تعزيز الأمان",
            "📈 تحسين الأداء"
        ])
        
        optimization_opportunities.extend([
            "تطوير لوحة تحكم موحدة",
            "إضافة تكامل مع API خارجية",
            "تحسين واجهة المستخدم",
            "إضافة نظام إشعارات",
            "تطوير أدوات تشخيص متقدمة"
        ])
        
        self.report["recommendations"] = recommendations
        self.report["optimization_opportunities"] = optimization_opportunities
        
        return recommendations, optimization_opportunities
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة"""
        print("🏥 تقييم الحالة العامة...")
        
        # حساب النقاط
        structure_score = 0
        vscode_score = 0
        functionality_score = 0
        
        # نقاط الهيكل
        structure = self.report["components"].get("structure", {})
        main_dirs = structure.get("main_directories", {})
        existing_dirs = sum(1 for d in main_dirs.values() if d.get("exists", False))
        structure_score = (existing_dirs / 5) * 100 if existing_dirs else 0
        
        # نقاط أدوات VSCode
        vscode_tools = self.report["components"].get("vscode_tools", {})
        vscode_indicators = [
            vscode_tools.get("optimizer_projects"),
            vscode_tools.get("emergency_tools", {}).get("exists", False),
            vscode_tools.get("agents", {}).get("exists", False)
        ]
        vscode_score = (sum(bool(x) for x in vscode_indicators) / 3) * 100
        
        # نقاط الوظائف
        functionality = self.report.get("functionality_analysis", {})
        automation_level = functionality.get("automation_level", "low")
        categories_count = len(functionality.get("categories", {}))
        
        automation_scores = {"high": 30, "medium": 20, "low": 10}
        functionality_score = automation_scores.get(automation_level, 0) + (categories_count * 10)
        
        # النقاط الإجمالية
        total_score = (structure_score + vscode_score + functionality_score) / 3
        
        # تحديد الحالة
        if total_score >= 80:
            self.report["overall_health"] = "excellent"
            health_text = "ممتاز"
            emoji = "🟢"
        elif total_score >= 60:
            self.report["overall_health"] = "good"
            health_text = "جيد"
            emoji = "🟡"
        elif total_score >= 40:
            self.report["overall_health"] = "fair"
            health_text = "متوسط"
            emoji = "🟠"
        else:
            self.report["overall_health"] = "poor"
            health_text = "يحتاج تحسين"
            emoji = "🔴"
        
        print(f"\n{emoji} الحالة العامة: {health_text} ({total_score:.1f}/100)")
        
        return self.report["overall_health"]
    
    def run_inspection(self):
        """تشغيل الفحص الشامل"""
        print("🛠️ بدء فحص مجلد tools_and_utilities الشامل")
        print("=" * 60)
        
        # تشغيل جميع عمليات الفحص
        self.analyze_structure()
        self.analyze_vscode_tools()
        self.analyze_functionality()
        
        # التحليل والتوصيات
        self.generate_recommendations()
        self.evaluate_overall_health()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("📋 تقرير فحص tools_and_utilities المفصل")
        print("="*60)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "fair": "🟠",
            "poor": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']}")
        
        # تفاصيل الهيكل
        print(f"\n🏗️ تفاصيل الهيكل:")
        structure = self.report["components"].get("structure", {})
        for dir_name, dir_info in structure.get("main_directories", {}).items():
            status = dir_info.get("status", "❓ غير معروف")
            print(f"   📁 {dir_name}: {status}")
        
        # أدوات VSCode
        print(f"\n🔧 أدوات VSCode:")
        vscode_tools = self.report["components"].get("vscode_tools", {})
        
        for project_name, project_info in vscode_tools.get("optimizer_projects", {}).items():
            files_count = project_info.get("files_count", 0)
            scripts_count = project_info.get("scripts_count", 0)
            print(f"   🚀 {project_name}: {files_count} ملف، {scripts_count} سكريبت")
        
        if vscode_tools.get("emergency_tools", {}).get("exists"):
            tools_count = vscode_tools["emergency_tools"].get("files_count", 0)
            print(f"   🚨 أدوات الطوارئ: {tools_count} أداة")
        
        if vscode_tools.get("agents", {}).get("exists"):
            agents_count = vscode_tools["agents"].get("agents_count", 0)
            print(f"   🤖 الوكلاء الذكيين: {agents_count} وكيل")
        
        # تحليل الوظائف
        print(f"\n⚙️ تحليل الوظائف:")
        functionality = self.report.get("functionality_analysis", {})
        
        for category, details in functionality.get("categories", {}).items():
            level = details.get("level", "unknown")
            description = details.get("description", "")
            print(f"   📊 {category}: {level} - {description}")
        
        automation_level = functionality.get("automation_level", "unknown")
        print(f"   🤖 مستوى الأتمتة: {automation_level}")
        
        # القدرات
        capabilities = functionality.get("capabilities", [])
        if capabilities:
            print(f"\n✨ القدرات المتاحة:")
            for capability in capabilities:
                print(f"   ✅ {capability}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        for rec in self.report.get("recommendations", []):
            print(f"   {rec}")
        
        # فرص التحسين
        opportunities = self.report.get("optimization_opportunities", [])
        if opportunities:
            print(f"\n🚀 فرص التحسين:")
            for opp in opportunities:
                print(f"   📈 {opp}")
        
        print("\n" + "="*60)
        print("🛠️ انتهى فحص tools_and_utilities")
        print("="*60)
    
    def save_report(self):
        """حفظ التقرير في ملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"tools_utilities_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    inspector = ToolsUtilitiesInspector()
    
    # تشغيل الفحص
    report = inspector.run_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    return report

if __name__ == "__main__":
    main()
