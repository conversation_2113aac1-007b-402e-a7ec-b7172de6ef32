#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 وحدة الاتصال بقاعدة بيانات MySQL لنظام أنوبيس
Anubis AI Assistants System - MySQL Database Connector

هذه الوحدة تدير الاتصال مع قاعدة بيانات MySQL وتوفر واجهة برمجية سهلة الاستخدام
"""

import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Tuple

from mysql.connector import Error, pooling

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class MySQLConnector:
    """🔌 موصل قاعدة بيانات MySQL لنظام أنوبيس"""

    def __init__(self, config_path: str = "configs/database_config.json"):
        """
        تهيئة موصل قاعدة البيانات

        Args:
            config_path: مسار ملف إعدادات قاعدة البيانات
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.connection_pool = None
        self.logger = self._setup_logger()

        # إنشاء مجموعة الاتصالات
        self._create_connection_pool()

    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config["database"]["mysql"]
        except FileNotFoundError:
            raise FileNotFoundError(f"ملف الإعدادات غير موجود: {self.config_path}")
        except json.JSONDecodeError:
            raise ValueError(f"خطأ في تحليل ملف الإعدادات: {self.config_path}")

    def _setup_logger(self) -> logging.Logger:
        """إعداد نظام التسجيل"""
        logger = logging.getLogger("anubis_mysql")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _create_connection_pool(self):
        """إنشاء مجموعة اتصالات قاعدة البيانات"""
        try:
            pool_config = {
                "pool_name": "anubis_pool",
                "pool_size": self.config.get("pool_size", 10),
                "pool_reset_session": True,
                "host": self.config["host"],
                "port": self.config["port"],
                "user": self.config["user"],
                "password": self.config["password"],
                "database": self.config["database"],
                "charset": self.config.get("charset", "utf8mb4"),
                "autocommit": self.config.get("autocommit", True),
                "raise_on_warnings": True,
            }

            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            self.logger.info("✅ تم إنشاء مجموعة اتصالات قاعدة البيانات بنجاح")

        except Error as e:
            self.logger.error(f"❌ خطأ في إنشاء مجموعة الاتصالات: {e}")
            raise

    def get_connection(self):
        """الحصول على اتصال من مجموعة الاتصالات"""
        try:
            return self.connection_pool.get_connection()
        except Error as e:
            self.logger.error(f"❌ خطأ في الحصول على اتصال: {e}")
            raise

    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        تنفيذ استعلام SELECT وإرجاع النتائج

        Args:
            query: استعلام SQL
            params: معاملات الاستعلام

        Returns:
            قائمة بالنتائج
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            cursor.execute(query, params or ())
            results = cursor.fetchall()

            cursor.close()
            self.logger.info(f"✅ تم تنفيذ الاستعلام بنجاح: {len(results)} نتيجة")

            return results

        except Error as e:
            self.logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE

        Args:
            query: استعلام SQL
            params: معاملات الاستعلام

        Returns:
            عدد الصفوف المتأثرة
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount

            connection.commit()
            cursor.close()

            self.logger.info(f"✅ تم تنفيذ التحديث بنجاح: {affected_rows} صف متأثر")

            return affected_rows

        except Error as e:
            if connection:
                connection.rollback()
            self.logger.error(f"❌ خطأ في تنفيذ التحديث: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    def insert_project(self, name: str, path: str, project_type: str, description: str = "") -> int:
        """إدراج مشروع جديد"""
        query = """
        INSERT INTO projects (name, path, type, description)
        VALUES (%s, %s, %s, %s)
        """
        self.execute_update(query, (name, path, project_type, description))

        # الحصول على ID المشروع الجديد
        result = self.execute_query("SELECT LAST_INSERT_ID() as id")
        return result[0]["id"]

    def get_projects(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المشاريع"""
        query = "SELECT * FROM projects ORDER BY created_at DESC"
        return self.execute_query(query)

    def insert_analysis(
        self,
        project_id: int,
        agent_type: str,
        analysis_data: Dict,
        results: Dict,
        score: float,
    ) -> int:
        """إدراج تحليل جديد"""
        query = """
        INSERT INTO analyses (project_id, agent_type, analysis_data, results, score)
        VALUES (%s, %s, %s, %s, %s)
        """
        self.execute_update(
            query,
            (
                project_id,
                agent_type,
                json.dumps(analysis_data, ensure_ascii=False),
                json.dumps(results, ensure_ascii=False),
                score,
            ),
        )

        result = self.execute_query("SELECT LAST_INSERT_ID() as id")
        return result[0]["id"]

    def get_analyses(self, project_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """الحصول على التحليلات"""
        if project_id:
            query = "SELECT * FROM analyses WHERE project_id = %s ORDER BY created_at DESC"
            return self.execute_query(query, (project_id,))
        else:
            query = "SELECT * FROM analyses ORDER BY created_at DESC"
            return self.execute_query(query)

    def insert_error(
        self,
        project_id: int,
        file_path: str,
        line_number: int,
        error_type: str,
        severity: str,
        message: str,
    ) -> int:
        """إدراج خطأ جديد"""
        query = """
        INSERT INTO errors (project_id, file_path, line_number, error_type, severity, message)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        self.execute_update(
            query, (project_id, file_path, line_number, error_type, severity, message)
        )

        result = self.execute_query("SELECT LAST_INSERT_ID() as id")
        return result[0]["id"]

    def get_errors(
        self, project_id: Optional[int] = None, severity: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """الحصول على الأخطاء"""
        query = "SELECT * FROM errors WHERE 1=1"
        params = []

        if project_id:
            query += " AND project_id = %s"
            params.append(project_id)

        if severity:
            query += " AND severity = %s"
            params.append(severity)

        query += " ORDER BY created_at DESC"

        return self.execute_query(query, tuple(params) if params else None)

    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()

            self.logger.info("✅ اختبار الاتصال نجح")
            return True

        except Error as e:
            self.logger.error(f"❌ فشل اختبار الاتصال: {e}")
            return False

    def get_database_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        stats = {}

        tables = ["projects", "analyses", "errors", "reports", "plugins", "activities"]

        for table in tables:
            try:
                result = self.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                stats[table] = result[0]["count"]
            except Error:
                stats[table] = 0

        return stats

    def close_pool(self):
        """إغلاق مجموعة الاتصالات"""
        if self.connection_pool:
            # لا توجد طريقة مباشرة لإغلاق المجموعة في mysql-connector-python
            # سيتم إغلاقها تلقائياً عند انتهاء البرنامج
            self.logger.info("🔒 تم إغلاق مجموعة اتصالات قاعدة البيانات")


# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء موصل قاعدة البيانات
    db = MySQLConnector()

    # اختبار الاتصال
    if db.test_connection():
        print("🎉 الاتصال بقاعدة البيانات نجح!")

        # عرض إحصائيات قاعدة البيانات
        stats = db.get_database_stats()
        print("\n📊 إحصائيات قاعدة البيانات:")
        for table, count in stats.items():
            print(f"  {table}: {count}")

        # عرض المشاريع
        projects = db.get_projects()
        print(f"\n📁 المشاريع ({len(projects)}):")
        for project in projects[:3]:  # عرض أول 3 مشاريع
            print(f"  - {project['name']} ({project['type']})")

    else:
        print("❌ فشل الاتصال بقاعدة البيانات!")
