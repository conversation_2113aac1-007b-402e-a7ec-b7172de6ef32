{"🏺 النظام الأساسي": {"main": "C:\\Users\\<USER>\\main.py", "system": "C:\\Users\\<USER>\\anubis_main_system", "config": "C:\\Users\\<USER>\\configs"}, "🔒 النظام المعزول": {"root": "C:\\Users\\<USER>\\anubis_isolation_system", "api": "C:\\Users\\<USER>\\anubis_isolation_system\\api", "worker": "C:\\Users\\<USER>\\anubis_isolation_system\\worker", "monitor": "C:\\Users\\<USER>\\anubis_isolation_system\\monitor", "docker": "C:\\Users\\<USER>\\anubis_isolation_system\\docker-compose-isolation.yml"}, "🤖 فريق الذكاء الاصطناعي": {"root": "C:\\Users\\<USER>\\anubis_ai_team", "workflow": "C:\\Users\\<USER>\\anubis_ai_team\\team_workflow_manager.py", "collaboration": "C:\\Users\\<USER>\\anubis_ai_team\\anubis_ai_collaboration_helper.py", "gemini": "C:\\Users\\<USER>\\anubis_ai_team\\anubis_gemini_cli_helper.py"}, "📁 مجلدات البيانات": {"data": "C:\\Users\\<USER>\\data", "logs": "C:\\Users\\<USER>\\logs", "reports": "C:\\Users\\<USER>\\reports", "workspace": "C:\\Users\\<USER>\\workspace"}, "🌐 نقاط الخدمات": {"main_system": {"base_url": "http://localhost:8000", "health": "http://localhost:8000/health", "docs": "http://localhost:8000/docs"}, "isolation_api": {"base_url": "http://localhost:8080", "health": "http://localhost:8080/health", "docs": "http://localhost:8080/docs", "status": "http://localhost:8080/status"}, "isolation_monitor": {"base_url": "http://localhost:9090", "health": "http://localhost:9090/health", "services": "http://localhost:9090/monitor/services", "system": "http://localhost:9090/monitor/system"}}}