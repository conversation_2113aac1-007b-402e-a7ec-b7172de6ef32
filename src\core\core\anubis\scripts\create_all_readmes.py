#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤝 سكريبت التعاون مع Gemini CLI لإنشاء ملفات README
Collaboration Script with Gemini CLI for README Creation

يتعاون مع Gemini CLI لفحص جميع المجلدات وإنشاء ملفات README شاملة
"""

import json
import subprocess
from datetime import datetime
from pathlib import Path


class AnubisREADMEGenerator:
    """🤝 مولد ملفات README بالتعاون مع Gemini CLI"""

    def __init__(self):
        self.base_path = Path(".")
        self.folders_to_process = [
            "scripts",
            "docs",
            "configs",
            "reports",
            "logs",
            "backup",
            "temp",
            "examples",
            "tools",
        ]

        print("🤝 مولد ملفات README - نظام أنوبيس")
        print("=" * 50)
        print("🔗 التعاون مع Gemini CLI لفحص المجلدات")

    def check_gemini_availability(self):
        """فحص توفر Gemini CLI"""
        try:
            result = subprocess.run(
                ["gemini", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                print("✅ Gemini CLI متاح ويعمل")
                return True
            else:
                print("❌ Gemini CLI غير متاح")
                return False
        except Exception as e:
            print(f"❌ خطأ في فحص Gemini CLI: {e}")
            return False

    def analyze_folder_with_gemini(self, folder_name):
        """تحليل مجلد باستخدام Gemini CLI"""
        print(f"\n🔍 تحليل مجلد {folder_name} مع Gemini CLI...")

        # إنشاء prompt مفصل لـ Gemini
        prompt = f"""
أحتاج مساعدتك في تحليل مجلد '{folder_name}' في نظام أنوبيس للذكاء الاصطناعي.

المطلوب:
1. فحص محتويات المجلد وفهم الغرض من كل ملف
2. تحديد الوظيفة الرئيسية للمجلد
3. اقتراح محتوى README.md شامل يتضمن:
   - وصف المجلد ووظيفته
   - قائمة الملفات مع شرح كل ملف
   - كيفية الاستخدام مع أمثلة عملية
   - أفضل الممارسات
   - روابط ذات صلة

المجلد موجود في: {self.base_path / folder_name}

أريد محتوى README باللغة العربية والإنجليزية، منسق بـ Markdown، ومناسب لنظام أنوبيس.
"""

        try:
            # تشغيل Gemini CLI
            result = subprocess.run(
                ["gemini", "--prompt", prompt],
                capture_output=True,
                text=True,
                timeout=120,
            )

            if result.returncode == 0:
                print(f"   ✅ تم تحليل {folder_name} بنجاح")
                return result.stdout
            else:
                print(f"   ❌ فشل تحليل {folder_name}: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print(f"   ⏰ انتهت مهلة تحليل {folder_name}")
            return None
        except Exception as e:
            print(f"   ❌ خطأ في تحليل {folder_name}: {e}")
            return None

    def create_folder_readme(self, folder_name, gemini_analysis):
        """إنشاء ملف README للمجلد"""
        folder_path = self.base_path / folder_name
        readme_path = folder_path / "README.md"

        # إنشاء المجلد إذا لم يكن موجوداً
        folder_path.mkdir(exist_ok=True)

        # محتوى README أساسي إذا فشل Gemini
        if not gemini_analysis:
            gemini_analysis = self.create_fallback_readme(folder_name)

        # إضافة header موحد
        header = f"""# 📁 مجلد {folder_name} - نظام أنوبيس
## {folder_name.title()} Directory - Anubis System

**آخر تحديث**: {datetime.now().strftime('%Y-%m-%d')}
**الحالة**: ✅ محسن بالتعاون مع Gemini CLI

---

"""

        # دمج المحتوى
        full_content = header + gemini_analysis

        # إضافة footer موحد
        footer = f"""

---

<div align="center">

**📁 مجلد {folder_name} - نظام أنوبيس**

**جزء من نظام الذكاء الاصطناعي المتقدم**

[![Anubis](https://img.shields.io/badge/Anubis-AI%20System-blue.svg)](../README.md)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)](README.md)

**محسن بالتعاون مع Gemini CLI**

</div>"""

        full_content += footer

        # حفظ الملف
        try:
            with open(readme_path, "w", encoding="utf-8") as f:
                f.write(full_content)
            print(f"   ✅ تم إنشاء README لمجلد {folder_name}")
            return True
        except Exception as e:
            print(f"   ❌ فشل إنشاء README لمجلد {folder_name}: {e}")
            return False

    def create_fallback_readme(self, folder_name):
        """إنشاء محتوى README احتياطي"""
        folder_path = self.base_path / folder_name

        # فحص الملفات الموجودة
        files = []
        if folder_path.exists():
            files = [f.name for f in folder_path.iterdir() if f.is_file()]

        content = f"""## 📜 الوصف

مجلد `{folder_name}` هو جزء من نظام أنوبيس للذكاء الاصطناعي.

## 📁 المحتويات

### الملفات الموجودة:
"""

        if files:
            for file in files:
                content += f"- `{file}`\n"
        else:
            content += "- لا توجد ملفات حالياً\n"

        content += f"""
## 🚀 الاستخدام

```bash
# الوصول إلى المجلد
cd {folder_name}/

# عرض المحتويات
ls -la
```

## 📝 ملاحظات

هذا المجلد جزء من نظام أنوبيس المتكامل للذكاء الاصطناعي.
"""

        return content

    def scan_existing_folders(self):
        """فحص المجلدات الموجودة"""
        print("\n📂 فحص المجلدات الموجودة...")

        existing_folders = []
        for folder in self.folders_to_process:
            folder_path = self.base_path / folder
            if folder_path.exists() and folder_path.is_dir():
                file_count = len(list(folder_path.iterdir()))
                existing_folders.append((folder, file_count))
                print(f"   ✅ {folder}: {file_count} عنصر")
            else:
                print(f"   ❌ {folder}: غير موجود")

        return existing_folders

    def generate_all_readmes(self):
        """إنشاء جميع ملفات README"""
        print("\n🚀 بدء إنشاء ملفات README...")

        # فحص Gemini CLI
        gemini_available = self.check_gemini_availability()

        # فحص المجلدات الموجودة
        existing_folders = self.scan_existing_folders()

        results = {
            "successful": [],
            "failed": [],
            "total_folders": len(existing_folders),
            "gemini_used": gemini_available,
        }

        # معالجة كل مجلد
        for folder_name, file_count in existing_folders:
            print(f"\n📁 معالجة مجلد {folder_name}...")

            # تحليل مع Gemini إذا كان متاحاً
            gemini_analysis = None
            if gemini_available:
                gemini_analysis = self.analyze_folder_with_gemini(folder_name)

            # إنشاء README
            success = self.create_folder_readme(folder_name, gemini_analysis)

            if success:
                results["successful"].append(folder_name)
            else:
                results["failed"].append(folder_name)

        return results

    def create_summary_report(self, results):
        """إنشاء تقرير ملخص"""
        print("\n📊 إنشاء تقرير الملخص...")

        report = {
            "timestamp": datetime.now().isoformat(),
            "results": results,
            "summary": {
                "total_processed": results["total_folders"],
                "successful": len(results["successful"]),
                "failed": len(results["failed"]),
                "success_rate": (
                    len(results["successful"]) / results["total_folders"] * 100
                    if results["total_folders"] > 0
                    else 0
                ),
            },
        }

        # حفظ التقرير
        report_path = self.base_path / "readme_generation_report.json"
        try:
            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"   ✅ تم حفظ التقرير: {report_path}")
        except Exception as e:
            print(f"   ❌ فشل حفظ التقرير: {e}")

        return report

    def print_final_summary(self, results):
        """طباعة الملخص النهائي"""
        print("\n" + "=" * 50)
        print("🏆 ملخص إنشاء ملفات README")
        print("=" * 50)

        print(f"📊 إجمالي المجلدات: {results['total_folders']}")
        print(f"✅ نجح: {len(results['successful'])}")
        print(f"❌ فشل: {len(results['failed'])}")

        if results["successful"]:
            print(f"\n✅ المجلدات الناجحة:")
            for folder in results["successful"]:
                print(f"   - {folder}")

        if results["failed"]:
            print(f"\n❌ المجلدات الفاشلة:")
            for folder in results["failed"]:
                print(f"   - {folder}")

        success_rate = (
            len(results["successful"]) / results["total_folders"] * 100
            if results["total_folders"] > 0
            else 0
        )
        print(f"\n📈 معدل النجاح: {success_rate:.1f}%")

        if results["gemini_used"]:
            print("🤝 تم استخدام Gemini CLI للتحليل")
        else:
            print("⚠️ تم استخدام المحتوى الاحتياطي (Gemini غير متاح)")

        print("\n🏺 إنشاء ملفات README لنظام أنوبيس مكتمل!")


def main():
    """الدالة الرئيسية"""
    generator = AnubisREADMEGenerator()

    # إنشاء جميع ملفات README
    results = generator.generate_all_readmes()

    # إنشاء تقرير
    report = generator.create_summary_report(results)

    # طباعة الملخص
    generator.print_final_summary(results)

    return results


if __name__ == "__main__":
    main()
