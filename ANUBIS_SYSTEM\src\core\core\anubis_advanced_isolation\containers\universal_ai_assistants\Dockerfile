# Dockerfile for Universal-AI-Assistants
# Generated by Anubis Advanced Isolation System
FROM alpine:latest

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache bash

# Copy application code
COPY . .

# Create non-root user
RUN adduser -D -s /bin/bash universauser
RUN chown -R universauser:users /app
USER universauser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "main.py"]
