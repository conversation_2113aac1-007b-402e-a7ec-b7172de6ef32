{"timestamp": "2025-07-20T06:18:40.445137", "tester": "Anubis Simple System Tester", "test_type": "basic_system_functionality_test", "overall_status": "excellent", "test_categories": {"infrastructure": {"docker_availability": true, "docker_compose_availability": true, "python_version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "network_connectivity": true, "disk_space": {"total_gb": 352.12, "used_gb": 334.47, "free_gb": 17.65, "usage_percent": 94.99}}, "file_structure": {"system_directories": {"anubis_main_system": true, "universal_ai_system": true, "workflows_and_automation": true, "workspace": true, "database": true, "configs": true, "isolation_systems": true, "tools_and_utilities": true, "archive_and_backups": true, "documentation": true, "scripts": true, "reports": true, "logs": true, "isolation_configs": true, "utilities": true}, "required_files": {"docker-compose.yml": true, "Dockerfile": true, "README.md": true, "start_anubis_isolated.sh": true, ".env.template": true}, "script_files": {"anubis_main_system/start_isolated_main_system.sh": false, "universal_ai_system/start_isolated_ai_system.sh": true, "workflows_and_automation/start_isolated_workflows.sh": true, "workspace/start_isolated_workspace.sh": true, "tools_and_utilities/start_isolated_tools.sh": true}, "config_files": {}}, "core_services": {"main_system": {"available": false, "error": "Connection refused", "url": "http://localhost:8080"}, "ai_system": {"available": false, "error": "Connection refused", "url": "http://localhost:8090"}, "workflows": {"available": false, "error": "Connection refused", "url": "http://localhost:5678"}, "workspace": {"available": false, "error": "Connection refused", "url": "http://localhost:8888"}, "monitoring": {"available": false, "error": "Connection refused", "url": "http://localhost:9090"}}, "ai_components": {"ollama_server": {"available": true, "status_code": 200, "response_time": 2.087, "url": "http://localhost:11434"}, "chroma_db": {"available": false, "error": "Connection refused", "url": "http://localhost:8000"}, "ai_configs": {"configs/ai_config.json": true, "universal_ai_system/configs": true}, "model_directories": {"universal_ai_system/configs": true, "universal_ai_system/src": true, "configs": true}}, "workflow_components": {"n8n_interface": {"available": false, "error": "Connection refused", "url": "http://localhost:5678"}, "custom_nodes": {"total": 3, "nodes": ["AnubisAgents.node.ts", "AnubisGemini.node.ts", "AnubisOllama.node.ts"]}, "workflow_files": {}, "workflow_configs": {"workflows_and_automation/docker-compose.yml": true, "workflows_and_automation/configs": true}}, "database_components": {"sqlite_databases": {"database/anubis.db": {"exists": true, "size_mb": 0.0}, "anubis_main_system/database/anubis.db": {"exists": true, "size_mb": 0.0}}, "database_configs": {"configs/database_config.json": true, "database": true}, "database_services": {"postgresql": {"available": false, "error": "Connection refused", "url": "http://localhost:5432"}, "redis": {"available": false, "error": "Connection refused", "url": "http://localhost:6379"}, "chroma_db": {"available": false, "error": "Connection refused", "url": "http://localhost:8000"}}}, "security_components": {"isolation_configs": {}, "security_directories": {"isolation_configs/security": {"exists": true, "files": 3}, "universal_ai_system/security": {"exists": true, "files": 1}, "workflows_and_automation/security": {"exists": true, "files": 2}, "workspace/security": {"exists": true, "files": 2}}, "environment_files": {".env": true, ".env.template": true}, "docker_security": {"docker-compose.yml": true, "Dockerfile": true}}}, "service_endpoints": {"main_system": {"available": false, "error": "Connection refused", "url": "http://localhost:8080"}, "ai_system": {"available": false, "error": "Connection refused", "url": "http://localhost:8090"}, "workflows": {"available": false, "error": "Connection refused", "url": "http://localhost:5678"}, "workspace": {"available": false, "error": "Connection refused", "url": "http://localhost:8888"}, "monitoring": {"available": false, "error": "Connection refused", "url": "http://localhost:9090"}}, "performance_metrics": {"disk_usage": {"total_gb": 352.12, "used_gb": 334.47, "free_gb": 17.65, "usage_percent": 94.99}, "response_times": {}, "file_counts": {"scripts": 16, "reports": 15, "logs": 8, "configs": 4}, "system_info": {}}, "security_tests": {}, "failures": ["لا توجد خدمات متاحة"], "recommendations": ["🚀 تشغيل الخدمات: main_system, ai_system, workflows, workspace, monitoring", "💾 تنظيف القرص - الاستخدام أكثر من 85%", "🔧 تشغيل الخدمات الأساسية باستخدام سكريبتات التشغيل", "📊 مراجعة سجلات النظام للأخطاء", "🔒 تفعيل أنظمة الأمان والعزل", "🤖 اختبار نماذج الذكاء الاصطناعي", "📚 مراجعة التوثيق والأدلة"], "test_summary": {"total_tests": 5, "passed_tests": 4, "failed_tests": 1, "success_rate": 80.0, "status": "مم<PERSON><PERSON><PERSON>", "emoji": "🟢", "available_services": 0, "total_services": 5, "existing_dirs": 15, "total_dirs": 15}}