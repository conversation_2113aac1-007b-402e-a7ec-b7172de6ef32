# 🧠 مجلد Core - نواة نظام أنوبيس
## Core Module - Anubis System Heart

**آخر تحديث**: 2025-07-16
**الحالة**: ✅ مكتمل ومحسن بالتعاون مع Gemini CLI

---

## 📜 الوصف

يحتوي هذا المجلد على المكونات الأساسية والجوهرية لنظام أنوبيس للذكاء الاصطناعي. هذه الوحدات مسؤولة عن إدارة النظام، والتكامل مع نماذج الذكاء الاصطناعي، والتعامل مع الوكلاء، وإدارة التكوين، وتسجيل الأحداث.

## 📁 قائمة الملفات

| الملف | الوصف | الحالة |
|-------|--------|--------|
| `__init__.py` | ملف تهيئة يجعل المجلد حزمة Python | ✅ |
| `ai_integration.py` | تكامل مع مزودي AI (Ollama, Gemini, OpenAI) | ✅ |
| `assistant_system.py` | النظام الرئيسي لإدارة المساعدين والوكلاء | ✅ |
| `base_agent.py` | الفئة الأساسية المجردة لجميع الوكلاء | ✅ |
| `config_manager.py` | مدير إعدادات وتكوينات النظام | ✅ |
| `langsmith_wrapper.py` | تكامل مع LangSmith للتتبع والمراقبة | ✅ |
| `logger.py` | نظام تسجيل متقدم للأحداث والأخطاء | ✅ |
| `database_manager.py` | مدير قاعدة البيانات المتقدم | ✅ |

---

## 🚀 كيفية الاستخدام

### **1. تهيئة النظام:**
```python
from core.assistant_system import UniversalAssistantSystem

# تهيئة النظام لمشروع معين
project_path = "/path/to/your/project"
system = UniversalAssistantSystem(project_path)
```

### **2. تحليل المشروع:**
```python
# إجراء تحليل شامل
analysis_results = system.analyze_project()
print(analysis_results)
```

### **3. إدارة التكوين:**
```python
from core.config_manager import ConfigManager

config = ConfigManager()
agent_config = config.get_agent_config("database_agent")
```

---

## ⚙️ أمثلة عملية

### **مثال 1: تشغيل وكيل محدد**
```python
from core.assistant_system import UniversalAssistantSystem

system = UniversalAssistantSystem("/path/to/your/project")

# تشغيل وكيل تحليل قاعدة البيانات
db_analysis = system.run_agent("database_agent")
print(db_analysis)
```

### **مثال 2: استخدام الذكاء الاصطناعي**
```python
from core.ai_integration import OllamaProvider

# إنشاء موفر Ollama
provider = OllamaProvider("llama3:8b")

# توليد استجابة
prompt = "اكتب دالة Python بسيطة لجمع رقمين"
response = provider.generate_response(prompt)
print(response)
```

### **مثال 3: تسجيل الأحداث**
```python
from core.logger import SystemLogger

logger = SystemLogger()
logger.log_info("تمت معالجة البيانات بنجاح", agent="DataProcessor")
```

### **مثال 4: تتبع العمليات مع LangSmith**
```python
from core.langsmith_wrapper import langsmith_wrapper

# تتبع عملية وكيل
with langsmith_wrapper.trace_agent_operation("error_detector", "scan_file"):
    # تنفيذ العملية
    result = detector.scan_file("file.py")
```

---

## 🔧 المكونات التفصيلية

### **🤖 ai_integration.py**
- **OllamaProvider**: تكامل مع نماذج Ollama المحلية
- **GeminiProvider**: تكامل مع Google Gemini
- **OpenAIProvider**: تكامل مع OpenAI GPT
- **AIManager**: مدير موحد لجميع مزودي AI

### **🏗️ assistant_system.py**
- **UniversalAssistantSystem**: النظام الرئيسي
- **تحليل المشاريع**: كشف نوع المشروع تلقائياً
- **إدارة الوكلاء**: تحميل وتشغيل الوكلاء
- **تجميع النتائج**: دمج نتائج الوكلاء المختلفة

### **👤 base_agent.py**
- **BaseAgent**: الفئة الأساسية لجميع الوكلاء
- **واجهة موحدة**: تحديد الطرق المطلوبة
- **تسجيل الأحداث**: نظام تسجيل مدمج
- **إدارة التكوين**: تحميل إعدادات الوكيل

### **⚙️ config_manager.py**
- **ConfigManager**: مدير التكوين الرئيسي
- **تحميل الإعدادات**: من ملفات JSON
- **إعدادات الوكلاء**: تكوين كل وكيل
- **إعدادات النظام**: تكوين عام للنظام

### **📊 langsmith_wrapper.py**
- **AnubisLangSmithWrapper**: تكامل مع LangSmith
- **تتبع العمليات**: مراقبة أداء الوكلاء
- **تسجيل الأداء**: قياس أوقات الاستجابة
- **تجميع البيانات**: إحصائيات شاملة

### **📝 logger.py**
- **SystemLogger**: نظام تسجيل متقدم
- **ملفات منفصلة**: system.log, agents.log, errors.log
- **مستويات متعددة**: INFO, WARNING, ERROR, DEBUG
- **تنسيق موحد**: طوابع زمنية ومعلومات مفصلة

---

## 🔗 التكامل مع المكونات الأخرى

### **مع الوكلاء:**
```python
# جميع الوكلاء ترث من BaseAgent
from core.base_agent import BaseAgent

class MyCustomAgent(BaseAgent):
    def run_analysis(self):
        # تنفيذ التحليل
        pass
```

### **مع قاعدة البيانات:**
```python
from core.database_manager import DatabaseManager

db = DatabaseManager()
db.save_analysis_result(project_id, results)
```

### **مع LangSmith:**
```python
from core.langsmith_wrapper import langsmith_wrapper

# تتبع تلقائي لجميع العمليات
@langsmith_wrapper.trace_agent_operation
def my_analysis_function():
    # العملية ستُتتبع تلقائياً
    pass
```

---

<div align="center">

**🧠 نواة نظام أنوبيس**

**المكونات الأساسية للذكاء الاصطناعي المتقدم**

[![Core](https://img.shields.io/badge/Core-8%20Modules-blue.svg)](README.md)
[![Status](https://img.shields.io/badge/Status-Complete-brightgreen.svg)](README.md)
[![Integration](https://img.shields.io/badge/AI%20Integration-3%20Providers-gold.svg)](README.md)

**محسن بالتعاون مع Gemini CLI**

</div>
