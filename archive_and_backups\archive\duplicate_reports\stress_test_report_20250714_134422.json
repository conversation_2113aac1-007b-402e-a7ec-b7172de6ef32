{"timestamp": "2025-07-14T13:44:22.313241", "total_duration": 0.7190902233123779, "test_results": [{"test": "insert_projects", "count": 100, "successful": 100, "failed": 0, "duration": 0.2962956428527832, "rate": 337.5007443146431}, {"test": "concurrent_read", "thread_count": 10, "queries_per_thread": 50, "total_queries": 500, "successful_queries": 500, "failed_queries": 0, "duration": 0.32195544242858887, "queries_per_second": 1553.0099327670232, "avg_query_time": 0.00287320613861084}, {"test": "bulk_insert", "batch_size": 1000, "duration": 0.059688568115234375, "rate": 16753.626893334185}], "errors_count": 0, "errors": [], "cleanup": {"deleted_records": 1100}, "database_config": {"host": "localhost", "port": 3306, "database": "anubis_system"}}