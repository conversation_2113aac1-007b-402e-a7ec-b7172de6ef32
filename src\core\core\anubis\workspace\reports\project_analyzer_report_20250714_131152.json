{"project_info": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants", "name": "Universal-AI-Assistants", "type": "streamlit", "structure": {"project_exists": true, "is_directory": true, "files_count": 135, "directories_count": 30, "python_files": ["ask_anubis.py", "main.py", "test_anubis_system.py", "agents\\database_agent.py", "agents\\error_detector_agent.py", "agents\\file_organizer_agent.py", "agents\\memory_agent.py", "agents\\project_analyzer_agent.py", "agents\\__init__.py", "core\\assistant_system.py", "core\\base_agent.py", "core\\config_manager.py", "core\\logger.py", "core\\__init__.py", "database\\anubis_database.py", "database\\database_manager.py", "plugins\\base_plugin.py", "plugins\\example_plugin.py", "plugins\\plugin_manager.py", "plugins\\__init__.py", "scripts\\quick_start.py", "scripts\\__init__.py", "tests\\run_all_tests.py", "tests\\test_agents.py", "tests\\test_error_detector.py", "tests\\test_jewelry_database.py", "tests\\test_jewelry_logic.py", "tests\\test_plugins.py", "tests\\test_project_analyzer.py", "tests\\test_system.py", "tests\\__init__.py", "templates\\streamlit_template\\main.py"], "config_files": ["configs\\database_config.json", "configs\\default_config.json", "workspace\\knowledge_base\\knowledge_base.json", "workspace\\reports\\assistant_report_20250712_150556.json", "workspace\\reports\\assistant_report_20250712_150648.json", "workspace\\reports\\assistant_report_20250712_150742.json", "workspace\\reports\\assistant_report_20250712_150924.json", "workspace\\reports\\assistant_report_20250712_151038.json", "workspace\\reports\\assistant_report_20250712_151119.json", "workspace\\reports\\assistant_report_20250712_151309.json", "workspace\\reports\\assistant_report_20250712_152601.json", "workspace\\reports\\assistant_report_20250712_153602.json", "workspace\\reports\\assistant_report_20250712_154134.json", "workspace\\reports\\error_detector_report_20250714_123543.json", "workspace\\reports\\error_detector_report_20250714_130858.json", "workspace\\reports\\error_detector_report_20250714_131152.json", "workspace\\reports\\project_analyzer_report_20250714_123543.json", "workspace\\reports\\project_analyzer_report_20250714_123544.json", "workspace\\reports\\project_analyzer_report_20250714_130859.json", "workspace\\shared_memory\\session_20250712.json", "workspace\\shared_memory\\session_20250714.json", "tests\\configs\\default_config.json", "tests\\workspace\\knowledge_base\\knowledge_base.json", "tests\\workspace\\reports\\error_detector_report_20250714_124719.json", "tests\\workspace\\reports\\error_detector_report_20250714_124920.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124719.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124720.json", "tests\\workspace\\reports\\project_analyzer_report_20250714_124920.json", "tests\\workspace\\shared_memory\\session_20250714.json"], "data_files": ["project_db.db", "database\\anubis.db", "workspace\\backups\\anubis_backup_20250714_130707.db", "workspace\\backups\\anubis_backup_20250714_130746.db", "workspace\\backups\\anubis_backup_20250714_130858.db"]}, "agent_type": "project_analyzer", "analysis_time": "2025-07-14T13:11:52.254600"}, "project_size_analysis": {"total_files": 135, "total_lines": 8427, "code_lines": 6286, "comment_lines": 582, "blank_lines": 1559, "file_types": {".py": 32, ".db": 5, ".md": 13, ".txt": 5, ".json": 29, ".pyc": 19, ".log": 32}, "largest_files": [{"file": "agents\\project_analyzer_agent.py", "lines": 571}, {"file": "agents\\file_organizer_agent.py", "lines": 465}, {"file": "agents\\memory_agent.py", "lines": 455}, {"file": "agents\\database_agent.py", "lines": 437}, {"file": "agents\\error_detector_agent.py", "lines": 434}, {"file": "tests\\test_project_analyzer.py", "lines": 414}, {"file": "core\\assistant_system.py", "lines": 391}, {"file": "database\\anubis_database.py", "lines": 383}, {"file": "tests\\test_plugins.py", "lines": 381}, {"file": "core\\config_manager.py", "lines": 327}], "directory_structure": {}}, "complexity_analysis": {"cyclomatic_complexity": 0, "cognitive_complexity": 0, "function_complexity": [], "class_complexity": [], "module_complexity": [{"file": "ask_anubis.py", "complexity": 58}, {"file": "main.py", "complexity": 25}, {"file": "test_anubis_system.py", "complexity": 28}, {"file": "agents\\database_agent.py", "complexity": 90}, {"file": "agents\\error_detector_agent.py", "complexity": 72}, {"file": "agents\\file_organizer_agent.py", "complexity": 85}, {"file": "agents\\memory_agent.py", "complexity": 67}, {"file": "agents\\project_analyzer_agent.py", "complexity": 129}, {"file": "agents\\__init__.py", "complexity": 14}, {"file": "core\\assistant_system.py", "complexity": 74}, {"file": "core\\base_agent.py", "complexity": 50}, {"file": "core\\config_manager.py", "complexity": 29}, {"file": "core\\logger.py", "complexity": 20}, {"file": "core\\__init__.py", "complexity": 3}, {"file": "database\\anubis_database.py", "complexity": 54}, {"file": "database\\database_manager.py", "complexity": 49}, {"file": "plugins\\base_plugin.py", "complexity": 11}, {"file": "plugins\\example_plugin.py", "complexity": 18}, {"file": "plugins\\plugin_manager.py", "complexity": 32}, {"file": "plugins\\__init__.py", "complexity": 3}, {"file": "scripts\\quick_start.py", "complexity": 5}, {"file": "scripts\\__init__.py", "complexity": 3}, {"file": "tests\\run_all_tests.py", "complexity": 46}, {"file": "tests\\test_agents.py", "complexity": 17}, {"file": "tests\\test_error_detector.py", "complexity": 24}, {"file": "tests\\test_jewelry_database.py", "complexity": 29}, {"file": "tests\\test_jewelry_logic.py", "complexity": 9}, {"file": "tests\\test_plugins.py", "complexity": 12}, {"file": "tests\\test_project_analyzer.py", "complexity": 23}, {"file": "tests\\test_system.py", "complexity": 53}, {"file": "tests\\__init__.py", "complexity": 3}, {"file": "templates\\streamlit_template\\main.py", "complexity": 8}], "complexity_distribution": {"low": 7, "medium": 5, "high": 20}, "average_complexity": 35.71875}, "dependency_analysis": {"total_dependencies": 27, "external_dependencies": ["pathlib2>=2.3.7", "typing-extensions>=4.0.0", "mysql-connector-python>=8.0.0  # لـ MySQL - for MySQL", "psycopg2-binary>=2.9.0          # لـ PostgreSQL - for PostgreSQL", "chardet>=5.0.0", "pathspec>=0.10.0", "pyyaml>=6.0", "python-dateutil>=2.8.0", "colorlog>=6.7.0", "black>=22.0.0      # تنسيق الكود - Code formatting", "flake8>=5.0.0      # فحص جودة الكود - Code quality checking", "pylint>=2.17.0     # تحليل الكود - Code analysis", "pytest>=7.0.0     # الاختبارات - Testing framework", "pytest-cov>=4.0.0 # تغطية الاختبارات - Test coverage", "streamlit>=1.28.0  # لمشاريع Streamlit - for Streamlit projects", "django>=4.0.0      # لمشاريع Django - for Django projects", "fastapi>=0.100.0   # لمشاريع FastAPI - for FastAPI projects", "flask>=2.3.0       # لمشاريع Flask - for Flask projects", "tqdm>=4.64.0        # شريط التقدم - Progress bars", "click>=8.0.0        # واجهة سطر الأوامر - CLI interface", "bandit>=1.7.0       # فحص الأمان - Security linting", "safety>=2.3.0       # فحص الثغرات - Vulnerability checking", "radon>=5.1.0        # قياس التعقيد - Complexity metrics", "pandas>=2.0.0       # معالجة البيانات - Data manipulation", "numpy>=1.24.0       # العمليات الرياضية - Numerical operations", "psutil>=5.9.0       # معلومات النظام - System information", "gitpython>=3.1.0    # التعامل مع Git - Git integration"], "internal_dependencies": ["", "datetime", "base_agent", "decimal", "jewelry_database_models", "unittest", "numpy", "re", "pandas", "streamlit", "plugins", "<PERSON><PERSON><PERSON><PERSON>", "pathlib", "mysql", "logger", "sys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traceback", "json", "jewelry_workshop_business_logic", "core", "anubis_database", "file_organizer_agent", "subprocess", "memory_agent", "config_manager", "time", "database", "typing", "sqlite3", "requests", "database_agent", "shutil", "logging", "agents", "importlib", "tempfile", "fnmatch", "database_manager", "psycopg2", "abc", "ast", "plotly", "os"], "dependency_tree": {}, "outdated_dependencies": [], "security_vulnerabilities": []}, "architecture_analysis": {"project_type": "streamlit", "directory_structure": {"depth": 3, "directories": ["agents", "configs", "core", "database", "docs", "plugins", "scripts", "templates", "tests", "workspace", "__pycache__", "agents\\__pycache__", "core\\__pycache__", "database\\__pycache__", "plugins\\__pycache__", "templates\\streamlit_template", "tests\\configs", "tests\\workspace", "workspace\\backups", "workspace\\collaboration_logs", "workspace\\knowledge_base", "workspace\\logs", "workspace\\reports", "workspace\\shared_memory", "tests\\workspace\\backups", "tests\\workspace\\collaboration_logs", "tests\\workspace\\knowledge_base", "tests\\workspace\\logs", "tests\\workspace\\reports", "tests\\workspace\\shared_memory"], "organization_score": 71.42857142857143}, "design_patterns": [], "architectural_style": "Monolithic", "modularity_score": 50, "coupling_analysis": {}, "cohesion_analysis": {}}, "performance_analysis": {"potential_bottlenecks": [{"file": "ask_anubis.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\database_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\error_detector_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\file_organizer_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\memory_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\project_analyzer_agent.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "agents\\project_analyzer_agent.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": "core\\assistant_system.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "core\\config_manager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "database\\database_manager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "plugins\\example_plugin.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "plugins\\plugin_manager.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\run_all_tests.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_error_detector.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_project_analyzer.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام time.sleep قد يؤثر على الاستجابة", "severity": "low"}, {"file": "tests\\test_system.py", "issue": "حلقات متعددة قد تؤثر على الأداء", "severity": "medium"}], "optimization_opportunities": [], "resource_usage_patterns": {}, "scalability_assessment": {}}, "security_analysis": {"security_issues": [{"file": "agents\\project_analyzer_agent.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "agents\\project_analyzer_agent.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": "tests\\test_error_detector.py", "issue": "كلمة مرور مكشوفة في الكود", "severity": "high"}, {"file": "tests\\test_plugins.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام eval() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "استخدام exec() خطر أمني", "severity": "high"}, {"file": "tests\\test_project_analyzer.py", "issue": "كلمة مرور مكشوفة في الكود", "severity": "high"}], "vulnerability_assessment": {}, "security_score": 0, "recommendations": []}, "maintainability_analysis": {"maintainability_index": 54.878787878787875, "documentation_coverage": 99.69696969696969, "test_coverage_estimate": 25.0, "code_duplication": 0, "technical_debt": []}, "recommendations": [{"category": "architecture", "priority": "high", "title": "تحسين الهيكل المعماري", "description": "فصل الاهتمامات وتطبيق أنماط التصميم", "action": "إعادة تنظيم الكود حسب المسؤوليات"}, {"category": "performance", "priority": "medium", "title": "تحسين الأداء", "description": "تحسين الخوارزميات وتقليل التعقيد", "action": "مراجعة الحلقات والاستعلامات"}, {"category": "security", "priority": "high", "title": "تعزيز الأمان", "description": "إضافة طبقات حماية وتشفير البيانات الحساسة", "action": "مراجعة نقاط الضعف الأمنية"}, {"category": "maintainability", "priority": "medium", "title": "تحسين قابلية الصيانة", "description": "إضافة توثيق واختبارات شاملة", "action": "كتابة docstrings واختبارات وحدة"}], "summary": {"overall_score": 26.46, "project_size": "135 ملف", "code_lines": 6286, "complexity_level": "عالي", "security_status": "يحتاج تحسين", "maintainability_level": "متوسط", "recommendations_count": 4, "analysis_time": "2025-07-14T13:11:52.438232"}}