# 📁 مجلد scripts - نظام أنوبيس
## Scripts Directory - Anubis System

**آخر تحديث**: 2025-07-16  
**الحالة**: ✅ محسن بالتعاون مع Gemini CLI  

---

## 📜 الوصف

مجلد `scripts` هو جزء من نظام أنوبيس للذكاء الاصطناعي.

## 📁 المحتويات

### الملفات الموجودة:
- `activate_real_langsmith.py`
- `agents_cleanup.py`
- `check_ollama.py`
- `complete_file_organizer.py`
- `fix_agents_with_gemini.py`
- `gemini_cli_helper.py`
- `gemini_integration_system.py`
- `langsmith_integration_demo.py`
- `organize_project_files.py`
- `README.md`
- `setup_langsmith.py`
- `simple_langsmith_test.py`
- `smart_workflow_demo.py`
- `test_langsmith_integration.py`
- `__init__.py`

## 🚀 الاستخدام

```bash
# الوصول إلى المجلد
cd scripts/

# عرض المحتويات
ls -la
```

## 📝 ملاحظات

هذا المجلد جزء من نظام أنوبيس المتكامل للذكاء الاصطناعي.


---

<div align="center">

**📁 مجلد scripts - نظام أنوبيس**

**جزء من نظام الذكاء الاصطناعي المتقدم**

[![Anubis](https://img.shields.io/badge/Anubis-AI%20System-blue.svg)](../README.md)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)](README.md)

**محسن بالتعاون مع Gemini CLI**

</div>