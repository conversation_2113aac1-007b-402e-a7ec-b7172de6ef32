#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 مثال عملي لدمج LangSmith مع نظام أنوبيس
LangSmith Integration Demo for Anubis System

مثال شامل لكيفية استخدام LangSmith لربط الوكلاء والنماذج
"""

import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List

# محاكاة LangSmith (في حالة عدم التثبيت)
try:
    from langsmith import Client

    LANGSMITH_AVAILABLE = True
except ImportError:
    print("⚠️ LangSmith غير مثبت - سيتم استخدام محاكاة")
    LANGSMITH_AVAILABLE = False

    class MockLangSmithClient:
        def __init__(self):
            self.traces = []

        def trace(self, name):
            return MockTrace(name, self)

        def log_trace(self, trace_data):
            self.traces.append(trace_data)
            print(f"📊 LangSmith Trace: {trace_data['name']} - {trace_data['status']}")

    class MockTrace:
        def __init__(self, name, client):
            self.name = name
            self.client = client
            self.start_time = time.time()

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            end_time = time.time()
            status = "success" if exc_type is None else "error"
            self.client.log_trace(
                {
                    "name": self.name,
                    "duration": end_time - self.start_time,
                    "status": status,
                    "timestamp": datetime.now().isoformat(),
                }
            )


# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "agents"))


class AnubisOrchestrator:
    """منسق مركزي للوكلاء باستخدام LangSmith"""

    def __init__(self):
        # إعداد LangSmith
        if LANGSMITH_AVAILABLE:
            self.langsmith_client = Client()
        else:
            self.langsmith_client = MockLangSmithClient()

        # تهيئة الوكلاء
        self.agents = {}
        self.models = ["llama3:8b", "mistral:7b", "phi3:mini"]
        self.model_performance = {}

        print("🔗 تم تهيئة منسق أنوبيس مع LangSmith")

    def register_agent(self, agent_name: str, agent_instance):
        """تسجيل وكيل في النظام"""
        with self.langsmith_client.trace(f"register_agent_{agent_name}"):
            self.agents[agent_name] = agent_instance
            print(f"✅ تم تسجيل الوكيل: {agent_name}")

    def select_optimal_model(self, task_type: str, complexity: str = "medium") -> str:
        """اختيار النموذج الأمثل للمهمة"""
        with self.langsmith_client.trace("model_selection"):
            if task_type == "quick_response":
                return "phi3:mini"
            elif complexity == "high":
                return "llama3:8b"
            elif task_type == "creative":
                return "mistral:7b"
            else:
                return "llama3:8b"  # افتراضي

    def coordinate_multi_agent_analysis(self, project_path: str) -> Dict[str, Any]:
        """تنسيق تحليل متعدد الوكلاء"""
        with self.langsmith_client.trace("multi_agent_analysis") as trace:
            results = {}

            # 1. تحليل المشروع الأساسي
            if "smart_analyzer" in self.agents:
                with self.langsmith_client.trace("code_analysis"):
                    analyzer = self.agents["smart_analyzer"]
                    code_analysis = analyzer.analyze_project_files(max_files=3)
                    results["code_analysis"] = code_analysis
                    print("✅ تم تحليل الكود")

            # 2. الحصول على توصيات ذكية
            if "smart_analyzer" in self.agents:
                with self.langsmith_client.trace("smart_recommendations"):
                    recommendations = self.agents["smart_analyzer"].get_smart_recommendations()
                    results["recommendations"] = recommendations
                    print(f"💡 تم الحصول على {len(recommendations)} توصية")

            # 3. تجميع النتائج
            with self.langsmith_client.trace("results_aggregation"):
                aggregated_results = self.aggregate_results(results)
                print("🔄 تم تجميع النتائج")

            return aggregated_results

    def aggregate_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """تجميع نتائج الوكلاء المختلفين"""
        aggregated = {
            "timestamp": datetime.now().isoformat(),
            "total_agents_used": len([k for k in results.keys() if results[k]]),
            "summary": {},
            "detailed_results": results,
        }

        # تلخيص النتائج
        if "code_analysis" in results:
            code_data = results["code_analysis"]
            aggregated["summary"]["files_analyzed"] = code_data.get("files_analyzed", 0)
            aggregated["summary"]["analysis_status"] = code_data.get("status", "unknown")

        if "recommendations" in results:
            aggregated["summary"]["recommendations_count"] = len(results["recommendations"])

        return aggregated

    def benchmark_models(
        self, test_prompt: str = "اكتب جملة عن الذكاء الاصطناعي"
    ) -> Dict[str, Any]:
        """مقارنة أداء النماذج المختلفة"""
        with self.langsmith_client.trace("model_benchmarking"):
            benchmark_results = {}

            for model in self.models:
                with self.langsmith_client.trace(f"benchmark_{model}"):
                    try:
                        from anubis.core.ai_integration import OllamaProvider

                        provider = OllamaProvider(model_name=model)

                        if provider.is_available():
                            start_time = time.time()
                            response = provider.generate_response(test_prompt)
                            end_time = time.time()

                            benchmark_results[model] = {
                                "response_time": round(end_time - start_time, 2),
                                "response_length": len(response),
                                "success": not response.startswith("خطأ"),
                                "performance_score": self.calculate_performance_score(
                                    end_time - start_time, len(response)
                                ),
                            }

                            print(f"📊 {model}: {benchmark_results[model]['response_time']}ث")
                        else:
                            benchmark_results[model] = {
                                "available": False,
                                "error": "النموذج غير متاح",
                            }

                    except Exception as e:
                        benchmark_results[model] = {"error": str(e), "success": False}

            # حفظ نتائج الأداء
            self.model_performance = benchmark_results
            return benchmark_results

    def calculate_performance_score(self, response_time: float, response_length: int) -> float:
        """حساب نقاط الأداء للنموذج"""
        # نقاط الأداء بناءً على السرعة وجودة الاستجابة
        speed_score = max(0, 10 - response_time)  # كلما أسرع كلما أفضل
        quality_score = min(10, response_length / 20)  # طول معقول للاستجابة

        return round((speed_score + quality_score) / 2, 2)

    def intelligent_task_routing(self, task_description: str, task_data: Any) -> Dict[str, Any]:
        """توجيه المهام بذكاء للوكلاء المناسبين"""
        with self.langsmith_client.trace("intelligent_routing"):
            # تحليل نوع المهمة
            task_type = self.analyze_task_type(task_description)

            # اختيار الوكيل المناسب
            selected_agent = self.select_best_agent(task_type)

            # اختيار النموذج الأمثل
            optimal_model = self.select_optimal_model(task_type)

            # تنفيذ المهمة
            with self.langsmith_client.trace(f"execute_task_{task_type}"):
                if selected_agent and selected_agent in self.agents:
                    result = self.execute_task_with_agent(
                        self.agents[selected_agent], task_data, optimal_model
                    )
                else:
                    result = {"error": "لا يوجد وكيل مناسب للمهمة"}

            return {
                "task_type": task_type,
                "selected_agent": selected_agent,
                "optimal_model": optimal_model,
                "result": result,
                "timestamp": datetime.now().isoformat(),
            }

    def analyze_task_type(self, description: str) -> str:
        """تحليل نوع المهمة"""
        description_lower = description.lower()

        if any(word in description_lower for word in ["كود", "برمجة", "تحليل"]):
            return "code_analysis"
        elif any(word in description_lower for word in ["خطأ", "مشكلة", "bug"]):
            return "error_detection"
        elif any(word in description_lower for word in ["تنظيم", "ملفات", "organize"]):
            return "file_organization"
        elif any(word in description_lower for word in ["ذاكرة", "حفظ", "memory"]):
            return "memory_management"
        else:
            return "general_analysis"

    def select_best_agent(self, task_type: str) -> str:
        """اختيار أفضل وكيل للمهمة"""
        agent_mapping = {
            "code_analysis": "smart_analyzer",
            "error_detection": "error_detector",
            "file_organization": "file_organizer",
            "memory_management": "memory_agent",
            "general_analysis": "smart_analyzer",
        }

        return agent_mapping.get(task_type, "smart_analyzer")

    def execute_task_with_agent(self, agent, task_data, model_name: str) -> Dict[str, Any]:
        """تنفيذ المهمة مع الوكيل المحدد"""
        try:
            # تحديث نموذج الوكيل إذا أمكن
            if hasattr(agent, "set_model"):
                agent.set_model(model_name)

            # تنفيذ المهمة
            if hasattr(agent, "run_analysis"):
                return agent.run_analysis()
            else:
                return {"status": "completed", "message": "تم تنفيذ المهمة الأساسية"}

        except Exception as e:
            return {"error": str(e), "status": "failed"}

    def get_system_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى شاملة للنظام"""
        with self.langsmith_client.trace("system_insights"):
            insights = {
                "timestamp": datetime.now().isoformat(),
                "registered_agents": list(self.agents.keys()),
                "available_models": self.models,
                "model_performance": self.model_performance,
                "langsmith_traces": len(getattr(self.langsmith_client, "traces", [])),
                "system_health": self.calculate_system_health(),
            }

            return insights

    def calculate_system_health(self) -> str:
        """حساب صحة النظام"""
        working_agents = len(self.agents)
        working_models = len(
            [m for m in self.model_performance.values() if m.get("success", False)]
        )

        if working_agents >= 3 and working_models >= 2:
            return "ممتاز"
        elif working_agents >= 1 and working_models >= 1:
            return "جيد"
        else:
            return "يحتاج تحسين"


def demo_langsmith_integration():
    """عرض توضيحي لدمج LangSmith"""
    print("🔗 عرض توضيحي لدمج LangSmith مع نظام أنوبيس")
    print("=" * 60)

    # إنشاء المنسق
    orchestrator = AnubisOrchestrator()

    # تسجيل الوكلاء المتاحين
    try:
        from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer

        smart_analyzer = SmartCodeAnalyzer(
            project_path=".", config={"langsmith_enabled": True}, verbose=False
        )
        orchestrator.register_agent("smart_analyzer", smart_analyzer)
    except Exception as e:
        print(f"⚠️ لم يتم تسجيل SmartCodeAnalyzer: {e}")

    # اختبار مقارنة النماذج
    print("\n📊 اختبار مقارنة أداء النماذج...")
    benchmark_results = orchestrator.benchmark_models()

    print("\n🏆 نتائج مقارنة النماذج:")
    for model, result in benchmark_results.items():
        if result.get("success"):
            print(f"   {model}: {result['response_time']}ث - نقاط: {result['performance_score']}")
        else:
            print(f"   {model}: ❌ غير متاح")

    # اختبار التنسيق متعدد الوكلاء
    print("\n🤝 اختبار التنسيق متعدد الوكلاء...")
    if orchestrator.agents:
        multi_agent_results = orchestrator.coordinate_multi_agent_analysis(".")
        print(f"✅ تم استخدام {multi_agent_results['total_agents_used']} وكيل")
        print(f"📊 الملفات المحللة: {multi_agent_results['summary'].get('files_analyzed', 0)}")
    else:
        print("⚠️ لا توجد وكلاء مسجلين للاختبار")

    # اختبار التوجيه الذكي
    print("\n🎯 اختبار التوجيه الذكي للمهام...")
    task_examples = ["تحليل كود Python", "كشف أخطاء في البرنامج", "تنظيم ملفات المشروع"]

    for task in task_examples:
        routing_result = orchestrator.intelligent_task_routing(task, {"test": True})
        print(
            f"   📋 '{task}' → {routing_result['selected_agent']} + {routing_result['optimal_model']}"
        )

    # عرض رؤى النظام
    print("\n📈 رؤى النظام:")
    insights = orchestrator.get_system_insights()
    print(f"   🤖 الوكلاء المسجلين: {len(insights['registered_agents'])}")
    print(f"   🔥 النماذج المتاحة: {len(insights['available_models'])}")
    print(f"   📊 تتبع LangSmith: {insights['langsmith_traces']} عملية")
    print(f"   💚 صحة النظام: {insights['system_health']}")

    return orchestrator


if __name__ == "__main__":
    orchestrator = demo_langsmith_integration()

    print("\n" + "=" * 60)
    print("🏆 انتهى العرض التوضيحي لدمج LangSmith!")
    print("\n💡 الخطوات التالية:")
    print("   1. تثبيت LangSmith: pip install langsmith")
    print("   2. إعداد API Key من https://smith.langchain.com/")
    print("   3. تطبيق التكامل على جميع الوكلاء")
    print("   4. تطوير لوحة تحكم للمراقبة")
    print("\n🏺 نظام أنوبيس + LangSmith = قوة خارقة! 🚀")
