#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار نظام أنوبيس الشامل
Anubis Comprehensive System Tester
"""

import os
import json
import subprocess
import time
import requests
from pathlib import Path
from datetime import datetime
import docker
import logging

class AnubisSystemTester:
    def __init__(self):
        self.base_path = Path(".")
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tester": "Anubis Comprehensive System Tester",
            "test_type": "full_system_integration_test",
            "overall_status": "in_progress",
            "test_categories": {},
            "service_endpoints": {},
            "performance_metrics": {},
            "security_tests": {},
            "isolation_tests": {},
            "ai_model_tests": {},
            "workflow_tests": {},
            "failures": [],
            "recommendations": []
        }
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('anubis_system_test.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Docker client
        try:
            self.docker_client = docker.from_env()
        except:
            self.docker_client = None
            self.logger.warning("Docker client not available")
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🧪 بدء اختبار نظام أنوبيس الشامل")
        print("=" * 60)
        
        # اختبار البنية التحتية
        self.test_infrastructure()
        
        # اختبار أنظمة العزل
        self.test_isolation_systems()
        
        # اختبار الخدمات الأساسية
        self.test_core_services()
        
        # اختبار نماذج الذكاء الاصطناعي
        self.test_ai_models()
        
        # اختبار سير العمل والأتمتة
        self.test_workflows()
        
        # اختبار قواعد البيانات
        self.test_databases()
        
        # اختبار الأمان
        self.test_security()
        
        # اختبار الأداء
        self.test_performance()
        
        # تقييم النتائج
        self.evaluate_results()
        
        return self.test_results
    
    def test_infrastructure(self):
        """اختبار البنية التحتية"""
        print("🏗️ اختبار البنية التحتية...")
        
        infrastructure_tests = {
            "docker_availability": False,
            "docker_compose_availability": False,
            "system_directories": {},
            "required_files": {},
            "network_connectivity": False
        }
        
        # اختبار Docker
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                infrastructure_tests["docker_availability"] = True
                self.logger.info("✅ Docker متاح")
            else:
                self.logger.error("❌ Docker غير متاح")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار Docker: {e}")
        
        # اختبار Docker Compose
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                infrastructure_tests["docker_compose_availability"] = True
                self.logger.info("✅ Docker Compose متاح")
            else:
                self.logger.error("❌ Docker Compose غير متاح")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار Docker Compose: {e}")
        
        # اختبار المجلدات المطلوبة
        required_dirs = [
            "anubis_main_system", "universal_ai_system", "workflows_and_automation",
            "workspace", "database", "configs", "isolation_systems", 
            "tools_and_utilities", "archive_and_backups"
        ]
        
        for dir_name in required_dirs:
            dir_path = self.base_path / dir_name
            infrastructure_tests["system_directories"][dir_name] = dir_path.exists()
            if dir_path.exists():
                self.logger.info(f"✅ مجلد {dir_name} موجود")
            else:
                self.logger.warning(f"⚠️ مجلد {dir_name} مفقود")
        
        # اختبار الملفات المطلوبة
        required_files = [
            "docker-compose.yml", "Dockerfile", "README.md",
            "start_anubis_isolated.sh"
        ]
        
        for file_name in required_files:
            file_path = self.base_path / file_name
            infrastructure_tests["required_files"][file_name] = file_path.exists()
            if file_path.exists():
                self.logger.info(f"✅ ملف {file_name} موجود")
            else:
                self.logger.warning(f"⚠️ ملف {file_name} مفقود")
        
        # اختبار الاتصال بالإنترنت
        try:
            response = requests.get("https://www.google.com", timeout=5)
            if response.status_code == 200:
                infrastructure_tests["network_connectivity"] = True
                self.logger.info("✅ الاتصال بالإنترنت متاح")
            else:
                self.logger.warning("⚠️ مشكلة في الاتصال بالإنترنت")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
        
        self.test_results["test_categories"]["infrastructure"] = infrastructure_tests
    
    def test_isolation_systems(self):
        """اختبار أنظمة العزل"""
        print("🐳 اختبار أنظمة العزل...")
        
        isolation_tests = {
            "docker_networks": {},
            "docker_volumes": {},
            "containers_running": {},
            "isolation_scripts": {},
            "security_configs": {}
        }
        
        if self.docker_client:
            try:
                # اختبار الشبكات
                networks = self.docker_client.networks.list()
                anubis_networks = [net for net in networks if 'anubis' in net.name.lower()]
                isolation_tests["docker_networks"]["total"] = len(anubis_networks)
                isolation_tests["docker_networks"]["networks"] = [net.name for net in anubis_networks]
                
                if len(anubis_networks) > 0:
                    self.logger.info(f"✅ تم العثور على {len(anubis_networks)} شبكة أنوبيس")
                else:
                    self.logger.warning("⚠️ لم يتم العثور على شبكات أنوبيس")
                
                # اختبار الأحجام
                volumes = self.docker_client.volumes.list()
                anubis_volumes = [vol for vol in volumes if 'anubis' in vol.name.lower()]
                isolation_tests["docker_volumes"]["total"] = len(anubis_volumes)
                isolation_tests["docker_volumes"]["volumes"] = [vol.name for vol in anubis_volumes]
                
                if len(anubis_volumes) > 0:
                    self.logger.info(f"✅ تم العثور على {len(anubis_volumes)} حجم أنوبيس")
                else:
                    self.logger.warning("⚠️ لم يتم العثور على أحجام أنوبيس")
                
                # اختبار الحاويات
                containers = self.docker_client.containers.list(all=True)
                anubis_containers = [cont for cont in containers if 'anubis' in cont.name.lower()]
                isolation_tests["containers_running"]["total"] = len(anubis_containers)
                isolation_tests["containers_running"]["running"] = len([c for c in anubis_containers if c.status == 'running'])
                isolation_tests["containers_running"]["containers"] = [
                    {"name": c.name, "status": c.status} for c in anubis_containers
                ]
                
                self.logger.info(f"✅ تم العثور على {len(anubis_containers)} حاوية أنوبيس")
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في اختبار Docker: {e}")
        
        # اختبار سكريبتات العزل
        isolation_scripts = [
            "start_anubis_isolated.sh",
            "anubis_main_system/start_isolated_main_system.sh",
            "universal_ai_system/start_isolated_ai_system.sh",
            "workflows_and_automation/start_isolated_workflows.sh",
            "workspace/start_isolated_workspace.sh",
            "tools_and_utilities/start_isolated_tools.sh"
        ]
        
        for script in isolation_scripts:
            script_path = self.base_path / script
            isolation_tests["isolation_scripts"][script] = script_path.exists()
            if script_path.exists():
                self.logger.info(f"✅ سكريبت {script} موجود")
            else:
                self.logger.warning(f"⚠️ سكريبت {script} مفقود")
        
        self.test_results["test_categories"]["isolation"] = isolation_tests
    
    def test_core_services(self):
        """اختبار الخدمات الأساسية"""
        print("🏠 اختبار الخدمات الأساسية...")
        
        core_services = {
            "main_system": self._test_service_endpoint("http://localhost:8080", "النظام الرئيسي"),
            "ai_system": self._test_service_endpoint("http://localhost:8090", "نظام الذكاء الاصطناعي"),
            "workflows": self._test_service_endpoint("http://localhost:5678", "سير العمل"),
            "workspace": self._test_service_endpoint("http://localhost:8888", "بيئة العمل"),
            "monitoring": self._test_service_endpoint("http://localhost:9090", "المراقبة")
        }
        
        self.test_results["test_categories"]["core_services"] = core_services
        self.test_results["service_endpoints"] = core_services
    
    def _test_service_endpoint(self, url, service_name):
        """اختبار نقطة خدمة"""
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                self.logger.info(f"✅ {service_name} متاح على {url}")
                return {
                    "available": True,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "url": url
                }
            else:
                self.logger.warning(f"⚠️ {service_name} يستجيب بكود {response.status_code}")
                return {
                    "available": False,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "url": url
                }
        except requests.exceptions.ConnectionError:
            self.logger.warning(f"⚠️ {service_name} غير متاح على {url}")
            return {
                "available": False,
                "error": "Connection refused",
                "url": url
            }
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار {service_name}: {e}")
            return {
                "available": False,
                "error": str(e),
                "url": url
            }
    
    def test_ai_models(self):
        """اختبار نماذج الذكاء الاصطناعي"""
        print("🤖 اختبار نماذج الذكاء الاصطناعي...")
        
        ai_tests = {
            "ollama_server": self._test_service_endpoint("http://localhost:11434", "خادم Ollama"),
            "chroma_db": self._test_service_endpoint("http://localhost:8000", "قاعدة البيانات المتجهة"),
            "model_files": {},
            "api_endpoints": {}
        }
        
        # اختبار ملفات النماذج
        model_paths = [
            "universal_ai_system/models",
            "universal_ai_system/configs"
        ]
        
        for path in model_paths:
            model_path = self.base_path / path
            ai_tests["model_files"][path] = model_path.exists()
            if model_path.exists():
                self.logger.info(f"✅ مجلد النماذج {path} موجود")
            else:
                self.logger.warning(f"⚠️ مجلد النماذج {path} مفقود")
        
        # اختبار واجهات برمجة التطبيقات
        api_endpoints = {
            "gemini": "http://localhost:8090/api/v1/models/gemini",
            "ollama": "http://localhost:8090/api/v1/models/ollama",
            "openai": "http://localhost:8090/api/v1/models/openai"
        }
        
        for model_name, endpoint in api_endpoints.items():
            ai_tests["api_endpoints"][model_name] = self._test_service_endpoint(endpoint, f"API {model_name}")
        
        self.test_results["ai_model_tests"] = ai_tests
    
    def test_workflows(self):
        """اختبار سير العمل والأتمتة"""
        print("🔄 اختبار سير العمل والأتمتة...")
        
        workflow_tests = {
            "n8n_interface": self._test_service_endpoint("http://localhost:5678", "واجهة n8n"),
            "custom_nodes": {},
            "workflow_files": {},
            "credentials": {}
        }
        
        # اختبار العقد المخصصة
        custom_nodes_path = self.base_path / "workflows_and_automation" / "n8n_1" / "nodes"
        if custom_nodes_path.exists():
            node_files = list(custom_nodes_path.glob("*.ts"))
            workflow_tests["custom_nodes"]["total"] = len(node_files)
            workflow_tests["custom_nodes"]["nodes"] = [f.name for f in node_files]
            self.logger.info(f"✅ تم العثور على {len(node_files)} عقدة مخصصة")
        else:
            workflow_tests["custom_nodes"]["total"] = 0
            self.logger.warning("⚠️ مجلد العقد المخصصة غير موجود")
        
        # اختبار ملفات سير العمل
        workflow_files_path = self.base_path / "workflows_and_automation" / "n8n_1" / "workflows"
        if workflow_files_path.exists():
            workflow_files = list(workflow_files_path.glob("*.json"))
            workflow_tests["workflow_files"]["total"] = len(workflow_files)
            workflow_tests["workflow_files"]["workflows"] = [f.name for f in workflow_files]
            self.logger.info(f"✅ تم العثور على {len(workflow_files)} سير عمل")
        else:
            workflow_tests["workflow_files"]["total"] = 0
            self.logger.warning("⚠️ مجلد سير العمل غير موجود")
        
        self.test_results["workflow_tests"] = workflow_tests
    
    def test_databases(self):
        """اختبار قواعد البيانات"""
        print("🗄️ اختبار قواعد البيانات...")
        
        database_tests = {
            "sqlite_db": {},
            "postgresql": self._test_service_endpoint("http://localhost:5432", "PostgreSQL"),
            "chroma_db": self._test_service_endpoint("http://localhost:8000", "ChromaDB"),
            "redis": self._test_service_endpoint("http://localhost:6379", "Redis")
        }
        
        # اختبار قاعدة بيانات SQLite
        sqlite_paths = [
            "database/anubis.db",
            "anubis_main_system/database/anubis.db"
        ]
        
        for db_path in sqlite_paths:
            db_file = self.base_path / db_path
            database_tests["sqlite_db"][db_path] = {
                "exists": db_file.exists(),
                "size": db_file.stat().st_size if db_file.exists() else 0
            }
            if db_file.exists():
                self.logger.info(f"✅ قاعدة البيانات {db_path} موجودة")
            else:
                self.logger.warning(f"⚠️ قاعدة البيانات {db_path} مفقودة")
        
        self.test_results["test_categories"]["databases"] = database_tests
    
    def test_security(self):
        """اختبار الأمان"""
        print("🛡️ اختبار الأمان...")
        
        security_tests = {
            "isolation_configs": {},
            "ssl_certificates": {},
            "environment_variables": {},
            "file_permissions": {}
        }
        
        # اختبار ملفات إعدادات الأمان
        security_config_paths = [
            "isolation_configs/security",
            "universal_ai_system/security",
            "workflows_and_automation/security",
            "workspace/security"
        ]
        
        for config_path in security_config_paths:
            security_path = self.base_path / config_path
            security_tests["isolation_configs"][config_path] = {
                "exists": security_path.exists(),
                "files": len(list(security_path.glob("*"))) if security_path.exists() else 0
            }
            if security_path.exists():
                self.logger.info(f"✅ إعدادات الأمان {config_path} موجودة")
            else:
                self.logger.warning(f"⚠️ إعدادات الأمان {config_path} مفقودة")
        
        # اختبار متغيرات البيئة
        env_files = [".env", ".env.template"]
        for env_file in env_files:
            env_path = self.base_path / env_file
            security_tests["environment_variables"][env_file] = env_path.exists()
            if env_path.exists():
                self.logger.info(f"✅ ملف البيئة {env_file} موجود")
            else:
                self.logger.warning(f"⚠️ ملف البيئة {env_file} مفقود")
        
        self.test_results["security_tests"] = security_tests
    
    def test_performance(self):
        """اختبار الأداء"""
        print("⚡ اختبار الأداء...")
        
        performance_metrics = {
            "disk_usage": {},
            "memory_available": {},
            "container_performance": {},
            "response_times": {}
        }
        
        # اختبار استخدام القرص
        try:
            total, used, free = shutil.disk_usage(self.base_path)
            performance_metrics["disk_usage"] = {
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "usage_percent": round((used / total) * 100, 2)
            }
            self.logger.info(f"💾 استخدام القرص: {performance_metrics['disk_usage']['usage_percent']}%")
        except Exception as e:
            self.logger.error(f"❌ خطأ في قياس استخدام القرص: {e}")
        
        # قياس أوقات الاستجابة
        if self.test_results.get("service_endpoints"):
            for service, endpoint_data in self.test_results["service_endpoints"].items():
                if endpoint_data.get("available") and "response_time" in endpoint_data:
                    performance_metrics["response_times"][service] = endpoint_data["response_time"]
        
        self.test_results["performance_metrics"] = performance_metrics
    
    def evaluate_results(self):
        """تقييم النتائج الإجمالية"""
        print("📊 تقييم النتائج...")
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        # تقييم اختبارات البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        if infrastructure.get("docker_availability"):
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("Docker غير متاح")
        total_tests += 1
        
        if infrastructure.get("docker_compose_availability"):
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("Docker Compose غير متاح")
        total_tests += 1
        
        # تقييم الخدمات الأساسية
        core_services = self.test_results["test_categories"].get("core_services", {})
        for service, data in core_services.items():
            total_tests += 1
            if data.get("available"):
                passed_tests += 1
            else:
                failed_tests += 1
                self.test_results["failures"].append(f"خدمة {service} غير متاحة")
        
        # حساب معدل النجاح
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
        else:
            success_rate = 0
        
        # تحديد الحالة الإجمالية
        if success_rate >= 80:
            self.test_results["overall_status"] = "excellent"
            status_text = "ممتاز"
            emoji = "🟢"
        elif success_rate >= 60:
            self.test_results["overall_status"] = "good"
            status_text = "جيد"
            emoji = "🟡"
        elif success_rate >= 40:
            self.test_results["overall_status"] = "fair"
            status_text = "متوسط"
            emoji = "🟠"
        else:
            self.test_results["overall_status"] = "poor"
            status_text = "يحتاج تحسين"
            emoji = "🔴"
        
        self.test_results["test_summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": round(success_rate, 2),
            "status": status_text,
            "emoji": emoji
        }
        
        # توصيات التحسين
        self._generate_recommendations()
        
        print(f"\n{emoji} النتيجة الإجمالية: {status_text} ({success_rate:.1f}%)")
        print(f"✅ نجح: {passed_tests} اختبار")
        print(f"❌ فشل: {failed_tests} اختبار")
    
    def _generate_recommendations(self):
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        # توصيات البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        if not infrastructure.get("docker_availability"):
            recommendations.append("🐳 تثبيت Docker لتشغيل الحاويات")
        if not infrastructure.get("docker_compose_availability"):
            recommendations.append("🐳 تثبيت Docker Compose لإدارة الخدمات")
        
        # توصيات الخدمات
        core_services = self.test_results["test_categories"].get("core_services", {})
        unavailable_services = [name for name, data in core_services.items() if not data.get("available")]
        if unavailable_services:
            recommendations.append(f"🚀 تشغيل الخدمات المتوقفة: {', '.join(unavailable_services)}")
        
        # توصيات الأداء
        performance = self.test_results.get("performance_metrics", {})
        disk_usage = performance.get("disk_usage", {})
        if disk_usage.get("usage_percent", 0) > 80:
            recommendations.append("💾 تنظيف القرص - الاستخدام أكثر من 80%")
        
        # توصيات الأمان
        security = self.test_results.get("security_tests", {})
        missing_security = [path for path, data in security.get("isolation_configs", {}).items() if not data.get("exists")]
        if missing_security:
            recommendations.append("🛡️ إنشاء ملفات الأمان المفقودة")
        
        # توصيات عامة
        recommendations.extend([
            "📊 تشغيل نظام المراقبة المستمرة",
            "🔄 جدولة النسخ الاحتياطية الدورية",
            "📚 مراجعة التوثيق والأدلة",
            "🔧 تحديث النظام والمكونات"
        ])
        
        self.test_results["recommendations"] = recommendations
    
    def save_test_report(self):
        """حفظ تقرير الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_system_test_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ تقرير الاختبار: {filename}")
        return filename
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*70)
        print("🧪 تقرير اختبار نظام أنوبيس الشامل")
        print("="*70)
        
        # الملخص الإجمالي
        summary = self.test_results.get("test_summary", {})
        print(f"\n{summary.get('emoji', '❓')} الحالة الإجمالية: {summary.get('status', 'غير معروف')}")
        print(f"📊 معدل النجاح: {summary.get('success_rate', 0)}%")
        print(f"✅ الاختبارات الناجحة: {summary.get('passed_tests', 0)}")
        print(f"❌ الاختبارات الفاشلة: {summary.get('failed_tests', 0)}")
        
        # تفاصيل البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        print(f"\n🏗️ البنية التحتية:")
        print(f"   🐳 Docker: {'✅ متاح' if infrastructure.get('docker_availability') else '❌ غير متاح'}")
        print(f"   🐳 Docker Compose: {'✅ متاح' if infrastructure.get('docker_compose_availability') else '❌ غير متاح'}")
        print(f"   🌐 الاتصال بالإنترنت: {'✅ متاح' if infrastructure.get('network_connectivity') else '❌ غير متاح'}")
        
        # حالة المجلدات
        system_dirs = infrastructure.get("system_directories", {})
        existing_dirs = sum(1 for exists in system_dirs.values() if exists)
        print(f"   📁 المجلدات الموجودة: {existing_dirs}/{len(system_dirs)}")
        
        # حالة الخدمات
        core_services = self.test_results["test_categories"].get("core_services", {})
        print(f"\n🏠 الخدمات الأساسية:")
        for service, data in core_services.items():
            status = "✅ متاح" if data.get("available") else "❌ غير متاح"
            response_time = data.get("response_time", "N/A")
            print(f"   🔹 {service}: {status} ({response_time}s)")
        
        # اختبارات الذكاء الاصطناعي
        ai_tests = self.test_results.get("ai_model_tests", {})
        if ai_tests:
            print(f"\n🤖 نماذج الذكاء الاصطناعي:")
            ollama_status = "✅ متاح" if ai_tests.get("ollama_server", {}).get("available") else "❌ غير متاح"
            chroma_status = "✅ متاح" if ai_tests.get("chroma_db", {}).get("available") else "❌ غير متاح"
            print(f"   🏠 خادم Ollama: {ollama_status}")
            print(f"   🔍 قاعدة البيانات المتجهة: {chroma_status}")
        
        # اختبارات سير العمل
        workflow_tests = self.test_results.get("workflow_tests", {})
        if workflow_tests:
            print(f"\n🔄 سير العمل والأتمتة:")
            n8n_status = "✅ متاح" if workflow_tests.get("n8n_interface", {}).get("available") else "❌ غير متاح"
            custom_nodes_count = workflow_tests.get("custom_nodes", {}).get("total", 0)
            workflows_count = workflow_tests.get("workflow_files", {}).get("total", 0)
            print(f"   🔄 واجهة n8n: {n8n_status}")
            print(f"   🔧 العقد المخصصة: {custom_nodes_count}")
            print(f"   📋 ملفات سير العمل: {workflows_count}")
        
        # الأداء
        performance = self.test_results.get("performance_metrics", {})
        if performance.get("disk_usage"):
            disk = performance["disk_usage"]
            print(f"\n⚡ الأداء:")
            print(f"   💾 استخدام القرص: {disk['usage_percent']}% ({disk['used_gb']} GB / {disk['total_gb']} GB)")
        
        # الإخفاقات
        failures = self.test_results.get("failures", [])
        if failures:
            print(f"\n❌ الإخفاقات:")
            for failure in failures:
                print(f"   🔸 {failure}")
        
        # التوصيات
        recommendations = self.test_results.get("recommendations", [])
        if recommendations:
            print(f"\n💡 التوصيات:")
            for i, rec in enumerate(recommendations[:8], 1):  # أول 8 توصيات
                print(f"   {i}. {rec}")
        
        print("\n" + "="*70)
        print("✅ انتهى تقرير اختبار النظام الشامل")
        print("="*70)

def main():
    """الدالة الرئيسية"""
    tester = AnubisSystemTester()
    
    # تشغيل الاختبار الشامل
    test_results = tester.run_comprehensive_test()
    
    # طباعة التقرير المفصل
    tester.print_detailed_report()
    
    # حفظ التقرير
    tester.save_test_report()
    
    return test_results

if __name__ == "__main__":
    import shutil
    main()
