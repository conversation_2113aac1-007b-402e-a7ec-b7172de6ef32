{"test_info": {"timestamp": "2025-07-16T08:26:56.293200", "duration": 90.184306, "test_type": "langsmith_integration"}, "langsmith_status": {"available": true, "wrapper_functional": true, "traces_recorded": 19}, "test_results": {"langsmith_availability": true, "wrapper_functionality": true, "agents_test": {"enhanced_error_detector": {"status": "success", "info": {"status": "loaded"}}, "enhanced_project_analyzer": {"status": "success", "info": {"status": "loaded"}}, "enhanced_file_organizer": {"status": "success", "info": {"status": "loaded"}}, "enhanced_memory_agent": {"status": "success", "info": {"status": "loaded"}}, "smart_code_analyzer": {"status": "success", "info": {"status": "loaded"}}}, "models_test": {"llama3:8b": {"status": "success", "response_time": 28.44, "response_length": 133, "available": true}, "mistral:7b": {"status": "success", "response_time": 32.37, "response_length": 115, "available": true}, "phi3:mini": {"status": "success", "response_time": 11.86, "response_length": 36, "available": true}}, "coordination_test": {"status": "success", "steps_completed": 4, "results": {"analyzer": "تم تحليل المشروع", "detector": "تم كشف الأخطاء", "organizer": "تم تنظيم النتائج", "memory": "تم حفظ البيانات"}}}, "traces_summary": {"total_traces": 19, "traces": [{"name": "smart_code_analyzer_load_agent", "inputs": null, "timestamp": "2025-07-16T08:27:05.119397", "agent": "smart_code_analyzer", "operation": "load_agent", "completed_at": "2025-07-16T08:27:07.233466"}, {"name": "smart_code_analyzer_test_operation", "inputs": null, "timestamp": "2025-07-16T08:27:07.233497", "agent": "smart_code_analyzer", "operation": "test_operation", "completed_at": "2025-07-16T08:27:07.233958"}, {"name": "ollama_provider_test_llama3:8b", "inputs": null, "timestamp": "2025-07-16T08:27:07.236239", "agent": "ollama_provider", "operation": "test_llama3:8b", "completed_at": "2025-07-16T08:27:37.742767"}, {"name": "ollama_provider_test_mistral:7b", "inputs": null, "timestamp": "2025-07-16T08:27:37.743094", "agent": "ollama_provider", "operation": "test_mistral:7b", "completed_at": "2025-07-16T08:28:12.152602"}, {"name": "ollama_provider_test_phi3:mini", "inputs": null, "timestamp": "2025-07-16T08:28:12.152713", "agent": "ollama_provider", "operation": "test_phi3:mini", "completed_at": "2025-07-16T08:28:26.072232"}, {"name": "orchestrator_multi_agent_test", "inputs": null, "timestamp": "2025-07-16T08:28:26.072704", "agent": "orchestrator", "operation": "multi_agent_test", "completed_at": "2025-07-16T08:28:26.476911"}, {"name": "coordination_analyzer", "inputs": null, "timestamp": "2025-07-16T08:28:26.072896", "agent": "coordination", "operation": "analyzer", "completed_at": "2025-07-16T08:28:26.173520"}, {"name": "coordination_detector", "inputs": null, "timestamp": "2025-07-16T08:28:26.173547", "agent": "coordination", "operation": "detector", "completed_at": "2025-07-16T08:28:26.274023"}, {"name": "coordination_organizer", "inputs": null, "timestamp": "2025-07-16T08:28:26.274087", "agent": "coordination", "operation": "organizer", "completed_at": "2025-07-16T08:28:26.375123"}, {"name": "coordination_memory", "inputs": null, "timestamp": "2025-07-16T08:28:26.375164", "agent": "coordination", "operation": "memory", "completed_at": "2025-07-16T08:28:26.476442"}], "langsmith_enabled": false}, "recommendations": [{"type": "api_key", "priority": "high", "description": "تعيين LANGCHAIN_API_KEY للاستفادة الكاملة من LangSmith"}, {"type": "monitoring", "priority": "medium", "description": "إعداد مراقبة مستمرة للأداء"}, {"type": "optimization", "priority": "low", "description": "تحسين prompts والتفاعلات بناءً على البيانات المجمعة"}]}