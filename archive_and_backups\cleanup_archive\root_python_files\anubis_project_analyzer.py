#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 محلل مشروع أنوبيس - تحليل الوضع الحالي
Anubis Project Analyzer - Current State Analysis
"""

import os
import json
from pathlib import Path
from datetime import datetime
import subprocess

class AnubisProjectAnalyzer:
    """محلل مشروع أنوبيس"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.analysis_results = {}
        
    def analyze_main_files(self):
        """تحليل الملفات الرئيسية"""
        main_files = {
            "main.py": "الملف الرئيسي للنظام",
            "README.md": "التوثيق الأساسي",
            "requirements.txt": "متطلبات Python",
            "docker-compose.yml": "Docker Compose الأساسي",
            "Dockerfile": "Docker الأساسي"
        }
        
        results = {}
        for file_name, description in main_files.items():
            file_path = self.project_root / file_name
            if file_path.exists():
                stat = file_path.stat()
                results[file_name] = {
                    "exists": True,
                    "description": description,
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "lines": self.count_lines(file_path) if file_path.suffix in ['.py', '.md', '.txt', '.yml'] else None
                }
            else:
                results[file_name] = {
                    "exists": False,
                    "description": description
                }
        
        self.analysis_results["main_files"] = results
        return results
    
    def analyze_directories(self):
        """تحليل المجلدات الرئيسية"""
        expected_dirs = {
            "anubis_main_system": "النظام الأساسي",
            "configs": "ملفات الإعدادات",
            "database": "قاعدة البيانات",
            "data": "البيانات",
            "logs": "ملفات السجلات",
            "documentation": "التوثيق",
            "reports": "التقارير",
            "scripts": "السكريبتات",
            "utilities": "الأدوات المساعدة",
            "archive_and_backups": "الأرشيف والنسخ الاحتياطية",
            "isolation": "نظام العزل الجديد",
            "universal_ai_system": "نظام AI العام",
            "workflows_and_automation": "سير العمل والأتمتة",
            "tools_and_utilities": "الأدوات والمرافق",
            "workspace": "مساحة العمل"
        }
        
        results = {}
        for dir_name, description in expected_dirs.items():
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                file_count = len(list(dir_path.rglob("*")))
                results[dir_name] = {
                    "exists": True,
                    "description": description,
                    "file_count": file_count,
                    "subdirs": [d.name for d in dir_path.iterdir() if d.is_dir()][:10]  # أول 10 مجلدات فرعية
                }
            else:
                results[dir_name] = {
                    "exists": False,
                    "description": description
                }
        
        self.analysis_results["directories"] = results
        return results
    
    def analyze_new_files(self):
        """تحليل الملفات الجديدة المضافة"""
        new_file_patterns = [
            "anubis_*.py",
            "docker-compose-anubis-*.yml",
            "*_gemini_*.py",
            "*_isolation_*.py",
            "*_test*.py",
            "final_*.py"
        ]
        
        new_files = []
        for pattern in new_file_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    stat = file_path.stat()
                    new_files.append({
                        "name": file_path.name,
                        "size": stat.st_size,
                        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "lines": self.count_lines(file_path) if file_path.suffix == '.py' else None
                    })
        
        # ترتيب حسب تاريخ التعديل
        new_files.sort(key=lambda x: x["modified"], reverse=True)
        
        self.analysis_results["new_files"] = new_files
        return new_files
    
    def analyze_docker_files(self):
        """تحليل ملفات Docker"""
        docker_files = []
        
        # البحث عن ملفات Docker
        for file_path in self.project_root.rglob("docker-compose*.yml"):
            docker_files.append({
                "name": file_path.name,
                "path": str(file_path.relative_to(self.project_root)),
                "type": "docker-compose"
            })
        
        for file_path in self.project_root.rglob("Dockerfile"):
            docker_files.append({
                "name": file_path.name,
                "path": str(file_path.relative_to(self.project_root)),
                "type": "dockerfile"
            })
        
        self.analysis_results["docker_files"] = docker_files
        return docker_files
    
    def count_lines(self, file_path):
        """عد أسطر الملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return len(f.readlines())
        except:
            return None
    
    def check_running_services(self):
        """فحص الخدمات العاملة"""
        services = {}
        
        # فحص المنفذ 8000 (النظام الأساسي)
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=3)
            services["main_system"] = {
                "port": 8000,
                "status": "running" if response.status_code == 200 else "error",
                "response": response.json() if response.status_code == 200 else None
            }
        except:
            services["main_system"] = {"port": 8000, "status": "not_running"}
        
        # فحص المنفذ 8080 (النظام المعزول)
        try:
            import requests
            response = requests.get("http://localhost:8080/health", timeout=3)
            services["isolation_system"] = {
                "port": 8080,
                "status": "running" if response.status_code == 200 else "error",
                "response": response.json() if response.status_code == 200 else None
            }
        except:
            services["isolation_system"] = {"port": 8080, "status": "not_running"}
        
        # فحص المنفذ 9090 (المراقبة)
        try:
            import requests
            response = requests.get("http://localhost:9090/health", timeout=3)
            services["monitor_system"] = {
                "port": 9090,
                "status": "running" if response.status_code == 200 else "error",
                "response": response.json() if response.status_code == 200 else None
            }
        except:
            services["monitor_system"] = {"port": 9090, "status": "not_running"}
        
        self.analysis_results["running_services"] = services
        return services
    
    def generate_summary(self):
        """إنشاء ملخص التحليل"""
        summary = {
            "analysis_timestamp": datetime.now().isoformat(),
            "project_name": "Universal AI Assistants (Anubis)",
            "main_files_status": {},
            "directories_status": {},
            "new_files_count": len(self.analysis_results.get("new_files", [])),
            "docker_files_count": len(self.analysis_results.get("docker_files", [])),
            "running_services_count": len([s for s in self.analysis_results.get("running_services", {}).values() if s.get("status") == "running"]),
            "recommendations": []
        }
        
        # تحليل الملفات الرئيسية
        main_files = self.analysis_results.get("main_files", {})
        summary["main_files_status"] = {
            "total": len(main_files),
            "existing": len([f for f in main_files.values() if f.get("exists")]),
            "missing": len([f for f in main_files.values() if not f.get("exists")])
        }
        
        # تحليل المجلدات
        directories = self.analysis_results.get("directories", {})
        summary["directories_status"] = {
            "total": len(directories),
            "existing": len([d for d in directories.values() if d.get("exists")]),
            "missing": len([d for d in directories.values() if not d.get("exists")])
        }
        
        # توصيات
        if summary["new_files_count"] > 10:
            summary["recommendations"].append("تنظيم الملفات الجديدة في مجلدات مناسبة")
        
        if summary["docker_files_count"] > 3:
            summary["recommendations"].append("توحيد ملفات Docker وتنظيمها")
        
        if summary["running_services_count"] > 1:
            summary["recommendations"].append("دمج الخدمات المتعددة في نظام موحد")
        
        return summary
    
    def run_complete_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🏺 محلل مشروع أنوبيس - تحليل الوضع الحالي")
        print("=" * 60)
        
        print("\n📄 تحليل الملفات الرئيسية...")
        main_files = self.analyze_main_files()
        
        print("📁 تحليل المجلدات...")
        directories = self.analyze_directories()
        
        print("🆕 تحليل الملفات الجديدة...")
        new_files = self.analyze_new_files()
        
        print("🐳 تحليل ملفات Docker...")
        docker_files = self.analyze_docker_files()
        
        print("🔍 فحص الخدمات العاملة...")
        services = self.check_running_services()
        
        print("\n📊 إنشاء الملخص...")
        summary = self.generate_summary()
        
        # عرض النتائج
        self.display_results(summary)
        
        # حفظ التقرير
        self.save_analysis_report()
        
        return self.analysis_results
    
    def display_results(self, summary):
        """عرض نتائج التحليل"""
        print("\n📋 ملخص التحليل:")
        print("-" * 40)
        print(f"📄 الملفات الرئيسية: {summary['main_files_status']['existing']}/{summary['main_files_status']['total']}")
        print(f"📁 المجلدات: {summary['directories_status']['existing']}/{summary['directories_status']['total']}")
        print(f"🆕 ملفات جديدة: {summary['new_files_count']}")
        print(f"🐳 ملفات Docker: {summary['docker_files_count']}")
        print(f"🔍 خدمات عاملة: {summary['running_services_count']}")
        
        if summary["recommendations"]:
            print(f"\n💡 التوصيات:")
            for i, rec in enumerate(summary["recommendations"], 1):
                print(f"   {i}. {rec}")
    
    def save_analysis_report(self):
        """حفظ تقرير التحليل"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"anubis_project_analysis_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 تم حفظ تقرير التحليل: {report_file}")
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")

def main():
    """الدالة الرئيسية"""
    analyzer = AnubisProjectAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
