#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 Anubis AI System API Server
FastAPI server for n8n integration with OpenAPI specification
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent.parent))

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import APIKeyHeader
from pydantic import BaseModel, Field

# استيراد مكونات النظام
try:
    from anubis.agents.database_agent import DatabaseAgent
    from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent
    from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent
    from anubis.agents.enhanced_memory_agent import EnhancedMemoryAgent
    from anubis.agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
    from anubis.agents.smart_ai_agent import SmartAIAgent
    from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer
    from anubis.core.ai_integration import OllamaProvider
    from anubis.database.anubis_database import AnubisDatabase
except ImportError as e:
    print(f"⚠️ تحذير: لا يمكن استيراد بعض المكونات: {e}")

# إعداد متغيرات البيئة لـ LangSmith
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "anubis-ai-system"

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Anubis AI System API",
    description="API for Anubis AI System - Multi-model AI collaboration platform",
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد API Key Authentication
API_KEY = "anubis-api-key-2025"
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


def get_api_key(api_key: str = Security(api_key_header)):
    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="Invalid API Key")
    return api_key


# نماذج البيانات
class OllamaRequest(BaseModel):
    prompt: str = Field(..., description="Input prompt for the model")
    model: str = Field(
        ...,
        description="Ollama model to use",
        regex="^(llama3:8b|mistral:7b|phi3:mini)$",
    )
    temperature: float = Field(0.7, ge=0, le=2, description="Sampling temperature")
    max_tokens: int = Field(1000, ge=1, le=4096, description="Maximum tokens to generate")


class OllamaResponse(BaseModel):
    model: str
    prompt: str
    response: str
    response_time: float
    tokens_generated: Optional[int] = None
    status: str


class GeminiRequest(BaseModel):
    prompt: str = Field(..., description="Input prompt for Gemini")
    temperature: float = Field(0.7, ge=0, le=1)


class GeminiResponse(BaseModel):
    prompt: str
    response: str
    response_time: float
    status: str


class AgentRequest(BaseModel):
    agent_type: str = Field(..., description="Type of agent to run")
    project_path: str = Field(..., description="Path to project for analysis")
    config: Dict[str, Any] = Field(default_factory=dict, description="Agent configuration")
    verbose: bool = Field(True, description="Enable verbose output")


class AgentResponse(BaseModel):
    agent_type: str
    project_path: str
    result: Dict[str, Any]
    execution_time: float
    status: str


class CollaborationRequest(BaseModel):
    task: str = Field(..., description="Task description")
    models: List[str] = Field(..., description="Models to use in collaboration")
    agents: List[str] = Field(..., description="Agents to use in collaboration")
    project_path: str = Field(..., description="Project path for analysis")
    collaboration_mode: str = Field("parallel", description="Collaboration mode")


class CollaborationResponse(BaseModel):
    task: str
    results: Dict[str, Any]
    consensus: Dict[str, Any]
    execution_time: float
    status: str


# متغيرات عامة
ollama_providers = {}
database = None


@app.on_event("startup")
async def startup_event():
    """تهيئة النظام عند بدء التشغيل"""
    global ollama_providers, database

    print("🏺 بدء تشغيل خادم Anubis API...")

    # تهيئة موفري Ollama
    models = ["llama3:8b", "mistral:7b", "phi3:mini"]
    for model in models:
        try:
            ollama_providers[model] = OllamaProvider(model)
            print(f"✅ تم تهيئة {model}")
        except Exception as e:
            print(f"❌ خطأ في تهيئة {model}: {e}")

    # تهيئة قاعدة البيانات
    try:
        database = AnubisDatabase()
        print("✅ تم الاتصال بقاعدة البيانات")
    except Exception as e:
        print(f"⚠️ تحذير: لا يمكن الاتصال بقاعدة البيانات: {e}")

    print("🚀 خادم Anubis API جاهز!")


# المسارات الأساسية
@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "🏺 Anubis AI System API",
        "version": "1.0.0",
        "status": "active",
        "docs": "/docs",
        "openapi": "/api/v1/openapi.json",
    }


@app.get("/health")
async def health_check():
    """فحص صحة النظام"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models_available": list(ollama_providers.keys()),
        "database_connected": database is not None,
    }


# مسارات نماذج Ollama
@app.post("/api/v1/models/ollama/generate", response_model=OllamaResponse)
async def generate_ollama_response(request: OllamaRequest, api_key: str = Depends(get_api_key)):
    """توليد استجابة باستخدام نماذج Ollama"""
    try:
        if request.model not in ollama_providers:
            raise HTTPException(status_code=400, detail=f"Model {request.model} not available")

        provider = ollama_providers[request.model]
        start_time = time.time()

        response = provider.generate_response(request.prompt)

        end_time = time.time()
        response_time = end_time - start_time

        return OllamaResponse(
            model=request.model,
            prompt=request.prompt,
            response=response,
            response_time=response_time,
            tokens_generated=len(response.split()),
            status="success",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# مسار Gemini
@app.post("/api/v1/models/gemini/generate", response_model=GeminiResponse)
async def generate_gemini_response(request: GeminiRequest, api_key: str = Depends(get_api_key)):
    """توليد استجابة باستخدام Gemini"""
    try:
        import subprocess

        start_time = time.time()

        # استدعاء Gemini CLI
        result = subprocess.run(
            ["gemini", "--prompt", request.prompt],
            capture_output=True,
            text=True,
            timeout=60,
        )

        end_time = time.time()
        response_time = end_time - start_time

        if result.returncode == 0:
            return GeminiResponse(
                prompt=request.prompt,
                response=result.stdout.strip(),
                response_time=response_time,
                status="success",
            )
        else:
            raise HTTPException(status_code=500, detail=f"Gemini error: {result.stderr}")

    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Gemini request timeout")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# مسار الوكلاء
@app.post("/api/v1/agents/run", response_model=AgentResponse)
async def run_anubis_agent(request: AgentRequest, api_key: str = Depends(get_api_key)):
    """تشغيل وكيل أنوبيس"""
    try:
        start_time = time.time()

        # اختيار الوكيل المناسب
        agent = None
        if request.agent_type == "error_detector":
            agent = EnhancedErrorDetectorAgent(
                request.project_path, request.config, request.verbose
            )
        elif request.agent_type == "project_analyzer":
            agent = EnhancedProjectAnalyzerAgent(
                request.project_path, request.config, request.verbose
            )
        elif request.agent_type == "file_organizer":
            agent = EnhancedFileOrganizerAgent(
                request.project_path, request.config, request.verbose
            )
        elif request.agent_type == "memory_agent":
            agent = EnhancedMemoryAgent(request.project_path, request.config, request.verbose)
        elif request.agent_type == "database_agent":
            agent = DatabaseAgent(request.project_path, request.config, request.verbose)
        elif request.agent_type == "smart_ai_agent":
            agent = SmartAIAgent(request.project_path, request.config, request.verbose)
        elif request.agent_type == "code_analyzer":
            agent = SmartCodeAnalyzer(request.project_path, request.config, request.verbose)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown agent type: {request.agent_type}")

        # تشغيل الوكيل
        result = agent.run_analysis()

        end_time = time.time()
        execution_time = end_time - start_time

        return AgentResponse(
            agent_type=request.agent_type,
            project_path=request.project_path,
            result=result,
            execution_time=execution_time,
            status="success",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# مسار التعاون
@app.post("/api/v1/collaboration/analyze", response_model=CollaborationResponse)
async def collaborative_analysis(
    request: CollaborationRequest, api_key: str = Depends(get_api_key)
):
    """تحليل تعاوني باستخدام عدة نماذج ووكلاء"""
    try:
        start_time = time.time()
        results = {}

        # تشغيل النماذج
        for model in request.models:
            if model in ollama_providers:
                provider = ollama_providers[model]
                response = provider.generate_response(
                    f"Task: {request.task}\nProject: {request.project_path}"
                )
                results[f"model_{model}"] = {
                    "type": "model",
                    "response": response,
                    "model": model,
                }

        # تشغيل الوكلاء
        for agent_type in request.agents:
            try:
                agent_request = AgentRequest(
                    agent_type=agent_type,
                    project_path=request.project_path,
                    config={},
                    verbose=False,
                )
                agent_response = await run_anubis_agent(
                    agent_request, api_key="anubis-api-key-2025"
                )
                results[f"agent_{agent_type}"] = {
                    "type": "agent",
                    "result": agent_response.result,
                    "agent_type": agent_type,
                }
            except Exception as e:
                results[f"agent_{agent_type}"] = {
                    "type": "agent",
                    "error": str(e),
                    "agent_type": agent_type,
                }

        # إنشاء إجماع بسيط
        consensus = {
            "task": request.task,
            "total_responses": len(results),
            "successful_responses": len([r for r in results.values() if "error" not in r]),
            "summary": f"Analyzed {request.project_path} using {len(request.models)} models and {len(request.agents)} agents",
        }

        end_time = time.time()
        execution_time = end_time - start_time

        return CollaborationResponse(
            task=request.task,
            results=results,
            consensus=consensus,
            execution_time=execution_time,
            status="success",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# مسارات قاعدة البيانات
@app.get("/api/v1/database/projects")
async def get_projects(api_key: str = Depends(get_api_key)):
    """الحصول على جميع المشاريع"""
    try:
        if not database:
            raise HTTPException(status_code=503, detail="Database not available")

        projects = database.get_all_projects()
        return projects

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/database/projects")
async def create_project(project_data: dict, api_key: str = Depends(get_api_key)):
    """إنشاء مشروع جديد"""
    try:
        if not database:
            raise HTTPException(status_code=503, detail="Database not available")

        project_id = database.add_project(
            name=project_data.get("name"),
            path=project_data.get("path"),
            project_type=project_data.get("type"),
            description=project_data.get("description", ""),
        )

        return {"id": project_id, "status": "created"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# مسار LangSmith
@app.post("/api/v1/langsmith/trace")
async def create_langsmith_trace(trace_data: dict, api_key: str = Depends(get_api_key)):
    """إنشاء تتبع في LangSmith"""
    try:
        from langsmith import traceable

        @traceable(name=trace_data.get("name", "anubis_trace"))
        def create_trace(inputs):
            return {"trace_created": True, "inputs": inputs}

        result = create_trace(trace_data.get("inputs", {}))

        return {
            "trace_id": "generated_trace_id",
            "name": trace_data.get("name"),
            "status": "created",
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    print("🏺 تشغيل خادم Anubis API...")
    uvicorn.run(
        "anubis_api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
