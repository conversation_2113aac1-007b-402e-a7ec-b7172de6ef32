#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 فاحص أنوبيس الشامل باستخدام الوكلاء
Anubis Agents Comprehensive Scanner

نظام فحص وعزل متقدم يستخدم جميع الوكلاء المتاحين:
- EnhancedErrorDetectorAgent: كشف الأخطاء المتقدم
- EnhancedProjectAnalyzerAgent: تحليل المشاريع الشامل  
- EnhancedFileOrganizerAgent: تنظيم الملفات الذكي
- DatabaseAgent: إدارة قواعد البيانات
- SmartAIAgent: الذكاء الاصطناعي الشامل

مطور بالتعاون مع الوكلاء الذكيين
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# إضافة مسار أنوبيس
sys.path.append(str(Path(__file__).parent / "anubis"))

try:
    # استيراد الوكلاء المتاحين فعلياً
    from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent
    from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent
    from anubis.agents.database_agent import DatabaseAgent

    # استيراد النواة الأساسية (إذا كانت متاحة)
    try:
        from anubis.core.config_manager import ConfigManager
        from anubis.core.logger import Logger
    except ImportError:
        # إنشاء بدائل بسيطة إذا لم تكن النواة متاحة
        class ConfigManager:
            def __init__(self): pass
        class Logger:
            def __init__(self, name): self.name = name

except ImportError as e:
    print(f"❌ خطأ في استيراد الوكلاء: {e}")
    print("💡 سيتم إنشاء فاحص بديل بدون الوكلاء")

    # إنشاء وكلاء وهميين للاختبار
    class MockAgent:
        def __init__(self, project_path, config):
            self.project_path = project_path
            self.config = config
        def analyze(self):
            return {"execution_time": 0.1, "status": "mock"}

    EnhancedErrorDetectorAgent = MockAgent
    EnhancedFileOrganizerAgent = MockAgent
    DatabaseAgent = MockAgent

    class ConfigManager:
        def __init__(self): pass
    class Logger:
        def __init__(self, name): self.name = name


class AnubisAgentsScanner:
    """فاحص أنوبيس الشامل باستخدام الوكلاء"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.scan_timestamp = datetime.now().isoformat()
        
        # إعداد النظام
        self.config_manager = ConfigManager()
        self.logger = Logger("AnubisAgentsScanner")
        
        # نتائج الفحص
        self.scan_results = {
            "scan_info": {
                "timestamp": self.scan_timestamp,
                "project_root": str(self.project_root),
                "scanner_version": "2.0",
                "agents_used": []
            },
            "agents_analysis": {},
            "components_discovered": {},
            "isolation_plan": {},
            "recommendations": []
        }
        
        # الوكلاء المتاحين
        self.agents = {}
        
    def initialize_agents(self):
        """تهيئة جميع الوكلاء"""
        print("🤖 تهيئة الوكلاء الذكيين...")
        
        try:
            # إعداد التكوين للوكلاء
            agent_config = {
                "project_path": str(self.project_root),
                "verbose": True,
                "enable_ai": True,
                "language": "ar"
            }
            
            # تهيئة الوكلاء المحسنين
            self.agents["error_detector"] = EnhancedErrorDetectorAgent(
                str(self.project_root), agent_config
            )
            
            self.agents["file_organizer"] = EnhancedFileOrganizerAgent(
                str(self.project_root), agent_config
            )

            self.agents["database_agent"] = DatabaseAgent(
                str(self.project_root), agent_config
            )
            
            # تسجيل الوكلاء المستخدمين
            self.scan_results["scan_info"]["agents_used"] = list(self.agents.keys())
            
            print(f"  ✅ تم تهيئة {len(self.agents)} وكيل بنجاح")
            
            for agent_name in self.agents.keys():
                print(f"    🤖 {agent_name}")
                
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في تهيئة الوكلاء: {e}")
            return False
    
    def run_comprehensive_scan(self):
        """تشغيل الفحص الشامل باستخدام جميع الوكلاء"""
        print("🔍 بدء الفحص الشامل باستخدام الوكلاء الذكيين")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("=" * 60)
        
        # تهيئة الوكلاء
        if not self.initialize_agents():
            print("❌ فشل في تهيئة الوكلاء")
            return self.scan_results
        
        # تشغيل الوكلاء المتاحين
        self.run_error_detection_analysis()
        self.run_file_organization_analysis()
        self.run_database_analysis()
        
        # تحليل النتائج وإنشاء خطة العزل
        self.analyze_results_and_create_isolation_plan()
        
        # إنشاء التوصيات
        self.generate_recommendations()
        
        print("\n🎉 تم إكمال الفحص الشامل بنجاح!")
        return self.scan_results
    
    def run_error_detection_analysis(self):
        """تشغيل وكيل كشف الأخطاء"""
        print("\n🔍 تشغيل وكيل كشف الأخطاء المتقدم...")
        
        try:
            agent = self.agents["error_detector"]
            
            # تشغيل تحليل الأخطاء
            error_results = agent.run_analysis()
            
            self.scan_results["agents_analysis"]["error_detector"] = {
                "status": "completed",
                "execution_time": error_results.get("execution_time", 0),
                "errors_found": error_results.get("errors_found", []),
                "error_count": len(error_results.get("errors_found", [])),
                "recommendations": error_results.get("recommendations", [])
            }
            
            print(f"  ✅ تم العثور على {len(error_results.get('errors_found', []))} خطأ")
            
        except Exception as e:
            print(f"  ❌ خطأ في وكيل كشف الأخطاء: {e}")
            self.scan_results["agents_analysis"]["error_detector"] = {
                "status": "failed",
                "error": str(e)
            }
    

    
    def run_file_organization_analysis(self):
        """تشغيل وكيل تنظيم الملفات"""
        print("\n📁 تشغيل وكيل تنظيم الملفات الذكي...")
        
        try:
            agent = self.agents["file_organizer"]
            
            # تشغيل تحليل تنظيم الملفات
            organization_results = agent.run_analysis()
            
            self.scan_results["agents_analysis"]["file_organizer"] = {
                "status": "completed",
                "execution_time": organization_results.get("execution_time", 0),
                "files_analyzed": organization_results.get("files_count", 0),
                "organization_suggestions": organization_results.get("suggestions", []),
                "misplaced_files": organization_results.get("misplaced_files", []),
                "cleanup_recommendations": organization_results.get("cleanup", [])
            }
            
            print(f"  ✅ تم تحليل {organization_results.get('files_count', 0)} ملف")
            
        except Exception as e:
            print(f"  ❌ خطأ في وكيل تنظيم الملفات: {e}")
            self.scan_results["agents_analysis"]["file_organizer"] = {
                "status": "failed",
                "error": str(e)
            }
    
    def run_database_analysis(self):
        """تشغيل وكيل قاعدة البيانات"""
        print("\n🗄️ تشغيل وكيل قاعدة البيانات...")
        
        try:
            agent = self.agents["database_agent"]
            
            # تشغيل تحليل قاعدة البيانات
            db_results = agent.run_analysis()
            
            self.scan_results["agents_analysis"]["database_agent"] = {
                "status": "completed",
                "execution_time": db_results.get("execution_time", 0),
                "databases_found": db_results.get("databases", []),
                "connection_status": db_results.get("connection_status", "unknown"),
                "schema_analysis": db_results.get("schema", {}),
                "optimization_suggestions": db_results.get("optimizations", [])
            }
            
            print(f"  ✅ تم تحليل {len(db_results.get('databases', []))} قاعدة بيانات")
            
        except Exception as e:
            print(f"  ❌ خطأ في وكيل قاعدة البيانات: {e}")
            self.scan_results["agents_analysis"]["database_agent"] = {
                "status": "failed",
                "error": str(e)
            }
    

    
    def calculate_isolation_priority(self, component: Dict) -> str:
        """حساب أولوية العزل للمكون"""
        component_type = component.get("type", "").lower()
        dependencies_count = len(component.get("dependencies", []))
        
        # تحديد الأولوية بناءً على النوع والتبعيات
        if component_type in ["api", "server", "service"]:
            return "high"
        elif component_type in ["database", "storage"]:
            return "critical"
        elif dependencies_count > 5:
            return "medium"
        else:
            return "low"
    
    def analyze_results_and_create_isolation_plan(self):
        """تحليل النتائج وإنشاء خطة العزل"""
        print("\n🔧 تحليل النتائج وإنشاء خطة العزل...")
        
        # تجميع جميع المكونات المكتشفة
        all_components = self.scan_results["components_discovered"]
        
        # تصنيف المكونات حسب الأولوية
        isolation_plan = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": []
        }
        
        for component_name, component_info in all_components.items():
            priority = component_info.get("isolation_priority", "low")
            isolation_plan[priority].append({
                "name": component_name,
                "path": component_info.get("path", ""),
                "type": component_info.get("type", ""),
                "container_config": self.generate_container_config(component_info)
            })
        
        self.scan_results["isolation_plan"] = isolation_plan
        
        # إحصائيات خطة العزل
        total_components = sum(len(components) for components in isolation_plan.values())
        print(f"  ✅ تم إنشاء خطة عزل لـ {total_components} مكون")
        
        for priority, components in isolation_plan.items():
            if components:
                print(f"    🔹 {priority}: {len(components)} مكون")
    
    def generate_container_config(self, component_info: Dict) -> Dict:
        """إنشاء تكوين الحاوية للمكون"""
        component_type = component_info.get("type", "").lower()
        
        # تكوين أساسي
        config = {
            "base_image": "python:3.11-slim",
            "ports": [],
            "volumes": [],
            "environment": [],
            "dependencies": component_info.get("dependencies", [])
        }
        
        # تخصيص التكوين حسب نوع المكون
        if component_type == "api":
            config["ports"] = ["8000:8000"]
            config["environment"] = ["PYTHONPATH=/app"]
            
        elif component_type == "database":
            config["base_image"] = "mysql:8.0"
            config["ports"] = ["3306:3306"]
            config["volumes"] = ["db_data:/var/lib/mysql"]
            config["environment"] = ["MYSQL_ROOT_PASSWORD=secure_password"]
            
        elif component_type == "frontend":
            config["base_image"] = "node:18-alpine"
            config["ports"] = ["3000:3000"]
            
        elif component_type == "ai_service":
            config["base_image"] = "python:3.11-slim"
            config["ports"] = ["11434:11434"]
            config["volumes"] = ["ai_models:/models"]
        
        return config
    
    def generate_recommendations(self):
        """إنشاء التوصيات النهائية"""
        print("\n💡 إنشاء التوصيات النهائية...")
        
        recommendations = []
        
        # تحليل نتائج الوكلاء
        agents_analysis = self.scan_results["agents_analysis"]
        
        # توصيات بناءً على كشف الأخطاء
        if "error_detector" in agents_analysis:
            error_count = agents_analysis["error_detector"].get("error_count", 0)
            if error_count > 0:
                recommendations.append({
                    "priority": "high",
                    "category": "error_fixing",
                    "title": f"إصلاح {error_count} خطأ مكتشف",
                    "description": "يجب إصلاح الأخطاء المكتشفة قبل العزل"
                })
        
        # توصيات بناءً على تحليل المشروع
        if "project_analyzer" in agents_analysis:
            quality_score = agents_analysis["project_analyzer"].get("quality_score", 0)
            if quality_score < 70:
                recommendations.append({
                    "priority": "medium",
                    "category": "quality_improvement",
                    "title": "تحسين جودة المشروع",
                    "description": f"نقاط الجودة الحالية: {quality_score}/100"
                })
        
        # توصيات بناءً على تنظيم الملفات
        if "file_organizer" in agents_analysis:
            misplaced_files = agents_analysis["file_organizer"].get("misplaced_files", [])
            if misplaced_files:
                recommendations.append({
                    "priority": "medium",
                    "category": "file_organization",
                    "title": f"إعادة تنظيم {len(misplaced_files)} ملف",
                    "description": "توجد ملفات في مواقع غير مناسبة"
                })
        
        # توصيات العزل
        isolation_plan = self.scan_results["isolation_plan"]
        critical_components = len(isolation_plan.get("critical", []))
        high_priority_components = len(isolation_plan.get("high", []))
        
        if critical_components > 0:
            recommendations.append({
                "priority": "critical",
                "category": "isolation",
                "title": f"عزل {critical_components} مكون حرج فوراً",
                "description": "هذه المكونات تحتاج عزل فوري لضمان الأمان"
            })
        
        if high_priority_components > 0:
            recommendations.append({
                "priority": "high",
                "category": "isolation",
                "title": f"عزل {high_priority_components} مكون عالي الأولوية",
                "description": "مكونات مهمة تحتاج عزل سريع"
            })
        
        self.scan_results["recommendations"] = recommendations
        
        print(f"  ✅ تم إنشاء {len(recommendations)} توصية")
    
    def save_scan_results(self, filename: Optional[str] = None) -> str:
        """حفظ نتائج الفحص"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_agents_scan_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الفحص في: {filename}")
        return filename
    
    def print_summary_report(self):
        """طباعة ملخص التقرير"""
        print("\n" + "="*60)
        print("🤖 تقرير الفحص الشامل باستخدام الوكلاء")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("="*60)
        
        # معلومات الفحص
        scan_info = self.scan_results["scan_info"]
        print(f"📅 وقت الفحص: {scan_info['timestamp']}")
        print(f"📁 مجلد المشروع: {scan_info['project_root']}")
        print(f"🤖 الوكلاء المستخدمين: {len(scan_info['agents_used'])}")
        
        # نتائج الوكلاء
        print(f"\n🔍 نتائج الوكلاء:")
        agents_analysis = self.scan_results["agents_analysis"]
        
        for agent_name, results in agents_analysis.items():
            status = results.get("status", "unknown")
            icon = "✅" if status == "completed" else "❌"
            print(f"  {icon} {agent_name}: {status}")
        
        # المكونات المكتشفة
        components = self.scan_results["components_discovered"]
        print(f"\n📦 المكونات المكتشفة: {len(components)}")
        
        # خطة العزل
        isolation_plan = self.scan_results["isolation_plan"]
        print(f"\n🔧 خطة العزل:")
        for priority, components in isolation_plan.items():
            if components:
                print(f"  🔹 {priority}: {len(components)} مكون")
        
        # التوصيات
        recommendations = self.scan_results["recommendations"]
        print(f"\n💡 التوصيات: {len(recommendations)}")
        
        for rec in recommendations[:3]:  # أول 3 توصيات
            priority = rec.get("priority", "medium")
            title = rec.get("title", "")
            print(f"  🔸 [{priority}] {title}")
        
        print("\n🎉 تم إكمال الفحص الشامل بنجاح!")
        print("🤖 تم تطوير هذا النظام بالتعاون مع الوكلاء الذكيين")
        print("="*60)


def main():
    """الدالة الرئيسية"""
    print("🤖 فاحص أنوبيس الشامل باستخدام الوكلاء")
    print("🏺 نظام أنوبيس للذكاء الاصطناعي")
    
    # إنشاء الفاحص
    scanner = AnubisAgentsScanner()
    
    # تشغيل الفحص الشامل
    results = scanner.run_comprehensive_scan()
    
    # طباعة الملخص
    scanner.print_summary_report()
    
    # حفظ النتائج
    report_file = scanner.save_scan_results()
    
    print(f"\n📊 تقرير مفصل متاح في: {report_file}")


if __name__ == "__main__":
    main()
