# سياسات الأمان المتقدمة لنظام أنوبيس
# Advanced Security Policies for Anubis System

apiVersion: v1
kind: SecurityPolicy
metadata:
  name: anubis-advanced-security
  namespace: anubis-system
  labels:
    app: anubis
    security-level: maximum
spec:
  # سياسات التحكم في الوصول
  access_control:
    # منع الوصول المميز
    privileged: false
    allow_privilege_escalation: false
    
    # قيود المستخدم
    run_as_user:
      min: 1000
      max: 65535
    run_as_group:
      min: 1000
      max: 65535
    
    # قيود نظام الملفات
    read_only_root_filesystem: true
    allowed_volumes:
      - "emptyDir"
      - "configMap"
      - "secret"
      - "persistentVolumeClaim"
    
    # قيود الشبكة
    host_network: false
    host_ports: false
    host_pid: false
    host_ipc: false
  
  # سياسات الأمان المتقدمة
  security_context:
    # إزالة جميع القدرات
    drop_capabilities:
      - ALL
    
    # إضافة قدرات محددة فقط
    add_capabilities: []
    
    # منع الامتيازات الجديدة
    allow_privilege_escalation: false
    
    # تشغيل كمستخدم غير مميز
    run_as_non_root: true
    
    # معرف المستخدم المطلوب
    run_as_user: 1000
    run_as_group: 1000
    
    # إعدادات SELinux
    se_linux_options:
      level: "s0:c123,c456"
      role: "object_r"
      type: "container_t"
      user: "system_u"
  
  # سياسات الشبكة
  network_policy:
    # منع كل الاتصالات بشكل افتراضي
    policy_types:
      - Ingress
      - Egress
    
    # اتصالات الدخول المسموحة
    ingress:
      - from:
          - namespace_selector:
              match_labels:
                name: anubis-system
        ports:
          - protocol: TCP
            port: 8000
    
    # اتصالات الخروج المسموحة
    egress:
      - to:
          - namespace_selector:
              match_labels:
                name: anubis-monitoring
        ports:
          - protocol: TCP
            port: 9090
      
      # السماح بـ DNS فقط
      - to: []
        ports:
          - protocol: UDP
            port: 53
  
  # سياسات الموارد
  resource_policy:
    # حدود الموارد
    limits:
      cpu: "1000m"
      memory: "1Gi"
      ephemeral-storage: "2Gi"
    
    # طلبات الموارد
    requests:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
  
  # سياسات المراقبة والتدقيق
  monitoring_policy:
    # تفعيل المراقبة
    enabled: true
    
    # تسجيل الأحداث
    audit_logging: true
    
    # مراقبة الأداء
    performance_monitoring: true
    
    # تنبيهات الأمان
    security_alerts: true
    
    # فحص الثغرات
    vulnerability_scanning: true
  
  # سياسات التشفير
  encryption_policy:
    # تشفير البيانات أثناء النقل
    transit_encryption: true
    
    # تشفير البيانات أثناء الراحة
    at_rest_encryption: true
    
    # إدارة المفاتيح
    key_management:
      provider: "vault"
      rotation_interval: "30d"
      
  # سياسات النسخ الاحتياطي
  backup_policy:
    # تفعيل النسخ الاحتياطي
    enabled: true
    
    # جدولة النسخ الاحتياطي
    schedule: "0 2 * * *"  # يومياً في الساعة 2 صباحاً
    
    # الاحتفاظ
    retention: "30d"
    
    # التشفير
    encryption: true

---
# ملف تكوين Seccomp
apiVersion: v1
kind: ConfigMap
metadata:
  name: anubis-seccomp-profile
data:
  seccomp-profile.json: |
    {
      "defaultAction": "SCMP_ACT_ERRNO",
      "architectures": ["SCMP_ARCH_X86_64"],
      "syscalls": [
        {
          "names": [
            "accept",
            "accept4",
            "access",
            "adjtimex",
            "alarm",
            "bind",
            "brk",
            "capget",
            "capset",
            "chdir",
            "chmod",
            "chown",
            "chroot",
            "clock_getres",
            "clock_gettime",
            "clock_nanosleep",
            "close",
            "connect",
            "copy_file_range",
            "creat",
            "dup",
            "dup2",
            "dup3",
            "epoll_create",
            "epoll_create1",
            "epoll_ctl",
            "epoll_pwait",
            "epoll_wait",
            "eventfd",
            "eventfd2",
            "execve",
            "execveat",
            "exit",
            "exit_group",
            "faccessat",
            "fadvise64",
            "fallocate",
            "fanotify_mark",
            "fchdir",
            "fchmod",
            "fchmodat",
            "fchown",
            "fchownat",
            "fcntl",
            "fdatasync",
            "fgetxattr",
            "flistxattr",
            "flock",
            "fork",
            "fremovexattr",
            "fsetxattr",
            "fstat",
            "fstatfs",
            "fsync",
            "ftruncate",
            "futex",
            "getcwd",
            "getdents",
            "getdents64",
            "getegid",
            "geteuid",
            "getgid",
            "getgroups",
            "getpeername",
            "getpgid",
            "getpgrp",
            "getpid",
            "getppid",
            "getpriority",
            "getrandom",
            "getresgid",
            "getresuid",
            "getrlimit",
            "get_robust_list",
            "getrusage",
            "getsid",
            "getsockname",
            "getsockopt",
            "get_thread_area",
            "gettid",
            "gettimeofday",
            "getuid",
            "getxattr",
            "inotify_add_watch",
            "inotify_init",
            "inotify_init1",
            "inotify_rm_watch",
            "io_cancel",
            "ioctl",
            "io_destroy",
            "io_getevents",
            "ioprio_get",
            "ioprio_set",
            "io_setup",
            "io_submit",
            "ipc",
            "kill",
            "lchown",
            "lgetxattr",
            "link",
            "linkat",
            "listen",
            "listxattr",
            "llistxattr",
            "lremovexattr",
            "lseek",
            "lsetxattr",
            "lstat",
            "madvise",
            "memfd_create",
            "mincore",
            "mkdir",
            "mkdirat",
            "mknod",
            "mknodat",
            "mlock",
            "mlock2",
            "mlockall",
            "mmap",
            "mprotect",
            "mq_getsetattr",
            "mq_notify",
            "mq_open",
            "mq_timedreceive",
            "mq_timedsend",
            "mq_unlink",
            "mremap",
            "msgctl",
            "msgget",
            "msgrcv",
            "msgsnd",
            "msync",
            "munlock",
            "munlockall",
            "munmap",
            "nanosleep",
            "newfstatat",
            "open",
            "openat",
            "pause",
            "pipe",
            "pipe2",
            "poll",
            "ppoll",
            "prctl",
            "pread64",
            "preadv",
            "prlimit64",
            "pselect6",
            "ptrace",
            "pwrite64",
            "pwritev",
            "read",
            "readahead",
            "readlink",
            "readlinkat",
            "readv",
            "recv",
            "recvfrom",
            "recvmmsg",
            "recvmsg",
            "remap_file_pages",
            "removexattr",
            "rename",
            "renameat",
            "renameat2",
            "restart_syscall",
            "rmdir",
            "rt_sigaction",
            "rt_sigpending",
            "rt_sigprocmask",
            "rt_sigqueueinfo",
            "rt_sigreturn",
            "rt_sigsuspend",
            "rt_sigtimedwait",
            "rt_tgsigqueueinfo",
            "sched_getaffinity",
            "sched_getattr",
            "sched_getparam",
            "sched_get_priority_max",
            "sched_get_priority_min",
            "sched_getscheduler",
            "sched_rr_get_interval",
            "sched_setaffinity",
            "sched_setattr",
            "sched_setparam",
            "sched_setscheduler",
            "sched_yield",
            "seccomp",
            "select",
            "semctl",
            "semget",
            "semop",
            "semtimedop",
            "send",
            "sendfile",
            "sendmmsg",
            "sendmsg",
            "sendto",
            "setfsgid",
            "setfsuid",
            "setgid",
            "setgroups",
            "setitimer",
            "setpgid",
            "setpriority",
            "setregid",
            "setresgid",
            "setresuid",
            "setreuid",
            "setrlimit",
            "setsid",
            "setsockopt",
            "set_thread_area",
            "set_tid_address",
            "setuid",
            "setxattr",
            "shmat",
            "shmctl",
            "shmdt",
            "shmget",
            "shutdown",
            "sigaltstack",
            "signalfd",
            "signalfd4",
            "sigreturn",
            "socket",
            "socketcall",
            "socketpair",
            "splice",
            "stat",
            "statfs",
            "statx",
            "symlink",
            "symlinkat",
            "sync",
            "sync_file_range",
            "syncfs",
            "sysinfo",
            "tee",
            "tgkill",
            "time",
            "timer_create",
            "timer_delete",
            "timer_getoverrun",
            "timer_gettime",
            "timer_settime",
            "times",
            "tkill",
            "truncate",
            "umask",
            "uname",
            "unlink",
            "unlinkat",
            "utime",
            "utimensat",
            "utimes",
            "vfork",
            "vmsplice",
            "wait4",
            "waitid",
            "waitpid",
            "write",
            "writev"
          ],
          "action": "SCMP_ACT_ALLOW"
        }
      ]
    }
