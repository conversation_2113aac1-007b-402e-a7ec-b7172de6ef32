{"app_info": {"name": "VS Code Control Center - AI Enhanced", "version": "1.0.0", "description": "نظام ذكي متقدم لمراقبة وتحسين أداء VS Code", "author": "Universal AI Assistants", "created": "2025-07-17"}, "agent_config": {"update_interval": 5, "max_analysis_depth": 3, "enable_auto_optimization": false, "language": "ar", "gemini_model": "gemini-pro", "ollama_model": "llama2", "security_level": "medium", "ollama_api_url": "http://localhost:11434"}, "user_preferences": {"developer_type": "general", "experience_level": "intermediate", "auto_analysis": false, "theme": "dark", "notifications": true, "save_reports": true}, "ui_settings": {"window_width": 1400, "window_height": 900, "auto_refresh_interval": 5000, "show_advanced_metrics": true, "enable_animations": true, "font_family": "Segoe UI", "font_size": 10}, "monitoring_thresholds": {"cpu_warning": 70, "cpu_critical": 85, "memory_warning": 75, "memory_critical": 90, "disk_warning": 85, "disk_critical": 95, "process_count_warning": 300, "vscode_process_warning": 8}, "security_settings": {"enable_security_monitoring": true, "scan_network_connections": true, "check_suspicious_processes": true, "security_scan_interval": 30, "alert_on_threats": true}, "ai_agents": {"process_analyzer": {"enabled": true, "priority": "high", "analysis_depth": "detailed"}, "performance_optimizer": {"enabled": true, "priority": "high", "auto_optimize": false}, "security_monitor": {"enabled": true, "priority": "medium", "real_time_monitoring": true}, "smart_recommendations": {"enabled": true, "priority": "medium", "learning_enabled": true}, "gemini_agent": {"enabled": true, "priority": "low", "timeout": 30, "fallback_enabled": true}, "ollama_agent": {"enabled": true, "priority": "low", "timeout": 60, "fallback_enabled": true}}, "logging": {"level": "INFO", "save_to_file": true, "log_file": "vscode_control_center.log", "max_log_size_mb": 10, "backup_count": 3}, "reports": {"auto_save": false, "save_interval_minutes": 60, "report_format": "json", "include_recommendations": true, "include_agent_details": true, "compress_old_reports": true}, "advanced": {"enable_experimental_features": false, "debug_mode": false, "performance_profiling": false, "memory_optimization": true, "parallel_processing": true, "cache_analysis_results": true}}