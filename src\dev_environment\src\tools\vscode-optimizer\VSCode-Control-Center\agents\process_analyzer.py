# -*- coding: utf-8 -*-
"""
🔍 وكيل تحليل العمليات - Process Analyzer Agent
==============================================

وكيل ذكي متخصص في تحليل العمليات وتحديد المشاكل والاختناقات
"""

import psutil
import time
from typing import Dict, List, Any
from .base_agent import BaseAgent

class ProcessAnalyzerAgent(BaseAgent):
    """وكيل تحليل العمليات الذكي"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("ProcessAnalyzer", config)
        self.suspicious_processes = []
        self.high_resource_processes = []
        self.vscode_processes = []
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل شامل للعمليات"""
        try:
            analysis = {
                'timestamp': time.time(),
                'total_processes': 0,
                'vscode_processes': [],
                'high_cpu_processes': [],
                'high_memory_processes': [],
                'suspicious_processes': [],
                'system_health': {},
                'recommendations': []
            }
            
            # تحليل جميع العمليات
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    pinfo = proc.info
                    pinfo['cpu_percent'] = proc.cpu_percent()
                    pinfo['memory_percent'] = proc.memory_percent()
                    processes.append(pinfo)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            analysis['total_processes'] = len(processes)
            
            # تحليل عمليات VS Code
            vscode_procs = [p for p in processes if 'code' in p['name'].lower() or 'electron' in p['name'].lower()]
            analysis['vscode_processes'] = self._analyze_vscode_processes(vscode_procs)
            
            # تحليل العمليات عالية الاستهلاك
            analysis['high_cpu_processes'] = self._find_high_cpu_processes(processes)
            analysis['high_memory_processes'] = self._find_high_memory_processes(processes)
            
            # تحليل العمليات المشبوهة
            analysis['suspicious_processes'] = self._find_suspicious_processes(processes)
            
            # تحليل صحة النظام
            analysis['system_health'] = self._analyze_system_health()
            
            # حفظ التحليل
            self.save_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل العمليات: {e}")
            return {'error': str(e)}
    
    def _analyze_vscode_processes(self, vscode_procs: List[Dict]) -> Dict[str, Any]:
        """تحليل عمليات VS Code بالتفصيل"""
        if not vscode_procs:
            return {'count': 0, 'status': 'VS Code غير مفتوح'}
        
        total_cpu = sum(p['cpu_percent'] for p in vscode_procs)
        total_memory = sum(p['memory_percent'] for p in vscode_procs)
        
        analysis = {
            'count': len(vscode_procs),
            'total_cpu_usage': round(total_cpu, 2),
            'total_memory_usage': round(total_memory, 2),
            'processes': vscode_procs,
            'status': self._get_vscode_status(total_cpu, total_memory)
        }
        
        return analysis
    
    def _get_vscode_status(self, cpu: float, memory: float) -> str:
        """تحديد حالة VS Code"""
        if cpu > 50 or memory > 30:
            return '🔴 استهلاك عالي'
        elif cpu > 20 or memory > 15:
            return '🟡 استهلاك متوسط'
        else:
            return '🟢 استهلاك طبيعي'
    
    def _find_high_cpu_processes(self, processes: List[Dict]) -> List[Dict]:
        """العثور على العمليات عالية استهلاك المعالج"""
        high_cpu = [p for p in processes if p['cpu_percent'] > 10]
        return sorted(high_cpu, key=lambda x: x['cpu_percent'], reverse=True)[:10]
    
    def _find_high_memory_processes(self, processes: List[Dict]) -> List[Dict]:
        """العثور على العمليات عالية استهلاك الذاكرة"""
        high_memory = [p for p in processes if p['memory_percent'] > 5]
        return sorted(high_memory, key=lambda x: x['memory_percent'], reverse=True)[:10]
    
    def _find_suspicious_processes(self, processes: List[Dict]) -> List[Dict]:
        """العثور على العمليات المشبوهة"""
        suspicious = []
        
        # عمليات متعددة بنفس الاسم
        name_counts = {}
        for proc in processes:
            name = proc['name']
            name_counts[name] = name_counts.get(name, 0) + 1
        
        for proc in processes:
            # عمليات متكررة كثيراً
            if name_counts[proc['name']] > 10:
                suspicious.append({
                    **proc,
                    'reason': f'عمليات متكررة ({name_counts[proc["name"]]})'
                })
            
            # عمليات معلقة
            if proc['status'] == 'zombie':
                suspicious.append({
                    **proc,
                    'reason': 'عملية معلقة (zombie)'
                })
        
        return suspicious[:5]  # أول 5 عمليات مشبوهة
    
    def _analyze_system_health(self) -> Dict[str, Any]:
        """تحليل صحة النظام العامة"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            health = {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent,
                'available_memory_gb': round(memory.available / (1024**3), 2),
                'status': self._get_system_status(cpu_percent, memory.percent)
            }
            
            return health
            
        except Exception as e:
            return {'error': f'خطأ في تحليل النظام: {e}'}
    
    def _get_system_status(self, cpu: float, memory: float) -> str:
        """تحديد حالة النظام"""
        if cpu > 80 or memory > 85:
            return '🔴 حمولة عالية'
        elif cpu > 60 or memory > 70:
            return '🟡 حمولة متوسطة'
        else:
            return '🟢 أداء جيد'
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات التحسين"""
        recommendations = []
        
        if not analysis or 'error' in analysis:
            return ['❌ لا يمكن تقديم توصيات بسبب خطأ في التحليل']
        
        # توصيات VS Code
        vscode_data = analysis.get('vscode_processes', {})
        if vscode_data.get('total_cpu_usage', 0) > 30:
            recommendations.append('🔄 إعادة تشغيل VS Code لتحسين الأداء')
        
        if vscode_data.get('total_memory_usage', 0) > 20:
            recommendations.append('🧩 تعطيل بعض الإضافات غير المستخدمة')
        
        # توصيات النظام
        system_health = analysis.get('system_health', {})
        if system_health.get('memory_usage', 0) > 80:
            recommendations.append('💾 إغلاق التطبيقات غير الضرورية لتحرير الذاكرة')
        
        if system_health.get('cpu_usage', 0) > 70:
            recommendations.append('⚡ إيقاف العمليات عالية استهلاك المعالج')
        
        # توصيات العمليات المشبوهة
        if analysis.get('suspicious_processes'):
            recommendations.append('🔍 فحص العمليات المشبوهة وإزالة غير الضروري منها')
        
        if not recommendations:
            recommendations.append('✅ النظام يعمل بكفاءة جيدة!')
        
        return recommendations
