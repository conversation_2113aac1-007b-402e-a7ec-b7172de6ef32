{"timestamp": "2025-07-16T09:16:25.572080", "moved_files": [["test_db_connection.py", "database/tests/"], ["final_db_test.py", "database/tests/"], ["FINAL_PROJECT_REPORT.md", "docs/"], ["PROJECT_STRUCTURE_README.md", "docs/"], ["README_NEW.md", "docs/"], ["create_all_readmes.py", "scripts/"], ["readme_generation_report.json", "reports/"], ["setup_langsmith_env.ps1", "scripts/"], ["scripts/agents/", "archive/old_files/"], ["scripts/backup/", "archive/old_files/"], ["scripts/configs/", "archive/old_files/"], ["scripts/core/", "archive/old_files/"], ["scripts/docs/", "archive/old_files/"], ["scripts/examples/", "archive/old_files/"], ["scripts/logs/", "archive/old_files/"], ["scripts/reports/", "archive/old_files/"], ["scripts/scripts/", "archive/old_files/"], ["scripts/temp/", "archive/old_files/"], ["scripts/tests/", "archive/old_files/"], ["scripts/tools/", "archive/old_files/"]], "errors": [], "summary": {"total_moved": 20, "total_errors": 0, "success_rate": 100.0}}