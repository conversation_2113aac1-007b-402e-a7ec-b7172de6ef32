#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 منظم مشروع نظام أنوبيس
Anubis Project Organizer

سكريبت شامل لتنظيف وتنظيم ملفات ومجلدات المشروع
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
import glob


class AnubisProjectOrganizer:
    """🗂️ منظم مشروع أنوبيس"""
    
    def __init__(self, project_root: str = "."):
        """تهيئة المنظم"""
        self.project_root = Path(project_root).resolve()
        self.archive_dir = self.project_root / "archive"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.organization_log = []
        
    def log_action(self, action: str, details: str = ""):
        """تسجيل العمليات"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'details': details
        }
        self.organization_log.append(log_entry)
        print(f"📝 {action}: {details}")
    
    def create_archive_structure(self):
        """إنشاء هيكل الأرشيف"""
        archive_dirs = [
            "old_files",
            "unused_files", 
            "duplicate_reports",
            "cache_files",
            "temp_files",
            "old_databases"
        ]
        
        for dir_name in archive_dirs:
            archive_path = self.archive_dir / dir_name
            archive_path.mkdir(parents=True, exist_ok=True)
        
        self.log_action("إنشاء هيكل الأرشيف", f"تم إنشاء {len(archive_dirs)} مجلدات")
    
    def clean_pycache(self):
        """تنظيف ملفات __pycache__"""
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
                self.log_action("حذف __pycache__", str(pycache_dir.relative_to(self.project_root)))
            except Exception as e:
                self.log_action("خطأ في حذف __pycache__", f"{pycache_dir}: {e}")
    
    def archive_duplicate_reports(self):
        """أرشفة التقارير المكررة"""
        database_dir = self.project_root / "database"
        if not database_dir.exists():
            return
        
        # البحث عن ملفات التقارير المكررة
        report_patterns = [
            "*_report_*.json",
            "*_report_*.html",
            "final_validation_report_*.json"
        ]
        
        archived_count = 0
        for pattern in report_patterns:
            report_files = list(database_dir.glob(pattern))
            
            # الاحتفاظ بأحدث ملف من كل نوع
            if len(report_files) > 1:
                # ترتيب الملفات حسب تاريخ التعديل
                report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                
                # أرشفة الملفات القديمة (الاحتفاظ بأحدث ملف)
                for old_file in report_files[1:]:
                    archive_path = self.archive_dir / "duplicate_reports" / old_file.name
                    shutil.move(str(old_file), str(archive_path))
                    archived_count += 1
                    self.log_action("أرشفة تقرير مكرر", old_file.name)
        
        self.log_action("أرشفة التقارير المكررة", f"تم أرشفة {archived_count} ملف")
    
    def archive_old_databases(self):
        """أرشفة قواعد البيانات القديمة"""
        old_db_files = [
            "project_db.db",
            "database/anubis.db"
        ]
        
        archived_count = 0
        for db_file in old_db_files:
            db_path = self.project_root / db_file
            if db_path.exists():
                archive_path = self.archive_dir / "old_databases" / db_path.name
                shutil.move(str(db_path), str(archive_path))
                archived_count += 1
                self.log_action("أرشفة قاعدة بيانات قديمة", db_file)
        
        self.log_action("أرشفة قواعد البيانات القديمة", f"تم أرشفة {archived_count} ملف")
    
    def organize_test_files(self):
        """تنظيم ملفات الاختبار"""
        # نقل ملفات الاختبار المتناثرة إلى مجلد tests
        test_files_in_root = [
            "test_anubis_system.py",
            "ask_anubis.py"  # ملف اختبار تفاعلي
        ]
        
        tests_dir = self.project_root / "tests"
        tests_dir.mkdir(exist_ok=True)
        
        moved_count = 0
        for test_file in test_files_in_root:
            test_path = self.project_root / test_file
            if test_path.exists():
                new_path = tests_dir / test_file
                shutil.move(str(test_path), str(new_path))
                moved_count += 1
                self.log_action("نقل ملف اختبار", f"{test_file} -> tests/")
        
        self.log_action("تنظيم ملفات الاختبار", f"تم نقل {moved_count} ملف")
    
    def clean_empty_directories(self):
        """تنظيف المجلدات الفارغة"""
        empty_dirs = []
        
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                
                # تجاهل مجلد الأرشيف
                if self.archive_dir in dir_path.parents or dir_path == self.archive_dir:
                    continue
                
                # فحص إذا كان المجلد فارغ
                try:
                    if not any(dir_path.iterdir()):
                        empty_dirs.append(dir_path)
                except PermissionError:
                    continue
        
        # حذف المجلدات الفارغة
        for empty_dir in empty_dirs:
            try:
                empty_dir.rmdir()
                self.log_action("حذف مجلد فارغ", str(empty_dir.relative_to(self.project_root)))
            except Exception as e:
                self.log_action("خطأ في حذف مجلد فارغ", f"{empty_dir}: {e}")
    
    def create_gitignore(self):
        """إنشاء ملف .gitignore محسن"""
        gitignore_content = """# 🏺 نظام أنوبيس - ملفات مستبعدة من Git
# Anubis System - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/
workspace/logs/

# Reports and Archives
archive/
*.report
reports/
workspace/reports/

# Temporary files
*.tmp
*.temp
temp/
.tmp/

# OS
.DS_Store
Thumbs.db
desktop.ini

# Backup files
*.bak
*.backup
backups/
workspace/backups/

# Configuration (sensitive)
.env
*.key
*.pem
config/secrets.json

# Cache
.cache/
*.cache
workspace/shared_memory/

# Test outputs
test_output/
coverage/
.coverage
.pytest_cache/

# Documentation builds
docs/_build/
docs/build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version
"""
        
        gitignore_path = self.project_root / ".gitignore"
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        self.log_action("إنشاء .gitignore", "تم إنشاء ملف .gitignore محسن")
    
    def create_project_structure_doc(self):
        """إنشاء توثيق هيكل المشروع"""
        structure_doc = """# 🏺 هيكل مشروع نظام أنوبيس
# Anubis System Project Structure

## 📁 الهيكل الرئيسي

```
Universal-AI-Assistants/
├── 📋 README.md                 # دليل المشروع الرئيسي
├── 🐍 main.py                   # نقطة البداية الرئيسية
├── 📦 requirements.txt          # متطلبات Python الأساسية
├── 📦 requirements_database.txt # متطلبات قاعدة البيانات
├── 🚫 .gitignore               # ملفات مستبعدة من Git
│
├── 🧠 core/                     # النواة الأساسية للنظام
│   ├── __init__.py
│   ├── assistant_system.py     # نظام المساعد الرئيسي
│   ├── base_agent.py          # الفئة الأساسية للوكلاء
│   ├── config_manager.py      # مدير الإعدادات
│   └── logger.py              # نظام التسجيل
│
├── 🤖 agents/                   # الوكلاء الذكيون
│   ├── __init__.py
│   ├── database_agent.py       # وكيل قاعدة البيانات
│   ├── error_detector_agent.py # وكيل كشف الأخطاء
│   ├── file_organizer_agent.py # وكيل تنظيم الملفات
│   ├── memory_agent.py         # وكيل الذاكرة
│   └── project_analyzer_agent.py # وكيل تحليل المشاريع
│
├── 🗄️ database/                # قاعدة البيانات والاختبارات
│   ├── 📋 README.md            # دليل قاعدة البيانات
│   ├── 📋 TEST_SUMMARY.md      # ملخص الاختبارات
│   ├── 📋 FIXES_SUMMARY.md     # ملخص الإصلاحات
│   ├── 🔧 mysql_connector.py   # موصل MySQL
│   ├── 🔧 mysql_manager.py     # مدير قاعدة البيانات
│   ├── 🧪 comprehensive_test.py # اختبار شامل
│   ├── 🧪 simple_validation.py # تحقق مبسط
│   ├── 🧪 stress_test.py       # اختبار الضغط
│   ├── 🧪 test_connection.py   # اختبار الاتصال
│   ├── 🧪 run_all_tests.py     # تشغيل جميع الاختبارات
│   ├── ⚙️ direct_setup.py      # إعداد مباشر
│   └── 📊 *.sql               # سكريبتات SQL
│
├── ⚙️ configs/                 # ملفات الإعدادات
│   ├── database_config.json    # إعدادات قاعدة البيانات
│   └── default_config.json     # الإعدادات الافتراضية
│
├── 🔌 plugins/                 # النظام الإضافي
│   ├── __init__.py
│   ├── base_plugin.py         # الفئة الأساسية للإضافات
│   ├── plugin_manager.py      # مدير الإضافات
│   └── example_plugin.py      # مثال على إضافة
│
├── 🧪 tests/                   # اختبارات النظام
│   ├── __init__.py
│   ├── 📋 README.md           # دليل الاختبارات
│   ├── run_all_tests.py       # تشغيل جميع الاختبارات
│   ├── test_*.py              # ملفات الاختبار
│   ├── ask_anubis.py          # اختبار تفاعلي
│   ├── test_anubis_system.py  # اختبار النظام الشامل
│   └── configs/               # إعدادات الاختبار
│
├── 📚 docs/                    # التوثيق
│   ├── README.md              # دليل التوثيق
│   ├── installation_guide.md  # دليل التثبيت
│   ├── user_guide.md          # دليل المستخدم
│   └── developer_guide.md     # دليل المطور
│
├── 📜 scripts/                 # سكريبتات مساعدة
│   ├── __init__.py
│   ├── README.md              # دليل السكريبتات
│   └── quick_start.py         # بداية سريعة
│
├── 📄 templates/               # قوالب المشاريع
│   ├── README.md              # دليل القوالب
│   └── streamlit_template/    # قالب Streamlit
│
├── 💼 workspace/               # مساحة العمل
│   ├── README.md              # دليل مساحة العمل
│   ├── backups/               # النسخ الاحتياطية
│   ├── logs/                  # ملفات السجل
│   ├── reports/               # التقارير المنتجة
│   ├── knowledge_base/        # قاعدة المعرفة
│   ├── shared_memory/         # الذاكرة المشتركة
│   └── collaboration_logs/    # سجلات التعاون
│
└── 📦 archive/                 # الأرشيف (مستبعد من Git)
    ├── old_files/             # ملفات قديمة
    ├── unused_files/          # ملفات غير مستخدمة
    ├── duplicate_reports/     # تقارير مكررة
    ├── cache_files/           # ملفات التخزين المؤقت
    ├── temp_files/            # ملفات مؤقتة
    └── old_databases/         # قواعد بيانات قديمة
```

## 🎯 الملفات الرئيسية

- **main.py**: نقطة البداية الرئيسية لتشغيل النظام
- **requirements.txt**: متطلبات Python الأساسية
- **requirements_database.txt**: متطلبات قاعدة البيانات
- **.gitignore**: ملفات مستبعدة من نظام التحكم بالإصدارات

## 🔧 الاستخدام

```bash
# تشغيل النظام الرئيسي
python main.py

# تشغيل اختبارات قاعدة البيانات
python database/run_all_tests.py

# تشغيل جميع اختبارات النظام
python tests/run_all_tests.py

# اختبار تفاعلي
python tests/ask_anubis.py
```

---

**تم إنشاء هذا التوثيق تلقائياً بواسطة منظم مشروع أنوبيس** 🏺
"""
        
        structure_path = self.project_root / "PROJECT_STRUCTURE.md"
        with open(structure_path, 'w', encoding='utf-8') as f:
            f.write(structure_doc)
        
        self.log_action("إنشاء توثيق الهيكل", "تم إنشاء PROJECT_STRUCTURE.md")
    
    def save_organization_log(self):
        """حفظ سجل عمليات التنظيم"""
        log_file = self.archive_dir / f"organization_log_{self.timestamp}.json"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.organization_log, f, ensure_ascii=False, indent=2)
        
        # إنشاء ملخص نصي
        summary_file = self.archive_dir / f"organization_summary_{self.timestamp}.md"
        
        summary_content = f"""# 🧹 ملخص تنظيم مشروع أنوبيس
# Anubis Project Organization Summary

**تاريخ التنظيم:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 إحصائيات العمليات

إجمالي العمليات المنجزة: {len(self.organization_log)}

## 📝 تفاصيل العمليات

"""
        
        for i, log_entry in enumerate(self.organization_log, 1):
            summary_content += f"{i}. **{log_entry['action']}**\n"
            if log_entry['details']:
                summary_content += f"   - {log_entry['details']}\n"
            summary_content += f"   - الوقت: {log_entry['timestamp']}\n\n"
        
        summary_content += """
## 🎯 النتيجة النهائية

تم تنظيف وتنظيم مشروع نظام أنوبيس بنجاح! 🎉

- ✅ تم حذف ملفات التخزين المؤقت
- ✅ تم أرشفة الملفات القديمة والمكررة
- ✅ تم تنظيم ملفات الاختبار
- ✅ تم إنشاء هيكل أرشيف منظم
- ✅ تم إنشاء .gitignore محسن
- ✅ تم توثيق هيكل المشروع

المشروع الآن نظيف ومنظم وجاهز للتطوير! 🏺✨
"""
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"\n📄 تم حفظ سجل التنظيم في: {log_file}")
        print(f"📄 تم حفظ ملخص التنظيم في: {summary_file}")
    
    def run_full_organization(self):
        """تشغيل التنظيم الشامل"""
        print("🧹 بدء تنظيم مشروع نظام أنوبيس")
        print("=" * 50)
        
        # إنشاء هيكل الأرشيف
        self.create_archive_structure()
        
        # تنظيف ملفات التخزين المؤقت
        self.clean_pycache()
        
        # أرشفة التقارير المكررة
        self.archive_duplicate_reports()
        
        # أرشفة قواعد البيانات القديمة
        self.archive_old_databases()
        
        # تنظيم ملفات الاختبار
        self.organize_test_files()
        
        # تنظيف المجلدات الفارغة
        self.clean_empty_directories()
        
        # إنشاء .gitignore
        self.create_gitignore()
        
        # إنشاء توثيق الهيكل
        self.create_project_structure_doc()
        
        # حفظ سجل العمليات
        self.save_organization_log()
        
        print("\n" + "=" * 50)
        print("🎉 تم تنظيم المشروع بنجاح!")
        print(f"📁 الأرشيف: {self.archive_dir}")
        print(f"📊 العمليات المنجزة: {len(self.organization_log)}")


def main():
    """الدالة الرئيسية"""
    organizer = AnubisProjectOrganizer()
    organizer.run_full_organization()


if __name__ == "__main__":
    main()
