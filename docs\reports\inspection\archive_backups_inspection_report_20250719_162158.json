{"timestamp": "2025-07-19T16:21:58.888871", "system_name": "Archive and Backups System", "inspection_type": "comprehensive_analysis", "overall_health": "good", "components": {"archive": {"status": "active", "total_size": 281114, "subdirectories": {"duplicate_reports": {"exists": true, "files_count": 15, "size_bytes": 38550, "size_mb": 0.04, "status": "✅ نشط"}, "old_databases": {"exists": true, "files_count": 2, "size_bytes": 32768, "size_mb": 0.03, "status": "✅ نشط"}, "old_files": {"exists": true, "files_count": 26, "size_bytes": 159906, "size_mb": 0.15, "status": "✅ نشط"}, "old_versions": {"exists": true, "files_count": 1, "size_bytes": 53, "size_mb": 0.0, "status": "✅ نشط"}, "temp_files": {"exists": true, "files_count": 0, "size_bytes": 0, "size_mb": 0.0, "status": "⚠️ فارغ"}, "cache_files": {"exists": true, "files_count": 0, "size_bytes": 0, "size_mb": 0.0, "status": "⚠️ فارغ"}, "deprecated": {"exists": true, "files_count": 1, "size_bytes": 49, "size_mb": 0.0, "status": "✅ نشط"}, "unused_files": {"exists": true, "files_count": 3, "size_bytes": 49788, "size_mb": 0.05, "status": "✅ نشط"}}, "file_types": {".json": 18, ".md": 23, ".html": 1, ".db": 2, ".py": 10}, "issues": ["⚠️ temp_files/ فارغ", "⚠️ cache_files/ فارغ"], "strengths": ["✅ duplicate_reports/ (15 ملف، 0.04 MB)", "✅ old_databases/ (2 ملف، 0.03 MB)", "✅ old_files/ (26 ملف، 0.15 MB)", "✅ old_versions/ (1 ملف، 0.0 MB)", "✅ deprecated/ (1 ملف، 0.0 MB)", "✅ unused_files/ (3 ملف، 0.05 MB)"]}, "backup": {"status": "active", "backup_categories": {"old_agents": {"exists": true, "files_count": 5, "files": ["error_detector.py_20250716_081504", "error_detector_agent.py_20250716_081504", "file_organizer_agent.py_20250716_081504", "memory_agent.py_20250716_081504", "project_analyzer_agent.py_20250716_081504"], "status": "✅ متوفر"}}, "total_backups": 6, "issues": [], "strengths": ["✅ old_agents (5 نسخة احتياطية)", "✅ 1 ملف Python احتياطي"]}, "duplicate_reports": {"status": "cleanup_needed", "total_duplicates": 15, "report_types": {"final_validation": {"count": 7, "files": [{"name": "final_validation_report_20250714_134540.json", "size": 634, "modified": "2025-07-14T13:45:40.699150"}, {"name": "final_validation_report_20250714_134549.json", "size": 634, "modified": "2025-07-14T13:45:49.513945"}, {"name": "final_validation_report_20250714_134653.json", "size": 3559, "modified": "2025-07-14T13:46:53.659615"}, {"name": "final_validation_report_20250714_141600.json", "size": 3557, "modified": "2025-07-14T14:16:00.691380"}, {"name": "final_validation_report_20250714_142229.json", "size": 3557, "modified": "2025-07-14T14:22:29.516905"}, {"name": "final_validation_report_20250714_143107.html", "size": 6078, "modified": "2025-07-14T14:31:07.752116"}, {"name": "final_validation_report_20250714_143107.json", "size": 3558, "modified": "2025-07-14T14:31:07.752116"}]}, "simple_validation": {"count": 3, "files": [{"name": "simple_validation_report_20250714_134926.json", "size": 876, "modified": "2025-07-14T13:49:26.433350"}, {"name": "simple_validation_report_20250714_135030.json", "size": 877, "modified": "2025-07-14T13:50:30.673587"}, {"name": "simple_validation_report_20250714_135840.json", "size": 876, "modified": "2025-07-14T13:58:40.916672"}]}, "stress_test": {"count": 2, "files": [{"name": "stress_test_report_20250714_134422.json", "size": 983, "modified": "2025-07-14T13:44:22.314102"}, {"name": "stress_test_report_20250714_135031.json", "size": 985, "modified": "2025-07-14T13:50:31.869168"}]}, "test_report": {"count": 4, "files": [{"name": "stress_test_report_20250714_134422.json", "size": 983, "modified": "2025-07-14T13:44:22.314102"}, {"name": "stress_test_report_20250714_135031.json", "size": 985, "modified": "2025-07-14T13:50:31.869168"}, {"name": "test_report_20250714_134314.json", "size": 2096, "modified": "2025-07-14T13:43:14.214154"}, {"name": "test_report_20250714_135031.json", "size": 2095, "modified": "2025-07-14T13:50:31.033810"}]}, "all_tests": {"count": 1, "files": [{"name": "all_tests_report_20250714_135032.json", "size": 8185, "modified": "2025-07-14T13:50:32.243990"}]}}, "size_analysis": {"total_size_bytes": 38550, "total_size_mb": 0.04, "average_file_size": 2570.0}, "issues": [], "cleanup_candidates": ["⚠️ final_validation: 7 نسخ", "⚠️ test_report: 4 نسخ"]}, "old_databases": {"status": "archived", "databases": {"anubis.db": {"size_bytes": 32768, "size_kb": 32.0, "modified": "2025-07-14T13:25:46.275646", "status": "✅ محفوظة"}, "project_db.db": {"size_bytes": 0, "size_kb": 0.0, "modified": "2025-07-14T13:08:58.417998", "status": "✅ محفوظة"}}, "total_size": 32768, "issues": [], "strengths": ["✅ anubis.db (32.0 KB)", "✅ project_db.db (0.0 KB)"]}}, "statistics": {"total_files": 69, "total_size_mb": 0.29934120178222656, "categories_summary": {}, "space_usage": {"archive_mb": 0.27, "backups_mb": 0, "largest_category": "archive"}, "file_distribution": {}}, "issues": [], "recommendations": [], "cleanup_suggestions": ["🗑️ حذف التقارير المكررة القديمة (الاحتفاظ بالأحدث فقط)", "📋 إنشاء سياسة احتفاظ للملفات القديمة", "🔄 أتمتة عملية التنظيف الدورية", "📊 مراقبة نمو حجم الأرشيف"]}