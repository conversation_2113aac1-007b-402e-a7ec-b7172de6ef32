#!/usr/bin/env python3
"""
VS Code Process Monitor Script
سكريبت مراقبة عمليات VS Code

This script monitors and checks all processes shown in VS Code Task Manager,
specifically focusing on VS Code processes and system performance.
"""

import json
import logging
import os
import platform
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Optional

import psutil


class VSCodeProcessMonitor:
    def __init__(self):
        self.setup_logging()
        self.processes_data = []
        self.vscode_processes = []
        self.system_stats = {}
        self.extensions_data = []

    def setup_logging(self):
        """إعداد نظام التسجيل"""
        log_dir = "Universal-AI-Assistants/logs"
        os.makedirs(log_dir, exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(f"{log_dir}/vscode_process_monitor.log"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def get_system_stats(self) -> Dict:
        """الحصول على إحصائيات النظام العامة"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            # تحديد مسار القرص حسب نظام التشغيل
            if platform.system() == "Windows":
                disk = psutil.disk_usage("C:")
            else:
                disk = psutil.disk_usage("/")

            return {
                "timestamp": datetime.now().isoformat(),
                "cpu_usage": cpu_percent,
                "memory_total": memory.total,
                "memory_used": memory.used,
                "memory_percent": memory.percent,
                "disk_total": disk.total,
                "disk_used": disk.used,
                "disk_percent": (disk.used / disk.total) * 100,
                "platform": platform.system(),
            }
        except Exception as e:
            self.logger.error(f"Error getting system stats: {e}")
            return {}

    def get_process_info(self, process) -> Optional[Dict]:
        """الحصول على معلومات العملية"""
        try:
            with process.oneshot():
                memory_info = process.memory_info()
                return {
                    "pid": process.pid,
                    "name": process.name(),
                    "status": process.status(),
                    "cpu_percent": process.cpu_percent(),
                    "memory_percent": process.memory_percent(),
                    "memory_rss": memory_info.rss,
                    "memory_vms": memory_info.vms,
                    "create_time": process.create_time(),
                    "num_threads": process.num_threads(),
                    "cmdline": " ".join(process.cmdline()) if process.cmdline() else "",
                    "exe": process.exe() if hasattr(process, "exe") else "",
                    "cwd": process.cwd() if hasattr(process, "cwd") else "",
                }
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None

    def find_vscode_processes(self) -> List[Dict]:
        """البحث عن عمليات VS Code المحددة"""
        vscode_keywords = [
            "code",
            "Code.exe",
            "electron",
            "extensionHost",
            "Microsoft VS Code",
            "Visual Studio Code",
            "code-tunnel",
            "rg.exe",
            "node.exe",
        ]

        vscode_procs = []

        for proc in psutil.process_iter(["pid", "name", "cmdline"]):
            try:
                proc_info = proc.info
                proc_name = proc_info["name"].lower()
                cmdline = " ".join(proc_info["cmdline"] or []).lower()

                # التحقق من كلمات VS Code المفتاحية
                is_vscode = any(
                    keyword.lower() in proc_name or keyword.lower() in cmdline
                    for keyword in vscode_keywords
                )

                # التحقق من مسار VS Code
                if "code" in cmdline or "vscode" in cmdline or is_vscode:
                    detailed_info = self.get_process_info(proc)
                    if detailed_info:
                        # تحديد نوع العملية
                        detailed_info["process_type"] = self.identify_vscode_process_type(
                            detailed_info
                        )
                        vscode_procs.append(detailed_info)

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return vscode_procs

    def identify_vscode_process_type(self, proc_info: Dict) -> str:
        """تحديد نوع عملية VS Code"""
        cmdline = proc_info["cmdline"].lower()
        name = proc_info["name"].lower()

        if "extensionhost" in cmdline or "extensionhost" in name:
            return "Extension Host"
        elif "electron" in name or "code.exe" in name:
            return "Main Process"
        elif "rg.exe" in name:
            return "Search Process"
        elif "node.exe" in name and "vscode" in cmdline:
            return "Node Process"
        elif "code-tunnel" in cmdline:
            return "Tunnel Process"
        else:
            return "Unknown VS Code Process"

    def get_vscode_extensions(self) -> List[Dict]:
        """الحصول على قائمة إضافات VS Code"""
        try:
            result = subprocess.run(
                ["code", "--list-extensions", "--show-versions"],
                capture_output=True,
                text=True,
                timeout=30,
            )

            extensions = []
            if result.returncode == 0:
                for line in result.stdout.strip().split("\n"):
                    if line:
                        parts = line.split("@")
                        if len(parts) == 2:
                            extensions.append({"name": parts[0], "version": parts[1]})

            return extensions
        except Exception as e:
            self.logger.error(f"Error getting VS Code extensions: {e}")
            return []

    def analyze_vscode_performance(self) -> Dict:
        """تحليل أداء VS Code"""
        analysis = {
            "total_vscode_processes": len(self.vscode_processes),
            "total_memory_usage": 0,
            "total_cpu_usage": 0,
            "process_breakdown": {},
            "performance_issues": [],
            "recommendations": [],
        }

        # تحليل كل عملية VS Code
        for proc in self.vscode_processes:
            proc_type = proc["process_type"]

            if proc_type not in analysis["process_breakdown"]:
                analysis["process_breakdown"][proc_type] = {
                    "count": 0,
                    "memory_usage": 0,
                    "cpu_usage": 0,
                }

            analysis["process_breakdown"][proc_type]["count"] += 1
            analysis["process_breakdown"][proc_type]["memory_usage"] += proc["memory_percent"]
            analysis["process_breakdown"][proc_type]["cpu_usage"] += proc["cpu_percent"]

            analysis["total_memory_usage"] += proc["memory_percent"]
            analysis["total_cpu_usage"] += proc["cpu_percent"]

            # تحديد مشاكل الأداء
            if proc["memory_percent"] > 10:
                analysis["performance_issues"].append(
                    {
                        "type": "High Memory Usage",
                        "process": proc["name"],
                        "pid": proc["pid"],
                        "value": proc["memory_percent"],
                    }
                )

            if proc["cpu_percent"] > 20:
                analysis["performance_issues"].append(
                    {
                        "type": "High CPU Usage",
                        "process": proc["name"],
                        "pid": proc["pid"],
                        "value": proc["cpu_percent"],
                    }
                )

        # إضافة التوصيات
        if analysis["total_memory_usage"] > 30:
            analysis["recommendations"].append(
                "استهلاك ذاكرة VS Code عالي - فكر في إغلاق بعض النوافذ أو الإضافات"
            )

        if len(analysis["performance_issues"]) > 5:
            analysis["recommendations"].append("عدد كبير من مشاكل الأداء - أعد تشغيل VS Code")

        return analysis

    def generate_comprehensive_report(self) -> Dict:
        """إنشاء تقرير شامل"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_stats": self.system_stats,
            "vscode_analysis": self.analyze_vscode_performance(),
            "vscode_processes": self.vscode_processes,
            "extensions": self.extensions_data,
            "summary": {
                "total_processes": len(self.processes_data),
                "vscode_processes": len(self.vscode_processes),
                "system_health": self.get_system_health_status(),
            },
        }

        return report

    def get_system_health_status(self) -> str:
        """تحديد حالة صحة النظام"""
        cpu = self.system_stats.get("cpu_usage", 0)
        memory = self.system_stats.get("memory_percent", 0)

        if cpu > 80 or memory > 80:
            return "Critical"
        elif cpu > 60 or memory > 60:
            return "Warning"
        else:
            return "Good"

    def save_report(self, report: Dict, filename: str = None):
        """حفظ التقرير"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"Universal-AI-Assistants/reports/vscode_monitor_report_{timestamp}.json"

        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Report saved to: {filename}")
        return filename

    def run_full_check(self):
        """تشغيل فحص شامل"""
        self.logger.info("Starting comprehensive VS Code process check...")

        # جمع البيانات
        self.system_stats = self.get_system_stats()
        self.vscode_processes = self.find_vscode_processes()
        self.extensions_data = self.get_vscode_extensions()

        # إنشاء التقرير
        report = self.generate_comprehensive_report()

        # حفظ التقرير
        filename = self.save_report(report)

        # طباعة الملخص
        self.print_detailed_summary(report)

        return report, filename

    def print_detailed_summary(self, report: Dict):
        """طباعة ملخص مفصل"""
        print("\n" + "=" * 60)
        print("VS CODE PROCESS MONITOR - DETAILED SUMMARY")
        print("=" * 60)

        # إحصائيات النظام
        stats = report["system_stats"]
        print(f"System Health: {report['summary']['system_health']}")
        print(f"CPU Usage: {stats.get('cpu_usage', 0):.1f}%")
        print(f"Memory Usage: {stats.get('memory_percent', 0):.1f}%")
        print(f"Platform: {stats.get('platform', 'Unknown')}")

        # تحليل VS Code
        vscode_analysis = report["vscode_analysis"]
        print(f"\nVS Code Processes: {vscode_analysis['total_vscode_processes']}")
        print(f"Total VS Code Memory Usage: {vscode_analysis['total_memory_usage']:.1f}%")
        print(f"Total VS Code CPU Usage: {vscode_analysis['total_cpu_usage']:.1f}%")

        # تفصيل العمليات
        print("\nProcess Breakdown:")
        for proc_type, data in vscode_analysis["process_breakdown"].items():
            print(
                f"  {proc_type}: {data['count']} processes, "
                f"Memory: {data['memory_usage']:.1f}%, CPU: {data['cpu_usage']:.1f}%"
            )

        # مشاكل الأداء
        if vscode_analysis["performance_issues"]:
            print(f"\nPerformance Issues ({len(vscode_analysis['performance_issues'])}):")
            for issue in vscode_analysis["performance_issues"][:5]:  # أعلى 5 مشاكل
                print(
                    f"  - {issue['type']}: {issue['process']} (PID: {issue['pid']}) - {issue['value']:.1f}%"
                )

        # التوصيات
        if vscode_analysis["recommendations"]:
            print("\nRecommendations:")
            for rec in vscode_analysis["recommendations"]:
                print(f"  - {rec}")

        # الإضافات
        print(f"\nInstalled Extensions: {len(report['extensions'])}")

        print("=" * 60)


def main():
    """الدالة الرئيسية"""
    monitor = VSCodeProcessMonitor()

    try:
        report, filename = monitor.run_full_check()
        print(f"\nFull report saved to: {filename}")

    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    except Exception as e:
        print(f"Error during monitoring: {e}")


if __name__ == "__main__":
    main()
