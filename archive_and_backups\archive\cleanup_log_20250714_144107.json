[{"timestamp": "2025-07-14T14:41:07.617270", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_143107.json"}, {"timestamp": "2025-07-14T14:41:07.618428", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_142229.json"}, {"timestamp": "2025-07-14T14:41:07.619827", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_141600.json"}, {"timestamp": "2025-07-14T14:41:07.622451", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_143107.html"}, {"timestamp": "2025-07-14T14:41:07.624579", "action": "أرشفة تقرير مكرر", "details": "simple_validation_report_20250714_135840.json"}, {"timestamp": "2025-07-14T14:41:07.625336", "action": "تنظيف التقارير المكررة", "details": "تم أرشفة 5 ملف"}, {"timestamp": "2025-07-14T14:41:07.647091", "action": "أرشفة نسخة احتياطية", "details": "final_validation.backup_20250714_142217.py"}, {"timestamp": "2025-07-14T14:41:07.683911", "action": "تنظيف النسخ الاحتياطية", "details": "تم أرشفة 1 ملف"}, {"timestamp": "2025-07-14T14:41:07.686169", "action": "أرشفة سكريبت غير مستخدم", "details": "anubis_auto_fix.py"}, {"timestamp": "2025-07-14T14:41:07.687622", "action": "أرشفة سكريبت غير مستخدم", "details": "anubis_error_fix.py"}, {"timestamp": "2025-07-14T14:41:07.689489", "action": "أرشفة سكريبت غير مستخدم", "details": "organize_project.py"}, {"timestamp": "2025-07-14T14:41:07.689605", "action": "تنظيف السكريبتات", "details": "تم أرشفة 3 ملف"}, {"timestamp": "2025-07-14T14:41:07.691158", "action": "أرشفة وثيقة مكررة", "details": "FINAL_VALIDATION_FIXES.md"}, {"timestamp": "2025-07-14T14:41:07.692777", "action": "أرشفة وثيقة مكررة", "details": "FILE_SPLIT_REPORT.md"}, {"timestamp": "2025-07-14T14:41:07.694225", "action": "أرشفة وثيقة مكررة", "details": "README_SPLIT.md"}, {"timestamp": "2025-07-14T14:41:07.694406", "action": "تنظيف الوثائق المكررة", "details": "تم أرشفة 3 ملف"}, {"timestamp": "2025-07-14T14:41:07.695969", "action": "أرشفة وثيقة جذر", "details": "ANUBIS_ERROR_CORRECTION_REPORT.md"}, {"timestamp": "2025-07-14T14:41:07.697202", "action": "أرشفة وثيقة جذر", "details": "ORGANIZATION_COMPLETE.md"}, {"timestamp": "2025-07-14T14:41:07.697299", "action": "تنظيف وثائق الجذر", "details": "تم أرشفة 2 ملف"}, {"timestamp": "2025-07-14T14:41:07.713727", "action": "حذف __pycache__", "details": "agents\\__pycache__"}, {"timestamp": "2025-07-14T14:41:07.715808", "action": "حذف __pycache__", "details": "core\\__pycache__"}, {"timestamp": "2025-07-14T14:41:07.717149", "action": "حذف __pycache__", "details": "database\\__pycache__"}, {"timestamp": "2025-07-14T14:41:07.718840", "action": "نقل ملف قاعدة البيانات", "details": "comprehensive_test.py -> tests/"}, {"timestamp": "2025-07-14T14:41:07.719898", "action": "نقل ملف قاعدة البيانات", "details": "run_all_tests.py -> tests/"}, {"timestamp": "2025-07-14T14:41:07.720658", "action": "نقل ملف قاعدة البيانات", "details": "stress_test.py -> tests/"}, {"timestamp": "2025-07-14T14:41:07.721989", "action": "نقل ملف قاعدة البيانات", "details": "test_connection.py -> tests/"}, {"timestamp": "2025-07-14T14:41:07.723340", "action": "نقل ملف قاعدة البيانات", "details": "direct_setup.py -> setup/"}, {"timestamp": "2025-07-14T14:41:07.724126", "action": "نقل ملف قاعدة البيانات", "details": "setup_database.py -> setup/"}, {"timestamp": "2025-07-14T14:41:07.725040", "action": "نقل ملف قاعدة البيانات", "details": "create_mysql_database.sql -> setup/"}, {"timestamp": "2025-07-14T14:41:07.726331", "action": "نقل ملف قاعدة البيانات", "details": "database_validator.py -> core/"}, {"timestamp": "2025-07-14T14:41:07.727161", "action": "نقل ملف قاعدة البيانات", "details": "final_validation_runner.py -> core/"}, {"timestamp": "2025-07-14T14:41:07.728251", "action": "نقل ملف قاعدة البيانات", "details": "mysql_connector.py -> core/"}, {"timestamp": "2025-07-14T14:41:07.729093", "action": "نقل ملف قاعدة البيانات", "details": "mysql_manager.py -> core/"}, {"timestamp": "2025-07-14T14:41:07.730195", "action": "نقل ملف قاعدة البيانات", "details": "README.md -> docs/"}, {"timestamp": "2025-07-14T14:41:07.730876", "action": "نقل ملف قاعدة البيانات", "details": "TEST_SUMMARY.md -> docs/"}, {"timestamp": "2025-07-14T14:41:07.731665", "action": "نقل ملف قاعدة البيانات", "details": "FIXES_SUMMARY.md -> docs/"}, {"timestamp": "2025-07-14T14:41:07.731741", "action": "تنظيم ملفات قاعدة البيانات", "details": "تم نقل 14 ملف"}, {"timestamp": "2025-07-14T14:41:07.732536", "action": "تحديث README الرئيسي", "details": "تم تحديث ملف README.md"}]