{"scan_date": "2025-07-18T17:23:37.690950", "project_root": "C:\\Users\\<USER>\\Universal-AI-Assistants", "directories": {"anubis": {"path": "anubis", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "files": [{"name": "INDEX.md", "size": 7743, "size_mb": 0.007, "modified": "2025-07-18T02:32:31.109117", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 199, "non_empty_lines": 146}, {"name": "main.py", "size": 9892, "size_mb": 0.009, "modified": "2025-07-18T16:21:14.248741", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 292, "non_empty_lines": 217}, {"name": "MAIN_README.md", "size": 6307, "size_mb": 0.006, "modified": "2025-07-18T02:31:14.284912", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 189, "non_empty_lines": 137}, {"name": "ORGANIZED_FILES_README.md", "size": 7427, "size_mb": 0.007, "modified": "2025-07-18T06:26:48.367835", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 238, "non_empty_lines": 177}, {"name": "PROJECT_README.md", "size": 11924, "size_mb": 0.011, "modified": "2025-07-18T02:24:28.175089", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 293, "non_empty_lines": 220}, {"name": "README.md", "size": 9891, "size_mb": 0.009, "modified": "2025-07-18T02:22:32.156338", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 250, "non_empty_lines": 193}, {"name": "__init__.py", "size": 61, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.553049", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1, "non_empty_lines": 1}], "subdirectories": {"agents": {"path": "anubis\\agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents", "files": [{"name": "README.md", "size": 43, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.085368", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}, {"name": "__init__.py", "size": 61, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.554012", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1, "non_empty_lines": 1}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 104, "file_types": {".md": 1, ".py": 1}, "python_files": 1, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "api": {"path": "anubis\\api", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api", "files": [{"name": "anubis_api_server.py", "size": 15127, "size_mb": 0.014, "modified": "2025-07-18T16:21:13.927978", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 444, "non_empty_lines": 360}, {"name": "anubis_openapi.json", "size": 14363, "size_mb": 0.014, "modified": "2025-07-16T11:06:56.517055", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 556, "non_empty_lines": 556}, {"name": "__init__.py", "size": 61, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.555019", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1, "non_empty_lines": 1}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 29551, "file_types": {".py": 2, ".json": 1}, "python_files": 2, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.028}}, "api.backup_20250718_160241": {"path": "anubis\\api.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api.backup_20250718_160241", "files": [{"name": "README.md", "size": 51, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.085368", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 51, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "configs": {"path": "anubis\\configs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\configs", "files": [{"name": ".env.langsmith", "size": 898, "size_mb": 0.001, "modified": "2025-07-16T08:34:15.289561", "extension": ".langsmith", "type": "Unknown", "is_empty": false}, {"name": "ai_config.json", "size": 2921, "size_mb": 0.003, "modified": "2025-07-16T06:20:04.949794", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 61, "non_empty_lines": 61}, {"name": "database_config.json", "size": 1484, "size_mb": 0.001, "modified": "2025-07-14T13:28:51.414849", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 57, "non_empty_lines": 57}, {"name": "default_config.json", "size": 4167, "size_mb": 0.004, "modified": "2025-07-16T07:46:12.567710", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 147, "non_empty_lines": 147}, {"name": "langsmith_config.json", "size": 1538, "size_mb": 0.001, "modified": "2025-07-16T08:25:25.841099", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 62, "non_empty_lines": 62}, {"name": "memory.json", "size": 227, "size_mb": 0.0, "modified": "2025-07-16T07:41:12.903887", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 11, "non_empty_lines": 11}, {"name": "ollama_check_report_20250716_092556.json", "size": 1840, "size_mb": 0.002, "modified": "2025-07-16T09:25:56.560832", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 74, "non_empty_lines": 74}, {"name": "openapi.json", "size": 543501, "size_mb": 0.518, "modified": "2025-07-16T08:33:25.938796", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 1, "non_empty_lines": 1}, {"name": "README.md", "size": 1195, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.375319", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 52, "non_empty_lines": 33}, {"name": "README.md.backup_20250718_160241", "size": 40, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.092260", "extension": ".backup_20250718_160241", "type": "Unknown", "is_empty": false}, {"name": "system_paths.json", "size": 799, "size_mb": 0.001, "modified": "2025-07-16T07:29:18.525689", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 17, "non_empty_lines": 17}], "subdirectories": {}, "statistics": {"total_files": 11, "total_size": 558610, "file_types": {".langsmith": 1, ".json": 8, ".md": 1, ".backup_20250718_160241": 1}, "python_files": 0, "config_files": 8, "doc_files": 1, "script_files": 0, "total_size_mb": 0.533}}, "core": {"path": "anubis\\core", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core", "files": [{"name": "ai_integration.py", "size": 12371, "size_mb": 0.012, "modified": "2025-07-18T16:21:13.768517", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 331, "non_empty_lines": 264}, {"name": "assistant_system.py", "size": 15427, "size_mb": 0.015, "modified": "2025-07-18T16:21:13.866022", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 388, "non_empty_lines": 317}, {"name": "base_agent.py", "size": 12914, "size_mb": 0.012, "modified": "2025-07-18T16:21:14.034102", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 341, "non_empty_lines": 279}, {"name": "config_manager.py", "size": 13698, "size_mb": 0.013, "modified": "2025-07-18T16:21:13.966435", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 335, "non_empty_lines": 296}, {"name": "langsmith_wrapper.py", "size": 3955, "size_mb": 0.004, "modified": "2025-07-18T16:21:13.564586", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 120, "non_empty_lines": 99}, {"name": "logger.py", "size": 10126, "size_mb": 0.01, "modified": "2025-07-18T16:21:13.676909", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 286, "non_empty_lines": 224}, {"name": "README.md", "size": 6520, "size_mb": 0.006, "modified": "2025-07-16T09:00:32.930322", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 188, "non_empty_lines": 140}, {"name": "__init__.py", "size": 577, "size_mb": 0.001, "modified": "2025-07-18T16:17:33.118469", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 15, "non_empty_lines": 12}], "subdirectories": {}, "statistics": {"total_files": 8, "total_size": 75588, "file_types": {".py": 7, ".md": 1}, "python_files": 7, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.072}}, "core.backup_20250718_160241": {"path": "anubis\\core.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core.backup_20250718_160241", "files": [{"name": "README.md", "size": 54, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.083882", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 54, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "database": {"path": "anubis\\database", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database", "files": [{"name": "all_tests_report_20250714_135032.html", "size": 3703, "size_mb": 0.004, "modified": "2025-07-14T13:50:32.243990", "extension": ".html", "type": "HTML File", "is_empty": false, "lines": 77, "non_empty_lines": 73}, {"name": "anubis_database.py", "size": 15955, "size_mb": 0.015, "modified": "2025-07-18T16:21:13.831114", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 435, "non_empty_lines": 369}, {"name": "final_validation_report_20250714_143312.html", "size": 6078, "size_mb": 0.006, "modified": "2025-07-14T14:33:12.413975", "extension": ".html", "type": "HTML File", "is_empty": false, "lines": 163, "non_empty_lines": 152}, {"name": "final_validation_report_20250714_143312.json", "size": 3558, "size_mb": 0.003, "modified": "2025-07-14T14:33:12.413975", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "final_validation_report_20250714_144832.html", "size": 6078, "size_mb": 0.006, "modified": "2025-07-14T14:48:32.137101", "extension": ".html", "type": "HTML File", "is_empty": false, "lines": 163, "non_empty_lines": 152}, {"name": "final_validation_report_20250714_144832.json", "size": 3557, "size_mb": 0.003, "modified": "2025-07-14T14:48:32.136084", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "README.md", "size": 9220, "size_mb": 0.009, "modified": "2025-07-16T08:59:02.788247", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 294, "non_empty_lines": 228}, {"name": "simple_validation.py", "size": 12259, "size_mb": 0.012, "modified": "2025-07-18T16:21:14.147363", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 358, "non_empty_lines": 286}, {"name": "simple_validation_report_20250714_141135.json", "size": 878, "size_mb": 0.001, "modified": "2025-07-14T14:11:35.676327", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 37, "non_empty_lines": 37}, {"name": "__init__.py", "size": 61, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.556020", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1, "non_empty_lines": 1}], "subdirectories": {"core": {"path": "anubis\\database\\core", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core", "files": [{"name": "database_validator.py", "size": 20835, "size_mb": 0.02, "modified": "2025-07-18T16:21:13.944833", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 549, "non_empty_lines": 475}, {"name": "final_validation_runner.py", "size": 12226, "size_mb": 0.012, "modified": "2025-07-18T16:21:13.605243", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 341, "non_empty_lines": 289}, {"name": "mysql_connector.py", "size": 11556, "size_mb": 0.011, "modified": "2025-07-18T16:21:13.684686", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 317, "non_empty_lines": 258}, {"name": "mysql_manager.py", "size": 10937, "size_mb": 0.01, "modified": "2025-07-18T16:21:13.891991", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 285, "non_empty_lines": 243}], "subdirectories": {}, "statistics": {"total_files": 4, "total_size": 55554, "file_types": {".py": 4}, "python_files": 4, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.053}}, "docs": {"path": "anubis\\database\\docs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\docs", "files": [{"name": "FIXES_SUMMARY.md", "size": 7317, "size_mb": 0.007, "modified": "2025-07-14T13:59:36.980125", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 242, "non_empty_lines": 185}, {"name": "README.md", "size": 7631, "size_mb": 0.007, "modified": "2025-07-14T13:36:56.926287", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 254, "non_empty_lines": 209}, {"name": "TEST_SUMMARY.md", "size": 7671, "size_mb": 0.007, "modified": "2025-07-14T13:51:41.546501", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 184, "non_empty_lines": 137}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 22619, "file_types": {".md": 3}, "python_files": 0, "config_files": 0, "doc_files": 3, "script_files": 0, "total_size_mb": 0.022}}, "setup": {"path": "anubis\\database\\setup", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup", "files": [{"name": "create_mysql_database.sql", "size": 6684, "size_mb": 0.006, "modified": "2025-07-14T13:28:08.809976", "extension": ".sql", "type": "SQL Script", "is_empty": false}, {"name": "direct_setup.py", "size": 12518, "size_mb": 0.012, "modified": "2025-07-18T16:21:13.886138", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 347, "non_empty_lines": 302}, {"name": "setup_database.py", "size": 6908, "size_mb": 0.007, "modified": "2025-07-18T16:21:13.932138", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 188, "non_empty_lines": 149}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 26110, "file_types": {".sql": 1, ".py": 2}, "python_files": 2, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.025}}, "tests": {"path": "anubis\\database\\tests", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests", "files": [{"name": "comprehensive_test.py", "size": 22454, "size_mb": 0.021, "modified": "2025-07-18T16:21:14.348078", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 579, "non_empty_lines": 487}, {"name": "final_db_test.py", "size": 2545, "size_mb": 0.002, "modified": "2025-07-18T16:12:12.000180", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 74, "non_empty_lines": 57}, {"name": "run_all_tests.py", "size": 12706, "size_mb": 0.012, "modified": "2025-07-18T16:21:14.192213", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 309, "non_empty_lines": 259}, {"name": "stress_test.py", "size": 15008, "size_mb": 0.014, "modified": "2025-07-18T16:21:14.410068", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 408, "non_empty_lines": 323}, {"name": "test_connection.py", "size": 2679, "size_mb": 0.003, "modified": "2025-07-18T16:21:14.002277", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 79, "non_empty_lines": 62}, {"name": "test_db_connection.py", "size": 5330, "size_mb": 0.005, "modified": "2025-07-18T16:21:14.103275", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 151, "non_empty_lines": 121}], "subdirectories": {}, "statistics": {"total_files": 6, "total_size": 60722, "file_types": {".py": 6}, "python_files": 6, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.058}}}, "statistics": {"total_files": 10, "total_size": 61347, "file_types": {".html": 3, ".py": 3, ".json": 3, ".md": 1}, "python_files": 3, "config_files": 3, "doc_files": 4, "script_files": 0, "total_size_mb": 0.059}}, "database.backup_20250718_160241": {"path": "anubis\\database.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database.backup_20250718_160241", "files": [{"name": "README.md", "size": 60, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.085368", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 60, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "docs": {"path": "anubis\\docs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\docs", "files": [{"name": "AI_STATUS_REPORT.md", "size": 7390, "size_mb": 0.007, "modified": "2025-07-16T01:13:08.359613", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 184, "non_empty_lines": 138}, {"name": "COMPLETE_SUCCESS_SUMMARY.md", "size": 12777, "size_mb": 0.012, "modified": "2025-07-16T09:21:37.332872", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 302, "non_empty_lines": 241}, {"name": "COMPREHENSIVE_PROJECT_REPORT.md", "size": 11202, "size_mb": 0.011, "modified": "2025-07-16T07:48:21.055856", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 273, "non_empty_lines": 210}, {"name": "developer_guide.md", "size": 29722, "size_mb": 0.028, "modified": "2025-07-14T12:35:18.892644", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 996, "non_empty_lines": 798}, {"name": "FINAL_COMPREHENSIVE_REPORT.md", "size": 7939, "size_mb": 0.008, "modified": "2025-07-16T06:54:30.012439", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 208, "non_empty_lines": 162}, {"name": "FINAL_FILE_ORGANIZATION_REPORT.md", "size": 12190, "size_mb": 0.012, "modified": "2025-07-16T09:19:49.271307", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 317, "non_empty_lines": 244}, {"name": "FINAL_ORGANIZATION_REPORT.md", "size": 10524, "size_mb": 0.01, "modified": "2025-07-14T14:49:41.064957", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 263, "non_empty_lines": 202}, {"name": "FINAL_PROJECT_COMPLETION_REPORT.md", "size": 12663, "size_mb": 0.012, "modified": "2025-07-16T07:55:34.677573", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 298, "non_empty_lines": 234}, {"name": "FINAL_PROJECT_REPORT.md", "size": 12080, "size_mb": 0.012, "modified": "2025-07-16T09:10:17.445801", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 291, "non_empty_lines": 227}, {"name": "FIXES_COMPLETED_REPORT.md", "size": 6542, "size_mb": 0.006, "modified": "2025-07-16T06:40:07.671511", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 181, "non_empty_lines": 136}, {"name": "installation_guide.md", "size": 7591, "size_mb": 0.007, "modified": "2025-07-14T12:13:08.319322", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 311, "non_empty_lines": 238}, {"name": "LANGSMITH_FINAL_SUCCESS_REPORT.md", "size": 11336, "size_mb": 0.011, "modified": "2025-07-16T08:44:10.557906", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 316, "non_empty_lines": 241}, {"name": "LANGSMITH_INTEGRATION_ANALYSIS.md", "size": 9552, "size_mb": 0.009, "modified": "2025-07-16T06:50:10.641671", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 279, "non_empty_lines": 219}, {"name": "LANGSMITH_OPTIMIZATION_GUIDE.md", "size": 14277, "size_mb": 0.014, "modified": "2025-07-16T08:30:54.109039", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 349, "non_empty_lines": 267}, {"name": "LANGSMITH_SETUP_GUIDE.md", "size": 3004, "size_mb": 0.003, "modified": "2025-07-16T08:25:25.853061", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 113, "non_empty_lines": 87}, {"name": "LANGSMITH_STATUS_REPORT.md", "size": 8762, "size_mb": 0.008, "modified": "2025-07-16T08:38:19.720555", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 261, "non_empty_lines": 201}, {"name": "MAIN_PY_FIXES_REPORT.md", "size": 8733, "size_mb": 0.008, "modified": "2025-07-16T08:03:56.752380", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 240, "non_empty_lines": 185}, {"name": "N8N_INTEGRATION_GUIDE.md", "size": 11937, "size_mb": 0.011, "modified": "2025-07-16T11:18:24.505564", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 443, "non_empty_lines": 361}, {"name": "OLLAMA_INTEGRATION_REPORT.md", "size": 10227, "size_mb": 0.01, "modified": "2025-07-16T09:31:11.922437", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 310, "non_empty_lines": 231}, {"name": "project_index.json", "size": 3994, "size_mb": 0.004, "modified": "2025-07-16T07:44:47.265025", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 158, "non_empty_lines": 158}, {"name": "PROJECT_INDEX.md", "size": 10198, "size_mb": 0.01, "modified": "2025-07-16T07:49:17.333291", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 301, "non_empty_lines": 244}, {"name": "PROJECT_STRUCTURE.md", "size": 13600, "size_mb": 0.013, "modified": "2025-07-14T14:47:52.237194", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 257, "non_empty_lines": 222}, {"name": "PROJECT_STRUCTURE_README.md", "size": 9862, "size_mb": 0.009, "modified": "2025-07-16T09:08:45.172020", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 267, "non_empty_lines": 218}, {"name": "PROJECT_SUMMARY.md", "size": 9127, "size_mb": 0.009, "modified": "2025-07-16T16:57:52.123847", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 216, "non_empty_lines": 167}, {"name": "QUICK_GUIDE.md", "size": 5139, "size_mb": 0.005, "modified": "2025-07-16T16:55:58.628733", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 167, "non_empty_lines": 128}, {"name": "README.md", "size": 1645, "size_mb": 0.002, "modified": "2025-07-16T09:06:13.371851", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 66, "non_empty_lines": 47}, {"name": "README.md.backup_20250718_160241", "size": 39, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.094284", "extension": ".backup_20250718_160241", "type": "Unknown", "is_empty": false}, {"name": "README_NEW.md", "size": 11301, "size_mb": 0.011, "modified": "2025-07-16T07:47:10.122137", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 312, "non_empty_lines": 240}, {"name": "requirements.txt", "size": 2329, "size_mb": 0.002, "modified": "2025-07-14T12:13:43.411099", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 54, "non_empty_lines": 42}, {"name": "requirements_database.txt", "size": 947, "size_mb": 0.001, "modified": "2025-07-14T13:30:27.249762", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 55, "non_empty_lines": 41}, {"name": "user_guide.md", "size": 16322, "size_mb": 0.016, "modified": "2025-07-14T12:24:58.602080", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 653, "non_empty_lines": 524}, {"name": "vscode_72_processes_emergency_guide.md", "size": 5752, "size_mb": 0.005, "modified": "2025-07-16T16:26:12.719338", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 204, "non_empty_lines": 159}], "subdirectories": {}, "statistics": {"total_files": 32, "total_size": 298703, "file_types": {".md": 28, ".json": 1, ".backup_20250718_160241": 1, ".txt": 2}, "python_files": 0, "config_files": 1, "doc_files": 30, "script_files": 0, "total_size_mb": 0.285}}, "examples": {"path": "anubis\\examples", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\examples", "files": [{"name": "README.md", "size": 1062, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.444284", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 46, "non_empty_lines": 27}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 1062, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.001}}, "examples.backup_20250718_160241": {"path": "anubis\\examples.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\examples.backup_20250718_160241", "files": [{"name": "README.md", "size": 45, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.103599", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 45, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "plugins": {"path": "anubis\\plugins", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins", "files": [{"name": "base_plugin.py", "size": 2890, "size_mb": 0.003, "modified": "2025-07-18T16:21:14.082959", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 93, "non_empty_lines": 75}, {"name": "example_plugin.py", "size": 3811, "size_mb": 0.004, "modified": "2025-07-18T16:21:14.118331", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 99, "non_empty_lines": 84}, {"name": "plugin_manager.py", "size": 7795, "size_mb": 0.007, "modified": "2025-07-18T16:21:14.194217", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 228, "non_empty_lines": 178}, {"name": "README.md", "size": 9111, "size_mb": 0.009, "modified": "2025-07-16T09:17:23.771645", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 301, "non_empty_lines": 230}, {"name": "__init__.py", "size": 385, "size_mb": 0.0, "modified": "2025-07-18T16:17:33.316798", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 12, "non_empty_lines": 9}], "subdirectories": {}, "statistics": {"total_files": 5, "total_size": 23992, "file_types": {".py": 4, ".md": 1}, "python_files": 4, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.023}}, "plugins.backup_20250718_160241": {"path": "anubis\\plugins.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins.backup_20250718_160241", "files": [{"name": "README.md", "size": 40, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.085368", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 40, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "scripts": {"path": "anubis\\scripts", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts", "files": [{"name": "activate_real_langsmith.py", "size": 16617, "size_mb": 0.016, "modified": "2025-07-18T16:21:14.504087", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 449, "non_empty_lines": 354}, {"name": "agents_cleanup.py", "size": 17755, "size_mb": 0.017, "modified": "2025-07-18T16:21:14.308511", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 499, "non_empty_lines": 390}, {"name": "check_ollama.py", "size": 9245, "size_mb": 0.009, "modified": "2025-07-18T16:21:14.388308", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 255, "non_empty_lines": 196}, {"name": "check_ready.py", "size": 1211, "size_mb": 0.001, "modified": "2025-07-18T16:21:14.126112", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 50, "non_empty_lines": 38}, {"name": "code_quality_checker.py", "size": 13626, "size_mb": 0.013, "modified": "2025-07-18T16:21:14.517518", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 361, "non_empty_lines": 291}, {"name": "complete_file_organizer.py", "size": 13508, "size_mb": 0.013, "modified": "2025-07-18T16:21:14.534967", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 346, "non_empty_lines": 289}, {"name": "comprehensive_vscode_scan.ps1", "size": 7382, "size_mb": 0.007, "modified": "2025-07-16T16:31:56.426901", "extension": ".ps1", "type": "PowerShell Script", "is_empty": false}, {"name": "create_all_readmes.py", "size": 11175, "size_mb": 0.011, "modified": "2025-07-18T16:21:14.469039", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 331, "non_empty_lines": 254}, {"name": "emergency_vscode_check.py", "size": 5787, "size_mb": 0.006, "modified": "2025-07-18T16:21:14.359814", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 173, "non_empty_lines": 134}, {"name": "fix_agents_with_gemini.py", "size": 22169, "size_mb": 0.021, "modified": "2025-07-18T16:21:14.388308", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 547, "non_empty_lines": 441}, {"name": "gemini_cli_helper.py", "size": 9621, "size_mb": 0.009, "modified": "2025-07-18T16:21:14.431847", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 271, "non_empty_lines": 217}, {"name": "gemini_integration_system.py", "size": 15124, "size_mb": 0.014, "modified": "2025-07-18T16:21:14.609591", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 395, "non_empty_lines": 315}, {"name": "langsmith_integration_demo.py", "size": 15855, "size_mb": 0.015, "modified": "2025-07-18T16:21:14.865746", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 367, "non_empty_lines": 298}, {"name": "organize_all_files.py", "size": 12074, "size_mb": 0.012, "modified": "2025-07-18T16:21:14.780116", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 317, "non_empty_lines": 257}, {"name": "organize_project_files.py", "size": 13695, "size_mb": 0.013, "modified": "2025-07-18T16:21:14.803744", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 368, "non_empty_lines": 292}, {"name": "process_monitor.py", "size": 10652, "size_mb": 0.01, "modified": "2025-07-18T16:21:14.783109", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 300, "non_empty_lines": 242}, {"name": "quick_start.py", "size": 5407, "size_mb": 0.005, "modified": "2025-07-18T16:21:14.576512", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 161, "non_empty_lines": 126}, {"name": "quick_vscode_check.py", "size": 7067, "size_mb": 0.007, "modified": "2025-07-18T16:21:14.689820", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 205, "non_empty_lines": 154}, {"name": "quick_vscode_count.ps1", "size": 3757, "size_mb": 0.004, "modified": "2025-07-16T16:28:52.599641", "extension": ".ps1", "type": "PowerShell Script", "is_empty": false}, {"name": "README.backup_20250716_091625.md", "size": 1456, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.367364", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 60, "non_empty_lines": 41}, {"name": "README.md", "size": 8705, "size_mb": 0.008, "modified": "2025-07-16T09:07:43.549331", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 266, "non_empty_lines": 206}, {"name": "README.md.backup_20250718_160241", "size": 44, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.094284", "extension": ".backup_20250718_160241", "type": "Unknown", "is_empty": false}, {"name": "run_vscode_check.bat", "size": 4624, "size_mb": 0.004, "modified": "2025-07-16T16:21:16.885655", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "run_vscode_monitor.ps1", "size": 9606, "size_mb": 0.009, "modified": "2025-07-16T14:00:12.646410", "extension": ".ps1", "type": "PowerShell Script", "is_empty": false}, {"name": "run_vscode_monitor.py", "size": 7857, "size_mb": 0.007, "modified": "2025-07-18T16:21:14.753142", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 255, "non_empty_lines": 201}, {"name": "setup_langsmith.py", "size": 17640, "size_mb": 0.017, "modified": "2025-07-18T16:21:14.848133", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 526, "non_empty_lines": 417}, {"name": "setup_langsmith_env.ps1", "size": 482, "size_mb": 0.0, "modified": "2025-07-16T08:34:32.719521", "extension": ".ps1", "type": "PowerShell Script", "is_empty": false}, {"name": "simple_langsmith_test.py", "size": 7086, "size_mb": 0.007, "modified": "2025-07-18T16:12:12.855206", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 214, "non_empty_lines": 170}, {"name": "smart_workflow_demo.py", "size": 20764, "size_mb": 0.02, "modified": "2025-07-18T16:21:15.250604", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 438, "non_empty_lines": 366}, {"name": "start_anubis_n8n_system.py", "size": 11103, "size_mb": 0.011, "modified": "2025-07-18T16:21:14.976513", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 336, "non_empty_lines": 277}, {"name": "test_langsmith_integration.py", "size": 15905, "size_mb": 0.015, "modified": "2025-07-18T16:21:15.166627", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 413, "non_empty_lines": 330}, {"name": "test_ollama_langsmith.py", "size": 2853, "size_mb": 0.003, "modified": "2025-07-18T16:21:14.726078", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 86, "non_empty_lines": 66}, {"name": "vscode_emergency_cleanup.py", "size": 13096, "size_mb": 0.012, "modified": "2025-07-18T16:21:15.096455", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 352, "non_empty_lines": 281}, {"name": "vscode_heavy_load_analyzer.py", "size": 15378, "size_mb": 0.015, "modified": "2025-07-18T16:21:15.317267", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 408, "non_empty_lines": 341}, {"name": "VSCODE_MONITOR_README.md", "size": 6807, "size_mb": 0.006, "modified": "2025-07-16T16:17:54.933563", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 248, "non_empty_lines": 197}, {"name": "vscode_process_alerts.py", "size": 13904, "size_mb": 0.013, "modified": "2025-07-18T16:21:15.223736", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 372, "non_empty_lines": 309}, {"name": "vscode_process_monitor.py", "size": 13740, "size_mb": 0.013, "modified": "2025-07-18T16:21:15.334720", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 364, "non_empty_lines": 303}, {"name": "__init__.py", "size": 506, "size_mb": 0.0, "modified": "2025-07-18T16:12:12.052082", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 14, "non_empty_lines": 10}], "subdirectories": {}, "statistics": {"total_files": 38, "total_size": 383283, "file_types": {".py": 29, ".ps1": 4, ".md": 3, ".backup_20250718_160241": 1, ".bat": 1}, "python_files": 29, "config_files": 0, "doc_files": 3, "script_files": 5, "total_size_mb": 0.366}}, "templates": {"path": "anubis\\templates", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates", "files": [{"name": "README.md", "size": 4792, "size_mb": 0.005, "modified": "2025-07-14T12:10:50.654545", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 120, "non_empty_lines": 96}], "subdirectories": {"streamlit_template": {"path": "anubis\\templates\\streamlit_template", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates\\streamlit_template", "files": [{"name": "main.py", "size": 7873, "size_mb": 0.008, "modified": "2025-07-18T16:21:15.127039", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 241, "non_empty_lines": 198}, {"name": "requirements.txt", "size": 300, "size_mb": 0.0, "modified": "2025-07-14T12:11:45.666943", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 15, "non_empty_lines": 11}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 8173, "file_types": {".py": 1, ".txt": 1}, "python_files": 1, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.008}}}, "statistics": {"total_files": 1, "total_size": 4792, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.005}}, "templates.backup_20250718_160241": {"path": "anubis\\templates.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates.backup_20250718_160241", "files": [{"name": "README.md", "size": 44, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.101016", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 44, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "tests": {"path": "anubis\\tests", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests", "files": [{"name": "ask_anubis.py", "size": 13157, "size_mb": 0.013, "modified": "2025-07-18T16:21:15.269698", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 320, "non_empty_lines": 253}, {"name": "comprehensive_agents_test.py", "size": 14932, "size_mb": 0.014, "modified": "2025-07-18T16:21:15.418348", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 398, "non_empty_lines": 313}, {"name": "comprehensive_system_test.py", "size": 15776, "size_mb": 0.015, "modified": "2025-07-18T16:21:15.393498", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 422, "non_empty_lines": 335}, {"name": "quick_ai_test.py", "size": 5367, "size_mb": 0.005, "modified": "2025-07-18T16:21:15.049790", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 158, "non_empty_lines": 120}, {"name": "README.backup_20250716_091625.md", "size": 4671, "size_mb": 0.004, "modified": "2025-07-14T12:46:24.603492", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 140, "non_empty_lines": 108}, {"name": "README.md", "size": 9491, "size_mb": 0.009, "modified": "2025-07-16T09:05:04.016070", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 247, "non_empty_lines": 186}, {"name": "run_all_tests.py", "size": 10608, "size_mb": 0.01, "modified": "2025-07-18T16:21:15.355670", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 320, "non_empty_lines": 247}, {"name": "test_agents.py", "size": 2808, "size_mb": 0.003, "modified": "2025-07-18T16:21:15.148633", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 88, "non_empty_lines": 68}, {"name": "test_ai_fixed.py", "size": 13074, "size_mb": 0.012, "modified": "2025-07-18T16:21:15.538931", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 341, "non_empty_lines": 267}, {"name": "test_ai_integration.py", "size": 14041, "size_mb": 0.013, "modified": "2025-07-18T16:21:15.578515", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 351, "non_empty_lines": 275}, {"name": "test_anubis_system.py", "size": 13018, "size_mb": 0.012, "modified": "2025-07-18T16:21:15.510083", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 274, "non_empty_lines": 218}, {"name": "test_enhanced_error_detector.py", "size": 10035, "size_mb": 0.01, "modified": "2025-07-18T16:21:15.467003", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 264, "non_empty_lines": 208}, {"name": "test_error_detector.py", "size": 11726, "size_mb": 0.011, "modified": "2025-07-18T16:21:15.577802", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 336, "non_empty_lines": 276}, {"name": "test_fixed_agents.py", "size": 816, "size_mb": 0.001, "modified": "2025-07-18T16:17:33.691824", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 25, "non_empty_lines": 18}, {"name": "test_jewelry_database.py", "size": 12397, "size_mb": 0.012, "modified": "2025-07-18T16:21:15.638907", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 321, "non_empty_lines": 265}, {"name": "test_jewelry_logic.py", "size": 13026, "size_mb": 0.012, "modified": "2025-07-18T16:21:15.636777", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 328, "non_empty_lines": 252}, {"name": "test_plugins.py", "size": 14073, "size_mb": 0.013, "modified": "2025-07-18T16:21:15.739525", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 380, "non_empty_lines": 301}, {"name": "test_project_analyzer.py", "size": 15050, "size_mb": 0.014, "modified": "2025-07-18T16:21:15.717850", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 431, "non_empty_lines": 355}, {"name": "test_smart_analyzer.py", "size": 8138, "size_mb": 0.008, "modified": "2025-07-18T16:21:15.611758", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 193, "non_empty_lines": 152}, {"name": "test_system.py", "size": 10398, "size_mb": 0.01, "modified": "2025-07-18T16:21:15.694896", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 289, "non_empty_lines": 216}, {"name": "__init__.py", "size": 775, "size_mb": 0.001, "modified": "2025-07-18T16:12:12.871585", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 25, "non_empty_lines": 20}], "subdirectories": {"configs": {"path": "anubis\\tests\\configs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\configs", "files": [{"name": "default_config.json", "size": 4183, "size_mb": 0.004, "modified": "2025-07-14T12:47:18.980573", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 190, "non_empty_lines": 190}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 4183, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.004}}, "workspace": {"path": "anubis\\tests\\workspace", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace", "files": [], "subdirectories": {"collaboration_logs": {"path": "anubis\\tests\\workspace\\collaboration_logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace\\collaboration_logs", "files": [{"name": "collaboration_log.md", "size": 294, "size_mb": 0.0, "modified": "2025-07-14T12:47:19.249306", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 8, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 294, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "knowledge_base": {"path": "anubis\\tests\\workspace\\knowledge_base", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace\\knowledge_base", "files": [{"name": "knowledge_base.json", "size": 856, "size_mb": 0.001, "modified": "2025-07-14T12:47:19.256077", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 35, "non_empty_lines": 35}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 856, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.001}}, "logs": {"path": "anubis\\tests\\workspace\\logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace\\logs", "files": [{"name": "database_agent.log", "size": 1100, "size_mb": 0.001, "modified": "2025-07-14T12:49:19.973377", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "error_detector_agent.log", "size": 16234, "size_mb": 0.015, "modified": "2025-07-14T12:49:20.356469", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "file_organizer_agent.log", "size": 1092, "size_mb": 0.001, "modified": "2025-07-14T12:49:19.984698", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "memory_agent.log", "size": 932, "size_mb": 0.001, "modified": "2025-07-14T12:49:19.988211", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "project_analyzer_agent.log", "size": 17238, "size_mb": 0.016, "modified": "2025-07-14T12:49:20.787192", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_124906_20250714_124906.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-14T12:49:06.627099", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_124919_20250714_124919.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-14T12:49:19.734007", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "testlogger.log", "size": 1120, "size_mb": 0.001, "modified": "2025-07-14T12:49:19.617158", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "universalassistants.log", "size": 3124, "size_mb": 0.003, "modified": "2025-07-14T12:49:19.749314", "extension": ".log", "type": "Log File", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 9, "total_size": 41022, "file_types": {".log": 9}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.039}}, "reports": {"path": "anubis\\tests\\workspace\\reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace\\reports", "files": [{"name": "error_detector_report_20250714_124719.json", "size": 2282, "size_mb": 0.002, "modified": "2025-07-14T12:47:19.607978", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 82, "non_empty_lines": 82}, {"name": "error_detector_report_20250714_124920.json", "size": 2282, "size_mb": 0.002, "modified": "2025-07-14T12:49:20.354233", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 82, "non_empty_lines": 82}, {"name": "project_analyzer_report_20250714_124719.json", "size": 4237, "size_mb": 0.004, "modified": "2025-07-14T12:47:19.986587", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 153, "non_empty_lines": 153}, {"name": "project_analyzer_report_20250714_124720.json", "size": 3807, "size_mb": 0.004, "modified": "2025-07-14T12:47:20.064941", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 135, "non_empty_lines": 135}, {"name": "project_analyzer_report_20250714_124920.json", "size": 3807, "size_mb": 0.004, "modified": "2025-07-14T12:49:20.776965", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 135, "non_empty_lines": 135}], "subdirectories": {}, "statistics": {"total_files": 5, "total_size": 16415, "file_types": {".json": 5}, "python_files": 0, "config_files": 5, "doc_files": 0, "script_files": 0, "total_size_mb": 0.016}}, "shared_memory": {"path": "anubis\\tests\\workspace\\shared_memory", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\workspace\\shared_memory", "files": [{"name": "session_20250714.json", "size": 285, "size_mb": 0.0, "modified": "2025-07-14T12:47:19.256077", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 13, "non_empty_lines": 13}, {"name": "shared_memory.md", "size": 960, "size_mb": 0.001, "modified": "2025-07-14T12:47:19.249306", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 31, "non_empty_lines": 24}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 1245, "file_types": {".json": 1, ".md": 1}, "python_files": 0, "config_files": 1, "doc_files": 1, "script_files": 0, "total_size_mb": 0.001}}}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}}, "statistics": {"total_files": 21, "total_size": 213377, "file_types": {".py": 19, ".md": 2}, "python_files": 19, "config_files": 0, "doc_files": 2, "script_files": 0, "total_size_mb": 0.203}}, "tests.backup_20250718_160241": {"path": "anubis\\tests.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests.backup_20250718_160241", "files": [{"name": "README.md", "size": 48, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.094284", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 48, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "workspace": {"path": "anubis\\workspace", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace", "files": [{"name": "agents_cleanup_report_20250716_081504.json", "size": 1255, "size_mb": 0.001, "modified": "2025-07-16T08:15:04.997264", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 53, "non_empty_lines": 53}, {"name": "ai_integration_test_report_20250716_011209.json", "size": 3061, "size_mb": 0.003, "modified": "2025-07-16T01:12:09.406415", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 65, "non_empty_lines": 65}, {"name": "complete_organization_report_20250716_075352.json", "size": 1383, "size_mb": 0.001, "modified": "2025-07-16T07:53:52.307320", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 52, "non_empty_lines": 52}, {"name": "comprehensive_system_test_report_20250716_064902.json", "size": 2103, "size_mb": 0.002, "modified": "2025-07-16T06:49:02.289358", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 76, "non_empty_lines": 76}, {"name": "comprehensive_test_report_20250716_074112.json", "size": 1072, "size_mb": 0.001, "modified": "2025-07-16T07:41:12.921103", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 50, "non_empty_lines": 50}, {"name": "file_organization_report_20250716_091625.json", "size": 1541, "size_mb": 0.001, "modified": "2025-07-16T09:16:25.576313", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 91, "non_empty_lines": 91}, {"name": "langsmith_activation_report_20250716_084129.json", "size": 411, "size_mb": 0.0, "modified": "2025-07-16T08:41:29.798669", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 16, "non_empty_lines": 16}, {"name": "langsmith_integration_test_20250716_082826.json", "size": 5248, "size_mb": 0.005, "modified": "2025-07-16T08:28:26.479460", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 181, "non_empty_lines": 181}, {"name": "langsmith_integration_test_20250716_083715.json", "size": 5249, "size_mb": 0.005, "modified": "2025-07-16T08:37:15.198519", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 181, "non_empty_lines": 181}, {"name": "ollama_check_report_20250716_010906.json", "size": 1840, "size_mb": 0.002, "modified": "2025-07-16T01:09:06.117754", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 74, "non_empty_lines": 74}, {"name": "organization_report_20250716_074447.json", "size": 2231, "size_mb": 0.002, "modified": "2025-07-16T07:44:47.272984", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 89, "non_empty_lines": 89}, {"name": "README.md", "size": 1576, "size_mb": 0.002, "modified": "2025-07-16T09:06:13.378232", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 56, "non_empty_lines": 37}, {"name": "README.md.backup_20250718_160241", "size": 1042, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.380232", "extension": ".backup_20250718_160241", "type": "Unknown", "is_empty": false}, {"name": "readme_generation_report.json", "size": 436, "size_mb": 0.0, "modified": "2025-07-16T09:06:13.449830", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 25, "non_empty_lines": 25}], "subdirectories": {"backups": {"path": "anubis\\workspace\\backups", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups", "files": [{"name": "anubis_backup_20250714_130707.db", "size": 32768, "size_mb": 0.031, "modified": "2025-07-14T13:07:07.301421", "extension": ".db", "type": "Database File", "is_empty": false}, {"name": "anubis_backup_20250714_130746.db", "size": 32768, "size_mb": 0.031, "modified": "2025-07-14T13:07:46.167767", "extension": ".db", "type": "Database File", "is_empty": false}, {"name": "anubis_backup_20250714_130858.db", "size": 32768, "size_mb": 0.031, "modified": "2025-07-14T13:08:58.322883", "extension": ".db", "type": "Database File", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 98304, "file_types": {".db": 3}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.094}}, "collaboration_logs": {"path": "anubis\\workspace\\collaboration_logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\collaboration_logs", "files": [{"name": "collaboration_log.md", "size": 294, "size_mb": 0.0, "modified": "2025-07-12T15:08:29.413531", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 8, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 294, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "knowledge_base": {"path": "anubis\\workspace\\knowledge_base", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\knowledge_base", "files": [{"name": "knowledge_base.json", "size": 859, "size_mb": 0.001, "modified": "2025-07-12T15:08:29.414583", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 35, "non_empty_lines": 35}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 859, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.001}}, "logs": {"path": "anubis\\workspace\\logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\logs", "files": [{"name": "database_agent.log", "size": 9797, "size_mb": 0.009, "modified": "2025-07-16T08:52:54.314349", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "enhanced_error_detector_agent.log", "size": 2545, "size_mb": 0.002, "modified": "2025-07-16T08:35:58.671095", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "enhanced_file_organizer_agent.log", "size": 1910, "size_mb": 0.002, "modified": "2025-07-16T08:36:02.812118", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "enhanced_memory_agent_agent.log", "size": 1958, "size_mb": 0.002, "modified": "2025-07-16T08:36:04.912638", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "enhanced_project_analyzer_agent.log", "size": 1984, "size_mb": 0.002, "modified": "2025-07-16T08:36:00.737307", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "error_detector_agent.log", "size": 10579, "size_mb": 0.01, "modified": "2025-07-14T14:21:01.664520", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "file_organizer_agent.log", "size": 5119, "size_mb": 0.005, "modified": "2025-07-14T13:11:52.213429", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "fixed_ai_test_agent.log", "size": 521, "size_mb": 0.0, "modified": "2025-07-16T06:23:12.473676", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "memory_agent.log", "size": 4549, "size_mb": 0.004, "modified": "2025-07-16T06:47:03.020029", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "project_analyzer_agent.log", "size": 11233, "size_mb": 0.011, "modified": "2025-07-14T14:21:04.361755", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150546_20250712_150546.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-12T15:05:46.776795", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150556_20250712_150556.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-12T15:05:56.115440", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150648_20250712_150648.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-12T15:06:48.178903", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150742_20250712_150742.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-12T15:07:42.620290", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150802_20250712_150802.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-12T15:08:02.476670", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_150850_20250712_150850.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:09:24.907230", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_151004_20250712_151004.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:10:38.094138", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_151046_20250712_151046.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:11:19.352645", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_151156_20250712_151156.log", "size": 337, "size_mb": 0.0, "modified": "2025-07-12T15:13:09.151443", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_152600_20250712_152600.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:26:01.119277", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_153601_20250712_153601.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:36:02.273607", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250712_154035_20250712_154035.log", "size": 272, "size_mb": 0.0, "modified": "2025-07-12T15:41:34.144346", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_123542_20250714_123542.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-14T12:35:42.800425", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_130746_20250714_130746.log", "size": 91, "size_mb": 0.0, "modified": "2025-07-14T13:07:46.216267", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_130858_20250714_130858.log", "size": 404, "size_mb": 0.0, "modified": "2025-07-14T13:08:59.167715", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "session_20250714_131151_20250714_131151.log", "size": 404, "size_mb": 0.0, "modified": "2025-07-14T13:11:52.441390", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "simple_test_agent.log", "size": 281, "size_mb": 0.0, "modified": "2025-07-16T06:34:28.565605", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "smart_code_analyzer_agent.log", "size": 2589, "size_mb": 0.002, "modified": "2025-07-16T08:36:06.957897", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "testlogger.log", "size": 840, "size_mb": 0.001, "modified": "2025-07-14T12:35:42.509348", "extension": ".log", "type": "Log File", "is_empty": false}, {"name": "universalassistants.log", "size": 20338, "size_mb": 0.019, "modified": "2025-07-14T13:11:52.441390", "extension": ".log", "type": "Log File", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 30, "total_size": 77657, "file_types": {".log": 30}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.074}}, "reports": {"path": "anubis\\workspace\\reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\reports", "files": [{"name": "anubis_error_analysis_20250714_142104.json", "size": 1034916, "size_mb": 0.987, "modified": "2025-07-14T14:21:04.424681", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 26073, "non_empty_lines": 26073}, {"name": "assistant_report_20250712_150556.json", "size": 463, "size_mb": 0.0, "modified": "2025-07-12T15:05:56.226138", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 20, "non_empty_lines": 20}, {"name": "assistant_report_20250712_150648.json", "size": 463, "size_mb": 0.0, "modified": "2025-07-12T15:06:48.322315", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 20, "non_empty_lines": 20}, {"name": "assistant_report_20250712_150742.json", "size": 463, "size_mb": 0.0, "modified": "2025-07-12T15:07:42.758981", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 20, "non_empty_lines": 20}, {"name": "assistant_report_20250712_150924.json", "size": 5438990, "size_mb": 5.187, "modified": "2025-07-12T15:09:25.064009", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 95539, "non_empty_lines": 95539}, {"name": "assistant_report_20250712_151038.json", "size": 5440462, "size_mb": 5.188, "modified": "2025-07-12T15:10:38.249389", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 95580, "non_empty_lines": 95580}, {"name": "assistant_report_20250712_151119.json", "size": 5439107, "size_mb": 5.187, "modified": "2025-07-12T15:11:19.503857", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 95543, "non_empty_lines": 95543}, {"name": "assistant_report_20250712_151309.json", "size": 10492425, "size_mb": 10.006, "modified": "2025-07-12T15:13:09.451050", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 190957, "non_empty_lines": 190957}, {"name": "assistant_report_20250712_152601.json", "size": 20846, "size_mb": 0.02, "modified": "2025-07-12T15:26:01.123811", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 572, "non_empty_lines": 572}, {"name": "assistant_report_20250712_153602.json", "size": 23841, "size_mb": 0.023, "modified": "2025-07-12T15:36:02.275617", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 649, "non_empty_lines": 649}, {"name": "assistant_report_20250712_154134.json", "size": 5438990, "size_mb": 5.187, "modified": "2025-07-12T15:41:34.372300", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 95539, "non_empty_lines": 95539}, {"name": "code_quality_report.json", "size": 144665, "size_mb": 0.138, "modified": "2025-07-18T16:21:39.125276", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 108, "non_empty_lines": 108}, {"name": "code_quality_report.md", "size": 5118, "size_mb": 0.005, "modified": "2025-07-18T16:21:39.126283", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 98, "non_empty_lines": 89}, {"name": "error_detector_report_20250714_123543.json", "size": 2282, "size_mb": 0.002, "modified": "2025-07-14T12:35:43.645081", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 82, "non_empty_lines": 82}, {"name": "error_detector_report_20250714_130858.json", "size": 309855, "size_mb": 0.296, "modified": "2025-07-14T13:08:58.661183", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 9531, "non_empty_lines": 9531}, {"name": "error_detector_report_20250714_131152.json", "size": 320500, "size_mb": 0.306, "modified": "2025-07-14T13:11:52.019572", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 9877, "non_empty_lines": 9877}, {"name": "error_detector_report_20250714_142101.json", "size": 765717, "size_mb": 0.73, "modified": "2025-07-14T14:21:01.664520", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 21894, "non_empty_lines": 21894}, {"name": "final_quality_report.md", "size": 16934, "size_mb": 0.016, "modified": "2025-07-18T17:00:10.803979", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 368, "non_empty_lines": 274}, {"name": "organization_report.json", "size": 35471, "size_mb": 0.034, "modified": "2025-07-18T16:02:41.560135", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 1023, "non_empty_lines": 1023}, {"name": "organization_report.md", "size": 1541, "size_mb": 0.001, "modified": "2025-07-18T16:02:41.561852", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 26, "non_empty_lines": 21}, {"name": "project_analyzer_report_20250714_123543.json", "size": 4288, "size_mb": 0.004, "modified": "2025-07-14T12:35:43.987134", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 157, "non_empty_lines": 157}, {"name": "project_analyzer_report_20250714_123544.json", "size": 3807, "size_mb": 0.004, "modified": "2025-07-14T12:35:44.211130", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 135, "non_empty_lines": 135}, {"name": "project_analyzer_report_20250714_130859.json", "size": 18270, "size_mb": 0.017, "modified": "2025-07-14T13:08:59.167715", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 582, "non_empty_lines": 582}, {"name": "project_analyzer_report_20250714_131152.json", "size": 18680, "size_mb": 0.018, "modified": "2025-07-14T13:11:52.439422", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 594, "non_empty_lines": 594}, {"name": "project_analyzer_report_20250714_142104.json", "size": 163829, "size_mb": 0.156, "modified": "2025-07-14T14:21:04.358605", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 4142, "non_empty_lines": 4142}, {"name": "test_report_20250714_123544.txt", "size": 542, "size_mb": 0.001, "modified": "2025-07-14T12:35:44.517216", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 16, "non_empty_lines": 14}, {"name": "test_report_20250714_124720.txt", "size": 541, "size_mb": 0.001, "modified": "2025-07-14T12:47:20.418090", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 16, "non_empty_lines": 14}, {"name": "test_report_20250714_124921.txt", "size": 542, "size_mb": 0.001, "modified": "2025-07-14T12:49:21.083316", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 16, "non_empty_lines": 14}], "subdirectories": {}, "statistics": {"total_files": 28, "total_size": 35143548, "file_types": {".json": 22, ".md": 3, ".txt": 3}, "python_files": 0, "config_files": 22, "doc_files": 6, "script_files": 0, "total_size_mb": 33.515}}, "shared_memory": {"path": "anubis\\workspace\\shared_memory", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\shared_memory", "files": [{"name": "session_20250712.json", "size": 285, "size_mb": 0.0, "modified": "2025-07-12T15:41:34.144346", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 13, "non_empty_lines": 13}, {"name": "session_20250714.json", "size": 285, "size_mb": 0.0, "modified": "2025-07-14T13:11:52.220090", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 13, "non_empty_lines": 13}, {"name": "session_20250716.json", "size": 270, "size_mb": 0.0, "modified": "2025-07-16T06:47:03.014381", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 13, "non_empty_lines": 13}, {"name": "shared_memory.md", "size": 963, "size_mb": 0.001, "modified": "2025-07-12T15:08:29.413001", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 31, "non_empty_lines": 24}], "subdirectories": {}, "statistics": {"total_files": 4, "total_size": 1803, "file_types": {".json": 3, ".md": 1}, "python_files": 0, "config_files": 3, "doc_files": 1, "script_files": 0, "total_size_mb": 0.002}}}, "statistics": {"total_files": 14, "total_size": 28448, "file_types": {".json": 12, ".md": 1, ".backup_20250718_160241": 1}, "python_files": 0, "config_files": 12, "doc_files": 1, "script_files": 0, "total_size_mb": 0.027}}}, "statistics": {"total_files": 7, "total_size": 53245, "file_types": {".md": 5, ".py": 2}, "python_files": 2, "config_files": 0, "doc_files": 5, "script_files": 0, "total_size_mb": 0.051}}, "tools": {"path": "tools", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools", "files": [{"name": "README.md", "size": 1047, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.448285", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 46, "non_empty_lines": 27}], "subdirectories": {"emergency": {"path": "tools\\emergency", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\emergency", "files": [{"name": "emergency_cleanup_67_processes.bat", "size": 2771, "size_mb": 0.003, "modified": "2025-07-16T16:33:01.688227", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "emergency_vscode_fix.bat", "size": 2076, "size_mb": 0.002, "modified": "2025-07-16T16:27:52.353095", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "EMERGENCY_VSCODE_KILLER.bat", "size": 5352, "size_mb": 0.005, "modified": "2025-07-16T16:38:33.919346", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 10199, "file_types": {".bat": 3}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 3, "total_size_mb": 0.01}}, "emergency.backup_20250718_160241": {"path": "tools\\emergency.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\emergency.backup_20250718_160241", "files": [{"name": "README.md", "size": 42, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.107094", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 42, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "monitoring.backup_20250718_160241": {"path": "tools\\monitoring.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\monitoring.backup_20250718_160241", "files": [{"name": "README.md", "size": 45, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.108979", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 45, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "vscode-optimizer": {"path": "tools\\vscode-optimizer", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer", "files": [{"name": "dashboard_config.json", "size": 3174, "size_mb": 0.003, "modified": "2025-07-16T16:54:39.005971", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 135, "non_empty_lines": 135}, {"name": "DASHBOARD_README.md", "size": 5697, "size_mb": 0.005, "modified": "2025-07-16T16:52:03.219033", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 171, "non_empty_lines": 128}, {"name": "LAUNCH_SUITE.bat", "size": 9638, "size_mb": 0.009, "modified": "2025-07-18T01:59:26.696909", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "README.md", "size": 8122, "size_mb": 0.008, "modified": "2025-07-18T01:58:17.365606", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 251, "non_empty_lines": 188}, {"name": "README.md.backup_20250718_160241", "size": 53, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.106055", "extension": ".backup_20250718_160241", "type": "Unknown", "is_empty": false}, {"name": "run_dashboard.sh", "size": 9066, "size_mb": 0.009, "modified": "2025-07-16T16:56:41.617989", "extension": ".sh", "type": "<PERSON> Script", "is_empty": false}, {"name": "Start-Dashboard.ps1", "size": 9590, "size_mb": 0.009, "modified": "2025-07-16T16:55:21.220785", "extension": ".ps1", "type": "PowerShell Script", "is_empty": false}, {"name": "SUITE_INFO.md", "size": 11142, "size_mb": 0.011, "modified": "2025-07-18T02:00:50.533818", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 283, "non_empty_lines": 223}, {"name": "test_dashboard.py", "size": 6089, "size_mb": 0.006, "modified": "2025-07-16T17:04:21.344782", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 184, "non_empty_lines": 152}], "subdirectories": {"agents": {"path": "tools\\vscode-optimizer\\agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\agents", "files": [{"name": "database_agent.py", "size": 17122, "size_mb": 0.016, "modified": "2025-07-16T08:51:48.186398", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 447, "non_empty_lines": 360}, {"name": "enhanced_error_detector.py", "size": 25291, "size_mb": 0.024, "modified": "2025-07-16T07:14:04.707036", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 583, "non_empty_lines": 488}, {"name": "enhanced_file_organizer.py", "size": 7262, "size_mb": 0.007, "modified": "2025-07-16T07:38:33.794947", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 170, "non_empty_lines": 143}, {"name": "enhanced_memory_agent.py", "size": 5619, "size_mb": 0.005, "modified": "2025-07-16T07:38:33.794947", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 154, "non_empty_lines": 131}, {"name": "enhanced_project_analyzer.py", "size": 5988, "size_mb": 0.006, "modified": "2025-07-16T07:38:33.792319", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 160, "non_empty_lines": 133}, {"name": "README.md", "size": 6410, "size_mb": 0.006, "modified": "2025-07-16T09:01:46.617446", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 181, "non_empty_lines": 135}, {"name": "smart_ai_agent.py", "size": 13055, "size_mb": 0.012, "modified": "2025-07-16T01:03:58.439138", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 331, "non_empty_lines": 266}, {"name": "smart_code_analyzer.py", "size": 13948, "size_mb": 0.013, "modified": "2025-07-16T06:35:32.782131", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 361, "non_empty_lines": 293}, {"name": "__init__.py", "size": 2036, "size_mb": 0.002, "modified": "2025-07-16T08:15:04.994632", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 53, "non_empty_lines": 45}], "subdirectories": {}, "statistics": {"total_files": 9, "total_size": 96731, "file_types": {".py": 8, ".md": 1}, "python_files": 8, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.092}}, "VS-Code-Performance-Optimizer": {"path": "tools\\vscode-optimizer\\VS-Code-Performance-Optimizer", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\VS-Code-Performance-Optimizer", "files": [{"name": "analyze_extensions.py", "size": 4617, "size_mb": 0.004, "modified": "2025-07-17T12:40:21.490849", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 117, "non_empty_lines": 98}, {"name": "analyze_vscode.py", "size": 6597, "size_mb": 0.006, "modified": "2025-07-17T12:37:03.326623", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 162, "non_empty_lines": 133}, {"name": "apply_vscode_optimizations.bat", "size": 2610, "size_mb": 0.002, "modified": "2025-07-17T23:54:19.116942", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "auto_apply_vscode_settings.py", "size": 7618, "size_mb": 0.007, "modified": "2025-07-17T23:58:00.312726", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 215, "non_empty_lines": 178}, {"name": "disable_heavy_extensions.bat", "size": 1726, "size_mb": 0.002, "modified": "2025-07-17T23:52:23.203103", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "FINAL_SOLUTION.md", "size": 7058, "size_mb": 0.007, "modified": "2025-07-17T09:55:38.113337", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 186, "non_empty_lines": 146}, {"name": "HOW_TO_RUN.md", "size": 1744, "size_mb": 0.002, "modified": "2025-07-17T09:40:39.771525", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 75, "non_empty_lines": 55}, {"name": "PROJECT_INFO.md", "size": 7933, "size_mb": 0.008, "modified": "2025-07-18T01:49:45.214138", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 210, "non_empty_lines": 165}, {"name": "quick_start.bat", "size": 286, "size_mb": 0.0, "modified": "2025-07-17T09:40:07.580339", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "README.md", "size": 10078, "size_mb": 0.01, "modified": "2025-07-17T09:06:43.741298", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 284, "non_empty_lines": 216}, {"name": "README_MAIN.md", "size": 6563, "size_mb": 0.006, "modified": "2025-07-18T01:44:49.203050", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 185, "non_empty_lines": 138}, {"name": "README_PRO.md", "size": 11878, "size_mb": 0.011, "modified": "2025-07-17T09:30:47.149227", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 292, "non_empty_lines": 232}, {"name": "RUN_OPTIMIZER.bat", "size": 4013, "size_mb": 0.004, "modified": "2025-07-18T01:45:53.486230", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "start.bat", "size": 2342, "size_mb": 0.002, "modified": "2025-07-17T09:38:32.557844", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "start_pro.bat", "size": 2600, "size_mb": 0.002, "modified": "2025-07-17T09:38:13.142288", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "start_stable.bat", "size": 2338, "size_mb": 0.002, "modified": "2025-07-17T09:53:41.432135", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "test_system.py", "size": 9231, "size_mb": 0.009, "modified": "2025-07-17T23:27:43.392234", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 262, "non_empty_lines": 214}, {"name": "vscode_control_center.py", "size": 28638, "size_mb": 0.027, "modified": "2025-07-17T09:05:23.001938", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 738, "non_empty_lines": 621}, {"name": "vscode_control_center_pro.py", "size": 61040, "size_mb": 0.058, "modified": "2025-07-17T09:48:34.943691", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1548, "non_empty_lines": 1267}, {"name": "vscode_control_center_stable.py", "size": 38625, "size_mb": 0.037, "modified": "2025-07-17T09:53:04.631820", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1005, "non_empty_lines": 841}, {"name": "vscode_optimized_settings.json", "size": 3540, "size_mb": 0.003, "modified": "2025-07-18T02:10:58.196757", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 114, "non_empty_lines": 99}, {"name": "vscode_settings_fixed.json", "size": 2846, "size_mb": 0.003, "modified": "2025-07-18T02:12:16.232013", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 86, "non_empty_lines": 86}], "subdirectories": {"agents": {"path": "tools\\vscode-optimizer\\VS-Code-Performance-Optimizer\\agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\VS-Code-Performance-Optimizer\\agents", "files": [{"name": "agent_coordinator.py", "size": 16910, "size_mb": 0.016, "modified": "2025-07-17T08:52:40.304436", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 398, "non_empty_lines": 318}, {"name": "base_agent.py", "size": 4634, "size_mb": 0.004, "modified": "2025-07-17T08:45:20.610837", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 129, "non_empty_lines": 106}, {"name": "gemini_agent.py", "size": 9385, "size_mb": 0.009, "modified": "2025-07-17T08:46:50.559310", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 233, "non_empty_lines": 192}, {"name": "ollama_agent.py", "size": 11781, "size_mb": 0.011, "modified": "2025-07-17T08:47:56.562558", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 304, "non_empty_lines": 252}, {"name": "performance_optimizer.py", "size": 15838, "size_mb": 0.015, "modified": "2025-07-17T08:49:00.944439", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 377, "non_empty_lines": 315}, {"name": "process_analyzer.py", "size": 8394, "size_mb": 0.008, "modified": "2025-07-17T08:46:03.880514", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 196, "non_empty_lines": 156}, {"name": "security_monitor.py", "size": 15803, "size_mb": 0.015, "modified": "2025-07-17T08:50:06.041508", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 358, "non_empty_lines": 293}, {"name": "smart_recommendations.py", "size": 20642, "size_mb": 0.02, "modified": "2025-07-17T08:51:32.549759", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 427, "non_empty_lines": 357}, {"name": "__init__.py", "size": 2219, "size_mb": 0.002, "modified": "2025-07-17T08:44:54.117868", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 63, "non_empty_lines": 55}], "subdirectories": {}, "statistics": {"total_files": 9, "total_size": 105606, "file_types": {".py": 9}, "python_files": 9, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.101}}}, "statistics": {"total_files": 22, "total_size": 223921, "file_types": {".py": 7, ".bat": 7, ".md": 6, ".json": 2}, "python_files": 7, "config_files": 2, "doc_files": 6, "script_files": 7, "total_size_mb": 0.214}}, "VSCode-Control-Center": {"path": "tools\\vscode-optimizer\\VSCode-Control-Center", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\VSCode-Control-Center", "files": [{"name": "CHANGELOG.md", "size": 6515, "size_mb": 0.006, "modified": "2025-07-17T09:00:35.121053", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 181, "non_empty_lines": 145}, {"name": "config.json", "size": 2814, "size_mb": 0.003, "modified": "2025-07-17T08:58:45.047643", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 119, "non_empty_lines": 110}, {"name": "DESIGN_COMPARISON.md", "size": 4878, "size_mb": 0.005, "modified": "2025-07-17T08:36:32.520761", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 123, "non_empty_lines": 90}, {"name": "FINAL_SOLUTION.md", "size": 7058, "size_mb": 0.007, "modified": "2025-07-17T09:55:38.113337", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 186, "non_empty_lines": 146}, {"name": "HOW_TO_RUN.md", "size": 1744, "size_mb": 0.002, "modified": "2025-07-17T09:40:39.771525", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 75, "non_empty_lines": 55}, {"name": "QUICK_START.md", "size": 6456, "size_mb": 0.006, "modified": "2025-07-17T09:08:18.864927", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 139, "non_empty_lines": 109}, {"name": "README.md", "size": 10078, "size_mb": 0.01, "modified": "2025-07-17T09:06:43.741298", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 284, "non_empty_lines": 216}, {"name": "README_PRO.md", "size": 11878, "size_mb": 0.011, "modified": "2025-07-17T09:30:47.149227", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 292, "non_empty_lines": 232}, {"name": "requirements.txt", "size": 994, "size_mb": 0.001, "modified": "2025-07-17T08:57:55.200082", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 26, "non_empty_lines": 20}, {"name": "run.bat", "size": 3552, "size_mb": 0.003, "modified": "2025-07-17T09:05:51.926523", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "run_pro.bat", "size": 3921, "size_mb": 0.004, "modified": "2025-07-17T09:28:41.943084", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}, {"name": "vscode_control_center.py", "size": 28638, "size_mb": 0.027, "modified": "2025-07-17T09:05:23.001938", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 738, "non_empty_lines": 621}, {"name": "vscode_control_center_pro.py", "size": 61040, "size_mb": 0.058, "modified": "2025-07-17T09:48:34.943691", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1548, "non_empty_lines": 1267}, {"name": "vscode_control_center_stable.py", "size": 38625, "size_mb": 0.037, "modified": "2025-07-17T09:53:04.631820", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 1005, "non_empty_lines": 841}], "subdirectories": {"agents": {"path": "tools\\vscode-optimizer\\VSCode-Control-Center\\agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\VSCode-Control-Center\\agents", "files": [{"name": "agent_coordinator.py", "size": 16910, "size_mb": 0.016, "modified": "2025-07-17T08:52:40.304436", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 398, "non_empty_lines": 318}, {"name": "base_agent.py", "size": 4634, "size_mb": 0.004, "modified": "2025-07-17T08:45:20.610837", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 129, "non_empty_lines": 106}, {"name": "gemini_agent.py", "size": 9385, "size_mb": 0.009, "modified": "2025-07-17T08:46:50.559310", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 233, "non_empty_lines": 192}, {"name": "ollama_agent.py", "size": 11781, "size_mb": 0.011, "modified": "2025-07-17T08:47:56.562558", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 304, "non_empty_lines": 252}, {"name": "performance_optimizer.py", "size": 15838, "size_mb": 0.015, "modified": "2025-07-17T08:49:00.944439", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 377, "non_empty_lines": 315}, {"name": "process_analyzer.py", "size": 8394, "size_mb": 0.008, "modified": "2025-07-17T08:46:03.880514", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 196, "non_empty_lines": 156}, {"name": "security_monitor.py", "size": 15803, "size_mb": 0.015, "modified": "2025-07-17T08:50:06.041508", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 358, "non_empty_lines": 293}, {"name": "smart_recommendations.py", "size": 20642, "size_mb": 0.02, "modified": "2025-07-17T08:51:32.549759", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 427, "non_empty_lines": 357}, {"name": "__init__.py", "size": 2219, "size_mb": 0.002, "modified": "2025-07-17T08:44:54.117868", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 63, "non_empty_lines": 55}], "subdirectories": {}, "statistics": {"total_files": 9, "total_size": 105606, "file_types": {".py": 9}, "python_files": 9, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.101}}, "core": {"path": "tools\\vscode-optimizer\\VSCode-Control-Center\\core", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools\\vscode-optimizer\\VSCode-Control-Center\\core", "files": [{"name": "modern_dashboard.py", "size": 30180, "size_mb": 0.029, "modified": "2025-07-17T08:35:35.424744", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 674, "non_empty_lines": 541}, {"name": "process_control_dashboard.py", "size": 23366, "size_mb": 0.022, "modified": "2025-07-16T16:53:43.958477", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 526, "non_empty_lines": 425}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 53546, "file_types": {".py": 2}, "python_files": 2, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.051}}}, "statistics": {"total_files": 14, "total_size": 188191, "file_types": {".md": 7, ".json": 1, ".txt": 1, ".bat": 2, ".py": 3}, "python_files": 3, "config_files": 1, "doc_files": 8, "script_files": 2, "total_size_mb": 0.179}}}, "statistics": {"total_files": 9, "total_size": 62571, "file_types": {".json": 1, ".md": 3, ".bat": 1, ".backup_20250718_160241": 1, ".sh": 1, ".ps1": 1, ".py": 1}, "python_files": 1, "config_files": 1, "doc_files": 3, "script_files": 3, "total_size_mb": 0.06}}}, "statistics": {"total_files": 1, "total_size": 1047, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.001}}, "archive": {"path": "archive", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive", "files": [{"name": "cleanup_log_20250714_144107.json", "size": 6098, "size_mb": 0.006, "modified": "2025-07-14T14:41:07.732778", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 192, "non_empty_lines": 192}, {"name": "cleanup_summary_20250714_144107.md", "size": 5614, "size_mb": 0.005, "modified": "2025-07-14T14:41:07.733785", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 175, "non_empty_lines": 128}, {"name": "organization_log_20250714_140701.json", "size": 13812, "size_mb": 0.013, "modified": "2025-07-14T14:07:01.520654", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 402, "non_empty_lines": 402}, {"name": "organization_summary_20250714_140701.md", "size": 11634, "size_mb": 0.011, "modified": "2025-07-14T14:07:01.522407", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 344, "non_empty_lines": 255}, {"name": "README.md", "size": 9058, "size_mb": 0.009, "modified": "2025-07-16T09:18:27.121386", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 271, "non_empty_lines": 201}], "subdirectories": {"backups.backup_20250718_160241": {"path": "archive\\backups.backup_20250718_160241", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\backups.backup_20250718_160241", "files": [{"name": "README.md", "size": 46, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.112978", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 46, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "cache_files": {"path": "archive\\cache_files", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\cache_files", "files": [], "subdirectories": {}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "deprecated": {"path": "archive\\deprecated", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\deprecated", "files": [{"name": "README.md", "size": 49, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.114809", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 49, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "duplicate_reports": {"path": "archive\\duplicate_reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\duplicate_reports", "files": [{"name": "all_tests_report_20250714_135032.json", "size": 8185, "size_mb": 0.008, "modified": "2025-07-14T13:50:32.243990", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 69, "non_empty_lines": 69}, {"name": "final_validation_report_20250714_134540.json", "size": 634, "size_mb": 0.001, "modified": "2025-07-14T13:45:40.699150", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 17, "non_empty_lines": 17}, {"name": "final_validation_report_20250714_134549.json", "size": 634, "size_mb": 0.001, "modified": "2025-07-14T13:45:49.513945", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 17, "non_empty_lines": 17}, {"name": "final_validation_report_20250714_134653.json", "size": 3559, "size_mb": 0.003, "modified": "2025-07-14T13:46:53.659615", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "final_validation_report_20250714_141600.json", "size": 3557, "size_mb": 0.003, "modified": "2025-07-14T14:16:00.691380", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "final_validation_report_20250714_142229.json", "size": 3557, "size_mb": 0.003, "modified": "2025-07-14T14:22:29.516905", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "final_validation_report_20250714_143107.html", "size": 6078, "size_mb": 0.006, "modified": "2025-07-14T14:31:07.752116", "extension": ".html", "type": "HTML File", "is_empty": false, "lines": 163, "non_empty_lines": 152}, {"name": "final_validation_report_20250714_143107.json", "size": 3558, "size_mb": 0.003, "modified": "2025-07-14T14:31:07.752116", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 120, "non_empty_lines": 120}, {"name": "simple_validation_report_20250714_134926.json", "size": 876, "size_mb": 0.001, "modified": "2025-07-14T13:49:26.433350", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 37, "non_empty_lines": 37}, {"name": "simple_validation_report_20250714_135030.json", "size": 877, "size_mb": 0.001, "modified": "2025-07-14T13:50:30.673587", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 37, "non_empty_lines": 37}, {"name": "simple_validation_report_20250714_135840.json", "size": 876, "size_mb": 0.001, "modified": "2025-07-14T13:58:40.916672", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 37, "non_empty_lines": 37}, {"name": "stress_test_report_20250714_134422.json", "size": 983, "size_mb": 0.001, "modified": "2025-07-14T13:44:22.314102", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 41, "non_empty_lines": 41}, {"name": "stress_test_report_20250714_135031.json", "size": 985, "size_mb": 0.001, "modified": "2025-07-14T13:50:31.869168", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 41, "non_empty_lines": 41}, {"name": "test_report_20250714_134314.json", "size": 2096, "size_mb": 0.002, "modified": "2025-07-14T13:43:14.214154", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 57, "non_empty_lines": 57}, {"name": "test_report_20250714_135031.json", "size": 2095, "size_mb": 0.002, "modified": "2025-07-14T13:50:31.033810", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 57, "non_empty_lines": 57}], "subdirectories": {}, "statistics": {"total_files": 15, "total_size": 38550, "file_types": {".json": 14, ".html": 1}, "python_files": 0, "config_files": 14, "doc_files": 1, "script_files": 0, "total_size_mb": 0.037}}, "old_databases": {"path": "archive\\old_databases", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_databases", "files": [{"name": "anubis.db", "size": 32768, "size_mb": 0.031, "modified": "2025-07-14T13:25:46.275646", "extension": ".db", "type": "Database File", "is_empty": false}, {"name": "project_db.db", "size": 0, "size_mb": 0.0, "modified": "2025-07-14T13:08:58.417998", "extension": ".db", "type": "Database File", "is_empty": true}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 32768, "file_types": {".db": 2}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.031}}, "old_files": {"path": "archive\\old_files", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files", "files": [{"name": "ANUBIS_ERROR_CORRECTION_REPORT.md", "size": 10398, "size_mb": 0.01, "modified": "2025-07-14T14:23:37.665454", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 292, "non_empty_lines": 219}, {"name": "FILE_SPLIT_REPORT.md", "size": 10276, "size_mb": 0.01, "modified": "2025-07-14T14:32:10.992201", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 308, "non_empty_lines": 244}, {"name": "final_validation.backup_20250714_142217.py", "size": 21170, "size_mb": 0.02, "modified": "2025-07-14T14:22:17.132929", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 482, "non_empty_lines": 398}, {"name": "FINAL_VALIDATION_FIXES.md", "size": 6579, "size_mb": 0.006, "modified": "2025-07-14T14:17:08.161551", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 230, "non_empty_lines": 183}, {"name": "ORGANIZATION_COMPLETE.md", "size": 9207, "size_mb": 0.009, "modified": "2025-07-14T14:11:23.358254", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 262, "non_empty_lines": 201}, {"name": "README_SPLIT.md", "size": 5810, "size_mb": 0.006, "modified": "2025-07-14T14:32:59.358454", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 222, "non_empty_lines": 163}], "subdirectories": {"agents": {"path": "archive\\old_files\\agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\agents", "files": [{"name": "README.md", "size": 211, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.756577", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 211, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "backup": {"path": "archive\\old_files\\backup", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\backup", "files": [{"name": "README.md", "size": 197, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.766613", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 197, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "configs": {"path": "archive\\old_files\\configs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\configs", "files": [{"name": "README.md", "size": 186, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.760311", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 186, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "core": {"path": "archive\\old_files\\core", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\core", "files": [{"name": "README.md", "size": 221, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.754388", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 221, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "docs": {"path": "archive\\old_files\\docs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\docs", "files": [{"name": "project_index.json", "size": 2435, "size_mb": 0.002, "modified": "2025-07-16T07:49:37.784887", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 112, "non_empty_lines": 112}, {"name": "README.md", "size": 195, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.761369", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 2630, "file_types": {".json": 1, ".md": 1}, "python_files": 0, "config_files": 1, "doc_files": 1, "script_files": 0, "total_size_mb": 0.003}}, "examples": {"path": "archive\\old_files\\examples", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\examples", "files": [{"name": "README.md", "size": 195, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.769982", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 195, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "logs": {"path": "archive\\old_files\\logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\logs", "files": [{"name": "README.md", "size": 183, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.764369", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 183, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "reports": {"path": "archive\\old_files\\reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\reports", "files": [{"name": "organization_report_20250716_074937.json", "size": 1378, "size_mb": 0.001, "modified": "2025-07-16T07:49:37.786659", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 63, "non_empty_lines": 63}, {"name": "README.md", "size": 202, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.763366", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 1580, "file_types": {".json": 1, ".md": 1}, "python_files": 0, "config_files": 1, "doc_files": 1, "script_files": 0, "total_size_mb": 0.002}}, "scripts": {"path": "archive\\old_files\\scripts", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\scripts", "files": [{"name": "cleanup_and_organize.py", "size": 21211, "size_mb": 0.02, "modified": "2025-07-14T14:40:58.111611", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 542, "non_empty_lines": 415}, {"name": "quick_gemini_fix.py", "size": 17093, "size_mb": 0.016, "modified": "2025-07-16T07:37:02.301640", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 504, "non_empty_lines": 398}, {"name": "quick_start.py", "size": 2173, "size_mb": 0.002, "modified": "2025-07-14T12:45:44.541955", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 49, "non_empty_lines": 37}, {"name": "README.md", "size": 232, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.758598", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}, {"name": "safe_gemini_integration.py", "size": 15570, "size_mb": 0.015, "modified": "2025-07-16T07:29:01.351210", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 391, "non_empty_lines": 313}, {"name": "simple_agent_fix.py", "size": 21266, "size_mb": 0.02, "modified": "2025-07-16T07:38:24.471482", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 590, "non_empty_lines": 481}, {"name": "system_paths_manager.py", "size": 12959, "size_mb": 0.012, "modified": "2025-07-16T07:27:48.985371", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 335, "non_empty_lines": 273}], "subdirectories": {}, "statistics": {"total_files": 7, "total_size": 90504, "file_types": {".py": 6, ".md": 1}, "python_files": 6, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.086}}, "temp": {"path": "archive\\old_files\\temp", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\temp", "files": [{"name": "README.md", "size": 191, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.767610", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 191, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "tests": {"path": "archive\\old_files\\tests", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\tests", "files": [{"name": "README.md", "size": 188, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.757577", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 188, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "tools": {"path": "archive\\old_files\\tools", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_files\\tools", "files": [{"name": "README.md", "size": 180, "size_mb": 0.0, "modified": "2025-07-16T07:49:37.771988", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 9, "non_empty_lines": 6}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 180, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}}, "statistics": {"total_files": 6, "total_size": 63440, "file_types": {".md": 5, ".py": 1}, "python_files": 1, "config_files": 0, "doc_files": 5, "script_files": 0, "total_size_mb": 0.061}}, "old_versions": {"path": "archive\\old_versions", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\old_versions", "files": [{"name": "README.md", "size": 53, "size_mb": 0.0, "modified": "2025-07-18T16:02:41.110784", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3, "non_empty_lines": 2}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 53, "file_types": {".md": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.0}}, "temp_files": {"path": "archive\\temp_files", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\temp_files", "files": [], "subdirectories": {}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "unused_files": {"path": "archive\\unused_files", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive\\unused_files", "files": [{"name": "anubis_auto_fix.py", "size": 14153, "size_mb": 0.013, "modified": "2025-07-14T14:22:04.183628", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 357, "non_empty_lines": 277}, {"name": "anubis_error_fix.py", "size": 17901, "size_mb": 0.017, "modified": "2025-07-14T14:20:47.895676", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 407, "non_empty_lines": 327}, {"name": "organize_project.py", "size": 17734, "size_mb": 0.017, "modified": "2025-07-14T14:06:48.473119", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 498, "non_empty_lines": 406}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 49788, "file_types": {".py": 3}, "python_files": 3, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.047}}}, "statistics": {"total_files": 5, "total_size": 46216, "file_types": {".json": 2, ".md": 3}, "python_files": 0, "config_files": 2, "doc_files": 3, "script_files": 0, "total_size_mb": 0.044}}, "n8n": {"path": "n8n", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n", "files": [], "subdirectories": {"credentials": {"path": "n8n\\credentials", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n\\credentials", "files": [{"name": "AnubisApi.credentials.ts", "size": 1400, "size_mb": 0.001, "modified": "2025-07-16T11:12:44.119039", "extension": ".ts", "type": "TypeScript", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 1400, "file_types": {".ts": 1}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.001}}, "nodes": {"path": "n8n\\nodes", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n\\nodes", "files": [{"name": "AnubisAgents.node.ts", "size": 6356, "size_mb": 0.006, "modified": "2025-07-16T11:11:23.824974", "extension": ".ts", "type": "TypeScript", "is_empty": false}, {"name": "AnubisGemini.node.ts", "size": 5599, "size_mb": 0.005, "modified": "2025-07-16T11:10:10.038918", "extension": ".ts", "type": "TypeScript", "is_empty": false}, {"name": "AnubisOllama.node.ts", "size": 4023, "size_mb": 0.004, "modified": "2025-07-16T11:09:18.735967", "extension": ".ts", "type": "TypeScript", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 15978, "file_types": {".ts": 3}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.015}}, "workflows": {"path": "n8n\\workflows", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n\\workflows", "files": [{"name": "anubis-project-analysis.json", "size": 11284, "size_mb": 0.011, "modified": "2025-07-16T11:14:47.402130", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 416, "non_empty_lines": 416}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 11284, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.011}}}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "backup": {"path": "backup", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\backup", "files": [{"name": "main_old.py", "size": 8468, "size_mb": 0.008, "modified": "2025-07-16T08:00:30.460921", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 248, "non_empty_lines": 199}, {"name": "README.md", "size": 1069, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.425666", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 47, "non_empty_lines": 28}], "subdirectories": {"old_agents": {"path": "backup\\old_agents", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\backup\\old_agents", "files": [{"name": "error_detector.py_20250716_081504", "size": 21344, "size_mb": 0.02, "modified": "2025-07-16T07:06:50.461694", "extension": ".py_20250716_081504", "type": "Unknown", "is_empty": false}, {"name": "error_detector_agent.py_20250716_081504", "size": 17622, "size_mb": 0.017, "modified": "2025-07-14T12:06:53.166528", "extension": ".py_20250716_081504", "type": "Unknown", "is_empty": false}, {"name": "file_organizer_agent.py_20250716_081504", "size": 19248, "size_mb": 0.018, "modified": "2025-07-12T15:07:20.488287", "extension": ".py_20250716_081504", "type": "Unknown", "is_empty": false}, {"name": "memory_agent.py_20250716_081504", "size": 18128, "size_mb": 0.017, "modified": "2025-07-12T15:07:32.458565", "extension": ".py_20250716_081504", "type": "Unknown", "is_empty": false}, {"name": "project_analyzer_agent.py_20250716_081504", "size": 23447, "size_mb": 0.022, "modified": "2025-07-14T12:08:16.972299", "extension": ".py_20250716_081504", "type": "Unknown", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 5, "total_size": 99789, "file_types": {".py_20250716_081504": 5}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.095}}}, "statistics": {"total_files": 2, "total_size": 9537, "file_types": {".py": 1, ".md": 1}, "python_files": 1, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.009}}, "temp": {"path": "temp", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\temp", "files": [{"name": "README.md", "size": 1112, "size_mb": 0.001, "modified": "2025-07-16T09:06:13.434982", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 49, "non_empty_lines": 30}, {"name": "test_python_file.py", "size": 376, "size_mb": 0.0, "modified": "2025-07-16T07:16:15.787817", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 14, "non_empty_lines": 11}, {"name": "test_react_file.jsx", "size": 888, "size_mb": 0.001, "modified": "2025-07-16T07:17:19.949201", "extension": ".jsx", "type": "React JSX", "is_empty": false}, {"name": "test_style.css", "size": 212, "size_mb": 0.0, "modified": "2025-07-16T07:18:24.150714", "extension": ".css", "type": "CSS Stylesheet", "is_empty": false, "lines": 10, "non_empty_lines": 8}], "subdirectories": {}, "statistics": {"total_files": 4, "total_size": 2588, "file_types": {".md": 1, ".py": 1, ".jsx": 1, ".css": 1}, "python_files": 1, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.002}}, "augment-cht": {"path": "augment-cht", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\augment-cht", "files": [{"name": "chat.md", "size": 208578, "size_mb": 0.199, "modified": "2025-07-18T10:16:34.616383", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 4781, "non_empty_lines": 4176}, {"name": "cht.md", "size": 138180, "size_mb": 0.132, "modified": "2025-07-16T08:11:10.584074", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 3196, "non_empty_lines": 2725}], "subdirectories": {}, "statistics": {"total_files": 2, "total_size": 346758, "file_types": {".md": 2}, "python_files": 0, "config_files": 0, "doc_files": 2, "script_files": 0, "total_size_mb": 0.331}}, "Universal-AI-Assistants": {"path": "Universal-AI-Assistants", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants", "files": [], "subdirectories": {"logs": {"path": "Universal-AI-Assistants\\logs", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants\\logs", "files": [{"name": "vscode_process_monitor.log", "size": 332, "size_mb": 0.0, "modified": "2025-07-16T16:22:46.899545", "extension": ".log", "type": "Log File", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 332, "file_types": {".log": 1}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "reports": {"path": "Universal-AI-Assistants\\reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants\\reports", "files": [{"name": "vscode_monitor_report_20250716_162246.json", "size": 88299, "size_mb": 0.084, "modified": "2025-07-16T16:22:46.899545", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 886, "non_empty_lines": 886}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 88299, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.084}}}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "workspace": {"path": "workspace", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\workspace", "files": [], "subdirectories": {"reports": {"path": "workspace\\reports", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\workspace\\reports", "files": [{"name": "test_report_20250718_162025.txt", "size": 540, "size_mb": 0.001, "modified": "2025-07-18T16:20:25.996551", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 16, "non_empty_lines": 14}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 540, "file_types": {".txt": 1}, "python_files": 0, "config_files": 0, "doc_files": 1, "script_files": 0, "total_size_mb": 0.001}}}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, ".kiro": {"path": ".kiro", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.kiro", "files": [], "subdirectories": {"hooks": {"path": ".kiro\\hooks", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.kiro\\hooks", "files": [{"name": "localization-translator-hook.kiro.hook", "size": 1327, "size_mb": 0.001, "modified": "2025-07-18T15:35:25.389690", "extension": ".hook", "type": "Unknown", "is_empty": false}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 1327, "file_types": {".hook": 1}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.001}}, "settings": {"path": ".kiro\\settings", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.kiro\\settings", "files": [{"name": "mcp.json", "size": 172, "size_mb": 0.0, "modified": "2025-07-18T15:34:44.991296", "extension": ".json", "type": "JSON Configuration", "is_empty": false, "lines": 11, "non_empty_lines": 11}], "subdirectories": {}, "statistics": {"total_files": 1, "total_size": 172, "file_types": {".json": 1}, "python_files": 0, "config_files": 1, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}, "steering": {"path": ".kiro\\steering", "absolute_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.kiro\\steering", "files": [{"name": "product.md", "size": 1462, "size_mb": 0.001, "modified": "2025-07-18T14:51:15.206892", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 32, "non_empty_lines": 26}, {"name": "structure.md", "size": 4924, "size_mb": 0.005, "modified": "2025-07-18T14:52:17.577977", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 142, "non_empty_lines": 114}, {"name": "tech.md", "size": 3131, "size_mb": 0.003, "modified": "2025-07-18T14:51:40.058198", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 123, "non_empty_lines": 96}], "subdirectories": {}, "statistics": {"total_files": 3, "total_size": 9517, "file_types": {".md": 3}, "python_files": 0, "config_files": 0, "doc_files": 3, "script_files": 0, "total_size_mb": 0.009}}}, "statistics": {"total_files": 0, "total_size": 0, "file_types": {}, "python_files": 0, "config_files": 0, "doc_files": 0, "script_files": 0, "total_size_mb": 0.0}}}, "file_types": {}, "statistics": {"total_directories": 88, "total_files": 430, "total_size": 39376064, "total_size_mb": 37.552, "file_types": {".md": 114, ".py": 133, ".json": 84, ".langsmith": 1, ".backup_20250718_160241": 5, ".html": 4, ".sql": 1, ".txt": 9, ".ps1": 5, ".bat": 15, ".log": 40, ".db": 5, ".sh": 1, ".ts": 4, ".py_20250716_081504": 5, ".jsx": 1, ".css": 1, ".hook": 1, "": 1}, "python_files": 131, "config_files": 84, "doc_files": 125, "script_files": 20, "largest_files": [], "largest_directories": []}, "detailed_analysis": {}, "root_files": [{"name": ".giti<PERSON>re", "size": 1053, "size_mb": 0.001, "modified": "2025-07-18T15:10:33.000038", "extension": "", "type": "Unknown", "is_empty": false}, {"name": "comprehensive_project_scanner.py", "size": 13844, "size_mb": 0.013, "modified": "2025-07-18T17:23:08.957576", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 321, "non_empty_lines": 263}, {"name": "organize_anubis_system.py", "size": 20230, "size_mb": 0.019, "modified": "2025-07-18T16:02:22.666686", "extension": ".py", "type": "Python Source", "is_empty": false, "lines": 486, "non_empty_lines": 391}, {"name": "pylint_report.txt", "size": 284114, "size_mb": 0.271, "modified": "2025-07-18T16:13:09.745642", "extension": ".txt", "type": "Text File", "is_empty": false, "lines": 3108, "non_empty_lines": 3108}, {"name": "README.md", "size": 1894, "size_mb": 0.002, "modified": "2025-07-18T16:02:41.557018", "extension": ".md", "type": "Markdown Documentation", "is_empty": false, "lines": 49, "non_empty_lines": 38}, {"name": "setup_gemini.bat", "size": 425, "size_mb": 0.0, "modified": "2025-07-18T15:39:38.663720", "extension": ".bat", "type": "<PERSON><PERSON>", "is_empty": false}]}