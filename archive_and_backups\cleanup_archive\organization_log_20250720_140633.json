[{"timestamp": "2025-07-20T14:06:33.913564", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_AI_MODELS_AND_APPLICATIONS_DETAILED_GUIDE.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\ANUBIS_AI_MODELS_AND_APPLICATIONS_DETAILED_GUIDE.md"}, {"timestamp": "2025-07-20T14:06:33.916311", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md"}, {"timestamp": "2025-07-20T14:06:33.918959", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SERVICES_COMPREHENSIVE_GUIDE.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\ANUBIS_SERVICES_COMPREHENSIVE_GUIDE.md"}, {"timestamp": "2025-07-20T14:06:33.921713", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_TRANSFORMATION_COMPLETE.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\ANUBIS_TRANSFORMATION_COMPLETE.md"}, {"timestamp": "2025-07-20T14:06:33.924076", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_organization_completion_report.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\anubis_organization_completion_report.md"}, {"timestamp": "2025-07-20T14:06:33.926198", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_project_analysis_task_distribution.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\anubis_project_analysis_task_distribution.md"}, {"timestamp": "2025-07-20T14:06:33.928038", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_project_organization_plan.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\guides\\anubis_project_organization_plan.md"}, {"timestamp": "2025-07-20T14:06:33.931022", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_gemini_docker_help_request.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\gemini_requests\\anubis_gemini_docker_help_request.md"}, {"timestamp": "2025-07-20T14:06:33.932471", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_gemini_request_20250720_072632.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\gemini_requests\\anubis_gemini_request_20250720_072632.md"}, {"timestamp": "2025-07-20T14:06:33.935284", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_project_organization_gemini_request.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\gemini_requests\\anubis_project_organization_gemini_request.md"}, {"timestamp": "2025-07-20T14:06:33.937323", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\gemini_coordination_request_anubis_task_20250720_101017.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\gemini_requests\\gemini_coordination_request_anubis_task_20250720_101017.md"}, {"timestamp": "2025-07-20T14:06:33.939087", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\gemini_project_analysis_request.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\docs\\gemini_requests\\gemini_project_analysis_request.md"}, {"timestamp": "2025-07-20T14:06:33.941249", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_ai_team_collaboration_plan.json", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\config\\anubis_ai_team_collaboration_plan.json"}, {"timestamp": "2025-07-20T14:06:33.943530", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_navigation_shortcuts.json", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\config\\anubis_navigation_shortcuts.json"}, {"timestamp": "2025-07-20T14:06:33.945805", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_project_paths_config.json", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\config\\anubis_project_paths_config.json"}, {"timestamp": "2025-07-20T14:06:33.957188", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\migration_plan.json", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\config\\migration_plan.json"}, {"timestamp": "2025-07-20T14:06:33.963241", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\create_new_structure.py", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\organizers\\create_new_structure.py"}, {"timestamp": "2025-07-20T14:06:33.978259", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\execute_migration.py", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\organizers\\execute_migration.py"}, {"timestamp": "2025-07-20T14:06:33.983905", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\move_isolation_files.py", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\organizers\\move_isolation_files.py"}, {"timestamp": "2025-07-20T14:06:33.987517", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\start_team_analysis.py", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\organizers\\start_team_analysis.py"}, {"timestamp": "2025-07-20T14:06:33.989951", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\migration_log.json", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\reports\\migration_log.json"}, {"timestamp": "2025-07-20T14:06:33.993193", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\cline_task_jul-20-2025_7-13-06-am.md", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\reports\\cline_task_jul-20-2025_7-13-06-am.md"}, {"timestamp": "2025-07-20T14:06:33.995933", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\start_anubis_isolated.sh", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\start_anubis_isolated.sh"}, {"timestamp": "2025-07-20T14:06:33.997912", "action": "نقل", "source": "C:\\Users\\<USER>\\Universal-AI-Assistants\\setup_gemini.bat", "destination": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\setup_gemini.bat"}]