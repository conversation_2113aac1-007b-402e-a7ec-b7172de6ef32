networks:
  anubis_network:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
services:
  anubis_enhanced:
    build: ./services/anubis_enhanced
    container_name: anubis_enhanced_container
    depends_on:
    - database_mysql
    - ollama_service
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
    - PYTHONPATH=/app
    - DATABASE_URL=mysql://anubis:2452329511@database_mysql:3306/anubis_system
    - OLLAMA_URL=http://ollama_service:11434
    networks:
    - anubis_network
    ports:
    - 8000:8000
    restart: unless-stopped
    volumes:
    - ./volumes/logs:/app/logs
    - ./configs:/app/configs:ro
  api_gateway:
    container_name: nginx_gateway
    depends_on:
    - anubis_enhanced
    - universal_ai
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    image: nginx:alpine
    networks:
    - anubis_network
    ports:
    - 80:80
    - 443:443
    restart: unless-stopped
    volumes:
    - ./services/api_gateway/nginx.conf:/etc/nginx/nginx.conf:ro
    - ./volumes/logs:/var/log/nginx
  database_mysql:
    container_name: mysql_container
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.2'
          memory: 512M
    environment:
    - MYSQL_ROOT_PASSWORD=2452329511
    - MYSQL_DATABASE=anubis_system
    - MYSQL_USER=anubis
    - MYSQL_PASSWORD=2452329511
    image: mysql:8.0
    networks:
    - anubis_network
    ports:
    - 3306:3306
    restart: unless-stopped
    volumes:
    - ./volumes/database_data:/var/lib/mysql
    - ./volumes/logs:/var/log/mysql
  grafana:
    container_name: grafana_container
    depends_on:
    - prometheus
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M
    environment:
    - GF_SECURITY_ADMIN_PASSWORD=anubis2024
    image: grafana/grafana:latest
    networks:
    - anubis_network
    ports:
    - 3000:3000
    restart: unless-stopped
    volumes:
    - ./volumes/monitoring_data:/var/lib/grafana
  ollama_service:
    container_name: ollama_container
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    environment:
    - OLLAMA_MODELS=/root/.ollama
    image: ollama/ollama:latest
    networks:
    - anubis_network
    ports:
    - 11434:11434
    restart: unless-stopped
    volumes:
    - ./volumes/ollama_models:/root/.ollama
    - ./volumes/logs:/app/logs
  prometheus:
    container_name: prometheus_container
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M
    image: prom/prometheus:latest
    networks:
    - anubis_network
    ports:
    - 9090:9090
    restart: unless-stopped
    volumes:
    - ./services/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    - ./volumes/monitoring_data:/prometheus
  universal_ai:
    build: ./services/universal_ai
    container_name: universal_ai_container
    depends_on:
    - database_mysql
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M
    environment:
    - PYTHONPATH=/app
    - DATABASE_URL=mysql://anubis:2452329511@database_mysql:3306/anubis_system
    networks:
    - anubis_network
    ports:
    - 8001:8001
    restart: unless-stopped
    volumes:
    - ./volumes/logs:/app/logs
    - ./configs:/app/configs:ro
version: '3.8'
volumes:
  database_data:
    driver: local
  logs:
    driver: local
  monitoring_data:
    driver: local
  ollama_models:
    driver: local
