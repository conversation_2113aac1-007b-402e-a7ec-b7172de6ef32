# 🚀 Universal AI Assistant Suite

## مجموعة شاملة من أدوات الذكاء الاصطناعي ومراقبة الأداء

[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://python.org)
[![Platform](https://img.shields.io/badge/Platform-Windows-green.svg)](https://microsoft.com/windows)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)](README.md)
[![Tests](https://img.shields.io/badge/Tests-6/6%20Passed-success.svg)](README.md)

**Universal AI Assistant Suite** هي مجموعة متكاملة ومتقدمة من أدوات الذكاء الاصطناعي ومراقبة الأداء، مصممة خصيصاً لتحسين أداء VS Code والنظام بشكل عام.

### 🏆 **النتائج المحققة:**
- **⚡ تحسن المعالج:** 95.9% → 11.1% (**تحسن 84.8%!**)
- **💾 تحسن الذاكرة:** 89.0% → 62.2% (**تحسن 26.8%!**)
- **🧩 تحسن VS Code:** 56.2% → 26.0% (**تحسن 30.2%!**)
- **🖥️ حالة النظام:** من شبه متجمد إلى سريع ومستجيب

---

## 📁 **محتويات المجموعة**

### 🚀 **VS Code Performance Optimizer**
نظام متقدم لتحسين أداء VS Code مع نتائج مذهلة:
- **3 واجهات مختلفة** (موحدة، متقدمة، مستقرة)
- **Task Manager متقدم** مع تحكم كامل في العمليات
- **تحسين تلقائي للإعدادات** مع 57 إعداد محسن
- **تعطيل الإضافات الثقيلة** لتوفير الموارد
- **مراقبة الشبكة والأمان** في الوقت الفعلي

### 🎛️ **VSCode Control Center**
مركز تحكم شامل لمراقبة وإدارة VS Code:
- **مراقبة العمليات** في الوقت الفعلي
- **إحصائيات مفصلة** للنظام والموارد
- **أدوات تنظيف وتحسين** متقدمة
- **واجهات متعددة** للاستخدامات المختلفة

### 🤖 **AI Agents System**
نظام وكلاء ذكيين متطور للتحليل والتوصيات:
- **🔍 Process Analyzer** - محلل العمليات المتقدم
- **⚡ Performance Optimizer** - محسن الأداء الذكي
- **🛡️ Security Monitor** - مراقب الأمان المتقدم
- **💡 Smart Recommendations** - التوصيات الذكية المتعلمة
- **🌟 Gemini Agent** - وكيل Gemini للتحليل المتقدم
- **🦙 Ollama Agent** - وكيل Ollama للتحليل المحلي
- ✅ **اختبارات شاملة** - نظام اختبار متكامل مع تقارير مفصلة

## 🚀 البدء السريع

### المتطلبات
- Python 3.8+
- MySQL 8.0+
- pip

### التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/your-username/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate     # على Windows

# تثبيت المتطلبات
pip install -r requirements.txt
pip install -r requirements_database.txt

# إعداد قاعدة البيانات
python database/setup/direct_setup.py

# تشغيل النظام
python main.py
```

### إعداد قاعدة البيانات

```bash
# إعداد سريع
python database/setup/direct_setup.py

# اختبار الاتصال
python database/tests/test_connection.py

# تشغيل جميع الاختبارات
python database/tests/run_all_tests.py
```

## 📁 هيكل المشروع

```
Universal-AI-Assistants/
├── 📋 README.md                 # دليل المشروع الرئيسي
├── 🐍 main.py                   # نقطة البداية الرئيسية
├── 📦 requirements.txt          # متطلبات Python الأساسية
├── 📦 requirements_database.txt # متطلبات قاعدة البيانات
├── 🚫 .gitignore               # ملفات مستبعدة من Git
├── 📋 PROJECT_STRUCTURE.md     # توثيق هيكل المشروع
│
├── 🧠 core/                     # النواة الأساسية للنظام
├── 🤖 agents/                   # الوكلاء الذكيون
├── 🗄️ database/                # قاعدة البيانات والاختبارات
│   ├── 🔧 core/                # ملفات قاعدة البيانات الأساسية
│   ├── 🧪 tests/               # اختبارات قاعدة البيانات
│   ├── ⚙️ setup/               # ملفات الإعداد
│   └── 📚 docs/                # توثيق قاعدة البيانات
├── ⚙️ configs/                 # ملفات الإعدادات
├── 🔌 plugins/                 # النظام الإضافي
├── 🧪 tests/                   # اختبارات النظام
├── 📚 docs/                    # التوثيق
├── 📜 scripts/                 # سكريبتات مساعدة
├── 📄 templates/               # قوالب المشاريع
├── 💼 workspace/               # مساحة العمل
└── 📦 archive/                 # الأرشيف (مستبعد من Git)
```

## 🧪 الاختبارات

### اختبارات قاعدة البيانات
```bash
# اختبار الاتصال الأساسي
python database/tests/test_connection.py

# التحقق المبسط
python database/tests/simple_validation.py

# الاختبار الشامل
python database/tests/comprehensive_test.py

# اختبار الضغط
python database/tests/stress_test.py

# تشغيل جميع اختبارات قاعدة البيانات
python database/tests/run_all_tests.py
```

### اختبارات النظام
```bash
# تشغيل جميع اختبارات النظام
python tests/run_all_tests.py

# اختبار تفاعلي
python tests/ask_anubis.py

# اختبار النظام الشامل
python tests/test_anubis_system.py
```

## 📊 حالة النظام

### قاعدة البيانات
- ✅ **MySQL 8.0.42** - يعمل بشكل مثالي
- ✅ **6 جداول** - جميعها منشأة ومختبرة
- ✅ **42+ سجل** - بيانات تجريبية جاهزة
- ✅ **100% نجاح** في جميع الاختبارات
- ✅ **أداء ممتاز** - متوسط وقت الاستعلام 0.001s

### الاختبارات
- ✅ **5/5 اختبارات** نجحت بنسبة 100%
- ✅ **اختبار الاتصال** - نجح
- ✅ **التحقق المبسط** - نجح
- ✅ **الاختبار الشامل** - نجح
- ✅ **اختبار الضغط** - نجح
- ✅ **مدير قاعدة البيانات** - نجح

## 🛠️ الصيانة والتنظيم

```bash
# تنظيف وتنظيم المشروع
python cleanup_and_organize.py

# عرض هيكل المشروع
cat PROJECT_STRUCTURE.md

# عرض ملخص الاختبارات
cat database/docs/TEST_SUMMARY.md
```

## 📖 التوثيق

- [📋 دليل التثبيت](docs/installation_guide.md)
- [📋 دليل المستخدم](docs/user_guide.md)
- [📋 دليل المطور](docs/developer_guide.md)
- [📋 هيكل المشروع](PROJECT_STRUCTURE.md)
- [📋 ملخص الاختبارات](database/docs/TEST_SUMMARY.md)
- [📋 ملخص الإصلاحات](database/docs/FIXES_SUMMARY.md)

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```json
{
  "database": {
    "mysql": {
      "host": "localhost",
      "port": 3306,
      "user": "root",
      "password": "your_password",
      "database": "anubis_system",
      "charset": "utf8mb4"
    }
  }
}
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### معايير الكود
- اتباع معايير PEP 8
- إضافة اختبارات للميزات الجديدة
- توثيق الكود باللغة العربية والإنجليزية
- تشغيل جميع الاختبارات قبل الإرسال

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🏺 حول أنوبيس

أنوبيس هو إله مصري قديم مرتبط بالحكمة والمعرفة والحماية. اخترنا هذا الاسم لنظامنا الذكي لأنه يجسد:

- 🧠 **الحكمة** - في تحليل وفهم المشاريع
- 🔍 **المعرفة** - في قاعدة البيانات الشاملة
- 🛡️ **الحماية** - في كشف الأخطاء والمشاكل
- ⚡ **القوة** - في الأداء والكفاءة

## 📞 الدعم والتواصل

- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [انضم إلى خادمنا](https://discord.gg/anubis-ai)
- 📱 **Twitter**: [@AnubisAI](https://twitter.com/AnubisAI)
- 🐛 **تقرير الأخطاء**: [GitHub Issues](https://github.com/your-username/Universal-AI-Assistants/issues)

---

**تم تطوير هذا النظام بحب وشغف لخدمة المجتمع التقني العربي** ❤️🏺

**الإصدار الحالي:** 2.0.0 - منظم ومحسن  
**آخر تحديث:** 14 يوليو 2025
