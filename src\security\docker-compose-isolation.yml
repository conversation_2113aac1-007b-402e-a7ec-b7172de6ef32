version: '3.8'

# 🏺 نظام العزل المتقدم - أنوبيس
# Anubis Advanced Isolation System

services:
  # خدمة API المعزولة
  anubis-api-isolated:
    build:
      context: ./isolation/api
      dockerfile: Dockerfile
    container_name: anubis-api-isolated
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-api-data:/app/data
      - anubis-api-logs:/app/logs
      - anubis-api-config:/app/config
    environment:
      - ANUBIS_SERVICE=api
      - ANUBIS_ISOLATED=true
      - PYTHONUNBUFFERED=1
      - API_HOST=0.0.0.0
      - API_PORT=8080
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    mem_limit: 512m
    cpus: 0.5
    pids_limit: 100
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=api"
      - "anubis.type=web"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # خدمة العامل المعزولة
  anubis-worker-isolated:
    build:
      context: ./isolation/worker
      dockerfile: Dockerfile
    container_name: anubis-worker-isolated
    restart: unless-stopped
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-worker-data:/app/data
      - anubis-worker-logs:/app/logs
      - anubis-worker-config:/app/config
    environment:
      - ANUBIS_SERVICE=worker
      - ANUBIS_ISOLATED=true
      - PYTHONUNBUFFERED=1
      - WORKER_CONCURRENCY=4
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    mem_limit: 1g
    cpus: 1.0
    pids_limit: 200
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=200m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=worker"
      - "anubis.type=background"
    depends_on:
      - anubis-api-isolated

  # خدمة المراقبة المعزولة
  anubis-monitor-isolated:
    build:
      context: ./isolation/monitor
      dockerfile: Dockerfile
    container_name: anubis-monitor-isolated
    restart: unless-stopped
    ports:
      - "9090:9090"
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-monitor-data:/app/data
      - anubis-monitor-logs:/app/logs
      - anubis-monitor-config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ANUBIS_SERVICE=monitor
      - ANUBIS_ISOLATED=true
      - PYTHONUNBUFFERED=1
      - MONITOR_PORT=9090
    security_opt:
      - no-new-privileges:true
    mem_limit: 256m
    cpus: 0.25
    pids_limit: 50
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=monitor"
      - "anubis.type=monitoring"
    depends_on:
      - anubis-api-isolated

  # قاعدة بيانات معزولة
  anubis-database-isolated:
    image: postgres:15-alpine
    container_name: anubis-database-isolated
    restart: unless-stopped
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-db-data:/var/lib/postgresql/data
      - anubis-db-config:/etc/postgresql
    environment:
      - POSTGRES_DB=anubis_isolated
      - POSTGRES_USER=anubis
      - POSTGRES_PASSWORD=anubis_secure_2024
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    security_opt:
      - no-new-privileges:true
    mem_limit: 512m
    cpus: 0.5
    pids_limit: 100
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /run:noexec,nosuid,size=100m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=database"
      - "anubis.type=storage"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U anubis -d anubis_isolated"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # خدمة Redis معزولة
  anubis-redis-isolated:
    image: redis:7-alpine
    container_name: anubis-redis-isolated
    restart: unless-stopped
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-redis-data:/data
      - anubis-redis-config:/usr/local/etc/redis
    command: redis-server --appendonly yes --requirepass anubis_redis_2024
    environment:
      - REDIS_PASSWORD=anubis_redis_2024
    security_opt:
      - no-new-privileges:true
    mem_limit: 256m
    cpus: 0.25
    pids_limit: 50
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=redis"
      - "anubis.type=cache"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # خدمة Nginx معزولة (Reverse Proxy)
  anubis-nginx-isolated:
    image: nginx:alpine
    container_name: anubis-nginx-isolated
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    networks:
      - anubis-isolation-net
    volumes:
      - anubis-nginx-config:/etc/nginx
      - anubis-nginx-logs:/var/log/nginx
      - anubis-ssl-certs:/etc/ssl/certs
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    security_opt:
      - no-new-privileges:true
    mem_limit: 128m
    cpus: 0.25
    pids_limit: 50
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
      - /var/cache/nginx:noexec,nosuid,size=50m
    labels:
      - "anubis.system=isolation"
      - "anubis.service=nginx"
      - "anubis.type=proxy"
    depends_on:
      - anubis-api-isolated
      - anubis-monitor-isolated

# الشبكات المعزولة
networks:
  anubis-isolation-net:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      - "anubis.system=isolation"
      - "anubis.type=network"

# الأحجام المعزولة
volumes:
  # أحجام API
  anubis-api-data:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=api"
      - "anubis.type=data"
  
  anubis-api-logs:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=api"
      - "anubis.type=logs"
  
  anubis-api-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=api"
      - "anubis.type=config"

  # أحجام Worker
  anubis-worker-data:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=worker"
      - "anubis.type=data"
  
  anubis-worker-logs:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=worker"
      - "anubis.type=logs"
  
  anubis-worker-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=worker"
      - "anubis.type=config"

  # أحجام Monitor
  anubis-monitor-data:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=monitor"
      - "anubis.type=data"
  
  anubis-monitor-logs:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=monitor"
      - "anubis.type=logs"
  
  anubis-monitor-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=monitor"
      - "anubis.type=config"

  # أحجام قاعدة البيانات
  anubis-db-data:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=database"
      - "anubis.type=data"
  
  anubis-db-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=database"
      - "anubis.type=config"

  # أحجام Redis
  anubis-redis-data:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=redis"
      - "anubis.type=data"
  
  anubis-redis-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=redis"
      - "anubis.type=config"

  # أحجام Nginx
  anubis-nginx-config:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=nginx"
      - "anubis.type=config"
  
  anubis-nginx-logs:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=nginx"
      - "anubis.type=logs"
  
  anubis-ssl-certs:
    driver: local
    labels:
      - "anubis.system=isolation"
      - "anubis.service=nginx"
      - "anubis.type=ssl"
