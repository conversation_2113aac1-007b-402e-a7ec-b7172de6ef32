# نظام المساعدين الذكيين العالمي - المتطلبات
# Universal AI Assistants System - Requirements

# المتطلبات الأساسية - Core Requirements
pathlib2>=2.3.7
typing-extensions>=4.0.0

# قواعد البيانات - Database Support
# sqlite3 متوفر مع Python - SQLite3 included with Python
mysql-connector-python>=8.0.0  # لـ MySQL - for MySQL
psycopg2-binary>=2.9.0          # لـ PostgreSQL - for PostgreSQL

# معالجة الملفات والنصوص - File and Text Processing
chardet>=5.0.0
pathspec>=0.10.0

# JSON وYAML - JSON and YAML Processing
pyyaml>=6.0

# التاريخ والوقت - Date and Time Utilities
python-dateutil>=2.8.0

# السجلات المتقدمة - Advanced Logging
colorlog>=6.7.0

# أدوات التطوير - Development Tools
black>=22.0.0      # تنسيق الكود - Code formatting
flake8>=5.0.0      # فحص جودة الكود - Code quality checking
pylint>=2.17.0     # تحليل الكود - Code analysis
pytest>=7.0.0     # الاختبارات - Testing framework
pytest-cov>=4.0.0 # تغطية الاختبارات - Test coverage

# دعم المشاريع المختلفة - Project Type Support
streamlit>=1.28.0  # لمشاريع Streamlit - for Streamlit projects
django>=4.0.0      # لمشاريع Django - for Django projects
fastapi>=0.100.0   # لمشاريع FastAPI - for FastAPI projects
flask>=2.3.0       # لمشاريع Flask - for Flask projects

# أدوات إضافية - Additional Utilities
tqdm>=4.64.0        # شريط التقدم - Progress bars
click>=8.0.0        # واجهة سطر الأوامر - CLI interface

# تحليل الكود والأمان - Code Analysis and Security
bandit>=1.7.0       # فحص الأمان - Security linting
safety>=2.3.0       # فحص الثغرات - Vulnerability checking
radon>=5.1.0        # قياس التعقيد - Complexity metrics

# معالجة البيانات - Data Processing (للتحليلات)
pandas>=2.0.0       # معالجة البيانات - Data manipulation
numpy>=1.24.0       # العمليات الرياضية - Numerical operations

# أدوات النظام - System Utilities
psutil>=5.9.0       # معلومات النظام - System information
gitpython>=3.1.0    # التعامل مع Git - Git integration
