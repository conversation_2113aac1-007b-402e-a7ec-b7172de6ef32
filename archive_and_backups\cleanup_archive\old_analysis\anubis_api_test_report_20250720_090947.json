{"test_results": {"/": {"url": "http://localhost:8080/", "status_code": 200, "response_time_ms": 15.1, "success": true, "timestamp": "2025-07-20T09:09:46.931812", "data": {"message": "🏺 Anubis API Service - Isolated", "status": "running", "service": "api", "isolated": true, "timestamp": "2025-07-20T06:09:46.932033"}}, "/health": {"url": "http://localhost:8080/health", "status_code": 200, "response_time_ms": 23.14, "success": true, "timestamp": "2025-07-20T09:09:46.955344", "data": {"status": "healthy", "service": "api", "timestamp": "2025-07-20T06:09:46.955171", "version": "1.0.0", "isolated": true}}, "/info": {"url": "http://localhost:8080/info", "status_code": 200, "response_time_ms": 15.66, "success": true, "timestamp": "2025-07-20T09:09:46.971393", "data": {"name": "Anubis API Service", "version": "1.0.0", "description": "خدمة API معزولة لنظام أنوبيس", "endpoints": ["/", "/health", "/info", "/status", "/metrics", "/docs", "/redoc"]}}, "/docs": {"url": "http://localhost:8080/docs", "status_code": 200, "response_time_ms": 6.87, "success": true, "timestamp": "2025-07-20T09:09:46.978729", "data": "\n    <!DOCTYPE html>\n    <html>\n    <head>\n    <link type=\"text/css\" rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css\">\n    <link rel=\"shortcut icon\" href=\"http"}, "/redoc": {"url": "http://localhost:8080/redoc", "status_code": 200, "response_time_ms": 16.91, "success": true, "timestamp": "2025-07-20T09:09:46.996209", "data": "\n    <!DOCTYPE html>\n    <html>\n    <head>\n    <title>🏺 Anubis API Service - ReDoc</title>\n    <!-- needed for adaptive design -->\n    <meta charset=\"utf-8\"/>\n    <meta name=\"viewport\" content=\"width="}, "/status": {"url": "http://localhost:8080/status", "status_code": 500, "response_time_ms": 17.75, "success": false, "timestamp": "2025-07-20T09:09:47.114163", "error": "{\"error\":\"خطأ داخلي في الخدمة\",\"service\":\"api\",\"timestamp\":\"2025-07-20T06:09:47.112182\"}"}, "/metrics": {"url": "http://localhost:8080/metrics", "status_code": 200, "response_time_ms": 97.74, "success": true, "timestamp": "2025-07-20T09:09:47.212462", "data": {"metrics": "# HELP python_gc_objects_collected_total Objects collected during gc\n# TYPE python_gc_objects_collected_total counter\npython_gc_objects_collected_total{generation=\"0\"} 2830.0\npython_gc_objects_collected_total{generation=\"1\"} 9271.0\npython_gc_objects_collected_total{generation=\"2\"} 1042.0\n# HELP python_gc_objects_uncollectable_total Uncollectable objects found during GC\n# TYPE python_gc_objects_uncollectable_total counter\npython_gc_objects_uncollectable_total{generation=\"0\"} 0.0\npython_gc_objects_uncollectable_total{generation=\"1\"} 0.0\npython_gc_objects_uncollectable_total{generation=\"2\"} 0.0\n# HELP python_gc_collections_total Number of times this generation was collected\n# TYPE python_gc_collections_total counter\npython_gc_collections_total{generation=\"0\"} 160.0\npython_gc_collections_total{generation=\"1\"} 14.0\npython_gc_collections_total{generation=\"2\"} 1.0\n# HELP python_info Python platform information\n# TYPE python_info gauge\npython_info{implementation=\"CPython\",major=\"3\",minor=\"11\",patchlevel=\"13\",version=\"3.11.13\"} 1.0\n# HELP process_virtual_memory_bytes Virtual memory size in bytes.\n# TYPE process_virtual_memory_bytes gauge\nprocess_virtual_memory_bytes 1.70139648e+08\n# HELP process_resident_memory_bytes Resident memory size in bytes.\n# TYPE process_resident_memory_bytes gauge\nprocess_resident_memory_bytes 5.9277312e+07\n# HELP process_start_time_seconds Start time of the process since unix epoch in seconds.\n# TYPE process_start_time_seconds gauge\nprocess_start_time_seconds 1.75299157675e+09\n# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.\n# TYPE process_cpu_seconds_total counter\nprocess_cpu_seconds_total 2.25\n# HELP process_open_fds Number of open file descriptors.\n# TYPE process_open_fds gauge\nprocess_open_fds 18.0\n# HELP process_max_fds Maximum number of open file descriptors.\n# TYPE process_max_fds gauge\nprocess_max_fds 1.048576e+06\n"}}}, "metadata": {"base_url": "http://localhost:8080", "test_timestamp": "20250720_090947", "tester_version": "1.0.0"}}