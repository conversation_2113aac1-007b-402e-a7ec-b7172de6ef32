{"anubis_ai_team": {"phi3:mini": {"role": "المحلل السريع", "specialty": "تحليل سريع وإجابات مباشرة", "use_case": "تحليل الكود، فحص الأخطاء، اقتراحات سريعة", "speed": "عالية جداً", "accuracy": "جيدة"}, "mistral:7b": {"role": "المطور الخبير", "specialty": "البرمجة والتطوير المتقدم", "use_case": "كتابة الكود، حل المشاكل التقنية، التصميم", "speed": "عالية", "accuracy": "ممتازة"}, "llama3:8b": {"role": "المستشار الاستراتيجي", "specialty": "التخطيط والاستراتيجية", "use_case": "تخطيط المشاريع، اتخاذ القرارات، التوجيه", "speed": "متوسطة", "accuracy": "ممتازة جداً"}, "strikegpt-r1-zero-8b": {"role": "المبدع والمبتكر", "specialty": "الحلول الإبداعية والابتكار", "use_case": "حلول غير تقليدية، تحسينات إبداعية، أفكار جديدة", "speed": "متوسطة", "accuracy": "عالية"}, "Qwen2.5-VL-7B": {"role": "المحلل البصري", "specialty": "تحليل الصور والمحتوى البصري", "use_case": "تحليل الواجهات، فهم الرسوم البيانية، التوثيق البصري", "speed": "متوسطة", "accuracy": "ممتازة"}}, "gemini_coordinator": {"role": "المنسق الرئيسي", "specialty": "التنسيق والإشراف العام", "use_case": "إدارة المشروع، التنسيق بين النماذج، المراجعة النهائية"}, "collaboration_workflow": {"project_phases": {"1_analysis": {"name": "مرحلة التحليل", "primary_model": "phi3:mini", "support_models": ["mistral:7b"], "tasks": ["تحليل المتطلبات", "فحص الكود الحالي", "تحديد المشاكل", "اقتراح حلول سريعة"]}, "2_planning": {"name": "مرحلة التخطيط", "primary_model": "llama3:8b", "support_models": ["gemini_cli"], "tasks": ["وضع الاستراتيجية", "تخطيط المراحل", "تحديد الأولويات", "توزيع المهام"]}, "3_development": {"name": "مرحلة التطوير", "primary_model": "mistral:7b", "support_models": ["phi3:mini", "strikegpt-r1-zero-8b"], "tasks": ["كتابة الكود", "تطوير الميزات", "حل المشاكل التقنية", "تحسين الأداء"]}, "4_innovation": {"name": "مرحلة الابتكار", "primary_model": "strikegpt-r1-zero-8b", "support_models": ["llama3:8b", "Qwen2.5-VL-7B"], "tasks": ["اقتراح حلول إبداعية", "تحسينات غير تقليدية", "أ<PERSON><PERSON><PERSON><PERSON> جديدة", "تطوير مميز"]}, "5_review": {"name": "مرحلة المراجعة", "primary_model": "gemini_cli", "support_models": ["llama3:8b", "Qwen2.5-VL-7B"], "tasks": ["مراجعة شاملة", "<PERSON><PERSON><PERSON> الجودة", "التوثيق", "الموافقة النهائية"]}}}, "usage_guidelines": {"quick_analysis": "استخدم phi3:mini للتحليل السريع", "complex_development": "استخدم mistral:7b للتطوير المعقد", "strategic_planning": "استخدم llama3:8b للتخطيط الاستراتيجي", "creative_solutions": "استخدم strikegpt-r1-zero-8b للحلول الإبداعية", "visual_analysis": "استخدم Qwen2.5-VL-7B للتحليل البصري", "final_review": "استخدم Gemini CLI للمراجعة النهائية"}, "created_at": "2025-07-20T09:37:57.802500"}