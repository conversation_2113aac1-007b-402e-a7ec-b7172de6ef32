#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 محلل محادثات Cline لنظام أنوبيس
Anubis Cline Conversation Analyzer
"""

import json
import re
from pathlib import Path
from datetime import datetime
from collections import Counter, defaultdict
import sys

class AnubisClinetConversationAnalyzer:
    """محلل محادثات Cline المتقدم"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.content = ""
        self.analysis_results = {}
        self.conversation_data = {
            "user_messages": [],
            "assistant_messages": [],
            "commands": [],
            "file_operations": [],
            "errors": [],
            "timestamps": []
        }
        
    def load_conversation(self):
        """تحميل ملف المحادثة"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            print(f"✅ تم تحميل الملف: {self.file_path}")
            print(f"📊 حجم الملف: {len(self.content):,} حرف")
            print(f"📄 عدد الأسطر: {len(self.content.splitlines()):,} سطر")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل الملف: {e}")
            return False
    
    def extract_basic_stats(self):
        """استخراج الإحصائيات الأساسية"""
        lines = self.content.splitlines()
        
        stats = {
            "total_lines": len(lines),
            "total_characters": len(self.content),
            "total_words": len(self.content.split()),
            "user_messages": len(re.findall(r'\*\*User:\*\*', self.content)),
            "assistant_messages": len(re.findall(r'\*\*Assistant:\*\*', self.content)),
            "commands": len(re.findall(r'<command>', self.content)),
            "file_operations": len(re.findall(r'<file_operation>', self.content)),
            "errors": len(re.findall(r'Error|error|خطأ|❌', self.content))
        }
        
        self.analysis_results["basic_stats"] = stats
        return stats
    
    def extract_timestamps(self):
        """استخراج الطوابع الزمنية"""
        timestamp_pattern = r'(\d{1,2}/\d{1,2}/\d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)'
        timestamps = re.findall(timestamp_pattern, self.content)
        
        self.conversation_data["timestamps"] = timestamps
        self.analysis_results["timestamps"] = {
            "count": len(timestamps),
            "first": timestamps[0] if timestamps else None,
            "last": timestamps[-1] if timestamps else None
        }
        
        return timestamps
    
    def extract_commands(self):
        """استخراج الأوامر المنفذة"""
        command_pattern = r'<command>(.*?)</command>'
        commands = re.findall(command_pattern, self.content, re.DOTALL)
        
        # تنظيف الأوامر
        cleaned_commands = []
        for cmd in commands:
            cmd = cmd.strip()
            if cmd:
                cleaned_commands.append(cmd)
        
        self.conversation_data["commands"] = cleaned_commands
        
        # تحليل أنواع الأوامر
        command_types = Counter()
        for cmd in cleaned_commands:
            if cmd.startswith('python'):
                command_types['python'] += 1
            elif cmd.startswith('cd'):
                command_types['navigation'] += 1
            elif cmd.startswith('ls') or cmd.startswith('dir'):
                command_types['listing'] += 1
            elif cmd.startswith('git'):
                command_types['git'] += 1
            elif cmd.startswith('docker'):
                command_types['docker'] += 1
            elif cmd.startswith('pip') or cmd.startswith('npm'):
                command_types['package_management'] += 1
            else:
                command_types['other'] += 1
        
        self.analysis_results["commands"] = {
            "total": len(cleaned_commands),
            "types": dict(command_types),
            "most_common": command_types.most_common(5)
        }
        
        return cleaned_commands
    
    def extract_file_operations(self):
        """استخراج عمليات الملفات"""
        # البحث عن أنماط عمليات الملفات
        patterns = {
            "file_creation": r'File saved\.|Created file|New file',
            "file_editing": r'str_replace_editor|edit|modify',
            "file_reading": r'view|cat|read',
            "file_deletion": r'delete|remove|rm'
        }
        
        operations = defaultdict(int)
        for op_type, pattern in patterns.items():
            matches = re.findall(pattern, self.content, re.IGNORECASE)
            operations[op_type] = len(matches)
        
        self.analysis_results["file_operations"] = dict(operations)
        return operations
    
    def extract_errors_and_issues(self):
        """استخراج الأخطاء والمشاكل"""
        error_patterns = [
            r'Error:.*',
            r'Exception:.*',
            r'❌.*',
            r'خطأ.*',
            r'Failed.*',
            r'فشل.*'
        ]
        
        all_errors = []
        for pattern in error_patterns:
            errors = re.findall(pattern, self.content, re.IGNORECASE)
            all_errors.extend(errors)
        
        self.conversation_data["errors"] = all_errors
        self.analysis_results["errors"] = {
            "total": len(all_errors),
            "types": Counter([error[:50] + "..." if len(error) > 50 else error for error in all_errors])
        }
        
        return all_errors
    
    def analyze_conversation_flow(self):
        """تحليل تدفق المحادثة"""
        # تقسيم المحادثة إلى أجزاء
        sections = re.split(r'\*\*(?:User|Assistant):\*\*', self.content)
        
        flow_analysis = {
            "total_exchanges": len(sections) // 2,
            "avg_section_length": sum(len(s) for s in sections) // len(sections) if sections else 0,
            "longest_section": max(len(s) for s in sections) if sections else 0,
            "shortest_section": min(len(s) for s in sections) if sections else 0
        }
        
        self.analysis_results["conversation_flow"] = flow_analysis
        return flow_analysis
    
    def extract_topics_and_keywords(self):
        """استخراج المواضيع والكلمات المفتاحية"""
        # كلمات مفتاحية تقنية
        tech_keywords = [
            'python', 'docker', 'git', 'database', 'mysql', 'sqlite',
            'anubis', 'agent', 'scanner', 'isolation', 'test',
            'file', 'directory', 'config', 'setup', 'install'
        ]
        
        keyword_counts = Counter()
        content_lower = self.content.lower()
        
        for keyword in tech_keywords:
            count = content_lower.count(keyword)
            if count > 0:
                keyword_counts[keyword] = count
        
        # البحث عن أسماء الملفات
        file_pattern = r'([a-zA-Z_][a-zA-Z0-9_]*\.py|[a-zA-Z_][a-zA-Z0-9_]*\.md|[a-zA-Z_][a-zA-Z0-9_]*\.json)'
        files_mentioned = re.findall(file_pattern, self.content)
        file_counts = Counter(files_mentioned)
        
        self.analysis_results["topics"] = {
            "tech_keywords": dict(keyword_counts.most_common(10)),
            "files_mentioned": dict(file_counts.most_common(10))
        }
        
        return keyword_counts, file_counts
    
    def generate_summary_report(self):
        """إنشاء تقرير ملخص شامل"""
        print("\n" + "🏺" + "=" * 60)
        print("📊 تقرير تحليل محادثة Cline - نظام أنوبيس")
        print("=" * 62)
        
        # الإحصائيات الأساسية
        stats = self.analysis_results.get("basic_stats", {})
        print(f"\n📈 الإحصائيات الأساسية:")
        print(f"   📄 إجمالي الأسطر: {stats.get('total_lines', 0):,}")
        print(f"   📝 إجمالي الكلمات: {stats.get('total_words', 0):,}")
        print(f"   👤 رسائل المستخدم: {stats.get('user_messages', 0)}")
        print(f"   🤖 رسائل المساعد: {stats.get('assistant_messages', 0)}")
        print(f"   ⚡ الأوامر المنفذة: {stats.get('commands', 0)}")
        print(f"   ❌ الأخطاء المكتشفة: {stats.get('errors', 0)}")
        
        # الطوابع الزمنية
        timestamps = self.analysis_results.get("timestamps", {})
        print(f"\n⏰ التوقيتات:")
        print(f"   🕐 بداية المحادثة: {timestamps.get('first', 'غير محدد')}")
        print(f"   🕕 نهاية المحادثة: {timestamps.get('last', 'غير محدد')}")
        
        # تحليل الأوامر
        commands = self.analysis_results.get("commands", {})
        print(f"\n⚡ تحليل الأوامر:")
        print(f"   📊 إجمالي الأوامر: {commands.get('total', 0)}")
        if commands.get('most_common'):
            print("   🔝 أكثر أنواع الأوامر:")
            for cmd_type, count in commands['most_common']:
                print(f"      - {cmd_type}: {count}")
        
        # المواضيع والكلمات المفتاحية
        topics = self.analysis_results.get("topics", {})
        print(f"\n🔍 الكلمات المفتاحية الأكثر تكراراً:")
        for keyword, count in list(topics.get('tech_keywords', {}).items())[:5]:
            print(f"   - {keyword}: {count}")
        
        print(f"\n📁 الملفات الأكثر ذكراً:")
        for file, count in list(topics.get('files_mentioned', {}).items())[:5]:
            print(f"   - {file}: {count}")
        
        # تدفق المحادثة
        flow = self.analysis_results.get("conversation_flow", {})
        print(f"\n💬 تحليل تدفق المحادثة:")
        print(f"   🔄 إجمالي التبادلات: {flow.get('total_exchanges', 0)}")
        print(f"   📏 متوسط طول القسم: {flow.get('avg_section_length', 0):,} حرف")
        
        return self.analysis_results
    
    def save_detailed_report(self):
        """حفظ تقرير مفصل"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"anubis_cline_analysis_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "analysis_results": self.analysis_results,
                    "conversation_data": {
                        "commands_sample": self.conversation_data["commands"][:10],
                        "errors_sample": self.conversation_data["errors"][:10],
                        "timestamps": self.conversation_data["timestamps"]
                    },
                    "metadata": {
                        "analyzed_file": str(self.file_path),
                        "analysis_timestamp": timestamp,
                        "analyzer_version": "1.0.0"
                    }
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ التقرير المفصل: {report_file}")
            return report_file
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None
    
    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🏺 بدء تحليل محادثة Cline...")
        
        if not self.load_conversation():
            return False
        
        print("📊 استخراج الإحصائيات الأساسية...")
        self.extract_basic_stats()
        
        print("⏰ استخراج الطوابع الزمنية...")
        self.extract_timestamps()
        
        print("⚡ تحليل الأوامر...")
        self.extract_commands()
        
        print("📁 تحليل عمليات الملفات...")
        self.extract_file_operations()
        
        print("❌ استخراج الأخطاء...")
        self.extract_errors_and_issues()
        
        print("💬 تحليل تدفق المحادثة...")
        self.analyze_conversation_flow()
        
        print("🔍 استخراج المواضيع...")
        self.extract_topics_and_keywords()
        
        print("📋 إنشاء التقرير...")
        self.generate_summary_report()
        
        print("💾 حفظ التقرير المفصل...")
        self.save_detailed_report()
        
        print("\n✅ تم إكمال التحليل بنجاح!")
        return True

def main():
    """الدالة الرئيسية"""
    file_path = "cline_task_jul-20-2025_7-13-06-am.md"
    
    if not Path(file_path).exists():
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    analyzer = AnubisClinetConversationAnalyzer(file_path)
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
