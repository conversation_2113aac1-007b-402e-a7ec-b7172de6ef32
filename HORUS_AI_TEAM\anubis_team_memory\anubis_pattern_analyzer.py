#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 محلل الأنماط لفريق الذكاء الاصطناعي - أنوبيس
Anubis AI Team Pattern Analyzer
"""

import json
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import statistics

class AnubisPatternAnalyzer:
    """محلل أنماط فريق الذكاء الاصطناعي"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
        self.memory_root = memory_manager.memory_root
        self.insights_path = self.memory_root / "insights"
        self.learning_path = self.memory_root / "learning"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self.insights_path.mkdir(exist_ok=True)
        self.learning_path.mkdir(exist_ok=True)
    
    def identify_success_patterns(self) -> Dict:
        """تحديد أنماط النجاح"""
        
        print("🔍 تحليل أنماط النجاح...")
        
        # جمع جميع التجارب الناجحة
        successful_experiences = self.collect_successful_experiences()
        
        if not successful_experiences:
            return {"message": "لا توجد تجارب ناجحة كافية للتحليل"}
        
        patterns = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_successful_experiences": len(successful_experiences),
            "team_composition_patterns": self.analyze_team_composition_patterns(successful_experiences),
            "timing_patterns": self.analyze_timing_patterns(successful_experiences),
            "task_type_patterns": self.analyze_task_type_patterns(successful_experiences),
            "model_synergy_patterns": self.analyze_model_synergy_patterns(successful_experiences),
            "workflow_sequence_patterns": self.analyze_workflow_sequence_patterns(successful_experiences),
            "success_factors": self.extract_success_factors(successful_experiences)
        }
        
        # حفظ النتائج
        self.save_success_patterns(patterns)
        
        return patterns
    
    def analyze_failure_causes(self) -> Dict:
        """تحليل أسباب الفشل"""
        
        print("⚠️ تحليل أسباب الفشل...")
        
        # جمع التجارب الفاشلة
        failed_experiences = self.collect_failed_experiences()
        
        if not failed_experiences:
            return {"message": "لا توجد تجارب فاشلة للتحليل"}
        
        failure_analysis = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_failed_experiences": len(failed_experiences),
            "common_failure_points": self.identify_common_failure_points(failed_experiences),
            "model_specific_failures": self.analyze_model_specific_failures(failed_experiences),
            "task_type_failures": self.analyze_task_type_failures(failed_experiences),
            "timing_related_failures": self.analyze_timing_failures(failed_experiences),
            "resource_related_failures": self.analyze_resource_failures(failed_experiences),
            "improvement_recommendations": self.generate_failure_improvements(failed_experiences)
        }
        
        # حفظ النتائج
        self.save_failure_analysis(failure_analysis)
        
        return failure_analysis
    
    def detect_collaboration_synergies(self) -> Dict:
        """اكتشاف التآزر في التعاون"""
        
        print("🤝 تحليل التآزر في التعاون...")
        
        all_experiences = self.collect_all_experiences()
        
        synergies = {
            "analysis_timestamp": datetime.now().isoformat(),
            "total_experiences_analyzed": len(all_experiences),
            "model_pair_synergies": self.analyze_model_pair_synergies(all_experiences),
            "optimal_team_sizes": self.analyze_optimal_team_sizes(all_experiences),
            "sequence_synergies": self.analyze_sequence_synergies(all_experiences),
            "complementary_strengths": self.identify_complementary_strengths(all_experiences),
            "synergy_recommendations": self.generate_synergy_recommendations(all_experiences)
        }
        
        # حفظ النتائج
        self.save_collaboration_synergies(synergies)
        
        return synergies
    
    def predict_optimal_team_composition(self, task_type: str, task_complexity: str = "medium") -> Dict:
        """توقع التشكيل الأمثل للفريق"""
        
        print(f"🎯 توقع التشكيل الأمثل لمهمة: {task_type}")
        
        # تحليل التجارب السابقة لنفس نوع المهمة
        similar_experiences = self.get_experiences_by_task_type(task_type)
        
        # تحليل الأداء لكل نموذج في هذا النوع من المهام
        model_performance = self.analyze_model_performance_by_task_type(task_type, similar_experiences)
        
        # تحليل التآزر بين النماذج
        synergy_analysis = self.analyze_team_synergies(similar_experiences)
        
        # توقع التشكيل الأمثل
        optimal_composition = self.calculate_optimal_composition(
            task_type, task_complexity, model_performance, synergy_analysis
        )
        
        prediction = {
            "task_type": task_type,
            "task_complexity": task_complexity,
            "prediction_timestamp": datetime.now().isoformat(),
            "optimal_team_composition": optimal_composition,
            "model_performance_scores": model_performance,
            "synergy_scores": synergy_analysis,
            "confidence_level": self.calculate_prediction_confidence(similar_experiences),
            "expected_success_rate": self.estimate_success_rate(optimal_composition, task_type),
            "alternative_compositions": self.suggest_alternative_compositions(optimal_composition, model_performance)
        }
        
        return prediction
    
    def collect_successful_experiences(self, success_threshold: float = 0.7) -> List[Dict]:
        """جمع التجارب الناجحة"""
        successful_experiences = []
        
        for task_dir in self.memory_manager.experiences_path.iterdir():
            if task_dir.is_dir():
                for exp_file in task_dir.glob("experience_*.json"):
                    try:
                        with open(exp_file, 'r', encoding='utf-8') as f:
                            experience = json.load(f)
                        
                        if experience.get("success_score", 0) >= success_threshold:
                            successful_experiences.append(experience)
                            
                    except Exception as e:
                        print(f"⚠️ خطأ في قراءة {exp_file}: {e}")
        
        return successful_experiences
    
    def collect_failed_experiences(self, failure_threshold: float = 0.3) -> List[Dict]:
        """جمع التجارب الفاشلة"""
        failed_experiences = []
        
        for task_dir in self.memory_manager.experiences_path.iterdir():
            if task_dir.is_dir():
                for exp_file in task_dir.glob("experience_*.json"):
                    try:
                        with open(exp_file, 'r', encoding='utf-8') as f:
                            experience = json.load(f)
                        
                        if experience.get("success_score", 1.0) <= failure_threshold:
                            failed_experiences.append(experience)
                            
                    except Exception as e:
                        print(f"⚠️ خطأ في قراءة {exp_file}: {e}")
        
        return failed_experiences
    
    def collect_all_experiences(self) -> List[Dict]:
        """جمع جميع التجارب"""
        all_experiences = []
        
        for task_dir in self.memory_manager.experiences_path.iterdir():
            if task_dir.is_dir():
                for exp_file in task_dir.glob("experience_*.json"):
                    try:
                        with open(exp_file, 'r', encoding='utf-8') as f:
                            experience = json.load(f)
                        all_experiences.append(experience)
                    except Exception as e:
                        print(f"⚠️ خطأ في قراءة {exp_file}: {e}")
        
        return all_experiences
    
    def analyze_team_composition_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل أنماط تشكيل الفريق"""
        composition_patterns = defaultdict(int)
        composition_success_rates = defaultdict(list)
        
        for exp in experiences:
            models_used = []
            for result in exp.get("results", {}).get("execution_log", []):
                if result.get("success", False):
                    models_used.append(result["model"])
            
            if models_used:
                composition_key = tuple(sorted(set(models_used)))
                composition_patterns[composition_key] += 1
                composition_success_rates[composition_key].append(exp.get("success_score", 0))
        
        # حساب معدلات النجاح لكل تشكيل
        composition_analysis = {}
        for composition, count in composition_patterns.items():
            success_rates = composition_success_rates[composition]
            composition_analysis[",".join(composition)] = {
                "usage_count": count,
                "average_success_rate": statistics.mean(success_rates) if success_rates else 0,
                "success_consistency": 1 - statistics.stdev(success_rates) if len(success_rates) > 1 else 1
            }
        
        return composition_analysis
    
    def analyze_timing_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل أنماط التوقيت"""
        timing_analysis = {
            "optimal_execution_times": {},
            "time_vs_success_correlation": {},
            "model_speed_patterns": {}
        }
        
        model_times = defaultdict(list)
        time_success_pairs = []
        
        for exp in experiences:
            total_time = 0
            for result in exp.get("results", {}).get("execution_log", []):
                execution_time = result.get("execution_time", 0)
                model = result.get("model", "unknown")
                success = result.get("success", False)
                
                model_times[model].append(execution_time)
                total_time += execution_time
            
            time_success_pairs.append((total_time, exp.get("success_score", 0)))
        
        # تحليل أوقات النماذج
        for model, times in model_times.items():
            if times:
                timing_analysis["model_speed_patterns"][model] = {
                    "average_time": statistics.mean(times),
                    "median_time": statistics.median(times),
                    "time_consistency": 1 - (statistics.stdev(times) / statistics.mean(times)) if len(times) > 1 and statistics.mean(times) > 0 else 1
                }
        
        # تحليل العلاقة بين الوقت والنجاح
        if time_success_pairs:
            times, successes = zip(*time_success_pairs)
            correlation = np.corrcoef(times, successes)[0, 1] if len(times) > 1 else 0
            timing_analysis["time_vs_success_correlation"] = {
                "correlation_coefficient": correlation,
                "interpretation": self.interpret_correlation(correlation)
            }
        
        return timing_analysis
    
    def analyze_task_type_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل أنماط أنواع المهام"""
        task_type_analysis = defaultdict(lambda: {
            "count": 0,
            "average_success_rate": 0,
            "best_models": [],
            "common_patterns": []
        })
        
        for exp in experiences:
            task_type = exp.get("task_data", {}).get("task_type", "unknown")
            success_score = exp.get("success_score", 0)
            
            task_type_analysis[task_type]["count"] += 1
            
            # تجميع معدلات النجاح
            current_avg = task_type_analysis[task_type]["average_success_rate"]
            current_count = task_type_analysis[task_type]["count"]
            task_type_analysis[task_type]["average_success_rate"] = (
                (current_avg * (current_count - 1) + success_score) / current_count
            )
            
            # تحديد أفضل النماذج
            successful_models = [
                r["model"] for r in exp.get("results", {}).get("execution_log", [])
                if r.get("success", False)
            ]
            task_type_analysis[task_type]["best_models"].extend(successful_models)
        
        # تنظيف وتلخيص البيانات
        for task_type, data in task_type_analysis.items():
            if data["best_models"]:
                model_counter = Counter(data["best_models"])
                data["best_models"] = [model for model, count in model_counter.most_common(3)]
        
        return dict(task_type_analysis)
    
    def analyze_model_synergy_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل أنماط التآزر بين النماذج"""
        synergy_patterns = {}
        model_pairs = defaultdict(list)
        
        for exp in experiences:
            models_in_task = []
            for result in exp.get("results", {}).get("execution_log", []):
                if result.get("success", False):
                    models_in_task.append(result["model"])
            
            # تحليل الأزواج
            for i in range(len(models_in_task)):
                for j in range(i + 1, len(models_in_task)):
                    pair = tuple(sorted([models_in_task[i], models_in_task[j]]))
                    model_pairs[pair].append(exp.get("success_score", 0))
        
        # حساب درجات التآزر
        for pair, success_scores in model_pairs.items():
            if len(success_scores) >= 2:  # نحتاج عينة كافية
                synergy_patterns[f"{pair[0]} + {pair[1]}"] = {
                    "collaboration_count": len(success_scores),
                    "average_success_rate": statistics.mean(success_scores),
                    "synergy_consistency": 1 - statistics.stdev(success_scores) if len(success_scores) > 1 else 1,
                    "synergy_strength": "قوي" if statistics.mean(success_scores) > 0.8 else "متوسط" if statistics.mean(success_scores) > 0.6 else "ضعيف"
                }
        
        return synergy_patterns
    
    def analyze_workflow_sequence_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل أنماط تسلسل سير العمل"""
        sequence_patterns = defaultdict(list)
        
        for exp in experiences:
            execution_log = exp.get("results", {}).get("execution_log", [])
            if len(execution_log) > 1:
                sequence = [result["model"] for result in execution_log if result.get("success", False)]
                if len(sequence) > 1:
                    sequence_key = " -> ".join(sequence)
                    sequence_patterns[sequence_key].append(exp.get("success_score", 0))
        
        # تحليل فعالية التسلسلات
        sequence_analysis = {}
        for sequence, success_scores in sequence_patterns.items():
            if len(success_scores) >= 2:
                sequence_analysis[sequence] = {
                    "usage_count": len(success_scores),
                    "average_success_rate": statistics.mean(success_scores),
                    "consistency": 1 - statistics.stdev(success_scores) if len(success_scores) > 1 else 1
                }
        
        return sequence_analysis
    
    def extract_success_factors(self, experiences: List[Dict]) -> List[str]:
        """استخراج عوامل النجاح"""
        success_factors = []
        
        # تحليل الأنماط المشتركة في التجارب الناجحة
        high_success_experiences = [exp for exp in experiences if exp.get("success_score", 0) > 0.8]
        
        if len(high_success_experiences) >= 3:
            # عوامل التشكيل
            common_models = self.find_common_models(high_success_experiences)
            if common_models:
                success_factors.append(f"النماذج الأكثر نجاحاً: {', '.join(common_models)}")
            
            # عوامل التوقيت
            avg_times = [
                sum(r.get("execution_time", 0) for r in exp.get("results", {}).get("execution_log", []))
                for exp in high_success_experiences
            ]
            if avg_times:
                optimal_time = statistics.median(avg_times)
                success_factors.append(f"الوقت الأمثل للتنفيذ: {optimal_time:.1f} ثانية")
            
            # عوامل أخرى
            success_factors.append("التنسيق الجيد بين النماذج يزيد من النجاح")
            success_factors.append("التخصص في نوع المهمة يحسن النتائج")
        
        return success_factors
    
    def find_common_models(self, experiences: List[Dict]) -> List[str]:
        """العثور على النماذج المشتركة في التجارب الناجحة"""
        model_counts = defaultdict(int)
        
        for exp in experiences:
            models_used = set()
            for result in exp.get("results", {}).get("execution_log", []):
                if result.get("success", False):
                    models_used.add(result["model"])
            
            for model in models_used:
                model_counts[model] += 1
        
        # النماذج التي ظهرت في أكثر من نصف التجارب
        threshold = len(experiences) * 0.5
        common_models = [model for model, count in model_counts.items() if count >= threshold]
        
        return common_models
    
    def interpret_correlation(self, correlation: float) -> str:
        """تفسير معامل الارتباط"""
        if abs(correlation) < 0.1:
            return "لا يوجد ارتباط"
        elif abs(correlation) < 0.3:
            return "ارتباط ضعيف"
        elif abs(correlation) < 0.7:
            return "ارتباط متوسط"
        else:
            return "ارتباط قوي"
    
    def save_success_patterns(self, patterns: Dict):
        """حفظ أنماط النجاح"""
        file_path = self.insights_path / "successful_patterns.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(patterns, f, ensure_ascii=False, indent=2)
        print(f"💾 تم حفظ أنماط النجاح في: {file_path}")
    
    def save_failure_analysis(self, analysis: Dict):
        """حفظ تحليل الفشل"""
        file_path = self.insights_path / "failure_analysis.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        print(f"💾 تم حفظ تحليل الفشل في: {file_path}")
    
    def save_collaboration_synergies(self, synergies: Dict):
        """حفظ تحليل التآزر"""
        file_path = self.insights_path / "collaboration_synergies.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(synergies, f, ensure_ascii=False, indent=2)
        print(f"💾 تم حفظ تحليل التآزر في: {file_path}")

def main():
    """دالة اختبار"""
    print("🔍 اختبار محلل أنماط فريق أنوبيس")
    print("=" * 50)
    
    # يحتاج إلى memory_manager للعمل
    print("⚠️ هذا المحلل يحتاج إلى memory_manager للعمل")
    print("استخدم: analyzer = AnubisPatternAnalyzer(memory_manager)")

if __name__ == "__main__":
    main()
