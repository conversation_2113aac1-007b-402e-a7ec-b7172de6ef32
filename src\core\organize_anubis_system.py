#!/usr/bin/env python3
"""
🏺 سكريبت إعادة التنظيم الشامل لنظام أنوبيس
Comprehensive Organization Script for Anubis System

يقوم بإعادة تنظيم جميع الملفات والمجلدات وإصلاح المسارات
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any


class AnubisOrganizer:
    """منظم نظام أنوبيس الشامل"""
    
    def __init__(self):
        self.root_path = Path(".")
        self.organization_log = []
        self.errors = []
        
        # الهيكل المستهدف الجديد
        self.target_structure = {
            "anubis/": {
                "core/": ["النواة الأساسية للنظام"],
                "agents/": ["الوكلاء الذكيين"],
                "api/": ["واجهة برمجة التطبيقات"],
                "database/": ["قاعدة البيانات وإدارتها"],
                "plugins/": ["نظام الإضافات"],
                "configs/": ["ملفات التكوين"],
                "tests/": ["الاختبارات الشاملة"],
                "scripts/": ["النصوص المساعدة"],
                "docs/": ["التوثيق الكامل"],
                "workspace/": ["مساحة العمل"],
                "templates/": ["قوالب المشاريع"],
                "examples/": ["أمثلة الاستخدام"]
            },
            "tools/": {
                "vscode-optimizer/": ["أدوات تحسين VS Code"],
                "emergency/": ["أدوات الطوارئ"],
                "monitoring/": ["أدوات المراقبة"]
            },
            "archive/": {
                "old_versions/": ["الإصدارات القديمة"],
                "backups/": ["النسخ الاحتياطية"],
                "deprecated/": ["الملفات المهجورة"]
            }
        }
        
        # قواعد التنظيم
        self.organization_rules = {
            # الملفات الأساسية
            "core_files": {
                "patterns": ["main.py", "requirements*.txt", "setup.py", "pyproject.toml"],
                "target": "anubis/"
            },
            
            # ملفات التوثيق الرئيسية
            "main_docs": {
                "patterns": ["README.md", "INDEX.md", "*README.md"],
                "target": "anubis/",
                "exclude": ["docs/README.md", "tests/README.md"]
            },
            
            # النواة الأساسية
            "core_system": {
                "source": "core/",
                "target": "anubis/core/"
            },
            
            # الوكلاء
            "agents": {
                "source": "agents/",
                "target": "anubis/agents/"
            },
            
            # واجهة برمجة التطبيقات
            "api": {
                "source": "api/",
                "target": "anubis/api/"
            },
            
            # قاعدة البيانات
            "database": {
                "source": "database/",
                "target": "anubis/database/"
            },
            
            # الإضافات
            "plugins": {
                "source": "plugins/",
                "target": "anubis/plugins/"
            },
            
            # التكوين
            "configs": {
                "sources": ["configs/", "config-files/"],
                "target": "anubis/configs/"
            },
            
            # الاختبارات
            "tests": {
                "source": "tests/",
                "target": "anubis/tests/"
            },
            
            # النصوص
            "scripts": {
                "sources": ["scripts/", "project-files/"],
                "target": "anubis/scripts/"
            },
            
            # التوثيق
            "documentation": {
                "sources": ["docs/", "documentation/"],
                "target": "anubis/docs/"
            },
            
            # مساحة العمل
            "workspace": {
                "sources": ["workspace/", "logs/", "reports/"],
                "target": "anubis/workspace/"
            },
            
            # القوالب والأمثلة
            "templates": {
                "source": "templates/",
                "target": "anubis/templates/"
            },
            
            "examples": {
                "source": "examples/",
                "target": "anubis/examples/"
            },
            
            # أدوات VS Code
            "vscode_tools": {
                "sources": ["Universal-AI-Assistant-Suite/", "dashboard-files/"],
                "target": "tools/vscode-optimizer/"
            },
            
            # أدوات الطوارئ
            "emergency_tools": {
                "source": "emergency-tools/",
                "target": "tools/emergency/"
            },
            
            # أدوات المراقبة
            "monitoring": {
                "source": "tools/",
                "target": "tools/monitoring/"
            },
            
            # الأرشيف
            "archive": {
                "sources": ["archive/", "backup/", "temp/", "Universal-AI-Assistants/"],
                "target": "archive/"
            }
        }
    
    def log_action(self, action: str, details: str = ""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            "timestamp": timestamp,
            "action": action,
            "details": details
        }
        self.organization_log.append(log_entry)
        print(f"🏺 [{timestamp}] {action}: {details}")
    
    def create_target_structure(self):
        """إنشاء الهيكل المستهدف"""
        self.log_action("إنشاء الهيكل المستهدف", "بدء إنشاء المجلدات الجديدة")
        
        for main_folder, subfolders in self.target_structure.items():
            main_path = Path(main_folder)
            main_path.mkdir(exist_ok=True)
            
            for subfolder, description in subfolders.items():
                subfolder_path = main_path / subfolder
                subfolder_path.mkdir(exist_ok=True)
                
                # إنشاء ملف README لكل مجلد
                readme_path = subfolder_path / "README.md"
                if not readme_path.exists():
                    readme_content = f"# {subfolder.rstrip('/')}\n\n{description[0]}\n"
                    readme_path.write_text(readme_content, encoding='utf-8')
        
        self.log_action("إنشاء الهيكل", "تم إنشاء جميع المجلدات المستهدفة")
    
    def move_files_safely(self, source: Path, target: Path):
        """نقل الملفات بأمان مع تجنب التضارب"""
        if not source.exists():
            return
        
        target.parent.mkdir(parents=True, exist_ok=True)
        
        if target.exists():
            # إنشاء نسخة احتياطية
            backup_name = f"{target.name}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_path = target.parent / backup_name
            shutil.move(str(target), str(backup_path))
            self.log_action("نسخ احتياطي", f"{target} -> {backup_path}")
        
        shutil.move(str(source), str(target))
        self.log_action("نقل ملف", f"{source} -> {target}")
    
    def organize_by_rules(self):
        """تنظيم الملفات حسب القواعد المحددة"""
        self.log_action("بدء التنظيم", "تطبيق قواعد التنظيم")
        
        for rule_name, rule_config in self.organization_rules.items():
            try:
                self.log_action(f"تطبيق قاعدة", rule_name)
                
                if "source" in rule_config:
                    # نقل مجلد واحد
                    source_path = Path(rule_config["source"])
                    target_path = Path(rule_config["target"])
                    
                    if source_path.exists():
                        self.move_files_safely(source_path, target_path)
                
                elif "sources" in rule_config:
                    # نقل عدة مجلدات إلى هدف واحد
                    target_path = Path(rule_config["target"])
                    target_path.mkdir(parents=True, exist_ok=True)
                    
                    for source in rule_config["sources"]:
                        source_path = Path(source)
                        if source_path.exists():
                            # نقل محتويات المجلد
                            for item in source_path.iterdir():
                                item_target = target_path / item.name
                                self.move_files_safely(item, item_target)
                            
                            # حذف المجلد الفارغ
                            if source_path.exists() and not any(source_path.iterdir()):
                                source_path.rmdir()
                
                elif "patterns" in rule_config:
                    # نقل ملفات حسب الأنماط
                    target_path = Path(rule_config["target"])
                    target_path.mkdir(parents=True, exist_ok=True)
                    
                    for pattern in rule_config["patterns"]:
                        for file_path in self.root_path.glob(pattern):
                            if file_path.is_file():
                                # تحقق من الاستثناءات
                                exclude = rule_config.get("exclude", [])
                                if not any(str(file_path).startswith(ex) for ex in exclude):
                                    target_file = target_path / file_path.name
                                    self.move_files_safely(file_path, target_file)
                
            except Exception as e:
                error_msg = f"خطأ في تطبيق قاعدة {rule_name}: {e}"
                self.errors.append(error_msg)
                self.log_action("خطأ", error_msg)
    
    def clean_empty_directories(self):
        """تنظيف المجلدات الفارغة"""
        self.log_action("تنظيف المجلدات", "حذف المجلدات الفارغة")
        
        def remove_empty_dirs(path: Path):
            if not path.is_dir():
                return
            
            # تنظيف المجلدات الفرعية أولاً
            for item in path.iterdir():
                if item.is_dir():
                    remove_empty_dirs(item)
            
            # حذف المجلد إذا كان فارغاً
            try:
                if path.exists() and not any(path.iterdir()):
                    path.rmdir()
                    self.log_action("حذف مجلد فارغ", str(path))
            except OSError:
                pass
        
        # تنظيف المجلدات في الجذر
        for item in self.root_path.iterdir():
            if item.is_dir() and item.name not in ['.git', '.venv', '__pycache__', 'anubis', 'tools', 'archive']:
                remove_empty_dirs(item)
    
    def update_import_paths(self):
        """تحديث مسارات الاستيراد في الملفات"""
        self.log_action("تحديث المسارات", "إصلاح مسارات الاستيراد")
        
        # خريطة تحديث المسارات
        path_mappings = {
            "from core.": "from anubis.core.",
            "from agents.": "from anubis.agents.",
            "from database.": "from anubis.database.",
            "from plugins.": "from anubis.plugins.",
            "import core.": "import anubis.core.",
            "import agents.": "import anubis.agents.",
            "import database.": "import anubis.database.",
            "import plugins.": "import anubis.plugins.",
        }
        
        # البحث في جميع ملفات Python
        for py_file in Path("anubis").rglob("*.py"):
            try:
                content = py_file.read_text(encoding='utf-8')
                original_content = content
                
                # تطبيق التحديثات
                for old_path, new_path in path_mappings.items():
                    content = content.replace(old_path, new_path)
                
                # حفظ الملف إذا تم تغييره
                if content != original_content:
                    py_file.write_text(content, encoding='utf-8')
                    self.log_action("تحديث مسارات", str(py_file))
                    
            except Exception as e:
                error_msg = f"خطأ في تحديث {py_file}: {e}"
                self.errors.append(error_msg)
    
    def create_main_structure_files(self):
        """إنشاء الملفات الأساسية للهيكل الجديد"""
        self.log_action("إنشاء ملفات أساسية", "إنشاء ملفات الهيكل الجديد")
        
        # إنشاء __init__.py للحزم
        init_files = [
            "anubis/__init__.py",
            "anubis/core/__init__.py",
            "anubis/agents/__init__.py",
            "anubis/api/__init__.py",
            "anubis/database/__init__.py",
            "anubis/plugins/__init__.py",
            "anubis/tests/__init__.py",
            "anubis/scripts/__init__.py"
        ]
        
        for init_file in init_files:
            init_path = Path(init_file)
            if not init_path.exists():
                init_path.write_text('"""نظام أنوبيس للذكاء الاصطناعي"""\n', encoding='utf-8')
        
        # إنشاء README.md الرئيسي الجديد
        main_readme = Path("README.md")
        readme_content = """# 🏺 نظام أنوبيس للذكاء الاصطناعي
## Anubis AI Assistant System

نظام متطور للمساعدين الذكيين مع وكلاء متخصصين وقاعدة بيانات متكاملة.

## الهيكل الجديد

```
anubis/                 # النظام الأساسي
├── core/              # النواة الأساسية
├── agents/            # الوكلاء الذكيين
├── api/               # واجهة برمجة التطبيقات
├── database/          # قاعدة البيانات
├── plugins/           # نظام الإضافات
├── configs/           # ملفات التكوين
├── tests/             # الاختبارات
├── scripts/           # النصوص المساعدة
├── docs/              # التوثيق
├── workspace/         # مساحة العمل
├── templates/         # قوالب المشاريع
└── examples/          # أمثلة الاستخدام

tools/                 # الأدوات المساعدة
├── vscode-optimizer/  # أدوات تحسين VS Code
├── emergency/         # أدوات الطوارئ
└── monitoring/        # أدوات المراقبة

archive/               # الأرشيف
├── old_versions/      # الإصدارات القديمة
├── backups/           # النسخ الاحتياطية
└── deprecated/        # الملفات المهجورة
```

## التشغيل السريع

```bash
# تشغيل النظام
python anubis/main.py

# تشغيل الاختبارات
python anubis/tests/run_all_tests.py

# تشغيل واجهة المساعدة
python anubis/scripts/ask_anubis.py
```

## المزيد من المعلومات

راجع مجلد `anubis/docs/` للحصول على التوثيق الكامل.
"""
        main_readme.write_text(readme_content, encoding='utf-8')
    
    def generate_organization_report(self):
        """إنشاء تقرير إعادة التنظيم"""
        report_data = {
            "organization_date": datetime.now().isoformat(),
            "total_actions": len(self.organization_log),
            "errors_count": len(self.errors),
            "actions": self.organization_log,
            "errors": self.errors,
            "new_structure": self.target_structure
        }
        
        # حفظ التقرير JSON
        report_path = Path("anubis/workspace/reports/organization_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير نصي
        text_report_path = Path("anubis/workspace/reports/organization_report.md")
        report_content = f"""# 🏺 تقرير إعادة التنظيم الشامل لنظام أنوبيس

**تاريخ التنظيم:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## الإحصائيات
- **إجمالي العمليات:** {len(self.organization_log)}
- **الأخطاء:** {len(self.errors)}
- **معدل النجاح:** {((len(self.organization_log) - len(self.errors)) / len(self.organization_log) * 100):.1f}%

## العمليات المنفذة
"""
        
        for action in self.organization_log[-10:]:  # آخر 10 عمليات
            report_content += f"- **{action['timestamp']}:** {action['action']} - {action['details']}\n"
        
        if self.errors:
            report_content += "\n## الأخطاء\n"
            for error in self.errors:
                report_content += f"- ❌ {error}\n"
        
        report_content += "\n## الهيكل الجديد تم إنشاؤه بنجاح! 🎉"
        
        text_report_path.write_text(report_content, encoding='utf-8')
        
        self.log_action("إنشاء التقرير", f"تم حفظ التقرير في {report_path}")
    
    def run_organization(self):
        """تشغيل عملية إعادة التنظيم الكاملة"""
        print("🏺 بدء إعادة التنظيم الشامل لنظام أنوبيس...")
        
        try:
            # الخطوة 1: إنشاء الهيكل المستهدف
            self.create_target_structure()
            
            # الخطوة 2: تنظيم الملفات حسب القواعد
            self.organize_by_rules()
            
            # الخطوة 3: تنظيف المجلدات الفارغة
            self.clean_empty_directories()
            
            # الخطوة 4: تحديث مسارات الاستيراد
            self.update_import_paths()
            
            # الخطوة 5: إنشاء الملفات الأساسية
            self.create_main_structure_files()
            
            # الخطوة 6: إنشاء التقرير
            self.generate_organization_report()
            
            print(f"\n🎉 تم إكمال إعادة التنظيم بنجاح!")
            print(f"📊 إجمالي العمليات: {len(self.organization_log)}")
            print(f"❌ الأخطاء: {len(self.errors)}")
            print(f"📋 التقرير محفوظ في: anubis/workspace/reports/")
            
        except Exception as e:
            print(f"❌ خطأ في إعادة التنظيم: {e}")
            raise


def main():
    """الدالة الرئيسية"""
    organizer = AnubisOrganizer()
    organizer.run_organization()


if __name__ == "__main__":
    main()