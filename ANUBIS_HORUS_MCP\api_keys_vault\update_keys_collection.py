#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 سكريبت تحديث مجموعة مفاتيح API
API Keys Collection Update Script

يقوم بتحديث وإدارة مجموعة مفاتيح API الجديدة بشكل آمن
Updates and manages the new API keys collection securely
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime
import logging

# إعداد المسارات
SCRIPT_DIR = Path(__file__).parent
VAULT_DIR = SCRIPT_DIR / "vault"
COLLECTION_FILE = SCRIPT_DIR / "api_keys_collection.json"

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(SCRIPT_DIR / "keys_update.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class APIKeysUpdater:
    """🔐 محدث مفاتيح API"""
    
    def __init__(self):
        """تهيئة المحدث"""
        self.collection_file = COLLECTION_FILE
        self.vault_dir = VAULT_DIR
        self.vault_dir.mkdir(exist_ok=True)
        
        logger.info("🔐 تم تهيئة محدث مفاتيح API")
    
    def load_collection(self) -> dict:
        """تحميل مجموعة المفاتيح"""
        try:
            if self.collection_file.exists():
                with open(self.collection_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"✅ تم تحميل {data['api_keys_collection']['metadata']['total_keys']} مفتاح")
                return data
            else:
                logger.warning("⚠️ ملف المجموعة غير موجود")
                return {}
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المجموعة: {e}")
            return {}
    
    def validate_keys(self, collection: dict) -> dict:
        """التحقق من صحة المفاتيح"""
        validation_results = {
            "total_keys": 0,
            "valid_keys": 0,
            "invalid_keys": 0,
            "platforms": {},
            "issues": []
        }
        
        try:
            api_keys = collection.get("api_keys_collection", {})
            
            for platform, platform_data in api_keys.items():
                if platform == "metadata" or platform == "security_notes" or platform == "usage_instructions":
                    continue
                
                platform_info = {
                    "name": platform_data.get("platform", "Unknown"),
                    "keys_count": 0,
                    "valid_keys": 0,
                    "issues": []
                }
                
                keys = platform_data.get("keys", [])
                platform_info["keys_count"] = len(keys)
                validation_results["total_keys"] += len(keys)
                
                for key_info in keys:
                    key_name = key_info.get("name", "unnamed")
                    key_value = key_info.get("key", "")
                    
                    # التحقق من وجود المفتاح
                    if not key_value:
                        issue = f"❌ {platform} - {key_name}: مفتاح فارغ"
                        platform_info["issues"].append(issue)
                        validation_results["issues"].append(issue)
                        validation_results["invalid_keys"] += 1
                        continue
                    
                    # التحقق من طول المفتاح
                    if len(key_value) < 10:
                        issue = f"⚠️ {platform} - {key_name}: مفتاح قصير جداً"
                        platform_info["issues"].append(issue)
                        validation_results["issues"].append(issue)
                        validation_results["invalid_keys"] += 1
                        continue
                    
                    # التحقق من تنسيق المفتاح حسب المنصة
                    if self._validate_key_format(platform, key_value):
                        platform_info["valid_keys"] += 1
                        validation_results["valid_keys"] += 1
                    else:
                        issue = f"⚠️ {platform} - {key_name}: تنسيق مفتاح غير صحيح"
                        platform_info["issues"].append(issue)
                        validation_results["issues"].append(issue)
                        validation_results["invalid_keys"] += 1
                
                validation_results["platforms"][platform] = platform_info
            
            logger.info(f"✅ تم التحقق من {validation_results['total_keys']} مفتاح")
            logger.info(f"✅ صحيح: {validation_results['valid_keys']}")
            logger.info(f"❌ غير صحيح: {validation_results['invalid_keys']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من المفاتيح: {e}")
            return validation_results
    
    def _validate_key_format(self, platform: str, key: str) -> bool:
        """التحقق من تنسيق المفتاح حسب المنصة"""
        format_rules = {
            "google_gemini": lambda k: k.startswith("AIza"),
            "openrouter": lambda k: k.startswith("sk-or-v1-"),
            "github": lambda k: k.startswith("github_pat_") or k.startswith("ghp_"),
            "huggingface": lambda k: k.startswith("hf_"),
            "deepseek": lambda k: k.startswith("sk-"),
            "anthropic": lambda k: k.startswith("sk-ant-"),
            "together_ai": lambda k: len(k) > 20,
            "continue_extension": lambda k: k.startswith("con-"),
            "nebius_studio": lambda k: k.startswith("eyJ"),
            "other_platforms": lambda k: len(k) > 10
        }
        
        rule = format_rules.get(platform, lambda k: len(k) > 10)
        return rule(key)
    
    def create_secure_backup(self, collection: dict) -> str:
        """إنشاء نسخة احتياطية آمنة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.vault_dir / f"keys_backup_{timestamp}.json"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(collection, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return ""
    
    def generate_usage_report(self, validation_results: dict) -> str:
        """إنشاء تقرير الاستخدام"""
        report = []
        report.append("🔐 تقرير مفاتيح API")
        report.append("=" * 50)
        report.append(f"📊 إجمالي المفاتيح: {validation_results['total_keys']}")
        report.append(f"✅ مفاتيح صحيحة: {validation_results['valid_keys']}")
        report.append(f"❌ مفاتيح غير صحيحة: {validation_results['invalid_keys']}")
        report.append("")
        
        report.append("📋 تفاصيل المنصات:")
        report.append("-" * 30)
        
        for platform, info in validation_results["platforms"].items():
            report.append(f"🔹 {info['name']} ({platform}):")
            report.append(f"   📊 عدد المفاتيح: {info['keys_count']}")
            report.append(f"   ✅ صحيحة: {info['valid_keys']}")
            report.append(f"   ❌ مشاكل: {len(info['issues'])}")
            
            if info['issues']:
                for issue in info['issues']:
                    report.append(f"      {issue}")
            report.append("")
        
        if validation_results["issues"]:
            report.append("⚠️ المشاكل المكتشفة:")
            report.append("-" * 20)
            for issue in validation_results["issues"]:
                report.append(f"   {issue}")
        
        return "\n".join(report)
    
    def save_report(self, report: str) -> str:
        """حفظ التقرير"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = SCRIPT_DIR / f"keys_validation_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"✅ تم حفظ التقرير: {report_file}")
            return str(report_file)
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ التقرير: {e}")
            return ""
    
    def run_update(self):
        """تشغيل عملية التحديث الكاملة"""
        logger.info("🚀 بدء عملية تحديث مفاتيح API")
        
        # تحميل المجموعة
        collection = self.load_collection()
        if not collection:
            logger.error("❌ فشل في تحميل مجموعة المفاتيح")
            return False
        
        # إنشاء نسخة احتياطية
        backup_file = self.create_secure_backup(collection)
        if not backup_file:
            logger.warning("⚠️ فشل في إنشاء النسخة الاحتياطية")
        
        # التحقق من المفاتيح
        validation_results = self.validate_keys(collection)
        
        # إنشاء التقرير
        report = self.generate_usage_report(validation_results)
        print("\n" + report)
        
        # حفظ التقرير
        report_file = self.save_report(report)
        
        logger.info("✅ تم إكمال عملية التحديث بنجاح")
        return True

def main():
    """الدالة الرئيسية"""
    print("🔐 محدث مفاتيح API - نظام أنوبيس وحورس")
    print("=" * 50)
    
    updater = APIKeysUpdater()
    success = updater.run_update()
    
    if success:
        print("\n✅ تم إكمال العملية بنجاح!")
    else:
        print("\n❌ فشلت العملية!")
        sys.exit(1)

if __name__ == "__main__":
    main()
