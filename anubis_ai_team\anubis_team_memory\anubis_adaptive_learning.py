#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 نظام التعلم التكيفي لفريق الذكاء الاصطناعي - أنوبيس
Anubis AI Team Adaptive Learning System
"""

import json
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import statistics

class AnubisAdaptiveLearning:
    """نظام التعلم التكيفي لفريق أنوبيس"""
    
    def __init__(self, memory_manager, pattern_analyzer):
        self.memory_manager = memory_manager
        self.pattern_analyzer = pattern_analyzer
        self.learning_path = memory_manager.memory_root / "learning"
        
        # إنشاء المجلد إذا لم يكن موجوداً
        self.learning_path.mkdir(exist_ok=True)
        
        # تحميل بيانات التعلم
        self.lessons_learned = self.load_lessons_learned()
        self.adaptation_strategies = self.load_adaptation_strategies()
        self.best_practices = self.load_best_practices()
        self.prompt_templates = self.load_prompt_templates()
        
        # معاملات التعلم
        self.learning_rate = 0.1
        self.adaptation_threshold = 0.7
        self.innovation_factor = 0.2
    
    def learn_from_experience(self, experience_data: Dict) -> Dict:
        """التعلم من التجربة الجديدة"""
        
        print(f"🎓 التعلم من التجربة: {experience_data.get('id', 'غير محدد')}")
        
        learning_results = {
            "experience_id": experience_data.get("id"),
            "learning_timestamp": datetime.now().isoformat(),
            "lessons_extracted": [],
            "adaptations_made": [],
            "new_insights": [],
            "updated_strategies": []
        }
        
        # استخراج الدروس
        lessons = self.extract_lessons_from_experience(experience_data)
        learning_results["lessons_extracted"] = lessons
        
        # تحديث الدروس المستفادة
        self.update_lessons_learned(lessons)
        
        # تكييف الاستراتيجيات
        adaptations = self.adapt_strategies_from_experience(experience_data)
        learning_results["adaptations_made"] = adaptations
        
        # توليد رؤى جديدة
        insights = self.generate_new_insights(experience_data)
        learning_results["new_insights"] = insights
        
        # تحديث أفضل الممارسات
        practices = self.update_best_practices(experience_data)
        learning_results["updated_strategies"] = practices
        
        # تحسين قوالب الـ prompts
        self.optimize_prompt_templates_from_experience(experience_data)
        
        # حفظ نتائج التعلم
        self.save_learning_session(learning_results)
        
        return learning_results
    
    def adapt_strategies(self, performance_feedback: Dict) -> Dict:
        """تكييف الاستراتيجيات بناءً على ردود الفعل"""
        
        print("🔄 تكييف الاستراتيجيات...")
        
        adaptation_results = {
            "adaptation_timestamp": datetime.now().isoformat(),
            "feedback_analyzed": performance_feedback,
            "strategy_changes": [],
            "new_strategies": [],
            "deprecated_strategies": []
        }
        
        # تحليل الأداء
        performance_analysis = self.analyze_performance_feedback(performance_feedback)
        
        # تكييف استراتيجيات التشكيل
        team_adaptations = self.adapt_team_composition_strategies(performance_analysis)
        adaptation_results["strategy_changes"].extend(team_adaptations)
        
        # تكييف استراتيجيات التوقيت
        timing_adaptations = self.adapt_timing_strategies(performance_analysis)
        adaptation_results["strategy_changes"].extend(timing_adaptations)
        
        # تكييف استراتيجيات التعاون
        collaboration_adaptations = self.adapt_collaboration_strategies(performance_analysis)
        adaptation_results["strategy_changes"].extend(collaboration_adaptations)
        
        # إنشاء استراتيجيات جديدة إذا لزم الأمر
        if performance_analysis.get("needs_innovation", False):
            new_strategies = self.create_innovative_strategies(performance_analysis)
            adaptation_results["new_strategies"] = new_strategies
        
        # إزالة الاستراتيجيات غير الفعالة
        deprecated = self.deprecate_ineffective_strategies(performance_analysis)
        adaptation_results["deprecated_strategies"] = deprecated
        
        # حفظ التحديثات
        self.save_adaptation_strategies(self.adaptation_strategies)
        
        return adaptation_results
    
    def evolve_collaboration_patterns(self) -> Dict:
        """تطوير أنماط التعاون"""
        
        print("🤝 تطوير أنماط التعاون...")
        
        # تحليل الأنماط الحالية
        current_patterns = self.memory_manager.collaboration_patterns
        
        # تحليل التجارب الحديثة
        recent_experiences = self.get_recent_experiences(days=30)
        
        evolution_results = {
            "evolution_timestamp": datetime.now().isoformat(),
            "patterns_analyzed": len(current_patterns.get("successful_combinations", {})),
            "new_patterns_discovered": [],
            "improved_patterns": [],
            "pattern_optimizations": []
        }
        
        # اكتشاف أنماط جديدة
        new_patterns = self.discover_new_collaboration_patterns(recent_experiences)
        evolution_results["new_patterns_discovered"] = new_patterns
        
        # تحسين الأنماط الموجودة
        improved_patterns = self.improve_existing_patterns(recent_experiences)
        evolution_results["improved_patterns"] = improved_patterns
        
        # تحسين التسلسلات
        optimizations = self.optimize_collaboration_sequences(recent_experiences)
        evolution_results["pattern_optimizations"] = optimizations
        
        # تحديث أنماط التعاون
        self.update_collaboration_patterns(new_patterns, improved_patterns, optimizations)
        
        return evolution_results
    
    def optimize_prompt_templates(self) -> Dict:
        """تحسين قوالب الـ prompts"""
        
        print("📝 تحسين قوالب الـ prompts...")
        
        optimization_results = {
            "optimization_timestamp": datetime.now().isoformat(),
            "templates_optimized": [],
            "new_templates": [],
            "performance_improvements": {}
        }
        
        # تحليل أداء القوالب الحالية
        template_performance = self.analyze_prompt_template_performance()
        
        # تحسين القوالب الموجودة
        for model, templates in self.prompt_templates.items():
            for task_type, template in templates.items():
                optimized_template = self.optimize_single_template(
                    model, task_type, template, template_performance
                )
                
                if optimized_template != template:
                    self.prompt_templates[model][task_type] = optimized_template
                    optimization_results["templates_optimized"].append({
                        "model": model,
                        "task_type": task_type,
                        "improvement": "محسن للأداء"
                    })
        
        # إنشاء قوالب جديدة للمهام الجديدة
        new_templates = self.create_new_prompt_templates()
        optimization_results["new_templates"] = new_templates
        
        # حفظ القوالب المحدثة
        self.save_prompt_templates(self.prompt_templates)
        
        return optimization_results
    
    def extract_lessons_from_experience(self, experience: Dict) -> List[str]:
        """استخراج الدروس من التجربة"""
        lessons = []
        
        success_score = experience.get("success_score", 0)
        execution_log = experience.get("results", {}).get("execution_log", [])
        
        # دروس من النجاح
        if success_score > 0.8:
            successful_models = [r["model"] for r in execution_log if r.get("success", False)]
            if successful_models:
                lessons.append(f"النماذج {', '.join(set(successful_models))} تعمل بشكل ممتاز في هذا النوع من المهام")
            
            # تحليل التوقيت
            total_time = sum(r.get("execution_time", 0) for r in execution_log)
            if total_time < 30:
                lessons.append("التنفيذ السريع يؤدي إلى نتائج جيدة")
        
        # دروس من التحديات
        elif success_score < 0.5:
            failed_models = [r["model"] for r in execution_log if not r.get("success", False)]
            if failed_models:
                lessons.append(f"النماذج {', '.join(set(failed_models))} تحتاج تحسين في هذا النوع من المهام")
        
        # دروس من التعاون
        if len(execution_log) > 1:
            lessons.append("التعاون بين النماذج يحسن النتائج")
        
        return lessons
    
    def adapt_strategies_from_experience(self, experience: Dict) -> List[str]:
        """تكييف الاستراتيجيات من التجربة"""
        adaptations = []
        
        task_type = experience.get("task_data", {}).get("task_type", "unknown")
        success_score = experience.get("success_score", 0)
        
        # تكييف بناءً على النجاح
        if success_score > 0.8:
            successful_models = [
                r["model"] for r in experience.get("results", {}).get("execution_log", [])
                if r.get("success", False)
            ]
            
            if successful_models:
                # تحديث الاستراتيجيات الناجحة
                strategy_key = f"{task_type}_successful_models"
                if strategy_key not in self.adaptation_strategies:
                    self.adaptation_strategies[strategy_key] = []
                
                for model in successful_models:
                    if model not in self.adaptation_strategies[strategy_key]:
                        self.adaptation_strategies[strategy_key].append(model)
                        adaptations.append(f"إضافة {model} كنموذج موصى به لمهام {task_type}")
        
        return adaptations
    
    def generate_new_insights(self, experience: Dict) -> List[str]:
        """توليد رؤى جديدة"""
        insights = []
        
        # تحليل الأنماط الجديدة
        execution_log = experience.get("results", {}).get("execution_log", [])
        if len(execution_log) > 1:
            sequence = [r["model"] for r in execution_log if r.get("success", False)]
            if len(sequence) > 1:
                insights.append(f"تسلسل جديد فعال: {' -> '.join(sequence)}")
        
        # رؤى حول التوقيت
        times = [r.get("execution_time", 0) for r in execution_log]
        if times:
            avg_time = statistics.mean(times)
            if avg_time < 10:
                insights.append("المهام السريعة تميل للنجاح أكثر")
            elif avg_time > 60:
                insights.append("المهام المعقدة تحتاج وقت أطول لكن نتائج أفضل")
        
        return insights
    
    def update_best_practices(self, experience: Dict) -> List[str]:
        """تحديث أفضل الممارسات"""
        practices = []
        
        success_score = experience.get("success_score", 0)
        
        if success_score > 0.9:
            # استخراج الممارسات الممتازة
            task_type = experience.get("task_data", {}).get("task_type", "unknown")
            
            practice = {
                "task_type": task_type,
                "success_score": success_score,
                "best_practice": "استخدام التشكيل والتسلسل المطبق في هذه التجربة",
                "timestamp": datetime.now().isoformat()
            }
            
            if task_type not in self.best_practices:
                self.best_practices[task_type] = []
            
            self.best_practices[task_type].append(practice)
            practices.append(f"ممارسة جديدة ممتازة لمهام {task_type}")
        
        return practices
    
    def optimize_prompt_templates_from_experience(self, experience: Dict):
        """تحسين قوالب الـ prompts من التجربة"""
        
        # تحليل النتائج لكل نموذج
        for result in experience.get("results", {}).get("execution_log", []):
            model = result.get("model")
            success = result.get("success", False)
            response_quality = len(result.get("response", ""))
            
            if model in self.prompt_templates and success and response_quality > 100:
                # تحسين القالب بناءً على النجاح
                task_type = experience.get("task_data", {}).get("task_type", "general")
                
                if task_type in self.prompt_templates[model]:
                    # إضافة عناصر تحسين للقالب
                    current_template = self.prompt_templates[model][task_type]
                    if "🎯 نجح هذا النموذج مؤخراً في مهام مشابهة" not in current_template:
                        self.prompt_templates[model][task_type] += "\n\n🎯 نجح هذا النموذج مؤخراً في مهام مشابهة - استفد من هذه الخبرة"
    
    def get_recent_experiences(self, days: int = 30) -> List[Dict]:
        """الحصول على التجارب الحديثة"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_experiences = []
        
        for task_dir in self.memory_manager.experiences_path.iterdir():
            if task_dir.is_dir():
                for exp_file in task_dir.glob("experience_*.json"):
                    try:
                        with open(exp_file, 'r', encoding='utf-8') as f:
                            experience = json.load(f)
                        
                        exp_date = datetime.fromisoformat(experience.get("timestamp", ""))
                        if exp_date >= cutoff_date:
                            recent_experiences.append(experience)
                            
                    except Exception as e:
                        continue
        
        return recent_experiences
    
    def discover_new_collaboration_patterns(self, experiences: List[Dict]) -> List[Dict]:
        """اكتشاف أنماط تعاون جديدة"""
        new_patterns = []
        
        # تحليل التشكيلات الجديدة الناجحة
        successful_combinations = defaultdict(int)
        
        for exp in experiences:
            if exp.get("success_score", 0) > 0.8:
                models = [r["model"] for r in exp.get("results", {}).get("execution_log", []) if r.get("success", False)]
                if len(models) > 1:
                    combination = tuple(sorted(set(models)))
                    successful_combinations[combination] += 1
        
        # تحديد الأنماط الجديدة
        current_patterns = self.memory_manager.collaboration_patterns.get("successful_combinations", {})
        
        for combination, count in successful_combinations.items():
            if count >= 2:  # ظهر مرتين على الأقل
                combination_str = ",".join(combination)
                task_types = set()
                
                # تحديد أنواع المهام لهذا التشكيل
                for exp in experiences:
                    exp_models = [r["model"] for r in exp.get("results", {}).get("execution_log", []) if r.get("success", False)]
                    if set(exp_models) == set(combination):
                        task_types.add(exp.get("task_data", {}).get("task_type", "unknown"))
                
                new_pattern = {
                    "combination": combination_str,
                    "success_count": count,
                    "applicable_task_types": list(task_types),
                    "discovered_at": datetime.now().isoformat()
                }
                
                new_patterns.append(new_pattern)
        
        return new_patterns
    
    def load_lessons_learned(self) -> Dict:
        """تحميل الدروس المستفادة"""
        file_path = self.learning_path / "lessons_learned.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"lessons": [], "created_at": datetime.now().isoformat()}
    
    def load_adaptation_strategies(self) -> Dict:
        """تحميل استراتيجيات التكيف"""
        file_path = self.learning_path / "adaptation_strategies.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"strategies": {}, "created_at": datetime.now().isoformat()}
    
    def load_best_practices(self) -> Dict:
        """تحميل أفضل الممارسات"""
        file_path = self.learning_path / "best_practices.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"practices": {}, "created_at": datetime.now().isoformat()}
    
    def load_prompt_templates(self) -> Dict:
        """تحميل قوالب الـ prompts"""
        file_path = self.learning_path / "optimized_prompt_templates.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # قوالب افتراضية
        return {
            "phi3:mini": {
                "analysis": "أنت محلل سريع ومباشر. قم بتحليل المهمة بسرعة وكفاءة.",
                "review": "راجع النتائج بسرعة وحدد النقاط الرئيسية."
            },
            "mistral:7b": {
                "development": "أنت مطور خبير. اكتب كود عالي الجودة وحل المشاكل التقنية.",
                "technical": "حلل المشكلة التقنية وقدم حلول عملية."
            },
            "llama3:8b": {
                "planning": "أنت مستشار استراتيجي. ضع خطة شاملة ومدروسة.",
                "strategy": "فكر استراتيجياً وقدم توجيهات حكيمة."
            }
        }
    
    def save_learning_session(self, learning_results: Dict):
        """حفظ جلسة التعلم"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = self.learning_path / f"learning_session_{timestamp}.json"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(learning_results, f, ensure_ascii=False, indent=2)
        
        print(f"🎓 تم حفظ جلسة التعلم: {file_path}")
    
    def save_adaptation_strategies(self, strategies: Dict):
        """حفظ استراتيجيات التكيف"""
        file_path = self.learning_path / "adaptation_strategies.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(strategies, f, ensure_ascii=False, indent=2)
    
    def save_prompt_templates(self, templates: Dict):
        """حفظ قوالب الـ prompts"""
        file_path = self.learning_path / "optimized_prompt_templates.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(templates, f, ensure_ascii=False, indent=2)

def main():
    """دالة اختبار"""
    print("🎓 اختبار نظام التعلم التكيفي لفريق أنوبيس")
    print("=" * 50)
    
    print("⚠️ هذا النظام يحتاج إلى memory_manager و pattern_analyzer للعمل")
    print("استخدم: learning = AnubisAdaptiveLearning(memory_manager, pattern_analyzer)")

if __name__ == "__main__":
    main()
