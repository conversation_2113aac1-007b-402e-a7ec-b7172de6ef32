# 📦 ملف المتطلبات الموحد لأنظمة أنوبيس وحورس + MCP
# Unified Requirements File for Anubis and Horus Systems + MCP
#
# تم تحديثه في: 2025-07-23
# إجمالي المكتبات: 95 مكتبة (79 أساسية + 16 إضافية للـ MCP)
# متوافق مع: Python 3.8+ (مفضل 3.11+)
# الحجم التقديري: ~3.5 GB بعد التثبيت
# يشمل: بروتوكول MCP المتكامل مع فريق حورس

# ==========================================
# 🌟 مكتبات MCP الأساسية (جديدة)
# Core MCP Libraries (New)
# ==========================================
mcp>=1.0.0
websockets>=12.0
asyncio-mqtt>=0.16.0
grpcio>=1.60.0
protobuf>=4.25.0
socketio>=5.10.0

# ==========================================
# 🔧 مكتبات النظام الأساسية
# Core System Libraries
# ==========================================
certifi==2025.7.14
charset-normalizer==3.4.2
packaging==25.0
pip==25.1.1
urllib3==2.5.0
idna==3.10

# ==========================================
# 🌐 مكتبات الويب والشبكة
# Web and Network Libraries
# ==========================================
fastapi==0.116.1
uvicorn[standard]==0.35.0
starlette==0.47.1
httpx==0.28.1
httpcore==1.0.9
requests==2.32.4
h11==0.16.0
python-multipart==0.0.20
aiohttp>=3.9.0
paramiko>=3.4.0

# ==========================================
# 🗄️ قواعد البيانات
# Database Libraries
# ==========================================
mysql-connector-python==9.3.0
psycopg2-binary>=2.9.0
sqlalchemy==2.0.41
aiosqlite>=0.19.0
redis>=4.5.0
chromadb>=0.4.0
motor>=3.3.0
asyncpg>=0.29.0
aioredis>=2.0.0

# ==========================================
# 🤖 الذكاء الاصطناعي والتعلم الآلي
# AI and Machine Learning
# ==========================================
openai>=1.0.0
anthropic>=0.25.0
google-generativeai>=0.5.0
langchain>=0.0.200
transformers>=4.30.0
sentence-transformers>=2.2.0
tiktoken>=0.5.0
torch>=2.0.0
scikit-learn>=1.3.0
numpy>=1.24.0
scipy>=1.10.0
faiss-cpu>=1.7.0

# ==========================================
# 📊 تحليل البيانات والتصور
# Data Analysis and Visualization
# ==========================================
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
tabulate>=0.9.0
tqdm>=4.64.0

# ==========================================
# 🔒 الأمان والتشفير
# Security and Cryptography
# ==========================================
cryptography>=41.0.0
bcrypt>=4.0.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
keyring>=24.3.0

# ==========================================
# ☁️ تكاملات سحابية (جديدة)
# Cloud Integrations (New)
# ==========================================
google-cloud-storage>=2.10.0
boto3>=1.34.0
azure-storage-blob>=12.19.0

# ==========================================
# 🔗 تكاملات التطوير (جديدة)
# Development Integrations (New)
# ==========================================
pygithub>=2.1.0
python-gitlab>=4.4.0
kubernetes>=28.1.0

# ==========================================
# ⚙️ أدوات النظام والمراقبة
# System Tools and Monitoring
# ==========================================
psutil==7.0.0
prometheus-client>=0.16.0
docker>=6.0.0
aiofiles==24.1.0
pathlib2>=2.3.7

# ==========================================
# 🎨 واجهات المستخدم
# User Interfaces
# ==========================================
streamlit>=1.28.0
jinja2==3.1.6
markupsafe==3.0.2
click==8.2.1
rich>=13.0.0
colorama==0.4.6
colorlog>=6.7.0

# ==========================================
# 🧪 التطوير والاختبار
# Development and Testing
# ==========================================
pytest==8.4.1
pytest-cov>=4.0.0
iniconfig==2.1.0
pluggy==1.6.0
black>=22.0.0
flake8>=5.0.0
pylint>=2.17.0
pathspec>=0.10.0

# ==========================================
# 📓 بيئة التطوير
# Development Environment
# ==========================================
jupyter>=1.0.0
jupyterlab>=4.0.0
pygments==2.19.2

# ==========================================
# 🔧 مكتبات مساعدة
# Utility Libraries
# ==========================================
python-dotenv==1.1.1
python-dateutil>=2.8.0
pyyaml==6.0.2
chardet>=5.0.0

# ==========================================
# 🏷️ أدوات النوع والتحقق
# Type Checking and Validation
# ==========================================
pydantic==2.11.7
pydantic_core==2.33.2
annotated-types==0.7.0
typing-extensions==4.14.1
typing-inspection==0.4.1

# ==========================================
# ⚡ أدوات غير متزامنة
# Asynchronous Tools
# ==========================================
anyio==4.9.0
sniffio==1.3.1
greenlet==3.2.3

# ==========================================
# 🪟 مكتبات Windows (إذا كان النظام Windows)
# Windows Libraries (if on Windows)
# ==========================================
pywin32==311; sys_platform == "win32"

# ==========================================
# 📦 ملاحظات التثبيت
# Installation Notes
# ==========================================
#
# للتثبيت الأساسي:
# pip install -r requirements_anubis_horus_unified.txt
#
# للتثبيت مع GPU support:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# pip install -r requirements_anubis_horus_unified.txt --no-deps torch
#
# للتثبيت في بيئة افتراضية:
# python -m venv anubis_horus_env
# anubis_horus_env\Scripts\activate  # Windows
# source anubis_horus_env/bin/activate  # Linux/Mac
# pip install -r requirements_anubis_horus_unified.txt
#
# للتحقق من التثبيت:
# python -c "import fastapi, torch, transformers, mcp, websockets; print('✅ جميع المكتبات مثبتة بنجاح')"
#
# لتشغيل خادم MCP:
# python ANUBIS_HORUS_MCP/core/mcp_server.py
#
# لإعداد مفاتيح API:
# python ANUBIS_HORUS_MCP/api_keys_vault/setup_keys.py
#
# ==========================================
# 🌟 تحديثات الإصدار الجديد
# New Version Updates
# ==========================================
#
# تم إضافة 16 مكتبة جديدة للـ MCP:
# Added 16 new libraries for MCP:
#
# 🌟 مكتبات MCP الأساسية (6):
# - mcp>=1.0.0 (بروتوكول MCP الأساسي)
# - websockets>=12.0 (اتصالات WebSocket)
# - asyncio-mqtt>=0.16.0 (MQTT لإنترنت الأشياء)
# - grpcio>=1.60.0 (gRPC للأداء العالي)
# - protobuf>=4.25.0 (Protocol Buffers)
# - socketio>=5.10.0 (Socket.IO للاتصالات الفورية)
#
# 🔒 أمان إضافي (1):
# - keyring>=24.3.0 (تخزين آمن للمفاتيح)
#
# 🌐 شبكة متقدمة (2):
# - aiohttp>=3.9.0 (HTTP client/server غير متزامن)
# - paramiko>=3.4.0 (اتصالات SSH)
#
# ☁️ تكاملات سحابية (3):
# - google-cloud-storage>=2.10.0 (Google Cloud Storage)
# - boto3>=1.34.0 (AWS SDK)
# - azure-storage-blob>=12.19.0 (Azure Storage)
#
# 🔗 تكاملات التطوير (3):
# - pygithub>=2.1.0 (GitHub API)
# - python-gitlab>=4.4.0 (GitLab API)
# - kubernetes>=28.1.0 (Kubernetes client)
#
# 🗄️ قواعد بيانات غير متزامنة (3):
# - motor>=3.3.0 (MongoDB غير متزامن)
# - asyncpg>=0.29.0 (PostgreSQL غير متزامن)
# - aioredis>=2.0.0 (Redis غير متزامن)
#
# ==========================================
