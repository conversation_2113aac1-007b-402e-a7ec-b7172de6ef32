🔍 تقرير فحص أدوات التطوير الشامل
============================================================
تاريخ الفحص: 2025-07-23T03:04:50.516417

📊 الإحصائيات العامة:
   🔢 إجمالي الفئات: 6
   ✅ أدوات موجودة: 11
   ❌ أدوات غير موجودة: 12
   📦 إجمالي التثبيتات: 38
   📋 أدوات في السجل: 57

📂 DEVELOPMENT_TOOLS:

📂 PACKAGE_MANAGERS:
   ✅ npm:
      📍 C:/Program Files/nodejs\npm.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
'"node"' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Program Files/nodejs\node_modules\corepack\shims\npm.cmd
      📋 الإصدار: 'node' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Program Files/nodejs\node_modules\corepack\shims\nodewin\npm.cmd
      📋 الإصدار: 'node' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Program Files/nodejs\node_modules\npm\bin\npm.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
'"node"' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Users\mo_as\AppData\Roaming\npm\npm.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Users\mo_as\AppData\Roaming\npm\node_modules\npm\bin\npm.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
'"node"' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:\Program Files\nodejs\npm.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
'"node"' is not recognized as an internal or external command,
operable program or batch file.
   ✅ yarn:
      📍 C:/Users\mo_as\AppData\Roaming\npm\yarn.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:/Users\mo_as\AppData\Roaming\npm\node_modules\yarn\bin\yarn.cmd
      📋 الإصدار: 'node' is not recognized as an internal or external command,
operable program or batch file.
      📍 C:\Users\<USER>\AppData\Roaming\npm\yarn.cmd
      📋 الإصدار: '"node"' is not recognized as an internal or external command,
operable program or batch file.
   ✅ pip:
      📍 C:\Users\<USER>\Universal-AI-Assistants\.venv\Scripts\pip.exe
      📋 الإصدار: pip 25.1.1 from c:\Users\<USER>\Universal-AI-Assistants\.venv\Lib\site-packages\pip (python 3.13)
      📍 C:\Users\<USER>\Universal-AI-Assistants\.venv\Scripts\pip3.exe
      📋 الإصدار: pip 25.1.1 from c:\Users\<USER>\Universal-AI-Assistants\.venv\Lib\site-packages\pip (python 3.13)
   ❌ conda: غير موجود
   ❌ uv: غير موجود
   ❌ composer: غير موجود

📂 PROGRAMMING_LANGUAGES:
   ✅ python:
      📍 C:/Users\mo_as\AppData\Local\Programs\Python\Python313\python.exe
      📋 الإصدار: Python 3.13.5
      📍 C:\Users\<USER>\Universal-AI-Assistants\.venv\Scripts\python.exe
      📋 الإصدار: Python 3.13.5
      📍 C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe
      📋 الإصدار: Python was not found; run without arguments to install from the Microsoft Store, or disable this shortcut from Settings > Apps > Advanced app settings > App execution aliases.
   ❌ node: غير موجود
   ✅ java:
      📍 C:/Program Files/Java\jdk-23\bin\java.exe
      📋 الإصدار: 
      📍 C:/Program Files/Java\jdk-23\bin\javac.exe
      📋 الإصدار: javac 23.0.2
      📍 C:\Program Files\Common Files\Oracle\Java\javapath\java.exe
      📋 الإصدار: 
      📍 C:\Program Files\Common Files\Oracle\Java\javapath\javac.exe
      📋 الإصدار: javac 23.0.2
   ✅ dotnet:
      📍 C:/Program Files/dotnet\dotnet.exe
      📋 الإصدار: 10.0.100-preview.2.25164.34
      📍 C:\Program Files\dotnet\dotnet.exe
      📋 الإصدار: 10.0.100-preview.2.25164.34
   ✅ go:
      📍 C:/Program Files/Go\bin\go.exe
      📋 الإصدار: go version go1.24.2 windows/amd64
      📍 C:\Program Files\Go\bin\go.exe
      📋 الإصدار: go version go1.24.2 windows/amd64
   ❌ rust: غير موجود

📂 DATABASES:
   ✅ mysql:
      📍 C:/Program Files/MySQL\MySQL Server 8.0\bin\mysql.exe
      📋 الإصدار: C:/Program Files/MySQL\MySQL Server 8.0\bin\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)
      📍 C:/Program Files/MySQL\MySQL Server 8.0\bin\mysqld.exe
      📋 الإصدار: C:/Program Files/MySQL\MySQL Server 8.0\bin\mysqld.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)
      📍 C:/Program Files/MySQL\MySQL Workbench 8.0\mysql.exe
      📋 الإصدار: C:/Program Files/MySQL\MySQL Workbench 8.0\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)
      📍 C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe
      📋 الإصدار: C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)
      📍 C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqld.exe
      📋 الإصدار: C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqld.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)
   ✅ postgresql:
      📍 C:/Program Files/PostgreSQL\17\bin\psql.exe
      📋 الإصدار: psql (PostgreSQL) 17.4
      📍 C:/Program Files/PostgreSQL\17\pgAdmin 4\runtime\psql.exe
      📋 الإصدار: psql (PostgreSQL) 17.4
      📍 C:/Program Files/PostgreSQL\17\bin\postgres.exe
      📋 الإصدار: postgres (PostgreSQL) 17.4
   ❌ sqlite: غير موجود

📂 CONTAINERIZATION:
   ✅ docker:
      📍 C:/Program Files/Docker\Docker\resources\bin\docker.exe
      📋 الإصدار: Docker version 28.3.2, build 578ccf6
      📍 C:\Program Files\Docker\Docker\resources\bin\docker.exe
      📋 الإصدار: Docker version 28.3.2, build 578ccf6
   ❌ podman: غير موجود

📂 VERSION_CONTROL:
   ✅ git:
      📍 C:/Program Files/Git\bin\git.exe
      📋 الإصدار: git version 2.49.0.windows.1
      📍 C:/Program Files/Git\cmd\git.exe
      📋 الإصدار: git version 2.49.0.windows.1
      📍 C:/Program Files/Git\mingw64\bin\git.exe
      📋 الإصدار: git version 2.49.0.windows.1
      📍 C:/Program Files/Git\mingw64\libexec\git-core\git.exe
      📋 الإصدار: git version 2.49.0.windows.1
      📍 C:\Program Files\Git\cmd\git.exe
      📋 الإصدار: git version 2.49.0.windows.1
   ❌ svn: غير موجود

📂 EDITORS_IDES:

📂 BUILD_TOOLS:
   ❌ cmake: غير موجود
   ❌ make: غير موجود
   ❌ gradle: غير موجود
   ❌ maven: غير موجود

