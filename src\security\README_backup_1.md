# 🏺 نظام أنوبيس المعزول - Anubis Isolation System

## 📋 نظرة عامة
نظام معزول متقدم يعمل بشكل منفصل عن النظام الأساسي، مصمم للأمان العالي والأداء المثالي.

## 🏗️ المعمارية

### 🔧 الخدمات:
- **API Service** (Port 8080): خدمة API الرئيسية
- **Worker Service**: معالجة المهام في الخلفية
- **Monitor Service** (Port 9090): مراقبة النظام
- **PostgreSQL Database**: قاعدة بيانات معزولة
- **Redis Cache**: ذاكرة تخزين مؤقت

### 📁 بنية المجلدات:
```
anubis_isolation_system/
├── api/                                    # خدمة API
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── main.py
│   └── worker_simple.py
├── worker/                                 # خدمة Worker
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── worker.py
│   └── worker_simple.py
├── monitor/                                # خدمة المراقبة
│   ├── Dockerfile
│   ├── requirements.txt
│   └── monitor.py
├── docker-compose-isolation.yml           # ملف Docker Compose
├── anubis_docker_isolation_launcher.py    # مشغل النظام
├── anubis_isolation_system_manager.py     # مدير النظام
├── anubis_isolation_status_checker.py     # فاحص الحالة
└── README.md                              # هذا الملف
```

## 🚀 التشغيل السريع

### 1. تشغيل النظام الكامل:
```bash
cd anubis_isolation_system
docker-compose -f docker-compose-isolation.yml up -d
```

### 2. باستخدام المشغل الآلي:
```bash
python anubis_docker_isolation_launcher.py
```

### 3. فحص حالة النظام:
```bash
python anubis_isolation_status_checker.py
```

## 🌐 الروابط المتاحة

### 📱 خدمة API:
- **الرئيسية:** http://localhost:8080
- **التوثيق:** http://localhost:8080/docs
- **ReDoc:** http://localhost:8080/redoc
- **فحص الصحة:** http://localhost:8080/health

### 📊 خدمة المراقبة:
- **لوحة المراقبة:** http://localhost:9090
- **مراقبة الخدمات:** http://localhost:9090/monitor/services
- **حالة النظام:** http://localhost:9090/monitor/system

## 🛠️ أوامر الإدارة

### تشغيل خدمة واحدة:
```bash
docker-compose -f docker-compose-isolation.yml up -d [service-name]
```

### إيقاف النظام:
```bash
docker-compose -f docker-compose-isolation.yml down
```

### عرض السجلات:
```bash
docker-compose -f docker-compose-isolation.yml logs [service-name]
```

### إعادة بناء الخدمات:
```bash
docker-compose -f docker-compose-isolation.yml build
```

## 🔒 الأمان والعزل

### ميزات الأمان:
- ✅ شبكة معزولة منفصلة
- ✅ مستخدمين غير مميزين في الحاويات
- ✅ قيود الموارد (CPU, Memory, PIDs)
- ✅ أحجام تخزين معزولة
- ✅ فحوصات صحة مستمرة

### العزل:
- 🌐 شبكة منفصلة: `anubis-isolation-net`
- 💾 أحجام معزولة لكل خدمة
- 🔐 كلمات مرور منفصلة
- 🛡️ إعدادات أمان متقدمة

## 📊 المراقبة والتشخيص

### أدوات المراقبة:
- `anubis_isolation_status_checker.py` - فحص شامل للحالة
- `anubis_isolation_system_manager.py` - إدارة شاملة
- `anubis_quick_docker_diagnosis.py` - تشخيص سريع

### المقاييس المتاحة:
- حالة الخدمات
- استخدام الموارد
- أوقات الاستجابة
- فحوصات الصحة

## 🔧 التطوير والصيانة

### إضافة خدمة جديدة:
1. إنشاء مجلد للخدمة
2. إضافة Dockerfile
3. تحديث docker-compose-isolation.yml
4. اختبار الخدمة

### التحديث:
1. إيقاف النظام
2. تحديث الكود
3. إعادة البناء
4. إعادة التشغيل

## 🎯 الاستخدام مع النظام الأساسي

هذا النظام مصمم للعمل جنباً إلى جنب مع النظام الأساسي:
- **النظام الأساسي:** Port 8000
- **النظام المعزول:** Port 8080
- **المراقبة:** Port 9090

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع السجلات: `docker-compose logs`
2. استخدم أدوات التشخيص المتوفرة
3. تحقق من حالة الخدمات

---

**🏺 نظام أنوبيس المعزول - أمان وأداء متقدم**
