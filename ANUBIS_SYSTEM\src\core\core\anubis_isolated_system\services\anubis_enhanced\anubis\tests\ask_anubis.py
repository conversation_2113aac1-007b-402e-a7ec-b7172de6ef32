#!/usr/bin/env python3
"""
🏺 طلب المساعدة من نظام أنوبيس
Ask Anubis for Help

نص تفاعلي لطلب المساعدة من نظام أنوبيس الذكي
"""

import json
import os
import sys
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "database"))


def ask_anubis_for_help():
    """طلب المساعدة من نظام أنوبيس"""
    print("🏺 ═══════════════════════════════════════════════════════════════")
    print("   مرحباً بك في نظام أنوبيس للمساعدين الذكيين")
    print("   Welcome to Anubis AI Assistants System")
    print("═══════════════════════════════════════════════════════════════ 🏺")
    print()
    print("🏺 أنا أنوبيس، إله الحكمة والذكاء الاصطناعي")
    print("🤖 أستطيع مساعدتك في تحليل وتطوير مشاريعك البرمجية")
    print("📊 جميع التحليلات والنتائج ستحفظ في قاعدة البيانات")
    print()

    # عرض الخدمات المتاحة
    print("🔧 الخدمات المتاحة:")
    print("   1. 🔍 تحليل شامل للمشروع")
    print("   2. 🐛 كشف الأخطاء والمشاكل")
    print("   3. 📊 تحليل جودة الكود")
    print("   4. 🗂️ تنظيم الملفات")
    print("   5. 🧠 إدارة الذاكرة والمعرفة")
    print("   6. 📈 عرض إحصائيات قاعدة البيانات")
    print("   7. 🏺 اختبار النظام")
    print("   0. 🚪 خروج")
    print()

    while True:
        try:
            choice = input("🏺 اختر الخدمة المطلوبة (0-7): ").strip()

            if choice == "0":
                print("🏺 شكراً لاستخدام نظام أنوبيس! وداعاً 👋")
                break
            elif choice == "1":
                analyze_project()
            elif choice == "2":
                detect_errors()
            elif choice == "3":
                analyze_code_quality()
            elif choice == "4":
                organize_files()
            elif choice == "5":
                manage_memory()
            elif choice == "6":
                show_database_stats()
            elif choice == "7":
                test_system()
            else:
                print("❌ اختيار غير صحيح. يرجى اختيار رقم من 0 إلى 7")

            print("\n" + "=" * 60 + "\n")

        except KeyboardInterrupt:
            print("\n🏺 تم إيقاف النظام بواسطة المستخدم. وداعاً! 👋")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")


def analyze_project():
    """تحليل شامل للمشروع"""
    print("🔍 تحليل شامل للمشروع")
    print("=" * 30)

    project_path = input("📁 أدخل مسار المشروع (اتركه فارغاً للمشروع الحالي): ").strip()
    if not project_path:
        project_path = str(Path.cwd())

    if not Path(project_path).exists():
        print(f"❌ المسار غير موجود: {project_path}")
        return

    try:
        from anubis.core.assistant_system import UniversalAssistantSystem
        from anubis.core.config_manager import ConfigManager

        print(f"🔧 تحليل المشروع: {project_path}")

        # تحميل التكوين
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # إنشاء النظام
        system = UniversalAssistantSystem(project_path=project_path, config=config, verbose=True)

        print(f"✅ تم تحميل المشروع: {system.project_path.name}")
        print(f"📊 نوع المشروع: {system.project_type}")
        print(f"🤖 الوكلاء المتاحين: {len(system.available_agents)}")

        # تشغيل التحليل
        print("\n🔍 بدء التحليل الشامل...")
        results = system.analyze_project()

        if results.get("success", False):
            print("✅ تم التحليل بنجاح!")

            # عرض الملخص
            summary = results.get("summary", {})
            print("\n📊 ملخص النتائج:")
            print(f"   إجمالي الوكلاء: {summary.get('total_agents', 0)}")
            print(f"   الوكلاء الناجحين: {summary.get('successful_agents', 0)}")
            print(f"   الوكلاء الفاشلين: {summary.get('failed_agents', 0)}")

            # عرض نتائج كل وكيل
            agents_results = results.get("agents_results", {})
            print("\n🤖 تفاصيل النتائج:")
            for agent_name, result in agents_results.items():
                status = "✅ نجح" if result.get("success", False) else "❌ فشل"
                print(f"   {agent_name}: {status}")

                if result.get("success", False) and "analysis" in result:
                    analysis = result["analysis"]
                    if "summary" in analysis:
                        agent_summary = analysis["summary"]
                        files_count = agent_summary.get("files_analyzed", 0)
                        if files_count > 0:
                            print(f"      📁 الملفات المحللة: {files_count}")
                        if "score" in agent_summary:
                            print(f"      📊 النتيجة: {agent_summary['score']}")

            # معلومات قاعدة البيانات
            if system.db_manager:
                print("\n🏺 تم حفظ النتائج في قاعدة البيانات")
                dashboard_data = system.db_manager.get_dashboard_data()
                stats = dashboard_data.get("stats", {})
                print(f"   📊 إجمالي المشاريع: {stats.get('total_projects', 0)}")
                print(f"   📈 إجمالي التحليلات: {stats.get('total_analyses', 0)}")
        else:
            print("❌ فشل في التحليل")

    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")


def detect_errors():
    """كشف الأخطاء والمشاكل"""
    print("🐛 كشف الأخطاء والمشاكل")
    print("=" * 30)

    project_path = input("📁 أدخل مسار المشروع: ").strip()
    if not project_path:
        project_path = str(Path.cwd())

    try:
        from anubis.agents.error_detector_agent import ErrorDetectorAgent

        print(f"🔧 فحص الأخطاء في: {project_path}")
        # إنشاء وكيل كشف الأخطاء
        agent = ErrorDetectorAgent(
            project_path=project_path,
            config={"check_syntax": True, "check_logic": True, "check_style": True},
            verbose=True,
        )

        # تشغيل الفحص
        result = agent.run()

        if result.get("success", False):
            analysis = result.get("analysis", {})
            summary = analysis.get("summary", {})

            print(f"✅ تم فحص {summary.get('files_analyzed', 0)} ملف")
            print(f"🐛 تم العثور على {summary.get('total_issues', 0)} مشكلة")
            # عرض الأخطاء بحسب النوع
            issues_by_type = summary.get("issues_by_type", {})
            for issue_type, count in issues_by_type.items():
                if count > 0:
                    print(f"   {issue_type}: {count}")
        else:
            print("❌ فشل في فحص الأخطاء")

    except Exception as e:
        print(f"❌ خطأ في فحص الأخطاء: {e}")


def analyze_code_quality():
    """تحليل جودة الكود"""
    print("📊 تحليل جودة الكود")
    print("=" * 30)

    project_path = input("📁 أدخل مسار المشروع: ").strip()
    if not project_path:
        project_path = str(Path.cwd())

    try:
        from anubis.agents.project_analyzer_agent import ProjectAnalyzerAgent

        print(f"🔧 تحليل جودة الكود في: {project_path}")
        # إنشاء وكيل تحليل المشاريع
        agent = ProjectAnalyzerAgent(
            project_path=project_path,
            config={"deep_analysis": True, "performance_check": True},
            verbose=True,
        )

        # تشغيل التحليل
        result = agent.run()

        if result.get("success", False):
            analysis = result.get("analysis", {})
            summary = analysis.get("summary", {})

            print(f"✅ تم تحليل {summary.get('files_analyzed', 0)} ملف")
            overall_score = summary.get("overall_score", "غير محدد")
            print(f"📊 النتيجة الإجمالية: {overall_score}")
            complexity_score = summary.get("complexity_score", "غير محدد")
            print(f"🔧 التعقيد: {complexity_score}")
            security_score = summary.get("security_score", "غير محدد")
            print(f"🛡️ الأمان: {security_score}")
        else:
            print("❌ فشل في تحليل جودة الكود")

    except Exception as e:
        print(f"❌ خطأ في تحليل جودة الكود: {e}")


def organize_files():
    """تنظيم الملفات"""
    print("🗂️ تنظيم الملفات")
    print("=" * 30)
    print("🔧 هذه الميزة قيد التطوير...")


def manage_memory():
    """إدارة الذاكرة والمعرفة"""
    print("🧠 إدارة الذاكرة والمعرفة")
    print("=" * 30)
    print("🔧 هذه الميزة قيد التطوير...")


def show_database_stats():
    """عرض إحصائيات قاعدة البيانات"""
    print("📈 إحصائيات قاعدة البيانات")
    print("=" * 30)

    try:
        from database_manager import AnubisDatabaseManager

        # إنشاء مدير قاعدة البيانات
        db_manager = AnubisDatabaseManager()

        if db_manager.initialize_database():
            dashboard_data = db_manager.get_dashboard_data()
            stats = dashboard_data.get("stats", {})
            system_info = dashboard_data.get("system_info", {})

            print("🏺 معلومات النظام:")
            system_name = system_info.get("system_name", "غير محدد")
            print(f"   اسم النظام: {system_name}")
            db_type = system_info.get("database_type", "غير محدد")
            print(f"   نوع قاعدة البيانات: {db_type}")
            last_updated = system_info.get("last_updated", "غير محدد")
            print(f"   آخر تحديث: {last_updated}")

            print("\n📊 الإحصائيات:")
            print(f"   إجمالي المشاريع: {stats.get('total_projects', 0)}")
            print(f"   إجمالي التحليلات: {stats.get('total_analyses', 0)}")
            print(f"   إجمالي الأخطاء: {stats.get('total_errors', 0)}")
            print(f"   إجمالي التقارير: {stats.get('total_reports', 0)}")

            # المشاريع الحديثة
            recent_projects = dashboard_data.get("recent_projects", [])
            if recent_projects:
                print("\n📁 المشاريع الحديثة:")
                for project in recent_projects[:3]:
                    name = project.get("name", "غير محدد")
                    project_type = project.get("type", "غير محدد")
                    print(f"   - {name} ({project_type})")

            db_manager.close()
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")

    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")


def test_system():
    """اختبار النظام"""
    print("🏺 اختبار النظام")
    print("=" * 30)

    print("🔧 تشغيل اختبار شامل للنظام...")

    try:
        # تشغيل اختبار النظام
        os.system("python test_anubis_system.py")
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")


def main():
    """الدالة الرئيسية"""
    try:
        ask_anubis_for_help()
    except Exception as e:
        print(f"❌ خطأ في النظام: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
