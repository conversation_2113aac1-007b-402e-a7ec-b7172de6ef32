version: '3.8'

# 🏺 نظام أنوبيس - Docker Compose المحسن
# Anubis System - Enhanced Docker Compose

networks:
  anubis-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql_data:
  prometheus_data:
  grafana_data:
  n8n_data:
  redis_data:

services:
  # ===== النظام الأساسي - Core System =====
  anubis-core:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: anubis-core
    restart: unless-stopped
    command: python main.py
    volumes:
      - ./data:/app/data
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./src:/app/src
    environment:
      - ANUBIS_ENV=production
      - DATABASE_URL=mysql://anubis:anubis_secure_password@anubis-mysql:3306/anubis_system
      - REDIS_URL=redis://anubis-redis:6379
    ports:
      - "8000:8000"
    depends_on:
      - anubis-mysql
      - anubis-redis
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== قاعدة البيانات - Database =====
  anubis-mysql:
    image: mysql:8.0
    container_name: anubis-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: **********
      MYSQL_DATABASE: anubis_system
      MYSQL_USER: anubis
      MYSQL_PASSWORD: anubis_secure_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/mysql:/etc/mysql/conf.d:ro
    ports:
      - "3306:3306"
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ===== Redis للتخزين المؤقت - Redis Cache =====
  anubis-redis:
    image: redis:7-alpine
    container_name: anubis-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass anubis_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== أتمتة سير العمل - N8N Automation =====
  anubis-n8n:
    image: n8nio/n8n:latest
    container_name: anubis-n8n
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=anubis_n8n_password
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - WEBHOOK_URL=http://localhost:5678/
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
      - N8N_LOG_LEVEL=info
      - N8N_METRICS=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./src/automation:/home/<USER>/automation:ro
    ports:
      - "5678:5678"
    networks:
      - anubis-network
    depends_on:
      - anubis-mysql
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== مراقبة النظام - Prometheus =====
  anubis-prometheus:
    image: prom/prometheus:latest
    container_name: anubis-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./src/monitoring/prometheus:/etc/prometheus:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - anubis-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== لوحة المراقبة - Grafana =====
  anubis-grafana:
    image: grafana/grafana:latest
    container_name: anubis-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=anubis_grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./src/monitoring/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - anubis-network
    depends_on:
      - anubis-prometheus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
