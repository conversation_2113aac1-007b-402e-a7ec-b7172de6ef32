#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 نظام الاستدعاء الحقيقي لنماذج الذكاء الاصطناعي
Real AI Models Caller System

نظام حقيقي لاستدعاء نماذج الذكاء الاصطناعي باستخدام المفاتيح المشفرة
Real system for calling AI models using encrypted keys
"""

import os
import json
import asyncio
import aiohttp
import requests
from datetime import datetime
from pathlib import Path
from cryptography.fernet import Fernet
import base64
import hashlib
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealAICaller:
    """🔥 نظام الاستدعاء الحقيقي للذكاء الاصطناعي"""
    
    def __init__(self, master_password: str):
        """تهيئة النظام مع كلمة المرور الرئيسية"""
        self.vault_dir = Path(__file__).parent / "vault"
        self.master_password = master_password
        self.encryption_key = None
        self.decrypted_keys = {}
        
        # تحميل مفتاح التشفير
        self.load_master_key()
        
        logger.info("🔥 تم تهيئة نظام الاستدعاء الحقيقي")
    
    def load_master_key(self) -> bool:
        """تحميل مفتاح التشفير الرئيسي"""
        try:
            master_key_file = self.vault_dir / "secure" / "master.key"
            if not master_key_file.exists():
                logger.error("❌ ملف المفتاح الرئيسي غير موجود")
                return False
            
            with open(master_key_file, 'rb') as f:
                data = f.read()
            
            salt = data[:16]
            stored_key = data[16:]
            
            # إعادة إنشاء المفتاح
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.master_password.encode()))
            
            if key == stored_key:
                self.encryption_key = Fernet(key)
                logger.info("✅ تم تحميل مفتاح التشفير بنجاح")
                return True
            else:
                logger.error("❌ كلمة مرور خاطئة")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل المفتاح: {e}")
            return False
    
    def decrypt_keys(self) -> bool:
        """فك تشفير المفاتيح"""
        try:
            # البحث عن أحدث ملف مفاتيح مشفرة
            secure_dir = self.vault_dir / "secure"
            encrypted_files = list(secure_dir.glob("encrypted_keys_*.json"))
            
            if not encrypted_files:
                logger.error("❌ لم يتم العثور على ملفات المفاتيح المشفرة")
                return False
            
            latest_file = max(encrypted_files, key=os.path.getctime)
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                encrypted_data = json.load(f)
            
            # فك تشفير المفاتيح
            for platform, platform_data in encrypted_data.get("encrypted_keys", {}).items():
                self.decrypted_keys[platform] = []
                
                for key_info in platform_data.get("keys", []):
                    encrypted_key_data = key_info.get("encrypted_data", {})
                    
                    try:
                        # فك تشفير المفتاح
                        encrypted_key = base64.b64decode(encrypted_key_data["encrypted_key"])
                        decrypted_key = self.encryption_key.decrypt(encrypted_key).decode()
                        
                        # التحقق من صحة المفتاح
                        key_hash = hashlib.sha256(decrypted_key.encode()).hexdigest()
                        if key_hash == encrypted_key_data["key_hash"]:
                            self.decrypted_keys[platform].append({
                                "name": key_info.get("name"),
                                "key": decrypted_key,
                                "usage": key_info.get("usage"),
                                "platform": platform
                            })
                        
                    except Exception as e:
                        logger.warning(f"⚠️ فشل في فك تشفير مفتاح {key_info.get('name', 'unknown')}: {e}")
            
            total_keys = sum(len(keys) for keys in self.decrypted_keys.values())
            logger.info(f"✅ تم فك تشفير {total_keys} مفتاح من {len(self.decrypted_keys)} منصة")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فك تشفير المفاتيح: {e}")
            return False
    
    async def call_google_gemini_real(self, prompt: str, model: str = "gemini-pro") -> dict:
        """استدعاء حقيقي لـ Google Gemini"""
        try:
            if "google_gemini" not in self.decrypted_keys or not self.decrypted_keys["google_gemini"]:
                return {"status": "error", "error": "لا توجد مفاتيح Google Gemini متاحة"}
            
            api_key = self.decrypted_keys["google_gemini"][0]["key"]
            
            url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": api_key
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # استخراج النص من الاستجابة
                        text_response = ""
                        if "candidates" in result and result["candidates"]:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                text_response = candidate["content"]["parts"][0].get("text", "")
                        
                        return {
                            "platform": "Google Gemini",
                            "model": model,
                            "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                            "response": text_response,
                            "timestamp": datetime.now().isoformat(),
                            "status": "success",
                            "tokens_used": len(prompt.split()) + len(text_response.split()),
                            "raw_response": result
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "status": "error",
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Google Gemini: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_openrouter_real(self, prompt: str, model: str = "openai/gpt-3.5-turbo") -> dict:
        """استدعاء حقيقي لـ OpenRouter"""
        try:
            if "openrouter" not in self.decrypted_keys or not self.decrypted_keys["openrouter"]:
                return {"status": "error", "error": "لا توجد مفاتيح OpenRouter متاحة"}
            
            api_key = self.decrypted_keys["openrouter"][0]["key"]
            
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/your-repo",
                "X-Title": "Anubis Horus AI System"
            }
            
            data = {
                "model": model,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        text_response = ""
                        if "choices" in result and result["choices"]:
                            text_response = result["choices"][0]["message"]["content"]
                        
                        return {
                            "platform": "OpenRouter",
                            "model": model,
                            "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                            "response": text_response,
                            "timestamp": datetime.now().isoformat(),
                            "status": "success",
                            "tokens_used": result.get("usage", {}).get("total_tokens", 0),
                            "raw_response": result
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "status": "error",
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء OpenRouter: {e}")
            return {"status": "error", "error": str(e)}
    
    async def call_huggingface_real(self, prompt: str, model: str = "microsoft/DialoGPT-medium") -> dict:
        """استدعاء حقيقي لـ Hugging Face"""
        try:
            if "huggingface" not in self.decrypted_keys or not self.decrypted_keys["huggingface"]:
                return {"status": "error", "error": "لا توجد مفاتيح Hugging Face متاحة"}
            
            api_key = self.decrypted_keys["huggingface"][0]["key"]
            
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "inputs": prompt,
                "parameters": {
                    "max_length": 100,
                    "temperature": 0.7
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        text_response = ""
                        if isinstance(result, list) and result:
                            text_response = result[0].get("generated_text", "")
                        elif isinstance(result, dict):
                            text_response = result.get("generated_text", str(result))
                        
                        return {
                            "platform": "Hugging Face",
                            "model": model,
                            "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                            "response": text_response,
                            "timestamp": datetime.now().isoformat(),
                            "status": "success",
                            "tokens_used": len(prompt.split()) + len(text_response.split()),
                            "raw_response": result
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "status": "error",
                            "error": f"HTTP {response.status}: {error_text}"
                        }
                        
        except Exception as e:
            logger.error(f"❌ خطأ في استدعاء Hugging Face: {e}")
            return {"status": "error", "error": str(e)}
    
    def get_available_keys_summary(self) -> dict:
        """الحصول على ملخص المفاتيح المتاحة"""
        summary = {}
        for platform, keys in self.decrypted_keys.items():
            summary[platform] = {
                "count": len(keys),
                "keys": [{"name": key["name"], "usage": key["usage"]} for key in keys]
            }
        return summary
    
    async def test_all_platforms(self, test_prompt: str = "مرحباً، كيف حالك؟") -> dict:
        """اختبار جميع المنصات المتاحة"""
        results = {
            "test_prompt": test_prompt,
            "timestamp": datetime.now().isoformat(),
            "platforms_tested": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "results": {}
        }
        
        # اختبار Google Gemini
        if "google_gemini" in self.decrypted_keys and self.decrypted_keys["google_gemini"]:
            logger.info("🧪 اختبار Google Gemini...")
            gemini_result = await self.call_google_gemini_real(test_prompt)
            results["results"]["google_gemini"] = gemini_result
            results["platforms_tested"] += 1
            if gemini_result.get("status") == "success":
                results["successful_calls"] += 1
            else:
                results["failed_calls"] += 1
        
        # اختبار OpenRouter
        if "openrouter" in self.decrypted_keys and self.decrypted_keys["openrouter"]:
            logger.info("🧪 اختبار OpenRouter...")
            openrouter_result = await self.call_openrouter_real(test_prompt)
            results["results"]["openrouter"] = openrouter_result
            results["platforms_tested"] += 1
            if openrouter_result.get("status") == "success":
                results["successful_calls"] += 1
            else:
                results["failed_calls"] += 1
        
        # اختبار Hugging Face
        if "huggingface" in self.decrypted_keys and self.decrypted_keys["huggingface"]:
            logger.info("🧪 اختبار Hugging Face...")
            hf_result = await self.call_huggingface_real(test_prompt)
            results["results"]["huggingface"] = hf_result
            results["platforms_tested"] += 1
            if hf_result.get("status") == "success":
                results["successful_calls"] += 1
            else:
                results["failed_calls"] += 1
        
        return results

async def main():
    """الدالة الرئيسية"""
    print("🔥 نظام الاستدعاء الحقيقي لنماذج الذكاء الاصطناعي")
    print("=" * 80)
    
    # كلمة المرور الرئيسية (من النظام الأمني)
    master_password = "JIGc_1lK-oY-eiReZkevPDYuJkob0Uf1lB78J5XQcgM"
    
    # إنشاء النظام
    ai_caller = RealAICaller(master_password)
    
    # فك تشفير المفاتيح
    if not ai_caller.decrypt_keys():
        print("❌ فشل في فك تشفير المفاتيح")
        return
    
    # عرض المفاتيح المتاحة
    keys_summary = ai_caller.get_available_keys_summary()
    print("\n🔑 المفاتيح المتاحة:")
    for platform, info in keys_summary.items():
        print(f"   ✅ {platform}: {info['count']} مفتاح")
    
    # اختبار المنصات
    print("\n🧪 اختبار المنصات المتاحة...")
    test_results = await ai_caller.test_all_platforms("اكتب لي جملة ترحيب باللغة العربية")
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"   🧪 منصات مختبرة: {test_results['platforms_tested']}")
    print(f"   ✅ استدعاءات ناجحة: {test_results['successful_calls']}")
    print(f"   ❌ استدعاءات فاشلة: {test_results['failed_calls']}")
    
    print(f"\n📝 النتائج التفصيلية:")
    for platform, result in test_results["results"].items():
        if result.get("status") == "success":
            print(f"   ✅ {platform}: {result.get('response', 'No response')[:100]}...")
        else:
            print(f"   ❌ {platform}: {result.get('error', 'Unknown error')}")
    
    # حفظ نتائج الاختبار
    results_file = Path(__file__).parent / "vault" / "ai_models" / f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 تم حفظ نتائج الاختبار: {results_file}")

if __name__ == "__main__":
    asyncio.run(main())
