{"dashboard_settings": {"title": "🎛️ VS Code Process Control Dashboard", "window_size": {"width": 1200, "height": 800}, "theme": {"background": "#2d2d2d", "title_background": "#1e1e1e", "text_color": "#ffffff", "log_background": "#1e1e1e", "log_text_color": "#ffffff"}, "colors": {"stats": ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4"], "buttons": {"refresh": "#4ecdc4", "kill": "#ff6b6b", "cleanup": "#e74c3c", "disable": "#f39c12", "enable": "#27ae60", "restart": "#3498db", "optimize": "#9b59b6", "save": "#34495e"}}, "fonts": {"title": ["<PERSON><PERSON>", 16, "bold"], "stats": ["<PERSON><PERSON>", 10, "bold"], "stats_value": ["<PERSON><PERSON>", 14, "bold"], "section": ["<PERSON><PERSON>", 12, "bold"], "button": ["<PERSON><PERSON>", 10, "bold"], "log": ["Consolas", 9]}}, "monitoring": {"auto_refresh_interval": 5, "process_filters": ["code", "node", "electron", "typescript", "eslint", "tsc", "webpack"], "performance_thresholds": {"max_processes": 20, "max_memory_mb": 2048, "max_extensions": 30, "idle_cpu_threshold": 1.0}}, "optimization": {"vscode_startup_args": ["--max-memory=4096", "--disable-gpu"], "cleanup_idle_processes": true, "auto_restart_on_high_memory": false, "memory_threshold_restart": 4096}, "extensions": {"auto_disable_unused": false, "performance_extensions": ["ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "ms-python.python", "ms-vscode.cpptools"], "essential_extensions": ["ms-vscode.vscode-json"]}, "reports": {"auto_save": false, "save_interval_minutes": 30, "report_format": "txt", "include_performance_profile": true, "max_log_entries": 1000}, "alerts": {"high_memory_warning": true, "high_cpu_warning": true, "too_many_processes_warning": true, "thresholds": {"memory_warning_mb": 3072, "cpu_warning_percent": 80, "process_count_warning": 25}}, "advanced": {"debug_mode": false, "log_level": "INFO", "backup_settings": true, "check_for_updates": true, "telemetry": false}, "shortcuts": {"refresh_processes": "F5", "kill_selected": "Delete", "cleanup_all": "Ctrl+Shift+K", "toggle_monitoring": "Ctrl+M", "save_report": "Ctrl+S", "optimize_performance": "Ctrl+O"}, "ui_layout": {"show_stats_bar": true, "show_log_panel": true, "stats_bar_height": 60, "log_panel_height": 150, "process_tree_columns": {"process": 150, "pid": 80, "name": 200, "cpu": 80, "memory": 100, "status": 100}, "extensions_tree_columns": {"extension": 200, "id": 150, "version": 80, "status": 80, "publisher": 120}}, "language": {"locale": "ar", "rtl_support": true, "date_format": "%Y-%m-%d %H:%M:%S", "number_format": "arabic"}}