# Anubis MCP Server

## 🚀 نظرة عامة

خادم MCP (Model Context Protocol) لنظام أنوبيس للذكاء الاصطناعي. يوفر واجهة للتفاعل مع مكونات النظام المختلفة.

## 📋 المتطلبات

- Node.js 18.0.0 أو أحدث
- npm (يأتي مع Node.js)

## 🛠️ التثبيت والإعداد

### 1. التحقق من Node.js
```bash
node --version
npm --version
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل الخادم
```bash
# الطريقة الأولى
npm start

# الطريقة الثانية
node src/index.js

# الطريقة الثالثة (Windows)
start-mcp-server.bat
```

## 🔧 التكوين

### إعداد VS Code
1. افتح إعدادات VS Code
2. ابحث عن "MCP"
3. أضف التكوين التالي:

```json
{
  "mcpServers": {
    "anubis": {
      "command": "node",
      "args": ["src/index.js"],
      "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants"
    }
  }
}
```

## 🛠️ الأدوات المتاحة

### 1. anubis_status
- **الوصف**: الحصول على حالة نظام أنوبيس
- **المعاملات**: لا توجد
- **المثال**:
```json
{
  "status": "running",
  "timestamp": "2025-01-23T10:00:00.000Z",
  "components": {
    "database": "connected",
    "agents": "active",
    "api": "running"
  }
}
```

### 2. anubis_info
- **الوصف**: الحصول على معلومات مكونات النظام
- **المعاملات**:
  - `component`: نوع المكون (database, agents, api, all)
- **المثال**:
```json
{
  "component": "database",
  "info": "Information about database component",
  "version": "2.0.0",
  "last_updated": "2025-01-23T10:00:00.000Z"
}
```

## 🐛 استكشاف الأخطاء

### خطأ "node is not recognized"
- تأكد من تثبيت Node.js
- أعد تشغيل Terminal/Command Prompt
- تحقق من متغير البيئة PATH

### خطأ "Cannot find module"
```bash
npm install
```

### خطأ في الاتصال
- تأكد من تشغيل الخادم
- تحقق من المنفذ والتكوين
- راجع سجلات الأخطاء

## 📝 السجلات

الخادم يكتب السجلات إلى:
- `console.error` للأخطاء
- `stdout` للرسائل العادية

## 🔄 التطوير

### إضافة أدوات جديدة
1. أضف الأداة في `ListToolsRequestSchema`
2. أضف معالج في `CallToolRequestSchema`
3. اختبر الأداة

### مثال على أداة جديدة
```javascript
{
  name: 'new_tool',
  description: 'Description of new tool',
  inputSchema: {
    type: 'object',
    properties: {
      param: {
        type: 'string',
        description: 'Parameter description'
      }
    }
  }
}
```

## 📞 الدعم

للحصول على المساعدة:
1. تحقق من هذا الملف
2. راجع سجلات الأخطاء
3. تأكد من التكوين الصحيح
4. اتصل بفريق أنوبيس

## 📄 الترخيص

MIT License - انظر ملف package.json للتفاصيل
