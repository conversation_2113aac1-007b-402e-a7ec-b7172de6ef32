{"system_info": {"timestamp": "2025-07-19T08:09:33.199529", "version": "4.0", "type": "advanced_isolation_system", "based_on_scan": null}, "components": {}, "networks": {"anubis_main_network": {"driver": "bridge", "subnet": "172.31.0.0/16", "gateway": "172.31.0.1", "isolation": "strict"}, "anubis_monitoring_network": {"driver": "bridge", "subnet": "172.32.0.0/16", "gateway": "172.32.0.1", "isolation": "monitoring_only"}, "anubis_security_network": {"driver": "bridge", "subnet": "172.33.0.0/16", "gateway": "172.33.0.1", "isolation": "security_only"}}, "volumes": {"anubis_main_data": {"driver": "local", "type": "persistent", "backup_enabled": true}, "universal_ai_data": {"driver": "local", "type": "persistent", "backup_enabled": true}, "tools_data": {"driver": "local", "type": "persistent", "backup_enabled": false}, "database_data": {"driver": "local", "type": "persistent", "backup_enabled": true, "encryption": true}, "monitoring_data": {"driver": "local", "type": "persistent", "backup_enabled": true}, "shared_logs": {"driver": "local", "type": "temporary", "backup_enabled": false}}, "monitoring": {}, "security": {"network_policies": {"default_deny": true, "allowed_connections": [{"from": "anubis_main", "to": "database", "port": 3306}, {"from": "universal_ai", "to": "database", "port": 3306}, {"from": "monitoring", "to": "*", "port": "metrics"}]}, "container_security": {"non_root_users": true, "read_only_filesystem": true, "no_new_privileges": true, "security_opt": ["no-new-privileges:true"]}, "secrets_management": {"database_password": "anubis_db_secret", "api_keys": "anubis_api_secrets", "certificates": "anubis_tls_certs"}}}