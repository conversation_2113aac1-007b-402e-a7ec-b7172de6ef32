#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 فاحص قاعدة بيانات أنوبيس السريع
Anubis Database Quick Checker
"""

import json
import sys
from pathlib import Path
import sqlite3

def check_database_config():
    """فحص إعدادات قاعدة البيانات"""
    print("🏺 فاحص قاعدة بيانات أنوبيس")
    print("=" * 50)
    
    # البحث عن ملف إعدادات قاعدة البيانات في مسارات متعددة
    possible_paths = [
        Path("core/anubis/configs/database_config.json"),
        Path("anubis_main_system/core/anubis/configs/database_config.json"),
        Path("configs/database_config.json")
    ]
    
    config_path = None
    for path in possible_paths:
        if path.exists():
            config_path = path
            break
    
    if not config_path:
        print("❌ ملف إعدادات قاعدة البيانات غير موجود في المسارات التالية:")
        for path in possible_paths:
            print(f"   - {path}")
        return False, None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config['database']
        mysql_config = db_config['mysql']
        sqlite_config = db_config['sqlite']
        
        print(f"📊 نوع قاعدة البيانات المفضل: {db_config['type']}")
        print(f"🖥️  خادم MySQL: {mysql_config['host']}:{mysql_config['port']}")
        print(f"👤 مستخدم MySQL: {mysql_config['user']}")
        print(f"🗄️  قاعدة بيانات MySQL: {mysql_config['database']}")
        print(f"🔐 كلمة مرور MySQL: {'✅ محددة' if mysql_config['password'] else '❌ فارغة'}")
        print(f"📁 مسار SQLite: {sqlite_config['db_path']}")
        
        return True, config
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False, None

def check_sqlite():
    """فحص SQLite"""
    print("\n🔍 فحص SQLite:")
    print("-" * 30)
    
    try:
        # إنشاء مجلد قاعدة البيانات
        db_dir = Path("database")
        db_dir.mkdir(exist_ok=True)
        
        # اختبار الاتصال
        db_path = "database/anubis.db"
        connection = sqlite3.connect(db_path)
        cursor = connection.cursor()
        
        # اختبار بسيط
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()[0]
        
        print(f"✅ SQLite متصل بنجاح")
        print(f"📋 إصدار SQLite: {version}")
        print(f"📁 مسار قاعدة البيانات: {Path(db_path).absolute()}")
        
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if tables:
            print(f"📊 الجداول الموجودة: {len(tables)}")
            for table in tables:
                print(f"   - {table[0]}")
        else:
            print("📊 لا توجد جداول (قاعدة بيانات جديدة)")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ SQLite: {e}")
        return False

def check_mysql(config):
    """فحص MySQL"""
    print("\n🔍 فحص MySQL:")
    print("-" * 30)
    
    try:
        import mysql.connector
        
        mysql_config = config['database']['mysql']
        
        # محاولة الاتصال بالخادم
        connection = mysql.connector.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password']
        )
        
        if connection.is_connected():
            print("✅ خادم MySQL متصل بنجاح")
            
            cursor = connection.cursor()
            
            # فحص إصدار MySQL
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"📋 إصدار MySQL: {version}")
            
            # فحص قواعد البيانات
            cursor.execute("SHOW DATABASES")
            databases = [db[0] for db in cursor.fetchall()]
            
            target_db = mysql_config['database']
            if target_db in databases:
                print(f"✅ قاعدة البيانات '{target_db}' موجودة")
                
                # الاتصال بقاعدة البيانات المحددة
                cursor.execute(f"USE {target_db}")
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"📊 الجداول الموجودة: {len(tables)}")
                    for table in tables:
                        print(f"   - {table[0]}")
                else:
                    print("📊 لا توجد جداول (قاعدة بيانات فارغة)")
                    
            else:
                print(f"⚠️  قاعدة البيانات '{target_db}' غير موجودة")
                print("💡 يمكن إنشاؤها تلقائياً عند أول استخدام")
            
            cursor.close()
            connection.close()
            return True
            
        else:
            print("❌ فشل الاتصال بخادم MySQL")
            return False
            
    except ImportError:
        print("❌ مكتبة mysql-connector-python غير مثبتة")
        print("💡 لتثبيتها: pip install mysql-connector-python")
        return False
    except Exception as e:
        print(f"❌ خطأ MySQL: {e}")
        return False

def check_database_files():
    """فحص ملفات قاعدة البيانات"""
    print("\n📁 فحص ملفات قاعدة البيانات:")
    print("-" * 40)
    
    # فحص مجلد database
    db_dir = Path("database")
    if db_dir.exists():
        print(f"✅ مجلد database موجود: {db_dir.absolute()}")
        
        # عرض الملفات
        files = list(db_dir.glob("*"))
        if files:
            print(f"📄 الملفات الموجودة: {len(files)}")
            for file in files:
                size = file.stat().st_size if file.is_file() else 0
                print(f"   - {file.name} ({size} bytes)")
        else:
            print("📄 المجلد فارغ")
    else:
        print("⚠️  مجلد database غير موجود")
        print("💡 سيتم إنشاؤه تلقائياً عند الحاجة")

def main():
    """الدالة الرئيسية"""
    # فحص الإعدادات
    success, config = check_database_config()
    if not success:
        return
    
    # فحص الملفات
    check_database_files()
    
    # فحص SQLite
    sqlite_ok = check_sqlite()
    
    # فحص MySQL
    mysql_ok = check_mysql(config)
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📋 ملخص الفحص:")
    print(f"   SQLite: {'✅ يعمل' if sqlite_ok else '❌ مشكلة'}")
    print(f"   MySQL: {'✅ يعمل' if mysql_ok else '❌ مشكلة'}")
    
    if sqlite_ok or mysql_ok:
        print("🎉 نظام قاعدة البيانات جاهز للاستخدام!")
    else:
        print("⚠️  يحتاج نظام قاعدة البيانات إلى إعداد")

if __name__ == "__main__":
    main()
