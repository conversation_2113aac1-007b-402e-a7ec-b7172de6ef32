#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لأداء النظام و VS Code
"""

import psutil
import time
from datetime import datetime

def test_system_performance():
    """اختبار شامل لأداء النظام"""
    
    print('🧪 اختبار شامل لأداء النظام و VS Code')
    print('=' * 60)
    print(f'⏰ وقت الاختبار: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print()

    # 1. اختبار النظام العام
    print('📊 1. اختبار النظام العام:')
    print('-' * 40)

    try:
        # إحصائيات النظام
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        print(f'⚡ المعالج: {cpu_percent:.1f}%', end='')
        if cpu_percent > 80:
            print(' 🔴 مرتفع جداً!')
        elif cpu_percent > 60:
            print(' 🟡 مرتفع')
        else:
            print(' 🟢 طبيعي')
        
        print(f'💾 الذاكرة: {memory.percent:.1f}%', end='')
        if memory.percent > 85:
            print(' 🔴 مرتفع جداً!')
        elif memory.percent > 70:
            print(' 🟡 مرتفع')
        else:
            print(' 🟢 طبيعي')
        
        print(f'💿 القرص: {disk.percent:.1f}%', end='')
        if disk.percent > 90:
            print(' 🔴 ممتلئ!')
        elif disk.percent > 80:
            print(' 🟡 يحتاج تنظيف')
        else:
            print(' 🟢 مساحة جيدة')
        
        print(f'🖥️ العمليات: {len(psutil.pids())} عملية نشطة')
        
    except Exception as e:
        print(f'❌ خطأ في اختبار النظام: {e}')

    print()

    # 2. اختبار VS Code
    print('🧩 2. اختبار VS Code:')
    print('-' * 40)

    vscode_processes = []
    total_cpu = 0
    total_memory = 0

    try:
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'cmdline']):
            try:
                if 'code' in proc.info['name'].lower():
                    cpu_usage = proc.cpu_percent(interval=0.5)
                    memory_usage = proc.memory_percent()
                    
                    proc_info = {
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu_percent': cpu_usage,
                        'memory_percent': memory_usage,
                        'cmdline': proc.info.get('cmdline', [])
                    }
                    
                    vscode_processes.append(proc_info)
                    total_cpu += cpu_usage
                    total_memory += memory_usage
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
            except Exception:
                continue
        
        if vscode_processes:
            print(f'📊 عدد العمليات: {len(vscode_processes)}')
            print(f'⚡ إجمالي المعالج: {total_cpu:.1f}%', end='')
            if total_cpu > 50:
                print(' 🔴 مرتفع جداً!')
            elif total_cpu > 25:
                print(' 🟡 مرتفع')
            else:
                print(' 🟢 طبيعي')
            
            print(f'💾 إجمالي الذاكرة: {total_memory:.1f}%', end='')
            if total_memory > 30:
                print(' 🔴 مرتفع جداً!')
            elif total_memory > 15:
                print(' 🟡 مرتفع')
            else:
                print(' 🟢 طبيعي')
            
            # أعلى 5 عمليات استهلاكاً
            vscode_processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            print()
            print('🔍 أعلى 5 عمليات استهلاكاً:')
            for i, proc in enumerate(vscode_processes[:5], 1):
                cmdline = ' '.join(proc['cmdline']) if proc['cmdline'] else ''
                
                if '--type=renderer' in cmdline:
                    proc_type = '🖼️ عارض'
                elif '--type=extensionHost' in cmdline:
                    proc_type = '🔌 إضافات'
                elif '--type=gpu-process' in cmdline:
                    proc_type = '🎮 رسوميات'
                else:
                    proc_type = '📝 رئيسي'
                
                status = '🔴' if proc['cpu_percent'] > 15 else '🟡' if proc['cpu_percent'] > 5 else '🟢'
                print(f'  {i}. {status} PID {proc["pid"]} - {proc_type} - CPU: {proc["cpu_percent"]:.1f}% MEM: {proc["memory_percent"]:.1f}%')
        else:
            print('❌ VS Code غير مفتوح حالياً')

    except Exception as e:
        print(f'❌ خطأ في اختبار VS Code: {e}')

    print()

    # 3. اختبار العمليات الثقيلة
    print('🔥 3. اختبار العمليات الثقيلة:')
    print('-' * 40)

    try:
        heavy_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                cpu = proc.cpu_percent(interval=0.1)
                memory = proc.memory_percent()
                
                if cpu > 10 or memory > 5:
                    heavy_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu': cpu,
                        'memory': memory
                    })
            except:
                continue
        
        if heavy_processes:
            heavy_processes.sort(key=lambda x: x['cpu'], reverse=True)
            print(f'⚠️ وُجد {len(heavy_processes)} عملية ثقيلة:')
            for proc in heavy_processes[:10]:
                status = '🔴' if proc['cpu'] > 20 else '🟡'
                print(f'  {status} PID {proc["pid"]} - {proc["name"]} - CPU: {proc["cpu"]:.1f}% MEM: {proc["memory"]:.1f}%')
        else:
            print('✅ لا توجد عمليات ثقيلة')

    except Exception as e:
        print(f'❌ خطأ في اختبار العمليات الثقيلة: {e}')

    print()

    # 4. تقييم الأداء العام
    print('📈 4. تقييم الأداء العام:')
    print('-' * 40)

    score = 100
    issues = []

    if cpu_percent > 80:
        score -= 30
        issues.append('🔴 المعالج مرتفع جداً')
    elif cpu_percent > 60:
        score -= 15
        issues.append('🟡 المعالج مرتفع')

    if memory.percent > 85:
        score -= 25
        issues.append('🔴 الذاكرة مرتفعة جداً')
    elif memory.percent > 70:
        score -= 10
        issues.append('🟡 الذاكرة مرتفعة')

    if vscode_processes and total_cpu > 50:
        score -= 20
        issues.append('🔴 VS Code يستهلك موارد كثيرة')
    elif vscode_processes and total_cpu > 25:
        score -= 10
        issues.append('🟡 VS Code يستهلك موارد متوسطة')

    if len(vscode_processes) > 15:
        score -= 10
        issues.append('⚠️ عدد كبير من عمليات VS Code')

    print(f'🎯 نقاط الأداء: {score}/100')

    if score >= 90:
        print('🟢 أداء ممتاز!')
    elif score >= 70:
        print('🟡 أداء جيد')
    elif score >= 50:
        print('🟠 أداء متوسط')
    else:
        print('🔴 أداء ضعيف - يحتاج تحسين')

    if issues:
        print()
        print('⚠️ المشاكل المكتشفة:')
        for issue in issues:
            print(f'  • {issue}')

    print()

    # 5. توصيات
    print('💡 5. التوصيات:')
    print('-' * 40)

    recommendations = []

    if cpu_percent > 80:
        recommendations.append('🔴 أغلق العمليات عالية استهلاك المعالج')
    if memory.percent > 85:
        recommendations.append('🔴 حرر الذاكرة وأغلق التطبيقات غير الضرورية')
    if vscode_processes and total_cpu > 50:
        recommendations.append('🔴 أعد تشغيل VS Code أو عطل الإضافات الثقيلة')
    if len(vscode_processes) > 15:
        recommendations.append('🟡 أغلق نوافذ VS Code غير المستخدمة')

    if not recommendations:
        recommendations.append('✅ النظام يعمل بكفاءة جيدة!')

    for rec in recommendations:
        print(f'  • {rec}')

    print()
    print('✅ انتهى الاختبار الشامل')
    print('=' * 60)
    
    return {
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'vscode_processes': len(vscode_processes),
        'vscode_cpu': total_cpu,
        'vscode_memory': total_memory,
        'score': score
    }

if __name__ == "__main__":
    try:
        result = test_system_performance()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الاختبار")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
