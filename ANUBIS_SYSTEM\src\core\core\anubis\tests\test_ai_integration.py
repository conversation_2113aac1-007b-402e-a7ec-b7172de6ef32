#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار دمج الذكاء الاصطناعي مع وكلاء أنوبيس
Test AI Integration with Anubis Agents

اختبار شامل لنظام دمج الذكاء الاصطناعي
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "agents"))

try:
    from anubis.core.ai_integration import AIIntegrationManager, OllamaProvider
    from anubis.core.base_agent import BaseAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)


class AITestAgent(BaseAgent):
    """وكيل اختبار للذكاء الاصطناعي"""

    def get_agent_type(self) -> str:
        return "ai_test"

    def initialize_agent(self):
        self.test_results = []
        self.log_action("تهيئة وكيل اختبار الذكاء الاصطناعي")


def test_ollama_provider():
    """اختبار موفر Ollama"""
    print("🧪 اختبار موفر Ollama...")

    # اختبار النماذج المختلفة
    models_to_test = ["llama3:8b", "mistral:7b", "phi3:mini"]

    results = {}

    for model in models_to_test:
        print(f"\n   🔍 اختبار النموذج: {model}")

        try:
            provider = OllamaProvider(model_name=model)

            # فحص التوفر
            is_available = provider.is_available()
            print(f"   📡 متاح: {'✅ نعم' if is_available else '❌ لا'}")

            if is_available:
                # اختبار بسيط
                test_prompt = "اكتب جملة واحدة عن الذكاء الاصطناعي"

                print(f"   📤 إرسال: {test_prompt}")

                response = provider.generate_response(test_prompt)

                if response and not response.startswith("خطأ"):
                    print(f"   📥 الاستجابة: {response[:100]}...")
                    results[model] = {
                        "available": True,
                        "working": True,
                        "response_length": len(response),
                        "response_preview": response[:200],
                    }
                else:
                    print(f"   ❌ فشل في الحصول على استجابة: {response}")
                    results[model] = {
                        "available": True,
                        "working": False,
                        "error": response,
                    }
            else:
                results[model] = {"available": False, "working": False}

        except Exception as e:
            print(f"   ❌ خطأ في اختبار {model}: {e}")
            results[model] = {"available": False, "working": False, "error": str(e)}

    return results


def test_ai_manager():
    """اختبار مدير الذكاء الاصطناعي"""
    print("\n🧠 اختبار مدير الذكاء الاصطناعي...")

    try:
        # إنشاء مدير الذكاء الاصطناعي
        ai_manager = AIIntegrationManager()

        # فحص الحالة
        status = ai_manager.get_status()
        print(f"   📊 الحالة: {json.dumps(status, ensure_ascii=False, indent=2)}")

        # فحص الموفرين المتاحين
        available_providers = ai_manager.get_available_providers()
        print(f"   🔌 الموفرين المتاحين: {available_providers}")

        # اختبار توليد استجابة
        if available_providers:
            test_prompt = "ما هي أفضل الممارسات في البرمجة؟"
            print(f"   📤 اختبار التوليد: {test_prompt}")

            response = ai_manager.generate_ai_response(test_prompt)
            print(f"   📥 الاستجابة: {response[:150]}...")

            return {
                "manager_working": True,
                "available_providers": available_providers,
                "response_test": True,
                "response_length": len(response),
            }
        else:
            print("   ⚠️ لا توجد موفرين متاحين")
            return {
                "manager_working": True,
                "available_providers": [],
                "response_test": False,
            }

    except Exception as e:
        print(f"   ❌ خطأ في مدير الذكاء الاصطناعي: {e}")
        return {"manager_working": False, "error": str(e)}


def test_agent_ai_integration():
    """اختبار دمج الذكاء الاصطناعي مع الوكلاء"""
    print("\n🤖 اختبار دمج الذكاء الاصطناعي مع الوكلاء...")

    try:
        # إنشاء وكيل اختبار
        agent = AITestAgent(project_path=".", config={"test_mode": True}, verbose=True)

        # فحص حالة الذكاء الاصطناعي في الوكيل
        ai_status = agent.get_ai_status()
        print(f"   📊 حالة الذكاء الاصطناعي في الوكيل: {ai_status['available']}")

        if agent.is_ai_enabled():
            # اختبار التحليل الذكي
            test_context = {
                "project_type": "Python",
                "files_count": 50,
                "main_language": "Python",
            }

            analysis_prompt = "حلل هذا المشروع واقترح تحسينات"
            print(f"   📤 اختبار التحليل: {analysis_prompt}")

            analysis = agent.get_ai_analysis(analysis_prompt, test_context)
            print(f"   📥 التحليل: {analysis[:150]}...")

            # اختبار الاقتراحات الذكية
            test_data = {"code_quality": 75, "test_coverage": 60, "documentation": 40}

            suggestions = agent.get_smart_suggestions(test_data)
            print(f"   💡 الاقتراحات ({len(suggestions)}):")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"      {i}. {suggestion}")

            return {
                "agent_ai_enabled": True,
                "analysis_working": len(analysis) > 50,
                "suggestions_count": len(suggestions),
                "integration_successful": True,
            }
        else:
            print("   ⚠️ الذكاء الاصطناعي غير مفعل في الوكيل")
            return {"agent_ai_enabled": False, "integration_successful": False}

    except Exception as e:
        print(f"   ❌ خطأ في اختبار دمج الوكيل: {e}")
        return {
            "agent_ai_enabled": False,
            "integration_successful": False,
            "error": str(e),
        }


def test_specific_use_cases():
    """اختبار حالات استخدام محددة"""
    print("\n🎯 اختبار حالات الاستخدام المحددة...")

    test_cases = [
        {
            "name": "تحليل كود Python",
            "prompt": 'حلل هذا الكود Python وقدم اقتراحات:\n\ndef hello():\n    print("Hello World")',
            "expected_keywords": ["function", "print", "python"],
        },
        {
            "name": "اقتراحات تحسين المشروع",
            "prompt": "مشروع Python يحتوي على 100 ملف، ما هي اقتراحاتك لتحسينه؟",
            "expected_keywords": ["testing", "documentation", "structure"],
        },
        {
            "name": "نصائح الأمان",
            "prompt": "ما هي أهم نصائح الأمان في تطوير تطبيقات الويب؟",
            "expected_keywords": ["security", "validation", "authentication"],
        },
    ]

    results = {}

    try:
        ai_manager = AIIntegrationManager()

        for test_case in test_cases:
            print(f"\n   🧪 {test_case['name']}")
            print(f"   📤 {test_case['prompt'][:50]}...")

            response = ai_manager.generate_ai_response(test_case["prompt"])

            if response and not response.startswith("خطأ"):
                # فحص وجود الكلمات المتوقعة
                response_lower = response.lower()
                found_keywords = [
                    keyword
                    for keyword in test_case["expected_keywords"]
                    if keyword.lower() in response_lower
                ]

                results[test_case["name"]] = {
                    "success": True,
                    "response_length": len(response),
                    "keywords_found": found_keywords,
                    "relevance_score": len(found_keywords) / len(test_case["expected_keywords"]),
                }

                print(f"   📥 نجح ({len(response)} حرف)")
                print(f"   🎯 الصلة: {len(found_keywords)}/{len(test_case['expected_keywords'])}")
            else:
                results[test_case["name"]] = {"success": False, "error": response}
                print(f"   ❌ فشل: {response}")

    except Exception as e:
        print(f"   ❌ خطأ في اختبار حالات الاستخدام: {e}")
        results["error"] = str(e)

    return results


def generate_test_report(ollama_results, manager_results, agent_results, use_case_results):
    """إنتاج تقرير شامل للاختبارات"""

    report = {
        "timestamp": datetime.now().isoformat(),
        "test_summary": {
            "ollama_models_tested": len(ollama_results),
            "working_models": len([r for r in ollama_results.values() if r.get("working", False)]),
            "ai_manager_working": manager_results.get("manager_working", False),
            "agent_integration_working": agent_results.get("integration_successful", False),
            "use_cases_tested": len(use_case_results),
            "successful_use_cases": len(
                [r for r in use_case_results.values() if r.get("success", False)]
            ),
        },
        "detailed_results": {
            "ollama_providers": ollama_results,
            "ai_manager": manager_results,
            "agent_integration": agent_results,
            "use_cases": use_case_results,
        },
        "recommendations": [],
    }

    # إضافة توصيات
    if report["test_summary"]["working_models"] > 0:
        report["recommendations"].append(
            "✅ نماذج Ollama تعمل بشكل جيد - يمكن استخدام الذكاء الاصطناعي"
        )
    else:
        report["recommendations"].append("❌ لا توجد نماذج Ollama تعمل - تحقق من التثبيت")

    if manager_results.get("manager_working", False):
        report["recommendations"].append("✅ مدير الذكاء الاصطناعي يعمل بشكل صحيح")
    else:
        report["recommendations"].append("❌ مدير الذكاء الاصطناعي لا يعمل - تحقق من الإعدادات")

    if agent_results.get("integration_successful", False):
        report["recommendations"].append("✅ دمج الذكاء الاصطناعي مع الوكلاء ناجح")
    else:
        report["recommendations"].append("❌ دمج الذكاء الاصطناعي مع الوكلاء فاشل")

    # حفظ التقرير
    report_file = f"ai_integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n📄 تم حفظ تقرير الاختبار في: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")

    return report


def main():
    """الدالة الرئيسية"""
    print("🏺 اختبار دمج الذكاء الاصطناعي - نظام أنوبيس")
    print("=" * 60)

    # اختبار موفر Ollama
    ollama_results = test_ollama_provider()

    # اختبار مدير الذكاء الاصطناعي
    manager_results = test_ai_manager()

    # اختبار دمج الوكلاء
    agent_results = test_agent_ai_integration()

    # اختبار حالات الاستخدام
    use_case_results = test_specific_use_cases()

    # إنتاج التقرير
    report = generate_test_report(ollama_results, manager_results, agent_results, use_case_results)

    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   🤖 نماذج Ollama المختبرة: {report['test_summary']['ollama_models_tested']}")
    print(f"   ✅ النماذج التي تعمل: {report['test_summary']['working_models']}")
    print(
        f"   🧠 مدير الذكاء الاصطناعي: {'✅ يعمل' if report['test_summary']['ai_manager_working'] else '❌ لا يعمل'}"
    )
    print(
        f"   🔗 دمج الوكلاء: {'✅ ناجح' if report['test_summary']['agent_integration_working'] else '❌ فاشل'}"
    )
    print(
        f"   🎯 حالات الاستخدام الناجحة: {report['test_summary']['successful_use_cases']}/{report['test_summary']['use_cases_tested']}"
    )

    print("\n💡 التوصيات:")
    for recommendation in report["recommendations"]:
        print(f"   {recommendation}")

    print("\n🏺 انتهى اختبار دمج الذكاء الاصطناعي!")

    return 0 if report["test_summary"]["working_models"] > 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
