#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚙️ فاحص مجلدي configs + database الشامل
Configs and Database Comprehensive Inspector

يتضمن استراتيجية فحص متقدمة بناءً على أفضل الممارسات
(محاكاة توصيات Gemini CLI)
"""

import os
import json
import sqlite3
from pathlib import Path
from datetime import datetime

class ConfigsDatabaseInspector:
    def __init__(self):
        self.configs_path = Path("configs")
        self.database_path = Path("database")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "inspector": "Configs and Database Inspector",
            "inspection_type": "comprehensive_analysis_with_gemini_strategy",
            "overall_health": "unknown",
            "components": {},
            "security_analysis": {},
            "performance_metrics": {},
            "gemini_recommendations": [],
            "critical_issues": [],
            "next_steps": []
        }
        
        # استراتيجية الفحص بناءً على محاكاة توصيات Gemini CLI
        self.gemini_strategy = {
            "config_priorities": [
                "validate_json_structure",
                "check_security_settings", 
                "verify_ai_integrations",
                "analyze_database_configs",
                "test_configuration_completeness"
            ],
            "database_priorities": [
                "check_database_integrity",
                "analyze_schema_structure",
                "verify_data_consistency",
                "performance_analysis",
                "security_assessment"
            ],
            "critical_checks": [
                "api_keys_security",
                "database_connection_settings",
                "ai_model_configurations",
                "backup_and_recovery_settings"
            ]
        }
    
    def analyze_configs_structure(self):
        """تحليل هيكل مجلد التكوينات (استراتيجية Gemini)"""
        print("⚙️ تحليل هيكل مجلد التكوينات...")
        print("🤖 تطبيق استراتيجية Gemini للفحص الشامل...")
        
        component_health = {
            "status": "analyzing",
            "config_files": {},
            "json_validation": {},
            "security_score": 0,
            "completeness_score": 0,
            "issues": [],
            "strengths": [],
            "gemini_insights": []
        }
        
        if not self.configs_path.exists():
            component_health["issues"].append("❌ مجلد configs/ مفقود تماماً")
            component_health["gemini_insights"].append("🚨 Gemini: هذا خطأ حرج - النظام يحتاج ملفات التكوين للعمل")
            self.report["components"]["configs"] = component_health
            return component_health
        
        # فحص ملفات التكوين الأساسية (توصية Gemini)
        essential_configs = {
            "ai_config.json": {
                "description": "تكوين الذكاء الاصطناعي",
                "required_keys": ["default_provider", "providers", "agent_prompts"],
                "critical": True
            },
            "default_config.json": {
                "description": "التكوين الافتراضي",
                "required_keys": ["project", "paths", "settings"],
                "critical": True
            },
            "database_config.json": {
                "description": "تكوين قاعدة البيانات",
                "required_keys": ["database", "system"],
                "critical": True
            },
            "langsmith_config.json": {
                "description": "تكوين LangSmith",
                "required_keys": ["langsmith"],
                "critical": False
            }
        }
        
        # فحص كل ملف تكوين
        for config_file, config_info in essential_configs.items():
            config_path = self.configs_path / config_file
            file_analysis = self._analyze_config_file(config_path, config_info)
            component_health["config_files"][config_file] = file_analysis
            
            # تطبيق منطق Gemini للتقييم
            if file_analysis["exists"]:
                if file_analysis["valid_json"]:
                    component_health["strengths"].append(f"✅ {config_file} صحيح ومُحمّل")
                    component_health["completeness_score"] += 25
                    
                    # فحص الأمان (توصية Gemini)
                    security_issues = self._check_security(config_path, file_analysis["content"])
                    if not security_issues:
                        component_health["security_score"] += 20
                        component_health["gemini_insights"].append(f"🔒 Gemini: {config_file} آمن")
                    else:
                        component_health["issues"].extend(security_issues)
                        component_health["gemini_insights"].append(f"⚠️ Gemini: مشاكل أمان في {config_file}")
                else:
                    component_health["issues"].append(f"❌ {config_file} - JSON غير صحيح")
                    component_health["gemini_insights"].append(f"🔧 Gemini: إصلاح {config_file} أولوية عالية")
            else:
                if config_info["critical"]:
                    component_health["issues"].append(f"🚨 {config_file} مفقود (حرج)")
                    component_health["gemini_insights"].append(f"🚨 Gemini: {config_file} ضروري لعمل النظام")
                else:
                    component_health["issues"].append(f"⚠️ {config_file} مفقود (اختياري)")
        
        # تقييم Gemini للحالة العامة
        if component_health["completeness_score"] >= 75 and component_health["security_score"] >= 60:
            component_health["status"] = "excellent"
            component_health["gemini_insights"].append("🎯 Gemini: التكوينات في حالة ممتازة")
        elif component_health["completeness_score"] >= 50:
            component_health["status"] = "good"
            component_health["gemini_insights"].append("✅ Gemini: التكوينات جيدة مع تحسينات بسيطة")
        else:
            component_health["status"] = "needs_work"
            component_health["gemini_insights"].append("🔧 Gemini: التكوينات تحتاج عمل كبير")
        
        self.report["components"]["configs"] = component_health
        return component_health
    
    def _analyze_config_file(self, config_path, config_info):
        """تحليل مفصل لملف تكوين واحد"""
        analysis = {
            "exists": config_path.exists(),
            "valid_json": False,
            "content": None,
            "size": 0,
            "required_keys_present": [],
            "missing_keys": [],
            "extra_insights": []
        }
        
        if analysis["exists"]:
            analysis["size"] = config_path.stat().st_size
            
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                analysis["valid_json"] = True
                analysis["content"] = content
                
                # فحص المفاتيح المطلوبة
                for key in config_info["required_keys"]:
                    if key in content:
                        analysis["required_keys_present"].append(key)
                    else:
                        analysis["missing_keys"].append(key)
                
                # تحليل خاص لكل نوع ملف
                if config_path.name == "ai_config.json":
                    analysis["extra_insights"] = self._analyze_ai_config(content)
                elif config_path.name == "database_config.json":
                    analysis["extra_insights"] = self._analyze_database_config(content)
                    
            except json.JSONDecodeError as e:
                analysis["json_error"] = str(e)
            except Exception as e:
                analysis["error"] = str(e)
        
        return analysis
    
    def _analyze_ai_config(self, content):
        """تحليل خاص لملف ai_config.json"""
        insights = []
        
        if "providers" in content:
            providers = content["providers"]
            active_providers = [p for p, config in providers.items() if config.get("enabled", False)]
            insights.append(f"🤖 {len(active_providers)} مزودي AI نشطين: {', '.join(active_providers)}")
            
            # فحص إعدادات Ollama
            if "ollama" in providers and providers["ollama"].get("enabled"):
                insights.append("🦙 Ollama مُفعل (محلي ومجاني)")
            
            # فحص إعدادات Gemini
            if "gemini" in providers:
                if providers["gemini"].get("api_key"):
                    insights.append("💎 Gemini مُعد مع API key")
                else:
                    insights.append("⚠️ Gemini بدون API key")
        
        if "agent_prompts" in content:
            prompts_count = len(content["agent_prompts"])
            insights.append(f"📝 {prompts_count} قوالب prompts للوكلاء")
        
        return insights
    
    def _analyze_database_config(self, content):
        """تحليل خاص لملف database_config.json"""
        insights = []
        
        if "database" in content:
            db_config = content["database"]
            db_type = db_config.get("type", "unknown")
            insights.append(f"🗄️ نوع قاعدة البيانات: {db_type}")
            
            if "mysql" in db_config:
                mysql_config = db_config["mysql"]
                host = mysql_config.get("host", "unknown")
                port = mysql_config.get("port", "unknown")
                database = mysql_config.get("database", "unknown")
                insights.append(f"🐬 MySQL: {host}:{port}/{database}")
                
                if mysql_config.get("password"):
                    insights.append("🔐 كلمة مرور MySQL محددة")
                else:
                    insights.append("⚠️ كلمة مرور MySQL فارغة")
        
        return insights
    
    def _check_security(self, config_path, content):
        """فحص الأمان (توصية Gemini)"""
        security_issues = []
        
        # فحص API keys مكشوفة
        content_str = json.dumps(content, default=str).lower()
        if "password" in content_str and content_str.count("password") > 0:
            # فحص أعمق للكلمات المرور
            if isinstance(content, dict):
                self._deep_password_check(content, security_issues, config_path.name)
        
        # فحص مفاتيح API فارغة
        if "api_key" in content_str and "\"api_key\": \"\"" in content_str:
            security_issues.append(f"⚠️ {config_path.name}: مفاتيح API فارغة")
        
        return security_issues
    
    def _deep_password_check(self, data, issues, filename):
        """فحص عميق للكلمات المرور"""
        if isinstance(data, dict):
            for key, value in data.items():
                if "password" in key.lower() and value:
                    # كلمة مرور موجودة - قد تكون مشكلة أمان
                    if len(str(value)) < 8:
                        issues.append(f"🔐 {filename}: كلمة مرور ضعيفة في {key}")
                elif isinstance(value, dict):
                    self._deep_password_check(value, issues, filename)
    
    def analyze_database_system(self):
        """تحليل نظام قاعدة البيانات (استراتيجية Gemini)"""
        print("🗄️ تحليل نظام قاعدة البيانات...")
        print("🤖 تطبيق استراتيجية Gemini لفحص قاعدة البيانات...")
        
        component_health = {
            "status": "analyzing",
            "database_files": {},
            "integrity_score": 0,
            "performance_score": 0,
            "schema_analysis": {},
            "issues": [],
            "strengths": [],
            "gemini_insights": []
        }
        
        if not self.database_path.exists():
            component_health["issues"].append("❌ مجلد database/ مفقود")
            component_health["gemini_insights"].append("🚨 Gemini: قاعدة البيانات مفقودة - النظام لن يعمل")
            self.report["components"]["database"] = component_health
            return component_health
        
        # فحص ملفات قاعدة البيانات
        db_files = list(self.database_path.glob("*.db"))
        
        if not db_files:
            component_health["issues"].append("❌ لا توجد ملفات قاعدة بيانات")
            component_health["gemini_insights"].append("🔧 Gemini: إنشاء قاعدة البيانات مطلوب")
            self.report["components"]["database"] = component_health
            return component_health
        
        # تحليل كل ملف قاعدة بيانات
        for db_file in db_files:
            db_analysis = self._analyze_database_file(db_file)
            component_health["database_files"][db_file.name] = db_analysis
            
            if db_analysis["accessible"]:
                component_health["strengths"].append(f"✅ {db_file.name} قابل للوصول")
                component_health["integrity_score"] += 30
                
                # تحليل الأداء (توصية Gemini)
                perf_metrics = self._analyze_database_performance(db_file)
                component_health["performance_score"] += perf_metrics["score"]
                component_health["gemini_insights"].extend(perf_metrics["insights"])
                
                # تحليل المخطط (Schema)
                schema_analysis = self._analyze_database_schema(db_file)
                component_health["schema_analysis"][db_file.name] = schema_analysis
                
            else:
                component_health["issues"].append(f"❌ {db_file.name} غير قابل للوصول")
                component_health["gemini_insights"].append(f"🔧 Gemini: إصلاح {db_file.name} ضروري")
        
        # تقييم Gemini النهائي
        total_score = component_health["integrity_score"] + component_health["performance_score"]
        if total_score >= 80:
            component_health["status"] = "excellent"
            component_health["gemini_insights"].append("🎯 Gemini: قاعدة البيانات في حالة ممتازة")
        elif total_score >= 50:
            component_health["status"] = "good"
            component_health["gemini_insights"].append("✅ Gemini: قاعدة البيانات جيدة")
        else:
            component_health["status"] = "needs_work"
            component_health["gemini_insights"].append("🔧 Gemini: قاعدة البيانات تحتاج تحسين")
        
        self.report["components"]["database"] = component_health
        return component_health
    
    def _analyze_database_file(self, db_file):
        """تحليل مفصل لملف قاعدة بيانات"""
        analysis = {
            "size": db_file.stat().st_size,
            "size_mb": round(db_file.stat().st_size / (1024*1024), 3),
            "accessible": False,
            "tables": [],
            "record_counts": {},
            "last_modified": datetime.fromtimestamp(db_file.stat().st_mtime).isoformat()
        }
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            analysis["tables"] = [table[0] for table in tables]
            analysis["accessible"] = True
            
            # عد السجلات في كل جدول
            for table_name in analysis["tables"]:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    analysis["record_counts"][table_name] = count
                except:
                    analysis["record_counts"][table_name] = "error"
            
            conn.close()
            
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def _analyze_database_performance(self, db_file):
        """تحليل أداء قاعدة البيانات (توصية Gemini)"""
        metrics = {
            "score": 0,
            "insights": []
        }
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # فحص سرعة الاستعلام
            start_time = datetime.now()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master")
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            if query_time < 0.001:
                metrics["score"] += 20
                metrics["insights"].append(f"⚡ Gemini: أداء ممتاز ({query_time:.4f}s)")
            elif query_time < 0.01:
                metrics["score"] += 15
                metrics["insights"].append(f"✅ Gemini: أداء جيد ({query_time:.4f}s)")
            else:
                metrics["score"] += 5
                metrics["insights"].append(f"⚠️ Gemini: أداء بطيء ({query_time:.4f}s)")
            
            # فحص حجم قاعدة البيانات
            size_mb = db_file.stat().st_size / (1024*1024)
            if size_mb < 10:
                metrics["insights"].append(f"📊 Gemini: حجم معقول ({size_mb:.2f} MB)")
            elif size_mb < 100:
                metrics["insights"].append(f"📈 Gemini: حجم متوسط ({size_mb:.2f} MB)")
            else:
                metrics["insights"].append(f"📦 Gemini: حجم كبير ({size_mb:.2f} MB)")
            
            conn.close()
            
        except Exception as e:
            metrics["insights"].append(f"❌ Gemini: خطأ في تحليل الأداء: {e}")
        
        return metrics
    
    def _analyze_database_schema(self, db_file):
        """تحليل مخطط قاعدة البيانات"""
        schema = {
            "tables_info": {},
            "relationships": [],
            "indexes": []
        }
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # تحليل كل جدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            for table_name, in tables:
                # معلومات الأعمدة
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                schema["tables_info"][table_name] = {
                    "columns": len(columns),
                    "column_details": [
                        {
                            "name": col[1],
                            "type": col[2],
                            "not_null": bool(col[3]),
                            "primary_key": bool(col[5])
                        } for col in columns
                    ]
                }
            
            conn.close()
            
        except Exception as e:
            schema["error"] = str(e)
        
        return schema
    
    def generate_gemini_recommendations(self):
        """إنشاء توصيات بناءً على استراتيجية Gemini"""
        print("🤖 إنشاء توصيات Gemini...")
        
        recommendations = []
        
        # تحليل التكوينات
        configs_component = self.report["components"].get("configs", {})
        if configs_component.get("completeness_score", 0) < 75:
            recommendations.append("🔧 Gemini: إكمال ملفات التكوين المفقودة - أولوية عالية")
        
        if configs_component.get("security_score", 0) < 60:
            recommendations.append("🔒 Gemini: تحسين الأمان في ملفات التكوين")
        
        # تحليل قاعدة البيانات
        database_component = self.report["components"].get("database", {})
        if database_component.get("integrity_score", 0) < 50:
            recommendations.append("🗄️ Gemini: إصلاح مشاكل قاعدة البيانات الحرجة")
        
        if database_component.get("performance_score", 0) < 30:
            recommendations.append("⚡ Gemini: تحسين أداء قاعدة البيانات")
        
        # توصيات عامة من Gemini
        recommendations.extend([
            "📋 Gemini: إنشاء نسخ احتياطية دورية للتكوينات",
            "🔄 Gemini: مراقبة دورية لحالة النظام",
            "📊 Gemini: إعداد تنبيهات للمشاكل الحرجة",
            "🛡️ Gemini: مراجعة إعدادات الأمان بانتظام"
        ])
        
        self.report["gemini_recommendations"] = recommendations
        return recommendations
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة (منطق Gemini)"""
        print("🏥 تقييم الحالة العامة بمنطق Gemini...")
        
        configs_status = self.report["components"].get("configs", {}).get("status", "unknown")
        database_status = self.report["components"].get("database", {}).get("status", "unknown")
        
        status_scores = {
            "excellent": 3,
            "good": 2,
            "needs_work": 1,
            "unknown": 0
        }
        
        total_score = status_scores.get(configs_status, 0) + status_scores.get(database_status, 0)
        
        if total_score >= 5:
            self.report["overall_health"] = "excellent"
            health_text = "ممتاز"
            emoji = "🟢"
        elif total_score >= 3:
            self.report["overall_health"] = "good" 
            health_text = "جيد"
            emoji = "🟡"
        elif total_score >= 2:
            self.report["overall_health"] = "fair"
            health_text = "متوسط"
            emoji = "🟠"
        else:
            self.report["overall_health"] = "poor"
            health_text = "يحتاج عمل"
            emoji = "🔴"
        
        print(f"\n{emoji} الحالة العامة: {health_text}")
        
        return self.report["overall_health"]
    
    def run_inspection(self):
        """تشغيل الفحص الشامل مع استراتيجية Gemini"""
        print("⚙️🗄️ بدء فحص configs + database مع استراتيجية Gemini")
        print("=" * 60)
        
        # تشغيل الفحوصات
        self.analyze_configs_structure()
        self.analyze_database_system()
        
        # تطبيق تحليل Gemini
        self.generate_gemini_recommendations()
        self.evaluate_overall_health()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("📋 تقرير فحص configs + database (بتوصيات Gemini)")
        print("="*60)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "fair": "🟠",
            "poor": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']}")
        
        # تفاصيل المكونات
        print(f"\n🔍 تفاصيل المكونات:")
        for name, component in self.report["components"].items():
            print(f"\n🔹 {name.upper()}:")
            
            if "strengths" in component:
                for strength in component["strengths"]:
                    print(f"   {strength}")
            
            if "issues" in component:
                for issue in component["issues"]:
                    print(f"   {issue}")
            
            if "gemini_insights" in component:
                print(f"   🤖 رؤى Gemini:")
                for insight in component["gemini_insights"]:
                    print(f"      {insight}")
        
        # توصيات Gemini
        print(f"\n🤖 توصيات Gemini:")
        for rec in self.report.get("gemini_recommendations", []):
            print(f"   {rec}")
        
        print("\n" + "="*60)
        print("⚙️🗄️ انتهى فحص configs + database")
        print("="*60)
    
    def save_report(self):
        """حفظ التقرير في ملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"configs_database_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    inspector = ConfigsDatabaseInspector()
    
    # تشغيل الفحص
    report = inspector.run_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    return report

if __name__ == "__main__":
    main()
