#!/usr/bin/env python3
"""
فحص سريع لجاهزية التطبيق
"""

print("🔍 فحص جاهزية واجهة التحكم...")
print("=" * 40)

# فحص Python
import sys

print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

# فحص المكتبات
try:
    import tkinter

    print("✅ tkinter متاح")
except ImportError:
    print("❌ tkinter غير متاح")

try:
    import psutil

    print(f"✅ psutil متاح (الإصدار: {psutil.__version__})")
except ImportError:
    print("❌ psutil غير متاح - تثبيت: pip install psutil")

# فحص الملفات
import os

files_to_check = [
    "process_control_dashboard.py",
    "dashboard_config.json",
    "run_dashboard.bat",
]

print("\n📁 فحص الملفات:")
for file in files_to_check:
    if os.path.exists(file):
        print(f"✅ {file}")
    else:
        print(f"❌ {file} غير موجود")

print("\n🎛️ التطبيق جاهز للاستخدام!")
print("\n🚀 للتشغيل:")
print("   - Windows: انقر مرتين على run_dashboard.bat")
print("   - أو: python process_control_dashboard.py")

input("\nاضغط Enter للخروج...")
