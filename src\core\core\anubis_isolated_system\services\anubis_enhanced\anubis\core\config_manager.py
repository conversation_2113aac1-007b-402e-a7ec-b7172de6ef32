#!/usr/bin/env python3
"""
⚙️ مدير التكوين لنظام المساعدين الذكيين العالمي
Configuration Manager for Universal AI Assistants
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional


class ConfigManager:
    """مدير التكوين والإعدادات"""

    def __init__(self, config_file: str = "configs/default_config.json"):
        """
        تهيئة مدير التكوين

        Args:
            config_file: مسار ملف التكوين
        """
        self.config_file = Path(config_file)
        self.config_dir = self.config_file.parent
        self.config = {}

        # إنشاء مجلد التكوين
        self.config_dir.mkdir(exist_ok=True)

        # إنشاء التكوين الافتراضي إذا لم يكن موجود
        if not self.config_file.exists():
            self.create_default_config()

    def create_default_config(self):
        """إنشاء ملف التكوين الافتراضي"""
        default_config = {
            "system": {
                "name": "Universal AI Assistants",
                "version": "1.0.0",
                "language": "ar",
                "encoding": "utf-8",
                "created_date": datetime.now().isoformat(),
            },
            "project": {
                "auto_detect_type": True,
                "supported_types": [
                    "streamlit",
                    "django",
                    "fastapi",
                    "flask",
                    "custom",
                ],
                "default_type": "custom",
            },
            "agents": {
                "database_agent": {
                    "enabled": True,
                    "auto_backup": True,
                    "check_performance": True,
                    "check_security": True,
                    "supported_databases": ["mysql", "postgresql", "sqlite"],
                },
                "file_organizer": {
                    "enabled": True,
                    "auto_organize": False,
                    "backup_before_organize": True,
                    "organization_rules": {
                        "core_files": {
                            "target_dir": "",
                            "patterns": [
                                "main.py",
                                "app.py",
                                "requirements.txt",
                                "README.md",
                            ],
                        },
                        "pages": {
                            "target_dir": "pages",
                            "patterns": ["*page*.py", "*view*.py"],
                            "extensions": [".py"],
                        },
                        "database": {
                            "target_dir": "database",
                            "patterns": ["*db*.py", "*database*.py", "*model*.py"],
                            "extensions": [".py", ".sql"],
                        },
                        "static": {
                            "target_dir": "static",
                            "patterns": ["*.css", "*.js", "*.html"],
                            "extensions": [
                                ".css",
                                ".js",
                                ".html",
                                ".png",
                                ".jpg",
                                ".svg",
                            ],
                        },
                        "data": {
                            "target_dir": "data",
                            "patterns": ["*.csv", "*.json", "*.xlsx"],
                            "extensions": [".csv", ".json", ".xlsx", ".xml"],
                        },
                        "docs": {
                            "target_dir": "docs",
                            "patterns": ["*.md", "*.txt", "*.pdf"],
                            "extensions": [".md", ".txt", ".pdf", ".doc", ".docx"],
                        },
                        "tests": {
                            "target_dir": "tests",
                            "patterns": ["test_*.py", "*_test.py"],
                            "extensions": [".py"],
                        },
                        "config": {
                            "target_dir": "config",
                            "patterns": ["*.json", "*.yaml", "*.yml", "*.ini"],
                            "extensions": [".json", ".yaml", ".yml", ".ini", ".cfg"],
                        },
                    },
                },
                "memory_agent": {
                    "enabled": True,
                    "auto_save": True,
                    "memory_retention_days": 30,
                    "max_memory_size_mb": 100,
                },
                "error_detector": {
                    "enabled": True,
                    "check_syntax": True,
                    "check_imports": True,
                    "check_style": True,
                    "auto_fix": False,
                },
                "project_analyzer": {
                    "enabled": True,
                    "deep_analysis": True,
                    "performance_check": True,
                    "security_scan": True,
                },
            },
            "workspace": {
                "base_dir": "workspace",
                "subdirs": {
                    "logs": "logs",
                    "reports": "reports",
                    "backups": "backups",
                    "memory": "shared_memory",
                    "collaboration": "collaboration_logs",
                },
                "auto_cleanup": True,
                "max_log_size_mb": 50,
            },
            "database": {
                "default_type": "sqlite",
                "connection_timeout": 30,
                "backup_frequency": "daily",
                "security_checks": True,
            },
            "logging": {
                "level": "INFO",
                "format": "[%(asctime)s] %(name)s: %(message)s",
                "max_file_size_mb": 10,
                "backup_count": 5,
            },
            "plugins": {"enabled": True, "auto_load": True, "plugin_dir": "plugins"},
        }

        self.save_config(default_config)
        print(f"✅ تم إنشاء ملف التكوين الافتراضي: {self.config_file}")

    def load_config(self) -> Dict[str, Any]:
        """تحميل التكوين من الملف"""
        try:
            with open(self.config_file, "r", encoding="utf-8") as f:
                self.config = json.load(f)

            # التحقق من صحة التكوين
            self.validate_config()

            return self.config

        except FileNotFoundError:
            print(f"⚠️ ملف التكوين غير موجود: {self.config_file}")
            self.create_default_config()
            return self.load_config()

        except json.JSONDecodeError as e:
            print(f"❌ خطأ في تحليل ملف التكوين: {e}")
            raise

        except Exception as e:
            print(f"❌ خطأ في تحميل التكوين: {e}")
            raise

    def save_config(self, config: Dict[str, Any] = None):
        """حفظ التكوين في الملف"""
        if config is not None:
            self.config = config

        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ خطأ في حفظ التكوين: {e}")
            raise

    def validate_config(self):
        """التحقق من صحة التكوين"""
        required_sections = ["system", "agents", "workspace"]

        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"قسم مطلوب مفقود في التكوين: {section}")

        # التحقق من الوكلاء
        if "agents" in self.config:
            for agent_name, agent_config in self.config["agents"].items():
                if not isinstance(agent_config, dict):
                    raise ValueError(f"تكوين الوكيل {agent_name} يجب أن يكون dictionary")

                if "enabled" not in agent_config:
                    self.config["agents"][agent_name]["enabled"] = True

    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """الحصول على تكوين وكيل محدد"""
        if "agents" not in self.config:
            return {}

        return self.config["agents"].get(agent_name, {})

    def is_agent_enabled(self, agent_name: str) -> bool:
        """التحقق من تفعيل وكيل محدد"""
        agent_config = self.get_agent_config(agent_name)
        return agent_config.get("enabled", False)

    def get_workspace_config(self) -> Dict[str, Any]:
        """الحصول على تكوين مساحة العمل"""
        return self.config.get("workspace", {})

    def get_database_config(self) -> Dict[str, Any]:
        """الحصول على تكوين قاعدة البيانات"""
        return self.config.get("database", {})

    def update_agent_config(self, agent_name: str, new_config: Dict[str, Any]):
        """تحديث تكوين وكيل محدد"""
        if "agents" not in self.config:
            self.config["agents"] = {}

        if agent_name not in self.config["agents"]:
            self.config["agents"][agent_name] = {}

        self.config["agents"][agent_name].update(new_config)
        self.save_config()

    def enable_agent(self, agent_name: str):
        """تفعيل وكيل محدد"""
        self.update_agent_config(agent_name, {"enabled": True})

    def disable_agent(self, agent_name: str):
        """إلغاء تفعيل وكيل محدد"""
        self.update_agent_config(agent_name, {"enabled": False})

    def get_project_templates(self) -> Dict[str, Any]:
        """الحصول على قوالب المشاريع"""
        templates_file = self.config_dir / "project_templates.json"

        if not templates_file.exists():
            self.create_project_templates()

        try:
            with open(templates_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ خطأ في تحميل قوالب المشاريع: {e}")
            return {}

    def create_project_templates(self):
        """إنشاء قوالب المشاريع"""
        templates = {
            "streamlit": {
                "name": "Streamlit Application",
                "description": "تطبيق Streamlit للواجهات التفاعلية",
                "required_files": ["app.py", "requirements.txt"],
                "optional_files": ["pages/", "data/", "config/"],
                "database_support": True,
                "agents": ["database_agent", "file_organizer", "memory_agent"],
            },
            "django": {
                "name": "Django Web Application",
                "description": "تطبيق ويب Django",
                "required_files": ["manage.py", "settings.py"],
                "optional_files": ["models.py", "views.py", "urls.py"],
                "database_support": True,
                "agents": ["database_agent", "file_organizer", "error_detector"],
            },
            "fastapi": {
                "name": "FastAPI Application",
                "description": "تطبيق FastAPI للـ APIs",
                "required_files": ["main.py", "requirements.txt"],
                "optional_files": ["models/", "routers/", "database/"],
                "database_support": True,
                "agents": ["database_agent", "project_analyzer", "error_detector"],
            },
            "custom": {
                "name": "Custom Python Project",
                "description": "مشروع Python مخصص",
                "required_files": ["main.py"],
                "optional_files": ["requirements.txt", "README.md"],
                "database_support": False,
                "agents": ["file_organizer", "memory_agent", "error_detector"],
            },
        }

        templates_file = self.config_dir / "project_templates.json"
        with open(templates_file, "w", encoding="utf-8") as f:
            json.dump(templates, f, ensure_ascii=False, indent=2)

    def get_config_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التكوين"""
        enabled_agents = []
        for agent_name, agent_config in self.config.get("agents", {}).items():
            if agent_config.get("enabled", False):
                enabled_agents.append(agent_name)

        return {
            "config_file": str(self.config_file),
            "system_name": self.config.get("system", {}).get("name", "Unknown"),
            "version": self.config.get("system", {}).get("version", "1.0.0"),
            "enabled_agents": enabled_agents,
            "workspace_dir": self.config.get("workspace", {}).get("base_dir", "workspace"),
            "total_agents": len(self.config.get("agents", {})),
            "enabled_agents_count": len(enabled_agents),
        }
