#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 محلل محادثات Cline بالوكلاء الذكيين - نظام أنوبيس
Anubis Agents-Based Cline Conversation Analyzer
"""

import json
import re
from pathlib import Path
from datetime import datetime
from collections import Counter, defaultdict
import sys

class AnubisAgentsClinetAnalyzer:
    """محلل محادثات Cline بالوكلاء الذكيين"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.content = ""
        self.agents_analysis = {}
        
        # تعريف الوكلاء المختلفين
        self.agents = {
            "content_agent": "وكيل تحليل المحتوى",
            "technical_agent": "وكيل التحليل التقني", 
            "error_agent": "وكيل تحليل الأخطاء",
            "workflow_agent": "وكيل تحليل سير العمل",
            "performance_agent": "وكيل تحليل الأداء"
        }
        
    def load_conversation(self):
        """تحميل ملف المحادثة"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            print(f"🏺 تم تحميل الملف للوكلاء: {self.file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل الملف: {e}")
            return False
    
    def content_agent_analysis(self):
        """وكيل تحليل المحتوى"""
        print("🤖 وكيل تحليل المحتوى يعمل...")
        
        # تحليل اللغات المستخدمة
        arabic_pattern = r'[\u0600-\u06FF]+'
        english_pattern = r'[a-zA-Z]+'
        
        arabic_matches = re.findall(arabic_pattern, self.content)
        english_matches = re.findall(english_pattern, self.content)
        
        # تحليل أنواع المحتوى
        code_blocks = len(re.findall(r'```.*?```', self.content, re.DOTALL))
        json_blocks = len(re.findall(r'\{.*?\}', self.content, re.DOTALL))
        
        analysis = {
            "language_distribution": {
                "arabic_words": len(arabic_matches),
                "english_words": len(english_matches),
                "mixed_content": True if arabic_matches and english_matches else False
            },
            "content_types": {
                "code_blocks": code_blocks,
                "json_blocks": json_blocks,
                "commands": len(re.findall(r'<command>', self.content)),
                "file_operations": len(re.findall(r'<file_operation>', self.content))
            },
            "communication_style": {
                "questions": len(re.findall(r'\?', self.content)),
                "exclamations": len(re.findall(r'!', self.content)),
                "emojis": len(re.findall(r'[🏺📊⚡❌✅🔍💾🤖👤📄📁⏰💬🔝📋📈🕐🕕]', self.content))
            }
        }
        
        self.agents_analysis["content_agent"] = analysis
        print("   ✅ تحليل المحتوى مكتمل")
        return analysis
    
    def technical_agent_analysis(self):
        """وكيل التحليل التقني"""
        print("🔧 وكيل التحليل التقني يعمل...")
        
        # تحليل التقنيات المستخدمة
        technologies = {
            "python": len(re.findall(r'python|\.py', self.content, re.IGNORECASE)),
            "docker": len(re.findall(r'docker|dockerfile', self.content, re.IGNORECASE)),
            "git": len(re.findall(r'git|github', self.content, re.IGNORECASE)),
            "database": len(re.findall(r'mysql|sqlite|database', self.content, re.IGNORECASE)),
            "json": len(re.findall(r'json|\.json', self.content, re.IGNORECASE)),
            "markdown": len(re.findall(r'markdown|\.md', self.content, re.IGNORECASE))
        }
        
        # تحليل أنماط التطوير
        development_patterns = {
            "testing": len(re.findall(r'test|testing|pytest', self.content, re.IGNORECASE)),
            "debugging": len(re.findall(r'debug|error|exception', self.content, re.IGNORECASE)),
            "configuration": len(re.findall(r'config|configuration|setup', self.content, re.IGNORECASE)),
            "deployment": len(re.findall(r'deploy|deployment|production', self.content, re.IGNORECASE))
        }
        
        # تحليل أدوات التطوير
        dev_tools = {
            "vscode": len(re.findall(r'vscode|visual studio code', self.content, re.IGNORECASE)),
            "cline": len(re.findall(r'cline|claude-dev', self.content, re.IGNORECASE)),
            "pip": len(re.findall(r'pip install|pip', self.content, re.IGNORECASE)),
            "npm": len(re.findall(r'npm|node', self.content, re.IGNORECASE))
        }
        
        analysis = {
            "technologies": technologies,
            "development_patterns": development_patterns,
            "development_tools": dev_tools,
            "complexity_indicators": {
                "file_count": len(re.findall(r'\.py|\.md|\.json|\.yml|\.yaml', self.content)),
                "directory_operations": len(re.findall(r'mkdir|cd |ls |dir ', self.content)),
                "system_commands": len(re.findall(r'sudo|chmod|chown', self.content))
            }
        }
        
        self.agents_analysis["technical_agent"] = analysis
        print("   ✅ التحليل التقني مكتمل")
        return analysis
    
    def error_agent_analysis(self):
        """وكيل تحليل الأخطاء"""
        print("🚨 وكيل تحليل الأخطاء يعمل...")
        
        # أنواع الأخطاء المختلفة
        error_types = {
            "syntax_errors": len(re.findall(r'SyntaxError|syntax error', self.content, re.IGNORECASE)),
            "import_errors": len(re.findall(r'ImportError|ModuleNotFoundError', self.content, re.IGNORECASE)),
            "file_errors": len(re.findall(r'FileNotFoundError|No such file', self.content, re.IGNORECASE)),
            "permission_errors": len(re.findall(r'PermissionError|Permission denied', self.content, re.IGNORECASE)),
            "connection_errors": len(re.findall(r'ConnectionError|connection refused', self.content, re.IGNORECASE)),
            "docker_errors": len(re.findall(r'docker.*error|container.*error', self.content, re.IGNORECASE))
        }
        
        # تحليل أنماط حل المشاكل
        problem_solving = {
            "retry_attempts": len(re.findall(r'retry|try again|محاولة', self.content, re.IGNORECASE)),
            "alternative_solutions": len(re.findall(r'alternative|instead|بدلاً', self.content, re.IGNORECASE)),
            "debugging_steps": len(re.findall(r'debug|check|verify|تحقق', self.content, re.IGNORECASE)),
            "fixes_applied": len(re.findall(r'fixed|resolved|solved|تم حل', self.content, re.IGNORECASE))
        }
        
        # تحليل مستوى الخطورة
        severity_indicators = {
            "critical": len(re.findall(r'critical|fatal|خطير', self.content, re.IGNORECASE)),
            "warning": len(re.findall(r'warning|تحذير', self.content, re.IGNORECASE)),
            "info": len(re.findall(r'info|information|معلومات', self.content, re.IGNORECASE))
        }
        
        analysis = {
            "error_types": error_types,
            "problem_solving_patterns": problem_solving,
            "severity_distribution": severity_indicators,
            "error_frequency": {
                "total_errors": sum(error_types.values()),
                "avg_errors_per_exchange": sum(error_types.values()) / 159 if 159 > 0 else 0
            }
        }
        
        self.agents_analysis["error_agent"] = analysis
        print("   ✅ تحليل الأخطاء مكتمل")
        return analysis
    
    def workflow_agent_analysis(self):
        """وكيل تحليل سير العمل"""
        print("📋 وكيل تحليل سير العمل يعمل...")
        
        # تحليل مراحل العمل
        workflow_phases = {
            "planning": len(re.findall(r'plan|planning|خطة|تخطيط', self.content, re.IGNORECASE)),
            "implementation": len(re.findall(r'implement|create|build|تنفيذ|إنشاء', self.content, re.IGNORECASE)),
            "testing": len(re.findall(r'test|testing|اختبار', self.content, re.IGNORECASE)),
            "debugging": len(re.findall(r'debug|fix|إصلاح', self.content, re.IGNORECASE)),
            "deployment": len(re.findall(r'deploy|run|تشغيل', self.content, re.IGNORECASE))
        }
        
        # تحليل أنماط التعاون
        collaboration_patterns = {
            "user_requests": len(re.findall(r'\*\*User:\*\*', self.content)),
            "assistant_responses": len(re.findall(r'\*\*Assistant:\*\*', self.content)),
            "clarifications": len(re.findall(r'clarify|explain|وضح|اشرح', self.content, re.IGNORECASE)),
            "confirmations": len(re.findall(r'confirm|yes|نعم|موافق', self.content, re.IGNORECASE))
        }
        
        # تحليل الإنتاجية
        productivity_metrics = {
            "files_created": len(re.findall(r'File saved|Created file', self.content)),
            "commands_executed": len(re.findall(r'<command>', self.content)),
            "successful_operations": len(re.findall(r'success|successful|نجح|تم بنجاح', self.content, re.IGNORECASE)),
            "failed_operations": len(re.findall(r'failed|failure|فشل', self.content, re.IGNORECASE))
        }
        
        analysis = {
            "workflow_phases": workflow_phases,
            "collaboration_patterns": collaboration_patterns,
            "productivity_metrics": productivity_metrics,
            "efficiency_indicators": {
                "success_rate": productivity_metrics["successful_operations"] / 
                              (productivity_metrics["successful_operations"] + productivity_metrics["failed_operations"])
                              if (productivity_metrics["successful_operations"] + productivity_metrics["failed_operations"]) > 0 else 0,
                "avg_commands_per_exchange": productivity_metrics["commands_executed"] / 159 if 159 > 0 else 0
            }
        }
        
        self.agents_analysis["workflow_agent"] = analysis
        print("   ✅ تحليل سير العمل مكتمل")
        return analysis
    
    def performance_agent_analysis(self):
        """وكيل تحليل الأداء"""
        print("⚡ وكيل تحليل الأداء يعمل...")
        
        # تحليل الأداء الزمني
        timestamps = re.findall(r'(\d{1,2}/\d{1,2}/\d{4}, \d{1,2}:\d{2}:\d{2} [AP]M)', self.content)
        
        # تحليل حجم البيانات
        data_metrics = {
            "total_characters": len(self.content),
            "total_lines": len(self.content.splitlines()),
            "avg_line_length": len(self.content) / len(self.content.splitlines()) if self.content.splitlines() else 0,
            "largest_section": max(len(section) for section in re.split(r'\*\*(?:User|Assistant):\*\*', self.content)) if self.content else 0
        }
        
        # تحليل التعقيد
        complexity_metrics = {
            "nested_structures": len(re.findall(r'\{.*\{.*\}.*\}', self.content, re.DOTALL)),
            "code_complexity": len(re.findall(r'def |class |if |for |while ', self.content)),
            "configuration_complexity": len(re.findall(r'config|settings|parameters', self.content, re.IGNORECASE))
        }
        
        # تحليل الموارد
        resource_usage = {
            "memory_indicators": len(re.findall(r'memory|ram|storage', self.content, re.IGNORECASE)),
            "cpu_indicators": len(re.findall(r'cpu|processor|performance', self.content, re.IGNORECASE)),
            "network_indicators": len(re.findall(r'network|connection|url|http', self.content, re.IGNORECASE)),
            "disk_indicators": len(re.findall(r'disk|file|directory|path', self.content, re.IGNORECASE))
        }
        
        analysis = {
            "temporal_metrics": {
                "session_duration": f"{timestamps[0]} - {timestamps[-1]}" if len(timestamps) >= 2 else "غير محدد",
                "total_timestamps": len(timestamps)
            },
            "data_metrics": data_metrics,
            "complexity_metrics": complexity_metrics,
            "resource_usage": resource_usage,
            "performance_indicators": {
                "data_efficiency": data_metrics["total_characters"] / 159 if 159 > 0 else 0,
                "response_complexity": complexity_metrics["code_complexity"] / 159 if 159 > 0 else 0
            }
        }
        
        self.agents_analysis["performance_agent"] = analysis
        print("   ✅ تحليل الأداء مكتمل")
        return analysis
    
    def generate_agents_report(self):
        """إنشاء تقرير الوكلاء الشامل"""
        print("\n" + "🏺" + "=" * 70)
        print("🤖 تقرير تحليل الوكلاء الذكيين - محادثة Cline")
        print("=" * 72)
        
        # تقرير وكيل المحتوى
        content = self.agents_analysis.get("content_agent", {})
        print(f"\n📝 تقرير وكيل تحليل المحتوى:")
        lang_dist = content.get("language_distribution", {})
        print(f"   🌐 الكلمات العربية: {lang_dist.get('arabic_words', 0):,}")
        print(f"   🌐 الكلمات الإنجليزية: {lang_dist.get('english_words', 0):,}")
        print(f"   📊 محتوى مختلط: {'نعم' if lang_dist.get('mixed_content') else 'لا'}")
        
        content_types = content.get("content_types", {})
        print(f"   💻 كتل الكود: {content_types.get('code_blocks', 0)}")
        print(f"   📋 كتل JSON: {content_types.get('json_blocks', 0)}")
        
        # تقرير وكيل التحليل التقني
        technical = self.agents_analysis.get("technical_agent", {})
        print(f"\n🔧 تقرير وكيل التحليل التقني:")
        technologies = technical.get("technologies", {})
        print("   🛠️  التقنيات الأكثر استخداماً:")
        for tech, count in sorted(technologies.items(), key=lambda x: x[1], reverse=True)[:5]:
            if count > 0:
                print(f"      - {tech}: {count}")
        
        # تقرير وكيل الأخطاء
        errors = self.agents_analysis.get("error_agent", {})
        print(f"\n🚨 تقرير وكيل تحليل الأخطاء:")
        error_freq = errors.get("error_frequency", {})
        print(f"   📊 إجمالي الأخطاء: {error_freq.get('total_errors', 0)}")
        print(f"   📈 متوسط الأخطاء لكل تبادل: {error_freq.get('avg_errors_per_exchange', 0):.2f}")
        
        problem_solving = errors.get("problem_solving_patterns", {})
        print(f"   🔄 محاولات الإعادة: {problem_solving.get('retry_attempts', 0)}")
        print(f"   ✅ الإصلاحات المطبقة: {problem_solving.get('fixes_applied', 0)}")
        
        # تقرير وكيل سير العمل
        workflow = self.agents_analysis.get("workflow_agent", {})
        print(f"\n📋 تقرير وكيل سير العمل:")
        efficiency = workflow.get("efficiency_indicators", {})
        print(f"   📊 معدل النجاح: {efficiency.get('success_rate', 0):.2%}")
        print(f"   ⚡ متوسط الأوامر لكل تبادل: {efficiency.get('avg_commands_per_exchange', 0):.2f}")
        
        productivity = workflow.get("productivity_metrics", {})
        print(f"   📁 الملفات المنشأة: {productivity.get('files_created', 0)}")
        print(f"   ⚡ الأوامر المنفذة: {productivity.get('commands_executed', 0)}")
        
        # تقرير وكيل الأداء
        performance = self.agents_analysis.get("performance_agent", {})
        print(f"\n⚡ تقرير وكيل الأداء:")
        data_metrics = performance.get("data_metrics", {})
        print(f"   📊 إجمالي الأحرف: {data_metrics.get('total_characters', 0):,}")
        print(f"   📄 إجمالي الأسطر: {data_metrics.get('total_lines', 0):,}")
        print(f"   📏 متوسط طول السطر: {data_metrics.get('avg_line_length', 0):.1f} حرف")
        
        temporal = performance.get("temporal_metrics", {})
        print(f"   ⏰ مدة الجلسة: {temporal.get('session_duration', 'غير محدد')}")
        
        return self.agents_analysis
    
    def save_agents_report(self):
        """حفظ تقرير الوكلاء"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"anubis_agents_cline_analysis_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "agents_analysis": self.agents_analysis,
                    "metadata": {
                        "analyzed_file": str(self.file_path),
                        "analysis_timestamp": timestamp,
                        "agents_used": list(self.agents.keys()),
                        "analyzer_version": "1.0.0"
                    }
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ تقرير الوكلاء: {report_file}")
            return report_file
        except Exception as e:
            print(f"❌ خطأ في حفظ تقرير الوكلاء: {e}")
            return None
    
    def run_agents_analysis(self):
        """تشغيل تحليل الوكلاء الكامل"""
        print("🏺 بدء تحليل محادثة Cline بالوكلاء الذكيين...")
        
        if not self.load_conversation():
            return False
        
        # تشغيل جميع الوكلاء
        self.content_agent_analysis()
        self.technical_agent_analysis()
        self.error_agent_analysis()
        self.workflow_agent_analysis()
        self.performance_agent_analysis()
        
        # إنشاء التقرير
        self.generate_agents_report()
        
        # حفظ التقرير
        self.save_agents_report()
        
        print("\n✅ تم إكمال تحليل الوكلاء بنجاح!")
        return True

def main():
    """الدالة الرئيسية"""
    file_path = "cline_task_jul-20-2025_7-13-06-am.md"
    
    if not Path(file_path).exists():
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    analyzer = AnubisAgentsClinetAnalyzer(file_path)
    analyzer.run_agents_analysis()

if __name__ == "__main__":
    main()
