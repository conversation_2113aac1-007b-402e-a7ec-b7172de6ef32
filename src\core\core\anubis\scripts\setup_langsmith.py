#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 إعداد تكامل LangSmith مع نظام أنوبيس
LangSmith Integration Setup for Anubis System

إعداد شامل لتكامل LangSmith مع جميع الوكلاء
"""

import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path


class LangSmithSetup:
    """🔗 إعداد تكامل LangSmith"""

    def __init__(self):
        self.base_path = Path(__file__).parent.parent.absolute()
        self.config_path = self.base_path / "configs"

        print("🔗 إعداد تكامل LangSmith مع نظام أنوبيس")
        print(f"📁 المسار الأساسي: {self.base_path}")

    def check_langsmith_installation(self):
        """فحص تثبيت LangSmith"""
        print("\n🔍 فحص تثبيت LangSmith...")

        try:
            import langsmith

            print("✅ LangSmith مثبت بالفعل")
            return True
        except ImportError:
            print("❌ LangSmith غير مثبت")
            return False

    def install_langsmith(self):
        """تثبيت LangSmith"""
        print("\n📦 تثبيت LangSmith...")

        try:
            # تثبيت LangSmith و LangChain
            packages = ["langsmith", "langchain", "langchain-community"]

            for package in packages:
                print(f"   📦 تثبيت {package}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                )

                if result.returncode == 0:
                    print(f"   ✅ تم تثبيت {package}")
                else:
                    print(f"   ❌ فشل تثبيت {package}: {result.stderr}")
                    return False

            return True

        except Exception as e:
            print(f"❌ خطأ في التثبيت: {e}")
            return False

    def create_langsmith_config(self):
        """إنشاء ملف تكوين LangSmith"""
        print("\n⚙️ إنشاء ملف تكوين LangSmith...")

        config = {
            "langsmith": {
                "enabled": True,
                "project_name": "anubis-ai-system",
                "tracing_enabled": True,
                "api_url": "https://api.smith.langchain.com",
                "environment": "development",
                "tags": ["anubis", "ai-agents", "ollama"],
                "metadata": {
                    "system_version": "2.0",
                    "created_at": datetime.now().isoformat(),
                    "description": "نظام أنوبيس للذكاء الاصطناعي",
                },
            },
            "agents_integration": {
                "enhanced_error_detector": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
                "enhanced_project_analyzer": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
                "enhanced_file_organizer": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
                "enhanced_memory_agent": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
                "smart_ai_agent": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
                "smart_code_analyzer": {
                    "trace_enabled": True,
                    "performance_monitoring": True,
                },
            },
            "models_integration": {
                "ollama_models": ["llama3:8b", "mistral:7b", "phi3:mini"],
                "model_selection": {
                    "auto_select": True,
                    "performance_based": True,
                    "fallback_model": "llama3:8b",
                },
                "performance_tracking": {
                    "response_time": True,
                    "quality_metrics": True,
                    "usage_statistics": True,
                },
            },
        }

        config_file = self.config_path / "langsmith_config.json"

        try:
            self.config_path.mkdir(exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"✅ تم إنشاء ملف التكوين: {config_file}")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف التكوين: {e}")
            return False

    def create_environment_setup(self):
        """إنشاء ملف إعداد متغيرات البيئة"""
        print("\n🌍 إنشاء ملف إعداد البيئة...")

        env_content = """# 🔗 إعداد متغيرات البيئة لـ LangSmith
# LangSmith Environment Variables Setup

# تفعيل تتبع LangSmith
export LANGCHAIN_TRACING_V2=true

# مفتاح API (يجب الحصول عليه من https://smith.langchain.com/)
# export LANGCHAIN_API_KEY="your-api-key-here"

# اسم المشروع في LangSmith
export LANGCHAIN_PROJECT="anubis-ai-system"

# نقطة النهاية للـ API
export LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"

# إعدادات إضافية
export LANGCHAIN_SESSION="anubis-session"
export LANGCHAIN_TAGS="anubis,ai-agents,ollama"

# للاستخدام في Windows PowerShell:
# $env:LANGCHAIN_TRACING_V2="true"
# $env:LANGCHAIN_PROJECT="anubis-ai-system"
# $env:LANGCHAIN_API_KEY="your-api-key-here"

echo "🔗 تم تحميل متغيرات البيئة لـ LangSmith"
"""

        env_file = self.base_path / ".env.langsmith"

        try:
            with open(env_file, "w", encoding="utf-8") as f:
                f.write(env_content)

            print(f"✅ تم إنشاء ملف البيئة: {env_file}")

            # إنشاء ملف PowerShell للـ Windows
            ps_content = """# 🔗 إعداد متغيرات البيئة لـ LangSmith (PowerShell)

$env:LANGCHAIN_TRACING_V2="true"
$env:LANGCHAIN_PROJECT="anubis-ai-system"
# $env:LANGCHAIN_API_KEY="your-api-key-here"
$env:LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
$env:LANGCHAIN_SESSION="anubis-session"
$env:LANGCHAIN_TAGS="anubis,ai-agents,ollama"

Write-Host "🔗 تم تحميل متغيرات البيئة لـ LangSmith" -ForegroundColor Green
"""

            ps_file = self.base_path / "setup_langsmith_env.ps1"

            with open(ps_file, "w", encoding="utf-8") as f:
                f.write(ps_content)

            print(f"✅ تم إنشاء ملف PowerShell: {ps_file}")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف البيئة: {e}")
            return False

    def create_langsmith_wrapper(self):
        """إنشاء wrapper لـ LangSmith"""
        print("\n🔧 إنشاء LangSmith Wrapper...")

        wrapper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 LangSmith Wrapper لنظام أنوبيس
LangSmith Integration Wrapper for Anubis System
"""

import os
import json
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# محاولة استيراد LangSmith
try:
    from langsmith import Client
    from langsmith.utils import tracing_context
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    print("⚠️ LangSmith غير متاح - سيتم استخدام محاكاة")

class AnubisLangSmithWrapper:
    """🔗 Wrapper لتكامل LangSmith مع نظام أنوبيس"""

    def __init__(self, config_path: str = None):
        self.config = self.load_config(config_path)
        self.client = None
        self.traces = []

        if LANGSMITH_AVAILABLE and self.config.get('langsmith', {}).get('enabled', False):
            try:
                self.client = Client()
                print("✅ تم تهيئة LangSmith Client")
            except Exception as e:
                print(f"⚠️ فشل تهيئة LangSmith: {e}")
                self.client = None
        else:
            print("📝 تشغيل في وضع المحاكاة")

    def load_config(self, config_path: str = None) -> Dict[str, Any]:
        """تحميل تكوين LangSmith"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / 'configs' / 'langsmith_config.json'

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ فشل تحميل التكوين: {e}")
            return {}

    def trace_agent_operation(self, agent_name: str, operation: str, inputs: Dict[str, Any] = None):
        """تتبع عملية وكيل"""
        trace_name = f"{agent_name}_{operation}"

        if self.client:
            return self.client.trace(trace_name, inputs=inputs)
        else:
            # محاكاة
            trace_data = {
                'name': trace_name,
                'inputs': inputs,
                'timestamp': datetime.now().isoformat(),
                'agent': agent_name,
                'operation': operation
            }
            self.traces.append(trace_data)
            print(f"📊 Trace: {trace_name}")
            return MockTrace(trace_data)

    def log_model_performance(self, model_name: str, response_time: float,
                            input_tokens: int = 0, output_tokens: int = 0):
        """تسجيل أداء النموذج"""
        performance_data = {
            'model': model_name,
            'response_time': response_time,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'timestamp': datetime.now().isoformat()
        }

        if self.client:
            # تسجيل في LangSmith
            pass
        else:
            print(f"📈 أداء {model_name}: {response_time:.2f}ث")

    def get_traces_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التتبع"""
        return {
            'total_traces': len(self.traces),
            'traces': self.traces[-10:],  # آخر 10 عمليات
            'langsmith_enabled': self.client is not None
        }

class MockTrace:
    """محاكاة Trace لـ LangSmith"""

    def __init__(self, trace_data: Dict[str, Any]):
        self.trace_data = trace_data

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.trace_data['error'] = str(exc_val)
        self.trace_data['completed_at'] = datetime.now().isoformat()

# إنشاء instance عام
langsmith_wrapper = AnubisLangSmithWrapper()
'''

        wrapper_file = self.base_path / "core" / "langsmith_wrapper.py"

        try:
            with open(wrapper_file, "w", encoding="utf-8") as f:
                f.write(wrapper_content)

            print(f"✅ تم إنشاء LangSmith Wrapper: {wrapper_file}")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء Wrapper: {e}")
            return False

    def create_setup_guide(self):
        """إنشاء دليل الإعداد"""
        print("\n📚 إنشاء دليل الإعداد...")

        guide_content = """# 🔗 دليل إعداد LangSmith مع نظام أنوبيس
## LangSmith Setup Guide for Anubis System

## 🚀 الخطوات السريعة

### 1. إنشاء حساب LangSmith
1. اذهب إلى https://smith.langchain.com/
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد باسم "anubis-ai-system"

### 2. الحصول على API Key
1. اذهب إلى Settings → API Keys
2. أنشئ مفتاح API جديد
3. انسخ المفتاح واحفظه بأمان

### 3. إعداد متغيرات البيئة

#### في Linux/Mac:
```bash
export LANGCHAIN_TRACING_V2=true
export LANGCHAIN_API_KEY="your-api-key-here"
export LANGCHAIN_PROJECT="anubis-ai-system"
```

#### في Windows PowerShell:
```powershell
$env:LANGCHAIN_TRACING_V2="true"
$env:LANGCHAIN_API_KEY="your-api-key-here"
$env:LANGCHAIN_PROJECT="anubis-ai-system"
```

### 4. اختبار التكامل
```bash
python scripts/test_langsmith_integration.py
```

## 🎯 الميزات المتاحة

### 📊 مراقبة الوكلاء
- تتبع أداء جميع الوكلاء
- قياس أوقات الاستجابة
- مراقبة جودة النتائج

### 🔗 ربط الوكلاء
- تنسيق العمل بين الوكلاء
- تمرير البيانات بذكاء
- تحسين التفاعلات

### 🧠 تحسين النماذج
- اختيار النموذج الأمثل
- تحسين الـ prompts
- تعلم من التفاعلات

## 🛠️ الاستخدام

### استيراد Wrapper:
```python
from anubis.core.langsmith_wrapper import langsmith_wrapper

# تتبع عملية وكيل
with langsmith_wrapper.trace_agent_operation("error_detector", "scan_file"):
    result = detector.scan_file("file.py")
```

### مراقبة أداء النموذج:
```python
import time

start_time = time.time()
response = model.generate("prompt")
end_time = time.time()

langsmith_wrapper.log_model_performance(
    "llama3:8b",
    end_time - start_time
)
```

## 🔧 استكشاف الأخطاء

### مشكلة: API Key غير صحيح
```
خطأ: Authentication failed
الحل: تأكد من صحة API Key في متغيرات البيئة
```

### مشكلة: المشروع غير موجود
```
خطأ: Project not found
الحل: أنشئ مشروع "anubis-ai-system" في LangSmith
```

### مشكلة: LangSmith غير مثبت
```
خطأ: ImportError langsmith
الحل: pip install langsmith langchain
```

## 📈 المراقبة والتحليل

### عرض التتبع:
1. اذهب إلى https://smith.langchain.com/
2. اختر مشروع "anubis-ai-system"
3. راجع التتبع والإحصائيات

### تحليل الأداء:
- أوقات استجابة الوكلاء
- معدلات نجاح العمليات
- استخدام النماذج المختلفة

---

🏺 **نظام أنوبيس + LangSmith = قوة خارقة!** 🚀
"""

        guide_file = self.base_path / "docs" / "LANGSMITH_SETUP_GUIDE.md"

        try:
            with open(guide_file, "w", encoding="utf-8") as f:
                f.write(guide_content)

            print(f"✅ تم إنشاء دليل الإعداد: {guide_file}")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء الدليل: {e}")
            return False

    def run_setup(self):
        """تشغيل الإعداد الكامل"""
        print("🚀 بدء إعداد تكامل LangSmith مع نظام أنوبيس")
        print("=" * 60)

        steps = [
            ("فحص التثبيت", self.check_langsmith_installation),
            ("تثبيت LangSmith", self.install_langsmith),
            ("إنشاء ملف التكوين", self.create_langsmith_config),
            ("إعداد متغيرات البيئة", self.create_environment_setup),
            ("إنشاء Wrapper", self.create_langsmith_wrapper),
            ("إنشاء دليل الإعداد", self.create_setup_guide),
        ]

        results = {}

        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            try:
                result = step_func()
                results[step_name] = result
                if result:
                    print(f"✅ {step_name} - نجح")
                else:
                    print(f"❌ {step_name} - فشل")
            except Exception as e:
                print(f"❌ {step_name} - خطأ: {e}")
                results[step_name] = False

        # عرض النتائج
        print(f"\n🏆 تم إكمال إعداد LangSmith!")
        print(f"✅ العمليات الناجحة: {sum(results.values())}/{len(results)}")

        if all(results.values()):
            print("\n🎉 تم الإعداد بنجاح! الخطوات التالية:")
            print("   1. احصل على API Key من https://smith.langchain.com/")
            print("   2. قم بتعيين LANGCHAIN_API_KEY في متغيرات البيئة")
            print("   3. شغل: python scripts/test_langsmith_integration.py")
        else:
            print("\n⚠️ بعض الخطوات فشلت. راجع الأخطاء أعلاه.")

        return results


def main():
    """الدالة الرئيسية"""
    setup = LangSmithSetup()
    results = setup.run_setup()

    print(f"\n🏺 إعداد LangSmith لنظام أنوبيس مكتمل!")

    return 0 if all(results.values()) else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
