#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📁 وكيل تنظيم الملفات المحسن
Enhanced File Organizer Agent
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedFileOrganizerAgent(BaseAgent):
    """📁 وكيل تنظيم الملفات المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_file_organizer"
    
    def initialize_agent(self):
        """تهيئة وكيل تنظيم الملفات"""
        self.file_categories = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
            'documents': ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
            'code': ['.py', '.js', '.ts', '.jsx', '.tsx', '.vue', '.html', '.css'],
            'config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.env'],
            'archives': ['.zip', '.rar', '.tar', '.gz', '.7z'],
            'videos': ['.mp4', '.avi', '.mkv', '.mov', '.wmv']
        }
        
        self.project_structures = {
            'react': {
                'folders': ['src', 'src/components', 'src/pages', 'src/hooks', 'public', 'tests'],
                'files': {
                    'package.json': '{"name": "react-app", "version": "1.0.0"}',
                    'src/App.js': 'import React from "react";\n\nfunction App() {\n  return <div>Hello React!</div>;\n}\n\nexport default App;'
                }
            },
            'vue': {
                'folders': ['src', 'src/components', 'src/views', 'src/router', 'public', 'tests'],
                'files': {
                    'package.json': '{"name": "vue-app", "version": "1.0.0"}',
                    'src/App.vue': '<template>\n  <div>Hello Vue!</div>\n</template>'
                }
            },
            'django': {
                'folders': ['app', 'templates', 'static', 'tests', 'media'],
                'files': {
                    'requirements.txt': 'Django>=4.0\ndjango-cors-headers',
                    'manage.py': '#!/usr/bin/env python\nimport os\nimport sys'
                }
            },
            'fastapi': {
                'folders': ['app', 'tests', 'docs', 'static'],
                'files': {
                    'requirements.txt': 'fastapi>=0.68.0\nuvicorn[standard]',
                    'main.py': 'from fastapi import FastAPI\n\napp = FastAPI()\n\<EMAIL>("/")\ndef read_root():\n    return {"Hello": "World"}'
                }
            }
        }
        
        self.log_action("تهيئة منظم الملفات المحسن", "جاهز للتنظيم الذكي")
    
    def organize_files(self, target_path=None):
        """تنظيم الملفات حسب النوع"""
        target_path = Path(target_path) if target_path else self.project_path
        
        if not target_path.exists():
            return {'error': f'المسار غير موجود: {target_path}'}
        
        organized = {
            'organized_files': 0,
            'created_folders': [],
            'categories': {},
            'skipped_files': []
        }
        
        for category, extensions in self.file_categories.items():
            category_path = target_path / category
            files_moved = 0
            
            for ext in extensions:
                files = list(target_path.glob(f'*{ext}'))
                if files:
                    category_path.mkdir(exist_ok=True)
                    if str(category_path) not in organized['created_folders']:
                        organized['created_folders'].append(str(category_path))
                    
                    for file in files:
                        try:
                            destination = category_path / file.name
                            if not destination.exists():
                                shutil.move(str(file), str(destination))
                                files_moved += 1
                            else:
                                organized['skipped_files'].append(str(file))
                        except Exception as e:
                            organized['skipped_files'].append(f"{file}: {e}")
            
            if files_moved > 0:
                organized['categories'][category] = files_moved
                organized['organized_files'] += files_moved
        
        self.log_action("تنظيم الملفات", f"تم تنظيم {organized['organized_files']} ملف")
        return organized
    
    def create_project_structure(self, project_type, project_name):
        """إنشاء هيكل مشروع جديد"""
        if project_type not in self.project_structures:
            return {'error': f'نوع المشروع غير مدعوم: {project_type}'}
        
        project_path = self.project_path / project_name
        structure = self.project_structures[project_type]
        
        try:
            # إنشاء المجلد الرئيسي
            project_path.mkdir(exist_ok=True)
            
            # إنشاء المجلدات
            created_folders = []
            for folder in structure['folders']:
                folder_path = project_path / folder
                folder_path.mkdir(parents=True, exist_ok=True)
                created_folders.append(str(folder_path))
            
            # إنشاء الملفات
            created_files = []
            for file_name, content in structure.get('files', {}).items():
                file_path = project_path / file_name
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content.replace('\n', '\n'))
                created_files.append(str(file_path))
            
            result = {
                'project_path': str(project_path),
                'project_type': project_type,
                'created_folders': created_folders,
                'created_files': created_files,
                'status': 'success'
            }
            
            self.log_action("إنشاء مشروع جديد", f"{project_type}: {project_name}")
            return result
            
        except Exception as e:
            return {'error': f'خطأ في إنشاء المشروع: {e}'}
    
    def clean_empty_folders(self, target_path=None):
        """تنظيف المجلدات الفارغة"""
        target_path = Path(target_path) if target_path else self.project_path
        
        removed_folders = []
        
        for folder in target_path.rglob('*'):
            if folder.is_dir():
                try:
                    if not any(folder.iterdir()):  # مجلد فارغ
                        folder.rmdir()
                        removed_folders.append(str(folder))
                except:
                    pass
        
        return {
            'removed_folders': removed_folders,
            'count': len(removed_folders)
        }
