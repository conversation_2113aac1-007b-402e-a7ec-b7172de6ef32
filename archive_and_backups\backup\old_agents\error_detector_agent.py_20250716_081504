#!/usr/bin/env python3
"""
🔍 وكيل كشف الأخطاء الذكي العالمي
Universal Intelligent Error Detection Agent

تم تطويره بناءً على توصيات نماذج Ollama لكشف الأخطاء البرمجية وتحليل جودة الكود
"""

import os
import sys
import ast
import json
import re
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# إضافة مجلد core إلى المسار
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent

class ErrorDetectorAgent(BaseAgent):
    """وكيل ذكي مسؤول عن كشف الأخطاء البرمجية وتحليل جودة الكود"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "error_detector"
    
    def initialize_agent(self):
        """تهيئة وكيل كشف الأخطاء"""
        # إعدادات الكشف من التكوين
        self.check_syntax = self.config.get('check_syntax', True)
        self.check_imports = self.config.get('check_imports', True)
        self.check_style = self.config.get('check_style', True)
        self.auto_fix = self.config.get('auto_fix', False)
        
        # أنواع الأخطاء المدعومة
        self.error_types = {
            'syntax_errors': [],
            'import_errors': [],
            'style_issues': [],
            'logic_warnings': [],
            'security_issues': [],
            'performance_issues': []
        }
        
        # مقاييس جودة الكود
        self.quality_metrics = {
            'complexity': 0,
            'maintainability': 0,
            'readability': 0,
            'test_coverage': 0
        }
        
        self.log_action("تم تهيئة وكيل كشف الأخطاء")
    
    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل كشف الأخطاء"""
        self.log_action("بدء تحليل كشف الأخطاء")
        
        # الحصول على معلومات المشروع
        project_info = self.get_project_info()
        
        # تحليل الملفات البرمجية
        analysis_results = {
            'project_info': project_info,
            'error_analysis': self._analyze_errors(),
            'quality_analysis': self._analyze_code_quality(),
            'recommendations': self._generate_recommendations(),
            'summary': {}
        }
        
        # إنشاء الملخص
        analysis_results['summary'] = self._create_summary(analysis_results)
        
        # حفظ التقرير
        self.save_report(analysis_results)
        
        self.log_action("انتهاء تحليل كشف الأخطاء")
        return analysis_results
    
    def _analyze_errors(self) -> Dict[str, Any]:
        """تحليل الأخطاء في الكود"""
        error_analysis = {
            'syntax_errors': [],
            'import_errors': [],
            'style_issues': [],
            'logic_warnings': [],
            'security_issues': [],
            'performance_issues': [],
            'total_files_analyzed': 0,
            'files_with_errors': 0
        }
        
        # البحث عن ملفات Python
        python_files = list(self.project_path.rglob('*.py'))
        error_analysis['total_files_analyzed'] = len(python_files)
        
        for file_path in python_files:
            try:
                file_errors = self._analyze_file(file_path)
                
                # دمج الأخطاء
                for error_type, errors in file_errors.items():
                    if errors and error_type in error_analysis:
                        error_analysis[error_type].extend(errors)
                        if error_type != 'total_files_analyzed':
                            error_analysis['files_with_errors'] += 1
                            
            except Exception as e:
                self.log_action(f"خطأ في تحليل الملف {file_path}", str(e))
        
        return error_analysis
    
    def _analyze_file(self, file_path: Path) -> Dict[str, List]:
        """تحليل ملف واحد"""
        file_errors = {
            'syntax_errors': [],
            'import_errors': [],
            'style_issues': [],
            'logic_warnings': [],
            'security_issues': [],
            'performance_issues': []
        }
        
        try:
            # قراءة محتوى الملف
            content = file_path.read_text(encoding='utf-8')
            
            # فحص الأخطاء النحوية
            if self.check_syntax:
                syntax_errors = self._check_syntax_errors(file_path, content)
                file_errors['syntax_errors'].extend(syntax_errors)
            
            # فحص أخطاء الاستيراد
            if self.check_imports:
                import_errors = self._check_import_errors(file_path, content)
                file_errors['import_errors'].extend(import_errors)
            
            # فحص مشاكل الأسلوب
            if self.check_style:
                style_issues = self._check_style_issues(file_path, content)
                file_errors['style_issues'].extend(style_issues)
            
            # فحص التحذيرات المنطقية
            logic_warnings = self._check_logic_warnings(file_path, content)
            file_errors['logic_warnings'].extend(logic_warnings)
            
            # فحص المشاكل الأمنية
            security_issues = self._check_security_issues(file_path, content)
            file_errors['security_issues'].extend(security_issues)
            
            # فحص مشاكل الأداء
            performance_issues = self._check_performance_issues(file_path, content)
            file_errors['performance_issues'].extend(performance_issues)
            
        except Exception as e:
            file_errors['syntax_errors'].append({
                'file': str(file_path),
                'line': 0,
                'error': f"خطأ في قراءة الملف: {str(e)}",
                'severity': 'high'
            })
        
        return file_errors
    
    def _check_syntax_errors(self, file_path: Path, content: str) -> List[Dict]:
        """فحص الأخطاء النحوية"""
        syntax_errors = []
        
        try:
            # محاولة تحليل الكود باستخدام AST
            ast.parse(content)
        except SyntaxError as e:
            syntax_errors.append({
                'file': str(file_path.relative_to(self.project_path)),
                'line': e.lineno or 0,
                'column': e.offset or 0,
                'error': e.msg,
                'severity': 'high',
                'type': 'syntax_error'
            })
        except Exception as e:
            syntax_errors.append({
                'file': str(file_path.relative_to(self.project_path)),
                'line': 0,
                'error': f"خطأ في التحليل: {str(e)}",
                'severity': 'medium',
                'type': 'parse_error'
            })
        
        return syntax_errors
    
    def _check_import_errors(self, file_path: Path, content: str) -> List[Dict]:
        """فحص أخطاء الاستيراد"""
        import_errors = []
        
        # البحث عن عبارات الاستيراد
        import_pattern = r'^(import|from)\s+([^\s]+)'
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if re.match(import_pattern, line):
                try:
                    # محاولة تنفيذ الاستيراد (بشكل آمن)
                    if line.startswith('import '):
                        module_name = line.split()[1].split('.')[0]
                    elif line.startswith('from '):
                        module_name = line.split()[1].split('.')[0]
                    else:
                        continue
                    
                    # فحص المكتبات الشائعة المفقودة
                    if module_name in ['numpy', 'pandas', 'matplotlib', 'requests', 'flask', 'django', 'streamlit']:
                        import_errors.append({
                            'file': str(file_path.relative_to(self.project_path)),
                            'line': line_num,
                            'error': f"مكتبة مطلوبة قد تكون مفقودة: {module_name}",
                            'severity': 'medium',
                            'type': 'missing_dependency',
                            'suggestion': f"pip install {module_name}"
                        })
                        
                except Exception:
                    pass
        
        return import_errors
    
    def _check_style_issues(self, file_path: Path, content: str) -> List[Dict]:
        """فحص مشاكل الأسلوب"""
        style_issues = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # خطوط طويلة جداً
            if len(line) > 120:
                style_issues.append({
                    'file': str(file_path.relative_to(self.project_path)),
                    'line': line_num,
                    'error': f"السطر طويل جداً ({len(line)} حرف)",
                    'severity': 'low',
                    'type': 'line_too_long'
                })
            
            # مسافات زائدة في نهاية السطر
            if line.endswith(' ') or line.endswith('\t'):
                style_issues.append({
                    'file': str(file_path.relative_to(self.project_path)),
                    'line': line_num,
                    'error': "مسافات زائدة في نهاية السطر",
                    'severity': 'low',
                    'type': 'trailing_whitespace'
                })
        
        return style_issues
    
    def _check_logic_warnings(self, file_path: Path, content: str) -> List[Dict]:
        """فحص التحذيرات المنطقية"""
        logic_warnings = []
        
        # البحث عن متغيرات غير مستخدمة
        if 'import ' in content and content.count('import ') > content.count('# import'):
            logic_warnings.append({
                'file': str(file_path.relative_to(self.project_path)),
                'line': 0,
                'error': "قد توجد استيرادات غير مستخدمة",
                'severity': 'low',
                'type': 'unused_imports'
            })
        
        return logic_warnings
    
    def _check_security_issues(self, file_path: Path, content: str) -> List[Dict]:
        """فحص المشاكل الأمنية"""
        security_issues = []
        
        # البحث عن كلمات مرور مكشوفة
        password_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']'
        ]
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            for pattern in password_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    security_issues.append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'line': line_num,
                        'error': "كلمة مرور أو مفتاح API مكشوف في الكود",
                        'severity': 'high',
                        'type': 'exposed_credentials'
                    })
        
        return security_issues
    
    def _check_performance_issues(self, file_path: Path, content: str) -> List[Dict]:
        """فحص مشاكل الأداء"""
        performance_issues = []
        
        # البحث عن حلقات مكررة
        if content.count('for ') > 3 and 'for ' in content:
            performance_issues.append({
                'file': str(file_path.relative_to(self.project_path)),
                'line': 0,
                'error': "عدد كبير من الحلقات - قد يؤثر على الأداء",
                'severity': 'medium',
                'type': 'multiple_loops'
            })
        
        return performance_issues
    
    def _analyze_code_quality(self) -> Dict[str, Any]:
        """تحليل جودة الكود"""
        quality_analysis = {
            'complexity_score': 0,
            'maintainability_score': 0,
            'readability_score': 0,
            'documentation_score': 0,
            'test_coverage_estimate': 0
        }
        
        python_files = list(self.project_path.rglob('*.py'))
        if not python_files:
            return quality_analysis
        
        total_lines = 0
        documented_functions = 0
        total_functions = 0
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                lines = content.split('\n')
                total_lines += len(lines)
                
                # حساب الدوال والتوثيق
                function_count = content.count('def ')
                docstring_count = content.count('"""') + content.count("'''")
                
                total_functions += function_count
                documented_functions += min(function_count, docstring_count // 2)
                
            except Exception:
                continue
        
        # حساب النقاط
        if total_functions > 0:
            quality_analysis['documentation_score'] = (documented_functions / total_functions) * 100
        
        quality_analysis['complexity_score'] = max(0, 100 - (total_lines / len(python_files)) / 10)
        quality_analysis['maintainability_score'] = (quality_analysis['documentation_score'] + quality_analysis['complexity_score']) / 2
        quality_analysis['readability_score'] = quality_analysis['maintainability_score']
        
        # تقدير تغطية الاختبارات
        test_files = list(self.project_path.rglob('test_*.py')) + list(self.project_path.rglob('*_test.py'))
        if test_files and python_files:
            quality_analysis['test_coverage_estimate'] = min(100, (len(test_files) / len(python_files)) * 100)
        
        return quality_analysis
    
    def _generate_recommendations(self) -> List[Dict]:
        """إنشاء توصيات للتحسين"""
        recommendations = []
        
        # توصيات عامة
        recommendations.append({
            'category': 'general',
            'priority': 'medium',
            'title': 'استخدام أدوات فحص الكود',
            'description': 'استخدم أدوات مثل flake8 أو pylint لفحص جودة الكود',
            'action': 'pip install flake8 pylint'
        })
        
        recommendations.append({
            'category': 'testing',
            'priority': 'high',
            'title': 'إضافة اختبارات وحدة',
            'description': 'أضف اختبارات شاملة لتحسين جودة الكود',
            'action': 'pip install pytest'
        })
        
        recommendations.append({
            'category': 'documentation',
            'priority': 'medium',
            'title': 'تحسين التوثيق',
            'description': 'أضف docstrings للدوال والفئات',
            'action': 'إضافة توثيق للكود'
        })
        
        return recommendations
    
    def _create_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء ملخص التحليل"""
        error_analysis = analysis_results['error_analysis']
        quality_analysis = analysis_results['quality_analysis']
        
        total_errors = sum(len(errors) for errors in error_analysis.values() if isinstance(errors, list))
        
        return {
            'total_files_analyzed': error_analysis['total_files_analyzed'],
            'files_with_errors': error_analysis['files_with_errors'],
            'total_errors_found': total_errors,
            'quality_score': round(sum(quality_analysis.values()) / len(quality_analysis), 2),
            'severity_breakdown': self._calculate_severity_breakdown(error_analysis),
            'status': 'completed',
            'analysis_time': datetime.now().isoformat()
        }
    
    def _calculate_severity_breakdown(self, error_analysis: Dict[str, Any]) -> Dict[str, int]:
        """حساب توزيع الأخطاء حسب الخطورة"""
        severity_count = {'high': 0, 'medium': 0, 'low': 0}
        
        for error_type, errors in error_analysis.items():
            if isinstance(errors, list):
                for error in errors:
                    if isinstance(error, dict) and 'severity' in error:
                        severity = error['severity']
                        if severity in severity_count:
                            severity_count[severity] += 1
        
        return severity_count
