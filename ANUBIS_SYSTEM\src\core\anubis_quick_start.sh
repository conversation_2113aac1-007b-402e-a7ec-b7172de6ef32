#!/bin/bash
# 🏺 Anubis API Quick Start Script
# سكريبت البدء السريع لـ Anubis API

set -e

API_URL="http://localhost:8000"
API_KEY="anubis-api-key-2025"

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏺 Anubis API Quick Start${NC}"
echo "=================================="

# فحص إذا كان الخادم يعمل
check_server() {
    echo -e "${YELLOW}🔍 فحص حالة الخادم...${NC}"
    if curl -s "$API_URL/health" > /dev/null; then
        echo -e "${GREEN}✅ الخادم يعمل بشكل طبيعي${NC}"
        return 0
    else
        echo -e "${RED}❌ الخادم غير متاح. يرجى تشغيله أولاً${NC}"
        echo "تشغيل الخادم: cd anubis/api && python anubis_api_server.py"
        return 1
    fi
}

# تشغيل تحليل سريع
quick_analysis() {
    local project_path=${1:-.}
    echo -e "${YELLOW}📊 تشغيل تحليل سريع للمشروع: $project_path${NC}"
    
    curl -s -X POST "$API_URL/api/v1/agents/run" \
        -H "X-API-Key: $API_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"agent_type\": \"project_analyzer\", \"project_path\": \"$project_path\", \"verbose\": false}" \
        | jq -r '.result.summary // "لا توجد نتائج"'
}

# كشف الأخطاء
detect_errors() {
    local project_path=${1:-.}
    echo -e "${YELLOW}🔍 كشف الأخطاء في: $project_path${NC}"
    
    curl -s -X POST "$API_URL/api/v1/agents/run" \
        -H "X-API-Key: $API_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"agent_type\": \"error_detector\", \"project_path\": \"$project_path\", \"verbose\": false}" \
        | jq -r '.result.errors_found // 0'
}

# سؤال سريع لـ AI
ask_ai() {
    local question="$1"
    echo -e "${YELLOW}🤖 سؤال للذكاء الاصطناعي: $question${NC}"
    
    curl -s -X POST "$API_URL/api/v1/models/ollama/generate" \
        -H "X-API-Key: $API_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"prompt\": \"$question\", \"model\": \"llama3:8b\", \"temperature\": 0.7}" \
        | jq -r '.response // "لا توجد استجابة"'
}

# تحليل تعاوني
collaborative_analysis() {
    local task="$1"
    local project_path=${2:-.}
    echo -e "${YELLOW}🤝 تحليل تعاوني: $task${NC}"
    
    curl -s -X POST "$API_URL/api/v1/collaboration/analyze" \
        -H "X-API-Key: $API_KEY" \
        -H "Content-Type: application/json" \
        -d "{
            \"task\": \"$task\",
            \"models\": [\"llama3:8b\"],
            \"agents\": [\"project_analyzer\", \"error_detector\"],
            \"project_path\": \"$project_path\",
            \"collaboration_mode\": \"parallel\"
        }" \
        | jq -r '.consensus.summary // "لا توجد نتائج"'
}

# عرض القائمة الرئيسية
show_menu() {
    echo ""
    echo -e "${BLUE}اختر العملية:${NC}"
    echo "1. فحص حالة النظام"
    echo "2. تحليل سريع للمشروع"
    echo "3. كشف الأخطاء"
    echo "4. سؤال للذكاء الاصطناعي"
    echo "5. تحليل تعاوني"
    echo "6. عرض الوثائق التفاعلية"
    echo "0. خروج"
    echo ""
}

# الحلقة الرئيسية
main() {
    # فحص الخادم أولاً
    if ! check_server; then
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "اختيارك: " choice
        
        case $choice in
            1)
                echo -e "${GREEN}$(curl -s "$API_URL/health" | jq -r '.status // "غير متاح"')${NC}"
                ;;
            2)
                read -p "مسار المشروع (اتركه فارغاً للمجلد الحالي): " project_path
                quick_analysis "$project_path"
                ;;
            3)
                read -p "مسار المشروع (اتركه فارغاً للمجلد الحالي): " project_path
                errors_count=$(detect_errors "$project_path")
                if [ "$errors_count" -gt 0 ]; then
                    echo -e "${RED}تم العثور على $errors_count خطأ${NC}"
                else
                    echo -e "${GREEN}لم يتم العثور على أخطاء${NC}"
                fi
                ;;
            4)
                read -p "اكتب سؤالك: " question
                if [ -n "$question" ]; then
                    ask_ai "$question"
                fi
                ;;
            5)
                read -p "وصف المهمة: " task
                read -p "مسار المشروع (اتركه فارغاً للمجلد الحالي): " project_path
                if [ -n "$task" ]; then
                    collaborative_analysis "$task" "$project_path"
                fi
                ;;
            6)
                echo -e "${BLUE}فتح الوثائق التفاعلية...${NC}"
                if command -v xdg-open > /dev/null; then
                    xdg-open "$API_URL/docs"
                elif command -v open > /dev/null; then
                    open "$API_URL/docs"
                else
                    echo "افتح المتصفح على: $API_URL/docs"
                fi
                ;;
            0)
                echo -e "${GREEN}شكراً لاستخدام Anubis API!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}اختيار غير صحيح${NC}"
                ;;
        esac
        
        echo ""
        read -p "اضغط Enter للمتابعة..."
    done
}

# تشغيل البرنامج
main