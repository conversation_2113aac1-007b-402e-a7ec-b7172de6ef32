#!/usr/bin/env python3
"""
🤖 النظام الرئيسي للمساعدين الذكيين العالمي
Main Universal AI Assistants System
"""

import importlib
import importlib.util
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# إضافة مجلد agents إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "agents"))

try:
    from .base_agent import BaseAgent
    from .config_manager import ConfigManager
    from .logger import SessionLogger, SystemLogger
except ImportError:
    # استيراد مطلق عند التشغيل المباشر
    from base_agent import BaseAgent
    from config_manager import ConfigManager
    from logger import SessionLogger, SystemLogger

# إضافة دعم قاعدة البيانات أنوبيس
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), "..", "database"))
    from database_manager import AnubisDatabaseManager

    DATABASE_AVAILABLE = True
    print("🏺 تم تحميل دعم قاعدة بيانات أنوبيس")
except ImportError:
    DATABASE_AVAILABLE = False
    print("⚠️ قاعدة البيانات غير متاحة - سيتم العمل بدون قاعدة بيانات")


class UniversalAssistantSystem:
    """النظام الرئيسي للمساعدين الذكيين العالمي"""

    def __init__(self, project_path: str, config: Dict[str, Any] = None, verbose: bool = False):
        """
        تهيئة النظام

        Args:
            project_path: مسار المشروع
            config: إعدادات النظام
            verbose: عرض تفاصيل أكثر
        """
        self.project_path = Path(project_path)
        self.config = config or {}
        self.verbose = verbose

        # معلومات النظام
        self.system_name = "Universal AI Assistants"
        self.version = "1.0.0"
        self.start_time = datetime.now()

        # إعداد السجلات
        self.logger = SystemLogger()
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_logger = self.logger.create_session_log(self.session_id)

        # تهيئة قاعدة بيانات أنوبيس
        self.db_manager = None
        if DATABASE_AVAILABLE:
            try:
                self.db_manager = AnubisDatabaseManager()
                if self.db_manager.initialize_database():
                    if self.verbose:
                        print("🏺 تم تهيئة قاعدة بيانات أنوبيس")
                else:
                    self.db_manager = None
                    if self.verbose:
                        print("⚠️ فشل في تهيئة قاعدة البيانات")
            except Exception as e:
                self.db_manager = None
                if self.verbose:
                    print(f"⚠️ خطأ في تهيئة قاعدة البيانات: {e}")

        # الوكلاء
        self.available_agents = {}
        self.active_agents = {}

        # معلومات المشروع
        self.project_type = None
        self.project_info = {}

        # تهيئة النظام
        self._initialize_system()

    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            self.logger.log_system_event("تهيئة النظام", f"المشروع: {self.project_path}")

            # التحقق من المشروع
            if not self._validate_project():
                raise ValueError(f"مسار المشروع غير صحيح: {self.project_path}")

            # اكتشاف نوع المشروع
            self.project_type = self._detect_project_type()
            self.logger.info(f"تم اكتشاف نوع المشروع: {self.project_type}")

            # تحميل الوكلاء المتاحين
            self._load_available_agents()

            # تفعيل الوكلاء حسب التكوين
            self._activate_agents()

            self.logger.log_system_event(
                "تم تهيئة النظام بنجاح", f"الوكلاء النشطين: {len(self.active_agents)}"
            )

        except Exception as e:
            self.logger.log_error_with_traceback("خطأ في تهيئة النظام", e)
            raise

    def _validate_project(self) -> bool:
        """التحقق من صحة المشروع"""
        return self.project_path.exists() and self.project_path.is_dir()

    def _detect_project_type(self) -> str:
        """اكتشاف نوع المشروع"""
        # فحص ملفات المشروع
        files = list(self.project_path.glob("*.py"))

        # البحث عن مؤشرات نوع المشروع
        for file in files:
            try:
                content = file.read_text(encoding="utf-8")

                if "streamlit" in content.lower():
                    return "streamlit"
                elif "django" in content.lower():
                    return "django"
                elif "fastapi" in content.lower():
                    return "fastapi"
                elif "flask" in content.lower():
                    return "flask"

            except Exception:
                continue

        # فحص ملفات التكوين
        if (self.project_path / "manage.py").exists():
            return "django"
        elif (self.project_path / "requirements.txt").exists():
            try:
                req_content = (self.project_path / "requirements.txt").read_text()
                if "streamlit" in req_content:
                    return "streamlit"
                elif "fastapi" in req_content:
                    return "fastapi"
                elif "flask" in req_content:
                    return "flask"
            except Exception:
                pass

        return "custom"

    def _load_available_agents(self):
        """تحميل الوكلاء المتاحين"""
        agents_dir = Path(__file__).parent.parent / "agents"

        if not agents_dir.exists():
            self.logger.warning("مجلد الوكلاء غير موجود")
            return

        # إضافة مجلد الوكلاء إلى المسار
        if str(agents_dir) not in sys.path:
            sys.path.insert(0, str(agents_dir))

        # البحث عن ملفات الوكلاء
        for agent_file in agents_dir.glob("*_agent.py"):
            try:
                agent_name = agent_file.stem  # اسم الملف بدون امتداد

                # تحميل الوحدة
                spec = importlib.util.spec_from_file_location(agent_name, agent_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # البحث عن فئة الوكيل
                agent_class = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (
                        isinstance(attr, type)
                        and attr_name.endswith("Agent")
                        and attr_name != "BaseAgent"
                    ):

                        agent_class = attr
                        break

                if agent_class:
                    self.available_agents[agent_name] = agent_class
                    self.logger.info(f"تم تحميل الوكيل: {agent_name} -> {agent_class.__name__}")
                else:
                    self.logger.warning(f"لم يتم العثور على فئة وكيل في {agent_name}")

            except Exception as e:
                self.logger.warning(f"فشل تحميل الوكيل {agent_file.name}: {e}")
                if self.verbose:
                    import traceback

                    self.logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

    def _activate_agents(self):
        """تفعيل الوكلاء حسب التكوين"""
        for agent_name, agent_class in self.available_agents.items():
            try:
                # التحقق من التفعيل في التكوين
                agent_config = self.config.get("agents", {}).get(agent_name, {})

                if agent_config.get("enabled", True):  # افتراضياً مفعل
                    # إنشاء مثيل الوكيل
                    agent_instance = agent_class(
                        project_path=str(self.project_path),
                        config=agent_config,
                        verbose=self.verbose,
                    )

                    self.active_agents[agent_name] = agent_instance
                    self.logger.info(f"تم تفعيل الوكيل: {agent_name}")

            except Exception as e:
                self.logger.error(f"فشل تفعيل الوكيل {agent_name}: {e}")

    def analyze_project(self) -> Dict[str, Any]:
        """تحليل شامل للمشروع"""
        self.logger.log_system_event("بدء التحليل الشامل")

        analysis_results = {
            "project_info": {
                "path": str(self.project_path),
                "name": self.project_path.name,
                "type": self.project_type,
                "analysis_time": datetime.now().isoformat(),
            },
            "agents_results": {},
            "summary": {},
            "success": True,
        }

        # تشغيل جميع الوكلاء النشطين
        for agent_name, agent in self.active_agents.items():
            try:
                self.logger.info(f"تشغيل الوكيل: {agent_name}")
                result = agent.run()
                analysis_results["agents_results"][agent_name] = result

                # تسجيل النتيجة في سجل الجلسة
                self.session_logger.log_agent_result(agent_name, result)

            except Exception as e:
                error_result = {
                    "success": False,
                    "error": str(e),
                    "agent_name": agent_name,
                }
                analysis_results["agents_results"][agent_name] = error_result
                self.logger.error(f"خطأ في تشغيل الوكيل {agent_name}: {e}")

        # إنشاء الملخص
        analysis_results["summary"] = self._create_analysis_summary(analysis_results)

        self.logger.log_system_event("انتهاء التحليل الشامل")
        return analysis_results

    def _create_analysis_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء ملخص التحليل"""
        agents_results = results.get("agents_results", {})

        successful_agents = []
        failed_agents = []

        for agent_name, result in agents_results.items():
            if result.get("success", False):
                successful_agents.append(agent_name)
            else:
                failed_agents.append(agent_name)

        return {
            "total_agents": len(agents_results),
            "successful_agents": len(successful_agents),
            "failed_agents": len(failed_agents),
            "success_rate": (
                len(successful_agents) / len(agents_results) * 100 if agents_results else 0
            ),
            "successful_agents_list": successful_agents,
            "failed_agents_list": failed_agents,
        }

    def run_agent(self, agent_name: str) -> Dict[str, Any]:
        """تشغيل وكيل محدد"""
        if agent_name not in self.active_agents:
            return {
                "success": False,
                "error": f"الوكيل {agent_name} غير متاح أو غير مفعل",
            }

        try:
            self.logger.info(f"تشغيل الوكيل المحدد: {agent_name}")
            result = self.active_agents[agent_name].run()
            self.session_logger.log_agent_result(agent_name, result)
            return result

        except Exception as e:
            error_result = {"success": False, "error": str(e), "agent_name": agent_name}
            self.logger.error(f"خطأ في تشغيل الوكيل {agent_name}: {e}")
            return error_result

    def organize_files(self) -> Dict[str, Any]:
        """تنظيم ملفات المشروع"""
        if "file_organizer_agent" in self.active_agents:
            return self.run_agent("file_organizer_agent")
        else:
            return {"success": False, "error": "وكيل تنظيم الملفات غير متاح"}

    def health_check(self) -> Dict[str, Any]:
        """فحص صحة المشروع"""
        health_results = {
            "project_health": "good",
            "issues": [],
            "recommendations": [],
            "agents_status": {},
        }

        # فحص حالة الوكلاء
        for agent_name, agent in self.active_agents.items():
            try:
                status = agent.get_status()
                health_results["agents_status"][agent_name] = status

                if status.get("status") != "initialized":
                    health_results["issues"].append(f"الوكيل {agent_name} غير مهيأ بشكل صحيح")

            except Exception as e:
                health_results["issues"].append(f"خطأ في فحص الوكيل {agent_name}: {e}")

        # تحديد الحالة العامة
        if len(health_results["issues"]) > 0:
            health_results["project_health"] = "needs_attention"

        if len(health_results["issues"]) > 5:
            health_results["project_health"] = "critical"

        return health_results

    def fix_issues(self) -> Dict[str, Any]:
        """إصلاح المشاكل المكتشفة"""
        # هذه دالة أساسية - يمكن تطويرها لاحقاً
        return {
            "success": True,
            "message": "ميزة إصلاح المشاكل قيد التطوير",
            "fixed_issues": [],
        }

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        uptime = datetime.now() - self.start_time

        return {
            "system_name": self.system_name,
            "version": self.version,
            "uptime_seconds": uptime.total_seconds(),
            "project_path": str(self.project_path),
            "project_type": self.project_type,
            "session_id": self.session_id,
            "available_agents": list(self.available_agents.keys()),
            "active_agents": list(self.active_agents.keys()),
            "total_agents": len(self.available_agents),
            "active_agents_count": len(self.active_agents),
        }

    def shutdown(self):
        """إغلاق النظام"""
        self.logger.log_system_event("إغلاق النظام")

        # إنهاء سجل الجلسة
        if hasattr(self, "session_logger"):
            self.session_logger.end_session()

        self.logger.info("تم إغلاق النظام بنجاح")
