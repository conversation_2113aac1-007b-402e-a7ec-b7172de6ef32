{"scan_metadata": {"scan_date": "2025-07-23T07:06:54.144799", "scan_type": "Ultra Comprehensive Wide-Range Scan", "horus_team_consultation": true, "scan_duration": 10.09042739868164}, "system_overview": {"os": "Windows", "os_version": "10.0.26200", "os_release": "11", "architecture": ["64bit", "WindowsPE"], "processor": "Intel64 Family 6 Model 165 Stepping 2, GenuineIntel", "machine": "AMD64", "node_name": "ABO-ELDAHB", "python_version": "3.13.5", "boot_time": "2025-07-23T06:44:23.382946", "memory": {"total": "15.80 GB", "available": "3.65 GB", "used": "12.15 GB", "percentage": "76.9%"}, "cpu": {"physical_cores": 6, "logical_cores": 12, "current_frequency": "2592.00 MHz", "usage_percent": "22.9%"}}, "drives_analysis": {"C:\\": {"device": "C:\\", "mountpoint": "C:\\", "file_system": "NTFS", "total_size": "352.12 GB", "used_size": "325.61 GB", "free_size": "26.51 GB", "usage_percent": "92.5%", "development_tools": [{"tool": "dotnet.exe", "path": "C:\\Program Files\\dotnet\\dotnet.exe"}, {"tool": "git.exe", "path": "C:\\Program Files\\Git\\bin\\git.exe"}, {"tool": "git.exe", "path": "C:\\Program Files\\Git\\cmd\\git.exe"}, {"tool": "git.exe", "path": "C:\\Program Files\\Git\\mingw64\\bin\\git.exe"}, {"tool": "go.exe", "path": "C:\\Program Files\\Go\\bin\\go.exe"}, {"tool": "java.exe", "path": "C:\\Program Files\\Java\\jdk-23\\bin\\java.exe"}, {"tool": "python.exe", "path": "C:\\Program Files\\MySQL\\MySQL Workbench 8.0\\python.exe"}, {"tool": "python.exe", "path": "C:\\Program Files\\pgAdmin 4\\python\\python.exe"}, {"tool": "node.exe", "path": "C:\\Program Files (x86)\\Adobe\\Adobe Creative Cloud Experience\\libs\\node.exe"}, {"tool": "python.exe", "path": "C:\\ProgramData\\sema4ai\\ht\\5a0fee5_a78733b_f612b06d\\python.exe"}, {"tool": "python.exe", "path": "C:\\ProgramData\\sema4ai\\ht\\5a0fee5_c5e81ff_bcd2c8a9\\python.exe"}, {"tool": "python.exe", "path": "C:\\ProgramData\\sema4ai\\ht\\UNMNGED_277c34a_042d209e\\python.exe"}]}, "D:\\": {"device": "D:\\", "mountpoint": "D:\\", "file_system": "NTFS", "total_size": "6.74 GB", "used_size": "0.99 GB", "free_size": "5.75 GB", "usage_percent": "14.7%", "development_tools": []}, "F:\\": {"device": "F:\\", "mountpoint": "F:\\", "file_system": "NTFS", "total_size": "103.31 GB", "used_size": "44.95 GB", "free_size": "58.36 GB", "usage_percent": "43.5%", "development_tools": []}, "M:\\": {"device": "M:\\", "mountpoint": "M:\\", "file_system": "NTFS", "total_size": "14.65 GB", "used_size": "0.71 GB", "free_size": "13.94 GB", "usage_percent": "4.8%", "development_tools": []}}, "windows_services": {"total_services": 316, "running_services": 141, "stopped_services": 175, "development_related": [{"name": "com.docker.service", "display_name": "Docker Desktop Service", "status": "stopped"}, {"name": "MYSQL80", "display_name": "MYSQL80", "status": "running"}, {"name": "postgresql-x64-17", "display_name": "postgresql-x64-17 - PostgreSQL Server 17", "status": "running"}], "security_related": [{"name": "MDCoreSvc", "display_name": "Microsoft Defender Core Service", "status": "running"}, {"name": "mpssvc", "display_name": "Windows Defender Firewall", "status": "running"}, {"name": "rsDNSClientSvc", "display_name": "Reason Security DNS Client Service", "status": "stopped"}, {"name": "SamSs", "display_name": "Security Accounts Manager", "status": "running"}, {"name": "SecurityHealthService", "display_name": "Windows Security Service", "status": "stopped"}, {"name": "WdNisSvc", "display_name": "Microsoft Defender Antivirus Network Inspection Service", "status": "running"}, {"name": "WinDefend", "display_name": "Microsoft Defender Antivirus Service", "status": "running"}, {"name": "wscsvc", "display_name": "Security Center", "status": "running"}], "cloud_related": [{"name": "AzureAttestService", "display_name": "AzureAttestService", "status": "running"}, {"name": "GoogleChromeBetaElevationService", "display_name": "Google Chrome Beta Elevation Service (GoogleChromeBetaElevationService)", "status": "stopped"}, {"name": "GoogleChromeElevationService", "display_name": "Google Chrome Elevation Service (GoogleChromeElevationService)", "status": "stopped"}, {"name": "GoogleUpdaterInternalService138.0.7156.2", "display_name": "Google Updater Internal Service (GoogleUpdaterInternalService138.0.7156.2)", "status": "stopped"}, {"name": "GoogleUpdaterInternalService140.0.7273.0", "display_name": "Google Updater Internal Service (GoogleUpdaterInternalService140.0.7273.0)", "status": "stopped"}, {"name": "GoogleUpdaterService138.0.7156.2", "display_name": "Google Updater Service (GoogleUpdaterService138.0.7156.2)", "status": "stopped"}, {"name": "GoogleUpdaterService140.0.7273.0", "display_name": "Google Updater Service (GoogleUpdaterService140.0.7273.0)", "status": "stopped"}, {"name": "gupdate", "display_name": "خدمة Google Update (gupdate)", "status": "stopped"}, {"name": "gupdatem", "display_name": "خدمة Google Update (gupdatem)", "status": "stopped"}, {"name": "OneDrive Updater Service", "display_name": "OneDrive Updater Service", "status": "stopped"}, {"name": "CloudBackupRestoreSvc_da5e4", "display_name": "CloudBackupRestoreSvc_da5e4", "status": "stopped"}]}, "registry_analysis": {"installed_programs": {}, "development_tools": {"Docker Desktop": {"version": "4.43.2", "install_location": "C:\\Program Files\\Docker\\Docker", "registry_key": "<PERSON><PERSON> Des<PERSON>"}, "Git": {"version": "2.49.0", "install_location": "C:\\Program Files\\Git\\", "registry_key": "Git_is1"}, "Microsoft Visual Studio 2010 Tools for Office Runtime (x64)": {"version": "10.0.60922", "install_location": "", "registry_key": "{610487D9-3460-328A-9333-219D43A75CC5}"}, "PostgreSQL 17 ": {"version": "17.4-1", "install_location": "C:\\Program Files\\PostgreSQL\\17", "registry_key": "PostgreSQL 17"}, "Python 3.13.5 Documentation (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{011563DC-4658-4F00-838A-BE003808BD5B}"}, "Microsoft Visual C++ 2010  x64 Redistributable - 10.0.40219": {"version": "10.0.40219", "install_location": "", "registry_key": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}"}, "Python 3.13.5 pip Bootstrap (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{20342031-463F-47B0-B7D8-56D0BA99FCCC}"}, "GitHub CLI": {"version": "2.69.0", "install_location": "", "registry_key": "{2988DB68-7354-4318-9AB4-002FDCF5F4CE}"}, "MySQL Shell": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Shell 8.0", "registry_key": "{2D35FC76-5688-4C8A-B790-94C3117F7F84}"}, "vs_communityx64msi": {"version": "17.14.36025", "install_location": "", "registry_key": "{3873679C-FA03-4101-97E9-107D67C568B8}"}, "Python 3.13.5 Executables (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{3B9AEBC3-8AFD-40C4-8204-A3CEA6F21058}"}, "Microsoft Visual C++ 2008 Redistributable - x64 9.0.30729.6161": {"version": "9.0.30729.6161", "install_location": "", "registry_key": "{5FCE6D76-F5DC-37AB-B2B8-22AB8CEDB1D4}"}, "Microsoft Visual Studio Installer": {"version": "3.14.2082.42463", "install_location": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\"", "registry_key": "{6F320B93-EE3C-4826-85E0-ADF79F8D4C61}"}, "Microsoft Visual C++ 2008 Redistributable - x64 9.0.30729.17": {"version": "9.0.30729", "install_location": "", "registry_key": "{8220EEFE-38CD-377E-8595-13398D740ACE}"}, "Microsoft Visual Studio Tools for Applications 2019 x64 Hosting Support": {"version": "16.0.31110", "install_location": "", "registry_key": "{8E7A3713-551D-333A-9271-10EF4D77A80F}"}, "Java(TM) SE Development Kit 23.0.2 (64-bit)": {"version": "********", "install_location": "C:\\Program Files\\Java\\jdk-23\\", "registry_key": "{8EFDE921-88A2-5D0A-A920-0AB07B2A3181}"}, "Python 3.13.5 Core Interpreter (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{970C5B01-B0C6-4312-82D8-809E04BACDD5}"}, "MySQL Server 8.0": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\", "registry_key": "{A12B0204-7560-4821-9D34-A5F170287309}"}, "MySQL Router 8.0": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Router 8.0\\", "registry_key": "{A1C88C50-9EE5-45B8-88A3-ADFFC5EA5367}"}, "Python 3.13.5 Tcl/Tk Support (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{AC3F660A-8ABD-4E2A-9C2E-D9C1F734D7C9}"}, "Microsoft Visual C++ 2005 Redistributable (x64)": {"version": "8.0.61000", "install_location": "", "registry_key": "{ad8a2fa1-06e7-4b0d-927d-6e54b3d31028}"}, "Python 3.13.5 Development Libraries (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{B40BFBCD-3D0F-4608-B49A-3AA1030370A0}"}, "MySQL Workbench 8.0 CE": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Workbench 8.0", "registry_key": "{CB651F1E-4B3C-496C-88DA-54499A6F8C6B}"}, "Python 3.13.5 Standard Library (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{CCD10098-5E72-48F8-834E-AF2364A86653}"}, "Python 3.13.5 Add to Path (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{D556EDC0-3FAA-4124-B6EF-B3F5D1839EB3}"}, "Node.js": {"version": "22.14.0", "install_location": "", "registry_key": "{EA4A8E4A-F5BF-454F-B107-666BE3F30608}"}, "Visual Studio Community 2022": {"version": "17.14.8", "install_location": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community", "registry_key": "4b050061"}, "Visual Studio Build Tools 2022 (2)": {"version": "17.14.8", "install_location": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools", "registry_key": "6e64f870"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40664": {"version": "12.0.40664.0", "install_location": "Unknown", "registry_key": "{042d26ef-3dbe-4c25-95d3-4c1b11b235a7}"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.30501": {"version": "12.0.30501.0", "install_location": "Unknown", "registry_key": "{050d4fc8-5d48-4b8f-8972-47c82c46020f}"}, "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.44.35211": {"version": "14.44.35211.0", "install_location": "Unknown", "registry_key": "{0b5169e3-39da-4313-808e-1f9c0407f3bf}"}, "MySQL Examples and Samples": {"version": "8.0.42", "install_location": "C:\\Program Files (x86)\\MySQL\\Samples and Examples 8.0", "registry_key": "{1C037A10-844B-462A-8E7A-6FE64A7FAC46}"}, "vs_communitymsires": {"version": "17.14.36015", "install_location": "", "registry_key": "{2817364B-A3DE-472C-BF19-F19B6997061F}"}, "vs_githubprotocolhandlermsi": {"version": "17.14.36015", "install_location": "", "registry_key": "{29297AFE-9D24-4DFE-ACAF-D90090D905CF}"}, "Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030": {"version": "11.0.61030.0", "install_location": "Unknown", "registry_key": "{33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}"}, "MySQL Installer": {"version": "1.6.14.0", "install_location": "", "registry_key": "{3E861290-D963-4AC5-9A0B-93226D47F0F8}"}, "Microsoft Visual Studio Setup WMI Provider": {"version": "3.12.2140.44225", "install_location": "", "registry_key": "{49727420-70BA-4495-9405-31F8D711CB5A}"}, "Universal CRT Redistributable": {"version": "10.1.22621.5040", "install_location": "", "registry_key": "{6B9089CB-51B0-1F55-CED4-B6E0DE9FA6F2}"}, "Windows SDK Redistributables": {"version": "10.1.26100.4188", "install_location": "", "registry_key": "{633414D2-A28B-536C-ECBF-E6CD7820447E}"}, "Python Launcher": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{69012533-AE90-48A1-B7E5-7124E81C4B40}"}, "vs_communitysharedmsi": {"version": "17.14.36025", "install_location": "", "registry_key": "{71498EE6-F94E-4061-9DD5-55925CA8A74F}"}, "WPT Redistributables": {"version": "10.1.26100.3916", "install_location": "", "registry_key": "{7530A8A0-7EE1-AFF7-4BCA-D60EAFEEA63B}"}, "MySQL Documents": {"version": "8.0.42", "install_location": "C:\\Program Files (x86)\\MySQL\\MySQL Documentation 8.0", "registry_key": "{79936855-A2A1-4DD2-9CCC-7CF5962ADDCF}"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.21005": {"version": "12.0.21005.1", "install_location": "Unknown", "registry_key": "{7f51bdb9-ee21-49ee-94d6-90afc321780e}"}, "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664": {"version": "12.0.40664.0", "install_location": "Unknown", "registry_key": "{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}"}, "SDK ARM64 Redistributables": {"version": "10.1.26100.4188", "install_location": "", "registry_key": "{BF29C4FF-249B-980F-0978-3B1B66D54E48}"}, "vcpp_crt.redist.clickonce": {"version": "14.44.35211", "install_location": "", "registry_key": "{C7FC6DE7-5C06-4311-837A-E0D774618D17}"}, "Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030": {"version": "11.0.61030.0", "install_location": "Unknown", "registry_key": "{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}"}, "Microsoft Visual Studio Setup Configuration": {"version": "3.12.2140.44225", "install_location": "", "registry_key": "{D07657AA-968C-4629-BD6C-1B52AF825EA7}"}, "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.44.35211": {"version": "14.44.35211.0", "install_location": "Unknown", "registry_key": "{d8bbe9f9-7c5b-42c6-b715-9ee898a2e515}"}, "Microsoft Visual Studio Tools for Applications 2019 x86 Hosting Support": {"version": "16.0.31110", "install_location": "", "registry_key": "{E7A0CD34-1F9B-3496-ADB3-2F180D302F6A}"}, "Microsoft Visual C++ 2010  x86 Redistributable - 10.0.40219": {"version": "10.0.40219", "install_location": "", "registry_key": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}"}, "Microsoft Visual Studio Tools for Applications 2019": {"version": "16.0.31110", "install_location": "Unknown", "registry_key": "{f3fbabb4-bcfb-45eb-8fff-9b784fd68c38}"}, "GitHub": {"version": "1.0", "install_location": "Unknown", "registry_key": "f192d94383e08e536b73cea4723c5d6e"}, "GitHub Desktop": {"version": "3.4.22-beta1", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop", "registry_key": "GitHubDesktop"}, "Python 3.13.5 (64-bit)": {"version": "3.13.5150.0", "install_location": "Unknown", "registry_key": "{2a612b01-6a34-408a-b31b-2fa0f048823f}"}, "Microsoft Visual Studio Code (User)": {"version": "1.102.1", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\", "registry_key": "{771FD6B0-FA20-440A-A002-3B3BAC16DC50}_is1"}}, "startup_programs": {}, "environment_variables": {}, "uninstall_entries": 399}, "network_analysis": {"network_interfaces": {"Ethernet": [{"family": "-1", "address": "34-73-5A-AB-A0-9B", "netmask": null, "broadcast": null}, {"family": "2", "address": "*************", "netmask": "*************", "broadcast": null}, {"family": "23", "address": "fdb8:8d7:d810:f900:3c11:593:9f7:986a", "netmask": null, "broadcast": null}, {"family": "23", "address": "fdb8:8d7:d810:f900:d5a3:6e8:2a1b:37b4", "netmask": null, "broadcast": null}, {"family": "23", "address": "fe80::732c:2849:2f14:72a2", "netmask": null, "broadcast": null}], "Local Area Connection": [{"family": "-1", "address": "00-FF-36-08-94-55", "netmask": null, "broadcast": null}, {"family": "2", "address": "***************", "netmask": "***********", "broadcast": null}, {"family": "23", "address": "fe80::a21c:85a0:570b:6e4c", "netmask": null, "broadcast": null}], "Local Area Connection* 1": [{"family": "-1", "address": "18-26-49-9F-06-EC", "netmask": null, "broadcast": null}, {"family": "2", "address": "**************", "netmask": "***********", "broadcast": null}, {"family": "23", "address": "fe80::e15b:eb0:4af5:369a", "netmask": null, "broadcast": null}], "Local Area Connection* 10": [{"family": "-1", "address": "1A-26-49-9F-06-EB", "netmask": null, "broadcast": null}, {"family": "2", "address": "***************", "netmask": "***********", "broadcast": null}, {"family": "23", "address": "fe80::a299:33c0:2970:2dc2", "netmask": null, "broadcast": null}], "Wi-Fi": [{"family": "-1", "address": "9A-94-70-C1-34-AB", "netmask": null, "broadcast": null}, {"family": "2", "address": "************", "netmask": "*************", "broadcast": null}, {"family": "23", "address": "fdb8:8d7:d810:f900:542a:bbd4:a48f:cd68", "netmask": null, "broadcast": null}, {"family": "23", "address": "fdb8:8d7:d810:f900:e85d:c934:a41e:4fcd", "netmask": null, "broadcast": null}, {"family": "23", "address": "fe80::fa36:3635:1dad:a023", "netmask": null, "broadcast": null}], "vEthernet (WSL (Hyper-V firewall))": [{"family": "-1", "address": "00-15-5D-0E-E5-FE", "netmask": null, "broadcast": null}, {"family": "2", "address": "***********", "netmask": "*************", "broadcast": null}, {"family": "23", "address": "fe80::fc7:fa17:bbf3:329d", "netmask": null, "broadcast": null}], "Loopback Pseudo-Interface 1": [{"family": "2", "address": "127.0.0.1", "netmask": "*********", "broadcast": null}, {"family": "23", "address": "::1", "netmask": null, "broadcast": null}]}, "active_connections": [{"local_address": "************:56729", "remote_address": "**************:443", "status": "ESTABLISHED", "pid": 7260}, {"local_address": "************:55470", "remote_address": "***********:443", "status": "ESTABLISHED", "pid": 23432}, {"local_address": "127.0.0.1:49673", "remote_address": "127.0.0.1:49672", "status": "ESTABLISHED", "pid": 6532}, {"local_address": "************:56823", "remote_address": "**************:443", "status": "ESTABLISHED", "pid": 7260}, {"local_address": "************:57065", "remote_address": "************:443", "status": "ESTABLISHED", "pid": 23092}, {"local_address": "*************:50226", "remote_address": "*************:16451", "status": "ESTABLISHED", "pid": 5160}], "listening_ports": [{"address": "::1:49669", "pid": 5180}, {"address": ":::3306", "pid": 6532}, {"address": "0.0.0.0:33060", "pid": 6532}, {"address": ":::16452", "pid": 5160}, {"address": "::1:5678", "pid": 13276}, {"address": "0.0.0.0:17001", "pid": 19268}, {"address": "0.0.0.0:5432", "pid": 6744}, {"address": ":::49666", "pid": 1320}, {"address": "0.0.0.0:16450", "pid": 19268}, {"address": "127.0.0.1:11434", "pid": 15508}, {"address": "127.0.0.1:56913", "pid": 23432}, {"address": ":::16450", "pid": 19268}, {"address": "0.0.0.0:5040", "pid": 11152}, {"address": "::1:50151", "pid": 16576}, {"address": ":::49667", "pid": 3140}, {"address": "0.0.0.0:16452", "pid": 5160}, {"address": "127.0.0.1:52304", "pid": 16576}, {"address": "127.0.0.1:6443", "pid": 21936}, {"address": ":::63380", "pid": 23432}, {"address": ":::5678", "pid": 21936}, {"address": "0.0.0.0:49664", "pid": 1184}, {"address": "127.0.0.1:64120", "pid": 22244}, {"address": "0.0.0.0:16453", "pid": 19268}, {"address": "0.0.0.0:16451", "pid": 5160}, {"address": "0.0.0.0:49665", "pid": 1080}, {"address": "0.0.0.0:445", "pid": 4}, {"address": "0.0.0.0:63380", "pid": 23432}, {"address": ":::17001", "pid": 19268}, {"address": ":::57070", "pid": 23432}, {"address": ":::49688", "pid": 1156}, {"address": ":::49665", "pid": 1080}, {"address": ":::16453", "pid": 19268}, {"address": "0.0.0.0:57070", "pid": 23432}, {"address": ":::33060", "pid": 6532}, {"address": ":::49664", "pid": 1184}, {"address": "***********:139", "pid": 4}, {"address": "0.0.0.0:5678", "pid": 21936}, {"address": ":::445", "pid": 4}, {"address": "0.0.0.0:49688", "pid": 1156}, {"address": "************:139", "pid": 4}, {"address": ":::135", "pid": 1568}, {"address": "0.0.0.0:3306", "pid": 6532}, {"address": "0.0.0.0:49668", "pid": 3688}, {"address": "127.0.0.1:1434", "pid": 2276}, {"address": "0.0.0.0:49667", "pid": 3140}, {"address": "127.0.0.1:50151", "pid": 16576}, {"address": ":::5357", "pid": 4}, {"address": ":::50143", "pid": 16372}, {"address": "127.0.0.1:56812", "pid": 4204}, {"address": "::1:52304", "pid": 16576}, {"address": ":::49668", "pid": 3688}, {"address": "0.0.0.0:50143", "pid": 16372}, {"address": "0.0.0.0:135", "pid": 1568}, {"address": "0.0.0.0:5357", "pid": 4}, {"address": ":::5432", "pid": 6744}, {"address": "0.0.0.0:49666", "pid": 1320}, {"address": ":::16451", "pid": 5160}, {"address": "::1:1434", "pid": 2276}], "network_stats": {"bytes_sent": "14.09 MB", "bytes_received": "19.78 MB", "packets_sent": 96671, "packets_received": 31068}}, "processes_analysis": {"total_processes": 291, "development_processes": [{"pid": 2096, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.8424991210888454}, {"pid": 2472, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.6441646094350629}, {"pid": 2848, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.6172177729357178}, {"pid": 4972, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.6327435936069356}, {"pid": 5388, "name": "mysqld.exe", "cpu_percent": 0.0, "memory_percent": 0.008233755597022132}, {"pid": 5428, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.8991695738343319}, {"pid": 6000, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.009030570654798469}, {"pid": 6412, "name": "Docker Desktop.exe", "cpu_percent": 0.0, "memory_percent": 0.07381404944309872}, {"pid": 6532, "name": "mysqld.exe", "cpu_percent": 0.0, "memory_percent": 0.02530491456210908}, {"pid": 6552, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.010382741661934067}, {"pid": 6560, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.010286158018567238}, {"pid": 6744, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.011010535343818455}, {"pid": 7304, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.009561780693316025}, {"pid": 7316, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.014077066020715259}, {"pid": 7332, "name": "postgres.exe", "cpu_percent": 0.0, "memory_percent": 0.012749040924421367}, {"pid": 11656, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 3.26855951290937}, {"pid": 12220, "name": "com.docker.build.exe", "cpu_percent": 0.0, "memory_percent": 0.09301004856225589}, {"pid": 12728, "name": "python.exe", "cpu_percent": 0.0, "memory_percent": 0.023807868089923235}, {"pid": 13840, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.6705077981633654}, {"pid": 14632, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.9675266474272048}, {"pid": 15444, "name": "com.docker.backend.exe", "cpu_percent": 0.0, "memory_percent": 0.16568924019579437}, {"pid": 16864, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 1.3294255591227113}, {"pid": 19524, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 1.967601982669031}, {"pid": 21204, "name": "python.exe", "cpu_percent": 0.0, "memory_percent": 0.16151199762017904}, {"pid": 21268, "name": "Docker Desktop.exe", "cpu_percent": 0.0, "memory_percent": 0.5106618683912643}, {"pid": 21400, "name": "MySQLWorkbench.exe", "cpu_percent": 0.0, "memory_percent": 0.13352688695464046}, {"pid": 21840, "name": "Docker Desktop.exe", "cpu_percent": 0.0, "memory_percent": 0.4152613746556793}, {"pid": 21936, "name": "com.docker.backend.exe", "cpu_percent": 0.0, "memory_percent": 0.8523023608905784}, {"pid": 22244, "name": "java.exe", "cpu_percent": 0.0, "memory_percent": 2.0805082617648534}, {"pid": 22344, "name": "Docker Desktop.exe", "cpu_percent": 0.0, "memory_percent": 0.7347359210023064}, {"pid": 23092, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.3026448464899572}, {"pid": 23432, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 5.294522162082807}, {"pid": 24272, "name": "python.exe", "cpu_percent": 0.0, "memory_percent": 0.1773034233106555}, {"pid": 24600, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 0.6586280100292455}, {"pid": 25684, "name": "Code.exe", "cpu_percent": 0.0, "memory_percent": 3.6567291756006535}, {"pid": 26036, "name": "code-tunnel.exe", "cpu_percent": 0.0, "memory_percent": 0.13053279401026877}], "high_cpu_processes": [], "high_memory_processes": [{"pid": 20528, "name": "vmmemWSL", "memory_percent": 12.447506789830129}, {"pid": 23432, "name": "Code.exe", "memory_percent": 5.294522162082807}, {"pid": 3436, "name": "MemCompression", "memory_percent": 4.822928377433425}, {"pid": 25684, "name": "Code.exe", "memory_percent": 3.6567291756006535}, {"pid": 11656, "name": "Code.exe", "memory_percent": 3.26855951290937}, {"pid": 5352, "name": "MsMpEng.exe", "memory_percent": 2.3210739714807818}, {"pid": 22244, "name": "java.exe", "memory_percent": 2.0805082617648534}], "suspicious_processes": []}, "environment_variables": {"total_variables": 71, "development_variables": {"GOPATH": "C:\\Users\\<USER>\\go"}, "path_analysis": {"total_paths": 85, "development_paths": ["C:\\Program Files\\Common Files\\Oracle\\Java\\javapath", "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin", "C:\\Program Files\\nodejs\\", "C:\\Program Files\\GitHub CLI\\", "C:\\Program Files\\Git\\cmd", "C:\\Program Files\\Go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\", "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin", "C:\\Users\\<USER>\\go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Program Files\\MySQL\\MySQL Workbench 8.0 CE", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\", "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\", "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath", "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin", "C:\\Program Files\\nodejs\\", "C:\\Program Files\\GitHub CLI\\", "C:\\Program Files\\Docker\\Docker\\resources\\bin", "C:\\Program Files\\Git\\cmd", "C:\\Program Files\\Go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin", "C:\\Users\\<USER>\\go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Program Files\\MySQL\\MySQL Workbench 8.0 CE", "C:\\Users\\<USER>\\AppData\\Local\\Google\\Cloud SDK\\google-cloud-sdk\\bin", "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\scripts\\noConfigScripts", "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"], "duplicate_paths": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\", "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath", "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin", "C:\\Program Files\\nodejs\\", "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR", "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common", "C:\\Program Files\\dotnet\\", "C:\\Program Files\\GitHub CLI\\", "C:\\Program Files\\Git\\cmd", "C:\\Program Files\\Go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps", "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin", "C:\\Users\\<USER>\\.lmstudio\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin", "C:\\Users\\<USER>\\.dotnet\\tools", "C:\\Users\\<USER>\\go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Program Files\\MySQL\\MySQL Workbench 8.0 CE", "C:\\Program Files\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\OneDrive", "C:\\Windows\\System32\\WindowsPowerShell\\v1.0", "C:\\Users\\<USER>\\.dotnet\\tools", "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"], "invalid_paths": ["C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\.dotnet\\tools", "C:\\Users\\<USER>\\go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Program Files\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\OneDrive", "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin", "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp", "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.1\\", "C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\.dotnet\\tools", "C:\\Users\\<USER>\\go\\bin", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Program Files\\PyCharm Community Edition 2024.3.3\\bin", "C:\\Users\\<USER>\\OneDrive", "C:\\Users\\<USER>\\.dotnet\\tools", "C:\\xampp\\php", "C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin"]}, "system_variables": {}, "user_variables": {}}, "installed_programs": {}, "hidden_tools": {}, "security_analysis": {}, "cloud_tools": {}, "virtualization": {"virtualization_detected": false, "virtualization_type": "Unknown", "docker_info": {"installed": true, "version": "Docker version 28.3.2, build 578ccf6", "running_containers": 1}, "wsl_info": {"installed": true, "distributions": "W\u0000i\u0000n\u0000d\u0000o\u0000w\u0000s\u0000 \u0000S\u0000u\u0000b\u0000s\u0000y\u0000s\u0000t\u0000e\u0000m\u0000 \u0000f\u0000o\u0000r\u0000 \u0000L\u0000i\u0000n\u0000u\u0000x\u0000 \u0000D\u0000i\u0000s\u0000t\u0000r\u0000i\u0000b\u0000u\u0000t\u0000i\u0000o\u0000n\u0000s\u0000:\u0000\n\u0000\n\u0000d\u0000o\u0000c\u0000k\u0000e\u0000r\u0000-\u0000d\u0000e\u0000s\u0000k\u0000t\u0000o\u0000p\u0000 \u0000(\u0000D\u0000e\u0000f\u0000a\u0000u\u0000l\u0000t\u0000)\u0000\n\u0000\n\u0000"}, "hyperv_info": {"enabled": false}, "vmware_info": {}}, "development_environments": {}, "statistics": {"scan_duration_seconds": 10.09, "scan_duration_formatted": "0m 10s", "total_drives_scanned": 4, "total_services_analyzed": 316, "total_processes_analyzed": 291, "total_registry_entries": 399, "total_environment_variables": 71, "development_tools_found": 57, "network_interfaces": 7}}