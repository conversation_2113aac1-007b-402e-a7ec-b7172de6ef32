# 🏺 Anubis API Service - Isolated Container
FROM python:3.11-slim

# معلومات الصورة
LABEL maintainer="Anubis System"
LABEL version="1.0.0"
LABEL description="Anubis API Service - Isolated"

# إعداد المستخدم غير المميز
RUN groupadd -r anubis && useradd -r -g anubis anubis

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# إنشاء مجلدات العمل
RUN mkdir -p /app/data /app/logs /app/config /app/src
RUN chown -R anubis:anubis /app

# نسخ متطلبات Python
COPY requirements.txt /app/
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

# نسخ الكود
COPY . /app/src/
RUN chown -R anubis:anubis /app

# التبديل للمستخدم غير المميز
USER anubis

# مجلد العمل
WORKDIR /app/src

# المنفذ
EXPOSE 8080

# فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# نقطة الدخول
ENTRYPOINT ["python", "main.py"]
