#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مساعد Gemini CLI لنظام أنوبيس
Anubis Gemini CLI Helper
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path
from datetime import datetime

class AnubisGeminiHelper:
    """مساعد Gemini CLI"""
    
    def __init__(self):
        self.gemini_available = False
        self.help_file = "anubis_project_organization_gemini_request.md"
        
    def check_gemini_cli(self):
        """فحص توفر Gemini CLI"""
        commands_to_try = [
            ['gemini', '--version'],
            ['gemini-cli', '--version'],
            ['google-gemini', '--version']
        ]
        
        for cmd in commands_to_try:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ تم العثور على Gemini CLI: {' '.join(cmd)}")
                    print(f"📋 الإصدار: {result.stdout.strip()}")
                    self.gemini_available = True
                    return cmd[0]
            except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
                continue
        
        print("❌ Gemini CLI غير متوفر")
        return None
    
    def install_gemini_cli(self):
        """تعليمات تثبيت Gemini CLI"""
        print("\n📦 تعليمات تثبيت Gemini CLI:")
        print("=" * 40)
        
        print("🔧 الطرق المتاحة:")
        print("1. **npm (الأسرع):**")
        print("   npm install -g @google/generative-ai-cli")
        print("   # أو")
        print("   npm install -g gemini-cli")
        
        print("\n2. **pip:**")
        print("   pip install google-generativeai-cli")
        
        print("\n3. **من المصدر:**")
        print("   git clone https://github.com/google/generative-ai-cli")
        print("   cd generative-ai-cli")
        print("   npm install -g .")
        
        print("\n4. **تحميل مباشر:**")
        print("   https://github.com/google/generative-ai-cli/releases")
        
        print("\n🔑 إعداد API Key:")
        print("1. احصل على API Key من: https://makersuite.google.com/app/apikey")
        print("2. قم بتعيينه:")
        print("   export GEMINI_API_KEY=your_api_key_here")
        print("   # أو في Windows:")
        print("   set GEMINI_API_KEY=your_api_key_here")
        
        return False
    
    def try_gemini_chat(self, gemini_cmd):
        """محاولة تشغيل Gemini CLI"""
        if not Path(self.help_file).exists():
            print(f"❌ ملف المساعدة غير موجود: {self.help_file}")
            return False
        
        print(f"🚀 تشغيل Gemini CLI مع ملف المساعدة...")
        
        # طرق مختلفة لتشغيل Gemini
        commands_to_try = [
            [gemini_cmd, 'chat', '--file', self.help_file],
            [gemini_cmd, 'chat', '-f', self.help_file],
            [gemini_cmd, '--file', self.help_file],
            [gemini_cmd, 'ask', '--file', self.help_file]
        ]
        
        for cmd in commands_to_try:
            try:
                print(f"⏳ محاولة: {' '.join(cmd)}")
                
                # تشغيل الأمر
                result = subprocess.run(cmd, timeout=60)
                
                if result.returncode == 0:
                    print("✅ تم تشغيل Gemini CLI بنجاح!")
                    return True
                else:
                    print(f"⚠️  فشل الأمر مع كود الخروج: {result.returncode}")
                    
            except subprocess.TimeoutExpired:
                print("⏰ انتهت مهلة الانتظار")
            except Exception as e:
                print(f"❌ خطأ: {e}")
        
        return False
    
    def open_gemini_web(self):
        """فتح Gemini في المتصفح"""
        print("🌐 فتح Gemini في المتصفح...")
        
        try:
            # قراءة محتوى ملف المساعدة
            with open(self.help_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فتح المتصفح
            webbrowser.open("https://gemini.google.com/")
            
            print("✅ تم فتح Gemini في المتصفح")
            print(f"📋 انسخ محتوى الملف: {self.help_file}")
            print("📝 والصقه في Gemini للحصول على المساعدة")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فتح المتصفح: {e}")
            return False
    
    def show_manual_steps(self):
        """عرض الخطوات اليدوية"""
        print("\n📋 الخطوات اليدوية للحصول على مساعدة Gemini:")
        print("=" * 60)
        
        print("1. **افتح Gemini:**")
        print("   🌐 https://gemini.google.com/")
        
        print(f"\n2. **انسخ محتوى الملف:**")
        print(f"   📁 {Path(self.help_file).absolute()}")
        
        print("\n3. **الصق المحتوى في Gemini**")
        
        print("\n4. **اطلب المساعدة بهذا النص:**")
        print('   "يرجى مساعدتي في حل مشكلة Docker هذه خطوة بخطوة"')
        
        print("\n5. **اتبع التعليمات المقترحة**")
        
        # عرض جزء من المحتوى
        try:
            with open(self.help_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 معاينة المحتوى ({len(content)} حرف):")
            print("-" * 50)
            print(content[:500] + "..." if len(content) > 500 else content)
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
    
    def create_quick_diagnosis_script(self):
        """إنشاء سكريبت تشخيص سريع"""
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 تشخيص سريع لمشكلة Docker - أنوبيس
Quick Docker Diagnosis - Anubis
"""

import subprocess
import json

def run_command(cmd, description):
    """تشغيل أمر وعرض النتيجة"""
    print(f"\\n🔍 {description}:")
    print("-" * 40)
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ خطأ: {result.stderr}")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

def main():
    print("🏺 تشخيص سريع لمشكلة Docker")
    print("=" * 50)
    
    # فحص حالة الحاويات
    run_command(['docker', 'ps', '-a'], "حالة جميع الحاويات")
    
    # فحص سجلات API
    run_command(['docker', 'logs', 'anubis-api-isolated', '--tail', '20'], "آخر 20 سطر من سجلات API")
    
    # فحص الشبكة
    run_command(['docker', 'network', 'ls'], "الشبكات المتاحة")
    
    # فحص الأحجام
    run_command(['docker', 'volume', 'ls'], "الأحجام المتاحة")
    
    # فحص استخدام الموارد
    run_command(['docker', 'stats', '--no-stream'], "استخدام الموارد")

if __name__ == "__main__":
    main()
'''
        
        script_file = "anubis_quick_docker_diagnosis.py"
        try:
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)
            print(f"✅ تم إنشاء سكريبت التشخيص: {script_file}")
            print(f"🚀 لتشغيله: python {script_file}")
            return script_file
        except Exception as e:
            print(f"❌ خطأ في إنشاء السكريبت: {e}")
            return None
    
    def run_gemini_assistance(self):
        """تشغيل مساعدة Gemini الكاملة"""
        print("🏺 مساعد Gemini CLI - نظام أنوبيس")
        print("=" * 50)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # فحص توفر Gemini CLI
        gemini_cmd = self.check_gemini_cli()
        
        if gemini_cmd:
            # محاولة تشغيل Gemini CLI
            if self.try_gemini_chat(gemini_cmd):
                print("🎉 تم الحصول على مساعدة Gemini بنجاح!")
                return True
            else:
                print("⚠️  فشل تشغيل Gemini CLI، سنجرب الطرق البديلة...")
        
        # إذا فشل Gemini CLI، جرب المتصفح
        print("\\n🌐 محاولة فتح Gemini في المتصفح...")
        if self.open_gemini_web():
            self.show_manual_steps()
        else:
            # إذا فشل كل شيء، عرض التعليمات
            print("\\n📋 عرض التعليمات اليدوية...")
            if not self.gemini_available:
                self.install_gemini_cli()
            self.show_manual_steps()
        
        # إنشاء سكريبت تشخيص
        print("\\n🛠️ إنشاء أدوات التشخيص...")
        self.create_quick_diagnosis_script()
        
        print("\\n✅ تم إعداد جميع أدوات المساعدة!")
        return True

def main():
    """الدالة الرئيسية"""
    helper = AnubisGeminiHelper()
    helper.run_gemini_assistance()

if __name__ == "__main__":
    main()
