#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ Enhanced File Organizer Agent - وكيل تنظيم الملفات المحسن
ينظم الملفات في دليل المشروع، ويصنفها حسب النوع، ويكتشف التكرارات، ويقدم توصيات لإدارة الملفات.
"""

import hashlib
import json
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent


class EnhancedFileOrganizerAgent(BaseAgent):
    """وكيل تنظيم الملفات المحسن"""

    def get_agent_type(self) -> str:
        return "enhanced_file_organizer"

    def initialize_agent(self):
        """تهيئة وكيل تنظيم الملفات"""
        self.file_categories = {
            'code': ['.py', '.js', '.java', '.c', '.cpp', '.h', '.cs', '.ts', '.tsx', '.html', '.css', '.scss'],  
            'documents': ['.md', '.txt', '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'],
            'data': ['.json', '.xml', '.csv', '.yaml', '.yml', '.sql', '.db', '.sqlite'],
            'images': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.ico'],
            'archives': ['.zip', '.tar', '.gz', '.rar', '.7z'],
            'config': ['.ini', '.cfg', '.conf', '.env'],
            'logs': ['.log']
        }
        self.analysis_results = {}
        self.log_action("تم تهيئة وكيل تنظيم الملفات المحسن")

    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل تنظيم الملفات"""
        try:
            self.log_action("بدء تحليل تنظيم الملفات")

            all_files = self.get_all_project_files()
            categorized_files = self.categorize_files(all_files)
            duplicate_files = self.find_duplicate_files(all_files)
            file_size_analysis = self.analyze_file_sizes(all_files)

            result = {
                "status": "completed",
                "total_files": len(all_files),
                "categorized_files": {k: len(v) for k, v in categorized_files.items()},
                "duplicate_files": duplicate_files,
                "file_size_analysis": file_size_analysis,
                "recommendations": self.get_organization_recommendations(categorized_files, duplicate_files, file_size_analysis),
                "timestamp": datetime.now().isoformat()
            }

            self.analysis_results = result
            self.save_report(result, f"file_organization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            self.log_action("تم إكمال تحليل تنظيم الملفات")
            return result

        except Exception as e:
            self.log_action("خطأ في تحليل تنظيم الملفات", str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def get_all_project_files(self) -> List[Path]:
        """الحصول على جميع الملفات في المشروع"""
        files = [p for p in self.project_path.rglob('*') if p.is_file()]
        self.log_action(f"تم العثور على {len(files)} ملف في المشروع")
        return files

    def categorize_files(self, files: List[Path]) -> Dict[str, List[str]]:
        """تصنيف الملفات حسب النوع"""
        categorized = defaultdict(list)
        ext_to_category = {ext: category for category, exts in self.file_categories.items() for ext in exts}

        for file in files:
            category = ext_to_category.get(file.suffix.lower(), 'other')
            categorized[category].append(str(file.relative_to(self.project_path)))

        return dict(categorized)

    def find_duplicate_files(self, files: List[Path]) -> Dict[str, List[str]]:
        """العثور على الملفات المكررة بناءً على المحتوى"""
        hashes = defaultdict(list)
        for file in files:
            try:
                # تجاهل الملفات الكبيرة جدا لتجنب استهلاك الذاكرة
                if file.stat().st_size > 100 * 1024 * 1024:  # 100MB
                    continue

                hasher = hashlib.md5()
                with open(file, 'rb') as f:
                    buf = f.read(65536)
                    while len(buf) > 0:
                        hasher.update(buf)
                        buf = f.read(65536)
                file_hash = hasher.hexdigest()
                hashes[file_hash].append(str(file.relative_to(self.project_path)))
            except IOError:
                continue

        duplicates = {hash_val: paths for hash_val, paths in hashes.items() if len(paths) > 1}
        self.log_action(f"تم العثور على {len(duplicates)} مجموعة من الملفات المكررة")
        return duplicates

    def analyze_file_sizes(self, files: List[Path]) -> Dict[str, Any]:
        """تحليل أحجام الملفات"""
        total_size = 0
        largest_files = []

        for file in files:
            try:
                size = file.stat().st_size
                total_size += size
                largest_files.append({'path': str(file.relative_to(self.project_path)), 'size': size})
            except FileNotFoundError:
                continue

        # فرز الملفات حسب الحجم وأخذ أكبر 10
        largest_files.sort(key=lambda x: x['size'], reverse=True)

        return {
            "total_project_size_bytes": total_size,
            "total_project_size_mb": round(total_size / (1024 * 1024), 2),
            "average_file_size_bytes": total_size / len(files) if files else 0,
            "largest_files": largest_files[:10]
        }

    def get_organization_recommendations(self, categorized_files, duplicate_files, size_analysis) -> List[str]:
        """الحصول على توصيات لتحسين تنظيم الملفات"""
        recommendations = []

        # توصيات بناءً على التكرارات
        if duplicate_files:
            recommendations.append(f"إزالة الملفات المكررة ({len(duplicate_files)} مجموعة) لتوفير المساحة.")

        # توصيات بناءً على الحجم
        if size_analysis['total_project_size_mb'] > 500:
            recommendations.append("حجم المشروع كبير. ضع في اعتبارك أرشفة الملفات القديمة أو غير المستخدمة.")

        large_files = [f for f in size_analysis['largest_files'] if f['size'] > 50 * 1024 * 1024] # 50MB
        if large_files:
            recommendations.append(f"توجد ملفات كبيرة جدًا ({len(large_files)} ملف) قد تبطئ عمليات Git.")

        # توصيات بناءً على التصنيف
        if len(categorized_files.get('other', [])) > 20:
            recommendations.append("يوجد عدد كبير من الملفات غير المصنفة. قد تحتاج إلى تنظيمها في مجلدات.")

        if len(categorized_files.get('logs', [])) > 10:
            recommendations.append("توجد ملفات سجلات كثيرة. قم بأرشفتها أو حذفها بانتظام.")

        recommendations.append("إنشاء ملف .gitignore لتجاهل الملفات غير الضرورية (مثل .log, .tmp).")
        recommendations.append("استخدام هيكل مجلدات واضح ومتسق (مثل src, data, docs).")

        return recommendations

    def cleanup_duplicates(self, keep_first: bool = True) -> Dict[str, Any]:
        """تنظيف الملفات المكررة"""
        if not self.analysis_results or not self.analysis_results.get('duplicate_files'):
            return {"status": "no_duplicates", "message": "لم يتم العثور على ملفات مكررة أو لم يتم إجراء التحليل."}

        deleted_files = []
        failed_deletions = []

        for hash_val, paths in self.analysis_results['duplicate_files'].items():
            files_to_delete = paths[1:] if keep_first else paths[:-1]
            for file_path_str in files_to_delete:
                try:
                    file_path = self.project_path / file_path_str
                    file_path.unlink()
                    deleted_files.append(file_path_str)
                    self.log_action(f"تم حذف الملف المكرر: {file_path_str}")
                except Exception as e:
                    failed_deletions.append({"path": file_path_str, "error": str(e)})
                    self.log_action(f"فشل حذف الملف المكرر: {file_path_str}", str(e))

        return {
            "status": "completed",
            "deleted_count": len(deleted_files),
            "deleted_files": deleted_files,
            "failed_deletions": failed_deletions
        }

    def organize_into_folders(self, dry_run: bool = True) -> Dict[str, Any]:
        """تنظيم الملفات في مجلدات حسب الفئة"""
        if not self.analysis_results:
             return {"status": "no_analysis", "message": "يجب إجراء التحليل أولاً."}

        moves = []
        errors = []

        categorized_files = self.categorize_files(self.get_all_project_files())

        for category, files in categorized_files.items():
            if category in ['other', 'code']: # لا تنقل ملفات الكود الرئيسية أو غير المعروفة
                continue

            target_dir = self.project_path / category
            if not dry_run:
                target_dir.mkdir(exist_ok=True)

            for file_str in files:
                source_path = self.project_path / file_str
                # تجنب نقل الملفات الموجودة بالفعل في مجلد مناسب
                if source_path.parent.name == category:
                    continue

                dest_path = target_dir / source_path.name
                moves.append({"from": file_str, "to": str(dest_path.relative_to(self.project_path))})

                if not dry_run:
                    try:
                        source_path.rename(dest_path)
                        self.log_action(f"تم نقل {source_path} إلى {dest_path}")
                    except Exception as e:
                        errors.append({"file": file_str, "error": str(e)})
                        self.log_action(f"فشل نقل {source_path}", str(e))

        return {
            "status": "dry_run" if dry_run else "completed",
            "moves_planned": moves,
            "errors": errors
        }