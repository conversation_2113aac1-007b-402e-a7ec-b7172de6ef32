#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 مدير API Keys الآمن لأنوبيس وحورس
Secure API Keys Manager for Anubis and Horus

نظام متقدم لإدارة وتشفير وحماية جميع مفاتيح API
Advanced system for managing, encrypting, and securing all API keys
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import logging
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import secrets
import hashlib

class APIKeysManager:
    """🔐 مدير مفاتيح API الآمن"""
    
    def __init__(self, vault_path: Optional[Path] = None):
        """تهيئة مدير المفاتيح"""
        self.vault_path = vault_path or Path(__file__).parent / "vault"
        self.vault_path.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("APIKeysManager")
        self.encryption_key = None
        self.master_password = None
        
        # مقدمي الخدمات المدعومين
        self.supported_providers = {
            "openai": {
                "name": "OpenAI",
                "description": "OpenAI API keys for GPT models",
                "key_format": "sk-",
                "validation_url": "https://api.openai.com/v1/models"
            },
            "anthropic": {
                "name": "Anthropic",
                "description": "Anthropic API keys for Claude models",
                "key_format": "sk-ant-",
                "validation_url": "https://api.anthropic.com/v1/messages"
            },
            "google": {
                "name": "Google AI",
                "description": "Google AI API keys for Gemini models",
                "key_format": "AIza",
                "validation_url": "https://generativelanguage.googleapis.com/v1/models"
            },
            "github": {
                "name": "GitHub",
                "description": "GitHub API keys for repository access",
                "key_format": "ghp_",
                "validation_url": "https://api.github.com/user"
            },
            "langsmith": {
                "name": "LangSmith",
                "description": "LangSmith API keys for monitoring",
                "key_format": "lsv2_",
                "validation_url": "https://api.smith.langchain.com/info"
            },
            "azure": {
                "name": "Microsoft Azure",
                "description": "Azure API keys for cloud services",
                "key_format": "",
                "validation_url": "https://management.azure.com/subscriptions"
            },
            "aws": {
                "name": "Amazon Web Services",
                "description": "AWS access keys for cloud services",
                "key_format": "AKIA",
                "validation_url": "https://sts.amazonaws.com/"
            },
            "google_cloud": {
                "name": "Google Cloud",
                "description": "Google Cloud service account keys",
                "key_format": "",
                "validation_url": "https://cloudresourcemanager.googleapis.com/v1/projects"
            },
            "openrouter": {
                "name": "OpenRouter",
                "description": "OpenRouter API keys for multiple AI models",
                "key_format": "sk-or-v1-",
                "validation_url": "https://openrouter.ai/api/v1/models"
            },
            "huggingface": {
                "name": "Hugging Face",
                "description": "Hugging Face API tokens for model access",
                "key_format": "hf_",
                "validation_url": "https://huggingface.co/api/whoami"
            },
            "deepseek": {
                "name": "DeepSeek",
                "description": "DeepSeek AI API keys",
                "key_format": "sk-",
                "validation_url": "https://api.deepseek.com/v1/models"
            },
            "together_ai": {
                "name": "Together.ai",
                "description": "Together.ai API for collaborative AI",
                "key_format": "",
                "validation_url": "https://api.together.ai/models"
            },
            "continue_extension": {
                "name": "Continue Extension",
                "description": "Continue VS Code extension API keys",
                "key_format": "con-",
                "validation_url": ""
            },
            "nebius_studio": {
                "name": "Nebius Studio",
                "description": "Nebius Studio AI platform",
                "key_format": "eyJ",
                "validation_url": "https://studio.nebius.com/api/v1/models"
            },
            "apidog": {
                "name": "Apidog",
                "description": "Apidog API testing platform",
                "key_format": "APS-",
                "validation_url": ""
            },
            "kero_ide": {
                "name": "Kero IDE",
                "description": "Kero IDE integration",
                "key_format": "sk-",
                "validation_url": ""
            },
            "n8n": {
                "name": "n8n",
                "description": "n8n automation platform",
                "key_format": "sk-",
                "validation_url": ""
            }
        }
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            "keys_stored": 0,
            "keys_accessed": 0,
            "keys_rotated": 0,
            "security_events": 0
        }
        
        self.logger.info("🔐 تم تهيئة مدير API Keys")
    
    async def initialize(self):
        """تهيئة نظام إدارة المفاتيح"""
        try:
            # إنشاء مجلدات الخزنة
            (self.vault_path / "encrypted").mkdir(exist_ok=True)
            (self.vault_path / "config").mkdir(exist_ok=True)
            (self.vault_path / "logs").mkdir(exist_ok=True)
            (self.vault_path / "backups").mkdir(exist_ok=True)
            
            # تحميل أو إنشاء مفتاح التشفير الرئيسي
            await self._setup_encryption()
            
            # تحميل المفاتيح الموجودة
            await self._load_existing_keys()
            
            # إعداد دوران المفاتيح التلقائي
            await self._setup_key_rotation()
            
            self.logger.info("✅ تم تهيئة نظام إدارة المفاتيح بنجاح")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة مدير المفاتيح: {e}")
            raise
    
    async def _setup_encryption(self):
        """إعداد نظام التشفير"""
        master_key_file = self.vault_path / "config" / "master.key"
        
        if master_key_file.exists():
            # تحميل المفتاح الموجود
            with open(master_key_file, 'rb') as f:
                self.encryption_key = f.read()
            self.logger.info("🔑 تم تحميل مفتاح التشفير الرئيسي")
        else:
            # إنشاء مفتاح جديد
            self.encryption_key = Fernet.generate_key()
            with open(master_key_file, 'wb') as f:
                f.write(self.encryption_key)
            
            # تأمين الملف (Windows)
            if os.name == 'nt':
                os.chmod(master_key_file, 0o600)
            
            self.logger.info("🔑 تم إنشاء مفتاح التشفير الرئيسي")
    
    async def _load_existing_keys(self):
        """تحميل المفاتيح الموجودة"""
        encrypted_dir = self.vault_path / "encrypted"
        key_files = list(encrypted_dir.glob("*.enc"))
        
        self.usage_stats["keys_stored"] = len(key_files)
        
        if key_files:
            self.logger.info(f"📦 تم العثور على {len(key_files)} مفتاح مشفر")
        else:
            self.logger.info("📦 لا توجد مفاتيح مشفرة")
    
    async def _setup_key_rotation(self):
        """إعداد دوران المفاتيح التلقائي"""
        # سيتم تنفيذ دوران المفاتيح كمهمة خلفية
        self.logger.info("🔄 تم إعداد نظام دوران المفاتيح")
    
    def _encrypt_data(self, data: str) -> bytes:
        """تشفير البيانات"""
        fernet = Fernet(self.encryption_key)
        return fernet.encrypt(data.encode())
    
    def _decrypt_data(self, encrypted_data: bytes) -> str:
        """فك تشفير البيانات"""
        fernet = Fernet(self.encryption_key)
        return fernet.decrypt(encrypted_data).decode()
    
    async def store_api_key(self, 
                           provider: str, 
                           key_value: str, 
                           key_name: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None) -> bool:
        """تخزين مفتاح API"""
        try:
            if provider not in self.supported_providers:
                raise ValueError(f"مقدم الخدمة '{provider}' غير مدعوم")
            
            # التحقق من صيغة المفتاح
            expected_format = self.supported_providers[provider]["key_format"]
            if expected_format and not key_value.startswith(expected_format):
                self.logger.warning(f"⚠️ صيغة المفتاح قد تكون غير صحيحة لـ {provider}")
            
            # إنشاء معرف فريد للمفتاح
            key_id = key_name or f"{provider}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # إعداد البيانات الوصفية
            key_metadata = {
                "provider": provider,
                "key_id": key_id,
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "usage_count": 0,
                "is_active": True,
                "metadata": metadata or {}
            }
            
            # تشفير المفتاح
            encrypted_key = self._encrypt_data(key_value)
            encrypted_metadata = self._encrypt_data(json.dumps(key_metadata))
            
            # حفظ المفتاح المشفر
            key_file = self.vault_path / "encrypted" / f"{key_id}.enc"
            with open(key_file, 'wb') as f:
                f.write(encrypted_key + b"|||" + encrypted_metadata)
            
            # تسجيل العملية
            await self._log_security_event("KEY_STORED", {
                "provider": provider,
                "key_id": key_id,
                "timestamp": datetime.now().isoformat()
            })
            
            self.usage_stats["keys_stored"] += 1
            self.logger.info(f"✅ تم تخزين مفتاح {provider} بنجاح: {key_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تخزين المفتاح: {e}")
            return False
    
    async def get_api_key(self, provider: str, key_id: Optional[str] = None) -> Optional[str]:
        """الحصول على مفتاح API"""
        try:
            # البحث عن المفتاح
            if key_id:
                key_file = self.vault_path / "encrypted" / f"{key_id}.enc"
                if not key_file.exists():
                    return None
                key_files = [key_file]
            else:
                # البحث عن أحدث مفتاح للمقدم
                encrypted_dir = self.vault_path / "encrypted"
                key_files = [f for f in encrypted_dir.glob("*.enc") 
                           if f.stem.startswith(provider)]
                if not key_files:
                    return None
                key_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # قراءة وفك تشفير المفتاح
            with open(key_files[0], 'rb') as f:
                content = f.read()
            
            encrypted_key, encrypted_metadata = content.split(b"|||", 1)
            key_value = self._decrypt_data(encrypted_key)
            metadata = json.loads(self._decrypt_data(encrypted_metadata))
            
            # تحديث إحصائيات الاستخدام
            metadata["last_used"] = datetime.now().isoformat()
            metadata["usage_count"] += 1
            
            # حفظ البيانات المحدثة
            updated_metadata = self._encrypt_data(json.dumps(metadata))
            with open(key_files[0], 'wb') as f:
                f.write(encrypted_key + b"|||" + updated_metadata)
            
            # تسجيل الوصول
            await self._log_security_event("KEY_ACCESSED", {
                "provider": provider,
                "key_id": metadata["key_id"],
                "timestamp": datetime.now().isoformat()
            })
            
            self.usage_stats["keys_accessed"] += 1
            self.logger.info(f"🔑 تم الوصول لمفتاح {provider}")
            return key_value
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في الحصول على المفتاح: {e}")
            return None
    
    async def list_api_keys(self, provider: Optional[str] = None) -> List[Dict[str, Any]]:
        """قائمة مفاتيح API"""
        try:
            keys_info = []
            encrypted_dir = self.vault_path / "encrypted"
            
            for key_file in encrypted_dir.glob("*.enc"):
                try:
                    with open(key_file, 'rb') as f:
                        content = f.read()
                    
                    _, encrypted_metadata = content.split(b"|||", 1)
                    metadata = json.loads(self._decrypt_data(encrypted_metadata))
                    
                    if provider and metadata["provider"] != provider:
                        continue
                    
                    # إخفاء المفتاح الفعلي لأغراض الأمان
                    safe_info = {
                        "provider": metadata["provider"],
                        "key_id": metadata["key_id"],
                        "created_at": metadata["created_at"],
                        "last_used": metadata["last_used"],
                        "usage_count": metadata["usage_count"],
                        "is_active": metadata["is_active"]
                    }
                    keys_info.append(safe_info)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ خطأ في قراءة ملف المفتاح {key_file}: {e}")
                    continue
            
            return keys_info
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في قائمة المفاتيح: {e}")
            return []
    
    async def delete_api_key(self, key_id: str) -> bool:
        """حذف مفتاح API"""
        try:
            key_file = self.vault_path / "encrypted" / f"{key_id}.enc"
            
            if not key_file.exists():
                self.logger.warning(f"⚠️ المفتاح {key_id} غير موجود")
                return False
            
            # نسخ احتياطي قبل الحذف
            backup_dir = self.vault_path / "backups"
            backup_file = backup_dir / f"{key_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak"
            
            import shutil
            shutil.copy2(key_file, backup_file)
            
            # حذف المفتاح
            key_file.unlink()
            
            # تسجيل العملية
            await self._log_security_event("KEY_DELETED", {
                "key_id": key_id,
                "timestamp": datetime.now().isoformat(),
                "backup_location": str(backup_file)
            })
            
            self.usage_stats["keys_stored"] -= 1
            self.logger.info(f"🗑️ تم حذف المفتاح {key_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في حذف المفتاح: {e}")
            return False
    
    async def validate_api_key(self, provider: str, key_value: str) -> bool:
        """التحقق من صحة مفتاح API"""
        try:
            if provider not in self.supported_providers:
                return False
            
            # التحقق من الصيغة
            expected_format = self.supported_providers[provider]["key_format"]
            if expected_format and not key_value.startswith(expected_format):
                return False
            
            # يمكن إضافة تحقق عبر API هنا
            # validation_url = self.supported_providers[provider]["validation_url"]
            # ... تنفيذ طلب HTTP للتحقق
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في التحقق من المفتاح: {e}")
            return False
    
    async def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """تسجيل أحداث الأمان"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "details": details
            }
            
            log_file = self.vault_path / "logs" / f"security_{datetime.now().strftime('%Y%m')}.log"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
            self.usage_stats["security_events"] += 1
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تسجيل حدث الأمان: {e}")
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        return {
            "usage_statistics": self.usage_stats,
            "supported_providers": list(self.supported_providers.keys()),
            "vault_path": str(self.vault_path),
            "last_updated": datetime.now().isoformat()
        }
    
    async def shutdown(self):
        """إيقاف مدير المفاتيح"""
        await self._log_security_event("MANAGER_SHUTDOWN", {
            "timestamp": datetime.now().isoformat(),
            "final_stats": self.usage_stats
        })
        
        self.logger.info("🔐 تم إيقاف مدير API Keys")
