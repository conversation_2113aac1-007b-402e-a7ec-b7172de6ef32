#!/usr/bin/env python3
"""
🛡️ مراقب الأمان المتقدم
Advanced Security Monitor
"""

import os
import hashlib
import json
from pathlib import Path
from datetime import datetime

class SecurityMonitor:
    def __init__(self):
        self.security_log = Path("security_events.json")
        self.file_integrity = Path("file_integrity.json")
        self.baseline_hashes = {}
        
    def calculate_file_hash(self, file_path):
        """حساب hash للملف"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            return None
    
    def create_baseline(self, directories):
        """إنشاء خط الأساس للملفات"""
        print("🔒 إنشاء خط أساس الأمان...")
        
        for directory in directories:
            dir_path = Path(directory)
            if dir_path.exists():
                for file_path in dir_path.rglob("*"):
                    if file_path.is_file():
                        file_hash = self.calculate_file_hash(file_path)
                        if file_hash:
                            self.baseline_hashes[str(file_path)] = {
                                "hash": file_hash,
                                "size": file_path.stat().st_size,
                                "modified": datetime.fromtimestamp(
                                    file_path.stat().st_mtime
                                ).isoformat()
                            }
        
        with open(self.file_integrity, 'w', encoding='utf-8') as f:
            json.dump(self.baseline_hashes, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء خط الأساس لـ {len(self.baseline_hashes)} ملف")
    
    def check_integrity(self, directories):
        """فحص سلامة الملفات"""
        print("🔍 فحص سلامة الملفات...")
        
        if not self.file_integrity.exists():
            print("⚠️ لا يوجد خط أساس. يرجى إنشاؤه أولاً.")
            return []
        
        with open(self.file_integrity, 'r', encoding='utf-8') as f:
            baseline = json.load(f)
        
        violations = []
        
        for directory in directories:
            dir_path = Path(directory)
            if dir_path.exists():
                for file_path in dir_path.rglob("*"):
                    if file_path.is_file():
                        file_str = str(file_path)
                        current_hash = self.calculate_file_hash(file_path)
                        
                        if file_str in baseline:
                            if current_hash != baseline[file_str]["hash"]:
                                violations.append({
                                    "type": "file_modified",
                                    "file": file_str,
                                    "expected_hash": baseline[file_str]["hash"],
                                    "current_hash": current_hash,
                                    "timestamp": datetime.now().isoformat()
                                })
                        else:
                            violations.append({
                                "type": "new_file",
                                "file": file_str,
                                "hash": current_hash,
                                "timestamp": datetime.now().isoformat()
                            })
        
        if violations:
            self.log_security_event({
                "type": "integrity_violations",
                "violations": violations,
                "timestamp": datetime.now().isoformat()
            })
        
        return violations
    
    def log_security_event(self, event):
        """تسجيل حدث أمني"""
        events = []
        if self.security_log.exists():
            with open(self.security_log, 'r', encoding='utf-8') as f:
                events = json.load(f)
        
        events.append(event)
        
        with open(self.security_log, 'w', encoding='utf-8') as f:
            json.dump(events, f, ensure_ascii=False, indent=2)
    
    def scan_for_threats(self):
        """مسح للتهديدات"""
        threats = []
        
        # فحص العمليات المشبوهة
        suspicious_processes = [
            "nc", "netcat", "nmap", "sqlmap", 
            "hydra", "john", "hashcat"
        ]
        
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] in suspicious_processes:
                    threats.append({
                        "type": "suspicious_process",
                        "process": proc.info['name'],
                        "pid": proc.info['pid'],
                        "timestamp": datetime.now().isoformat()
                    })
        except ImportError:
            pass
        
        if threats:
            self.log_security_event({
                "type": "threat_detection",
                "threats": threats,
                "timestamp": datetime.now().isoformat()
            })
        
        return threats

if __name__ == "__main__":
    monitor = SecurityMonitor()
    
    # إنشاء خط الأساس
    directories = ["configs", "src", "database"]
    monitor.create_baseline(directories)
    
    # فحص السلامة
    violations = monitor.check_integrity(directories)
    if violations:
        print(f"⚠️ تم اكتشاف {len(violations)} انتهاك أمني")
    else:
        print("✅ لا توجد انتهاكات أمنية")
    
    # مسح التهديدات
    threats = monitor.scan_for_threats()
    if threats:
        print(f"🚨 تم اكتشاف {len(threats)} تهديد محتمل")
    else:
        print("✅ لا توجد تهديدات")
