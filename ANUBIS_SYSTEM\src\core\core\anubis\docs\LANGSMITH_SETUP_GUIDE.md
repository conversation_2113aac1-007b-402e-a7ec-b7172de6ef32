# 🔗 دليل إعد<PERSON> LangSmith مع نظام أنوبيس
## LangSmith Setup Guide for Anubis System

## 🚀 الخطوات السريعة

### 1. إنشاء حساب LangSmith
1. اذه<PERSON> إلى https://smith.langchain.com/
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد باسم "anubis-ai-system"

### 2. الحصول على API Key
1. اذهب إلى Settings → API Keys
2. أنشئ مفتاح API جديد
3. انسخ المفتاح واحفظه بأمان

### 3. إعداد متغيرات البيئة

#### في Linux/Mac:
```bash
export LANGCHAIN_TRACING_V2=true
export LANGCHAIN_API_KEY="your-api-key-here"
export LANGCHAIN_PROJECT="anubis-ai-system"
```

#### في Windows PowerShell:
```powershell
$env:LANGCHAIN_TRACING_V2="true"
$env:LANGCHAIN_API_KEY="your-api-key-here"
$env:LANGCHAIN_PROJECT="anubis-ai-system"
```

### 4. اختبار التكامل
```bash
python scripts/test_langsmith_integration.py
```

## 🎯 الميزات المتاحة

### 📊 مراقبة الوكلاء
- تتبع أداء جميع الوكلاء
- قياس أوقات الاستجابة
- مراقبة جودة النتائج

### 🔗 ربط الوكلاء
- تنسيق العمل بين الوكلاء
- تمرير البيانات بذكاء
- تحسين التفاعلات

### 🧠 تحسين النماذج
- اختيار النموذج الأمثل
- تحسين الـ prompts
- تعلم من التفاعلات

## 🛠️ الاستخدام

### استيراد Wrapper:
```python
from core.langsmith_wrapper import langsmith_wrapper

# تتبع عملية وكيل
with langsmith_wrapper.trace_agent_operation("error_detector", "scan_file"):
    result = detector.scan_file("file.py")
```

### مراقبة أداء النموذج:
```python
import time

start_time = time.time()
response = model.generate("prompt")
end_time = time.time()

langsmith_wrapper.log_model_performance(
    "llama3:8b", 
    end_time - start_time
)
```

## 🔧 استكشاف الأخطاء

### مشكلة: API Key غير صحيح
```
خطأ: Authentication failed
الحل: تأكد من صحة API Key في متغيرات البيئة
```

### مشكلة: المشروع غير موجود
```
خطأ: Project not found
الحل: أنشئ مشروع "anubis-ai-system" في LangSmith
```

### مشكلة: LangSmith غير مثبت
```
خطأ: ImportError langsmith
الحل: pip install langsmith langchain
```

## 📈 المراقبة والتحليل

### عرض التتبع:
1. اذهب إلى https://smith.langchain.com/
2. اختر مشروع "anubis-ai-system"
3. راجع التتبع والإحصائيات

### تحليل الأداء:
- أوقات استجابة الوكلاء
- معدلات نجاح العمليات
- استخدام النماذج المختلفة

---

🏺 **نظام أنوبيس + LangSmith = قوة خارقة!** 🚀
