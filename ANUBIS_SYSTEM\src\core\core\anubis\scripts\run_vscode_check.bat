@echo off
REM VS Code Process Monitor Batch Script
REM سكريبت Batch لمراقبة عمليات VS Code

title VS Code Process Monitor

echo.
echo ========================================
echo    VS Code Process Monitor
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found!
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python found
echo.

REM التحقق من وجود psutil
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] psutil library not found
    echo [INFO] Installing psutil...
    python -m pip install psutil
    if errorlevel 1 (
        echo [ERROR] Failed to install psutil
        pause
        exit /b 1
    )
    echo [SUCCESS] psutil installed successfully
    echo.
)

echo [INFO] All dependencies are ready
echo.

REM عرض القائمة
:menu
cls
echo.
echo ========================================
echo    VS Code Process Monitor
echo ========================================
echo.
echo 1. Quick Check (Fast)
echo 2. Detailed Monitor
echo 3. Continuous Monitoring
echo 4. View Recent Reports
echo 5. System Information
echo 6. Install/Update Dependencies
echo 0. Exit
echo.
set /p choice="Enter your choice (0-6): "

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto quick_check
if "%choice%"=="2" goto detailed_monitor
if "%choice%"=="3" goto continuous_monitor
if "%choice%"=="4" goto view_reports
if "%choice%"=="5" goto system_info
if "%choice%"=="6" goto install_deps

echo [ERROR] Invalid choice. Please try again.
pause
goto menu

:quick_check
cls
echo.
echo ========================================
echo    Running Quick VS Code Check
echo ========================================
echo.
python scripts\quick_vscode_check.py
echo.
echo [INFO] Quick check completed
pause
goto menu

:detailed_monitor
cls
echo.
echo ========================================
echo    Running Detailed Monitor
echo ========================================
echo.
python scripts\vscode_process_monitor.py
echo.
echo [INFO] Detailed monitoring completed
pause
goto menu

:continuous_monitor
cls
echo.
echo ========================================
echo    Starting Continuous Monitoring
echo ========================================
echo.
echo [INFO] Press Ctrl+C to stop monitoring
echo.
python scripts\run_vscode_monitor.py
echo.
echo [INFO] Monitoring stopped
pause
goto menu

:view_reports
cls
echo.
echo ========================================
echo    Recent Reports
echo ========================================
echo.
if exist "Universal-AI-Assistants\reports\*.json" (
    echo [INFO] Found reports:
    dir /b /o-d "Universal-AI-Assistants\reports\*vscode*.json" 2>nul | findstr /v "^$"
    echo.
    echo [INFO] Reports are located in: Universal-AI-Assistants\reports\
) else (
    echo [INFO] No reports found yet
    echo [INFO] Run a check first to generate reports
)
echo.
pause
goto menu

:system_info
cls
echo.
echo ========================================
echo    System Information
echo ========================================
echo.
echo [INFO] Gathering system information...
echo.

REM معلومات النظام الأساسية
echo System: %OS%
echo Computer: %COMPUTERNAME%
echo User: %USERNAME%
echo.

REM معلومات Python
echo Python Version:
python --version
echo.

REM معلومات psutil
echo psutil Status:
python -c "import psutil; print('psutil version:', psutil.__version__); print('CPU cores:', psutil.cpu_count()); print('Memory:', round(psutil.virtual_memory().total/1024/1024/1024, 1), 'GB')" 2>nul
if errorlevel 1 (
    echo psutil not available
)
echo.

REM عمليات VS Code
echo VS Code Processes:
tasklist /fi "imagename eq Code.exe" /fo table 2>nul | findstr /v "INFO:"
if errorlevel 1 (
    echo No VS Code processes found
)
echo.

pause
goto menu

:install_deps
cls
echo.
echo ========================================
echo    Installing Dependencies
echo ========================================
echo.
echo [INFO] Installing/updating Python packages...
python -m pip install --upgrade pip
python -m pip install --upgrade psutil
echo.
echo [SUCCESS] Dependencies updated
pause
goto menu

:exit
cls
echo.
echo ========================================
echo    VS Code Process Monitor
echo ========================================
echo.
echo Thank you for using VS Code Process Monitor!
echo.
echo Generated reports can be found in:
echo   Universal-AI-Assistants\reports\
echo.
echo Logs can be found in:
echo   Universal-AI-Assistants\logs\
echo.
pause
exit /b 0
