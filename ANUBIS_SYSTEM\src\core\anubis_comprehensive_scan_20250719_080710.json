{"scan_info": {"timestamp": "2025-07-19T08:07:10.265925", "project_root": "C:\\Users\\<USER>\\Universal-AI-Assistants", "scanner_version": "3.0", "scan_type": "comprehensive_project_scan"}, "project_structure": {"anubis": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "type": "main_system", "file_count": 296, "subdirectories": ["agents", "api", "api.backup_20250718_160241", "configs", "core", "core.backup_20250718_160241", "database", "database.backup_20250718_160241", "docs", "examples"], "main_files": ["INDEX.md", "main.py", "MAIN_README.md", "ORGANIZED_FILES_README.md", "PROJECT_README.md", "README.md", "__init__.py"], "technologies": ["Python", "Web", "SQL", "JSON", "<PERSON><PERSON>", "Python Package"], "isolation_priority": "critical"}, "anubis_isolated_system": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_isolated_system", "type": "unknown", "file_count": 307, "subdirectories": ["configs", "docs", "networks", "scripts", "security", "services", "volumes"], "main_files": ["docker-compose.yml", "isolation_report.json"], "technologies": ["Python", "Web", "SQL", "JSON", "YAML", "<PERSON><PERSON>", "Python Package", "<PERSON>er"], "isolation_priority": "high"}, "archive": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive", "type": "unknown", "file_count": 54, "subdirectories": ["backups.backup_20250718_160241", "cache_files", "deprecated", "duplicate_reports", "old_databases", "old_files", "old_versions", "temp_files", "unused_files"], "main_files": ["cleanup_log_20250714_144107.json", "cleanup_summary_20250714_144107.md", "organization_log_20250714_140701.json", "organization_summary_20250714_140701.md", "README.md"], "technologies": ["Python", "Web", "JSON", "<PERSON><PERSON>"], "isolation_priority": "medium"}, "augment-cht": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\augment-cht", "type": "unknown", "file_count": 3, "subdirectories": [], "main_files": ["chat-kiro.md", "chat.md", "cht.md"], "technologies": ["<PERSON><PERSON>"], "isolation_priority": "low"}, "backup": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\backup", "type": "unknown", "file_count": 7, "subdirectories": ["old_agents"], "main_files": ["main_old.py", "README.md"], "technologies": ["Python", "<PERSON><PERSON>"], "isolation_priority": "low"}, "configs": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\configs", "type": "configuration", "file_count": 2, "subdirectories": [], "main_files": ["ai_config.json", "default_config.json"], "technologies": ["JSON"], "isolation_priority": "low"}, "n8n": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n", "type": "workflow_tool", "file_count": 5, "subdirectories": ["credentials", "nodes", "workflows"], "main_files": [], "technologies": ["TypeScript", "JSON"], "isolation_priority": "medium"}, "reports": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports", "type": "unknown", "file_count": 1, "subdirectories": [], "main_files": ["anubis_report_20250718_173617.json"], "technologies": ["JSON"], "isolation_priority": "low"}, "temp": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\temp", "type": "unknown", "file_count": 4, "subdirectories": [], "main_files": ["README.md", "test_python_file.py", "test_react_file.jsx", "test_style.css"], "technologies": ["Python", "JavaScript", "Web", "<PERSON><PERSON>"], "isolation_priority": "medium"}, "tools": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools", "type": "tools", "file_count": 106, "subdirectories": ["emergency", "emergency.backup_20250718_160241", "monitoring.backup_20250718_160241", "vscode-optimizer"], "main_files": ["README.md"], "technologies": ["Python", "JSON", "<PERSON><PERSON>", "Python Package"], "isolation_priority": "high"}, "Universal-AI-Assistants": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants", "type": "main_system", "file_count": 2, "subdirectories": ["logs", "reports"], "main_files": [], "technologies": ["JSON"], "isolation_priority": "critical"}, "workspace": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\workspace", "type": "unknown", "file_count": 17, "subdirectories": ["logs", "reports"], "main_files": [], "technologies": ["JSON"], "isolation_priority": "low"}, "__pycache__": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\__pycache__", "type": "unknown", "file_count": 1, "subdirectories": [], "main_files": ["process_control_dashboard.cpython-313.pyc"], "technologies": [], "isolation_priority": "low"}}, "tools_discovered": {"anubis": {"type": "main_system", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "technologies": ["Python", "Web", "SQL", "JSON", "<PERSON><PERSON>", "Python Package"], "file_count": 296, "isolation_priority": "critical", "container_candidate": true, "dependencies": ["Python imports"]}, "n8n": {"type": "workflow_tool", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n", "technologies": ["TypeScript", "JSON"], "file_count": 5, "isolation_priority": "medium", "container_candidate": true, "dependencies": []}, "tools": {"type": "tools", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools", "technologies": ["Python", "JSON", "<PERSON><PERSON>", "Python Package"], "file_count": 106, "isolation_priority": "high", "container_candidate": true, "dependencies": ["Python imports"]}, "Universal-AI-Assistants": {"type": "main_system", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants", "technologies": ["JSON"], "file_count": 2, "isolation_priority": "critical", "container_candidate": true, "dependencies": []}}, "components_analysis": {"anubis": {"detailed_structure": {"subdirectories": ["agents", "api", "api.backup_20250718_160241", "configs", "core", "core.backup_20250718_160241", "database", "database.backup_20250718_160241", "docs", "examples", "examples.backup_20250718_160241", "plugins", "plugins.backup_20250718_160241", "scripts", "templates", "templates.backup_20250718_160241", "tests", "tests.backup_20250718_160241", "workspace", "__pycache__"], "key_files": ["INDEX.md", "main.py", "MAIN_README.md", "ORGANIZED_FILES_README.md", "PROJECT_README.md", "README.md", "__init__.py", "agents\\database_agent.py", "agents\\enhanced_error_detector.py", "agents\\enhanced_file_organizer.py", "agents\\README.md", "agents\\__init__.py", "api\\anubis_api_server.py", "api\\anubis_openapi.json", "api\\__init__.py", "api.backup_20250718_160241\\README.md", "configs\\ai_config.json", "configs\\database_config.json", "configs\\default_config.json", "configs\\langsmith_config.json"], "total_size_mb": 36.14}, "entry_points": ["main.py"], "configuration_files": [], "isolation_requirements": {"network_isolation": true, "file_system_isolation": true, "resource_limits": {"memory": "512M", "cpu": "0.5"}, "security_level": "high"}, "container_specs": {"base_image": "python:3.11-slim", "working_dir": "/app", "ports": [], "volumes": ["/app/data", "/app/logs"], "environment": [], "build_commands": ["COPY requirements.txt .", "RUN pip install --no-cache-dir -r requirements.txt", "COPY . ."], "run_command": "python main.py"}}, "n8n": {"detailed_structure": {"subdirectories": ["credentials", "nodes", "workflows"], "key_files": ["credentials\\AnubisApi.credentials.ts", "nodes\\AnubisAgents.node.ts", "nodes\\AnubisGemini.node.ts", "nodes\\AnubisOllama.node.ts", "workflows\\anubis-project-analysis.json"], "total_size_mb": 0.03}, "entry_points": [], "configuration_files": [], "isolation_requirements": {"network_isolation": false, "file_system_isolation": true, "resource_limits": {"memory": "256M", "cpu": "0.2"}, "security_level": "low"}, "container_specs": {"base_image": "alpine:latest", "working_dir": "/app", "ports": [], "volumes": ["/app/data", "/app/logs"], "environment": [], "build_commands": ["COPY . ."], "run_command": "sh"}}, "tools": {"detailed_structure": {"subdirectories": ["emergency", "emergency.backup_20250718_160241", "monitoring.backup_20250718_160241", "vscode-optimizer"], "key_files": ["README.md", "emergency.backup_20250718_160241\\README.md", "monitoring.backup_20250718_160241\\README.md", "vscode-optimizer\\dashboard_config.json", "vscode-optimizer\\DASHBOARD_README.md", "vscode-optimizer\\README.md", "vscode-optimizer\\SUITE_INFO.md", "vscode-optimizer\\test_dashboard.py", "vscode-optimizer\\agents\\database_agent.py", "vscode-optimizer\\agents\\enhanced_error_detector.py", "vscode-optimizer\\agents\\enhanced_file_organizer.py", "vscode-optimizer\\agents\\enhanced_memory_agent.py", "vscode-optimizer\\agents\\enhanced_project_analyzer.py", "vscode-optimizer\\agents\\README.md", "vscode-optimizer\\agents\\smart_ai_agent.py", "vscode-optimizer\\agents\\smart_code_analyzer.py", "vscode-optimizer\\agents\\__init__.py", "vscode-optimizer\\VS-Code-Performance-Optimizer\\analyze_extensions.py", "vscode-optimizer\\VS-Code-Performance-Optimizer\\analyze_vscode.py", "vscode-optimizer\\VS-Code-Performance-Optimizer\\auto_apply_vscode_settings.py"], "total_size_mb": 1.13}, "entry_points": [], "configuration_files": [], "isolation_requirements": {"network_isolation": false, "file_system_isolation": true, "resource_limits": {"memory": "512M", "cpu": "0.5"}, "security_level": "medium"}, "container_specs": {"base_image": "python:3.11-slim", "working_dir": "/app", "ports": [], "volumes": ["/app/data", "/app/logs"], "environment": [], "build_commands": ["COPY requirements.txt .", "RUN pip install --no-cache-dir -r requirements.txt", "COPY . ."], "run_command": "python main.py"}}, "Universal-AI-Assistants": {"detailed_structure": {"subdirectories": ["logs", "reports"], "key_files": ["reports\\vscode_monitor_report_20250716_162246.json"], "total_size_mb": 0.08}, "entry_points": [], "configuration_files": [], "isolation_requirements": {"network_isolation": true, "file_system_isolation": true, "resource_limits": {"memory": "256M", "cpu": "0.2"}, "security_level": "high"}, "container_specs": {"base_image": "alpine:latest", "working_dir": "/app", "ports": [], "volumes": ["/app/data", "/app/logs"], "environment": [], "build_commands": ["COPY . ."], "run_command": "sh"}}}, "isolation_recommendations": {"critical_components": [{"name": "anubis", "type": "main_system", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "technologies": ["Python", "Web", "SQL", "JSON", "<PERSON><PERSON>", "Python Package"], "estimated_isolation_time": "4-6 hours"}, {"name": "Universal-AI-Assistants", "type": "main_system", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants", "technologies": ["JSON"], "estimated_isolation_time": "30-60 minutes"}], "high_priority_components": [{"name": "tools", "type": "tools", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools", "technologies": ["Python", "JSON", "<PERSON><PERSON>", "Python Package"], "estimated_isolation_time": "2-4 hours"}], "medium_priority_components": [{"name": "n8n", "type": "workflow_tool", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n", "technologies": ["TypeScript", "JSON"], "estimated_isolation_time": "30-60 minutes"}], "low_priority_components": [], "isolation_strategy": "docker_containers", "network_design": "bridge_network", "data_isolation": "separate_volumes"}, "containerization_plan": {"total_containers": 4, "containers": {"anubis": {"container_name": "anubis_container", "build_context": "./containers/anubis", "dockerfile": "./containers/anubis/Dockerfile", "ports": ["8000:8000"], "volumes": ["./volumes/anubis/data:/app/data", "./volumes/anubis/logs:/app/logs"], "environment": ["PYTHONPATH=/app", "COMPONENT_NAME=anubis", "LOG_LEVEL=INFO"], "depends_on": ["database"], "restart_policy": "unless-stopped", "resource_limits": {"memory": "512M", "cpu": "0.5"}}, "n8n": {"container_name": "n8n_container", "build_context": "./containers/n8n", "dockerfile": "./containers/n8n/Dockerfile", "ports": ["5678:5678"], "volumes": ["./volumes/n8n/data:/app/data", "./volumes/n8n/logs:/app/logs"], "environment": ["PYTHONPATH=/app", "COMPONENT_NAME=n8n", "LOG_LEVEL=INFO"], "depends_on": [], "restart_policy": "unless-stopped", "resource_limits": {"memory": "256M", "cpu": "0.2"}}, "tools": {"container_name": "tools_container", "build_context": "./containers/tools", "dockerfile": "./containers/tools/Dockerfile", "ports": [], "volumes": ["./volumes/tools/data:/app/data", "./volumes/tools/logs:/app/logs"], "environment": ["PYTHONPATH=/app", "COMPONENT_NAME=tools", "LOG_LEVEL=INFO"], "depends_on": [], "restart_policy": "unless-stopped", "resource_limits": {"memory": "512M", "cpu": "0.5"}}, "Universal-AI-Assistants": {"container_name": "universal_ai_assistants_container", "build_context": "./containers/Universal-AI-Assistants", "dockerfile": "./containers/Universal-AI-Assistants/Dockerfile", "ports": ["8001:8001"], "volumes": ["./volumes/Universal-AI-Assistants/data:/app/data", "./volumes/Universal-AI-Assistants/logs:/app/logs"], "environment": ["PYTHONPATH=/app", "COMPONENT_NAME=Universal-AI-Assistants", "LOG_LEVEL=INFO"], "depends_on": ["database"], "restart_policy": "unless-stopped", "resource_limits": {"memory": "256M", "cpu": "0.2"}}}, "docker_compose_structure": {}, "network_configuration": {"network_name": "anubis_isolation_network", "subnet": "172.30.0.0/16", "driver": "bridge"}, "volume_configuration": {"shared_volumes": ["logs", "data", "configs"], "persistent_volumes": ["database_data", "user_data"]}}}