#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام أنوبيس - نقطة الدخول الرئيسية
Anubis System - Main Entry Point

هذا الملف يشير إلى الموقع الجديد للنظام الأساسي
"""

import sys
from pathlib import Path

# إضافة مسار src إلى Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# استيراد وتشغيل النظام الأساسي
if __name__ == "__main__":
    from core.main import app
    import uvicorn
    
    print("🏺 بدء تشغيل نظام أنوبيس...")
    print("🚀 النظام جاهز على: http://localhost:8000")
    
    uvicorn.run(
        "core.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
