#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
𓅃 مساعد فريق حورس لإدارة مفاتيح API
Horus Team API Keys Assistant

مساعد متخصص لفحص وإدارة مفاتيح API بمساعدة فريق حورس
Specialized assistant for scanning and managing API keys with Horus team help
"""

import os
import re
import json
import glob
from pathlib import Path
from datetime import datetime
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HorusAPIKeysAssistant:
    """𓅃 مساعد حورس لإدارة مفاتيح API"""
    
    def __init__(self):
        """تهيئة المساعد"""
        self.team_members = {
            "THOTH": "⚡ المحلل السريع - فحص وتحليل الملفات",
            "PTAH": "🔧 المطور الخبير - تطوير الحلول التقنية", 
            "RA": "🎯 المستشار الاستراتيجي - وضع الاستراتيجيات",
            "KHNUM": "💡 المبدع والمبتكر - الحلول الإبداعية",
            "SESHAT": "👁️ المحللة البصرية - التوثيق والتقارير"
        }
        
        self.api_patterns = {
            "google_gemini": r"AIza[A-Za-z0-9_-]{35}",
            "openrouter": r"sk-or-v1-[A-Za-z0-9]{64}",
            "github": r"(ghp_[A-Za-z0-9]{36}|github_pat_[A-Za-z0-9_]{82})",
            "huggingface": r"hf_[A-Za-z0-9]{37}",
            "deepseek": r"sk-[A-Za-z0-9]{32}",
            "anthropic": r"sk-ant-api03-[A-Za-z0-9_-]{95}",
            "mistral": r"[A-Za-z0-9]{28}",
            "continue": r"con-[A-Za-z0-9]{64}",
            "nebius": r"eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+",
            "generic": r"[A-Za-z0-9_-]{20,}"
        }
        
        self.discovered_keys = {}
        self.scan_results = {}
        
        logger.info("𓅃 تم تهيئة مساعد حورس لإدارة مفاتيح API")
    
    def activate_horus_team(self):
        """تفعيل فريق حورس"""
        print("\n" + "="*80)
        print("𓅃 تفعيل فريق حورس للذكاء الاصطناعي")
        print("="*80)
        
        for member, description in self.team_members.items():
            print(f"✅ {member}: {description}")
        
        print("\n🚀 فريق حورس جاهز للعمل!")
        print("="*80)
        
        return True
    
    def thoth_file_scanner(self, directory_path):
        """⚡ THOTH: فحص الملفات واستخراج المفاتيح"""
        print(f"\n⚡ THOTH يقوم بفحص المجلد: {directory_path}")
        
        if not os.path.exists(directory_path):
            print(f"❌ المجلد غير موجود: {directory_path}")
            return {}
        
        found_keys = {}
        file_count = 0
        
        # فحص جميع الملفات النصية
        for file_path in glob.glob(os.path.join(directory_path, "*.txt")):
            file_count += 1
            print(f"🔍 فحص الملف: {os.path.basename(file_path)}")
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # البحث عن المفاتيح باستخدام الأنماط
                for platform, pattern in self.api_patterns.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        if platform not in found_keys:
                            found_keys[platform] = []
                        
                        for match in matches:
                            key_info = {
                                "key": match if isinstance(match, str) else match[0],
                                "file": os.path.basename(file_path),
                                "platform": platform
                            }
                            found_keys[platform].append(key_info)
                            print(f"  ✅ وجد مفتاح {platform}: {key_info['key'][:20]}...")
            
            except Exception as e:
                print(f"  ❌ خطأ في قراءة الملف: {e}")
        
        print(f"⚡ THOTH انتهى من فحص {file_count} ملف")
        return found_keys
    
    def ptah_key_validator(self, keys_dict):
        """🔧 PTAH: التحقق من صحة المفاتيح وتصنيفها"""
        print(f"\n🔧 PTAH يقوم بالتحقق من صحة المفاتيح...")
        
        validated_keys = {}
        total_keys = 0
        valid_keys = 0
        
        for platform, keys_list in keys_dict.items():
            validated_keys[platform] = []
            
            for key_info in keys_list:
                total_keys += 1
                key = key_info["key"]
                
                # التحقق من طول المفتاح
                if len(key) < 10:
                    print(f"  ❌ مفتاح قصير جداً: {key}")
                    continue
                
                # التحقق من الأحرف المسموحة
                if not re.match(r'^[A-Za-z0-9_.-]+$', key):
                    print(f"  ⚠️ مفتاح يحتوي على أحرف غير مسموحة: {key[:20]}...")
                    continue
                
                # إضافة معلومات إضافية
                key_info.update({
                    "length": len(key),
                    "validated": True,
                    "discovered_at": datetime.now().isoformat()
                })
                
                validated_keys[platform].append(key_info)
                valid_keys += 1
                print(f"  ✅ مفتاح صحيح {platform}: {key[:20]}...")
        
        print(f"🔧 PTAH انتهى من التحقق: {valid_keys}/{total_keys} مفتاح صحيح")
        return validated_keys
    
    def ra_security_analysis(self, validated_keys):
        """🎯 RA: تحليل الأمان ووضع التوصيات"""
        print(f"\n🎯 RA يقوم بتحليل الأمان...")
        
        security_report = {
            "total_keys": 0,
            "platforms": len(validated_keys),
            "security_level": "متوسط",
            "recommendations": [],
            "risks": []
        }
        
        for platform, keys_list in validated_keys.items():
            security_report["total_keys"] += len(keys_list)
            
            # تحليل المخاطر حسب المنصة
            if platform in ["github", "google_gemini"]:
                security_report["risks"].append(f"مخاطر عالية: مفاتيح {platform} حساسة جداً")
            
            # فحص المفاتيح المكررة
            keys_only = [k["key"] for k in keys_list]
            if len(keys_only) != len(set(keys_only)):
                security_report["risks"].append(f"مفاتيح مكررة في {platform}")
        
        # وضع التوصيات
        security_report["recommendations"] = [
            "تشفير جميع المفاتيح باستخدام AES-256",
            "إنشاء نسخ احتياطية آمنة",
            "تفعيل مراقبة الوصول للمفاتيح",
            "تدوير المفاتيح كل 30-90 يوم",
            "استخدام متغيرات البيئة بدلاً من الملفات النصية"
        ]
        
        # تحديد مستوى الأمان
        if security_report["total_keys"] > 20:
            security_report["security_level"] = "يتطلب اهتمام عالي"
        
        print(f"🎯 RA انتهى من التحليل: {security_report['total_keys']} مفتاح، مستوى الأمان: {security_report['security_level']}")
        return security_report
    
    def khnum_creative_solutions(self, keys_data):
        """💡 KHNUM: ابتكار حلول إبداعية"""
        print(f"\n💡 KHNUM يبتكر حلول إبداعية...")
        
        creative_solutions = {
            "smart_categorization": {},
            "auto_backup_strategy": {},
            "intelligent_monitoring": {},
            "innovative_features": []
        }
        
        # تصنيف ذكي للمفاتيح
        for platform, keys_list in keys_data.items():
            creative_solutions["smart_categorization"][platform] = {
                "count": len(keys_list),
                "priority": "عالي" if platform in ["github", "google_gemini"] else "متوسط",
                "usage_pattern": "متكرر" if len(keys_list) > 2 else "عادي"
            }
        
        # استراتيجية النسخ الاحتياطي الذكية
        creative_solutions["auto_backup_strategy"] = {
            "frequency": "يومي للمفاتيح الحساسة، أسبوعي للأخرى",
            "encryption": "متعدد الطبقات",
            "storage": "محلي + سحابي مشفر"
        }
        
        # الميزات المبتكرة
        creative_solutions["innovative_features"] = [
            "كشف تلقائي للمفاتيح الجديدة",
            "تنبيهات ذكية لانتهاء صلاحية المفاتيح",
            "واجهة مرئية لإدارة المفاتيح",
            "تكامل مع أنظمة CI/CD",
            "مراقبة استخدام المفاتيح في الوقت الفعلي"
        ]
        
        print(f"💡 KHNUM ابتكر {len(creative_solutions['innovative_features'])} ميزة جديدة")
        return creative_solutions
    
    def seshat_documentation(self, all_data):
        """👁️ SESHAT: إنشاء التوثيق والتقارير"""
        print(f"\n👁️ SESHAT تقوم بإنشاء التوثيق...")
        
        report = []
        report.append("𓅃 تقرير فريق حورس - إدارة مفاتيح API")
        report.append("=" * 80)
        report.append(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # إحصائيات عامة
        total_keys = sum(len(keys) for keys in all_data["validated_keys"].values())
        report.append(f"📊 إحصائيات عامة:")
        report.append(f"   🔑 إجمالي المفاتيح: {total_keys}")
        report.append(f"   🏢 عدد المنصات: {len(all_data['validated_keys'])}")
        report.append(f"   🛡️ مستوى الأمان: {all_data['security_report']['security_level']}")
        report.append("")
        
        # تفاصيل المنصات
        report.append("🏷️ تفاصيل المنصات:")
        for platform, keys_list in all_data["validated_keys"].items():
            report.append(f"   📱 {platform}: {len(keys_list)} مفتاح")
        report.append("")
        
        # التوصيات الأمنية
        report.append("🔒 التوصيات الأمنية:")
        for rec in all_data["security_report"]["recommendations"]:
            report.append(f"   • {rec}")
        report.append("")
        
        # الحلول المبتكرة
        report.append("💡 الحلول المبتكرة:")
        for feature in all_data["creative_solutions"]["innovative_features"]:
            report.append(f"   ✨ {feature}")
        
        report_text = "\n".join(report)
        print(f"👁️ SESHAT انتهت من إنشاء تقرير شامل ({len(report)} سطر)")
        
        return report_text
    
    def run_horus_mission(self, scan_directory):
        """🚀 تشغيل مهمة فريق حورس الكاملة"""
        print("\n🚀 بدء مهمة فريق حورس لإدارة مفاتيح API")
        print("="*80)
        
        # تفعيل الفريق
        self.activate_horus_team()
        
        # المرحلة 1: THOTH - فحص الملفات
        discovered_keys = self.thoth_file_scanner(scan_directory)
        
        # المرحلة 2: PTAH - التحقق من المفاتيح
        validated_keys = self.ptah_key_validator(discovered_keys)
        
        # المرحلة 3: RA - تحليل الأمان
        security_report = self.ra_security_analysis(validated_keys)
        
        # المرحلة 4: KHNUM - الحلول الإبداعية
        creative_solutions = self.khnum_creative_solutions(validated_keys)
        
        # المرحلة 5: SESHAT - التوثيق
        all_data = {
            "discovered_keys": discovered_keys,
            "validated_keys": validated_keys,
            "security_report": security_report,
            "creative_solutions": creative_solutions
        }
        
        final_report = self.seshat_documentation(all_data)
        
        # حفظ التقرير
        report_file = f"horus_api_keys_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(final_report)
        
        print(f"\n✅ مهمة فريق حورس مكتملة!")
        print(f"📄 تم حفظ التقرير: {report_file}")
        print("="*80)
        
        return all_data, final_report

def main():
    """الدالة الرئيسية"""
    print("𓅃 مساعد فريق حورس لإدارة مفاتيح API")
    print("="*60)
    
    # إنشاء المساعد
    assistant = HorusAPIKeysAssistant()
    
    # المجلد المطلوب فحصه
    scan_directory = r"C:\Users\<USER>\OneDrive\Desktop\text"
    
    # تشغيل المهمة
    results, report = assistant.run_horus_mission(scan_directory)
    
    # عرض النتائج
    print("\n" + report)
    
    return results

if __name__ == "__main__":
    main()
