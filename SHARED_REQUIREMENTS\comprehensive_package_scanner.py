#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فاحص شامل للمكتبات والحزم المثبتة
Comprehensive Package Scanner

يقوم بفحص جميع المكتبات المثبتة على النظام وإنشاء قائمة شاملة
Scans all installed packages on the system and creates a comprehensive list
"""

import os
import sys
import subprocess
import json
import glob
from pathlib import Path
from datetime import datetime
import importlib.util

class ComprehensivePackageScanner:
    def __init__(self):
        self.scan_results = {
            'scan_date': datetime.now().isoformat(),
            'python_environments': [],
            'all_packages': {},
            'categorized_packages': {
                'ai_ml': [],
                'web_development': [],
                'data_science': [],
                'automation': [],
                'system_tools': [],
                'development_tools': [],
                'other': []
            },
            'statistics': {}
        }
        
        # تصنيفات المكتبات
        self.package_categories = {
            'ai_ml': [
                'tensorflow', 'torch', 'pytorch', 'sklearn', 'scikit-learn',
                'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
                'opencv', 'cv2', 'transformers', 'huggingface', 'openai',
                'anthropic', 'langchain', 'llama', 'ollama', 'gemini'
            ],
            'web_development': [
                'flask', 'django', 'fastapi', 'requests', 'urllib3',
                'aiohttp', 'httpx', 'websockets', 'socketio', 'tornado'
            ],
            'data_science': [
                'jupyter', 'ipython', 'notebook', 'pandas', 'numpy',
                'scipy', 'statsmodels', 'sympy', 'networkx'
            ],
            'automation': [
                'selenium', 'beautifulsoup4', 'scrapy', 'pyautogui',
                'schedule', 'celery', 'rq', 'apscheduler'
            ],
            'system_tools': [
                'psutil', 'pathlib', 'shutil', 'subprocess', 'threading',
                'multiprocessing', 'asyncio', 'concurrent'
            ],
            'development_tools': [
                'pytest', 'unittest', 'black', 'flake8', 'pylint',
                'mypy', 'pre-commit', 'tox', 'coverage'
            ]
        }

    def find_python_environments(self):
        """البحث عن جميع بيئات Python"""
        print("🔍 البحث عن بيئات Python...")
        
        environments = []
        
        # البيئة الحالية
        current_env = {
            'name': 'current',
            'path': sys.executable,
            'version': sys.version,
            'is_venv': hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
        }
        environments.append(current_env)
        
        # البحث عن بيئات أخرى
        search_patterns = [
            'C:/Python*',
            'C:/Program Files/Python*',
            'C:/Program Files (x86)/Python*',
            'C:/Users/<USER>/AppData/Local/Programs/Python*',
            'C:/Users/<USER>/Anaconda*',
            'C:/Users/<USER>/Miniconda*',
            'C:/ProgramData/Anaconda*',
            'C:/ProgramData/Miniconda*',
            'C:/Users/<USER>/.conda/envs/*',
            'C:/Users/<USER>/envs/*',
            'C:/Users/<USER>/.virtualenvs/*'
        ]
        
        for pattern in search_patterns:
            matches = glob.glob(pattern)
            for match in matches:
                if os.path.isdir(match):
                    python_exe = None
                    # البحث عن python.exe
                    possible_paths = [
                        os.path.join(match, 'python.exe'),
                        os.path.join(match, 'Scripts', 'python.exe'),
                        os.path.join(match, 'bin', 'python'),
                        os.path.join(match, 'bin', 'python3')
                    ]
                    
                    for path in possible_paths:
                        if os.path.exists(path):
                            python_exe = path
                            break
                    
                    if python_exe:
                        env_info = {
                            'name': os.path.basename(match),
                            'path': python_exe,
                            'directory': match,
                            'is_conda': 'conda' in match.lower() or 'anaconda' in match.lower(),
                            'is_venv': 'venv' in match.lower() or 'env' in match.lower()
                        }
                        environments.append(env_info)
        
        self.scan_results['python_environments'] = environments
        print(f"✅ وجد {len(environments)} بيئة Python")
        return environments

    def scan_environment_packages(self, python_path):
        """فحص المكتبات في بيئة محددة"""
        try:
            # تشغيل pip list
            result = subprocess.run(
                [python_path, '-m', 'pip', 'list', '--format=freeze'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                packages = {}
                for line in result.stdout.strip().split('\n'):
                    if line and '==' in line:
                        name, version = line.split('==', 1)
                        packages[name.lower()] = version
                return packages
            else:
                print(f"⚠️ خطأ في فحص {python_path}: {result.stderr}")
                return {}
                
        except Exception as e:
            print(f"❌ خطأ في فحص {python_path}: {e}")
            return {}

    def categorize_packages(self, all_packages):
        """تصنيف المكتبات حسب الاستخدام"""
        categorized = {category: [] for category in self.package_categories.keys()}
        categorized['other'] = []
        
        for package_name in all_packages.keys():
            categorized_flag = False
            
            for category, keywords in self.package_categories.items():
                if any(keyword in package_name.lower() for keyword in keywords):
                    categorized[category].append(package_name)
                    categorized_flag = True
                    break
            
            if not categorized_flag:
                categorized['other'].append(package_name)
        
        return categorized

    def generate_comprehensive_requirements(self):
        """إنشاء ملف متطلبات شامل"""
        print("📝 إنشاء ملف المتطلبات الشامل...")
        
        # جمع جميع المكتبات من جميع البيئات
        all_packages = {}
        
        for env in self.scan_results['python_environments']:
            print(f"🔍 فحص بيئة: {env['name']}")
            packages = self.scan_environment_packages(env['path'])
            
            for package, version in packages.items():
                if package not in all_packages:
                    all_packages[package] = {
                        'version': version,
                        'environments': [env['name']]
                    }
                else:
                    # إضافة البيئة للقائمة
                    if env['name'] not in all_packages[package]['environments']:
                        all_packages[package]['environments'].append(env['name'])
                    
                    # تحديث الإصدار إذا كان أحدث
                    if version != all_packages[package]['version']:
                        all_packages[package]['version'] = f"{all_packages[package]['version']}|{version}"
        
        self.scan_results['all_packages'] = all_packages
        self.scan_results['categorized_packages'] = self.categorize_packages(all_packages)
        
        # إحصائيات
        self.scan_results['statistics'] = {
            'total_packages': len(all_packages),
            'total_environments': len(self.scan_results['python_environments']),
            'categories_count': {cat: len(packages) for cat, packages in self.scan_results['categorized_packages'].items()}
        }
        
        return all_packages

    def save_results(self, output_dir='SHARED_REQUIREMENTS'):
        """حفظ النتائج في ملفات مختلفة"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # حفظ النتائج الكاملة كـ JSON
        with open(output_path / 'comprehensive_scan_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        # إنشاء ملف requirements شامل
        with open(output_path / 'comprehensive_requirements.txt', 'w', encoding='utf-8') as f:
            f.write("# 🔍 ملف المتطلبات الشامل - Comprehensive Requirements\n")
            f.write(f"# تم إنشاؤه في: {self.scan_results['scan_date']}\n")
            f.write(f"# إجمالي المكتبات: {self.scan_results['statistics']['total_packages']}\n")
            f.write(f"# إجمالي البيئات: {self.scan_results['statistics']['total_environments']}\n\n")
            
            for package, info in sorted(self.scan_results['all_packages'].items()):
                f.write(f"{package}=={info['version']}\n")
        
        # إنشاء ملفات requirements مصنفة
        for category, packages in self.scan_results['categorized_packages'].items():
            if packages:
                with open(output_path / f'requirements_{category}.txt', 'w', encoding='utf-8') as f:
                    f.write(f"# 📦 متطلبات {category.upper()}\n")
                    f.write(f"# عدد المكتبات: {len(packages)}\n\n")
                    
                    for package in sorted(packages):
                        if package in self.scan_results['all_packages']:
                            version = self.scan_results['all_packages'][package]['version']
                            f.write(f"{package}=={version}\n")
        
        print(f"✅ تم حفظ النتائج في مجلد: {output_path}")

def main():
    """الدالة الرئيسية"""
    print("🔍 بدء الفحص الشامل للمكتبات المثبتة")
    print("=" * 60)
    
    scanner = ComprehensivePackageScanner()
    
    # البحث عن بيئات Python
    environments = scanner.find_python_environments()
    
    # فحص المكتبات
    all_packages = scanner.generate_comprehensive_requirements()
    
    # حفظ النتائج
    scanner.save_results()
    
    # عرض الإحصائيات
    stats = scanner.scan_results['statistics']
    print("\n📊 إحصائيات الفحص:")
    print(f"   📦 إجمالي المكتبات: {stats['total_packages']}")
    print(f"   🐍 إجمالي بيئات Python: {stats['total_environments']}")
    print("\n📋 توزيع المكتبات حسب الفئة:")
    for category, count in stats['categories_count'].items():
        if count > 0:
            print(f"   {category}: {count} مكتبة")
    
    print("\n🎯 تم إكمال الفحص الشامل بنجاح!")
    return scanner.scan_results

if __name__ == "__main__":
    main()
