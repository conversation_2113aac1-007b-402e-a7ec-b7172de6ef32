# حاوية n8n معزولة وآمنة لنظام أنوبيس
FROM n8nio/n8n:latest

# إعداد متغيرات البيئة المحسنة للأمان
ENV N8N_ENCRYPTION_KEY=anubis_secure_encryption_key_2025_advanced
ENV N8N_USER_MANAGEMENT_DISABLED=false
ENV N8N_SECURE_COOKIE=true
ENV N8N_PROTOCOL=http
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV WEBHOOK_URL=http://localhost:5678/
ENV N8N_METRICS=true
ENV N8N_LOG_LEVEL=info
ENV N8N_LOG_OUTPUT=console,file
ENV N8N_LOG_FILE_LOCATION=/home/<USER>/.n8n/logs/
ENV N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
ENV N8N_RUNNERS_ENABLED=true
ENV DB_TYPE=sqlite
ENV DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

# إنشاء مستخدم غير مميز للأمان
USER root
RUN groupadd -g 1003 anubis_workflows && \
    useradd -u 1003 -g 1003 -m -s /bin/bash anubis_workflows && \
    mkdir -p /app/workflows /app/credentials /app/nodes /app/logs && \
    chown -R anubis_workflows:anubis_workflows /app && \
    chown -R anubis_workflows:anubis_workflows /home/<USER>/.n8n

# تثبيت الأدوات الإضافية للأمان
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    jq \
    openssl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# نسخ الملفات المخصصة
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/nodes/ /home/<USER>/.n8n/nodes/
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/credentials/ /home/<USER>/.n8n/credentials/
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/workflows/ /home/<USER>/.n8n/workflows/

# التبديل للمستخدم غير المميز
USER anubis_workflows

# إنشاء المجلدات المطلوبة
RUN mkdir -p /home/<USER>/.n8n/logs /home/<USER>/.n8n/backups

# فحص صحة متقدم
HEALTHCHECK --interval=30s --timeout=15s --start-period=45s --retries=3 \
    CMD curl -f http://localhost:5678/healthz || exit 1

# المنفذ المكشوف
EXPOSE 5678

# إنشاء سكريبت التهيئة المخصص لأنوبيس
USER root
RUN echo '#!/bin/bash' > /app/init-anubis.sh && \
    echo 'echo "🏺 تهيئة نظام أنوبيس المعزول مع N8N..."' >> /app/init-anubis.sh && \
    echo 'if [ -d "/home/<USER>/.n8n/nodes" ]; then' >> /app/init-anubis.sh && \
    echo '    echo "📦 تحميل العقد المخصصة لأنوبيس..."' >> /app/init-anubis.sh && \
    echo '    chown -R node:node /home/<USER>/.n8n/nodes/' >> /app/init-anubis.sh && \
    echo '    chmod -R 755 /home/<USER>/.n8n/nodes/' >> /app/init-anubis.sh && \
    echo 'fi' >> /app/init-anubis.sh && \
    echo 'if [ -d "/home/<USER>/.n8n/credentials" ]; then' >> /app/init-anubis.sh && \
    echo '    echo "🔐 تحميل بيانات الاعتماد المخصصة..."' >> /app/init-anubis.sh && \
    echo '    chown -R node:node /home/<USER>/.n8n/credentials/' >> /app/init-anubis.sh && \
    echo '    chmod -R 600 /home/<USER>/.n8n/credentials/' >> /app/init-anubis.sh && \
    echo 'fi' >> /app/init-anubis.sh && \
    echo 'mkdir -p /home/<USER>/.n8n/logs /home/<USER>/.n8n/backups' >> /app/init-anubis.sh && \
    echo 'chown -R node:node /home/<USER>/.n8n/' >> /app/init-anubis.sh && \
    echo 'echo "✅ تم تهيئة نظام أنوبيس بنجاح!"' >> /app/init-anubis.sh && \
    chmod +x /app/init-anubis.sh

# إضافة ملصقات للتعريف
LABEL maintainer="Anubis System"
LABEL version="2.0.0"
LABEL description="Anubis Isolated N8N System with Custom Nodes"
LABEL anubis.component="automation"
LABEL anubis.isolation.level="advanced"

# العودة للمستخدم غير المميز
USER anubis_workflows

# نقطة الدخول المحسنة مع التهيئة المخصصة
ENTRYPOINT ["/bin/bash", "-c", "/app/init-anubis.sh && exec tini -- /docker-entrypoint.sh n8n start"]
