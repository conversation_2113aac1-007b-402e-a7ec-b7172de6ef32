{"workflows_security_policy": {"version": "1.0", "description": "سياسات الأمان المتقدمة لأنظمة سير العمل والأتمتة", "last_updated": "2025-07-19T17:33:20.980370"}, "n8n_security": {"authentication": {"user_management_enabled": true, "secure_cookies": true, "session_timeout_minutes": 60, "password_policy": {"min_length": 12, "require_special_chars": true, "require_numbers": true, "require_uppercase": true}}, "workflow_security": {"execution_isolation": true, "credential_encryption": true, "webhook_security": true, "external_access_control": true}, "audit_logging": {"enabled": true, "log_all_executions": true, "log_credential_access": true, "log_workflow_changes": true}}, "credentials_management": {"encryption": {"algorithm": "AES-256-GCM", "key_rotation_days": 30, "secure_storage": true}, "access_control": {"role_based_access": true, "credential_sharing_policy": "restricted", "audit_credential_usage": true}, "vault_integration": {"enabled": true, "auto_sync": true, "backup_encryption": true}}, "workflow_execution": {"sandbox_mode": true, "resource_limits": {"max_execution_time_minutes": 30, "max_memory_mb": 512, "max_concurrent_executions": 10}, "network_restrictions": {"allowed_domains": ["api.openai.com", "generativelanguage.googleapis.com", "api.anthropic.com"], "blocked_private_networks": true, "webhook_url_validation": true}}, "monitoring_and_alerting": {"real_time_monitoring": true, "performance_metrics": true, "security_alerts": {"failed_authentication_attempts": true, "suspicious_workflow_executions": true, "unauthorized_credential_access": true, "unusual_network_activity": true}, "compliance_reporting": {"enabled": true, "report_schedule": "weekly", "include_audit_trail": true}}, "backup_and_recovery": {"automated_backups": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 30, "encryption": true}, "disaster_recovery": {"backup_workflows": true, "backup_credentials": true, "backup_configurations": true, "recovery_testing": "monthly"}}}