#!/usr/bin/env python3
"""
🔍 فاحص نظام أنوبيس الشامل
Anubis Comprehensive System Scanner

مطور بالتعاون مع Gemini CLI لفحص جميع مكونات النظام
"""

import json
import os
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import subprocess
import ast
import re


class AnubisSystemScanner:
    """فاحص شامل لنظام أنوبيس"""
    
    def __init__(self, project_root: str = "anubis"):
        self.project_root = Path(project_root)
        self.scan_results = {
            "scan_timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "components": {},
            "issues": [],
            "recommendations": [],
            "overall_score": 0,
            "gemini_analysis": "تم الفحص بالتعاون مع Gemini CLI"
        }
        
    def scan_all_components(self):
        """فحص جميع مكونات المشروع حسب توصيات Gemini CLI"""
        print("🔍 بدء الفحص الشامل لنظام أنوبيس...")
        print("🤖 تم تطوير هذا الفاحص بالتعاون مع Gemini CLI")
        
        # فحص الوكلاء الذكيين
        self.scan_agents()
        
        # فحص قاعدة البيانات
        self.scan_database()
        
        # فحص واجهة API
        self.scan_api()
        
        # فحص نظام الإضافات
        self.scan_plugins()
        
        # فحص نظام الاختبارات
        self.scan_tests()
        
        # فحص التكامل مع الخدمات الخارجية
        self.scan_integrations()
        
        # فحص الملف الرئيسي
        self.scan_main_file()
        
        # حساب النتيجة الإجمالية
        self.calculate_overall_score()
        
        return self.scan_results
    
    def scan_agents(self):
        """فحص نظام الوكلاء الذكيين حسب توصيات Gemini CLI"""
        print("🤖 فحص الوكلاء الذكيين...")
        
        agents_dir = self.project_root / "agents"
        agents_results = {
            "directory_exists": agents_dir.exists(),
            "agents_found": [],
            "code_quality_issues": [],
            "error_handling_check": [],
            "performance_issues": [],
            "score": 0
        }
        
        if not agents_dir.exists():
            agents_results["code_quality_issues"].append("مجلد الوكلاء غير موجود")
            self.scan_results["components"]["agents"] = agents_results
            return
            
        # البحث عن ملفات الوكلاء
        for agent_file in agents_dir.glob("*.py"):
            if agent_file.name.startswith("__"):
                continue
                
            agent_name = agent_file.stem
            agents_results["agents_found"].append(agent_name)
            
            # فحص جودة الكود
            self.check_agent_code_quality(agent_file, agents_results)
            
        # تقييم النتائج
        agents_results["score"] = self.calculate_component_score(agents_results)
        self.scan_results["components"]["agents"] = agents_results
    
    def scan_database(self):
        """فحص تكامل قاعدة البيانات حسب توصيات Gemini CLI"""
        print("🗄️ فحص قاعدة البيانات...")
        
        db_dir = self.project_root / "database"
        db_results = {
            "directory_exists": db_dir.exists(),
            "config_files": [],
            "security_issues": [],
            "connection_files": [],
            "schema_files": [],
            "score": 0
        }
        
        if not db_dir.exists():
            db_results["security_issues"].append("مجلد قاعدة البيانات غير موجود")
            self.scan_results["components"]["database"] = db_results
            return
            
        # فحص ملفات التكوين
        config_files = list(db_dir.glob("*config*.json")) + list(db_dir.glob("*config*.py"))
        db_results["config_files"] = [str(f.name) for f in config_files]
        
        # فحص ملفات الاتصال
        connection_files = list(db_dir.glob("*connector*.py")) + list(db_dir.glob("*manager*.py"))
        db_results["connection_files"] = [str(f.name) for f in connection_files]
        
        # فحص أمان الاتصال
        for config_file in config_files:
            self.check_database_security(config_file, db_results)
            
        # البحث عن ملفات المخطط
        schema_files = list(db_dir.glob("*.sql")) + list(db_dir.glob("*schema*"))
        db_results["schema_files"] = [str(f.name) for f in schema_files]
        
        db_results["score"] = self.calculate_component_score(db_results)
        self.scan_results["components"]["database"] = db_results
    
    def scan_api(self):
        """فحص واجهة برمجة التطبيقات حسب توصيات Gemini CLI"""
        print("🌐 فحص واجهة API...")
        
        api_dir = self.project_root / "api"
        api_results = {
            "directory_exists": api_dir.exists(),
            "api_files": [],
            "openapi_spec": None,
            "endpoints_found": [],
            "security_features": [],
            "documentation_issues": [],
            "score": 0
        }
        
        if not api_dir.exists():
            api_results["documentation_issues"].append("مجلد API غير موجود")
            self.scan_results["components"]["api"] = api_results
            return
            
        # البحث عن ملفات API
        api_files = list(api_dir.glob("*.py"))
        api_results["api_files"] = [str(f.name) for f in api_files]
        
        # فحص مواصفات OpenAPI
        openapi_file = api_dir / "anubis_openapi.json"
        if openapi_file.exists():
            api_results["openapi_spec"] = "موجود"
            try:
                with open(openapi_file, 'r', encoding='utf-8') as f:
                    openapi_data = json.load(f)
                    api_results["endpoints_found"] = list(openapi_data.get("paths", {}).keys())
            except Exception as e:
                api_results["documentation_issues"].append(f"خطأ في قراءة OpenAPI: {str(e)}")
        else:
            api_results["openapi_spec"] = "غير موجود"
            api_results["documentation_issues"].append("ملف OpenAPI غير موجود")
            
        api_results["score"] = self.calculate_component_score(api_results)
        self.scan_results["components"]["api"] = api_results
    
    def scan_plugins(self):
        """فحص نظام الإضافات حسب توصيات Gemini CLI"""
        print("🔌 فحص نظام الإضافات...")
        
        plugins_dir = self.project_root / "plugins"
        plugins_results = {
            "directory_exists": plugins_dir.exists(),
            "plugin_manager_exists": False,
            "base_plugin_exists": False,
            "plugins_found": [],
            "isolation_issues": [],
            "security_concerns": [],
            "score": 0
        }
        
        if not plugins_dir.exists():
            plugins_results["isolation_issues"].append("مجلد الإضافات غير موجود")
            self.scan_results["components"]["plugins"] = plugins_results
            return
            
        # فحص مدير الإضافات
        plugin_manager = plugins_dir / "plugin_manager.py"
        plugins_results["plugin_manager_exists"] = plugin_manager.exists()
        
        # فحص الإضافة الأساسية
        base_plugin = plugins_dir / "base_plugin.py"
        plugins_results["base_plugin_exists"] = base_plugin.exists()
        
        # البحث عن الإضافات
        plugin_files = [f for f in plugins_dir.glob("*.py") 
                       if not f.name.startswith("__") and f.name not in ["plugin_manager.py", "base_plugin.py"]]
        plugins_results["plugins_found"] = [f.stem for f in plugin_files]
        
        plugins_results["score"] = self.calculate_component_score(plugins_results)
        self.scan_results["components"]["plugins"] = plugins_results
    
    def scan_tests(self):
        """فحص نظام الاختبارات حسب توصيات Gemini CLI"""
        print("🧪 فحص نظام الاختبارات...")
        
        tests_dir = self.project_root / "tests"
        tests_results = {
            "directory_exists": tests_dir.exists(),
            "test_files": [],
            "test_runner_exists": False,
            "coverage_estimate": 0,
            "test_quality_issues": [],
            "environment_isolation": [],
            "score": 0
        }
        
        if not tests_dir.exists():
            tests_results["test_quality_issues"].append("مجلد الاختبارات غير موجود")
            self.scan_results["components"]["tests"] = tests_results
            return
            
        # البحث عن ملفات الاختبار
        test_files = list(tests_dir.glob("test_*.py")) + list(tests_dir.glob("*_test.py"))
        tests_results["test_files"] = [f.stem for f in test_files]
        
        # فحص منفذ الاختبارات
        test_runner = tests_dir / "run_all_tests.py"
        tests_results["test_runner_exists"] = test_runner.exists()
        
        # تقدير التغطية
        tests_results["coverage_estimate"] = min(len(test_files) * 15, 100)  # تقدير تقريبي
        
        tests_results["score"] = self.calculate_component_score(tests_results)
        self.scan_results["components"]["tests"] = tests_results

    def scan_integrations(self):
        """فحص التكامل مع الخدمات الخارجية"""
        print("🔗 فحص التكامل مع الخدمات الخارجية...")

        integrations_results = {
            "langsmith_config": False,
            "ollama_integration": False,
            "gemini_integration": False,
            "config_security": [],
            "connectivity_issues": [],
            "score": 0
        }

        # فحص تكوين LangSmith
        langsmith_config = self.project_root / "configs" / "langsmith_config.json"
        integrations_results["langsmith_config"] = langsmith_config.exists()

        # فحص تكوين AI
        ai_config = self.project_root / "configs" / "ai_config.json"
        if ai_config.exists():
            try:
                with open(ai_config, 'r', encoding='utf-8') as f:
                    ai_data = json.load(f)
                    if "ollama" in ai_data:
                        integrations_results["ollama_integration"] = True
                    if "gemini" in ai_data:
                        integrations_results["gemini_integration"] = True
            except Exception as e:
                integrations_results["connectivity_issues"].append(f"خطأ في قراءة تكوين AI: {str(e)}")

        integrations_results["score"] = self.calculate_component_score(integrations_results)
        self.scan_results["components"]["integrations"] = integrations_results

    def scan_main_file(self):
        """فحص الملف الرئيسي"""
        print("📄 فحص الملف الرئيسي...")

        main_file = self.project_root / "main.py"
        main_results = {
            "file_exists": main_file.exists(),
            "imports_check": [],
            "error_handling": [],
            "configuration": [],
            "score": 0
        }

        if main_file.exists():
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # فحص الاستيرادات
                if "from core.assistant_system import" in content:
                    main_results["imports_check"].append("استيراد النظام الأساسي: ✓")
                else:
                    main_results["imports_check"].append("استيراد النظام الأساسي: ✗")

                # فحص معالجة الأخطاء
                if "try:" in content and "except" in content:
                    main_results["error_handling"].append("معالجة الأخطاء: ✓")
                else:
                    main_results["error_handling"].append("معالجة الأخطاء: ✗")

            except Exception as e:
                main_results["error_handling"].append(f"خطأ في قراءة الملف الرئيسي: {str(e)}")
        else:
            main_results["imports_check"].append("الملف الرئيسي غير موجود")

        main_results["score"] = self.calculate_component_score(main_results)
        self.scan_results["components"]["main_file"] = main_results

    def check_agent_code_quality(self, agent_file: Path, results: Dict):
        """فحص جودة كود الوكيل"""
        try:
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # فحص التعليقات
            if content.count('#') < 5:
                results["code_quality_issues"].append(f"{agent_file.name}: قلة التعليقات")

            # فحص معالجة الأخطاء
            if "try:" not in content or "except" not in content:
                results["error_handling_check"].append(f"{agent_file.name}: لا توجد معالجة أخطاء")

            # فحص الأداء (البحث عن حلقات مشبوهة)
            if content.count("while True:") > 2:
                results["performance_issues"].append(f"{agent_file.name}: حلقات لا نهائية محتملة")

        except Exception as e:
            results["code_quality_issues"].append(f"خطأ في قراءة {agent_file.name}: {str(e)}")

    def check_database_security(self, config_file: Path, results: Dict):
        """فحص أمان تكوين قاعدة البيانات"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # فحص كلمات المرور المكشوفة
            if "password" in content.lower() and "your_password" in content.lower():
                results["security_issues"].append(f"{config_file.name}: كلمة مرور افتراضية")

            # فحص المفاتيح المكشوفة
            if "api_key" in content.lower() and len(content) > 100:
                results["security_issues"].append(f"{config_file.name}: مفاتيح API مكشوفة محتملة")

        except Exception as e:
            results["security_issues"].append(f"خطأ في قراءة {config_file.name}: {str(e)}")

    def calculate_component_score(self, component_results: Dict) -> int:
        """حساب نقاط المكون"""
        score = 50  # نقطة البداية

        # إضافة نقاط للميزات الموجودة
        for key, value in component_results.items():
            if key.endswith("_exists") and value:
                score += 10
            elif key.endswith("_found") and isinstance(value, list) and len(value) > 0:
                score += len(value) * 5
            elif key.endswith("_issues") and isinstance(value, list):
                score -= len(value) * 5

        return max(0, min(100, score))  # ضمان أن النقاط بين 0 و 100

    def calculate_overall_score(self):
        """حساب النقاط الإجمالية"""
        if not self.scan_results["components"]:
            self.scan_results["overall_score"] = 0
            return

        total_score = sum(comp.get("score", 0) for comp in self.scan_results["components"].values())
        component_count = len(self.scan_results["components"])

        self.scan_results["overall_score"] = total_score // component_count if component_count > 0 else 0

        # إضافة التوصيات
        self.generate_recommendations()

    def generate_recommendations(self):
        """إنشاء التوصيات بناءً على النتائج"""
        recommendations = []

        for component_name, component_data in self.scan_results["components"].items():
            score = component_data.get("score", 0)

            if score < 50:
                recommendations.append(f"🔴 {component_name}: يحتاج تحسين عاجل (النقاط: {score})")
            elif score < 75:
                recommendations.append(f"🟡 {component_name}: يحتاج تحسين (النقاط: {score})")
            else:
                recommendations.append(f"🟢 {component_name}: حالة جيدة (النقاط: {score})")

        self.scan_results["recommendations"] = recommendations

    def save_results(self, output_file: str = None):
        """حفظ نتائج الفحص"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"anubis_scan_report_{timestamp}.json"

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)

        print(f"📄 تم حفظ تقرير الفحص في: {output_file}")
        return output_file

    def print_summary(self):
        """طباعة ملخص النتائج"""
        print("\n" + "="*60)
        print("🏺 تقرير فحص نظام أنوبيس الشامل")
        print("="*60)
        print(f"📅 تاريخ الفحص: {self.scan_results['scan_timestamp']}")
        print(f"📁 مجلد المشروع: {self.scan_results['project_root']}")
        print(f"🎯 النقاط الإجمالية: {self.scan_results['overall_score']}/100")
        print("\n📊 نتائج المكونات:")

        for component_name, component_data in self.scan_results["components"].items():
            score = component_data.get("score", 0)
            status = "🟢" if score >= 75 else "🟡" if score >= 50 else "🔴"
            print(f"  {status} {component_name}: {score}/100")

        print("\n💡 التوصيات:")
        for rec in self.scan_results["recommendations"]:
            print(f"  {rec}")

        print("\n🤖 تم تطوير هذا الفاحص بالتعاون مع Gemini CLI")
        print("="*60)


def main():
    """الدالة الرئيسية"""
    print("🔍 فاحص نظام أنوبيس الشامل")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("-" * 50)

    scanner = AnubisSystemScanner()
    results = scanner.scan_all_components()

    # طباعة الملخص
    scanner.print_summary()

    # حفظ النتائج
    report_file = scanner.save_results()

    print(f"\n✅ تم إكمال الفحص بنجاح!")
    print(f"📄 التقرير محفوظ في: {report_file}")


if __name__ == "__main__":
    main()
