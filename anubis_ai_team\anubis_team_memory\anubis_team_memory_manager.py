#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 مدير ذاكرة فريق الذكاء الاصطناعي - أنوبيس
Anubis AI Team Memory Manager
"""

import json
import os
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import uuid

class AnubisTeamMemoryManager:
    """مدير ذاكرة فريق الذكاء الاصطناعي"""
    
    def __init__(self):
        self.memory_root = Path("anubis_team_memory")
        self.ensure_memory_structure()
        
        # مسارات الذاكرة الأساسية
        self.core_memory_path = self.memory_root / "core_memory"
        self.experiences_path = self.memory_root / "experiences"
        self.insights_path = self.memory_root / "insights"
        self.learning_path = self.memory_root / "learning"
        self.connections_path = self.memory_root / "connections"
        
        # تحميل قواعد البيانات
        self.knowledge_base = self.load_knowledge_base()
        self.model_profiles = self.load_model_profiles()
        self.collaboration_patterns = self.load_collaboration_patterns()
    
    def ensure_memory_structure(self):
        """إنشاء بنية مجلدات الذاكرة"""
        directories = [
            "anubis_team_memory/core_memory",
            "anubis_team_memory/experiences/development_tasks",
            "anubis_team_memory/experiences/analysis_tasks", 
            "anubis_team_memory/experiences/planning_tasks",
            "anubis_team_memory/experiences/innovation_tasks",
            "anubis_team_memory/experiences/review_tasks",
            "anubis_team_memory/insights",
            "anubis_team_memory/learning",
            "anubis_team_memory/connections"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def load_knowledge_base(self) -> Dict:
        """تحميل قاعدة المعرفة الأساسية"""
        kb_file = self.core_memory_path / "team_knowledge_base.json"
        if kb_file.exists():
            with open(kb_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # إنشاء قاعدة معرفة أساسية
        default_kb = {
            "created_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "total_experiences": 0,
            "successful_patterns": {},
            "failure_patterns": {},
            "model_specializations": {
                "phi3:mini": ["quick_analysis", "error_detection", "simple_tasks"],
                "mistral:7b": ["development", "coding", "technical_solutions"],
                "llama3:8b": ["planning", "strategy", "decision_making"],
                "strikegpt-r1-zero-8b": ["innovation", "creative_solutions", "brainstorming"],
                "Qwen2.5-VL-7B": ["visual_analysis", "ui_review", "documentation"],
                "gemini_cli": ["coordination", "review", "quality_assurance"]
            },
            "collaboration_synergies": {},
            "learning_insights": []
        }
        
        self.save_knowledge_base(default_kb)
        return default_kb
    
    def load_model_profiles(self) -> Dict:
        """تحميل ملفات أداء النماذج"""
        profiles_file = self.core_memory_path / "model_performance_profiles.json"
        if profiles_file.exists():
            with open(profiles_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # ملفات أداء افتراضية
        default_profiles = {
            "phi3:mini": {
                "average_response_time": 2.5,
                "success_rate": 0.85,
                "best_task_types": ["analysis", "quick_review"],
                "performance_trends": [],
                "strengths": ["speed", "efficiency", "resource_light"],
                "weaknesses": ["complex_reasoning", "long_context"]
            },
            "mistral:7b": {
                "average_response_time": 8.0,
                "success_rate": 0.92,
                "best_task_types": ["development", "coding", "technical"],
                "performance_trends": [],
                "strengths": ["coding_quality", "technical_accuracy", "problem_solving"],
                "weaknesses": ["creative_tasks", "visual_analysis"]
            },
            "llama3:8b": {
                "average_response_time": 12.0,
                "success_rate": 0.94,
                "best_task_types": ["planning", "strategy", "analysis"],
                "performance_trends": [],
                "strengths": ["strategic_thinking", "comprehensive_analysis", "decision_making"],
                "weaknesses": ["speed", "simple_tasks"]
            },
            "strikegpt-r1-zero-8b": {
                "average_response_time": 10.0,
                "success_rate": 0.88,
                "best_task_types": ["innovation", "creative", "brainstorming"],
                "performance_trends": [],
                "strengths": ["creativity", "innovation", "unique_solutions"],
                "weaknesses": ["routine_tasks", "strict_requirements"]
            },
            "Qwen2.5-VL-7B": {
                "average_response_time": 15.0,
                "success_rate": 0.90,
                "best_task_types": ["visual_analysis", "ui_review", "documentation"],
                "performance_trends": [],
                "strengths": ["visual_understanding", "multimodal_analysis", "detail_oriented"],
                "weaknesses": ["text_only_tasks", "speed"]
            }
        }
        
        self.save_model_profiles(default_profiles)
        return default_profiles
    
    def load_collaboration_patterns(self) -> Dict:
        """تحميل أنماط التعاون"""
        patterns_file = self.connections_path / "collaboration_patterns.json"
        if patterns_file.exists():
            with open(patterns_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # أنماط تعاون افتراضية
        default_patterns = {
            "successful_combinations": {
                "development_tasks": ["mistral:7b", "phi3:mini", "strikegpt-r1-zero-8b"],
                "analysis_tasks": ["phi3:mini", "mistral:7b"],
                "planning_tasks": ["llama3:8b", "gemini_cli"],
                "innovation_tasks": ["strikegpt-r1-zero-8b", "llama3:8b", "Qwen2.5-VL-7B"],
                "review_tasks": ["gemini_cli", "llama3:8b", "mistral:7b"]
            },
            "synergy_scores": {},
            "optimal_sequences": {},
            "coordination_strategies": {}
        }
        
        self.save_collaboration_patterns(default_patterns)
        return default_patterns
    
    def store_experience(self, task_data: Dict, results: Dict, insights: Dict) -> str:
        """تخزين تجربة جديدة"""
        
        # إنشاء معرف فريد للتجربة
        experience_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # بناء بيانات التجربة
        experience = {
            "id": experience_id,
            "timestamp": timestamp,
            "task_data": task_data,
            "results": results,
            "insights": insights,
            "success_score": self.calculate_success_score(results),
            "lessons_learned": self.extract_lessons(task_data, results, insights),
            "performance_metrics": self.calculate_performance_metrics(results)
        }
        
        # تحديد مجلد التخزين حسب نوع المهمة
        task_type = task_data.get("task_type", "general")
        storage_path = self.experiences_path / f"{task_type}_tasks"
        storage_path.mkdir(exist_ok=True)
        
        # حفظ التجربة
        experience_file = storage_path / f"experience_{experience_id}.json"
        with open(experience_file, 'w', encoding='utf-8') as f:
            json.dump(experience, f, ensure_ascii=False, indent=2)
        
        # تحديث قاعدة المعرفة
        self.update_knowledge_base(experience)
        
        # تحديث ملفات أداء النماذج
        self.update_model_profiles(experience)
        
        print(f"🧠 تم حفظ التجربة: {experience_id}")
        return experience_id
    
    def retrieve_similar_experiences(self, task_description: str, task_type: str = None, limit: int = 5) -> List[Dict]:
        """استرجاع التجارب المشابهة"""
        
        similar_experiences = []
        
        # تحديد مجلدات البحث
        search_paths = []
        if task_type:
            search_paths.append(self.experiences_path / f"{task_type}_tasks")
        else:
            # البحث في جميع أنواع المهام
            for task_dir in self.experiences_path.iterdir():
                if task_dir.is_dir():
                    search_paths.append(task_dir)
        
        # البحث في التجارب
        for search_path in search_paths:
            if not search_path.exists():
                continue
                
            for experience_file in search_path.glob("experience_*.json"):
                try:
                    with open(experience_file, 'r', encoding='utf-8') as f:
                        experience = json.load(f)
                    
                    # حساب درجة التشابه
                    similarity_score = self.calculate_similarity(
                        task_description, 
                        experience["task_data"].get("task_description", "")
                    )
                    
                    if similarity_score > 0.3:  # حد أدنى للتشابه
                        experience["similarity_score"] = similarity_score
                        similar_experiences.append(experience)
                        
                except Exception as e:
                    print(f"⚠️ خطأ في قراءة التجربة {experience_file}: {e}")
        
        # ترتيب حسب درجة التشابه
        similar_experiences.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return similar_experiences[:limit]
    
    def generate_recommendations(self, task_type: str, task_description: str = "") -> Dict:
        """توليد توصيات للمهمة"""
        
        # البحث عن تجارب مشابهة
        similar_experiences = self.retrieve_similar_experiences(task_description, task_type)
        
        # تحليل الأنماط الناجحة
        successful_patterns = self.analyze_successful_patterns(similar_experiences)
        
        # اختيار أفضل تشكيل للفريق
        optimal_team = self.recommend_team_composition(task_type, similar_experiences)
        
        # توصيات تحسين سير العمل
        workflow_optimizations = self.suggest_workflow_optimizations(task_type, similar_experiences)
        
        recommendations = {
            "task_type": task_type,
            "optimal_team_composition": optimal_team,
            "workflow_optimizations": workflow_optimizations,
            "successful_patterns": successful_patterns,
            "estimated_success_probability": self.estimate_success_probability(task_type, optimal_team),
            "similar_experiences_count": len(similar_experiences),
            "confidence_level": self.calculate_confidence_level(similar_experiences),
            "generated_at": datetime.now().isoformat()
        }
        
        return recommendations
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """حساب درجة التشابه بين النصوص (مبسط)"""
        if not text1 or not text2:
            return 0.0
        
        # تحويل إلى كلمات
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        # حساب التشابه باستخدام Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def calculate_success_score(self, results: Dict) -> float:
        """حساب درجة نجاح المهمة"""
        # منطق مبسط لحساب النجاح
        successful_phases = len([r for r in results.get("execution_log", []) if r.get("success", False)])
        total_phases = len(results.get("execution_log", []))
        
        if total_phases == 0:
            return 0.0
        
        return successful_phases / total_phases
    
    def extract_lessons(self, task_data: Dict, results: Dict, insights: Dict) -> List[str]:
        """استخراج الدروس المستفادة"""
        lessons = []
        
        # تحليل النجاحات
        successful_models = [r["model"] for r in results.get("execution_log", []) if r.get("success", False)]
        if successful_models:
            lessons.append(f"النماذج الناجحة: {', '.join(set(successful_models))}")
        
        # تحليل الفشل
        failed_models = [r["model"] for r in results.get("execution_log", []) if not r.get("success", False)]
        if failed_models:
            lessons.append(f"النماذج التي واجهت صعوبات: {', '.join(set(failed_models))}")
        
        # إضافة رؤى إضافية
        if insights.get("key_insights"):
            lessons.extend(insights["key_insights"])
        
        return lessons
    
    def calculate_performance_metrics(self, results: Dict) -> Dict:
        """حساب مؤشرات الأداء"""
        execution_log = results.get("execution_log", [])
        
        if not execution_log:
            return {}
        
        # حساب المؤشرات
        total_time = sum(r.get("execution_time", 0) for r in execution_log)
        successful_phases = len([r for r in execution_log if r.get("success", False)])
        total_phases = len(execution_log)
        
        return {
            "total_execution_time": total_time,
            "average_phase_time": total_time / total_phases if total_phases > 0 else 0,
            "success_rate": successful_phases / total_phases if total_phases > 0 else 0,
            "total_phases": total_phases,
            "successful_phases": successful_phases
        }
    
    def update_knowledge_base(self, experience: Dict):
        """تحديث قاعدة المعرفة"""
        self.knowledge_base["total_experiences"] += 1
        self.knowledge_base["last_updated"] = datetime.now().isoformat()
        
        # إضافة الرؤى الجديدة
        if experience.get("lessons_learned"):
            self.knowledge_base["learning_insights"].extend(experience["lessons_learned"])
        
        self.save_knowledge_base(self.knowledge_base)
    
    def update_model_profiles(self, experience: Dict):
        """تحديث ملفات أداء النماذج"""
        for result in experience.get("results", {}).get("execution_log", []):
            model = result.get("model")
            if model in self.model_profiles:
                # تحديث الإحصائيات
                profile = self.model_profiles[model]
                
                # إضافة نقطة أداء جديدة
                performance_point = {
                    "timestamp": experience["timestamp"],
                    "success": result.get("success", False),
                    "execution_time": result.get("execution_time", 0),
                    "task_type": experience["task_data"].get("task_type", "unknown")
                }
                
                profile["performance_trends"].append(performance_point)
                
                # تحديث المتوسطات (مبسط)
                recent_trends = profile["performance_trends"][-10:]  # آخر 10 نقاط
                if recent_trends:
                    profile["average_response_time"] = sum(p.get("execution_time", 0) for p in recent_trends) / len(recent_trends)
                    profile["success_rate"] = sum(1 for p in recent_trends if p.get("success", False)) / len(recent_trends)
        
        self.save_model_profiles(self.model_profiles)
    
    def analyze_successful_patterns(self, experiences: List[Dict]) -> Dict:
        """تحليل الأنماط الناجحة"""
        if not experiences:
            return {}
        
        successful_experiences = [exp for exp in experiences if exp.get("success_score", 0) > 0.7]
        
        patterns = {
            "common_team_compositions": {},
            "optimal_sequences": {},
            "success_factors": []
        }
        
        # تحليل تشكيلات الفريق الناجحة
        for exp in successful_experiences:
            models_used = [r["model"] for r in exp.get("results", {}).get("execution_log", [])]
            team_key = ",".join(sorted(set(models_used)))
            patterns["common_team_compositions"][team_key] = patterns["common_team_compositions"].get(team_key, 0) + 1
        
        return patterns
    
    def recommend_team_composition(self, task_type: str, similar_experiences: List[Dict]) -> List[str]:
        """توصية تشكيل الفريق الأمثل"""
        
        # الحصول على التشكيل الافتراضي من أنماط التعاون
        default_team = self.collaboration_patterns.get("successful_combinations", {}).get(task_type, [])
        
        if not similar_experiences:
            return default_team
        
        # تحليل التجارب المشابهة الناجحة
        successful_teams = {}
        for exp in similar_experiences:
            if exp.get("success_score", 0) > 0.7:
                models = [r["model"] for r in exp.get("results", {}).get("execution_log", [])]
                team_key = tuple(sorted(set(models)))
                successful_teams[team_key] = successful_teams.get(team_key, 0) + 1
        
        # اختيار أكثر التشكيلات نجاحاً
        if successful_teams:
            best_team = max(successful_teams.items(), key=lambda x: x[1])
            return list(best_team[0])
        
        return default_team
    
    def suggest_workflow_optimizations(self, task_type: str, similar_experiences: List[Dict]) -> List[str]:
        """اقتراح تحسينات سير العمل"""
        optimizations = []
        
        if not similar_experiences:
            return ["استخدم التشكيل الافتراضي للفريق", "ابدأ بالتحليل السريع", "اطلب المراجعة النهائية"]
        
        # تحليل الأنماط الزمنية
        avg_times = {}
        for exp in similar_experiences:
            for result in exp.get("results", {}).get("execution_log", []):
                model = result.get("model")
                time = result.get("execution_time", 0)
                if model not in avg_times:
                    avg_times[model] = []
                avg_times[model].append(time)
        
        # اقتراح تحسينات بناءً على الأداء
        for model, times in avg_times.items():
            avg_time = sum(times) / len(times)
            if avg_time > 30:  # إذا كان النموذج بطيء
                optimizations.append(f"فكر في تقليل تعقيد المهام لـ {model}")
            elif avg_time < 5:  # إذا كان سريع جداً
                optimizations.append(f"يمكن إعطاء مهام أكثر تعقيداً لـ {model}")
        
        return optimizations
    
    def estimate_success_probability(self, task_type: str, team_composition: List[str]) -> float:
        """تقدير احتمالية النجاح"""
        # منطق مبسط لتقدير النجاح
        base_probability = 0.7  # احتمالية أساسية
        
        # تعديل بناءً على خبرة النماذج في نوع المهمة
        for model in team_composition:
            if model in self.model_profiles:
                model_success_rate = self.model_profiles[model].get("success_rate", 0.5)
                best_tasks = self.model_profiles[model].get("best_task_types", [])
                
                if task_type in best_tasks:
                    base_probability += 0.1  # زيادة للتخصص
                
                base_probability = (base_probability + model_success_rate) / 2
        
        return min(base_probability, 0.95)  # حد أقصى 95%
    
    def calculate_confidence_level(self, similar_experiences: List[Dict]) -> str:
        """حساب مستوى الثقة في التوصيات"""
        count = len(similar_experiences)
        
        if count >= 10:
            return "عالي"
        elif count >= 5:
            return "متوسط"
        elif count >= 2:
            return "منخفض"
        else:
            return "محدود جداً"
    
    def save_knowledge_base(self, knowledge_base: Dict):
        """حفظ قاعدة المعرفة"""
        kb_file = self.core_memory_path / "team_knowledge_base.json"
        with open(kb_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_base, f, ensure_ascii=False, indent=2)
    
    def save_model_profiles(self, profiles: Dict):
        """حفظ ملفات أداء النماذج"""
        profiles_file = self.core_memory_path / "model_performance_profiles.json"
        with open(profiles_file, 'w', encoding='utf-8') as f:
            json.dump(profiles, f, ensure_ascii=False, indent=2)
    
    def save_collaboration_patterns(self, patterns: Dict):
        """حفظ أنماط التعاون"""
        patterns_file = self.connections_path / "collaboration_patterns.json"
        with open(patterns_file, 'w', encoding='utf-8') as f:
            json.dump(patterns, f, ensure_ascii=False, indent=2)
    
    def get_memory_statistics(self) -> Dict:
        """الحصول على إحصائيات الذاكرة"""
        stats = {
            "total_experiences": self.knowledge_base.get("total_experiences", 0),
            "memory_size_mb": self.calculate_memory_size(),
            "model_profiles_count": len(self.model_profiles),
            "collaboration_patterns_count": len(self.collaboration_patterns.get("successful_combinations", {})),
            "last_updated": self.knowledge_base.get("last_updated", "غير محدد"),
            "memory_health": "جيد"  # يمكن تطوير منطق أكثر تعقيداً
        }
        
        return stats
    
    def calculate_memory_size(self) -> float:
        """حساب حجم الذاكرة بالميجابايت"""
        total_size = 0
        
        for root, dirs, files in os.walk(self.memory_root):
            for file in files:
                file_path = Path(root) / file
                if file_path.exists():
                    total_size += file_path.stat().st_size
        
        return round(total_size / (1024 * 1024), 2)  # تحويل إلى MB

def main():
    """دالة اختبار"""
    print("🧠 اختبار مدير ذاكرة فريق أنوبيس")
    print("=" * 50)
    
    memory_manager = AnubisTeamMemoryManager()
    
    # عرض إحصائيات الذاكرة
    stats = memory_manager.get_memory_statistics()
    print("📊 إحصائيات الذاكرة:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # اختبار توليد توصيات
    recommendations = memory_manager.generate_recommendations("development", "تحسين أداء قاعدة البيانات")
    print(f"\n🎯 توصيات للتطوير:")
    print(f"   الفريق الأمثل: {recommendations['optimal_team_composition']}")
    print(f"   احتمالية النجاح: {recommendations['estimated_success_probability']:.2%}")
    print(f"   مستوى الثقة: {recommendations['confidence_level']}")

if __name__ == "__main__":
    main()
