#!/usr/bin/env python3
"""
🔌 الفئة الأساسية للإضافات
Base Plugin Class for Universal AI Assistants
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional


class BasePlugin(ABC):
    """الفئة الأساسية لجميع الإضافات"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة الإضافة الأساسية

        Args:
            config: إعدادات الإضافة
        """
        self.config = config or {}
        self.plugin_name = self.__class__.__name__
        self.version = "1.0.0"
        self.is_enabled = True
        self.is_initialized = False
        self.last_run = None

        # تهيئة الإضافة
        self._initialize()

    def _initialize(self):
        """تهيئة الإضافة"""
        try:
            self.initialize_plugin()
            self.is_initialized = True
        except Exception as e:
            print(f"❌ خطأ في تهيئة الإضافة {self.plugin_name}: {e}")
            self.is_enabled = False

    @abstractmethod
    def get_plugin_info(self) -> Dict[str, Any]:
        """إرجاع معلومات الإضافة"""
        pass

    @abstractmethod
    def initialize_plugin(self):
        """تهيئة الإضافة المخصصة"""
        pass

    @abstractmethod
    def execute(self, *args, **kwargs) -> Dict[str, Any]:
        """تنفيذ الإضافة"""
        pass

    def enable(self):
        """تفعيل الإضافة"""
        self.is_enabled = True

    def disable(self):
        """إلغاء تفعيل الإضافة"""
        self.is_enabled = False

    def get_status(self) -> Dict[str, Any]:
        """إرجاع حالة الإضافة"""
        return {
            "plugin_name": self.plugin_name,
            "version": self.version,
            "is_enabled": self.is_enabled,
            "is_initialized": self.is_initialized,
            "last_run": self.last_run,
        }

    def run(self, *args, **kwargs) -> Dict[str, Any]:
        """تشغيل الإضافة"""
        if not self.is_enabled or not self.is_initialized:
            return {
                "success": False,
                "error": f"الإضافة {self.plugin_name} غير مفعلة أو غير مهيأة",
            }

        try:
            result = self.execute(*args, **kwargs)
            self.last_run = datetime.now().isoformat()

            if isinstance(result, dict):
                result["plugin_info"] = self.get_status()
                result["success"] = result.get("success", True)

            return result

        except Exception as e:
            return {"success": False, "error": str(e), "plugin_info": self.get_status()}
