#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إعدادات VS Code تلقائياً
"""

import os
import json
import shutil
from pathlib import Path

def apply_vscode_settings():
    """تطبيق إعدادات VS Code المحسنة تلقائياً"""
    
    print('🚀 تطبيق إعدادات VS Code المحسنة تلقائياً...')
    print('=' * 60)
    
    # مسارات VS Code
    vscode_settings_paths = [
        os.path.expanduser('~/AppData/Roaming/Code/User/settings.json'),
        os.path.expanduser('~/.vscode/settings.json'),
        os.path.expanduser('~/Library/Application Support/Code/User/settings.json')  # macOS
    ]
    
    # الإعدادات المحسنة
    optimized_settings = {
        "editor.semanticHighlighting.enabled": False,
        "editor.bracketPairColorization.enabled": False,
        "editor.renderWhitespace": "none",
        "editor.minimap.enabled": False,
        "editor.codeLens": False,
        "editor.lightbulb.enabled": False,
        "editor.hover.enabled": <PERSON>als<PERSON>,
        "editor.parameterHints.enabled": False,
        "editor.quickSuggestions": False,
        "editor.suggestOnTriggerCharacters": False,
        "editor.wordBasedSuggestions": False,
        "editor.acceptSuggestionOnEnter": "off",
        "editor.acceptSuggestionOnCommitCharacter": False,
        "editor.formatOnSave": False,
        "editor.formatOnType": False,
        "editor.formatOnPaste": False,
        "editor.autoIndent": "none",
        "editor.trimAutoWhitespace": False,
        "editor.maxTokenizationLineLength": 1000,
        "editor.largeFileOptimizations": True,
        
        "files.watcherExclude": {
            "**/node_modules/**": True,
            "**/.git/**": True,
            "**/dist/**": True,
            "**/build/**": True,
            "**/out/**": True,
            "**/.vscode/**": True,
            "**/coverage/**": True,
            "**/.nyc_output/**": True,
            "**/tmp/**": True,
            "**/temp/**": True
        },
        
        "search.exclude": {
            "**/node_modules": True,
            "**/bower_components": True,
            "**/.git": True,
            "**/dist": True,
            "**/build": True,
            "**/coverage": True,
            "**/.nyc_output": True
        },
        
        "files.exclude": {
            "**/.git": True,
            "**/.svn": True,
            "**/.hg": True,
            "**/CVS": True,
            "**/.DS_Store": True,
            "**/node_modules": True,
            "**/bower_components": True
        },
        
        "typescript.disableAutomaticTypeAcquisition": True,
        "typescript.suggest.autoImports": False,
        "typescript.updateImportsOnFileMove.enabled": "never",
        "javascript.suggest.autoImports": False,
        "javascript.updateImportsOnFileMove.enabled": "never",
        
        "extensions.autoUpdate": False,
        "extensions.autoCheckUpdates": False,
        "extensions.ignoreRecommendations": True,
        "update.mode": "none",
        
        "telemetry.telemetryLevel": "off",
        "workbench.enableExperiments": False,
        "workbench.settings.enableNaturalLanguageSearch": False,
        "workbench.startupEditor": "none",
        "workbench.tips.enabled": False,
        "workbench.welcome.enabled": False,
        
        "git.enabled": True,
        "git.autorefresh": False,
        "git.autofetch": False,
        "git.decorations.enabled": False,
        
        "breadcrumbs.enabled": False,
        "outline.showVariables": False,
        "outline.showFunctions": False,
        "problems.decorations.enabled": False,
        
        "http.proxyStrictSSL": False,
        "security.workspace.trust.enabled": False,
        
        "python.analysis.autoImportCompletions": False,
        "python.analysis.autoSearchPaths": False,
        "python.analysis.diagnosticMode": "openFilesOnly",
        "python.linting.enabled": False,
        
        "omnisharp.enableEditorConfigSupport": False,
        "omnisharp.enableImportCompletion": False,
        "omnisharp.enableRoslynAnalyzers": False,
        
        "window.titleBarStyle": "custom",
        "disable-hardware-acceleration": True
    }
    
    # البحث عن مسار VS Code الصحيح
    settings_path = None
    for path in vscode_settings_paths:
        if os.path.exists(os.path.dirname(path)):
            settings_path = path
            break
    
    if not settings_path:
        print('❌ لم يتم العثور على مجلد إعدادات VS Code')
        print('💡 يرجى تطبيق الإعدادات يدوياً من ملف vscode_optimized_settings.json')
        return False
    
    print(f'📁 مسار الإعدادات: {settings_path}')
    
    # قراءة الإعدادات الحالية
    current_settings = {}
    if os.path.exists(settings_path):
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                current_settings = json.load(f)
            print('✅ تم قراءة الإعدادات الحالية')
        except Exception as e:
            print(f'⚠️ خطأ في قراءة الإعدادات الحالية: {e}')
    
    # نسخ احتياطية
    if os.path.exists(settings_path):
        backup_path = settings_path + '.backup'
        shutil.copy2(settings_path, backup_path)
        print(f'💾 تم إنشاء نسخة احتياطية: {backup_path}')
    
    # دمج الإعدادات
    current_settings.update(optimized_settings)
    
    # كتابة الإعدادات الجديدة
    try:
        os.makedirs(os.path.dirname(settings_path), exist_ok=True)
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(current_settings, f, indent=2, ensure_ascii=False)
        
        print('✅ تم تطبيق الإعدادات المحسنة بنجاح!')
        print(f'📄 الملف: {settings_path}')
        
        return True
        
    except Exception as e:
        print(f'❌ خطأ في كتابة الإعدادات: {e}')
        return False

def disable_heavy_extensions():
    """تعطيل الإضافات الثقيلة"""
    
    print('\n🔌 تعطيل الإضافات الثقيلة...')
    print('-' * 40)
    
    heavy_extensions = [
        'SonarSource.sonarlint-vscode',
        'ms-vscode.vscode-speech',
        'ms-dotnettools.csharp',
        'ms-vscode.powershell',
        'intelliphp.intelliphp',
        'vadimcn.vscode-lldb',
        'Ionide.Ionide-fsharp'
    ]
    
    print('💡 لتعطيل الإضافات الثقيلة، شغل هذه الأوامر في Terminal:')
    print()
    
    for ext in heavy_extensions:
        print(f'code --disable-extension {ext}')
    
    print()
    print('أو عطلها يدوياً من Extensions (Ctrl+Shift+X)')

if __name__ == "__main__":
    try:
        success = apply_vscode_settings()
        
        if success:
            print('\n🎉 تم تطبيق جميع التحسينات!')
            print('\n📋 الخطوات التالية:')
            print('1. أعد تشغيل VS Code')
            print('2. عطل الإضافات الثقيلة')
            print('3. استمتع بالأداء المحسن!')
            
            disable_heavy_extensions()
        else:
            print('\n⚠️ فشل في تطبيق بعض الإعدادات')
            print('💡 يرجى تطبيق الإعدادات يدوياً')
            
    except Exception as e:
        print(f'❌ خطأ عام: {e}')
