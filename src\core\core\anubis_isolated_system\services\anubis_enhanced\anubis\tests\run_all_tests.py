#!/usr/bin/env python3
"""
🧪 تشغيل جميع الاختبارات
Run All Tests

ملف شامل لتشغيل جميع اختبارات نظام المساعدين الذكيين العالمي
"""

import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path

# إضافة مجلد المشروع الرئيسي إلى المسار
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تغيير المجلد الحالي إلى مجلد anubis
import os

os.chdir(project_root)


def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)


def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n📋 {title}")
    print("-" * 40)


def run_test_file(test_file):
    """تشغيل ملف اختبار واحد"""
    print(f"▶️ تشغيل {test_file}...")

    try:
        start_time = time.time()
        result = subprocess.run(
            [sys.executable, test_file],
            capture_output=True,
            text=True,
            timeout=300,  # 5 دقائق كحد أقصى
        )
        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            print(f"✅ {test_file} - نجح ({duration:.2f}s)")
            return True, duration, result.stdout
        else:
            print(f"❌ {test_file} - فشل ({duration:.2f}s)")
            print(f"خطأ: {result.stderr}")
            return False, duration, result.stderr

    except subprocess.TimeoutExpired:
        print(f"⏰ {test_file} - انتهت المهلة الزمنية")
        return False, 300, "Timeout"
    except Exception as e:
        print(f"💥 {test_file} - خطأ في التنفيذ: {e}")
        return False, 0, str(e)


def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print_section("فحص التبعيات")

    required_modules = ["pathlib", "datetime", "json", "unittest", "tempfile", "shutil"]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - مفقود")
            missing_modules.append(module)

    if missing_modules:
        print(f"\n⚠️ وحدات مفقودة: {', '.join(missing_modules)}")
        return False

    print("✅ جميع التبعيات متوفرة")
    return True


def check_project_structure():
    """فحص هيكل المشروع"""
    print_section("فحص هيكل المشروع")

    required_dirs = [
        "core",
        "agents",
        "plugins",
        "configs",
        "workspace",
        "templates",
        "docs",
    ]

    required_files = [
        "main.py",
        "README.md",
        "core/__init__.py",
        "core/assistant_system.py",
        "core/base_agent.py",
        "agents/__init__.py",
        "plugins/__init__.py",
        "plugins/plugin_manager.py",
        "plugins/base_plugin.py",
    ]

    missing_items = []

    # فحص المجلدات
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            print(f"❌ مجلد مفقود: {dir_name}")
            missing_items.append(dir_name)
        else:
            print(f"✅ {dir_name}/")

    # فحص الملفات
    for file_name in required_files:
        if not Path(file_name).exists():
            print(f"❌ ملف مفقود: {file_name}")
            missing_items.append(file_name)
        else:
            print(f"✅ {file_name}")

    if missing_items:
        print(f"\n⚠️ عناصر مفقودة: {len(missing_items)}")
        return False

    print("✅ هيكل المشروع مكتمل")
    return True


def run_system_tests():
    """تشغيل اختبارات النظام الأساسية"""
    print_section("اختبارات النظام الأساسية")

    test_files = ["test_system.py", "test_agents.py"]

    results = []
    for test_file in test_files:
        if Path(test_file).exists():
            success, duration, output = run_test_file(test_file)
            results.append((test_file, success, duration))
        else:
            print(f"⚠️ ملف الاختبار غير موجود: {test_file}")
            results.append((test_file, False, 0))

    return results


def run_agent_tests():
    """تشغيل اختبارات الوكلاء"""
    print_section("اختبارات الوكلاء الذكيين")

    test_files = ["test_error_detector.py", "test_project_analyzer.py"]

    results = []
    for test_file in test_files:
        if Path(test_file).exists():
            success, duration, output = run_test_file(test_file)
            results.append((test_file, success, duration))
        else:
            print(f"⚠️ ملف الاختبار غير موجود: {test_file}")
            results.append((test_file, False, 0))

    return results


def run_plugin_tests():
    """تشغيل اختبارات نظام الإضافات"""
    print_section("اختبارات نظام الإضافات")

    test_files = ["test_plugins.py"]

    results = []
    for test_file in test_files:
        if Path(test_file).exists():
            success, duration, output = run_test_file(test_file)
            results.append((test_file, success, duration))
        else:
            print(f"⚠️ ملف الاختبار غير موجود: {test_file}")
            results.append((test_file, False, 0))

    return results


def run_integration_tests():
    """تشغيل اختبارات التكامل"""
    print_section("اختبارات التكامل")

    print("▶️ اختبار تشغيل النظام الأساسي...")
    try:
        result = subprocess.run(
            [sys.executable, "../main.py", "--help"],
            capture_output=True,
            text=True,
            timeout=30,
        )

        if result.returncode == 0:
            print("✅ النظام الأساسي يعمل")
            return [("main.py --help", True, 1)]
        else:
            print(f"❌ النظام الأساسي لا يعمل: {result.stderr}")
            return [("main.py --help", False, 1)]

    except Exception as e:
        print(f"💥 خطأ في اختبار النظام الأساسي: {e}")
        return [("main.py --help", False, 0)]


def generate_test_report(all_results):
    """إنشاء تقرير الاختبارات"""
    print_section("تقرير الاختبارات")

    total_tests = len(all_results)
    passed_tests = sum(1 for _, success, _ in all_results if success)
    failed_tests = total_tests - passed_tests
    total_duration = sum(duration for _, _, duration in all_results)

    print(f"📊 إجمالي الاختبارات: {total_tests}")
    print(f"✅ نجح: {passed_tests}")
    print(f"❌ فشل: {failed_tests}")
    print(f"⏱️ الوقت الإجمالي: {total_duration:.2f} ثانية")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")

    # تفاصيل الاختبارات الفاشلة
    if failed_tests > 0:
        print("\n❌ الاختبارات الفاشلة:")
        for test_name, success, duration in all_results:
            if not success:
                print(f"   - {test_name}")

    # حفظ التقرير في ملف
    report_file = (
        Path("../workspace/reports") / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    )
    report_file.parent.mkdir(parents=True, exist_ok=True)

    with open(report_file, "w", encoding="utf-8") as f:
        f.write(f"تقرير اختبارات نظام المساعدين الذكيين العالمي\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"إجمالي الاختبارات: {total_tests}\n")
        f.write(f"نجح: {passed_tests}\n")
        f.write(f"فشل: {failed_tests}\n")
        f.write(f"الوقت الإجمالي: {total_duration:.2f} ثانية\n")
        f.write(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%\n\n")

        f.write("تفاصيل الاختبارات:\n")
        for test_name, success, duration in all_results:
            status = "✅ نجح" if success else "❌ فشل"
            f.write(f"{status} - {test_name} ({duration:.2f}s)\n")

    print(f"\n📄 تم حفظ التقرير: {report_file}")

    return passed_tests == total_tests


def main():
    """الدالة الرئيسية"""
    print_header("نظام المساعدين الذكيين العالمي - تشغيل جميع الاختبارات")
    print(f"🕐 بدء الاختبارات: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # فحص التبعيات
    if not check_dependencies():
        print("❌ فشل في فحص التبعيات")
        return False

    # فحص هيكل المشروع
    if not check_project_structure():
        print("❌ فشل في فحص هيكل المشروع")
        return False

    # تجميع جميع النتائج
    all_results = []

    # تشغيل اختبارات النظام
    system_results = run_system_tests()
    all_results.extend(system_results)

    # تشغيل اختبارات الوكلاء
    agent_results = run_agent_tests()
    all_results.extend(agent_results)

    # تشغيل اختبارات الإضافات
    plugin_results = run_plugin_tests()
    all_results.extend(plugin_results)

    # تشغيل اختبارات التكامل
    integration_results = run_integration_tests()
    all_results.extend(integration_results)

    # إنشاء التقرير
    success = generate_test_report(all_results)

    print_header("انتهاء الاختبارات")
    print(f"🕐 انتهاء الاختبارات: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    if success:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - راجع التقرير للتفاصيل")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
