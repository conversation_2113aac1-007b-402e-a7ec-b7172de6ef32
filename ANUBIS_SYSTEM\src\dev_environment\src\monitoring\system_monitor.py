#!/usr/bin/env python3
"""
📊 مراقب النظام الشامل - معزول ومحمي
Comprehensive System Monitor - Isolated and Protected
"""

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

class SystemMonitor:
    def __init__(self):
        self.metrics_file = Path("monitoring_metrics.json")
        self.alerts_file = Path("system_alerts.json")
        self.running = False
        
    def collect_metrics(self):
        """جمع مقاييس النظام"""
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent,
                "used": psutil.virtual_memory().used
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv,
                "packets_sent": psutil.net_io_counters().packets_sent,
                "packets_recv": psutil.net_io_counters().packets_recv
            },
            "processes": len(psutil.pids()),
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    
    def check_alerts(self, metrics):
        """فحص التنبيهات"""
        alerts = []
        
        if metrics["cpu"]["usage_percent"] > 80:
            alerts.append({
                "type": "high_cpu",
                "message": f"استخدام CPU عالي: {metrics['cpu']['usage_percent']:.1f}%",
                "severity": "warning",
                "timestamp": datetime.now().isoformat()
            })
        
        if metrics["memory"]["percent"] > 85:
            alerts.append({
                "type": "high_memory",
                "message": f"استخدام الذاكرة عالي: {metrics['memory']['percent']:.1f}%",
                "severity": "warning",
                "timestamp": datetime.now().isoformat()
            })
        
        if metrics["disk"]["percent"] > 90:
            alerts.append({
                "type": "high_disk",
                "message": f"مساحة القرص منخفضة: {metrics['disk']['percent']:.1f}%",
                "severity": "critical",
                "timestamp": datetime.now().isoformat()
            })
        
        return alerts
    
    def save_metrics(self, metrics):
        """حفظ المقاييس"""
        with open(self.metrics_file, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, ensure_ascii=False, indent=2)
    
    def save_alerts(self, alerts):
        """حفظ التنبيهات"""
        if alerts:
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts, f, ensure_ascii=False, indent=2)
    
    def run_monitoring(self, duration=300):
        """تشغيل المراقبة لفترة محددة"""
        print("🚀 بدء مراقبة النظام...")
        self.running = True
        
        start_time = time.time()
        while self.running and (time.time() - start_time) < duration:
            try:
                metrics = self.collect_metrics()
                alerts = self.check_alerts(metrics)
                
                self.save_metrics(metrics)
                self.save_alerts(alerts)
                
                print(f"📊 CPU: {metrics['cpu']['usage_percent']:.1f}% | "
                      f"RAM: {metrics['memory']['percent']:.1f}% | "
                      f"Disk: {metrics['disk']['percent']:.1f}%")
                
                if alerts:
                    for alert in alerts:
                        print(f"⚠️ {alert['message']}")
                
                time.sleep(10)  # مراقبة كل 10 ثوان
                
            except Exception as e:
                print(f"❌ خطأ في المراقبة: {e}")
                time.sleep(5)
        
        print("✅ انتهت جلسة المراقبة")

if __name__ == "__main__":
    monitor = SystemMonitor()
    monitor.run_monitoring()
