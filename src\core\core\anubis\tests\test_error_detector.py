#!/usr/bin/env python3
"""
🧪 اختبارات وكيل كشف الأخطاء
Error Detection Agent Tests

اختبارات شاملة لوكيل كشف الأخطاء الذكي
"""

import os
import shutil
import sys
import tempfile
import unittest
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "agents"))

try:
    from anubis.agents.error_detector_agent import ErrorDetectorAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد ErrorDetectorAgent: {e}")
    sys.exit(1)


class TestErrorDetectorAgent(unittest.TestCase):
    """اختبارات وكيل كشف الأخطاء"""

    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء مجلد مؤقت للاختبار
        self.test_dir = tempfile.mkdtemp()
        self.test_project = Path(self.test_dir)

        # إعدادات الوكيل
        self.config = {
            "check_syntax": True,
            "check_imports": True,
            "check_style": True,
            "auto_fix": False,
        }

        # إنشاء الوكيل
        self.agent = ErrorDetectorAgent(
            project_path=str(self.test_project), config=self.config, verbose=False
        )

    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        # حذف المجلد المؤقت
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_agent_initialization(self):
        """اختبار تهيئة الوكيل"""
        self.assertEqual(self.agent.get_agent_type(), "error_detector")
        self.assertTrue(self.agent.is_initialized)
        self.assertEqual(self.agent.status, "initialized")
        self.assertTrue(self.agent.check_syntax)
        self.assertTrue(self.agent.check_imports)
        self.assertTrue(self.agent.check_style)
        self.assertFalse(self.agent.auto_fix)

    def test_syntax_error_detection(self):
        """اختبار كشف الأخطاء النحوية"""
        # إنشاء ملف بخطأ نحوي
        test_file = self.test_project / "syntax_error.py"
        test_file.write_text(
            """
def broken_function(
    print("Missing closing parenthesis")
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        self.assertIn("error_analysis", result)

        error_analysis = result["error_analysis"]
        self.assertGreater(len(error_analysis["syntax_errors"]), 0)

        # التحقق من تفاصيل الخطأ
        syntax_error = error_analysis["syntax_errors"][0]
        self.assertIn("syntax_error.py", syntax_error["file"])
        self.assertEqual(syntax_error["severity"], "high")

    def test_style_issues_detection(self):
        """اختبار كشف مشاكل الأسلوب"""
        # إنشاء ملف بمشاكل أسلوب
        test_file = self.test_project / "style_issues.py"
        long_line = "x = " + "1 + " * 50 + "1"  # سطر طويل جداً
        test_file.write_text(
            f"""
{long_line}
def function_with_trailing_spaces():
    pass
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]
        self.assertGreater(len(error_analysis["style_issues"]), 0)

        # التحقق من أنواع مشاكل الأسلوب
        style_issues = error_analysis["style_issues"]
        issue_types = [issue["type"] for issue in style_issues]
        self.assertIn("line_too_long", issue_types)

    def test_security_issues_detection(self):
        """اختبار كشف المشاكل الأمنية"""
        # إنشاء ملف بمشاكل أمنية
        test_file = self.test_project / "security_issues.py"
        test_file.write_text(
            """
password = "secret123"
api_key = "sk-1234567890abcdef"
secret = "my_secret_key"
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]
        self.assertGreater(len(error_analysis["security_issues"]), 0)

        # التحقق من نوع المشكلة الأمنية
        security_issue = error_analysis["security_issues"][0]
        self.assertEqual(security_issue["type"], "exposed_credentials")
        self.assertEqual(security_issue["severity"], "high")

    def test_import_errors_detection(self):
        """اختبار كشف أخطاء الاستيراد"""
        # إنشاء ملف باستيرادات مشكوك فيها
        test_file = self.test_project / "import_issues.py"
        test_file.write_text(
            """
import numpy
import pandas
import matplotlib
import requests
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]

        # قد تكون هناك تحذيرات حول المكتبات المفقودة
        if error_analysis["import_errors"]:
            import_error = error_analysis["import_errors"][0]
            self.assertEqual(import_error["type"], "missing_dependency")
            self.assertIn("pip install", import_error["suggestion"])

    def test_performance_issues_detection(self):
        """اختبار كشف مشاكل الأداء"""
        # إنشاء ملف بمشاكل أداء محتملة
        test_file = self.test_project / "performance_issues.py"
        test_file.write_text(
            """
for i in range(100):
    for j in range(100):
        for k in range(100):
            for l in range(100):
                print(i, j, k, l)
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]

        if error_analysis["performance_issues"]:
            performance_issue = error_analysis["performance_issues"][0]
            self.assertEqual(performance_issue["type"], "multiple_loops")
            self.assertEqual(performance_issue["severity"], "medium")

    def test_code_quality_analysis(self):
        """اختبار تحليل جودة الكود"""
        # إنشاء ملف بتوثيق جيد
        test_file = self.test_project / "quality_code.py"
        test_file.write_text(
            """
def well_documented_function():
    \"\"\"
    دالة موثقة جيداً
    \"\"\"
    return "Hello World"

class WellDocumentedClass:
    \"\"\"
    فئة موثقة جيداً
    \"\"\"

    def method(self):
        \"\"\"
        طريقة موثقة
        \"\"\"
        pass
"""
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        self.assertIn("quality_analysis", result)

        quality_analysis = result["quality_analysis"]
        self.assertGreater(quality_analysis["documentation_score"], 0)
        self.assertGreater(quality_analysis["complexity_score"], 0)

    def test_empty_project(self):
        """اختبار مشروع فارغ"""
        # تشغيل التحليل على مشروع فارغ
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]
        self.assertEqual(error_analysis["total_files_analyzed"], 0)
        self.assertEqual(error_analysis["files_with_errors"], 0)

    def test_multiple_files_analysis(self):
        """اختبار تحليل ملفات متعددة"""
        # إنشاء عدة ملفات
        files_data = {
            "good_file.py": "print('Hello World')",
            "bad_file.py": "def broken_function(\n    print('Missing parenthesis')",
            "style_file.py": "x = " + "1 + " * 50 + "1",  # سطر طويل
        }

        for filename, content in files_data.items():
            test_file = self.test_project / filename
            test_file.write_text(content)

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من النتائج
        self.assertTrue(result["success"])
        error_analysis = result["error_analysis"]
        self.assertEqual(error_analysis["total_files_analyzed"], 3)
        self.assertGreater(error_analysis["files_with_errors"], 0)

    def test_recommendations_generation(self):
        """اختبار إنشاء التوصيات"""
        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من وجود التوصيات
        self.assertTrue(result["success"])
        self.assertIn("recommendations", result)

        recommendations = result["recommendations"]
        self.assertGreater(len(recommendations), 0)

        # التحقق من بنية التوصية
        recommendation = recommendations[0]
        self.assertIn("category", recommendation)
        self.assertIn("priority", recommendation)
        self.assertIn("title", recommendation)
        self.assertIn("description", recommendation)
        self.assertIn("action", recommendation)

    def test_summary_creation(self):
        """اختبار إنشاء الملخص"""
        # إنشاء ملف بأخطاء متنوعة
        test_file = self.test_project / "mixed_issues.py"
        test_file.write_text(
            """
password = "secret"  # مشكلة أمنية
def broken_function(  # خطأ نحوي
    print("Missing parenthesis")

x = """
            + "1 + " * 50
            + "1  # سطر طويل"
        )

        # تشغيل التحليل
        result = self.agent.run()

        # التحقق من الملخص
        self.assertTrue(result["success"])
        self.assertIn("summary", result)

        summary = result["summary"]
        self.assertIn("total_files_analyzed", summary)
        self.assertIn("total_errors_found", summary)
        self.assertIn("quality_score", summary)
        self.assertIn("severity_breakdown", summary)
        self.assertIn("status", summary)
        self.assertEqual(summary["status"], "completed")


def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات وكيل كشف الأخطاء...")

    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestErrorDetectorAgent)

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # عرض النتائج
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        return True
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
