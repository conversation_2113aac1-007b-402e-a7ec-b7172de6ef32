{"timestamp": "2025-07-19T08:24:20.022707", "version": "5.0", "type": "comprehensive_organization", "actions_performed": [], "files_moved": [{"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_advanced_isolation", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_advanced_isolation", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_ADVANCED_ISOLATION_FINAL_REPORT.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\docs\\ANUBIS_ADVANCED_ISOLATION_FINAL_REPORT.md", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_advanced_isolation_system.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_advanced_isolation_system.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_agents_comprehensive_scanner.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_agents_comprehensive_scanner.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_agents_scan_report_20250719_080330.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_agents_scan_report_20250719_080330.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_agents_scan_report_20250719_080459.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_agents_scan_report_20250719_080459.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_comprehensive_organizer.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_comprehensive_organizer.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_comprehensive_project_scanner.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_comprehensive_project_scanner.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_comprehensive_scanner.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_comprehensive_scanner.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_comprehensive_scan_20250719_080710.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_comprehensive_scan_20250719_080710.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_containerized_system_test.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_containerized_system_test.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_containerized_test_report_20250719_072904.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_containerized_test_report_20250719_072904.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_core_configs_detailed_report_20250719_081704.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_core_configs_detailed_report_20250719_081704.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_core_configs_detailed_scanner.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_core_configs_detailed_scanner.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_FINAL_ACHIEVEMENT_REPORT.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\docs\\ANUBIS_FINAL_ACHIEVEMENT_REPORT.md", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_integration_improvements_report.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\docs\\anubis_integration_improvements_report.md", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_internal_system_test.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_internal_system_test.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_internal_test_report_20250719_072240.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_internal_test_report_20250719_072240.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_internal_test_report_20250719_072523.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_internal_test_report_20250719_072523.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_isolated_system", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_ISOLATION_FINAL_REPORT.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\docs\\ANUBIS_ISOLATION_FINAL_REPORT.md", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_isolation_quick_start.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_isolation_quick_start.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_isolation_system.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_isolation_system.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_isolation_test_report_20250719_074528.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_isolation_test_report_20250719_074528.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_quick_client.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_quick_client.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_quick_start.sh", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\anubis_quick_start.sh", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_scan_report_20250719_070938.json", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\reports\\anubis_scan_report_20250719_070938.json", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\archive", "classification": "archive_and_backups"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\backup", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\backup", "classification": "archive_and_backups"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\comprehensive_project_scanner.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\comprehensive_project_scanner.py", "classification": "reports_and_analysis"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\n8n", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\n8n_1", "classification": "workflows_and_automation"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\organize_anubis_system.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\organize_anubis_system.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\prompt.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\docs\\prompt.md", "classification": "documentation"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\pylint_report.txt", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\pylint_report.txt", "classification": "reports_and_analysis"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\README.md", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\docs\\README.md", "classification": "documentation"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\reports", "classification": "reports_and_analysis"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\temp", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\temp", "classification": "archive_and_backups"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\test_anubis_isolation_system.py", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\test_anubis_isolation_system.py", "classification": "anubis_main_system"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\src\\tools", "classification": "tools_and_utilities"}, {"from": "C:\\Users\\<USER>\\Universal-AI-Assistants\\Universal-AI-Assistants", "to": "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\Universal-AI-Assistants", "classification": "universal_ai_system"}], "files_deleted": ["C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\universal_ai\\universal\\logs\\vscode_process_monitor.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\database_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\enhanced_error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\enhanced_file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\enhanced_memory_agent_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\enhanced_project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\fixed_ai_test_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\memory_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150546_20250712_150546.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150556_20250712_150556.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150648_20250712_150648.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150742_20250712_150742.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150802_20250712_150802.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_150850_20250712_150850.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_151004_20250712_151004.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_151046_20250712_151046.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_151156_20250712_151156.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_152600_20250712_152600.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_153601_20250712_153601.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250712_154035_20250712_154035.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250714_123542_20250714_123542.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250714_130746_20250714_130746.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250714_130858_20250714_130858.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\session_20250714_131151_20250714_131151.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\simple_test_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\smart_code_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\testlogger.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\workspace\\logs\\universalassistants.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\database_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\memory_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\session_20250714_124906_20250714_124906.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\session_20250714_124919_20250714_124919.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\testlogger.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\tests\\workspace\\logs\\universalassistants.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\database_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\enhanced_error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\enhanced_file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\enhanced_memory_agent_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\enhanced_project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\fixed_ai_test_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\memory_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150546_20250712_150546.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150556_20250712_150556.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150648_20250712_150648.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150742_20250712_150742.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150802_20250712_150802.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_150850_20250712_150850.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_151004_20250712_151004.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_151046_20250712_151046.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_151156_20250712_151156.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_152600_20250712_152600.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_153601_20250712_153601.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250712_154035_20250712_154035.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250714_123542_20250714_123542.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250714_130746_20250714_130746.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250714_130858_20250714_130858.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\session_20250714_131151_20250714_131151.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\simple_test_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\smart_code_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\testlogger.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\workspace\\logs\\universalassistants.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\database_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\error_detector_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\file_organizer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\memory_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\project_analyzer_agent.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\session_20250714_124906_20250714_124906.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\session_20250714_124919_20250714_124919.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\testlogger.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\tests\\workspace\\logs\\universalassistants.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\agents\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\core\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\database\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\anubis\\database\\core\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\agents\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\core\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\database\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core\\anubis\\database\\core\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\Universal-AI-Assistants\\logs\\vscode_process_monitor.log", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\src\\tools\\vscode-optimizer\\agents\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\src\\tools\\vscode-optimizer\\VSCode-Control-Center\\agents\\__pycache__", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\src\\tools\\vscode-optimizer\\VS-Code-Performance-Optimizer\\agents\\__pycache__"], "directories_created": ["C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\core", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\agents", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\api", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\configs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\tests", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\src", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\configs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\tests", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\vscode_tools", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\optimizers", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\scripts", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\n8n", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\scripts", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\configs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems\\basic_isolation", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems\\advanced_isolation", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems\\configs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems\\docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\old_versions", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\backups", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\deprecated", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\temp_files", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\scan_reports", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\test_reports", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\analysis_data", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\logs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\user_guides", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\technical_docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\api_docs", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\tutorials"], "readme_files_created": ["C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis_main_system\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\universal_ai_system\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\tools_and_utilities\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\workflows_and_automation\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\isolation_systems\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\archive_and_backups\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\reports_and_analysis\\README.md", "C:\\Users\\<USER>\\Universal-AI-Assistants\\documentation\\README.md"], "current_structure": [{"name": "anubis", "type": "directory", "size": "36.1 MB", "classification": "anubis_main_system"}, {"name": "anubis_advanced_isolation", "type": "directory", "size": "20.0 KB", "classification": "anubis_main_system"}, {"name": "ANUBIS_ADVANCED_ISOLATION_FINAL_REPORT.md", "type": "file", "size": "12.5 KB", "classification": "anubis_main_system"}, {"name": "anubis_advanced_isolation_system.py", "type": "file", "size": "29.1 KB", "classification": "anubis_main_system"}, {"name": "anubis_agents_comprehensive_scanner.py", "type": "file", "size": "19.1 KB", "classification": "anubis_main_system"}, {"name": "anubis_agents_scan_report_20250719_080330.json", "type": "file", "size": "833 B", "classification": "anubis_main_system"}, {"name": "anubis_agents_scan_report_20250719_080459.json", "type": "file", "size": "1.4 KB", "classification": "anubis_main_system"}, {"name": "anubis_comprehensive_organizer.py", "type": "file", "size": "23.5 KB", "classification": "anubis_main_system"}, {"name": "anubis_comprehensive_project_scanner.py", "type": "file", "size": "27.6 KB", "classification": "anubis_main_system"}, {"name": "anubis_comprehensive_scanner.py", "type": "file", "size": "18.6 KB", "classification": "anubis_main_system"}, {"name": "anubis_comprehensive_scan_20250719_080710.json", "type": "file", "size": "19.0 KB", "classification": "anubis_main_system"}, {"name": "anubis_containerized_system_test.py", "type": "file", "size": "16.9 KB", "classification": "anubis_main_system"}, {"name": "anubis_containerized_test_report_20250719_072904.json", "type": "file", "size": "942 B", "classification": "anubis_main_system"}, {"name": "anubis_core_configs_detailed_report_20250719_081704.json", "type": "file", "size": "52.1 KB", "classification": "anubis_main_system"}, {"name": "anubis_core_configs_detailed_scanner.py", "type": "file", "size": "19.9 KB", "classification": "anubis_main_system"}, {"name": "ANUBIS_FINAL_ACHIEVEMENT_REPORT.md", "type": "file", "size": "8.0 KB", "classification": "anubis_main_system"}, {"name": "anubis_integration_improvements_report.md", "type": "file", "size": "6.8 KB", "classification": "anubis_main_system"}, {"name": "anubis_internal_system_test.py", "type": "file", "size": "16.3 KB", "classification": "anubis_main_system"}, {"name": "anubis_internal_test_report_20250719_072240.json", "type": "file", "size": "34.2 KB", "classification": "anubis_main_system"}, {"name": "anubis_internal_test_report_20250719_072523.json", "type": "file", "size": "62.7 KB", "classification": "anubis_main_system"}, {"name": "anubis_isolated_system", "type": "directory", "size": "36.2 MB", "classification": "anubis_main_system"}, {"name": "ANUBIS_ISOLATION_FINAL_REPORT.md", "type": "file", "size": "10.4 KB", "classification": "anubis_main_system"}, {"name": "anubis_isolation_quick_start.py", "type": "file", "size": "14.6 KB", "classification": "anubis_main_system"}, {"name": "anubis_isolation_system.py", "type": "file", "size": "23.1 KB", "classification": "anubis_main_system"}, {"name": "anubis_isolation_test_report_20250719_074528.json", "type": "file", "size": "1.4 KB", "classification": "anubis_main_system"}, {"name": "anubis_quick_client.py", "type": "file", "size": "4.9 KB", "classification": "anubis_main_system"}, {"name": "anubis_quick_start.sh", "type": "file", "size": "5.6 KB", "classification": "anubis_main_system"}, {"name": "anubis_scan_report_20250719_070938.json", "type": "file", "size": "3.2 KB", "classification": "anubis_main_system"}, {"name": "archive", "type": "directory", "size": "319.7 KB", "classification": "archive_and_backups"}, {"name": "augment-cht", "type": "directory", "size": "338.6 KB", "classification": "unclassified"}, {"name": "backup", "type": "directory", "size": "106.8 KB", "classification": "archive_and_backups"}, {"name": "comprehensive_project_scanner.py", "type": "file", "size": "14.7 KB", "classification": "reports_and_analysis"}, {"name": "configs", "type": "directory", "size": "4.5 KB", "classification": "unclassified"}, {"name": "docker-compose.quick.yml", "type": "file", "size": "1.3 KB", "classification": "unclassified"}, {"name": "n8n", "type": "directory", "size": "28.0 KB", "classification": "workflows_and_automation"}, {"name": "organize_anubis_system.py", "type": "file", "size": "19.8 KB", "classification": "anubis_main_system"}, {"name": "prompt.md", "type": "file", "size": "320 B", "classification": "documentation"}, {"name": "pylint_report.txt", "type": "file", "size": "277.5 KB", "classification": "reports_and_analysis"}, {"name": "README.md", "type": "file", "size": "1.8 KB", "classification": "documentation"}, {"name": "reports", "type": "directory", "size": "427 B", "classification": "reports_and_analysis"}, {"name": "setup_gemini.bat", "type": "file", "size": "425 B", "classification": "unclassified"}, {"name": "temp", "type": "directory", "size": "2.5 KB", "classification": "archive_and_backups"}, {"name": "test_anubis_isolation_system.py", "type": "file", "size": "20.1 KB", "classification": "anubis_main_system"}, {"name": "tools", "type": "directory", "size": "1.1 MB", "classification": "tools_and_utilities"}, {"name": "Universal-AI-Assistants", "type": "directory", "size": "86.6 KB", "classification": "universal_ai_system"}, {"name": "workspace", "type": "directory", "size": "1.6 MB", "classification": "unclassified"}, {"name": "__pycache__", "type": "directory", "size": "28.0 KB", "classification": "unclassified"}]}