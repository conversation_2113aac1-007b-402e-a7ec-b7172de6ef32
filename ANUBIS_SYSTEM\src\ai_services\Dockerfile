# حاوية نظام الذكاء الاصطناعي الشامل المعزولة
FROM python:3.11-slim

# إعداد متغيرات البيئة للذكاء الاصطناعي
ENV PYTHONUNBUFFERED=1
ENV AI_SYSTEM_MODE=isolated
ENV USER_ID=1002
ENV GROUP_ID=1002
ENV AI_MODELS_PATH=/app/models
ENV AI_CONFIGS_PATH=/app/ai_configs

# إنشاء مستخدم مخصص للذكاء الاصطناعي
RUN groupadd -g $GROUP_ID anubis_ai && \
    useradd -u $USER_ID -g $GROUP_ID -m -s /bin/bash anubis_ai

# تثبيت متطلبات الذكاء الاصطناعي
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    git \
    build-essential \
    && pip install --no-cache-dir \
    torch \
    transformers \
    langchain \
    openai \
    google-generativeai \
    anthropic \
    ollama \
    chromadb \
    faiss-cpu \
    sentence-transformers \
    tiktoken \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل للذكاء الاصطناعي
WORKDIR /app/universal_ai

# إنشاء هيكل المجلدات للذكاء الاصطناعي
RUN mkdir -p /app/universal_ai/models \
             /app/universal_ai/agents \
             /app/universal_ai/providers \
             /app/universal_ai/embeddings \
             /app/universal_ai/data \
             /app/universal_ai/configs \
             /app/universal_ai/logs \
             /app/universal_ai/cache \
             /app/universal_ai/temp \
    && chown -R anubis_ai:anubis_ai /app/universal_ai

# نسخ نظام الذكاء الاصطناعي
COPY --chown=anubis_ai:anubis_ai . .

# التبديل للمستخدم غير المميز
USER anubis_ai

# فحص صحة للذكاء الاصطناعي
HEALTHCHECK --interval=45s --timeout=30s --start-period=60s --retries=3 \
    CMD python -c "import torch, transformers; print('AI Systems OK')" || exit 1

# المنافذ المكشوفة للذكاء الاصطناعي
EXPOSE 8090 8091

# نقطة الدخول للذكاء الاصطناعي
ENTRYPOINT ["python", "-m", "universal_ai.main"]
