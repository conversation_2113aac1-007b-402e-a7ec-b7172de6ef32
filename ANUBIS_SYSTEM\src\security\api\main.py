#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 Anubis API Service - Isolated
خدمة API معزولة لنظام أنوبيس
"""

import os
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from datetime import datetime
import logging
import structlog

# إعداد السجلات
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="🏺 Anubis API Service",
    description="خدمة API معزولة لنظام أنوبيس",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# نماذج البيانات
class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: datetime
    version: str
    isolated: bool

class ServiceInfo(BaseModel):
    name: str
    version: str
    description: str
    endpoints: list

# متغيرات البيئة
SERVICE_NAME = os.getenv("ANUBIS_SERVICE", "api")
ISOLATED = os.getenv("ANUBIS_ISOLATED", "true").lower() == "true"
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8080"))

@app.on_event("startup")
async def startup_event():
    """أحداث بدء التشغيل"""
    logger.info("🏺 بدء تشغيل خدمة Anubis API المعزولة",
                service=SERVICE_NAME, isolated=ISOLATED)

@app.on_event("shutdown")
async def shutdown_event():
    """أحداث إيقاف التشغيل"""
    logger.info("🛑 إيقاف خدمة Anubis API المعزولة",
                service=SERVICE_NAME)

@app.get("/", response_model=dict)
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "🏺 Anubis API Service - Isolated",
        "status": "running",
        "service": SERVICE_NAME,
        "isolated": ISOLATED,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """فحص صحة الخدمة"""
    return HealthResponse(
        status="healthy",
        service=SERVICE_NAME,
        timestamp=datetime.now(),
        version="1.0.0",
        isolated=ISOLATED
    )

@app.get("/info", response_model=ServiceInfo)
async def service_info():
    """معلومات الخدمة"""
    return ServiceInfo(
        name="Anubis API Service",
        version="1.0.0",
        description="خدمة API معزولة لنظام أنوبيس",
        endpoints=[
            "/",
            "/health",
            "/info",
            "/status",
            "/metrics",
            "/docs",
            "/redoc"
        ]
    )

@app.get("/status")
async def service_status():
    """حالة الخدمة المفصلة"""
    import psutil
    import platform
    
    try:
        # معلومات النظام
        system_info = {
            "platform": platform.system(),
            "architecture": platform.architecture()[0],
            "hostname": platform.node(),
            "python_version": platform.python_version()
        }
        
        # معلومات الذاكرة
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used
        }
        
        # معلومات المعالج
        cpu_info = {
            "count": psutil.cpu_count(),
            "percent": psutil.cpu_percent(interval=1)
        }
        
        return {
            "service": SERVICE_NAME,
            "status": "running",
            "isolated": ISOLATED,
            "timestamp": datetime.now().isoformat(),
            "system": system_info,
            "memory": memory_info,
            "cpu": cpu_info,
            "uptime": "متاح"
        }
        
    except Exception as e:
        logger.error("خطأ في الحصول على حالة النظام", error=str(e))
        return {
            "service": SERVICE_NAME,
            "status": "running",
            "isolated": ISOLATED,
            "timestamp": datetime.now().isoformat(),
            "error": "لا يمكن الحصول على معلومات النظام"
        }

@app.get("/metrics")
async def metrics():
    """مقاييس الخدمة"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    
    try:
        metrics_data = generate_latest()
        return JSONResponse(
            content={"metrics": metrics_data.decode('utf-8')},
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.error("خطأ في إنشاء المقاييس", error=str(e))
        return {"error": "لا يمكن إنشاء المقاييس"}

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """معالج الأخطاء العام"""
    logger.error("خطأ غير متوقع", 
                 path=request.url.path,
                 method=request.method,
                 error=str(exc))
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "خطأ داخلي في الخدمة",
            "service": SERVICE_NAME,
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    # تشغيل الخدمة
    logger.info("🚀 تشغيل خدمة Anubis API المعزولة",
                host=API_HOST, port=API_PORT)
    
    uvicorn.run(
        app,
        host=API_HOST,
        port=API_PORT,
        log_level="info",
        access_log=True
    )
