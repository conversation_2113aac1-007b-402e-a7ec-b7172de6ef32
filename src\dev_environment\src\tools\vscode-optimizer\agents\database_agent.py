#!/usr/bin/env python3
"""
🗄️ وكيل قاعدة البيانات الذكي العالمي
Universal Intelligent Database Agent

تم تطويره من وكيل قاعدة البيانات في مشروع Crestal Diamond
وتعميمه ليعمل مع أي مشروع
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# إضافة مجلد core إلى المسار
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent

# محاولة استيراد مكتبات قواعد البيانات المختلفة
DATABASE_DRIVERS = {}

try:
    import mysql.connector
    DATABASE_DRIVERS['mysql'] = mysql.connector
except ImportError:
    pass

try:
    import psycopg2
    DATABASE_DRIVERS['postgresql'] = psycopg2
except ImportError:
    pass

# SQLite متوفر دائماً
DATABASE_DRIVERS['sqlite'] = sqlite3

class DatabaseAgent(BaseAgent):
    """وكيل ذكي مسؤول عن إدارة وصيانة قواعد البيانات"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "database"
    
    def initialize_agent(self):
        """تهيئة وكيل قاعدة البيانات"""
        # تحميل إعدادات قاعدة البيانات من ملف التكوين
        try:
            import json
            from pathlib import Path
            config_file = Path("configs/database_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    full_config = json.load(f)
                self.db_config = full_config.get('database', {}).get('mysql', {})
            else:
                self.db_config = self.config.get('database', {})
        except Exception:
            self.db_config = self.config.get('database', {})

        # إعدادات افتراضية
        self.db_type = 'mysql'  # استخدام MySQL كافتراضي
        self.db_name = self.db_config.get('database', 'anubis_system')
        self.host = self.db_config.get('host', 'localhost')
        self.user = self.db_config.get('user', 'root')
        self.password = self.db_config.get('password', '2452329511')
        self.port = self.db_config.get('port', 3306)
        
        # جداول النظام (سيتم اكتشافها تلقائياً)
        self.required_tables = self.db_config.get('required_tables', [])
        
        # التحقق من توفر مشغل قاعدة البيانات
        if self.db_type not in DATABASE_DRIVERS:
            raise RuntimeError(f"مشغل قاعدة البيانات {self.db_type} غير متوفر")
        
        self.log_action("تم تهيئة وكيل قاعدة البيانات", f"النوع: {self.db_type}")
    
    def _get_default_port(self) -> int:
        """الحصول على المنفذ الافتراضي حسب نوع قاعدة البيانات"""
        ports = {
            'mysql': 3306,
            'postgresql': 5432,
            'sqlite': 0  # لا يحتاج منفذ
        }
        return ports.get(self.db_type, 0)
    
    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل التحليل الأساسي لقاعدة البيانات"""
        analysis_result = {
            'database_type': self.db_type,
            'connection_test': False,
            'structure_analysis': None,
            'performance_metrics': None,
            'security_check': None,
            'recommendations': []
        }
        
        try:
            # اختبار الاتصال
            analysis_result['connection_test'] = self.test_database_connection()
            
            if analysis_result['connection_test']:
                # تحليل الهيكل
                analysis_result['structure_analysis'] = self.analyze_database_structure()
                
                # فحص الأداء
                analysis_result['performance_metrics'] = self.check_performance()
                
                # فحص الأمان
                analysis_result['security_check'] = self.check_security()
                
                # إنشاء التوصيات
                analysis_result['recommendations'] = self.generate_recommendations(analysis_result)
            
            else:
                analysis_result['recommendations'].append("إصلاح مشكلة الاتصال بقاعدة البيانات")
            
        except Exception as e:
            analysis_result['error'] = str(e)
            self.log_action("خطأ في تحليل قاعدة البيانات", str(e))
        
        return analysis_result
    
    def test_database_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        self.log_action("اختبار الاتصال بقاعدة البيانات")
        
        try:
            conn = self.get_connection()
            if conn:
                if self.db_type == 'sqlite':
                    # SQLite - اختبار بسيط
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                else:
                    # MySQL/PostgreSQL - اختبار الاتصال
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                
                conn.close()
                self.log_action("نجح الاتصال", "قاعدة البيانات متاحة")
                return True
            
        except Exception as e:
            self.log_action("فشل الاتصال", str(e))
            return False
        
        return False
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            if self.db_type == 'sqlite':
                # SQLite - ملف محلي
                db_path = self.project_path / f"{self.db_name}.db"
                return sqlite3.connect(str(db_path))
            
            elif self.db_type == 'mysql':
                # MySQL
                return DATABASE_DRIVERS['mysql'].connect(
                    host=self.host,
                    user=self.user,
                    password=self.password,
                    database=self.db_name,
                    port=self.port
                )
            
            elif self.db_type == 'postgresql':
                # PostgreSQL
                return DATABASE_DRIVERS['postgresql'].connect(
                    host=self.host,
                    user=self.user,
                    password=self.password,
                    database=self.db_name,
                    port=self.port
                )
            
        except Exception as e:
            self.log_action("خطأ في الاتصال", str(e))
            return None
    
    def analyze_database_structure(self) -> Dict[str, Any]:
        """تحليل هيكل قاعدة البيانات"""
        self.log_action("بدء تحليل هيكل قاعدة البيانات")
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'connection_status': 'connected',
            'tables': {},
            'missing_tables': [],
            'table_count': 0,
            'total_records': 0,
            'issues': []
        }
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            existing_tables = self.get_tables_list(cursor)
            analysis['table_count'] = len(existing_tables)
            
            # تحليل كل جدول
            for table in existing_tables:
                table_info = self.analyze_table(cursor, table)
                analysis['tables'][table] = table_info
                analysis['total_records'] += table_info['record_count']
            
            # فحص الجداول المطلوبة
            if self.required_tables:
                for table in self.required_tables:
                    if table not in existing_tables:
                        analysis['missing_tables'].append(table)
                        analysis['issues'].append(f"الجدول {table} مفقود")
            
            cursor.close()
            conn.close()
            
            self.log_action("تم تحليل قاعدة البيانات", 
                           f"الجداول: {analysis['table_count']}, السجلات: {analysis['total_records']}")
            
        except Exception as e:
            analysis['connection_status'] = 'error'
            analysis['issues'].append(f"خطأ في التحليل: {e}")
            self.log_action("خطأ في تحليل قاعدة البيانات", str(e))
        
        return analysis
    
    def get_tables_list(self, cursor) -> List[str]:
        """الحصول على قائمة الجداول"""
        if self.db_type == 'sqlite':
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            return [table[0] for table in cursor.fetchall()]
        
        elif self.db_type == 'mysql':
            cursor.execute("SHOW TABLES")
            return [table[0] for table in cursor.fetchall()]
        
        elif self.db_type == 'postgresql':
            cursor.execute("""
                SELECT tablename FROM pg_tables 
                WHERE schemaname = 'public'
            """)
            return [table[0] for table in cursor.fetchall()]
        
        return []
    
    def analyze_table(self, cursor, table_name: str) -> Dict[str, Any]:
        """تحليل جدول محدد"""
        table_info = {
            'name': table_name,
            'record_count': 0,
            'columns': [],
            'indexes': [],
            'size_mb': 0,
            'issues': []
        }
        
        try:
            # عدد السجلات
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            table_info['record_count'] = cursor.fetchone()[0]
            
            # معلومات الأعمدة
            table_info['columns'] = self.get_table_columns(cursor, table_name)
            
            # معلومات الفهارس (إذا كانت متوفرة)
            table_info['indexes'] = self.get_table_indexes(cursor, table_name)
            
            # فحص المشاكل
            if table_info['record_count'] == 0:
                table_info['issues'].append("الجدول فارغ")
            
            if len(table_info['indexes']) == 0 and self.db_type != 'sqlite':
                table_info['issues'].append("لا توجد فهارس")
            
        except Exception as e:
            table_info['issues'].append(f"خطأ في تحليل الجدول: {e}")
        
        return table_info
    
    def get_table_columns(self, cursor, table_name: str) -> List[Dict[str, Any]]:
        """الحصول على معلومات أعمدة الجدول"""
        columns = []
        
        try:
            if self.db_type == 'sqlite':
                cursor.execute(f"PRAGMA table_info({table_name})")
                for col in cursor.fetchall():
                    columns.append({
                        'name': col[1],
                        'type': col[2],
                        'not_null': bool(col[3]),
                        'default': col[4],
                        'primary_key': bool(col[5])
                    })
            
            elif self.db_type == 'mysql':
                cursor.execute(f"DESCRIBE {table_name}")
                for col in cursor.fetchall():
                    columns.append({
                        'name': col[0],
                        'type': col[1],
                        'null': col[2],
                        'key': col[3],
                        'default': col[4],
                        'extra': col[5]
                    })
            
            elif self.db_type == 'postgresql':
                cursor.execute(f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                """)
                for col in cursor.fetchall():
                    columns.append({
                        'name': col[0],
                        'type': col[1],
                        'nullable': col[2] == 'YES',
                        'default': col[3]
                    })
        
        except Exception as e:
            self.log_action("خطأ في الحصول على معلومات الأعمدة", str(e))
        
        return columns
    
    def get_table_indexes(self, cursor, table_name: str) -> List[Dict[str, Any]]:
        """الحصول على معلومات فهارس الجدول"""
        indexes = []
        
        try:
            if self.db_type == 'mysql':
                cursor.execute(f"SHOW INDEX FROM {table_name}")
                for idx in cursor.fetchall():
                    if idx[2] not in [i['name'] for i in indexes]:
                        indexes.append({
                            'name': idx[2],
                            'column': idx[4],
                            'unique': not bool(idx[1])
                        })
            
            # SQLite و PostgreSQL - تنفيذ مبسط
            
        except Exception:
            pass
        
        return indexes
    
    def check_performance(self) -> Dict[str, Any]:
        """فحص أداء قاعدة البيانات"""
        performance = {
            'query_time': 0,
            'connection_time': 0,
            'status': 'good'
        }
        
        try:
            start_time = datetime.now()
            conn = self.get_connection()
            connection_time = (datetime.now() - start_time).total_seconds()
            
            cursor = conn.cursor()
            
            # اختبار سرعة الاستعلام
            query_start = datetime.now()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            query_time = (datetime.now() - query_start).total_seconds()
            
            performance['connection_time'] = connection_time
            performance['query_time'] = query_time
            
            # تحديد الحالة
            if connection_time > 2 or query_time > 1:
                performance['status'] = 'slow'
            elif connection_time > 5 or query_time > 3:
                performance['status'] = 'critical'
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            performance['status'] = 'error'
            performance['error'] = str(e)
        
        return performance
    
    def check_security(self) -> Dict[str, Any]:
        """فحص أمان قاعدة البيانات"""
        security = {
            'password_protected': True,
            'remote_access': False,
            'encryption': False,
            'status': 'secure'
        }
        
        # فحص أساسي للأمان
        if self.db_type != 'sqlite' and self.password == '':
            security['password_protected'] = False
            security['status'] = 'vulnerable'
        
        if self.host != 'localhost' and self.host != '127.0.0.1':
            security['remote_access'] = True
        
        return security
    
    def generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """إنشاء التوصيات"""
        recommendations = []
        
        structure = analysis.get('structure_analysis', {})
        performance = analysis.get('performance_metrics', {})
        security = analysis.get('security_check', {})
        
        # توصيات الهيكل
        if structure:
            if len(structure.get('missing_tables', [])) > 0:
                recommendations.append(f"إنشاء {len(structure['missing_tables'])} جدول مفقود")
            
            if structure.get('total_records', 0) == 0:
                recommendations.append("إضافة بيانات تجريبية للاختبار")
        
        # توصيات الأداء
        if performance and performance.get('status') == 'slow':
            recommendations.append("تحسين أداء قاعدة البيانات")
        
        # توصيات الأمان
        if security and security.get('status') == 'vulnerable':
            recommendations.append("تحسين أمان قاعدة البيانات")
        
        return recommendations
