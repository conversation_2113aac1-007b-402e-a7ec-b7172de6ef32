#!/usr/bin/env python3
"""
VS Code Process Alerts and Continuous Monitoring
نظام التنبيهات والمراقبة المستمرة لعمليات VS Code

This script provides continuous monitoring with alerts for VS Code processes.
"""

import json
import os
import smtplib
import threading
import time
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from vscode_process_monitor import VSCodeProcessMonitor


class VSCodeProcessAlerts:
    def __init__(self, config_file="Universal-AI-Assistants/configs/alert_config.json"):
        self.monitor = VSCodeProcessMonitor()
        self.config_file = config_file
        self.config = self.load_config()
        self.alert_history = []
        self.is_monitoring = False

    def load_config(self) -> dict:
        """تحميل إعدادات التنبيهات"""
        default_config = {
            "thresholds": {
                "cpu_warning": 70,
                "cpu_critical": 90,
                "memory_warning": 70,
                "memory_critical": 90,
                "vscode_memory_warning": 20,
                "vscode_memory_critical": 40,
            },
            "monitoring": {
                "interval": 60,
                "alert_cooldown": 300,
                "max_alerts_per_hour": 10,
            },
            "notifications": {"console": True, "file": True, "email": False},
            "email": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "sender_email": "",
                "sender_password": "",
                "recipient_email": "",
            },
        }

        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    # دمج الإعدادات
                    default_config.update(loaded_config)
            else:
                # إنشاء ملف الإعدادات الافتراضي
                os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
                with open(self.config_file, "w", encoding="utf-8") as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"Error loading config: {e}")

        return default_config

    def check_thresholds(self, report: dict) -> list:
        """فحص العتبات وإنشاء التنبيهات"""
        alerts = []
        thresholds = self.config["thresholds"]

        # فحص استهلاك CPU النظام
        cpu_usage = report["system_stats"].get("cpu_usage", 0)
        if cpu_usage >= thresholds["cpu_critical"]:
            alerts.append(
                {
                    "level": "CRITICAL",
                    "type": "System CPU",
                    "message": f"Critical CPU usage: {cpu_usage:.1f}%",
                    "value": cpu_usage,
                    "timestamp": datetime.now().isoformat(),
                }
            )
        elif cpu_usage >= thresholds["cpu_warning"]:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "System CPU",
                    "message": f"High CPU usage: {cpu_usage:.1f}%",
                    "value": cpu_usage,
                    "timestamp": datetime.now().isoformat(),
                }
            )

        # فحص استهلاك الذاكرة النظام
        memory_usage = report["system_stats"].get("memory_percent", 0)
        if memory_usage >= thresholds["memory_critical"]:
            alerts.append(
                {
                    "level": "CRITICAL",
                    "type": "System Memory",
                    "message": f"Critical memory usage: {memory_usage:.1f}%",
                    "value": memory_usage,
                    "timestamp": datetime.now().isoformat(),
                }
            )
        elif memory_usage >= thresholds["memory_warning"]:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "System Memory",
                    "message": f"High memory usage: {memory_usage:.1f}%",
                    "value": memory_usage,
                    "timestamp": datetime.now().isoformat(),
                }
            )

        # فحص استهلاك VS Code للذاكرة
        vscode_memory = report["vscode_analysis"].get("total_memory_usage", 0)
        if vscode_memory >= thresholds["vscode_memory_critical"]:
            alerts.append(
                {
                    "level": "CRITICAL",
                    "type": "VS Code Memory",
                    "message": f"Critical VS Code memory usage: {vscode_memory:.1f}%",
                    "value": vscode_memory,
                    "timestamp": datetime.now().isoformat(),
                }
            )
        elif vscode_memory >= thresholds["vscode_memory_warning"]:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "VS Code Memory",
                    "message": f"High VS Code memory usage: {vscode_memory:.1f}%",
                    "value": vscode_memory,
                    "timestamp": datetime.now().isoformat(),
                }
            )

        # فحص مشاكل الأداء
        performance_issues = report["vscode_analysis"].get("performance_issues", [])
        if len(performance_issues) > 3:
            alerts.append(
                {
                    "level": "WARNING",
                    "type": "Performance Issues",
                    "message": f"Multiple performance issues detected: {len(performance_issues)} issues",
                    "value": len(performance_issues),
                    "timestamp": datetime.now().isoformat(),
                }
            )

        return alerts

    def should_send_alert(self, alert: dict) -> bool:
        """تحديد ما إذا كان يجب إرسال التنبيه"""
        now = datetime.now()
        cooldown = self.config["monitoring"]["alert_cooldown"]
        max_per_hour = self.config["monitoring"]["max_alerts_per_hour"]

        # فحص فترة التهدئة
        recent_alerts = [
            a
            for a in self.alert_history
            if (now - datetime.fromisoformat(a["timestamp"])).seconds < cooldown
            and a["type"] == alert["type"]
        ]

        if recent_alerts:
            return False

        # فحص الحد الأقصى للتنبيهات في الساعة
        hour_ago = now - timedelta(hours=1)
        alerts_last_hour = [
            a for a in self.alert_history if datetime.fromisoformat(a["timestamp"]) > hour_ago
        ]

        if len(alerts_last_hour) >= max_per_hour:
            return False

        return True

    def send_console_alert(self, alert: dict):
        """إرسال تنبيه إلى وحدة التحكم"""
        level_colors = {
            "WARNING": "\033[93m",  # أصفر
            "CRITICAL": "\033[91m",  # أحمر
            "INFO": "\033[92m",  # أخضر
        }

        color = level_colors.get(alert["level"], "\033[0m")
        reset_color = "\033[0m"

        print(f"{color}[{alert['level']}] {alert['timestamp']}: {alert['message']}{reset_color}")

    def send_file_alert(self, alert: dict):
        """حفظ التنبيه في ملف"""
        alert_file = "Universal-AI-Assistants/logs/vscode_alerts.log"
        os.makedirs(os.path.dirname(alert_file), exist_ok=True)

        with open(alert_file, "a", encoding="utf-8") as f:
            f.write(f"[{alert['level']}] {alert['timestamp']}: {alert['message']}\n")

    def send_email_alert(self, alert: dict):
        """إرسال تنبيه عبر البريد الإلكتروني"""
        if not self.config["email"]["sender_email"]:
            return

        try:
            msg = MIMEMultipart()
            msg["From"] = self.config["email"]["sender_email"]
            msg["To"] = self.config["email"]["recipient_email"]
            msg["Subject"] = f"VS Code Alert - {alert['level']}: {alert['type']}"

            body = f"""
            Alert Level: {alert['level']}
            Alert Type: {alert['type']}
            Message: {alert['message']}
            Value: {alert['value']}
            Timestamp: {alert['timestamp']}

            This is an automated alert from VS Code Process Monitor.
            """

            msg.attach(MIMEText(body, "plain"))

            server = smtplib.SMTP(
                self.config["email"]["smtp_server"], self.config["email"]["smtp_port"]
            )
            server.starttls()
            server.login(
                self.config["email"]["sender_email"],
                self.config["email"]["sender_password"],
            )

            text = msg.as_string()
            server.sendmail(
                self.config["email"]["sender_email"],
                self.config["email"]["recipient_email"],
                text,
            )
            server.quit()

        except Exception as e:
            print(f"Error sending email alert: {e}")

    def process_alerts(self, alerts: list):
        """معالجة وإرسال التنبيهات"""
        for alert in alerts:
            if self.should_send_alert(alert):
                # إضافة التنبيه إلى التاريخ
                self.alert_history.append(alert)

                # إرسال التنبيهات حسب الإعدادات
                if self.config["notifications"]["console"]:
                    self.send_console_alert(alert)

                if self.config["notifications"]["file"]:
                    self.send_file_alert(alert)

                if self.config["notifications"]["email"]:
                    self.send_email_alert(alert)

    def continuous_monitoring(self):
        """المراقبة المستمرة"""
        self.is_monitoring = True
        interval = self.config["monitoring"]["interval"]

        print(f"Starting continuous VS Code monitoring (interval: {interval}s)")
        print("Press Ctrl+C to stop monitoring")

        try:
            while self.is_monitoring:
                # تشغيل الفحص
                report, _ = self.monitor.run_full_check()

                # فحص العتبات
                alerts = self.check_thresholds(report)

                # معالجة التنبيهات
                if alerts:
                    self.process_alerts(alerts)

                # انتظار الفترة التالية
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
            self.is_monitoring = False
        except Exception as e:
            print(f"Error during continuous monitoring: {e}")
            self.is_monitoring = False

    def generate_alert_summary(self) -> dict:
        """إنشاء ملخص التنبيهات"""
        now = datetime.now()

        # تنبيهات آخر 24 ساعة
        day_ago = now - timedelta(days=1)
        recent_alerts = [
            a for a in self.alert_history if datetime.fromisoformat(a["timestamp"]) > day_ago
        ]

        # تجميع حسب النوع والمستوى
        summary = {
            "total_alerts": len(recent_alerts),
            "by_level": {},
            "by_type": {},
            "timeline": recent_alerts[-10:] if recent_alerts else [],  # آخر 10 تنبيهات
        }

        for alert in recent_alerts:
            level = alert["level"]
            alert_type = alert["type"]

            summary["by_level"][level] = summary["by_level"].get(level, 0) + 1
            summary["by_type"][alert_type] = summary["by_type"].get(alert_type, 0) + 1

        return summary

    def save_alert_summary(self):
        """حفظ ملخص التنبيهات"""
        summary = self.generate_alert_summary()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Universal-AI-Assistants/reports/alert_summary_{timestamp}.json"

        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"Alert summary saved to: {filename}")
        return filename


def main():
    """الدالة الرئيسية"""
    alerts_system = VSCodeProcessAlerts()

    import argparse

    parser = argparse.ArgumentParser(description="VS Code Process Alerts System")
    parser.add_argument("--monitor", "-m", action="store_true", help="Start continuous monitoring")
    parser.add_argument("--summary", "-s", action="store_true", help="Generate alert summary")
    parser.add_argument("--test", "-t", action="store_true", help="Test alert system")

    args = parser.parse_args()

    if args.monitor:
        alerts_system.continuous_monitoring()
    elif args.summary:
        alerts_system.save_alert_summary()
    elif args.test:
        # اختبار النظام
        report, _ = alerts_system.monitor.run_full_check()
        alerts = alerts_system.check_thresholds(report)
        print(f"Generated {len(alerts)} test alerts")
        alerts_system.process_alerts(alerts)
    else:
        print(
            "Use --monitor to start monitoring, --summary for alert summary, or --test to test alerts"
        )


if __name__ == "__main__":
    main()
