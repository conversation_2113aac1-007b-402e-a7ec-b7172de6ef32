#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌟 مدير البيئة التطويرية الشامل
Comprehensive Development Environment Manager

يدير جميع أدوات التطوير والمكتبات في النظام بطريقة شاملة ومنظمة
Manages all development tools and libraries in the system comprehensively
"""

import os
import sys
import json
import subprocess
import glob
import winreg
from pathlib import Path
from datetime import datetime
import shutil

class ComprehensiveDevelopmentEnvironmentManager:
    def __init__(self):
        self.scan_results = {
            'scan_date': datetime.now().isoformat(),
            'system_info': {},
            'discovered_tools': {},
            'package_managers': {},
            'development_environments': {},
            'cloud_tools': {},
            'package_managers_data': {},
            'environment_variables': {},
            'chocolatey_packages': {},
            'scoop_apps': {},
            'statistics': {}
        }
        
        # قائمة شاملة بأدوات التطوير
        self.comprehensive_tools = {
            'programming_languages': {
                'python': ['python.exe', 'python3.exe'],
                'node': ['node.exe'],
                'java': ['java.exe', 'javac.exe'],
                'dotnet': ['dotnet.exe'],
                'go': ['go.exe'],
                'rust': ['rustc.exe', 'cargo.exe'],
                'php': ['php.exe'],
                'ruby': ['ruby.exe'],
                'perl': ['perl.exe'],
                'lua': ['lua.exe'],
                'kotlin': ['kotlin.exe'],
                'scala': ['scala.exe']
            },
            'package_managers': {
                'npm': ['npm.exe', 'npm.cmd'],
                'yarn': ['yarn.exe', 'yarn.cmd'],
                'pnpm': ['pnpm.exe', 'pnpm.cmd'],
                'pip': ['pip.exe', 'pip3.exe'],
                'conda': ['conda.exe'],
                'mamba': ['mamba.exe'],
                'uv': ['uv.exe'],
                'composer': ['composer.exe', 'composer.phar'],
                'gem': ['gem.exe'],
                'bundle': ['bundle.exe'],
                'cargo': ['cargo.exe'],
                'nuget': ['nuget.exe']
            },
            'version_control': {
                'git': ['git.exe'],
                'svn': ['svn.exe'],
                'hg': ['hg.exe'],
                'bzr': ['bzr.exe']
            },
            'containerization': {
                'docker': ['docker.exe'],
                'podman': ['podman.exe'],
                'kubectl': ['kubectl.exe'],
                'helm': ['helm.exe'],
                'minikube': ['minikube.exe']
            },
            'cloud_tools': {
                'aws': ['aws.exe'],
                'azure': ['az.exe'],
                'gcloud': ['gcloud.exe'],
                'terraform': ['terraform.exe'],
                'ansible': ['ansible.exe']
            },
            'databases': {
                'mysql': ['mysql.exe', 'mysqld.exe'],
                'postgresql': ['psql.exe', 'postgres.exe'],
                'sqlite': ['sqlite3.exe'],
                'mongodb': ['mongo.exe', 'mongod.exe'],
                'redis': ['redis-server.exe', 'redis-cli.exe']
            },
            'build_tools': {
                'cmake': ['cmake.exe'],
                'make': ['make.exe', 'mingw32-make.exe'],
                'gradle': ['gradle.exe', 'gradle.bat'],
                'maven': ['mvn.exe', 'mvn.cmd'],
                'ant': ['ant.exe', 'ant.bat'],
                'ninja': ['ninja.exe'],
                'msbuild': ['msbuild.exe']
            },
            'editors_ides': {
                'vscode': ['code.exe'],
                'vim': ['vim.exe'],
                'emacs': ['emacs.exe'],
                'notepadpp': ['notepad++.exe'],
                'sublime': ['sublime_text.exe']
            }
        }

    def scan_environment_variables(self):
        """فحص متغيرات البيئة"""
        print("🔍 فحص متغيرات البيئة...")
        
        env_vars = {}
        development_paths = []
        
        # فحص PATH
        path_env = os.environ.get('PATH', '')
        path_dirs = path_env.split(';')
        
        for path_dir in path_dirs:
            if path_dir and any(keyword in path_dir.lower() for keyword in [
                'node', 'npm', 'python', 'java', 'git', 'docker', 'go', 
                'rust', 'php', 'ruby', 'mysql', 'postgres', 'mongo'
            ]):
                development_paths.append(path_dir.strip())
        
        env_vars['development_paths'] = development_paths
        
        # متغيرات بيئة مهمة أخرى
        important_vars = [
            'JAVA_HOME', 'PYTHON_HOME', 'NODE_PATH', 'GOPATH', 'GOROOT',
            'CARGO_HOME', 'RUSTUP_HOME', 'COMPOSER_HOME', 'GEM_HOME'
        ]
        
        for var in important_vars:
            value = os.environ.get(var)
            if value:
                env_vars[var] = value
        
        self.scan_results['environment_variables'] = env_vars
        print(f"✅ وجد {len(development_paths)} مسار تطوير في PATH")

    def scan_chocolatey_packages(self):
        """فحص حزم Chocolatey"""
        print("🍫 فحص حزم Chocolatey...")
        
        choco_path = Path('C:/ProgramData/chocolatey')
        packages = {}
        
        if choco_path.exists():
            lib_path = choco_path / 'lib'
            if lib_path.exists():
                try:
                    package_dirs = [d for d in lib_path.iterdir() if d.is_dir()]
                    for package_dir in package_dirs:
                        package_name = package_dir.name
                        
                        # البحث عن معلومات الحزمة
                        nuspec_files = list(package_dir.glob('*.nuspec'))
                        if nuspec_files:
                            packages[package_name] = {
                                'path': str(package_dir),
                                'nuspec': str(nuspec_files[0])
                            }
                        else:
                            packages[package_name] = {
                                'path': str(package_dir)
                            }
                    
                    print(f"✅ وجد {len(packages)} حزمة Chocolatey")
                except Exception as e:
                    print(f"⚠️ خطأ في فحص Chocolatey: {e}")
        else:
            print("❌ Chocolatey غير مثبت")
        
        self.scan_results['chocolatey_packages'] = packages

    def scan_scoop_apps(self):
        """فحص تطبيقات Scoop"""
        print("🥄 فحص تطبيقات Scoop...")
        
        scoop_path = Path.home() / 'scoop'
        apps = {}
        
        if scoop_path.exists():
            apps_path = scoop_path / 'apps'
            if apps_path.exists():
                try:
                    app_dirs = [d for d in apps_path.iterdir() if d.is_dir()]
                    for app_dir in app_dirs:
                        app_name = app_dir.name
                        
                        # البحث عن إصدارات التطبيق
                        version_dirs = [d for d in app_dir.iterdir() if d.is_dir() and d.name != 'current']
                        current_link = app_dir / 'current'
                        
                        apps[app_name] = {
                            'path': str(app_dir),
                            'versions': [d.name for d in version_dirs],
                            'current': str(current_link.resolve()) if current_link.exists() else None
                        }
                    
                    print(f"✅ وجد {len(apps)} تطبيق Scoop")
                except Exception as e:
                    print(f"⚠️ خطأ في فحص Scoop: {e}")
        else:
            print("❌ Scoop غير مثبت")
        
        self.scan_results['scoop_apps'] = apps

    def comprehensive_tool_scan(self):
        """فحص شامل لجميع الأدوات"""
        print("🔍 بدء الفحص الشامل لجميع أدوات التطوير...")
        
        all_discovered_tools = {}
        
        # مسارات البحث الشاملة
        search_patterns = [
            'C:/Program Files/*',
            'C:/Program Files (x86)/*',
            'C:/ProgramData/*',
            'C:/Users/<USER>/AppData/Local/Programs/*',
            'C:/Users/<USER>/AppData/Roaming/*',
            'C:/Users/<USER>/.cargo/bin',
            'C:/Users/<USER>/go/bin',
            'C:/Users/<USER>/.local/bin',
            'C:/tools/*',
            'C:/dev/*'
        ]
        
        for category, tools in self.comprehensive_tools.items():
            print(f"\n📂 فحص فئة: {category}")
            category_results = {}
            
            for tool_name, executables in tools.items():
                print(f"   🔍 البحث عن: {tool_name}")
                found_installations = []
                
                # البحث في PATH أولاً
                for executable in executables:
                    exe_in_path = shutil.which(executable)
                    if exe_in_path:
                        version = self.get_tool_version(exe_in_path, tool_name)
                        found_installations.append({
                            'path': exe_in_path,
                            'executable': executable,
                            'version': version,
                            'found_in_path': True
                        })
                
                # البحث في المسارات الشائعة
                for pattern in search_patterns:
                    try:
                        matches = glob.glob(pattern)
                        for match in matches:
                            if os.path.isdir(match):
                                for executable in executables:
                                    # البحث المباشر
                                    exe_path = os.path.join(match, executable)
                                    if os.path.exists(exe_path):
                                        version = self.get_tool_version(exe_path, tool_name)
                                        found_installations.append({
                                            'path': exe_path,
                                            'executable': executable,
                                            'version': version,
                                            'directory': match
                                        })
                                    
                                    # البحث في مجلدات فرعية (عمق محدود)
                                    for root, dirs, files in os.walk(match):
                                        if executable in files:
                                            full_path = os.path.join(root, executable)
                                            version = self.get_tool_version(full_path, tool_name)
                                            found_installations.append({
                                                'path': full_path,
                                                'executable': executable,
                                                'version': version,
                                                'directory': root
                                            })
                                        
                                        # تحديد عمق البحث
                                        if root.count(os.sep) - match.count(os.sep) > 2:
                                            dirs.clear()
                    except Exception as e:
                        continue
                
                # إزالة التكرارات
                unique_installations = []
                seen_paths = set()
                for installation in found_installations:
                    if installation['path'] not in seen_paths:
                        unique_installations.append(installation)
                        seen_paths.add(installation['path'])
                        print(f"      ✅ وجد في: {installation['path']}")
                        print(f"         📋 الإصدار: {installation['version']}")
                
                if unique_installations:
                    category_results[tool_name] = {
                        'found': True,
                        'installations': unique_installations
                    }
                else:
                    print(f"      ❌ لم يوجد: {tool_name}")
                    category_results[tool_name] = {'found': False}
            
            all_discovered_tools[category] = category_results
        
        self.scan_results['discovered_tools'] = all_discovered_tools

    def get_tool_version(self, tool_path, tool_name):
        """الحصول على إصدار الأداة مع معالجة خاصة لكل أداة"""
        version_commands = {
            'node': ['--version'],
            'npm': ['--version'],
            'python': ['--version'],
            'java': ['-version'],
            'git': ['--version'],
            'docker': ['--version'],
            'go': ['version'],
            'dotnet': ['--version'],
            'mysql': ['--version'],
            'postgresql': ['--version']
        }
        
        cmd = version_commands.get(tool_name, ['--version'])
        
        try:
            result = subprocess.run(
                [tool_path] + cmd,
                capture_output=True,
                text=True,
                timeout=10,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0:
                return result.stdout.strip()[:100]  # تحديد طول الإخراج
            else:
                return result.stderr.strip()[:100] if result.stderr else "Unknown"
                
        except subprocess.TimeoutExpired:
            return "Timeout"
        except Exception as e:
            return f"Error: {str(e)[:50]}"

    def analyze_package_managers(self):
        """تحليل مدراء الحزم وحزمهم"""
        print("📦 تحليل مدراء الحزم...")
        
        package_managers_data = {}
        
        # تحليل npm
        npm_path = shutil.which('npm.cmd') or shutil.which('npm.exe')
        if npm_path:
            try:
                result = subprocess.run([npm_path, 'list', '-g', '--depth=0'], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    package_managers_data['npm'] = {
                        'global_packages': result.stdout,
                        'path': npm_path
                    }
            except:
                pass
        
        # تحليل pip
        pip_path = shutil.which('pip.exe') or shutil.which('pip3.exe')
        if pip_path:
            try:
                result = subprocess.run([pip_path, 'list'], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    package_managers_data['pip'] = {
                        'packages': result.stdout,
                        'path': pip_path
                    }
            except:
                pass
        
        self.scan_results['package_managers_data'] = package_managers_data

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        print("📊 إنشاء التقرير الشامل...")
        
        # حساب الإحصائيات
        stats = {
            'total_tools_categories': len(self.comprehensive_tools),
            'tools_found': 0,
            'tools_not_found': 0,
            'total_installations': 0,
            'development_paths': len(self.scan_results.get('environment_variables', {}).get('development_paths', [])),
            'chocolatey_packages': len(self.scan_results.get('chocolatey_packages', {})),
            'scoop_apps': len(self.scan_results.get('scoop_apps', {}))
        }
        
        for category, tools in self.scan_results.get('discovered_tools', {}).items():
            for tool_name, tool_info in tools.items():
                if tool_info.get('found', False):
                    stats['tools_found'] += 1
                    stats['total_installations'] += len(tool_info.get('installations', []))
                else:
                    stats['tools_not_found'] += 1
        
        self.scan_results['statistics'] = stats

    def save_comprehensive_results(self, output_dir='SHARED_REQUIREMENTS'):
        """حفظ النتائج الشاملة"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # حفظ النتائج الكاملة
        results_file = output_path / 'comprehensive_development_environment_scan.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        # إنشاء تقرير مفصل
        report_file = output_path / 'comprehensive_development_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            self.write_markdown_report(f)
        
        print(f"✅ تم حفظ النتائج الشاملة في: {output_path}")

    def write_markdown_report(self, f):
        """كتابة تقرير Markdown مفصل"""
        stats = self.scan_results['statistics']
        
        f.write("# 🌟 تقرير البيئة التطويرية الشامل\n")
        f.write("# Comprehensive Development Environment Report\n\n")
        f.write(f"**تاريخ الفحص:** {self.scan_results['scan_date']}\n\n")
        
        f.write("## 📊 الإحصائيات العامة\n\n")
        f.write(f"- 🔢 **إجمالي فئات الأدوات:** {stats['total_tools_categories']}\n")
        f.write(f"- ✅ **أدوات موجودة:** {stats['tools_found']}\n")
        f.write(f"- ❌ **أدوات غير موجودة:** {stats['tools_not_found']}\n")
        f.write(f"- 📦 **إجمالي التثبيتات:** {stats['total_installations']}\n")
        f.write(f"- 📍 **مسارات التطوير في PATH:** {stats['development_paths']}\n")
        f.write(f"- 🍫 **حزم Chocolatey:** {stats['chocolatey_packages']}\n")
        f.write(f"- 🥄 **تطبيقات Scoop:** {stats['scoop_apps']}\n\n")
        
        # تفاصيل الأدوات المكتشفة
        f.write("## 🔍 الأدوات المكتشفة\n\n")
        for category, tools in self.scan_results.get('discovered_tools', {}).items():
            f.write(f"### 📂 {category.upper()}\n\n")
            for tool_name, tool_info in tools.items():
                if tool_info.get('found', False):
                    f.write(f"#### ✅ {tool_name}\n")
                    for installation in tool_info.get('installations', []):
                        f.write(f"- **المسار:** `{installation['path']}`\n")
                        f.write(f"- **الإصدار:** {installation['version']}\n")
                        f.write("\n")
                else:
                    f.write(f"#### ❌ {tool_name}: غير موجود\n\n")

def main():
    """الدالة الرئيسية"""
    print("🌟 مدير البيئة التطويرية الشامل")
    print("=" * 60)
    
    manager = ComprehensiveDevelopmentEnvironmentManager()
    
    # فحص متغيرات البيئة
    manager.scan_environment_variables()
    
    # فحص Chocolatey
    manager.scan_chocolatey_packages()
    
    # فحص Scoop
    manager.scan_scoop_apps()
    
    # الفحص الشامل للأدوات
    manager.comprehensive_tool_scan()
    
    # تحليل مدراء الحزم
    manager.analyze_package_managers()
    
    # إنشاء التقرير
    manager.generate_comprehensive_report()
    
    # حفظ النتائج
    manager.save_comprehensive_results()
    
    # عرض الإحصائيات النهائية
    stats = manager.scan_results['statistics']
    print("\n🎯 إحصائيات الفحص الشامل:")
    print(f"   ✅ أدوات موجودة: {stats['tools_found']}")
    print(f"   ❌ أدوات غير موجودة: {stats['tools_not_found']}")
    print(f"   📦 إجمالي التثبيتات: {stats['total_installations']}")
    print(f"   📍 مسارات التطوير: {stats['development_paths']}")
    print(f"   🍫 حزم Chocolatey: {stats['chocolatey_packages']}")
    print(f"   🥄 تطبيقات Scoop: {stats['scoop_apps']}")
    
    print("\n🌟 تم إكمال الفحص الشامل للبيئة التطويرية بنجاح!")
    return manager.scan_results

if __name__ == "__main__":
    main()
