#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فاحص شامل لجميع أدوات التطوير
Universal Development Tools Scanner

يقوم بفحص جميع أدوات التطوير المثبتة على القرص C
Scans all development tools installed on C: drive
"""

import os
import sys
import subprocess
import json
import glob
import winreg
from pathlib import Path
from datetime import datetime
import shutil

class UniversalDevelopmentToolsScanner:
    def __init__(self):
        self.scan_results = {
            'scan_date': datetime.now().isoformat(),
            'system_info': {},
            'development_tools': {},
            'package_managers': {},
            'programming_languages': {},
            'databases': {},
            'containerization': {},
            'version_control': {},
            'editors_ides': {},
            'build_tools': {},
            'statistics': {}
        }
        
        # تعريف أدوات التطوير المختلفة
        self.tools_to_scan = {
            'programming_languages': {
                'python': {
                    'executables': ['python.exe', 'python3.exe'],
                    'common_paths': [
                        'C:/Python*',
                        'C:/Program Files/Python*',
                        'C:/Users/<USER>/AppData/Local/Programs/Python*',
                        'C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python.exe'
                    ],
                    'version_command': ['--version']
                },
                'node': {
                    'executables': ['node.exe'],
                    'common_paths': [
                        'C:/Program Files/nodejs',
                        'C:/Program Files (x86)/nodejs',
                        'C:/Users/<USER>/AppData/Roaming/npm'
                    ],
                    'version_command': ['--version']
                },
                'java': {
                    'executables': ['java.exe', 'javac.exe'],
                    'common_paths': [
                        'C:/Program Files/Java/*',
                        'C:/Program Files (x86)/Java/*',
                        'C:/Program Files/Eclipse Adoptium/*',
                        'C:/Program Files/Amazon Corretto/*'
                    ],
                    'version_command': ['-version']
                },
                'dotnet': {
                    'executables': ['dotnet.exe'],
                    'common_paths': [
                        'C:/Program Files/dotnet',
                        'C:/Program Files (x86)/dotnet'
                    ],
                    'version_command': ['--version']
                },
                'go': {
                    'executables': ['go.exe'],
                    'common_paths': [
                        'C:/Program Files/Go',
                        'C:/Go',
                        'C:/Users/<USER>/go'
                    ],
                    'version_command': ['version']
                },
                'rust': {
                    'executables': ['rustc.exe', 'cargo.exe'],
                    'common_paths': [
                        'C:/Users/<USER>/.cargo/bin',
                        'C:/Program Files/Rust'
                    ],
                    'version_command': ['--version']
                }
            },
            'package_managers': {
                'npm': {
                    'executables': ['npm.exe', 'npm.cmd'],
                    'common_paths': [
                        'C:/Program Files/nodejs',
                        'C:/Users/<USER>/AppData/Roaming/npm'
                    ],
                    'version_command': ['--version']
                },
                'yarn': {
                    'executables': ['yarn.exe', 'yarn.cmd'],
                    'common_paths': [
                        'C:/Program Files (x86)/Yarn',
                        'C:/Users/<USER>/AppData/Roaming/npm',
                        'C:/Users/<USER>/AppData/Local/Yarn'
                    ],
                    'version_command': ['--version']
                },
                'pip': {
                    'executables': ['pip.exe', 'pip3.exe'],
                    'common_paths': [
                        'C:/Python*/Scripts',
                        'C:/Users/<USER>/AppData/Local/Programs/Python*/Scripts'
                    ],
                    'version_command': ['--version']
                },
                'conda': {
                    'executables': ['conda.exe'],
                    'common_paths': [
                        'C:/Users/<USER>/Anaconda*',
                        'C:/Users/<USER>/Miniconda*',
                        'C:/ProgramData/Anaconda*'
                    ],
                    'version_command': ['--version']
                },
                'uv': {
                    'executables': ['uv.exe'],
                    'common_paths': [
                        'C:/Users/<USER>/.cargo/bin',
                        'C:/Users/<USER>/AppData/Local/Programs/uv'
                    ],
                    'version_command': ['--version']
                },
                'composer': {
                    'executables': ['composer.exe', 'composer.phar'],
                    'common_paths': [
                        'C:/ProgramData/ComposerSetup/bin',
                        'C:/Users/<USER>/AppData/Roaming/Composer'
                    ],
                    'version_command': ['--version']
                }
            },
            'version_control': {
                'git': {
                    'executables': ['git.exe'],
                    'common_paths': [
                        'C:/Program Files/Git',
                        'C:/Program Files (x86)/Git'
                    ],
                    'version_command': ['--version']
                },
                'svn': {
                    'executables': ['svn.exe'],
                    'common_paths': [
                        'C:/Program Files/TortoiseSVN',
                        'C:/Program Files (x86)/Subversion'
                    ],
                    'version_command': ['--version']
                }
            },
            'containerization': {
                'docker': {
                    'executables': ['docker.exe'],
                    'common_paths': [
                        'C:/Program Files/Docker',
                        'C:/Users/<USER>/AppData/Local/Docker'
                    ],
                    'version_command': ['--version']
                },
                'podman': {
                    'executables': ['podman.exe'],
                    'common_paths': [
                        'C:/Program Files/RedHat/Podman'
                    ],
                    'version_command': ['--version']
                }
            },
            'databases': {
                'mysql': {
                    'executables': ['mysql.exe', 'mysqld.exe'],
                    'common_paths': [
                        'C:/Program Files/MySQL/*',
                        'C:/Program Files (x86)/MySQL/*'
                    ],
                    'version_command': ['--version']
                },
                'postgresql': {
                    'executables': ['psql.exe', 'postgres.exe'],
                    'common_paths': [
                        'C:/Program Files/PostgreSQL/*',
                        'C:/Program Files (x86)/PostgreSQL/*'
                    ],
                    'version_command': ['--version']
                },
                'sqlite': {
                    'executables': ['sqlite3.exe'],
                    'common_paths': [
                        'C:/sqlite',
                        'C:/Program Files/SQLite'
                    ],
                    'version_command': ['-version']
                }
            },
            'build_tools': {
                'cmake': {
                    'executables': ['cmake.exe'],
                    'common_paths': [
                        'C:/Program Files/CMake',
                        'C:/Program Files (x86)/CMake'
                    ],
                    'version_command': ['--version']
                },
                'make': {
                    'executables': ['make.exe', 'mingw32-make.exe'],
                    'common_paths': [
                        'C:/Program Files/Git/usr/bin',
                        'C:/MinGW/bin',
                        'C:/msys64/usr/bin'
                    ],
                    'version_command': ['--version']
                },
                'gradle': {
                    'executables': ['gradle.exe', 'gradle.bat'],
                    'common_paths': [
                        'C:/Gradle',
                        'C:/Program Files/Gradle'
                    ],
                    'version_command': ['--version']
                },
                'maven': {
                    'executables': ['mvn.exe', 'mvn.cmd'],
                    'common_paths': [
                        'C:/Program Files/Apache/maven',
                        'C:/apache-maven*'
                    ],
                    'version_command': ['--version']
                }
            }
        }

    def get_system_info(self):
        """جمع معلومات النظام"""
        print("🖥️ جمع معلومات النظام...")
        
        try:
            import platform
            self.scan_results['system_info'] = {
                'os': platform.system(),
                'os_version': platform.version(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'machine': platform.machine(),
                'node': platform.node()
            }
        except Exception as e:
            print(f"⚠️ خطأ في جمع معلومات النظام: {e}")

    def find_tool_installations(self, tool_name, tool_config):
        """البحث عن تثبيتات أداة محددة"""
        installations = []
        
        # البحث في المسارات الشائعة
        for path_pattern in tool_config.get('common_paths', []):
            try:
                matches = glob.glob(path_pattern, recursive=True)
                for match in matches:
                    if os.path.isdir(match):
                        # البحث عن الملفات التنفيذية
                        for executable in tool_config.get('executables', []):
                            exe_path = os.path.join(match, executable)
                            if os.path.exists(exe_path):
                                installations.append({
                                    'path': exe_path,
                                    'directory': match,
                                    'executable': executable
                                })
                            
                            # البحث في مجلدات فرعية
                            for root, dirs, files in os.walk(match):
                                if executable in files:
                                    full_path = os.path.join(root, executable)
                                    installations.append({
                                        'path': full_path,
                                        'directory': root,
                                        'executable': executable
                                    })
                                # تحديد عمق البحث لتجنب البحث المفرط
                                if root.count(os.sep) - match.count(os.sep) > 3:
                                    dirs.clear()
            except Exception as e:
                print(f"⚠️ خطأ في البحث في {path_pattern}: {e}")
        
        # البحث في PATH
        try:
            for executable in tool_config.get('executables', []):
                exe_in_path = shutil.which(executable)
                if exe_in_path:
                    installations.append({
                        'path': exe_in_path,
                        'directory': os.path.dirname(exe_in_path),
                        'executable': executable,
                        'found_in_path': True
                    })
        except Exception as e:
            print(f"⚠️ خطأ في البحث في PATH: {e}")
        
        # إزالة التكرارات
        unique_installations = []
        seen_paths = set()
        for installation in installations:
            if installation['path'] not in seen_paths:
                unique_installations.append(installation)
                seen_paths.add(installation['path'])
        
        return unique_installations

    def get_tool_version(self, tool_path, version_command):
        """الحصول على إصدار الأداة"""
        try:
            cmd = [tool_path] + version_command
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=10,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return result.stderr.strip() if result.stderr else "Unknown"
                
        except subprocess.TimeoutExpired:
            return "Timeout"
        except Exception as e:
            return f"Error: {str(e)}"

    def scan_all_tools(self):
        """فحص جميع الأدوات"""
        print("🔍 بدء الفحص الشامل لجميع أدوات التطوير...")
        
        for category, tools in self.tools_to_scan.items():
            print(f"\n📂 فحص فئة: {category}")
            category_results = {}
            
            for tool_name, tool_config in tools.items():
                print(f"   🔍 البحث عن: {tool_name}")
                
                installations = self.find_tool_installations(tool_name, tool_config)
                
                if installations:
                    tool_info = {
                        'found': True,
                        'installations': []
                    }
                    
                    for installation in installations:
                        version = self.get_tool_version(
                            installation['path'], 
                            tool_config.get('version_command', ['--version'])
                        )
                        
                        installation['version'] = version
                        tool_info['installations'].append(installation)
                        
                        print(f"      ✅ وجد في: {installation['path']}")
                        print(f"         📋 الإصدار: {version}")
                    
                    category_results[tool_name] = tool_info
                else:
                    print(f"      ❌ لم يوجد: {tool_name}")
                    category_results[tool_name] = {'found': False}
            
            self.scan_results[category] = category_results

    def scan_registry_for_tools(self):
        """فحص سجل Windows للأدوات المثبتة"""
        print("🔍 فحص سجل Windows...")
        
        registry_tools = {}
        
        # مفاتيح السجل الشائعة للبرامج المثبتة
        registry_keys = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
        ]
        
        development_keywords = [
            'python', 'node', 'npm', 'git', 'docker', 'java', 'visual studio',
            'intellij', 'eclipse', 'android studio', 'unity', 'unreal',
            'mysql', 'postgresql', 'mongodb', 'redis', 'cmake', 'gradle',
            'maven', 'composer', 'yarn', 'conda', 'anaconda', 'miniconda'
        ]
        
        for hkey, subkey in registry_keys:
            try:
                with winreg.OpenKey(hkey, subkey) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey_handle:
                                try:
                                    display_name = winreg.QueryValueEx(subkey_handle, "DisplayName")[0]
                                    
                                    # التحقق من وجود كلمات مفتاحية للتطوير
                                    if any(keyword in display_name.lower() for keyword in development_keywords):
                                        try:
                                            version = winreg.QueryValueEx(subkey_handle, "DisplayVersion")[0]
                                        except:
                                            version = "Unknown"
                                        
                                        try:
                                            install_location = winreg.QueryValueEx(subkey_handle, "InstallLocation")[0]
                                        except:
                                            install_location = "Unknown"
                                        
                                        registry_tools[display_name] = {
                                            'version': version,
                                            'install_location': install_location,
                                            'registry_key': subkey_name
                                        }
                                        
                                except:
                                    continue
                        except:
                            continue
            except Exception as e:
                print(f"⚠️ خطأ في فحص السجل {subkey}: {e}")
        
        self.scan_results['registry_tools'] = registry_tools
        print(f"✅ وجد {len(registry_tools)} أداة في السجل")

    def generate_statistics(self):
        """إنشاء الإحصائيات"""
        stats = {
            'total_categories': len(self.tools_to_scan),
            'tools_found': 0,
            'tools_not_found': 0,
            'total_installations': 0,
            'registry_tools': len(self.scan_results.get('registry_tools', {}))
        }
        
        for category, tools in self.scan_results.items():
            if category in ['scan_date', 'system_info', 'statistics', 'registry_tools']:
                continue
                
            for tool_name, tool_info in tools.items():
                if tool_info.get('found', False):
                    stats['tools_found'] += 1
                    stats['total_installations'] += len(tool_info.get('installations', []))
                else:
                    stats['tools_not_found'] += 1
        
        self.scan_results['statistics'] = stats

    def save_results(self, output_dir='SHARED_REQUIREMENTS'):
        """حفظ النتائج"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # حفظ النتائج الكاملة
        results_file = output_path / 'universal_development_tools_scan.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
        
        # إنشاء تقرير نصي
        report_file = output_path / 'development_tools_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("🔍 تقرير فحص أدوات التطوير الشامل\n")
            f.write("=" * 60 + "\n")
            f.write(f"تاريخ الفحص: {self.scan_results['scan_date']}\n\n")
            
            # إحصائيات عامة
            stats = self.scan_results['statistics']
            f.write("📊 الإحصائيات العامة:\n")
            f.write(f"   🔢 إجمالي الفئات: {stats['total_categories']}\n")
            f.write(f"   ✅ أدوات موجودة: {stats['tools_found']}\n")
            f.write(f"   ❌ أدوات غير موجودة: {stats['tools_not_found']}\n")
            f.write(f"   📦 إجمالي التثبيتات: {stats['total_installations']}\n")
            f.write(f"   📋 أدوات في السجل: {stats['registry_tools']}\n\n")
            
            # تفاصيل كل فئة
            for category, tools in self.scan_results.items():
                if category in ['scan_date', 'system_info', 'statistics', 'registry_tools']:
                    continue
                
                f.write(f"📂 {category.upper()}:\n")
                for tool_name, tool_info in tools.items():
                    if tool_info.get('found', False):
                        f.write(f"   ✅ {tool_name}:\n")
                        for installation in tool_info.get('installations', []):
                            f.write(f"      📍 {installation['path']}\n")
                            f.write(f"      📋 الإصدار: {installation['version']}\n")
                    else:
                        f.write(f"   ❌ {tool_name}: غير موجود\n")
                f.write("\n")
        
        print(f"✅ تم حفظ النتائج في: {output_path}")

def main():
    """الدالة الرئيسية"""
    print("🔍 فاحص شامل لجميع أدوات التطوير")
    print("=" * 60)
    
    scanner = UniversalDevelopmentToolsScanner()
    
    # جمع معلومات النظام
    scanner.get_system_info()
    
    # فحص جميع الأدوات
    scanner.scan_all_tools()
    
    # فحص السجل
    scanner.scan_registry_for_tools()
    
    # إنشاء الإحصائيات
    scanner.generate_statistics()
    
    # حفظ النتائج
    scanner.save_results()
    
    # عرض الإحصائيات
    stats = scanner.scan_results['statistics']
    print("\n📊 إحصائيات الفحص:")
    print(f"   ✅ أدوات موجودة: {stats['tools_found']}")
    print(f"   ❌ أدوات غير موجودة: {stats['tools_not_found']}")
    print(f"   📦 إجمالي التثبيتات: {stats['total_installations']}")
    print(f"   📋 أدوات في السجل: {stats['registry_tools']}")
    
    print("\n🎯 تم إكمال الفحص الشامل بنجاح!")
    return scanner.scan_results

if __name__ == "__main__":
    main()
