{"project_info": {"path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpoys3v90f", "name": "tmpoys3v90f", "type": "custom", "structure": {"project_exists": true, "is_directory": true, "files_count": 1, "directories_count": 0, "python_files": ["syntax_error.py"], "config_files": [], "data_files": []}, "agent_type": "error_detector", "analysis_time": "2025-07-14T12:47:19.605711"}, "error_analysis": {"syntax_errors": [{"file": "syntax_error.py", "line": 2, "column": 20, "error": "'(' was never closed", "severity": "high", "type": "syntax_error"}], "import_errors": [], "style_issues": [], "logic_warnings": [], "security_issues": [], "performance_issues": [], "total_files_analyzed": 1, "files_with_errors": 1}, "quality_analysis": {"complexity_score": 99.6, "maintainability_score": 49.8, "readability_score": 49.8, "documentation_score": 0.0, "test_coverage_estimate": 0}, "recommendations": [{"category": "general", "priority": "medium", "title": "استخدام أدوات فحص الكود", "description": "استخدم أدوات مثل flake8 أو pylint لفحص جودة الكود", "action": "pip install flake8 pylint"}, {"category": "testing", "priority": "high", "title": "إضافة اختبارات وحدة", "description": "أض<PERSON> اختبارات شاملة لتحسين جودة الكود", "action": "pip install pytest"}, {"category": "documentation", "priority": "medium", "title": "تحسين التوثيق", "description": "أض<PERSON> docstrings للدوال والفئات", "action": "إضافة توثيق للكود"}], "summary": {"total_files_analyzed": 1, "files_with_errors": 1, "total_errors_found": 1, "quality_score": 39.84, "severity_breakdown": {"high": 1, "medium": 0, "low": 0}, "status": "completed", "analysis_time": "2025-07-14T12:47:19.607542"}}