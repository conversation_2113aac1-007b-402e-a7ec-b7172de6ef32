#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقل ملفات النظام المعزول
"""

import shutil
import os
from pathlib import Path

def move_isolation_files():
    """نقل ملفات النظام المعزول"""
    source = Path("isolation")
    dest = Path("anubis_isolation_system")
    
    if source.exists():
        print(f"📁 نقل من: {source}")
        print(f"📁 إلى: {dest}")
        
        # إنشاء المجلد الهدف
        dest.mkdir(exist_ok=True)
        
        # نقل جميع الملفات
        for item in source.iterdir():
            dest_path = dest / item.name
            if item.is_dir():
                if dest_path.exists():
                    shutil.rmtree(dest_path)
                shutil.copytree(item, dest_path)
                print(f"✅ نقل مجلد: {item.name}")
            else:
                shutil.copy2(item, dest_path)
                print(f"✅ نقل ملف: {item.name}")
        
        print("🎉 تم نقل جميع الملفات بنجاح!")
        return True
    else:
        print("❌ مجلد isolation غير موجود")
        return False

def move_docker_compose():
    """نقل ملف docker-compose المعزول"""
    source = Path("docker-compose-anubis-isolation.yml")
    dest = Path("anubis_isolation_system/docker-compose-isolation.yml")
    
    if source.exists():
        shutil.copy2(source, dest)
        print(f"✅ نقل: {source.name} → {dest}")
        return True
    return False

def move_isolation_scripts():
    """نقل السكريبتات المتعلقة بالعزل"""
    isolation_files = [
        "anubis_docker_isolation_launcher.py",
        "anubis_docker_isolation_system.py", 
        "anubis_isolation_status_checker.py",
        "anubis_isolation_system_manager.py",
        "anubis_quick_docker_diagnosis.py"
    ]
    
    dest_dir = Path("anubis_isolation_system")
    dest_dir.mkdir(exist_ok=True)
    
    for file_name in isolation_files:
        source = Path(file_name)
        if source.exists():
            dest = dest_dir / file_name
            shutil.copy2(source, dest)
            print(f"✅ نقل: {file_name}")

if __name__ == "__main__":
    print("🏺 نقل ملفات النظام المعزول")
    print("=" * 40)
    
    move_isolation_files()
    move_docker_compose()
    move_isolation_scripts()
    
    print("\n📋 التحقق من النتيجة:")
    dest = Path("anubis_isolation_system")
    if dest.exists():
        items = list(dest.iterdir())
        print(f"📁 المجلد الجديد يحتوي على {len(items)} عنصر:")
        for item in items:
            print(f"   {'📁' if item.is_dir() else '📄'} {item.name}")
    
    print("\n✅ تم تنظيم النظام المعزول!")
