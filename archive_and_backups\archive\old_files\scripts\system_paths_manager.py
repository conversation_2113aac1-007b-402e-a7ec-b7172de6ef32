#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📁 مدير المسارات والعمليات لنظام أنوبيس
System Paths and Process Manager for Anubis

إدارة شاملة للمسارات والعمليات لتجنب أخطاء الترمينال
"""

import os
import sys
import json
import subprocess
import psutil
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional


class SystemPathsManager:
    """📁 مدير المسارات والعمليات"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.absolute()
        self.project_name = "Universal-AI-Assistants"
        self.active_processes = {}
        self.terminal_counter = 0
        
        # تحديد المسارات الأساسية
        self.paths = self._initialize_paths()
        
        # إنشاء المجلدات المطلوبة
        self._create_required_directories()
        
        print(f"📁 مدير المسارات - المسار الأساسي: {self.base_path}")
    
    def _initialize_paths(self) -> Dict[str, Path]:
        """تهيئة جميع المسارات المطلوبة"""
        paths = {
            'base': self.base_path,
            'core': self.base_path / 'core',
            'agents': self.base_path / 'agents',
            'configs': self.base_path / 'configs',
            'tests': self.base_path / 'tests',
            'logs': self.base_path / 'logs',
            'reports': self.base_path / 'reports',
            'temp': self.base_path / 'temp',
            'backup': self.base_path / 'backup'
        }
        
        return paths
    
    def _create_required_directories(self):
        """إنشاء المجلدات المطلوبة"""
        for name, path in self.paths.items():
            try:
                path.mkdir(exist_ok=True)
                print(f"   📂 {name}: {path}")
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء {name}: {e}")
    
    def get_path(self, path_name: str) -> Path:
        """الحصول على مسار محدد"""
        return self.paths.get(path_name, self.base_path)
    
    def get_agent_path(self, agent_name: str) -> Path:
        """الحصول على مسار وكيل محدد"""
        return self.paths['agents'] / f"{agent_name}.py"
    
    def get_safe_working_directory(self) -> str:
        """الحصول على مجلد عمل آمن"""
        return str(self.base_path)
    
    def kill_all_active_processes(self):
        """إنهاء جميع العمليات النشطة"""
        print("🔄 إنهاء جميع العمليات النشطة...")
        
        killed_count = 0
        for process_id, process_info in list(self.active_processes.items()):
            try:
                if 'process' in process_info:
                    process = process_info['process']
                    if process.poll() is None:  # العملية ما زالت تعمل
                        process.terminate()
                        time.sleep(1)
                        if process.poll() is None:
                            process.kill()
                        killed_count += 1
                        print(f"   ⚡ تم إنهاء العملية: {process_id}")
                
                del self.active_processes[process_id]
                
            except Exception as e:
                print(f"   ⚠️ خطأ في إنهاء العملية {process_id}: {e}")
        
        print(f"✅ تم إنهاء {killed_count} عملية")
    
    def run_safe_command(self, command: str, timeout: int = 60, background: bool = False) -> Dict[str, Any]:
        """تشغيل أمر بشكل آمن"""
        self.terminal_counter += 1
        process_id = f"process_{self.terminal_counter}"
        
        print(f"🚀 تشغيل أمر آمن [{process_id}]: {command}")
        
        try:
            # تشغيل الأمر
            if background:
                process = subprocess.Popen(
                    command,
                    shell=True,
                    cwd=self.get_safe_working_directory(),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                self.active_processes[process_id] = {
                    'process': process,
                    'command': command,
                    'start_time': datetime.now(),
                    'background': True
                }
                
                return {
                    'process_id': process_id,
                    'status': 'running_background',
                    'pid': process.pid
                }
            else:
                # تشغيل متزامن
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=self.get_safe_working_directory(),
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                
                return {
                    'process_id': process_id,
                    'status': 'completed',
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'success': result.returncode == 0
                }
                
        except subprocess.TimeoutExpired:
            return {
                'process_id': process_id,
                'status': 'timeout',
                'error': f'انتهت مهلة التشغيل ({timeout} ثانية)'
            }
        except Exception as e:
            return {
                'process_id': process_id,
                'status': 'error',
                'error': str(e)
            }
    
    def run_gemini_command(self, prompt: str, timeout: int = 120) -> Dict[str, Any]:
        """تشغيل أمر Gemini CLI بشكل آمن"""
        # تنظيف العمليات السابقة أولاً
        self.cleanup_old_processes()
        
        # تحضير الأمر
        safe_prompt = prompt.replace('"', '\\"')  # تأمين النص
        command = f'gemini --prompt "{safe_prompt}"'
        
        print(f"💎 تشغيل Gemini CLI...")
        
        return self.run_safe_command(command, timeout=timeout)
    
    def cleanup_old_processes(self):
        """تنظيف العمليات القديمة"""
        current_time = datetime.now()
        to_remove = []
        
        for process_id, process_info in self.active_processes.items():
            try:
                # فحص العمليات الأقدم من 5 دقائق
                if (current_time - process_info['start_time']).total_seconds() > 300:
                    if 'process' in process_info:
                        process = process_info['process']
                        if process.poll() is None:
                            process.terminate()
                    to_remove.append(process_id)
                    
            except Exception as e:
                print(f"   ⚠️ خطأ في تنظيف العملية {process_id}: {e}")
                to_remove.append(process_id)
        
        for process_id in to_remove:
            del self.active_processes[process_id]
            print(f"   🗑️ تم تنظيف العملية: {process_id}")
    
    def get_process_status(self, process_id: str) -> Dict[str, Any]:
        """الحصول على حالة عملية محددة"""
        if process_id not in self.active_processes:
            return {'status': 'not_found'}
        
        process_info = self.active_processes[process_id]
        
        try:
            if 'process' in process_info:
                process = process_info['process']
                if process.poll() is None:
                    return {
                        'status': 'running',
                        'pid': process.pid,
                        'start_time': process_info['start_time'].isoformat()
                    }
                else:
                    return {
                        'status': 'completed',
                        'returncode': process.returncode,
                        'start_time': process_info['start_time'].isoformat()
                    }
            else:
                return {'status': 'unknown'}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def save_paths_config(self) -> str:
        """حفظ إعدادات المسارات"""
        config = {
            'timestamp': datetime.now().isoformat(),
            'base_path': str(self.base_path),
            'project_name': self.project_name,
            'paths': {name: str(path) for name, path in self.paths.items()},
            'active_processes_count': len(self.active_processes)
        }
        
        config_file = self.paths['configs'] / 'system_paths.json'
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ إعدادات المسارات: {config_file}")
            return str(config_file)
        except Exception as e:
            print(f"❌ خطأ في حفظ الإعدادات: {e}")
            return None
    
    def create_agent_file_safely(self, agent_name: str, content: str) -> str:
        """إنشاء ملف وكيل بشكل آمن"""
        agent_path = self.get_agent_path(agent_name)
        
        try:
            # إنشاء نسخة احتياطية إذا كان الملف موجوداً
            if agent_path.exists():
                backup_path = self.paths['backup'] / f"{agent_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
                agent_path.rename(backup_path)
                print(f"📦 تم إنشاء نسخة احتياطية: {backup_path}")
            
            # كتابة الملف الجديد
            with open(agent_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إنشاء الوكيل: {agent_path}")
            return str(agent_path)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوكيل {agent_name}: {e}")
            return None
    
    def run_python_script_safely(self, script_name: str, timeout: int = 120) -> Dict[str, Any]:
        """تشغيل سكريبت Python بشكل آمن"""
        script_path = self.base_path / script_name
        
        if not script_path.exists():
            return {
                'status': 'error',
                'error': f'السكريبت غير موجود: {script_path}'
            }
        
        command = f"python {script_name}"
        return self.run_safe_command(command, timeout=timeout)
    
    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        return {
            'timestamp': datetime.now().isoformat(),
            'base_path': str(self.base_path),
            'paths_count': len(self.paths),
            'active_processes': len(self.active_processes),
            'terminal_counter': self.terminal_counter,
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'cwd': os.getcwd()
            },
            'paths': {name: str(path) for name, path in self.paths.items()}
        }


# إنشاء مثيل عام للاستخدام
paths_manager = SystemPathsManager()


def main():
    """اختبار مدير المسارات"""
    print("📁 اختبار مدير المسارات والعمليات")
    print("=" * 50)
    
    # عرض حالة النظام
    status = paths_manager.get_system_status()
    print(f"📊 حالة النظام:")
    print(f"   📂 المسار الأساسي: {status['base_path']}")
    print(f"   📁 عدد المسارات: {status['paths_count']}")
    print(f"   🔄 العمليات النشطة: {status['active_processes']}")
    
    # اختبار أمر بسيط
    print(f"\n🧪 اختبار تشغيل أمر بسيط...")
    result = paths_manager.run_safe_command("echo 'مرحبا من نظام أنوبيس'", timeout=10)
    
    if result['status'] == 'completed' and result['success']:
        print(f"   ✅ نجح الاختبار: {result['stdout'].strip()}")
    else:
        print(f"   ❌ فشل الاختبار: {result}")
    
    # حفظ الإعدادات
    config_file = paths_manager.save_paths_config()
    
    print(f"\n✅ مدير المسارات جاهز للاستخدام!")
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
