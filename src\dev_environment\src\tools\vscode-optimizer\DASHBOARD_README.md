# 🎛️ VS Code Process Control Dashboard

## 📋 نظرة عامة

واجهة تحكم شاملة وقوية لإدارة عمليات VS Code والإضافات بطريقة سهلة ومرئية.

## ✨ المميزات الرئيسية

### 🔄 إدارة العمليات
- **مراقبة في الوقت الفعلي** للعمليات النشطة
- **عرض تفصيلي** لاستهلاك المعالج والذاكرة
- **إغلاق انتقائي** للعمليات المعلقة
- **تنظيف شامل** لجميع العمليات

### 🧩 إدارة الإضافات
- **عرض جميع الإضافات** المثبتة
- **تفعيل/تعطيل** الإضافات بنقرة واحدة
- **معلومات تفصيلية** عن كل إضافة
- **إدارة سهلة** للإضافات المتعددة

### 📊 مراقبة الأداء
- **إحصائيات مباشرة** للنظام
- **رسوم بيانية ملونة** للبيانات
- **تحديث تلقائي** كل 5 ثواني
- **تقارير قابلة للحفظ**

### 🛠️ أدوات التحسين
- **إعادة تشغيل ذكية** لـ VS Code
- **تحسين الأداء** التلقائي
- **تنظيف الذاكرة** المتقدم
- **إعدادات محسنة** للتشغيل

## 🚀 كيفية التشغيل

### الطريقة السهلة (Windows)
```bash
# انقر مرتين على الملف
run_dashboard.bat
```

### الطريقة اليدوية
```bash
# تثبيت المتطلبات
pip install psutil

# تشغيل التطبيق
python process_control_dashboard.py
```

## 🎯 كيفية الاستخدام

### 1️⃣ مراقبة العمليات
- **تحديث**: انقر على "🔄 تحديث" لتحديث قائمة العمليات
- **إغلاق عملية**: حدد عملية واضغط "🚫 إغلاق المحدد"
- **تنظيف شامل**: اضغط "🧹 تنظيف شامل" لإغلاق جميع العمليات

### 2️⃣ إدارة الإضافات
- **تحديث الإضافات**: انقر "🔄 تحديث الإضافات"
- **تعطيل إضافة**: حدد إضافة واضغط "❌ تعطيل المحدد"
- **تفعيل إضافة**: حدد إضافة واضغط "✅ تفعيل المحدد"

### 3️⃣ المراقبة التلقائية
- فعل "مراقبة تلقائية" للتحديث كل 5 ثواني
- راقب الإحصائيات في الوقت الفعلي
- احفظ التقارير للمراجعة لاحقاً

### 4️⃣ أدوات التحسين
- **🚀 إعادة تشغيل VS Code**: إعادة تشغيل نظيفة
- **⚡ تحسين الأداء**: تحسين شامل للأداء
- **💾 حفظ التقرير**: حفظ تقرير مفصل

## 📊 فهم الإحصائيات

### العمليات النشطة (أحمر)
عدد العمليات المرتبطة بـ VS Code

### استهلاك الذاكرة (أزرق فاتح)
إجمالي استهلاك الذاكرة بالميجابايت

### استهلاك المعالج (أزرق)
إجمالي استهلاك المعالج كنسبة مئوية

### الإضافات المفعلة (أخضر)
عدد الإضافات النشطة حالياً

## ⚠️ تحذيرات مهمة

### قبل إغلاق العمليات
- **احفظ عملك** قبل إغلاق أي عملية
- **أغلق الملفات المفتوحة** في VS Code
- **تأكد من عدم وجود عمليات مهمة** قيد التشغيل

### عند تعطيل الإضافات
- بعض الإضافات قد تحتاج **إعادة تشغيل** VS Code
- **احفظ إعدادات المشروع** قبل التعطيل
- تأكد من عدم اعتماد مشروعك على الإضافة

## 🔧 استكشاف الأخطاء

### المشكلة: التطبيق لا يعمل
**الحل:**
```bash
# تحقق من Python
python --version

# تثبيت المكتبات
pip install psutil tkinter
```

### المشكلة: لا تظهر العمليات
**الحل:**
- تأكد من تشغيل VS Code
- اضغط "🔄 تحديث"
- تحقق من صلاحيات النظام

### المشكلة: لا تظهر الإضافات
**الحل:**
```bash
# تحقق من VS Code CLI
code --version

# إضافة VS Code للـ PATH
# Windows: إعدادات النظام > متغيرات البيئة
```

## 📝 ملاحظات تقنية

### متطلبات النظام
- **Python 3.6+**
- **VS Code** مثبت ومضاف للـ PATH
- **Windows/Linux/macOS**
- **ذاكرة**: 100MB على الأقل

### المكتبات المستخدمة
- `tkinter`: واجهة المستخدم الرسومية
- `psutil`: مراقبة العمليات والنظام
- `subprocess`: تنفيذ أوامر النظام
- `threading`: المعالجة المتوازية

## 🎨 تخصيص الواجهة

يمكنك تعديل الألوان والخطوط في الكود:

```python
# الألوان الرئيسية
bg_color = '#2d2d2d'      # لون الخلفية
title_color = '#1e1e1e'   # لون شريط العنوان
text_color = '#ffffff'    # لون النص

# ألوان الإحصائيات
stats_colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']
```

## 🤝 المساهمة

نرحب بمساهماتكم! يمكنكم:
- إضافة مميزات جديدة
- تحسين الواجهة
- إصلاح الأخطاء
- تحسين الأداء

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من قسم استكشاف الأخطاء
2. راجع ملف السجل في التطبيق
3. تأكد من تحديث جميع المكتبات

---

**🎛️ استمتع بالتحكم الكامل في VS Code! 🎛️**
