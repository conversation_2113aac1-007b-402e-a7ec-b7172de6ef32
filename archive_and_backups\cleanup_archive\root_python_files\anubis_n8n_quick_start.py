#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 تشغيل سريع لـ n8n - نظام أنوبيس
Anubis n8n Quick Start
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def check_node_npm():
    """فحص توفر Node.js و npm"""
    try:
        # فحص Node.js
        node_result = subprocess.run(['node', '--version'], 
                                   capture_output=True, text=True, timeout=10)
        if node_result.returncode == 0:
            print(f"✅ Node.js متوفر: {node_result.stdout.strip()}")
        else:
            print("❌ Node.js غير متوفر")
            return False
        
        # فحص npm
        npm_result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=10)
        if npm_result.returncode == 0:
            print(f"✅ npm متوفر: {npm_result.stdout.strip()}")
            return True
        else:
            print("❌ npm غير متوفر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص Node.js/npm: {e}")
        return False

def check_n8n_installed():
    """فحص تثبيت n8n"""
    try:
        result = subprocess.run(['npx', 'n8n', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ n8n متوفر: {result.stdout.strip()}")
            return True
        else:
            print("⚠️  n8n غير مثبت محلياً")
            return False
    except Exception as e:
        print(f"⚠️  n8n غير مثبت: {e}")
        return False

def install_n8n():
    """تثبيت n8n"""
    print("📦 تثبيت n8n...")
    try:
        result = subprocess.run(['npm', 'install', '-g', 'n8n'], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ تم تثبيت n8n بنجاح")
            return True
        else:
            print(f"❌ فشل تثبيت n8n: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت n8n: {e}")
        return False

def setup_n8n_environment():
    """إعداد بيئة n8n"""
    print("⚙️ إعداد بيئة n8n...")
    
    # إنشاء مجلد البيانات
    n8n_dir = Path.home() / ".n8n"
    n8n_dir.mkdir(exist_ok=True)
    
    # إعداد متغيرات البيئة
    env_vars = {
        "N8N_ENCRYPTION_KEY": "anubis_secure_key_2024_n8n_workflows",
        "N8N_USER_MANAGEMENT_DISABLED": "false",
        "N8N_SECURE_COOKIE": "false",
        "N8N_PROTOCOL": "http",
        "N8N_PORT": "5678",
        "N8N_LISTEN_ADDRESS": "0.0.0.0",
        "WEBHOOK_URL": "http://localhost:5678",
        "N8N_METRICS": "true",
        "N8N_LOG_LEVEL": "info",
        "DB_TYPE": "sqlite",
        "GENERIC_TIMEZONE": "UTC"
    }
    
    print("✅ تم إعداد بيئة n8n")
    return env_vars

def start_n8n():
    """تشغيل n8n"""
    print("🚀 تشغيل n8n...")
    
    # إعداد البيئة
    env_vars = setup_n8n_environment()
    
    try:
        # تشغيل n8n
        print("⏳ بدء تشغيل n8n...")
        print("🌐 سيتم فتح n8n على: http://localhost:5678")
        print("⚠️  اضغط Ctrl+C لإيقاف n8n")
        
        # انتظار قليل ثم فتح المتصفح
        def open_browser():
            time.sleep(5)
            try:
                webbrowser.open("http://localhost:5678")
                print("🌐 تم فتح المتصفح")
            except:
                print("⚠️  لم يتم فتح المتصفح تلقائياً")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # تشغيل n8n
        subprocess.run(['npx', 'n8n'], env=env_vars)
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف n8n")
    except Exception as e:
        print(f"❌ خطأ في تشغيل n8n: {e}")

def main():
    """الدالة الرئيسية"""
    print("🏺 تشغيل سريع لـ n8n - نظام أنوبيس")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_node_npm():
        print("\n❌ يجب تثبيت Node.js و npm أولاً")
        print("💡 تحميل من: https://nodejs.org/")
        return
    
    # فحص تثبيت n8n
    if not check_n8n_installed():
        print("\n📦 n8n غير مثبت. هل تريد تثبيته؟ (y/n)")
        choice = input().lower()
        if choice in ['y', 'yes', 'نعم']:
            if not install_n8n():
                print("❌ فشل تثبيت n8n")
                return
        else:
            print("⚠️  سيتم استخدام npx لتشغيل n8n")
    
    # تشغيل n8n
    start_n8n()

if __name__ == "__main__":
    main()
