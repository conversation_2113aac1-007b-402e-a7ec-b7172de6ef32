#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 Anubis API Quick Client
أداة سريعة للتفاعل مع Anubis API
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

class AnubisClient:
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = "anubis-api-key-2025"):
        self.base_url = base_url
        self.headers = {"X-API-Key": api_key, "Content-Type": "application/json"}
    
    def health_check(self) -> Dict[str, Any]:
        """فحص صحة النظام"""
        response = requests.get(f"{self.base_url}/health", headers=self.headers)
        return response.json()
    
    def run_agent(self, agent_type: str, project_path: str = ".", verbose: bool = True) -> Dict[str, Any]:
        """تشغيل وكيل محدد"""
        data = {
            "agent_type": agent_type,
            "project_path": project_path,
            "verbose": verbose
        }
        response = requests.post(f"{self.base_url}/api/v1/agents/run", 
                               json=data, headers=self.headers)
        return response.json()
    
    def ollama_generate(self, prompt: str, model: str = "llama3:8b", temperature: float = 0.7) -> Dict[str, Any]:
        """توليد استجابة باستخدام Ollama"""
        data = {
            "prompt": prompt,
            "model": model,
            "temperature": temperature
        }
        response = requests.post(f"{self.base_url}/api/v1/models/ollama/generate",
                               json=data, headers=self.headers)
        return response.json()
    
    def collaborative_analysis(self, task: str, project_path: str = ".", 
                             models: list = None, agents: list = None) -> Dict[str, Any]:
        """تحليل تعاوني"""
        if models is None:
            models = ["llama3:8b"]
        if agents is None:
            agents = ["project_analyzer", "error_detector"]
            
        data = {
            "task": task,
            "models": models,
            "agents": agents,
            "project_path": project_path,
            "collaboration_mode": "parallel"
        }
        response = requests.post(f"{self.base_url}/api/v1/collaboration/analyze",
                               json=data, headers=self.headers)
        return response.json()

def main():
    """الاستخدام السريع من سطر الأوامر"""
    client = AnubisClient()
    
    if len(sys.argv) < 2:
        print("🏺 Anubis Quick Client")
        print("الاستخدام:")
        print("  python anubis_quick_client.py health")
        print("  python anubis_quick_client.py analyze [project_path]")
        print("  python anubis_quick_client.py errors [project_path]")
        print("  python anubis_quick_client.py ask 'your question'")
        print("  python anubis_quick_client.py collab 'task description' [project_path]")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "health":
            result = client.health_check()
            print("✅ حالة النظام:", json.dumps(result, indent=2, ensure_ascii=False))
        
        elif command == "analyze":
            project_path = sys.argv[2] if len(sys.argv) > 2 else "."
            result = client.run_agent("project_analyzer", project_path)
            print("📊 تحليل المشروع:", json.dumps(result, indent=2, ensure_ascii=False))
        
        elif command == "errors":
            project_path = sys.argv[2] if len(sys.argv) > 2 else "."
            result = client.run_agent("error_detector", project_path)
            print("🔍 كشف الأخطاء:", json.dumps(result, indent=2, ensure_ascii=False))
        
        elif command == "ask":
            if len(sys.argv) < 3:
                print("❌ يرجى تقديم سؤال")
                return
            prompt = sys.argv[2]
            result = client.ollama_generate(prompt)
            print("🤖 الاستجابة:", result.get("response", "لا توجد استجابة"))
        
        elif command == "collab":
            if len(sys.argv) < 3:
                print("❌ يرجى تقديم وصف المهمة")
                return
            task = sys.argv[2]
            project_path = sys.argv[3] if len(sys.argv) > 3 else "."
            result = client.collaborative_analysis(task, project_path)
            print("🤝 التحليل التعاوني:", json.dumps(result, indent=2, ensure_ascii=False))
        
        else:
            print(f"❌ أمر غير معروف: {command}")
    
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بخادم Anubis. تأكد من تشغيل الخادم على http://localhost:8000")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()