#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Anubis n8n Integration Startup Script
تشغيل نظام أنوبيس المتكامل مع n8n
"""

import json
import os
import subprocess
import sys
import threading
import time
from datetime import datetime
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def print_banner():
    """طباعة شعار النظام"""
    banner = """
🏺 ═══════════════════════════════════════════════════════════════
    نظام أنوبيس المتكامل مع n8n
    Anubis AI System with n8n Integration

    🤖 Multi-Model AI Collaboration Platform
    🔗 Workflow Automation with n8n
    📊 Real-time Monitoring with LangSmith
    🗄️ Database Integration
═══════════════════════════════════════════════════════════════ 🏺
"""
    print(banner)


def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")

    requirements = {
        "python": {"command": "python --version", "required": True},
        "node": {"command": "node --version", "required": True},
        "npm": {"command": "npm --version", "required": True},
        "ollama": {"command": "ollama --version", "required": True},
        "gemini": {"command": "gemini --version", "required": False},
    }

    results = {}
    for name, req in requirements.items():
        try:
            result = subprocess.run(
                req["command"].split(), capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                version = result.stdout.strip().split("\n")[0]
                results[name] = {"status": "✅", "version": version}
                print(f"  ✅ {name}: {version}")
            else:
                results[name] = {"status": "❌", "error": result.stderr}
                print(f"  ❌ {name}: غير متاح")
        except Exception as e:
            results[name] = {"status": "❌", "error": str(e)}
            if req["required"]:
                print(f"  ❌ {name}: مطلوب - {e}")
            else:
                print(f"  ⚠️ {name}: اختياري - {e}")

    return results


def install_n8n():
    """تثبيت n8n إذا لم يكن مثبتاً"""
    print("📦 فحص تثبيت n8n...")

    try:
        result = subprocess.run(["n8n", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ n8n مثبت: {result.stdout.strip()}")
            return True
    except:
        pass

    print("  📥 تثبيت n8n...")
    try:
        subprocess.run(["npm", "install", "-g", "n8n"], check=True, timeout=300)
        print("  ✅ تم تثبيت n8n بنجاح")
        return True
    except Exception as e:
        print(f"  ❌ فشل تثبيت n8n: {e}")
        return False


def setup_environment():
    """إعداد متغيرات البيئة"""
    print("⚙️ إعداد متغيرات البيئة...")

    env_vars = {
        "LANGCHAIN_API_KEY": "***************************************************",
        "LANGCHAIN_TRACING_V2": "true",
        "LANGCHAIN_PROJECT": "anubis-ai-system",
        "N8N_BASIC_AUTH_ACTIVE": "true",
        "N8N_BASIC_AUTH_USER": "anubis",
        "N8N_BASIC_AUTH_PASSWORD": "anubis2025",
        "N8N_HOST": "0.0.0.0",
        "N8N_PORT": "5678",
        "N8N_PROTOCOL": "http",
    }

    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  ✅ {key} = {value}")


def start_anubis_api():
    """تشغيل خادم Anubis API"""
    print("🚀 تشغيل خادم Anubis API...")

    api_script = project_root / "api" / "anubis_api_server.py"

    if not api_script.exists():
        print(f"  ❌ ملف API غير موجود: {api_script}")
        return None

    try:
        process = subprocess.Popen(
            [sys.executable, str(api_script)],
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # انتظار قصير للتأكد من بدء التشغيل
        time.sleep(3)

        if process.poll() is None:
            print("  ✅ خادم Anubis API يعمل على http://localhost:8000")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ فشل تشغيل API: {stderr}")
            return None

    except Exception as e:
        print(f"  ❌ خطأ في تشغيل API: {e}")
        return None


def start_n8n():
    """تشغيل n8n"""
    print("🔗 تشغيل n8n...")

    try:
        process = subprocess.Popen(
            ["n8n", "start"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        # انتظار لبدء التشغيل
        time.sleep(5)

        if process.poll() is None:
            print("  ✅ n8n يعمل على http://localhost:5678")
            print("  👤 المستخدم: anubis")
            print("  🔑 كلمة المرور: anubis2025")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ فشل تشغيل n8n: {stderr}")
            return None

    except Exception as e:
        print(f"  ❌ خطأ في تشغيل n8n: {e}")
        return None


def test_integration():
    """اختبار التكامل"""
    print("🧪 اختبار التكامل...")

    import requests

    tests = [
        {
            "name": "Anubis API Health",
            "url": "http://localhost:8000/health",
            "method": "GET",
        },
        {"name": "n8n Health", "url": "http://localhost:5678/healthz", "method": "GET"},
        {
            "name": "Ollama Test",
            "url": "http://localhost:8000/api/v1/models/ollama/generate",
            "method": "POST",
            "headers": {"X-API-Key": "anubis-api-key-2025"},
            "data": {
                "prompt": "Test prompt",
                "model": "phi3:mini",
                "temperature": 0.7,
                "max_tokens": 100,
            },
        },
    ]

    results = {}
    for test in tests:
        try:
            if test["method"] == "GET":
                response = requests.get(test["url"], timeout=10)
            else:
                response = requests.post(
                    test["url"],
                    json=test.get("data"),
                    headers=test.get("headers", {}),
                    timeout=30,
                )

            if response.status_code == 200:
                results[test["name"]] = "✅ نجح"
                print(f"  ✅ {test['name']}: نجح")
            else:
                results[test["name"]] = f"❌ فشل ({response.status_code})"
                print(f"  ❌ {test['name']}: فشل ({response.status_code})")

        except Exception as e:
            results[test["name"]] = f"❌ خطأ: {e}"
            print(f"  ❌ {test['name']}: خطأ - {e}")

    return results


def create_startup_report():
    """إنشاء تقرير بدء التشغيل"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "system": "Anubis n8n Integration",
        "version": "1.0.0",
        "status": "started",
        "services": {
            "anubis_api": "http://localhost:8000",
            "n8n": "http://localhost:5678",
            "langsmith": "https://smith.langchain.com/",
            "docs": "http://localhost:8000/docs",
        },
        "credentials": {
            "n8n_user": "anubis",
            "n8n_password": "anubis2025",
            "api_key": "anubis-api-key-2025",
        },
    }

    report_file = (
        project_root / "reports" / f"startup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    report_file.parent.mkdir(exist_ok=True)

    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"📊 تقرير بدء التشغيل محفوظ في: {report_file}")
    return report


def main():
    """الدالة الرئيسية"""
    print_banner()

    # فحص المتطلبات
    requirements = check_requirements()

    # تثبيت n8n إذا لزم الأمر
    if not install_n8n():
        print("❌ فشل في تثبيت n8n. توقف التشغيل.")
        return

    # إعداد البيئة
    setup_environment()

    # تشغيل الخدمات
    print("\n🚀 تشغيل الخدمات...")

    api_process = start_anubis_api()
    if not api_process:
        print("❌ فشل في تشغيل Anubis API")
        return

    n8n_process = start_n8n()
    if not n8n_process:
        print("❌ فشل في تشغيل n8n")
        if api_process:
            api_process.terminate()
        return

    # اختبار التكامل
    print("\n🧪 اختبار التكامل...")
    time.sleep(5)  # انتظار لاستقرار الخدمات
    test_results = test_integration()

    # إنشاء التقرير
    report = create_startup_report()

    # عرض معلومات النظام
    print("\n" + "=" * 60)
    print("🎉 نظام أنوبيس جاهز للاستخدام!")
    print("=" * 60)
    print("🌐 الخدمات المتاحة:")
    print("  • Anubis API: http://localhost:8000")
    print("  • API Docs: http://localhost:8000/docs")
    print("  • n8n Interface: http://localhost:5678")
    print("  • LangSmith: https://smith.langchain.com/")
    print("\n🔑 بيانات الدخول:")
    print("  • n8n المستخدم: anubis")
    print("  • n8n كلمة المرور: anubis2025")
    print("  • API Key: anubis-api-key-2025")
    print("\n📚 الاستخدام:")
    print("  1. افتح n8n في المتصفح")
    print("  2. استورد workflow من n8n/workflows/")
    print("  3. أضف credentials للـ Anubis API")
    print("  4. شغل الـ workflow")
    print("=" * 60)

    try:
        print("\n⌨️ اضغط Ctrl+C للإيقاف...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        if api_process:
            api_process.terminate()
        if n8n_process:
            n8n_process.terminate()
        print("✅ تم إيقاف جميع الخدمات")


if __name__ == "__main__":
    main()
