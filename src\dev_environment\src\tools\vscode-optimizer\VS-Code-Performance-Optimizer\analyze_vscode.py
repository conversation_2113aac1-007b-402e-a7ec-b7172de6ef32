#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل مفصل لعمليات VS Code
"""

import psutil
import time
from datetime import datetime

def analyze_vscode_processes():
    """تحليل شامل لعمليات VS Code"""
    
    print('🔍 تحليل عمليات VS Code المفصل...')
    print('=' * 60)
    
    vscode_processes = []
    total_cpu = 0
    total_memory = 0
    
    # جمع جميع عمليات VS Code
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'cmdline', 'create_time']):
        try:
            if 'code' in proc.info['name'].lower():
                # الحصول على استهلاك المعالج الحالي
                cpu_usage = proc.cpu_percent(interval=0.5)
                memory_usage = proc.memory_percent()
                
                proc_info = {
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cpu_percent': cpu_usage,
                    'memory_percent': memory_usage,
                    'cmdline': proc.info.get('cmdline', []),
                    'create_time': proc.info['create_time']
                }
                
                vscode_processes.append(proc_info)
                total_cpu += cpu_usage
                total_memory += memory_usage
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
        except Exception:
            continue
    
    print(f'📊 إجمالي عمليات VS Code: {len(vscode_processes)}')
    print(f'⚡ إجمالي استهلاك المعالج: {total_cpu:.1f}%')
    print(f'💾 إجمالي استهلاك الذاكرة: {total_memory:.1f}%')
    print()
    
    if not vscode_processes:
        print('❌ لم يتم العثور على عمليات VS Code نشطة')
        return
    
    # ترتيب العمليات حسب استهلاك المعالج
    vscode_processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
    
    print('🔴 العمليات الأكثر استهلاكاً للمعالج:')
    print('-' * 80)
    print(f"{'PID':<8} {'الاسم':<20} {'المعالج':<10} {'الذاكرة':<10} {'الوصف':<30}")
    print('-' * 80)
    
    for i, proc in enumerate(vscode_processes[:10], 1):
        # تحديد نوع العملية من command line
        cmdline = ' '.join(proc['cmdline']) if proc['cmdline'] else ''
        
        if '--type=renderer' in cmdline:
            desc = '🖼️ عارض الواجهة'
        elif '--type=extensionHost' in cmdline:
            desc = '🔌 مضيف الإضافات'
        elif '--type=utility' in cmdline:
            desc = '🛠️ أدوات مساعدة'
        elif '--type=gpu-process' in cmdline:
            desc = '🎮 معالج الرسوميات'
        elif 'electron' in proc['name'].lower():
            desc = '⚡ العملية الرئيسية'
        elif '--inspect' in cmdline:
            desc = '🔍 مصحح الأخطاء'
        else:
            desc = '📝 عملية VS Code'
        
        status = '🔴' if proc['cpu_percent'] > 10 else '🟡' if proc['cpu_percent'] > 5 else '🟢'
        
        pid_str = str(proc['pid'])
        name_str = proc['name']
        cpu_str = f"{proc['cpu_percent']:.1f}%"
        mem_str = f"{proc['memory_percent']:.1f}%"
        desc_str = f"{status} {desc}"
        
        print(f"{pid_str:<8} {name_str:<20} {cpu_str:<10} {mem_str:<10} {desc_str}")
    
    print()
    print('🔍 تحليل مفصل للعمليات المشبوهة:')
    print('-' * 50)
    
    high_cpu_procs = [p for p in vscode_processes if p['cpu_percent'] > 15]
    high_memory_procs = [p for p in vscode_processes if p['memory_percent'] > 5]
    
    if high_cpu_procs:
        print('⚡ عمليات عالية استهلاك المعالج (>15%):')
        for proc in high_cpu_procs:
            pid = proc['pid']
            name = proc['name']
            cpu = proc['cpu_percent']
            print(f'  • PID {pid}: {name} - {cpu:.1f}%')
    
    if high_memory_procs:
        print('💾 عمليات عالية استهلاك الذاكرة (>5%):')
        for proc in high_memory_procs:
            pid = proc['pid']
            name = proc['name']
            mem = proc['memory_percent']
            print(f'  • PID {pid}: {name} - {mem:.1f}%')
    
    print()
    print('💡 التوصيات:')
    if total_cpu > 50:
        print('🔴 استهلاك المعالج مرتفع جداً! يُنصح بإعادة تشغيل VS Code')
    elif total_cpu > 25:
        print('🟡 استهلاك المعالج مرتفع - تحقق من الإضافات النشطة')
    
    if total_memory > 30:
        print('🔴 استهلاك الذاكرة مرتفع جداً! أغلق بعض النوافذ أو الملفات')
    elif total_memory > 15:
        print('🟡 استهلاك الذاكرة مرتفع - أغلق الملفات غير المستخدمة')
    
    if len(vscode_processes) > 15:
        print('⚠️ عدد كبير من العمليات - قد تحتاج لإعادة تشغيل VS Code')
    
    # تحليل إضافي للإضافات
    extension_processes = [p for p in vscode_processes if '--type=extensionHost' in ' '.join(p.get('cmdline', []))]
    if extension_processes:
        print()
        print('🔌 تحليل مضيف الإضافات:')
        total_ext_cpu = sum(p['cpu_percent'] for p in extension_processes)
        total_ext_memory = sum(p['memory_percent'] for p in extension_processes)
        print(f'  • عدد عمليات الإضافات: {len(extension_processes)}')
        print(f'  • استهلاك المعالج: {total_ext_cpu:.1f}%')
        print(f'  • استهلاك الذاكرة: {total_ext_memory:.1f}%')
        
        if total_ext_cpu > 20:
            print('  🔴 الإضافات تستهلك موارد كثيرة - فكر في تعطيل بعضها')
    
    print()
    print('✅ انتهى التحليل')
    
    return {
        'total_processes': len(vscode_processes),
        'total_cpu': total_cpu,
        'total_memory': total_memory,
        'high_cpu_processes': high_cpu_procs,
        'high_memory_processes': high_memory_procs
    }

if __name__ == "__main__":
    try:
        result = analyze_vscode_processes()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التحليل")
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")
