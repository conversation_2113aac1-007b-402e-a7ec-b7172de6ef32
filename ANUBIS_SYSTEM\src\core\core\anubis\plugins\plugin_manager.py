#!/usr/bin/env python3
"""
🔌 مدير الإضافات لنظام المساعدين الذكيين العالمي
Plugin Manager for Universal AI Assistants
"""

import importlib
import importlib.util
from pathlib import Path
from typing import Any, Dict, List, Optional

from .base_plugin import BasePlugin


class PluginManager:
    """مدير الإضافات المسؤول عن تحميل وإدارة الإضافات"""

    def __init__(self, plugin_dir: str = "plugins", config: Dict[str, Any] = None):
        """
        تهيئة مدير الإضافات

        Args:
            plugin_dir: مجلد الإضافات
            config: إعدادات الإضافات
        """
        self.plugin_dir = Path(plugin_dir)
        self.config = config or {}
        self.plugins = {}
        self.enabled_plugins = {}

        # إعدادات المدير
        self.auto_load = self.config.get("auto_load", True)
        self.auto_enable = self.config.get("auto_enable", True)

        # تحميل الإضافات تلقائياً
        if self.auto_load:
            self.load_all_plugins()

    def load_all_plugins(self):
        """تحميل جميع الإضافات من المجلد"""
        if not self.plugin_dir.exists():
            print(f"⚠️ مجلد الإضافات غير موجود: {self.plugin_dir}")
            return

        # البحث عن ملفات الإضافات
        plugin_files = list(self.plugin_dir.glob("*_plugin.py"))

        for plugin_file in plugin_files:
            try:
                self.load_plugin(plugin_file)
            except Exception as e:
                print(f"❌ خطأ في تحميل الإضافة {plugin_file}: {e}")

    def load_plugin(self, plugin_file: Path) -> bool:
        """
        تحميل إضافة واحدة

        Args:
            plugin_file: مسار ملف الإضافة

        Returns:
            True إذا تم التحميل بنجاح
        """
        try:
            # تحميل الوحدة
            spec = importlib.util.spec_from_file_location(plugin_file.stem, plugin_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # البحث عن فئات الإضافات
            for attr_name in dir(module):
                attr = getattr(module, attr_name)

                if isinstance(attr, type) and issubclass(attr, BasePlugin) and attr != BasePlugin:

                    # إنشاء مثيل من الإضافة
                    plugin_config = self.config.get(attr_name.lower(), {})
                    plugin_instance = attr(plugin_config)

                    # تسجيل الإضافة
                    self.plugins[attr_name] = plugin_instance

                    # تفعيل الإضافة إذا كان مطلوباً
                    if self.auto_enable and plugin_instance.is_initialized:
                        self.enabled_plugins[attr_name] = plugin_instance

                    print(f"✅ تم تحميل الإضافة: {attr_name}")
                    return True

            print(f"⚠️ لم يتم العثور على فئة إضافة صالحة في {plugin_file}")
            return False

        except Exception as e:
            print(f"❌ خطأ في تحميل الإضافة {plugin_file}: {e}")
            return False

    def enable_plugin(self, plugin_name: str) -> bool:
        """
        تفعيل إضافة

        Args:
            plugin_name: اسم الإضافة

        Returns:
            True إذا تم التفعيل بنجاح
        """
        if plugin_name not in self.plugins:
            print(f"❌ الإضافة {plugin_name} غير موجودة")
            return False

        plugin = self.plugins[plugin_name]
        plugin.enable()

        if plugin.is_enabled and plugin.is_initialized:
            self.enabled_plugins[plugin_name] = plugin
            print(f"✅ تم تفعيل الإضافة: {plugin_name}")
            return True
        else:
            print(f"❌ فشل في تفعيل الإضافة: {plugin_name}")
            return False

    def disable_plugin(self, plugin_name: str) -> bool:
        """
        إلغاء تفعيل إضافة

        Args:
            plugin_name: اسم الإضافة

        Returns:
            True إذا تم إلغاء التفعيل بنجاح
        """
        if plugin_name not in self.plugins:
            print(f"❌ الإضافة {plugin_name} غير موجودة")
            return False

        plugin = self.plugins[plugin_name]
        plugin.disable()

        if plugin_name in self.enabled_plugins:
            del self.enabled_plugins[plugin_name]

        print(f"✅ تم إلغاء تفعيل الإضافة: {plugin_name}")
        return True

    def run_plugin(self, plugin_name: str, *args, **kwargs) -> Dict[str, Any]:
        """
        تشغيل إضافة محددة

        Args:
            plugin_name: اسم الإضافة
            *args, **kwargs: معاملات التشغيل

        Returns:
            نتيجة تشغيل الإضافة
        """
        if plugin_name not in self.enabled_plugins:
            return {
                "success": False,
                "error": f"الإضافة {plugin_name} غير مفعلة أو غير موجودة",
            }

        plugin = self.enabled_plugins[plugin_name]
        return plugin.run(*args, **kwargs)

    def run_all_plugins(self, *args, **kwargs) -> Dict[str, Any]:
        """
        تشغيل جميع الإضافات المفعلة

        Args:
            *args, **kwargs: معاملات التشغيل

        Returns:
            نتائج تشغيل جميع الإضافات
        """
        results = {}

        for plugin_name, plugin in self.enabled_plugins.items():
            try:
                result = plugin.run(*args, **kwargs)
                results[plugin_name] = result
            except Exception as e:
                results[plugin_name] = {"success": False, "error": str(e)}

        return results

    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات إضافة

        Args:
            plugin_name: اسم الإضافة

        Returns:
            معلومات الإضافة أو None
        """
        if plugin_name not in self.plugins:
            return None

        plugin = self.plugins[plugin_name]
        info = plugin.get_plugin_info()
        info.update(plugin.get_status())

        return info

    def list_plugins(self) -> Dict[str, Any]:
        """
        قائمة بجميع الإضافات

        Returns:
            معلومات جميع الإضافات
        """
        return {
            "total_plugins": len(self.plugins),
            "enabled_plugins": len(self.enabled_plugins),
            "plugins": {name: self.get_plugin_info(name) for name in self.plugins.keys()},
        }

    def get_status(self) -> Dict[str, Any]:
        """إرجاع حالة مدير الإضافات"""
        return {
            "plugin_dir": str(self.plugin_dir),
            "total_plugins": len(self.plugins),
            "enabled_plugins": len(self.enabled_plugins),
            "auto_load": self.auto_load,
            "auto_enable": self.auto_enable,
            "plugin_names": list(self.plugins.keys()),
            "enabled_plugin_names": list(self.enabled_plugins.keys()),
        }
