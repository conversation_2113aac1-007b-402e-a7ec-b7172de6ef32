#!/usr/bin/env python3
"""
Process Monitor Script for VS Code and System Processes
مراقب العمليات لـ VS Code والعمليات النظام

This script monitors and checks all processes shown in the Task Manager,
specifically focusing on VS Code processes and system performance.
"""

import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import psutil


class ProcessMonitor:
    def __init__(self):
        self.setup_logging()
        self.processes_data = []
        self.vscode_processes = []
        self.system_stats = {}

    def setup_logging(self):
        """إعداد نظام التسجيل"""
        log_dir = "Universal-AI-Assistants/logs"
        os.makedirs(log_dir, exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(f"{log_dir}/process_monitor.log"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def get_system_stats(self) -> Dict:
        """الحصول على إحصائيات النظام العامة"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            return {
                "timestamp": datetime.now().isoformat(),
                "cpu_usage": cpu_percent,
                "memory_total": memory.total,
                "memory_used": memory.used,
                "memory_percent": memory.percent,
                "disk_total": disk.total,
                "disk_used": disk.used,
                "disk_percent": (disk.used / disk.total) * 100,
            }
        except Exception as e:
            self.logger.error(f"Error getting system stats: {e}")
            return {}

    def get_process_info(self, process) -> Optional[Dict]:
        """الحصول على معلومات العملية"""
        try:
            with process.oneshot():
                return {
                    "pid": process.pid,
                    "name": process.name(),
                    "status": process.status(),
                    "cpu_percent": process.cpu_percent(),
                    "memory_percent": process.memory_percent(),
                    "memory_info": process.memory_info()._asdict(),
                    "create_time": process.create_time(),
                    "num_threads": process.num_threads(),
                    "cmdline": " ".join(process.cmdline()) if process.cmdline() else "",
                    "exe": process.exe() if hasattr(process, "exe") else "",
                }
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None

    def find_vscode_processes(self) -> List[Dict]:
        """البحث عن عمليات VS Code"""
        vscode_keywords = [
            "code",
            "Code.exe",
            "electron",
            "extensionHost",
            "Microsoft VS Code",
            "Visual Studio Code",
        ]

        vscode_procs = []

        for proc in psutil.process_iter(["pid", "name", "cmdline"]):
            try:
                proc_info = proc.info
                proc_name = proc_info["name"].lower()
                cmdline = " ".join(proc_info["cmdline"] or []).lower()

                # التحقق من كلمات VS Code المفتاحية
                if any(
                    keyword.lower() in proc_name or keyword.lower() in cmdline
                    for keyword in vscode_keywords
                ):

                    detailed_info = self.get_process_info(proc)
                    if detailed_info:
                        vscode_procs.append(detailed_info)

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return vscode_procs

    def get_all_processes(self) -> List[Dict]:
        """الحصول على جميع العمليات"""
        all_processes = []

        for proc in psutil.process_iter():
            proc_info = self.get_process_info(proc)
            if proc_info:
                all_processes.append(proc_info)

        return sorted(all_processes, key=lambda x: x["cpu_percent"], reverse=True)

    def analyze_processes(self) -> Dict:
        """تحليل العمليات"""
        analysis = {
            "total_processes": 0,
            "vscode_processes": 0,
            "high_cpu_processes": [],
            "high_memory_processes": [],
            "suspicious_processes": [],
        }

        for proc in self.processes_data:
            analysis["total_processes"] += 1

            # عمليات استهلاك CPU عالي
            if proc["cpu_percent"] > 10:
                analysis["high_cpu_processes"].append(
                    {
                        "name": proc["name"],
                        "pid": proc["pid"],
                        "cpu_percent": proc["cpu_percent"],
                    }
                )

            # عمليات استهلاك ذاكرة عالي
            if proc["memory_percent"] > 5:
                analysis["high_memory_processes"].append(
                    {
                        "name": proc["name"],
                        "pid": proc["pid"],
                        "memory_percent": proc["memory_percent"],
                    }
                )

        analysis["vscode_processes"] = len(self.vscode_processes)
        return analysis

    def generate_report(self) -> Dict:
        """إنشاء تقرير شامل"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_stats": self.system_stats,
            "process_analysis": self.analyze_processes(),
            "vscode_processes": self.vscode_processes,
            "top_processes": self.processes_data[:20],  # أعلى 20 عملية
            "recommendations": self.get_recommendations(),
        }

        return report

    def get_recommendations(self) -> List[str]:
        """الحصول على توصيات التحسين"""
        recommendations = []

        if self.system_stats.get("cpu_usage", 0) > 80:
            recommendations.append("استهلاك CPU عالي - فكر في إغلاق بعض التطبيقات")

        if self.system_stats.get("memory_percent", 0) > 80:
            recommendations.append("استهلاك الذاكرة عالي - أعد تشغيل بعض التطبيقات")

        if len(self.vscode_processes) > 10:
            recommendations.append(
                "عدد كبير من عمليات VS Code - فكر في إغلاق النوافذ غير المستخدمة"
            )

        return recommendations

    def save_report(self, report: Dict, filename: str = None):
        """حفظ التقرير"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"Universal-AI-Assistants/reports/process_monitor_report_{timestamp}.json"

        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Report saved to: {filename}")

    def monitor_continuously(self, interval: int = 60, duration: int = 3600):
        """مراقبة مستمرة للعمليات"""
        self.logger.info(f"Starting continuous monitoring for {duration} seconds...")

        start_time = time.time()

        while time.time() - start_time < duration:
            try:
                self.run_check()
                time.sleep(interval)
            except KeyboardInterrupt:
                self.logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                time.sleep(interval)

    def run_check(self):
        """تشغيل فحص واحد"""
        self.logger.info("Starting process check...")

        # جمع البيانات
        self.system_stats = self.get_system_stats()
        self.processes_data = self.get_all_processes()
        self.vscode_processes = self.find_vscode_processes()

        # إنشاء التقرير
        report = self.generate_report()

        # حفظ التقرير
        self.save_report(report)

        # طباعة ملخص
        self.print_summary(report)

        return report

    def print_summary(self, report: Dict):
        """طباعة ملخص التقرير"""
        print("\n" + "=" * 50)
        print("PROCESS MONITOR SUMMARY")
        print("=" * 50)

        stats = report["system_stats"]
        print(f"CPU Usage: {stats.get('cpu_usage', 0):.1f}%")
        print(f"Memory Usage: {stats.get('memory_percent', 0):.1f}%")
        print(f"Disk Usage: {stats.get('disk_percent', 0):.1f}%")

        analysis = report["process_analysis"]
        print(f"\nTotal Processes: {analysis['total_processes']}")
        print(f"VS Code Processes: {analysis['vscode_processes']}")
        print(f"High CPU Processes: {len(analysis['high_cpu_processes'])}")
        print(f"High Memory Processes: {len(analysis['high_memory_processes'])}")

        if report["recommendations"]:
            print("\nRecommendations:")
            for rec in report["recommendations"]:
                print(f"- {rec}")

        print("=" * 50)


def main():
    """الدالة الرئيسية"""
    monitor = ProcessMonitor()

    import argparse

    parser = argparse.ArgumentParser(description="Process Monitor for VS Code")
    parser.add_argument("--continuous", "-c", action="store_true", help="Run continuous monitoring")
    parser.add_argument(
        "--interval",
        "-i",
        type=int,
        default=60,
        help="Monitoring interval in seconds (default: 60)",
    )
    parser.add_argument(
        "--duration",
        "-d",
        type=int,
        default=3600,
        help="Monitoring duration in seconds (default: 3600)",
    )

    args = parser.parse_args()

    if args.continuous:
        monitor.monitor_continuously(args.interval, args.duration)
    else:
        monitor.run_check()


if __name__ == "__main__":
    main()
