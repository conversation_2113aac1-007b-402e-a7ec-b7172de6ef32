FROM python:3.11-slim

WORKDIR /app

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# إنشاء مستخدم غير جذر
RUN useradd -m -u 1001 universal && chown -R universal:universal /app
USER universal

# تعريف المنفذ
EXPOSE 8001

# أمر التشغيل
CMD ["python", "main.py"]
