#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 منظم الملفات الخارجية لمشروع أنوبيس
Anubis External Files Organizer

نقل الملفات الخارجية إلى مجلداتها المناسبة
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class ExternalFilesOrganizer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.moves_log = []
        
    def log_move(self, source, destination, action="نقل"):
        """تسجيل عمليات النقل"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "source": str(source),
            "destination": str(destination)
        }
        self.moves_log.append(entry)
        print(f"✅ {action}: {source.name} -> {destination}")
    
    def move_file_safely(self, source_path, destination_dir, new_name=None):
        """نقل ملف بأمان مع تجنب الكتابة فوق الملفات الموجودة"""
        if not source_path.exists():
            print(f"❌ الملف غير موجود: {source_path}")
            return None
            
        # إنشاء المجلد الوجهة إذا لم يكن موجوداً
        destination_dir.mkdir(parents=True, exist_ok=True)
        
        # تحديد اسم الملف الجديد
        file_name = new_name if new_name else source_path.name
        destination_path = destination_dir / file_name
        
        # إذا كان الملف موجود، أضف timestamp
        if destination_path.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_parts = file_name.split('.')
            if len(name_parts) > 1:
                new_file_name = f"{'.'.join(name_parts[:-1])}_{timestamp}.{name_parts[-1]}"
            else:
                new_file_name = f"{file_name}_{timestamp}"
            destination_path = destination_dir / new_file_name
        
        try:
            shutil.move(str(source_path), str(destination_path))
            self.log_move(source_path, destination_path)
            return destination_path
        except Exception as e:
            print(f"❌ خطأ في نقل {source_path}: {e}")
            return None
    
    def organize_documentation_files(self):
        """تنظيم ملفات الوثائق"""
        print("📚 تنظيم ملفات الوثائق...")
        
        docs_files = [
            "ANUBIS_AI_MODELS_AND_APPLICATIONS_DETAILED_GUIDE.md",
            "ANUBIS_CLINE_COMPREHENSIVE_ANALYSIS_REPORT.md", 
            "ANUBIS_SERVICES_COMPREHENSIVE_GUIDE.md",
            "ANUBIS_TRANSFORMATION_COMPLETE.md",
            "anubis_organization_completion_report.md",
            "anubis_project_analysis_task_distribution.md",
            "anubis_project_organization_plan.md"
        ]
        
        docs_dir = self.project_root / "docs" / "guides"
        
        for file_name in docs_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, docs_dir)
    
    def organize_gemini_requests(self):
        """تنظيم طلبات Gemini"""
        print("🤖 تنظيم طلبات Gemini...")
        
        gemini_files = [
            "anubis_gemini_docker_help_request.md",
            "anubis_gemini_request_20250720_072632.md", 
            "anubis_project_organization_gemini_request.md",
            "gemini_coordination_request_anubis_task_20250720_101017.md",
            "gemini_project_analysis_request.md"
        ]
        
        gemini_dir = self.project_root / "docs" / "gemini_requests"
        
        for file_name in gemini_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, gemini_dir)
    
    def organize_config_files(self):
        """تنظيم ملفات التكوين"""
        print("⚙️ تنظيم ملفات التكوين...")
        
        config_files = [
            "anubis_ai_team_collaboration_plan.json",
            "anubis_navigation_shortcuts.json",
            "anubis_project_paths_config.json",
            "migration_plan.json"
        ]
        
        config_dir = self.project_root / "config"
        
        for file_name in config_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, config_dir)
    
    def organize_scripts(self):
        """تنظيم ملفات السكريبت"""
        print("📜 تنظيم ملفات السكريبت...")
        
        script_files = [
            "create_new_structure.py",
            "execute_migration.py", 
            "move_isolation_files.py",
            "start_team_analysis.py"
        ]
        
        scripts_dir = self.project_root / "scripts" / "organizers"
        
        for file_name in script_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, scripts_dir)
    
    def organize_logs_and_reports(self):
        """تنظيم ملفات السجلات والتقارير"""
        print("📊 تنظيم ملفات السجلات والتقارير...")
        
        log_files = [
            "migration_log.json",
            "cline_task_jul-20-2025_7-13-06-am.md"
        ]
        
        reports_dir = self.project_root / "reports_and_analysis" / "reports"
        
        for file_name in log_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, reports_dir)
    
    def organize_shell_scripts(self):
        """تنظيم ملفات Shell Scripts"""
        print("🐚 تنظيم ملفات Shell Scripts...")
        
        shell_files = [
            "start_anubis_isolated.sh",
            "setup_gemini.bat"
        ]
        
        scripts_dir = self.project_root / "scripts"
        
        for file_name in shell_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.move_file_safely(file_path, scripts_dir)
    
    def clean_empty_files(self):
        """تنظيف الملفات الفارغة"""
        print("🧹 تنظيف الملفات الفارغة...")
        
        # البحث عن ملفات Python فارغة أو تحتوي على سطر واحد فقط
        empty_files = []
        
        for file_path in self.project_root.glob("*.py"):
            if file_path.name in ["main.py", "setup.py"]:
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if len(content) <= 10:  # ملف فارغ أو يحتوي على محتوى قليل جداً
                        empty_files.append(file_path)
            except Exception:
                continue
        
        # نقل الملفات الفارغة إلى الأرشيف
        if empty_files:
            empty_archive = self.project_root / "archive_and_backups" / "cleanup_archive" / "empty_files"
            for file_path in empty_files:
                self.move_file_safely(file_path, empty_archive)
    
    def run_organization(self):
        """تشغيل التنظيم الكامل"""
        print("🏺 بدء تنظيم الملفات الخارجية لمشروع أنوبيس")
        print("=" * 60)
        
        # تنظيم الملفات حسب النوع
        self.organize_documentation_files()
        self.organize_gemini_requests()
        self.organize_config_files()
        self.organize_scripts()
        self.organize_logs_and_reports()
        self.organize_shell_scripts()
        self.clean_empty_files()
        
        print(f"\n🎉 تم الانتهاء من التنظيم!")
        print(f"📊 إجمالي الملفات المنقولة: {len(self.moves_log)}")
        
        # حفظ سجل العمليات
        log_file = self.project_root / "archive_and_backups" / "cleanup_archive" / f"organization_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        import json
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.moves_log, f, ensure_ascii=False, indent=2)
        
        print(f"📝 تم حفظ سجل العمليات في: {log_file}")
        
        return len(self.moves_log)

if __name__ == "__main__":
    organizer = ExternalFilesOrganizer()
    organizer.run_organization()
