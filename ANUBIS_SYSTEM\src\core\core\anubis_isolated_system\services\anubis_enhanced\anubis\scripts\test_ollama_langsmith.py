#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 اختبار تكامل نماذج Ollama مع LangSmith
Test Ollama Models Integration with LangSmith
"""

import os
import sys

sys.path.append(".")

# تعيين متغيرات البيئة
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "anubis-ai-system"

import time

from langsmith import traceable

from anubis.core.ai_integration import OllamaProvider

print("🔗 اختبار تكامل نماذج Ollama مع LangSmith")
print("=" * 50)


@traceable(name="anubis_ollama_test")
def test_ollama_with_langsmith(model_name, prompt):
    """اختبار نموذج Ollama مع تتبع LangSmith"""
    provider = OllamaProvider(model_name)
    start_time = time.time()
    response = provider.generate_response(prompt)
    end_time = time.time()

    return {
        "model": model_name,
        "prompt": prompt,
        "response": response[:100] + "..." if len(response) > 100 else response,
        "response_time": end_time - start_time,
        "status": "success",
    }


def main():
    """الدالة الرئيسية"""
    # اختبار النماذج مع التتبع
    models = ["llama3:8b", "mistral:7b", "phi3:mini"]
    prompt = "اشرح الذكاء الاصطناعي في جملة واحدة"

    results = []

    for model in models:
        print(f"\n🤖 اختبار {model} مع LangSmith...")
        try:
            result = test_ollama_with_langsmith(model, prompt)
            print(f"   ✅ تم التتبع بنجاح")
            print(f'   ⏱️ الوقت: {result["response_time"]:.2f} ثانية')
            print(f'   📝 الاستجابة: {result["response"]}')
            results.append(result)
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
            results.append({"model": model, "status": "error", "error": str(e)})

    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    successful = len([r for r in results if r.get("status") == "success"])
    total = len(results)
    print(f"✅ النماذج الناجحة: {successful}/{total}")

    if successful > 0:
        avg_time = (
            sum(r["response_time"] for r in results if r.get("status") == "success") / successful
        )
        print(f"⏱️ متوسط وقت الاستجابة: {avg_time:.2f} ثانية")

    print("\n🌐 يمكنك مراقبة النتائج في: https://smith.langchain.com/")
    print("📊 المشروع: anubis-ai-system")
    print("\n🏺 انتهى اختبار التكامل!")

    return results


if __name__ == "__main__":
    main()
