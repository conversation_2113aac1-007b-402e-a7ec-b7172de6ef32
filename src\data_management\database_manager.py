#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير قاعدة البيانات المحسن لنظام أنوبيس
Anubis Enhanced Database Manager

يدعم MySQL و SQLite مع إدارة الاتصالات والنسخ الاحتياطية
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

import mysql.connector
from mysql.connector import pooling
import sqlite3
import aiosqlite
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

class DatabaseManager:
    """مدير قاعدة البيانات الموحد"""
    
    def __init__(self, config_path: str = "config/database_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.mysql_pool = None
        self.sqlite_connection = None
        self.engines = {}
        self.logger = self._setup_logging()
        
    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            # إعدادات افتراضية
            return {
                "database": {
                    "type": "mysql",
                    "mysql": {
                        "host": "localhost",
                        "port": 3306,
                        "user": "root", 
                        "password": "2452329511",
                        "database": "anubis_system",
                        "charset": "utf8mb4"
                    },
                    "sqlite": {
                        "db_path": "data/anubis.db"
                    }
                }
            }
    
    def _setup_logging(self) -> logging.Logger:
        """إعداد نظام السجلات"""
        logger = logging.getLogger("anubis_database")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def initialize_mysql(self) -> bool:
        """تهيئة اتصال MySQL"""
        try:
            mysql_config = self.config["database"]["mysql"]
            
            # إنشاء pool الاتصالات
            pool_config = {
                'pool_name': 'anubis_pool',
                'pool_size': 10,
                'pool_reset_session': True,
                'host': mysql_config["host"],
                'port': mysql_config["port"],
                'user': mysql_config["user"],
                'password': mysql_config["password"],
                'database': mysql_config["database"],
                'charset': mysql_config.get("charset", "utf8mb4"),
                'autocommit': True,
                'time_zone': '+00:00'
            }
            
            self.mysql_pool = pooling.MySQLConnectionPool(**pool_config)
            
            # إنشاء SQLAlchemy engine
            mysql_url = (
                f"mysql+mysqlconnector://{mysql_config['user']}:"
                f"{mysql_config['password']}@{mysql_config['host']}:"
                f"{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config.get('charset', 'utf8mb4')}"
            )
            
            self.engines['mysql'] = create_engine(
                mysql_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600,
                echo=False
            )
            
            # اختبار الاتصال
            await self._test_mysql_connection()
            
            self.logger.info("✅ تم تهيئة MySQL بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة MySQL: {e}")
            return False
    
    async def _test_mysql_connection(self):
        """اختبار اتصال MySQL"""
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            if result[0] == 1:
                self.logger.info("🔗 اختبار اتصال MySQL نجح")
            else:
                raise Exception("فشل في اختبار الاتصال")
                
        except Exception as e:
            raise Exception(f"فشل اختبار اتصال MySQL: {e}")
    
    async def initialize_sqlite(self) -> bool:
        """تهيئة اتصال SQLite"""
        try:
            sqlite_config = self.config["database"]["sqlite"]
            db_path = Path(sqlite_config["db_path"])
            
            # إنشاء المجلد إذا لم يكن موجوداً
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # إنشاء SQLAlchemy engine
            sqlite_url = f"sqlite:///{db_path}"
            self.engines['sqlite'] = create_engine(
                sqlite_url,
                echo=False,
                pool_timeout=20,
                pool_recycle=-1
            )
            
            # اختبار الاتصال
            await self._test_sqlite_connection(str(db_path))
            
            self.logger.info("✅ تم تهيئة SQLite بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة SQLite: {e}")
            return False
    
    async def _test_sqlite_connection(self, db_path: str):
        """اختبار اتصال SQLite"""
        try:
            async with aiosqlite.connect(db_path) as db:
                async with db.execute("SELECT 1") as cursor:
                    result = await cursor.fetchone()
                    if result[0] == 1:
                        self.logger.info("🔗 اختبار اتصال SQLite نجح")
                    else:
                        raise Exception("فشل في اختبار الاتصال")
                        
        except Exception as e:
            raise Exception(f"فشل اختبار اتصال SQLite: {e}")
    
    async def initialize_all(self) -> Dict[str, bool]:
        """تهيئة جميع قواعد البيانات"""
        results = {}
        
        # تهيئة MySQL
        results['mysql'] = await self.initialize_mysql()
        
        # تهيئة SQLite
        results['sqlite'] = await self.initialize_sqlite()
        
        self.logger.info(f"📊 نتائج التهيئة: {results}")
        return results
    
    @asynccontextmanager
    async def get_mysql_connection(self):
        """الحصول على اتصال MySQL من pool"""
        connection = None
        try:
            if not self.mysql_pool:
                raise Exception("MySQL pool غير مهيأ")
                
            connection = self.mysql_pool.get_connection()
            yield connection
            
        except Exception as e:
            self.logger.error(f"خطأ في اتصال MySQL: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    @asynccontextmanager
    async def get_sqlite_connection(self):
        """الحصول على اتصال SQLite"""
        db_path = self.config["database"]["sqlite"]["db_path"]
        
        try:
            async with aiosqlite.connect(db_path) as connection:
                yield connection
        except Exception as e:
            self.logger.error(f"خطأ في اتصال SQLite: {e}")
            raise
    
    async def execute_query(self, query: str, params: tuple = None, 
                          database: str = "mysql") -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            if database == "mysql":
                async with self.get_mysql_connection() as conn:
                    cursor = conn.cursor(dictionary=True)
                    cursor.execute(query, params or ())
                    results = cursor.fetchall()
                    cursor.close()
                    return results
                    
            elif database == "sqlite":
                async with self.get_sqlite_connection() as conn:
                    conn.row_factory = aiosqlite.Row
                    async with conn.execute(query, params or ()) as cursor:
                        rows = await cursor.fetchall()
                        return [dict(row) for row in rows]
                        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    async def execute_non_query(self, query: str, params: tuple = None,
                               database: str = "mysql") -> int:
        """تنفيذ استعلام بدون إرجاع نتائج (INSERT, UPDATE, DELETE)"""
        try:
            if database == "mysql":
                async with self.get_mysql_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(query, params or ())
                    affected_rows = cursor.rowcount
                    conn.commit()
                    cursor.close()
                    return affected_rows
                    
            elif database == "sqlite":
                async with self.get_sqlite_connection() as conn:
                    await conn.execute(query, params or ())
                    await conn.commit()
                    return conn.total_changes
                    
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    async def create_tables(self, database: str = "mysql"):
        """إنشاء الجداول الأساسية"""
        tables_sql = {
            "users": """
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """,
            "ai_sessions": """
                CREATE TABLE IF NOT EXISTS ai_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    session_id VARCHAR(255) UNIQUE NOT NULL,
                    model_name VARCHAR(100) NOT NULL,
                    prompt TEXT NOT NULL,
                    response TEXT,
                    tokens_used INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """,
            "system_logs": """
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    level VARCHAR(20) NOT NULL,
                    message TEXT NOT NULL,
                    component VARCHAR(100),
                    user_id INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """
        }
        
        # تعديل SQL للـ SQLite
        if database == "sqlite":
            for table_name, sql in tables_sql.items():
                tables_sql[table_name] = sql.replace("AUTO_INCREMENT", "AUTOINCREMENT")
                tables_sql[table_name] = sql.replace("TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        
        try:
            for table_name, sql in tables_sql.items():
                await self.execute_non_query(sql, database=database)
                self.logger.info(f"✅ تم إنشاء جدول {table_name}")
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            raise
    
    async def get_connection_status(self) -> Dict[str, Any]:
        """الحصول على حالة الاتصالات"""
        status = {
            "mysql": {"connected": False, "pool_size": 0},
            "sqlite": {"connected": False, "file_exists": False}
        }
        
        # فحص MySQL
        try:
            if self.mysql_pool:
                async with self.get_mysql_connection() as conn:
                    if conn.is_connected():
                        status["mysql"]["connected"] = True
                        status["mysql"]["pool_size"] = self.mysql_pool.pool_size
        except:
            pass
        
        # فحص SQLite
        try:
            db_path = Path(self.config["database"]["sqlite"]["db_path"])
            status["sqlite"]["file_exists"] = db_path.exists()
            
            if db_path.exists():
                async with self.get_sqlite_connection() as conn:
                    await conn.execute("SELECT 1")
                    status["sqlite"]["connected"] = True
        except:
            pass
        
        return status
    
    async def close_all_connections(self):
        """إغلاق جميع الاتصالات"""
        try:
            if self.mysql_pool:
                # إغلاق pool connections
                pass  # MySQL pool يدير الإغلاق تلقائياً
                
            self.logger.info("🔒 تم إغلاق جميع اتصالات قاعدة البيانات")
            
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الاتصالات: {e}")

# إنشاء instance عام
db_manager = DatabaseManager()
