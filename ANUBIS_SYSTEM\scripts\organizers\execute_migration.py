#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 تنفيذ نقل الملفات لمشروع أنوبيس
Anubis Project Migration Executor

تنفيذ خطة النقل التدريجي للملفات والمجلدات
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
import subprocess
import sys

class AnubisFileMigrator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.migration_plan_file = self.project_root / "migration_plan.json"
        self.migration_log = []
        
    def load_migration_plan(self):
        """تحميل خطة النقل"""
        if not self.migration_plan_file.exists():
            raise FileNotFoundError("ملف خطة النقل غير موجود!")
        
        with open(self.migration_plan_file, "r", encoding="utf-8") as f:
            return json.load(f)
    
    def safe_move_directory(self, source: Path, destination: Path):
        """نقل آمن للمجلدات مع التحقق"""
        try:
            # التأكد من وجود المجلد المصدر
            if not source.exists():
                return False, f"المجلد المصدر غير موجود: {source}"
            
            # إنشاء المجلد الوجهة إذا لم يكن موجوداً
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # إذا كان المجلد الوجهة موجود، ادمج المحتويات
            if destination.exists():
                self.merge_directories(source, destination)
                shutil.rmtree(source)
            else:
                shutil.move(str(source), str(destination))
            
            return True, f"تم نقل {source} إلى {destination}"
            
        except Exception as e:
            return False, f"خطأ في نقل {source}: {str(e)}"
    
    def merge_directories(self, source: Path, destination: Path):
        """دمج محتويات المجلدات"""
        for item in source.rglob("*"):
            if item.is_file():
                relative_path = item.relative_to(source)
                dest_file = destination / relative_path
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                
                # إذا كان الملف موجود، أضف رقم
                if dest_file.exists():
                    counter = 1
                    while dest_file.exists():
                        name = dest_file.stem + f"_backup_{counter}" + dest_file.suffix
                        dest_file = dest_file.parent / name
                        counter += 1
                
                shutil.copy2(item, dest_file)
    
    def migrate_main_file(self):
        """نقل main.py إلى src/core"""
        main_file = self.project_root / "main.py"
        core_dir = self.project_root / "src" / "core"
        
        if main_file.exists():
            # نسخ main.py إلى src/core
            shutil.copy2(main_file, core_dir / "main.py")
            
            # إنشاء main.py جديد في الجذر يشير للموقع الجديد
            new_main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام أنوبيس - نقطة الدخول الرئيسية
Anubis System - Main Entry Point

هذا الملف يشير إلى الموقع الجديد للنظام الأساسي
"""

import sys
from pathlib import Path

# إضافة مسار src إلى Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# استيراد وتشغيل النظام الأساسي
if __name__ == "__main__":
    from core.main import app
    import uvicorn
    
    print("🏺 بدء تشغيل نظام أنوبيس...")
    print("🚀 النظام جاهز على: http://localhost:8000")
    
    uvicorn.run(
        "core.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
'''
            
            # حفظ main.py الجديد
            with open(main_file, "w", encoding="utf-8") as f:
                f.write(new_main_content)
            
            return True, "تم نقل main.py وإنشاء نقطة دخول جديدة"
        
        return False, "main.py غير موجود"
    
    def execute_single_migration(self, migration_item):
        """تنفيذ نقل واحد"""
        source_path = self.project_root / migration_item["from"]
        dest_path = self.project_root / migration_item["to"]
        
        print(f"📦 نقل: {migration_item['from']} → {migration_item['to']}")
        
        success, message = self.safe_move_directory(source_path, dest_path)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "from": migration_item["from"],
            "to": migration_item["to"],
            "success": success,
            "message": message,
            "files_count": migration_item.get("files_count", 0)
        }
        
        self.migration_log.append(log_entry)
        
        if success:
            print(f"✅ {message}")
            migration_item["status"] = "completed"
        else:
            print(f"❌ {message}")
            migration_item["status"] = "failed"
        
        return success
    
    def execute_all_migrations(self):
        """تنفيذ جميع عمليات النقل"""
        print("🏺 بدء تنفيذ خطة النقل...")
        print("=" * 50)
        
        # تحميل خطة النقل
        migration_plan = self.load_migration_plan()
        
        # إحصائيات
        total_migrations = len(migration_plan["migrations"])
        successful_migrations = 0
        failed_migrations = 0
        
        # تنفيذ كل عملية نقل
        for i, migration in enumerate(migration_plan["migrations"], 1):
            print(f"\n[{i}/{total_migrations}] ", end="")
            
            if self.execute_single_migration(migration):
                successful_migrations += 1
            else:
                failed_migrations += 1
        
        # نقل main.py
        print(f"\n[خاص] نقل main.py...")
        main_success, main_message = self.migrate_main_file()
        if main_success:
            print(f"✅ {main_message}")
        else:
            print(f"❌ {main_message}")
        
        # حفظ خطة النقل المحدثة
        with open(self.migration_plan_file, "w", encoding="utf-8") as f:
            json.dump(migration_plan, f, ensure_ascii=False, indent=2)
        
        # حفظ سجل النقل
        log_file = self.project_root / "migration_log.json"
        with open(log_file, "w", encoding="utf-8") as f:
            json.dump(self.migration_log, f, ensure_ascii=False, indent=2)
        
        # تقرير النتائج
        print("\n" + "=" * 50)
        print("📊 تقرير النقل:")
        print(f"✅ نجح: {successful_migrations}")
        print(f"❌ فشل: {failed_migrations}")
        print(f"📝 سجل مفصل: {log_file}")
        
        if failed_migrations == 0:
            print("🎉 تم تنفيذ جميع عمليات النقل بنجاح!")
            return True
        else:
            print("⚠️ بعض عمليات النقل فشلت. راجع السجل للتفاصيل.")
            return False
    
    def test_new_structure(self):
        """اختبار البنية الجديدة"""
        print("\n🧪 اختبار البنية الجديدة...")
        
        # اختبار تشغيل النظام الأساسي
        try:
            result = subprocess.run([
                sys.executable, "-c", 
                "from src.core.main import app; print('✅ النظام الأساسي يعمل')"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("✅ النظام الأساسي يعمل بشكل صحيح")
                return True
            else:
                print(f"❌ خطأ في النظام الأساسي: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            return False

if __name__ == "__main__":
    migrator = AnubisFileMigrator()
    
    # تنفيذ النقل
    success = migrator.execute_all_migrations()
    
    if success:
        # اختبار البنية الجديدة
        migrator.test_new_structure()
        
        print("\n🏺 تم تنظيم مشروع أنوبيس بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام باستخدام: python main.py")
    else:
        print("\n⚠️ هناك مشاكل في النقل. راجع السجل وأعد المحاولة.")
