#!/usr/bin/env python3
"""
🧪 اختبار قاعدة البيانات لتطبيق إدارة ورشة تصنيع الذهب والماس
Test Jewelry Workshop Database

اختبار إنشاء قاعدة البيانات والعمليات الأساسية
"""

import os
import sys
from datetime import date, datetime
from decimal import Decimal

try:
    from jewelry_database_models import (
        Customer,
        DatabaseManager,
        ExchangeRate,
        GoldPrice,
        Order,
        OrderItem,
        Payment,
    )

    SQLALCHEMY_AVAILABLE = True
except ImportError:
    from jewelry_database_models import SimpleDatabaseManager

    SQLALCHEMY_AVAILABLE = False


def test_database_creation():
    """اختبار إنشاء قاعدة البيانات"""
    print("🧪 اختبار إنشاء قاعدة البيانات...")

    if SQLALCHEMY_AVAILABLE:
        # استخدام SQLAlchemy
        db_manager = DatabaseManager("sqlite:///test_jewelry.db")
        db_manager.create_tables()
        db_manager.init_sample_data()
        print("✅ تم إنشاء قاعدة البيانات باستخدام SQLAlchemy")
        return db_manager
    else:
        # استخدام SQLite المباشر
        db_manager = SimpleDatabaseManager("test_jewelry_simple.db")
        db_manager.create_tables()
        print("✅ تم إنشاء قاعدة البيانات باستخدام SQLite المباشر")
        return db_manager


def test_customer_operations(db_manager):
    """اختبار عمليات العملاء"""
    if not SQLALCHEMY_AVAILABLE:
        print("⚠️ اختبار العملاء يتطلب SQLAlchemy")
        return

    print("🧪 اختبار عمليات العملاء...")

    session = db_manager.get_session()
    try:
        # إضافة عميل جديد
        new_customer = Customer(
            customer_code="CUST002",
            name="محل الماس الفاخر",
            phone="01234567891",
            customer_type="wholesale",
            credit_limit_usd=Decimal("10000.00"),
            credit_limit_egp=Decimal("300000.00"),
        )
        session.add(new_customer)
        session.commit()

        # البحث عن العميل
        found_customer = session.query(Customer).filter_by(customer_code="CUST002").first()
        assert found_customer is not None, "لم يتم العثور على العميل"
        assert found_customer.name == "محل الماس الفاخر", "اسم العميل غير صحيح"

        # عد العملاء
        customers_count = session.query(Customer).count()
        assert customers_count >= 2, f"عدد العملاء غير صحيح: {customers_count}"

        print(f"✅ تم إنشاء العميل: {found_customer.name}")
        print(f"📊 إجمالي العملاء: {customers_count}")

    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في عمليات العملاء: {e}")
        raise
    finally:
        db_manager.close_session(session)


def test_order_creation(db_manager):
    """اختبار إنشاء طلب"""
    if not SQLALCHEMY_AVAILABLE:
        print("⚠️ اختبار الطلبات يتطلب SQLAlchemy")
        return

    print("🧪 اختبار إنشاء طلب...")

    session = db_manager.get_session()
    try:
        # الحصول على عميل موجود
        customer = session.query(Customer).first()
        if not customer:
            print("❌ لا يوجد عملاء في قاعدة البيانات")
            return

        # إنشاء طلب جديد
        new_order = Order(
            order_number="ORD001",
            customer_id=customer.id,
            order_date=date.today(),
            status="pending",
            priority="high",
            total_pieces=10,
            total_gold_weight=Decimal("50.0"),
            total_diamond_weight=Decimal("5.0"),
            total_usd=Decimal("5750.00"),
            total_egp=Decimal("9450.00"),
            notes="طلب 10 خواتم ذهب أبيض مع ماس",
        )
        session.add(new_order)
        session.flush()  # للحصول على ID

        # إضافة تفاصيل القطع
        order_item = OrderItem(
            order_id=new_order.id,
            item_number=1,
            piece_type="خاتم",
            description="خاتم ذهب أبيض عيار 18 مع ماس",
            quantity=10,
            gold_karat=18,
            gold_weight_per_piece=Decimal("5.0"),
            gold_source="ours",
            gold_value_usd=Decimal("175.00"),
            diamond_type="round",
            diamond_size="medium",
            diamond_weight_per_piece=Decimal("0.5"),
            diamond_count_per_piece=50,
            diamond_source="ours",
            diamond_value_usd=Decimal("350.00"),
            manufacturing_cost_usd=Decimal("50.00"),
            stone_setting_cost_egp=Decimal("500.00"),
            has_plating=True,
            plating_type="white",
            plating_cost_egp=Decimal("200.00"),
            has_stamping=True,
            stamping_cost_egp=Decimal("20.00"),
            has_3d_design=True,
            design_complexity="medium",
            design_cost_egp=Decimal("225.00"),
            item_total_usd=Decimal("575.00"),
            item_total_egp=Decimal("945.00"),
            status="pending",
        )
        session.add(order_item)
        session.commit()

        # التحقق من الطلب
        found_order = session.query(Order).filter_by(order_number="ORD001").first()
        assert found_order is not None, "لم يتم العثور على الطلب"
        assert len(found_order.items) == 1, "عدد القطع في الطلب غير صحيح"

        print(f"✅ تم إنشاء الطلب: {found_order.order_number}")
        print(f"📊 العميل: {found_order.customer.name}")
        print(f"💰 الإجمالي: {found_order.total_usd}$ + {found_order.total_egp} ج.م")

        return found_order

    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء الطلب: {e}")
        raise
    finally:
        db_manager.close_session(session)


def test_payment_creation(db_manager, order):
    """اختبار إنشاء دفعة"""
    if not SQLALCHEMY_AVAILABLE or not order:
        print("⚠️ اختبار المدفوعات يتطلب SQLAlchemy وطلب موجود")
        return

    print("🧪 اختبار إنشاء دفعة...")

    session = db_manager.get_session()
    try:
        # إنشاء دفعة جديدة
        new_payment = Payment(
            payment_number="PAY001",
            customer_id=order.customer_id,
            order_id=order.id,
            payment_date=date.today(),
            payment_type="mixed",
            amount_usd=Decimal("2000.00"),
            amount_egp=Decimal("2000.00"),
            gold_karat=18,
            gold_weight=Decimal("20.0"),
            gold_value_usd=Decimal("700.00"),
            total_value_usd=Decimal("2700.00"),
            total_value_egp=Decimal("2000.00"),
            exchange_rate_used=Decimal("30.75"),
            payment_method="نقد + ذهب",
            notes="دفعة أولى من إجمالي الطلب",
        )
        session.add(new_payment)
        session.commit()

        # التحقق من الدفعة
        found_payment = session.query(Payment).filter_by(payment_number="PAY001").first()
        assert found_payment is not None, "لم يتم العثور على الدفعة"

        print(f"✅ تم إنشاء الدفعة: {found_payment.payment_number}")
        print(f"💰 المبلغ: {found_payment.total_value_usd}$ + {found_payment.total_value_egp} ج.م")
        print(f"🏆 الذهب المدفوع: {found_payment.gold_weight} جرام عيار {found_payment.gold_karat}")

    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء الدفعة: {e}")
        raise
    finally:
        db_manager.close_session(session)


def test_database_queries(db_manager):
    """اختبار الاستعلامات"""
    if not SQLALCHEMY_AVAILABLE:
        print("⚠️ اختبار الاستعلامات يتطلب SQLAlchemy")
        return

    print("🧪 اختبار الاستعلامات...")

    session = db_manager.get_session()
    try:
        # إحصائيات العملاء
        total_customers = session.query(Customer).count()
        active_customers = session.query(Customer).filter_by(is_active=True).count()

        # إحصائيات الطلبات
        total_orders = session.query(Order).count()
        pending_orders = session.query(Order).filter_by(status="pending").count()

        # إحصائيات المدفوعات
        total_payments = session.query(Payment).count()
        total_payments_usd = session.query(Payment).with_entities(
            session.query(Payment.total_value_usd).label("sum")
        ).scalar() or Decimal("0.0")

        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   العملاء: {total_customers} (نشط: {active_customers})")
        print(f"   الطلبات: {total_orders} (معلق: {pending_orders})")
        print(f"   المدفوعات: {total_payments} (إجمالي: {total_payments_usd}$)")

        # اختبار العلاقات
        if total_orders > 0:
            order_with_items = session.query(Order).first()
            print(f"   قطع في الطلب الأول: {len(order_with_items.items)}")
            print(f"   مدفوعات الطلب الأول: {len(order_with_items.payments)}")

    except Exception as e:
        print(f"❌ خطأ في الاستعلامات: {e}")
        raise
    finally:
        db_manager.close_session(session)


def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    test_files = ["test_jewelry.db", "test_jewelry_simple.db"]
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ تم حذف ملف الاختبار: {file}")
            except Exception as e:
                print(f"⚠️ لا يمكن حذف {file}: {e}")


def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 ═══════════════════════════════════════════════════════════════")
    print("   اختبار قاعدة البيانات - ورشة تصنيع الذهب والماس")
    print("   Jewelry Workshop Database Tests")
    print("═══════════════════════════════════════════════════════════════ 🧪")

    try:
        # تنظيف ملفات الاختبار السابقة
        cleanup_test_files()

        # اختبار إنشاء قاعدة البيانات
        db_manager = test_database_creation()

        if SQLALCHEMY_AVAILABLE:
            # اختبار العمليات المتقدمة
            test_customer_operations(db_manager)
            order = test_order_creation(db_manager)
            test_payment_creation(db_manager, order)
            test_database_queries(db_manager)
        else:
            print("ℹ️ تم اختبار الوظائف الأساسية فقط (SQLite المباشر)")

        print("\n🎉 جميع اختبارات قاعدة البيانات نجحت!")
        print("✅ قاعدة البيانات جاهزة للاستخدام!")

        # تنظيف ملفات الاختبار
        cleanup_test_files()

    except Exception as e:
        print(f"\n❌ فشل في اختبار قاعدة البيانات: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
