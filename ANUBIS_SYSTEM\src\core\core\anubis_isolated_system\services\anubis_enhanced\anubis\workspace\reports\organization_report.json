{"organization_date": "2025-07-18T16:02:41.557508", "total_actions": 190, "errors_count": 2, "actions": [{"timestamp": "2025-07-18 16:02:41", "action": "إنشاء الهيكل المستهدف", "details": "بدء إنشاء المجلدات الجديدة"}, {"timestamp": "2025-07-18 16:02:41", "action": "إنشاء الهيكل", "details": "تم إنشاء جميع المجلدات المستهدفة"}, {"timestamp": "2025-07-18 16:02:41", "action": "بدء التنظيم", "details": "تطبيق قواعد التنظيم"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "core_files"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "main.py -> anubis\\main.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "main_docs"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "README.md -> anubis\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "INDEX.md -> anubis\\INDEX.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "MAIN_README.md -> anubis\\MAIN_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "ORGANIZED_FILES_README.md -> anubis\\ORGANIZED_FILES_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "PROJECT_README.md -> anubis\\PROJECT_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "core_system"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\core -> anubis\\core.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "core -> anubis\\core"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "agents"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "api"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\api -> anubis\\api.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "api -> anubis\\api"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "database"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\database -> anubis\\database.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "database -> anubis\\database"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "plugins"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\plugins -> anubis\\plugins.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "plugins -> anubis\\plugins"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "configs"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\ai_config.json -> anubis\\configs\\ai_config.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\database_config.json -> anubis\\configs\\database_config.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\default_config.json -> anubis\\configs\\default_config.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\langsmith_config.json -> anubis\\configs\\langsmith_config.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\memory.json -> anubis\\configs\\memory.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\configs\\README.md -> anubis\\configs\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\README.md -> anubis\\configs\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "configs\\system_paths.json -> anubis\\configs\\system_paths.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "config-files\\.env.langsmith -> anubis\\configs\\.env.langsmith"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "config-files\\ollama_check_report_20250716_092556.json -> anubis\\configs\\ollama_check_report_20250716_092556.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "config-files\\openapi.json -> anubis\\configs\\openapi.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "tests"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\tests -> anubis\\tests.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "tests -> anubis\\tests"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "scripts"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\activate_real_langsmith.py -> anubis\\scripts\\activate_real_langsmith.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\agents_cleanup.py -> anubis\\scripts\\agents_cleanup.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\check_ollama.py -> anubis\\scripts\\check_ollama.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\complete_file_organizer.py -> anubis\\scripts\\complete_file_organizer.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\comprehensive_vscode_scan.ps1 -> anubis\\scripts\\comprehensive_vscode_scan.ps1"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\create_all_readmes.py -> anubis\\scripts\\create_all_readmes.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\emergency_vscode_check.py -> anubis\\scripts\\emergency_vscode_check.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\fix_agents_with_gemini.py -> anubis\\scripts\\fix_agents_with_gemini.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\gemini_cli_helper.py -> anubis\\scripts\\gemini_cli_helper.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\gemini_integration_system.py -> anubis\\scripts\\gemini_integration_system.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\langsmith_integration_demo.py -> anubis\\scripts\\langsmith_integration_demo.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\organize_all_files.py -> anubis\\scripts\\organize_all_files.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\organize_project_files.py -> anubis\\scripts\\organize_project_files.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\process_monitor.py -> anubis\\scripts\\process_monitor.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\quick_vscode_check.py -> anubis\\scripts\\quick_vscode_check.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\quick_vscode_count.ps1 -> anubis\\scripts\\quick_vscode_count.ps1"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\README.backup_20250716_091625.md -> anubis\\scripts\\README.backup_20250716_091625.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\scripts\\README.md -> anubis\\scripts\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\README.md -> anubis\\scripts\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\run_vscode_check.bat -> anubis\\scripts\\run_vscode_check.bat"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\run_vscode_monitor.ps1 -> anubis\\scripts\\run_vscode_monitor.ps1"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\run_vscode_monitor.py -> anubis\\scripts\\run_vscode_monitor.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\setup_langsmith.py -> anubis\\scripts\\setup_langsmith.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\setup_langsmith_env.ps1 -> anubis\\scripts\\setup_langsmith_env.ps1"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\simple_langsmith_test.py -> anubis\\scripts\\simple_langsmith_test.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\smart_workflow_demo.py -> anubis\\scripts\\smart_workflow_demo.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\start_anubis_n8n_system.py -> anubis\\scripts\\start_anubis_n8n_system.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\test_langsmith_integration.py -> anubis\\scripts\\test_langsmith_integration.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\test_ollama_langsmith.py -> anubis\\scripts\\test_ollama_langsmith.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\vscode_emergency_cleanup.py -> anubis\\scripts\\vscode_emergency_cleanup.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\vscode_heavy_load_analyzer.py -> anubis\\scripts\\vscode_heavy_load_analyzer.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\VSCODE_MONITOR_README.md -> anubis\\scripts\\VSCODE_MONITOR_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\vscode_process_alerts.py -> anubis\\scripts\\vscode_process_alerts.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\vscode_process_monitor.py -> anubis\\scripts\\vscode_process_monitor.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "scripts\\__init__.py -> anubis\\scripts\\__init__.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "project-files\\check_ready.py -> anubis\\scripts\\check_ready.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "project-files\\quick_start.py -> anubis\\scripts\\quick_start.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "documentation"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\AI_STATUS_REPORT.md -> anubis\\docs\\AI_STATUS_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\COMPREHENSIVE_PROJECT_REPORT.md -> anubis\\docs\\COMPREHENSIVE_PROJECT_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\developer_guide.md -> anubis\\docs\\developer_guide.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FINAL_COMPREHENSIVE_REPORT.md -> anubis\\docs\\FINAL_COMPREHENSIVE_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FINAL_FILE_ORGANIZATION_REPORT.md -> anubis\\docs\\FINAL_FILE_ORGANIZATION_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FINAL_ORGANIZATION_REPORT.md -> anubis\\docs\\FINAL_ORGANIZATION_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FINAL_PROJECT_COMPLETION_REPORT.md -> anubis\\docs\\FINAL_PROJECT_COMPLETION_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FINAL_PROJECT_REPORT.md -> anubis\\docs\\FINAL_PROJECT_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\FIXES_COMPLETED_REPORT.md -> anubis\\docs\\FIXES_COMPLETED_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\installation_guide.md -> anubis\\docs\\installation_guide.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\LANGSMITH_FINAL_SUCCESS_REPORT.md -> anubis\\docs\\LANGSMITH_FINAL_SUCCESS_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\LANGSMITH_INTEGRATION_ANALYSIS.md -> anubis\\docs\\LANGSMITH_INTEGRATION_ANALYSIS.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\LANGSMITH_OPTIMIZATION_GUIDE.md -> anubis\\docs\\LANGSMITH_OPTIMIZATION_GUIDE.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\LANGSMITH_SETUP_GUIDE.md -> anubis\\docs\\LANGSMITH_SETUP_GUIDE.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\LANGSMITH_STATUS_REPORT.md -> anubis\\docs\\LANGSMITH_STATUS_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\MAIN_PY_FIXES_REPORT.md -> anubis\\docs\\MAIN_PY_FIXES_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\N8N_INTEGRATION_GUIDE.md -> anubis\\docs\\N8N_INTEGRATION_GUIDE.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\OLLAMA_INTEGRATION_REPORT.md -> anubis\\docs\\OLLAMA_INTEGRATION_REPORT.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\project_index.json -> anubis\\docs\\project_index.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\PROJECT_INDEX.md -> anubis\\docs\\PROJECT_INDEX.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\PROJECT_STRUCTURE.md -> anubis\\docs\\PROJECT_STRUCTURE.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\PROJECT_STRUCTURE_README.md -> anubis\\docs\\PROJECT_STRUCTURE_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\docs\\README.md -> anubis\\docs\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\README.md -> anubis\\docs\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\README_NEW.md -> anubis\\docs\\README_NEW.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\requirements.txt -> anubis\\docs\\requirements.txt"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\requirements_database.txt -> anubis\\docs\\requirements_database.txt"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\user_guide.md -> anubis\\docs\\user_guide.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "docs\\vscode_72_processes_emergency_guide.md -> anubis\\docs\\vscode_72_processes_emergency_guide.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "documentation\\COMPLETE_SUCCESS_SUMMARY.md -> anubis\\docs\\COMPLETE_SUCCESS_SUMMARY.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "documentation\\PROJECT_SUMMARY.md -> anubis\\docs\\PROJECT_SUMMARY.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "documentation\\QUICK_GUIDE.md -> anubis\\docs\\QUICK_GUIDE.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "workspace"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\backups -> anubis\\workspace\\backups"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\collaboration_logs -> anubis\\workspace\\collaboration_logs"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\knowledge_base -> anubis\\workspace\\knowledge_base"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\logs -> anubis\\workspace\\logs"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\workspace\\README.md -> anubis\\workspace\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\README.md -> anubis\\workspace\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\reports -> anubis\\workspace\\reports"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "workspace\\shared_memory -> anubis\\workspace\\shared_memory"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\workspace\\README.md -> anubis\\workspace\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "logs\\README.md -> anubis\\workspace\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\agents_cleanup_report_20250716_081504.json -> anubis\\workspace\\agents_cleanup_report_20250716_081504.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\ai_integration_test_report_20250716_011209.json -> anubis\\workspace\\ai_integration_test_report_20250716_011209.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\complete_organization_report_20250716_075352.json -> anubis\\workspace\\complete_organization_report_20250716_075352.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\comprehensive_system_test_report_20250716_064902.json -> anubis\\workspace\\comprehensive_system_test_report_20250716_064902.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\comprehensive_test_report_20250716_074112.json -> anubis\\workspace\\comprehensive_test_report_20250716_074112.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\file_organization_report_20250716_091625.json -> anubis\\workspace\\file_organization_report_20250716_091625.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\langsmith_activation_report_20250716_084129.json -> anubis\\workspace\\langsmith_activation_report_20250716_084129.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\langsmith_integration_test_20250716_082826.json -> anubis\\workspace\\langsmith_integration_test_20250716_082826.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\langsmith_integration_test_20250716_083715.json -> anubis\\workspace\\langsmith_integration_test_20250716_083715.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\ollama_check_report_20250716_010906.json -> anubis\\workspace\\ollama_check_report_20250716_010906.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\organization_report_20250716_074447.json -> anubis\\workspace\\organization_report_20250716_074447.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\workspace\\README.md -> anubis\\workspace\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\README.md -> anubis\\workspace\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "reports\\readme_generation_report.json -> anubis\\workspace\\readme_generation_report.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "templates"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\templates -> anubis\\templates.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "templates -> anubis\\templates"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "examples"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "anubis\\examples -> anubis\\examples.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "examples -> anubis\\examples"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "vscode_tools"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\agents -> tools\\vscode-optimizer\\agents"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\LAUNCH_SUITE.bat -> tools\\vscode-optimizer\\LAUNCH_SUITE.bat"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "tools\\vscode-optimizer\\README.md -> tools\\vscode-optimizer\\README.md.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\README.md -> tools\\vscode-optimizer\\README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\SUITE_INFO.md -> tools\\vscode-optimizer\\SUITE_INFO.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\VS-Code-Performance-Optimizer -> tools\\vscode-optimizer\\VS-Code-Performance-Optimizer"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "Universal-AI-Assistant-Suite\\VSCode-Control-Center -> tools\\vscode-optimizer\\VSCode-Control-Center"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "dashboard-files\\dashboard_config.json -> tools\\vscode-optimizer\\dashboard_config.json"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "dashboard-files\\DASHBOARD_README.md -> tools\\vscode-optimizer\\DASHBOARD_README.md"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "dashboard-files\\run_dashboard.sh -> tools\\vscode-optimizer\\run_dashboard.sh"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "dashboard-files\\Start-Dashboard.ps1 -> tools\\vscode-optimizer\\Start-Dashboard.ps1"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "dashboard-files\\test_dashboard.py -> tools\\vscode-optimizer\\test_dashboard.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "emergency_tools"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "tools\\emergency -> tools\\emergency.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "نقل ملف", "details": "emergency-tools -> tools\\emergency"}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "monitoring"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "tools\\monitoring -> tools\\monitoring.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "خطأ", "details": "خطأ في تطبيق قاعدة monitoring: Cannot move a directory 'tools' into itself 'tools\\monitoring'."}, {"timestamp": "2025-07-18 16:02:41", "action": "تطبيق قاعدة", "details": "archive"}, {"timestamp": "2025-07-18 16:02:41", "action": "نسخ احتياطي", "details": "archive\\backups -> archive\\backups.backup_20250718_160241"}, {"timestamp": "2025-07-18 16:02:41", "action": "خطأ", "details": "خطأ في تطبيق قاعدة archive: [WinError 2] The system cannot find the file specified"}, {"timestamp": "2025-07-18 16:02:41", "action": "تنظيف المجلدات", "details": "حذف المجلدات الفارغة"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث المسارات", "details": "إصلاح مسارات الاستيراد"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\main.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\api\\anubis_api_server.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\agents_cleanup.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\fix_agents_with_gemini.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\langsmith_integration_demo.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\setup_langsmith.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\smart_workflow_demo.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\test_langsmith_integration.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\scripts\\test_ollama_langsmith.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\ask_anubis.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\comprehensive_system_test.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\quick_ai_test.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_agents.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_ai_fixed.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_ai_integration.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_anubis_system.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_enhanced_error_detector.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_error_detector.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_plugins.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_project_analyzer.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_smart_analyzer.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\tests\\test_system.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\database\\tests\\final_db_test.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "تحديث مسارات", "details": "anubis\\database\\tests\\test_db_connection.py"}, {"timestamp": "2025-07-18 16:02:41", "action": "إنشاء ملفات أساسية", "details": "إنشاء ملفات الهيكل الجديد"}], "errors": ["خطأ في تطبيق قاعدة monitoring: Cannot move a directory 'tools' into itself 'tools\\monitoring'.", "خطأ في تطبيق قاعدة archive: [WinError 2] The system cannot find the file specified"], "new_structure": {"anubis/": {"core/": ["النواة الأساسية للنظام"], "agents/": ["الوكلاء الذكيين"], "api/": ["واجهة برمجة التطبيقات"], "database/": ["قاعدة البيانات وإدارتها"], "plugins/": ["نظام الإضافات"], "configs/": ["ملفات التكوين"], "tests/": ["الاختبارات الشاملة"], "scripts/": ["النصوص المساعدة"], "docs/": ["التوثيق الكامل"], "workspace/": ["مساحة العمل"], "templates/": ["قوالب المشاريع"], "examples/": ["أمثلة الاستخدام"]}, "tools/": {"vscode-optimizer/": ["أدوات تحسين VS Code"], "emergency/": ["أدوات الطوارئ"], "monitoring/": ["أدوات المراقبة"]}, "archive/": {"old_versions/": ["الإصدارات القديمة"], "backups/": ["النسخ الاحتياطية"], "deprecated/": ["الملفات المهجورة"]}}}