@echo off
chcp 65001 >nul
color 0A
title VS Code Control Center - Stable Version

cls
echo.
echo ================================================================
echo                VS Code Control Center                          
echo                    Stable Version                             
echo ================================================================
echo.
echo Starting stable version with enhanced error handling...
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed!
    echo Please download and install Python from: https://python.org
    pause
    exit /b 1
)

echo Python OK

REM Check basic libraries
echo Checking required libraries...
python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: tkinter not available!
    echo Please install tkinter or use a different Python distribution
    pause
    exit /b 1
)

python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing psutil...
    pip install psutil
    if %errorlevel% neq 0 (
        echo WARNING: Will run in basic mode without process monitoring
    ) else (
        echo psutil installed successfully
    )
) else (
    echo psutil available
)

echo.
echo Stable version features:
echo    - Enhanced error handling for KeyboardInterrupt
echo    - Safe process monitoring with exception handling
echo    - Stable system statistics display
echo    - Protected chat interface
echo    - Safe cleanup operations
echo    - Robust auto-refresh mechanism

python -c "import sys; sys.path.append('agents'); from agents.agent_coordinator import AgentCoordinator" >nul 2>&1
if %errorlevel% equ 0 (
    echo    - Intelligent agents with error protection
    echo    - Safe AI chat integration
)

echo.
echo Starting VS Code Control Center - Stable Version...
echo.

REM Run the stable application
python vscode_control_center_stable.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start stable version!
    echo Trying fallback options...
    echo.
    echo Trying advanced version...
    python vscode_control_center_pro.py
    if %errorlevel% neq 0 (
        echo.
        echo Trying basic version...
        python vscode_control_center.py
    )
)

echo.
echo VS Code Control Center closed
echo Thank you for using the stable version!
pause
