#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 فاحص أنظمة سير العمل والأتمتة مع نظام العزل المتقدم
Workflows and Automation Inspector with Advanced Isolation System
"""

import os
import json
from pathlib import Path
from datetime import datetime

class WorkflowsAutomationInspector:
    def __init__(self):
        self.base_path = Path("workflows_and_automation")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "inspector": "Workflows and Automation Inspector with Isolation",
            "inspection_type": "comprehensive_workflows_automation_analysis",
            "overall_health": "unknown",
            "components": {},
            "workflow_analysis": {},
            "automation_capabilities": {},
            "n8n_analysis": {},
            "security_assessment": {},
            "isolation_plan": {},
            "recommendations": []
        }
        
        self.isolation_creator = WorkflowsIsolationCreator()
    
    def analyze_workflows_structure(self):
        """تحليل هيكل أنظمة سير العمل والأتمتة"""
        print("🔄 تحليل هيكل أنظمة سير العمل والأتمتة...")
        
        component_health = {
            "status": "analyzing",
            "structure_score": 0,
            "directories": {},
            "workflow_systems": {},
            "automation_tools": {},
            "issues": [],
            "strengths": []
        }
        
        if not self.base_path.exists():
            component_health["issues"].append("❌ مجلد workflows_and_automation/ مفقود")
            self.report["components"]["structure"] = component_health
            return component_health
        
        # فحص المجلدات الأساسية
        essential_dirs = {
            "n8n": {"critical": True, "description": "نظام n8n للأتمتة"},
            "n8n_1": {"critical": True, "description": "إعدادات n8n المتقدمة"},
            "scripts": {"critical": True, "description": "سكريبتات الأتمتة"},
            "configs": {"critical": True, "description": "إعدادات الأنظمة"},
            "docs": {"critical": False, "description": "التوثيق"}
        }
        
        for dir_name, dir_info in essential_dirs.items():
            dir_path = self.base_path / dir_name
            dir_analysis = self._analyze_workflow_directory(dir_path)
            component_health["directories"][dir_name] = dir_analysis
            
            if dir_analysis["exists"]:
                if dir_analysis["files_count"] > 0:
                    component_health["strengths"].append(f"✅ {dir_name}/ نشط ({dir_analysis['files_count']} ملف)")
                    component_health["structure_score"] += 20
                else:
                    component_health["issues"].append(f"⚠️ {dir_name}/ فارغ")
            else:
                if dir_info["critical"]:
                    component_health["issues"].append(f"🚨 {dir_name}/ مفقود (حرج)")
                else:
                    component_health["issues"].append(f"⚠️ {dir_name}/ مفقود")
        
        # تحليل أنظمة سير العمل
        self._analyze_workflow_systems(component_health)
        
        # تقييم الحالة
        if component_health["structure_score"] >= 80:
            component_health["status"] = "excellent"
        elif component_health["structure_score"] >= 60:
            component_health["status"] = "good"
        elif component_health["structure_score"] >= 40:
            component_health["status"] = "fair"
        else:
            component_health["status"] = "poor"
        
        self.report["components"]["structure"] = component_health
        return component_health
    
    def _analyze_workflow_directory(self, dir_path):
        """تحليل مجلد أنظمة سير العمل"""
        analysis = {
            "exists": dir_path.exists(),
            "files_count": 0,
            "subdirs_count": 0,
            "file_types": {},
            "workflow_patterns": []
        }
        
        if analysis["exists"]:
            all_items = list(dir_path.rglob("*"))
            files = [item for item in all_items if item.is_file()]
            dirs = [item for item in all_items if item.is_dir()]
            
            analysis["files_count"] = len(files)
            analysis["subdirs_count"] = len(dirs)
            
            # تحليل أنواع الملفات
            for file in files:
                ext = file.suffix.lower()
                if ext not in analysis["file_types"]:
                    analysis["file_types"][ext] = 0
                analysis["file_types"][ext] += 1
                
                # اكتشاف أنماط سير العمل
                filename_lower = file.name.lower()
                if "workflow" in filename_lower or ".json" in filename_lower:
                    analysis["workflow_patterns"].append("workflow_files")
                elif "node" in filename_lower or ".ts" in filename_lower:
                    analysis["workflow_patterns"].append("custom_nodes")
                elif "credential" in filename_lower:
                    analysis["workflow_patterns"].append("credentials")
                elif "automation" in filename_lower:
                    analysis["workflow_patterns"].append("automation_scripts")
        
        return analysis
    
    def _analyze_workflow_systems(self, component_health):
        """تحليل أنظمة سير العمل المختلفة"""
        workflow_systems = {}
        
        # تحليل n8n
        n8n_path = self.base_path / "n8n_1"
        if n8n_path.exists():
            n8n_analysis = {
                "exists": True,
                "has_credentials": (n8n_path / "credentials").exists(),
                "has_nodes": (n8n_path / "nodes").exists(),
                "has_workflows": (n8n_path / "workflows").exists(),
                "custom_nodes_count": 0,
                "workflows_count": 0
            }
            
            # عد العقد المخصصة
            if n8n_analysis["has_nodes"]:
                nodes_files = list((n8n_path / "nodes").glob("*.ts"))
                n8n_analysis["custom_nodes_count"] = len(nodes_files)
            
            # عد سير العمل
            if n8n_analysis["has_workflows"]:
                workflow_files = list((n8n_path / "workflows").glob("*.json"))
                n8n_analysis["workflows_count"] = len(workflow_files)
            
            workflow_systems["n8n"] = n8n_analysis
            
            if n8n_analysis["custom_nodes_count"] > 0:
                component_health["strengths"].append(f"✅ n8n مع {n8n_analysis['custom_nodes_count']} عقدة مخصصة")
                component_health["structure_score"] += 15
            
            if n8n_analysis["workflows_count"] > 0:
                component_health["strengths"].append(f"✅ n8n مع {n8n_analysis['workflows_count']} سير عمل")
                component_health["structure_score"] += 10
        
        component_health["workflow_systems"] = workflow_systems
    
    def analyze_n8n_system(self):
        """تحليل نظام n8n بالتفصيل"""
        print("🔧 تحليل نظام n8n بالتفصيل...")
        
        n8n_analysis = {
            "overall_score": 0,
            "custom_nodes": {},
            "credentials": {},
            "workflows": {},
            "capabilities": [],
            "security_issues": [],
            "strengths": []
        }
        
        n8n_path = self.base_path / "n8n_1"
        
        if not n8n_path.exists():
            n8n_analysis["security_issues"].append("❌ نظام n8n غير موجود")
            self.report["n8n_analysis"] = n8n_analysis
            return n8n_analysis
        
        # تحليل العقد المخصصة
        nodes_path = n8n_path / "nodes"
        if nodes_path.exists():
            node_files = list(nodes_path.glob("*.ts"))
            for node_file in node_files:
                node_analysis = self._analyze_n8n_node(node_file)
                n8n_analysis["custom_nodes"][node_file.stem] = node_analysis
            
            if node_files:
                n8n_analysis["capabilities"].append(f"عقد مخصصة ({len(node_files)})")
                n8n_analysis["overall_score"] += 25
                n8n_analysis["strengths"].append(f"✅ {len(node_files)} عقدة مخصصة متقدمة")
        
        # تحليل بيانات الاعتماد
        creds_path = n8n_path / "credentials"
        if creds_path.exists():
            cred_files = list(creds_path.glob("*.ts"))
            for cred_file in cred_files:
                cred_analysis = self._analyze_n8n_credential(cred_file)
                n8n_analysis["credentials"][cred_file.stem] = cred_analysis
            
            if cred_files:
                n8n_analysis["capabilities"].append(f"بيانات اعتماد ({len(cred_files)})")
                n8n_analysis["overall_score"] += 20
                n8n_analysis["strengths"].append(f"✅ {len(cred_files)} نظام اعتماد")
        
        # تحليل سير العمل
        workflows_path = n8n_path / "workflows"
        if workflows_path.exists():
            workflow_files = list(workflows_path.glob("*.json"))
            for workflow_file in workflow_files:
                workflow_analysis = self._analyze_n8n_workflow(workflow_file)
                n8n_analysis["workflows"][workflow_file.stem] = workflow_analysis
            
            if workflow_files:
                n8n_analysis["capabilities"].append(f"سير عمل ({len(workflow_files)})")
                n8n_analysis["overall_score"] += 30
                n8n_analysis["strengths"].append(f"✅ {len(workflow_files)} سير عمل")
        
        # تقييم الأمان
        if not n8n_analysis["credentials"]:
            n8n_analysis["security_issues"].append("⚠️ لا توجد إعدادات أمان لبيانات الاعتماد")
        
        self.report["n8n_analysis"] = n8n_analysis
        return n8n_analysis
    
    def _analyze_n8n_node(self, node_file):
        """تحليل عقدة n8n مخصصة"""
        analysis = {
            "exists": True,
            "type": "custom_node",
            "integrations": [],
            "capabilities": []
        }
        
        try:
            with open(node_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن التكاملات
            if "anubis" in content.lower():
                analysis["integrations"].append("Anubis System")
            if "gemini" in content.lower():
                analysis["integrations"].append("Google Gemini")
            if "ollama" in content.lower():
                analysis["integrations"].append("Ollama")
            if "agents" in content.lower():
                analysis["integrations"].append("AI Agents")
            
            # تحديد القدرات
            if "execute" in content.lower():
                analysis["capabilities"].append("execution")
            if "webhook" in content.lower():
                analysis["capabilities"].append("webhooks")
            if "api" in content.lower():
                analysis["capabilities"].append("api_integration")
                
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def _analyze_n8n_credential(self, cred_file):
        """تحليل بيانات اعتماد n8n"""
        analysis = {
            "exists": True,
            "type": "credential",
            "security_features": []
        }
        
        try:
            with open(cred_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص ميزات الأمان
            if "password" in content.lower():
                analysis["security_features"].append("password_auth")
            if "token" in content.lower():
                analysis["security_features"].append("token_auth")
            if "api_key" in content.lower():
                analysis["security_features"].append("api_key_auth")
            if "oauth" in content.lower():
                analysis["security_features"].append("oauth")
                
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def _analyze_n8n_workflow(self, workflow_file):
        """تحليل سير عمل n8n"""
        analysis = {
            "exists": True,
            "type": "workflow",
            "nodes_count": 0,
            "triggers": [],
            "actions": []
        }
        
        try:
            with open(workflow_file, 'r', encoding='utf-8') as f:
                workflow_data = json.load(f)
            
            if "nodes" in workflow_data:
                analysis["nodes_count"] = len(workflow_data["nodes"])
                
                # تحليل العقد
                for node in workflow_data["nodes"]:
                    node_type = node.get("type", "")
                    if "trigger" in node_type.lower():
                        analysis["triggers"].append(node_type)
                    else:
                        analysis["actions"].append(node_type)
                        
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def analyze_automation_capabilities(self):
        """تحليل قدرات الأتمتة"""
        print("⚙️ تحليل قدرات الأتمتة...")
        
        capabilities = {
            "overall_score": 0,
            "workflow_engines": {},
            "automation_scripts": {},
            "integration_points": [],
            "automation_level": "unknown",
            "strengths": [],
            "weaknesses": []
        }
        
        # تحليل محركات سير العمل
        structure = self.report["components"].get("structure", {})
        n8n_data = self.report.get("n8n_analysis", {})
        
        # تقييم n8n
        if n8n_data.get("overall_score", 0) > 0:
            capabilities["workflow_engines"]["n8n"] = {
                "score": n8n_data["overall_score"],
                "custom_nodes": len(n8n_data.get("custom_nodes", {})),
                "workflows": len(n8n_data.get("workflows", {})),
                "status": "active"
            }
            capabilities["overall_score"] += n8n_data["overall_score"]
            capabilities["strengths"].append("✅ نظام n8n متقدم مع عقد مخصصة")
        
        # تحليل السكريبتات
        scripts_dir = structure.get("directories", {}).get("scripts", {})
        if scripts_dir.get("exists"):
            scripts_count = scripts_dir.get("files_count", 0)
            if scripts_count > 0:
                capabilities["automation_scripts"] = {
                    "count": scripts_count,
                    "status": "available",
                    "types": scripts_dir.get("file_types", {})
                }
                capabilities["overall_score"] += 20
                capabilities["strengths"].append(f"✅ {scripts_count} سكريبت أتمتة")
        
        # تحديد نقاط التكامل
        for node_name, node_data in n8n_data.get("custom_nodes", {}).items():
            integrations = node_data.get("integrations", [])
            capabilities["integration_points"].extend(integrations)
        
        # إزالة التكرارات
        capabilities["integration_points"] = list(set(capabilities["integration_points"]))
        
        # تحديد مستوى الأتمتة
        if capabilities["overall_score"] >= 70:
            capabilities["automation_level"] = "advanced"
        elif capabilities["overall_score"] >= 50:
            capabilities["automation_level"] = "intermediate"
        elif capabilities["overall_score"] >= 30:
            capabilities["automation_level"] = "basic"
        else:
            capabilities["automation_level"] = "minimal"
        
        self.report["automation_capabilities"] = capabilities
        return capabilities
    
    def assess_security_and_isolation_needs(self):
        """تقييم احتياجات الأمان والعزل"""
        print("🔒 تقييم احتياجات الأمان والعزل...")
        
        security_assessment = {
            "current_security_level": "unknown",
            "vulnerabilities": [],
            "isolation_requirements": [],
            "security_recommendations": [],
            "isolation_plan": {}
        }
        
        # تحليل المخاطر الأمنية
        n8n_data = self.report.get("n8n_analysis", {})
        
        # مخاطر n8n
        if n8n_data.get("credentials"):
            security_assessment["vulnerabilities"].append("⚠️ بيانات اعتماد n8n قد تكون مكشوفة")
            security_assessment["isolation_requirements"].append("عزل بيانات الاعتماد")
        
        if n8n_data.get("workflows"):
            security_assessment["vulnerabilities"].append("⚠️ سير العمل قد يحتوي على مفاتيح API")
            security_assessment["isolation_requirements"].append("عزل تشغيل سير العمل")
        
        # تحديد متطلبات العزل
        security_assessment["isolation_requirements"].extend([
            "حاوية معزولة لـ n8n",
            "شبكة منفصلة للأتمتة",
            "تشفير بيانات الاعتماد",
            "مراقبة أمنية لسير العمل",
            "عزل التكاملات الخارجية"
        ])
        
        # خطة العزل
        security_assessment["isolation_plan"] = {
            "containers": ["n8n-isolated", "workflows-executor", "automation-monitor"],
            "networks": ["automation-net", "secure-credentials-net"],
            "volumes": ["workflows-data", "credentials-vault", "automation-logs"],
            "security_features": ["secrets-management", "network-isolation", "audit-logging"]
        }
        
        # توصيات الأمان
        security_assessment["security_recommendations"] = [
            "🔒 تشفير جميع بيانات الاعتماد",
            "🌐 عزل شبكة الأتمتة",
            "📊 مراقبة مستمرة لسير العمل",
            "🔐 إدارة أسرار متقدمة",
            "🛡️ فصل بيئات التطوير والإنتاج"
        ]
        
        self.report["security_assessment"] = security_assessment
        return security_assessment
    
    def generate_recommendations(self):
        """إنشاء التوصيات الشاملة"""
        print("💡 إنشاء التوصيات الشاملة...")
        
        recommendations = []
        
        # تحليل البيانات
        structure = self.report["components"].get("structure", {})
        automation = self.report.get("automation_capabilities", {})
        security = self.report.get("security_assessment", {})
        
        # توصيات بناءً على الهيكل
        structure_score = structure.get("structure_score", 0)
        if structure_score < 60:
            recommendations.append("🏗️ تحسين هيكل أنظمة سير العمل والأتمتة")
        
        # توصيات بناءً على الأتمتة
        automation_level = automation.get("automation_level", "minimal")
        if automation_level in ["minimal", "basic"]:
            recommendations.append("⚙️ تطوير قدرات الأتمتة وإضافة سير عمل جديد")
        
        # توصيات الأمان
        recommendations.extend(security.get("security_recommendations", []))
        
        # توصيات العزل
        recommendations.append("🐳 إنشاء نظام عزل متقدم للأتمتة")
        recommendations.append("🔒 تطبيق أمان متعدد الطبقات")
        
        self.report["recommendations"] = recommendations
        return recommendations
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة"""
        print("🏥 تقييم الحالة العامة...")
        
        # حساب النقاط
        structure_score = self.report["components"].get("structure", {}).get("structure_score", 0)
        automation_score = self.report.get("automation_capabilities", {}).get("overall_score", 0)
        n8n_score = self.report.get("n8n_analysis", {}).get("overall_score", 0)
        
        # وزن النقاط
        weighted_score = (
            (structure_score * 0.3) +
            (automation_score * 0.4) +
            (n8n_score * 0.3)
        )
        
        # تحديد الحالة
        if weighted_score >= 75:
            self.report["overall_health"] = "excellent"
            health_text = "ممتاز"
            emoji = "🟢"
        elif weighted_score >= 60:
            self.report["overall_health"] = "good"
            health_text = "جيد"
            emoji = "🟡"
        elif weighted_score >= 40:
            self.report["overall_health"] = "fair"
            health_text = "متوسط"
            emoji = "🟠"
        else:
            self.report["overall_health"] = "poor"
            health_text = "يحتاج تحسين"
            emoji = "🔴"
        
        print(f"\n{emoji} الحالة العامة: {health_text} ({weighted_score:.1f}/100)")
        
        return self.report["overall_health"]
    
    def run_comprehensive_inspection(self):
        """تشغيل الفحص الشامل"""
        print("🔄 بدء فحص أنظمة سير العمل والأتمتة الشامل")
        print("=" * 60)
        
        # تشغيل الفحوصات
        self.analyze_workflows_structure()
        self.analyze_n8n_system()
        self.analyze_automation_capabilities()
        self.assess_security_and_isolation_needs()
        self.generate_recommendations()
        self.evaluate_overall_health()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("📋 تقرير فحص أنظمة سير العمل والأتمتة")
        print("="*60)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "fair": "🟠",
            "poor": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']}")
        
        # تفاصيل الهيكل
        structure = self.report["components"].get("structure", {})
        print(f"\n🏗️ تفاصيل الهيكل:")
        for dir_name, dir_info in structure.get("directories", {}).items():
            status = "✅ موجود" if dir_info.get("exists") else "❌ مفقود"
            files_count = dir_info.get("files_count", 0)
            print(f"   📁 {dir_name}: {status} ({files_count} ملف)")
        
        # تحليل n8n
        n8n_data = self.report.get("n8n_analysis", {})
        if n8n_data.get("custom_nodes"):
            print(f"\n🔧 تحليل n8n:")
            print(f"   🎯 العقد المخصصة: {len(n8n_data['custom_nodes'])}")
            print(f"   🔑 بيانات الاعتماد: {len(n8n_data.get('credentials', {}))}")
            print(f"   🔄 سير العمل: {len(n8n_data.get('workflows', {}))}")
        
        # قدرات الأتمتة
        automation = self.report.get("automation_capabilities", {})
        print(f"\n⚙️ قدرات الأتمتة:")
        print(f"   📊 مستوى الأتمتة: {automation.get('automation_level', 'unknown')}")
        print(f"   🔗 نقاط التكامل: {len(automation.get('integration_points', []))}")
        
        # تقييم الأمان
        security = self.report.get("security_assessment", {})
        print(f"\n🔒 تقييم الأمان:")
        print(f"   ⚠️ نقاط الضعف: {len(security.get('vulnerabilities', []))}")
        print(f"   🛡️ متطلبات العزل: {len(security.get('isolation_requirements', []))}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        for rec in self.report.get("recommendations", []):
            print(f"   {rec}")
        
        print("\n" + "="*60)
        print("🔄 انتهى فحص أنظمة سير العمل والأتمتة")
        print("="*60)
    
    def save_report(self):
        """حفظ التقرير"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"workflows_automation_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename


class WorkflowsIsolationCreator:
    """منشئ نظام العزل لأنظمة سير العمل والأتمتة"""
    
    def __init__(self):
        self.base_path = Path("workflows_and_automation")
        self.isolation_log = {
            "timestamp": datetime.now().isoformat(),
            "creator": "Workflows Automation Isolation Creator",
            "isolation_features": [],
            "security_measures": [],
            "completion_status": "in_progress"
        }
    
    def create_workflows_isolation_system(self):
        """إنشاء نظام العزل لسير العمل والأتمتة"""
        print("🐳 إنشاء نظام العزل لسير العمل والأتمتة...")
        
        # إنشاء Dockerfile لـ n8n معزول
        dockerfile_content = """# حاوية n8n معزولة وآمنة لنظام أنوبيس
FROM n8nio/n8n:latest

# إعداد متغيرات البيئة للأمان
ENV N8N_ENCRYPTION_KEY=anubis_secure_key_2024
ENV N8N_USER_MANAGEMENT_DISABLED=false
ENV N8N_SECURE_COOKIE=true
ENV N8N_PROTOCOL=https
ENV N8N_PORT=5678
ENV N8N_LISTEN_ADDRESS=0.0.0.0
ENV WEBHOOK_URL=https://anubis-workflows.local
ENV N8N_METRICS=true
ENV N8N_LOG_LEVEL=info
ENV N8N_LOG_OUTPUT=console,file
ENV N8N_LOG_FILE_LOCATION=/home/<USER>/.n8n/logs/
ENV DB_TYPE=sqlite
ENV DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

# إنشاء مستخدم غير مميز للأمان
USER root
RUN groupadd -g 1003 anubis_workflows && \\
    useradd -u 1003 -g 1003 -m -s /bin/bash anubis_workflows && \\
    mkdir -p /app/workflows /app/credentials /app/nodes /app/logs && \\
    chown -R anubis_workflows:anubis_workflows /app && \\
    chown -R anubis_workflows:anubis_workflows /home/<USER>/.n8n

# تثبيت الأدوات الإضافية للأمان
RUN apt-get update && apt-get install -y --no-install-recommends \\
    curl \\
    jq \\
    openssl \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/*

# نسخ الملفات المخصصة
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/nodes/ /home/<USER>/.n8n/nodes/
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/credentials/ /home/<USER>/.n8n/credentials/
COPY --chown=anubis_workflows:anubis_workflows ./n8n_1/workflows/ /home/<USER>/.n8n/workflows/

# التبديل للمستخدم غير المميز
USER anubis_workflows

# إنشاء المجلدات المطلوبة
RUN mkdir -p /home/<USER>/.n8n/logs /home/<USER>/.n8n/backups

# فحص صحة متقدم
HEALTHCHECK --interval=30s --timeout=15s --start-period=45s --retries=3 \\
    CMD curl -f http://localhost:5678/healthz || exit 1

# المنفذ المكشوف
EXPOSE 5678

# نقطة الدخول الآمنة
ENTRYPOINT ["tini", "--", "/docker-entrypoint.sh"]
CMD ["n8n", "start"]
"""
        
        with open(self.base_path / "Dockerfile", 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        
        # إنشاء docker-compose.yml للأتمتة المعزولة
        docker_compose_content = """version: '3.8'

services:
  anubis-n8n:
    build: .
    container_name: anubis-n8n-isolated
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد للأتمتة
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # الشبكات المعزولة
    networks:
      - anubis-workflows-net
      - anubis-automation-secure-net
    
    # الأحجام المعزولة
    volumes:
      - anubis-n8n-data:/home/<USER>/.n8n
      - anubis-workflows-data:/app/workflows
      - anubis-credentials-vault:/app/credentials:ro
      - anubis-automation-logs:/app/logs:rw
    
    # متغيرات البيئة الآمنة
    environment:
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY:-anubis_secure_key_2024}
      - N8N_USER_MANAGEMENT_DISABLED=false
      - N8N_SECURE_COOKIE=true
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://anubis-workflows.local
      - N8N_METRICS=true
      - DB_TYPE=sqlite
      - N8N_LOG_LEVEL=info
      - GENERIC_TIMEZONE=UTC
    
    # حماية النظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=200m
      - /var/tmp:rw,noexec,nosuid,size=100m
    
    # إزالة الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
    
    # المنافذ المحمية
    ports:
      - "127.0.0.1:5678:5678"
    
    # تسميات للمراقبة
    labels:
      - "anubis.component=workflows"
      - "anubis.isolation.level=advanced"
      - "anubis.automation.enabled=true"
    
    # التبعيات
    depends_on:
      - anubis-workflows-db
      - anubis-automation-monitor
  
  anubis-workflows-db:
    image: postgres:15-alpine
    container_name: anubis-workflows-db
    restart: unless-stopped
    networks:
      - anubis-automation-secure-net
    volumes:
      - anubis-workflows-db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=n8n_workflows
      - POSTGRES_USER=anubis_workflows
      - POSTGRES_PASSWORD=${WORKFLOWS_DB_PASSWORD:-anubis_workflows_secure_2024}
    security_opt:
      - no-new-privileges:true
    
  anubis-automation-monitor:
    image: prom/prometheus:latest
    container_name: anubis-automation-monitor
    restart: unless-stopped
    networks:
      - anubis-workflows-net
    volumes:
      - ./monitoring/prometheus-workflows.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-automation-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9093:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
  
  anubis-workflows-vault:
    image: vault:latest
    container_name: anubis-workflows-vault
    restart: unless-stopped
    networks:
      - anubis-automation-secure-net
    volumes:
      - anubis-workflows-vault-data:/vault/data
      - ./security/vault-workflows-config.hcl:/vault/config/vault.hcl:ro
    ports:
      - "127.0.0.1:8201:8200"
    cap_add:
      - IPC_LOCK
    security_opt:
      - no-new-privileges:true
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=anubis-workflows-token
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
  
  anubis-automation-scheduler:
    build: .
    container_name: anubis-automation-scheduler
    restart: unless-stopped
    networks:
      - anubis-workflows-net
    volumes:
      - anubis-workflows-data:/app/workflows:ro
      - anubis-automation-logs:/app/logs:rw
    environment:
      - SCHEDULER_MODE=isolated
      - WORKFLOWS_PATH=/app/workflows
      - LOG_LEVEL=info
    security_opt:
      - no-new-privileges:true
    command: ["node", "-e", "console.log('Scheduler running...'); setInterval(() => console.log('Checking workflows...'), 60000);"]

# الشبكات المعزولة للأتمتة
networks:
  anubis-workflows-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
  
  anubis-automation-secure-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# الأحجام المعزولة للأتمتة
volumes:
  anubis-n8n-data:
    driver: local
  anubis-workflows-data:
    driver: local
  anubis-credentials-vault:
    driver: local
  anubis-automation-logs:
    driver: local
  anubis-workflows-db-data:
    driver: local
  anubis-automation-monitor-data:
    driver: local
  anubis-workflows-vault-data:
    driver: local
"""
        
        with open(self.base_path / "docker-compose.yml", 'w', encoding='utf-8') as f:
            f.write(docker_compose_content)
        
        self.isolation_log["isolation_features"].extend([
            "حاوية n8n معزولة وآمنة",
            "شبكتان منفصلتان للأمان",
            "قاعدة بيانات PostgreSQL معزولة",
            "مراقبة مخصصة للأتمتة",
            "مخزن أسرار Vault متخصص",
            "مجدول مهام معزول",
            "7 أحجام بيانات منفصلة"
        ])
        
        print("✅ تم إنشاء نظام العزل لسير العمل والأتمتة")
    
    def create_automation_security_configs(self):
        """إنشاء إعدادات الأمان للأتمتة"""
        print("🔒 إنشاء إعدادات الأمان للأتمتة...")
        
        # إنشاء مجلد الأمان
        security_path = self.base_path / "security"
        security_path.mkdir(exist_ok=True)
        
        # إعدادات أمان الأتمتة
        automation_security_config = {
            "workflows_security_policy": {
                "version": "1.0",
                "description": "سياسات الأمان المتقدمة لأنظمة سير العمل والأتمتة",
                "last_updated": datetime.now().isoformat()
            },
            
            "n8n_security": {
                "authentication": {
                    "user_management_enabled": True,
                    "secure_cookies": True,
                    "session_timeout_minutes": 60,
                    "password_policy": {
                        "min_length": 12,
                        "require_special_chars": True,
                        "require_numbers": True,
                        "require_uppercase": True
                    }
                },
                "workflow_security": {
                    "execution_isolation": True,
                    "credential_encryption": True,
                    "webhook_security": True,
                    "external_access_control": True
                },
                "audit_logging": {
                    "enabled": True,
                    "log_all_executions": True,
                    "log_credential_access": True,
                    "log_workflow_changes": True
                }
            },
            
            "credentials_management": {
                "encryption": {
                    "algorithm": "AES-256-GCM",
                    "key_rotation_days": 30,
                    "secure_storage": True
                },
                "access_control": {
                    "role_based_access": True,
                    "credential_sharing_policy": "restricted",
                    "audit_credential_usage": True
                },
                "vault_integration": {
                    "enabled": True,
                    "auto_sync": True,
                    "backup_encryption": True
                }
            },
            
            "workflow_execution": {
                "sandbox_mode": True,
                "resource_limits": {
                    "max_execution_time_minutes": 30,
                    "max_memory_mb": 512,
                    "max_concurrent_executions": 10
                },
                "network_restrictions": {
                    "allowed_domains": [
                        "api.openai.com",
                        "generativelanguage.googleapis.com",
                        "api.anthropic.com"
                    ],
                    "blocked_private_networks": True,
                    "webhook_url_validation": True
                }
            },
            
            "monitoring_and_alerting": {
                "real_time_monitoring": True,
                "performance_metrics": True,
                "security_alerts": {
                    "failed_authentication_attempts": True,
                    "suspicious_workflow_executions": True,
                    "unauthorized_credential_access": True,
                    "unusual_network_activity": True
                },
                "compliance_reporting": {
                    "enabled": True,
                    "report_schedule": "weekly",
                    "include_audit_trail": True
                }
            },
            
            "backup_and_recovery": {
                "automated_backups": {
                    "enabled": True,
                    "schedule": "0 2 * * *",
                    "retention_days": 30,
                    "encryption": True
                },
                "disaster_recovery": {
                    "backup_workflows": True,
                    "backup_credentials": True,
                    "backup_configurations": True,
                    "recovery_testing": "monthly"
                }
            }
        }
        
        with open(security_path / "automation_security_config.json", 'w', encoding='utf-8') as f:
            json.dump(automation_security_config, f, ensure_ascii=False, indent=2)
        
        # إنشاء تكوين Vault للأتمتة
        vault_config_content = '''ui = true
disable_mlock = true

storage "file" {
  path = "/vault/data"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = 1
}

api_addr = "http://127.0.0.1:8200"
cluster_addr = "https://127.0.0.1:8201"
'''
        
        with open(security_path / "vault-workflows-config.hcl", 'w', encoding='utf-8') as f:
            f.write(vault_config_content)
        
        # إنشاء ملف مراقبة للأتمتة
        monitoring_path = self.base_path / "monitoring"
        monitoring_path.mkdir(exist_ok=True)
        
        prometheus_config = '''global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'anubis-n8n'
    static_configs:
      - targets: ['anubis-n8n:5678']
    metrics_path: '/metrics'
  
  - job_name: 'anubis-workflows-db'
    static_configs:
      - targets: ['anubis-workflows-db:5432']
  
  - job_name: 'anubis-workflows-vault'
    static_configs:
      - targets: ['anubis-workflows-vault:8200']
'''
        
        with open(monitoring_path / "prometheus-workflows.yml", 'w', encoding='utf-8') as f:
            f.write(prometheus_config)
        
        self.isolation_log["security_measures"].extend([
            "تشفير بيانات الاعتماد AES-256",
            "مصادقة متعددة العوامل",
            "عزل تنفيذ سير العمل",
            "مراقبة أمنية مستمرة",
            "إدارة أسرار متقدمة",
            "تدقيق شامل للعمليات",
            "نسخ احتياطية مشفرة"
        ])
        
        print("✅ تم إنشاء إعدادات الأمان للأتمتة")
    
    def create_automation_startup_script(self):
        """إنشاء سكريبت تشغيل نظام الأتمتة المعزول"""
        print("🚀 إنشاء سكريبت تشغيل نظام الأتمتة المعزول...")
        
        startup_script = '''#!/bin/bash
# سكريبت تشغيل نظام سير العمل والأتمتة المعزول

echo "🔄 بدء تشغيل نظام سير العمل والأتمتة المعزول..."

# التحقق من المتطلبات
echo "🔍 فحص المتطلبات..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# الانتقال لمجلد الأتمتة
cd workflows_and_automation

# إنشاء البنية التحتية
echo "🏗️ إنشاء البنية التحتية للأتمتة..."
mkdir -p data workflows credentials logs monitoring security
mkdir -p data/n8n data/database data/vault
mkdir -p logs/workflows logs/executions logs/security
mkdir -p security/certificates security/vault

# تعيين الصلاحيات الآمنة
echo "🔒 تطبيق صلاحيات الأمان..."
chmod 700 security credentials
chmod 750 data workflows logs
chmod 755 monitoring

# إنشاء ملف متغيرات البيئة
echo "⚙️ إنشاء ملف متغيرات البيئة..."
cat > .env << EOF
# متغيرات البيئة الآمنة لنظام الأتمتة
N8N_ENCRYPTION_KEY=$(openssl rand -base64 32)
WORKFLOWS_DB_PASSWORD=$(openssl rand -base64 24)
VAULT_ROOT_TOKEN=anubis-workflows-$(openssl rand -hex 16)
POSTGRES_PASSWORD=$(openssl rand -base64 20)
EOF

# إنشاء الشبكات المعزولة
echo "🌐 إنشاء الشبكات المعزولة للأتمتة..."
docker network create anubis-workflows-net --driver bridge 2>/dev/null || true
docker network create anubis-automation-secure-net --driver bridge --internal 2>/dev/null || true

# بناء النظام
echo "🔨 بناء نظام الأتمتة..."
docker-compose build

# تشغيل خدمات البنية التحتية
echo "🗄️ تشغيل خدمات البنية التحتية..."
docker-compose up -d anubis-workflows-db anubis-workflows-vault anubis-automation-monitor

# انتظار تجهيز الخدمات
echo "⏳ انتظار تجهيز خدمات البنية التحتية..."
sleep 30

# تشغيل نظام n8n الرئيسي
echo "🔄 تشغيل نظام n8n الرئيسي..."
docker-compose up -d anubis-n8n

# تشغيل مجدول المهام
echo "⏰ تشغيل مجدول المهام..."
docker-compose up -d anubis-automation-scheduler

# التحقق من الحالة النهائية
echo "📊 فحص حالة النظام..."
sleep 20
docker-compose ps

# عرض معلومات الاتصال
echo ""
echo "✅ تم تشغيل نظام سير العمل والأتمتة في بيئة معزولة!"
echo ""
echo "🌐 الخدمات المتاحة:"
echo "   🔄 واجهة n8n: http://localhost:5678"
echo "   📊 مراقبة الأتمتة: http://localhost:9093"
echo "   🔐 مخزن الأسرار: http://localhost:8201"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات الشاملة: docker-compose logs -f"
echo "   سجلات n8n: docker-compose logs -f anubis-n8n"
echo "   حالة النظام: docker-compose ps"
echo "   إيقاف النظام: docker-compose down"
echo ""
echo "🔒 ملاحظات الأمان:"
echo "   - جميع الخدمات معزولة في شبكات منفصلة"
echo "   - بيانات الاعتماد مشفرة في Vault"
echo "   - المراقبة نشطة على جميع المكونات"
echo "   - سجلات الأمان محفوظة ومراقبة"
echo ""
echo "📚 للمزيد من المعلومات:"
echo "   - دليل الاستخدام: README.md"
echo "   - إعدادات الأمان: security/"
echo "   - مراقبة الأداء: monitoring/"
'''
        
        with open(self.base_path / "start_isolated_workflows.sh", 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        os.chmod(self.base_path / "start_isolated_workflows.sh", 0o755)
        
        # سكريبت الإيقاف
        stop_script = '''#!/bin/bash
# سكريبت إيقاف نظام الأتمتة المعزول

echo "🛑 إيقاف نظام سير العمل والأتمتة المعزول..."

cd workflows_and_automation

# إيقاف جميع الخدمات
echo "📱 إيقاف جميع خدمات الأتمتة..."
docker-compose down

# إزالة الشبكات (اختياري)
echo "🌐 تنظيف الشبكات (اختياري)..."
read -p "هل تريد إزالة الشبكات المعزولة؟ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker network rm anubis-workflows-net anubis-automation-secure-net 2>/dev/null || true
    echo "✅ تم تنظيف الشبكات"
fi

echo "✅ تم إيقاف نظام الأتمتة بنجاح"
'''
        
        with open(self.base_path / "stop_isolated_workflows.sh", 'w', encoding='utf-8') as f:
            f.write(stop_script)
        
        os.chmod(self.base_path / "stop_isolated_workflows.sh", 0o755)
        
        print("✅ تم إنشاء سكريبتات التشغيل والإيقاف")
    
    def run_isolation_creation(self):
        """تشغيل إنشاء نظام العزل"""
        print("🔄 بدء إنشاء نظام العزل للأتمتة")
        print("=" * 50)
        
        self.create_workflows_isolation_system()
        self.create_automation_security_configs()
        self.create_automation_startup_script()
        
        # إكمال السجل
        self.isolation_log["completion_status"] = "completed"
        self.isolation_log["total_isolation_features"] = len(self.isolation_log["isolation_features"])
        self.isolation_log["total_security_measures"] = len(self.isolation_log["security_measures"])
        
        return self.isolation_log


def main():
    """الدالة الرئيسية"""
    # فحص النظام أولاً
    inspector = WorkflowsAutomationInspector()
    
    print("🔄 تشغيل فحص أنظمة سير العمل والأتمتة مع إنشاء نظام العزل")
    print("=" * 60)
    
    # تشغيل الفحص
    report = inspector.run_comprehensive_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    # إنشاء نظام العزل
    print(f"\n{'='*60}")
    print("🐳 بدء إنشاء نظام العزل المتقدم...")
    
    isolation_log = inspector.isolation_creator.run_isolation_creation()
    
    # حفظ سجل العزل
    with open("workflows_isolation_creation_log.json", 'w', encoding='utf-8') as f:
        json.dump(isolation_log, f, ensure_ascii=False, indent=2)
    
    print(f"\n{'='*60}")
    print("🎉 تم إكمال فحص وإنشاء نظام العزل بنجاح!")
    
    print(f"\n🐳 ميزات العزل ({isolation_log['total_isolation_features']}):")
    for feature in isolation_log["isolation_features"]:
        print(f"   🔒 {feature}")
    
    print(f"\n🛡️ إجراءات الأمان ({isolation_log['total_security_measures']}):")
    for measure in isolation_log["security_measures"]:
        print(f"   🔐 {measure}")
    
    print(f"\n🚀 للتشغيل:")
    print(f"   bash workflows_and_automation/start_isolated_workflows.sh")
    
    print(f"\n💾 الملفات المنشأة:")
    print(f"   📋 تقرير الفحص: متاح في الملفات المحفوظة")
    print(f"   🐳 سجل العزل: workflows_isolation_creation_log.json")

if __name__ == "__main__":
    main()
