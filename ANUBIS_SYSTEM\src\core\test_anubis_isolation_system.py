#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار نظام العزل الشامل لأنوبيس
Anubis Isolation System Comprehensive Test

اختبار شامل لنظام العزل مع:
- فحص هيكل المجلدات
- فحص ملفات Docker
- فحص التكوينات
- فحص الأمان
- فحص المراقبة

مطور بالتعاون مع Gemini CLI و Ollama
"""

import json
import os
import sys
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any


class AnubisIsolationTester:
    """فاحص نظام العزل الشامل"""
    
    def __init__(self, isolation_dir: str = "anubis_isolated_system"):
        self.isolation_dir = Path(isolation_dir)
        self.test_results = {
            "test_timestamp": datetime.now().isoformat(),
            "isolation_dir": str(self.isolation_dir),
            "structure_tests": {},
            "docker_tests": {},
            "config_tests": {},
            "security_tests": {},
            "monitoring_tests": {},
            "overall_score": 0,
            "recommendations": []
        }
        
    def run_comprehensive_isolation_test(self):
        """تشغيل اختبار شامل لنظام العزل"""
        print("🧪 بدء الاختبار الشامل لنظام العزل")
        print("🏺 نظام أنوبيس المعزول")
        print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
        print("=" * 60)
        
        if not self.isolation_dir.exists():
            print(f"❌ مجلد العزل غير موجود: {self.isolation_dir}")
            return self.test_results
        
        # اختبار هيكل المجلدات
        self.test_directory_structure()
        
        # اختبار ملفات Docker
        self.test_docker_files()
        
        # اختبار التكوينات
        self.test_configurations()
        
        # اختبار الأمان
        self.test_security_measures()
        
        # اختبار المراقبة
        self.test_monitoring_system()
        
        # حساب النتيجة الإجمالية
        self.calculate_overall_score()
        
        # طباعة التقرير
        self.print_test_report()
        
        return self.test_results
    
    def test_directory_structure(self):
        """اختبار هيكل المجلدات"""
        print("\n📁 اختبار هيكل المجلدات...")
        
        required_dirs = [
            "services/anubis_enhanced",
            "services/universal_ai",
            "services/ollama", 
            "services/database",
            "services/api_gateway",
            "services/monitoring",
            "configs",
            "scripts",
            "volumes/database_data",
            "volumes/ollama_models",
            "volumes/logs",
            "volumes/monitoring_data",
            "networks",
            "security",
            "docs"
        ]
        
        structure_results = {}
        
        for dir_path in required_dirs:
            full_path = self.isolation_dir / dir_path
            exists = full_path.exists() and full_path.is_dir()
            structure_results[dir_path] = exists
            
            status = "✅" if exists else "❌"
            print(f"  {status} {dir_path}")
        
        self.test_results["structure_tests"] = structure_results
    
    def test_docker_files(self):
        """اختبار ملفات Docker"""
        print("\n🐳 اختبار ملفات Docker...")
        
        docker_results = {}
        
        # فحص docker-compose.yml
        compose_file = self.isolation_dir / "docker-compose.yml"
        if compose_file.exists():
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    compose_data = yaml.safe_load(f)
                
                # فحص الخدمات
                services = compose_data.get("services", {})
                expected_services = [
                    "anubis_enhanced", "universal_ai", "ollama_service",
                    "database_mysql", "api_gateway", "prometheus", "grafana"
                ]
                
                services_check = all(service in services for service in expected_services)
                docker_results["docker_compose_services"] = services_check
                
                # فحص الشبكات
                networks = compose_data.get("networks", {})
                networks_check = "anubis_network" in networks
                docker_results["docker_compose_networks"] = networks_check
                
                # فحص الأحجام
                volumes = compose_data.get("volumes", {})
                expected_volumes = ["database_data", "ollama_models", "logs", "monitoring_data"]
                volumes_check = all(vol in volumes for vol in expected_volumes)
                docker_results["docker_compose_volumes"] = volumes_check
                
                print(f"  ✅ docker-compose.yml موجود ومكتمل")
                print(f"    📦 الخدمات: {'✅' if services_check else '❌'} ({len(services)}/7)")
                print(f"    🌐 الشبكات: {'✅' if networks_check else '❌'}")
                print(f"    💾 الأحجام: {'✅' if volumes_check else '❌'} ({len(volumes)}/4)")
                
            except Exception as e:
                docker_results["docker_compose_valid"] = False
                print(f"  ❌ خطأ في قراءة docker-compose.yml: {e}")
        else:
            docker_results["docker_compose_exists"] = False
            print(f"  ❌ docker-compose.yml غير موجود")
        
        # فحص Dockerfiles
        dockerfiles = [
            "services/anubis_enhanced/Dockerfile",
            "services/universal_ai/Dockerfile"
        ]
        
        for dockerfile_path in dockerfiles:
            dockerfile = self.isolation_dir / dockerfile_path
            exists = dockerfile.exists()
            docker_results[f"dockerfile_{dockerfile_path.replace('/', '_')}"] = exists
            
            status = "✅" if exists else "❌"
            print(f"  {status} {dockerfile_path}")
        
        self.test_results["docker_tests"] = docker_results
    
    def test_configurations(self):
        """اختبار ملفات التكوين"""
        print("\n⚙️ اختبار ملفات التكوين...")
        
        config_results = {}
        
        # فحص تكوين Nginx
        nginx_config = self.isolation_dir / "services/api_gateway/nginx.conf"
        if nginx_config.exists():
            try:
                with open(nginx_config, 'r') as f:
                    nginx_content = f.read()
                
                # فحص وجود upstream configurations
                has_anubis_upstream = "upstream anubis_enhanced" in nginx_content
                has_universal_upstream = "upstream universal_ai" in nginx_content
                has_proxy_config = "proxy_pass" in nginx_content
                
                config_results["nginx_upstreams"] = has_anubis_upstream and has_universal_upstream
                config_results["nginx_proxy"] = has_proxy_config
                
                print(f"  ✅ nginx.conf موجود ومكتمل")
                print(f"    🔄 Upstreams: {'✅' if config_results['nginx_upstreams'] else '❌'}")
                print(f"    🌐 Proxy: {'✅' if has_proxy_config else '❌'}")
                
            except Exception as e:
                config_results["nginx_valid"] = False
                print(f"  ❌ خطأ في قراءة nginx.conf: {e}")
        else:
            config_results["nginx_exists"] = False
            print(f"  ❌ nginx.conf غير موجود")
        
        # فحص تكوين Prometheus
        prometheus_config = self.isolation_dir / "services/monitoring/prometheus.yml"
        if prometheus_config.exists():
            try:
                with open(prometheus_config, 'r') as f:
                    prometheus_data = yaml.safe_load(f)
                
                scrape_configs = prometheus_data.get("scrape_configs", [])
                job_names = [config.get("job_name") for config in scrape_configs]
                
                expected_jobs = ["anubis_enhanced", "universal_ai", "ollama"]
                jobs_check = all(job in job_names for job in expected_jobs)
                
                config_results["prometheus_jobs"] = jobs_check
                
                print(f"  ✅ prometheus.yml موجود ومكتمل")
                print(f"    📊 Jobs: {'✅' if jobs_check else '❌'} ({len(job_names)}/3)")
                
            except Exception as e:
                config_results["prometheus_valid"] = False
                print(f"  ❌ خطأ في قراءة prometheus.yml: {e}")
        else:
            config_results["prometheus_exists"] = False
            print(f"  ❌ prometheus.yml غير موجود")
        
        self.test_results["config_tests"] = config_results
    
    def test_security_measures(self):
        """اختبار إجراءات الأمان"""
        print("\n🛡️ اختبار إجراءات الأمان...")
        
        security_results = {}
        
        # فحص docker-compose للأمان
        compose_file = self.isolation_dir / "docker-compose.yml"
        if compose_file.exists():
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    compose_data = yaml.safe_load(f)
                
                services = compose_data.get("services", {})
                
                # فحص حدود الموارد
                has_resource_limits = True
                for service_name, service_config in services.items():
                    deploy = service_config.get("deploy", {})
                    resources = deploy.get("resources", {})
                    limits = resources.get("limits", {})
                    
                    if not limits.get("cpus") or not limits.get("memory"):
                        has_resource_limits = False
                        break
                
                security_results["resource_limits"] = has_resource_limits
                
                # فحص الشبكة المعزولة
                networks = compose_data.get("networks", {})
                has_custom_network = "anubis_network" in networks
                security_results["network_isolation"] = has_custom_network
                
                # فحص المجلدات المقيدة
                has_readonly_configs = True
                for service_name, service_config in services.items():
                    volumes = service_config.get("volumes", [])
                    for volume in volumes:
                        if "configs" in volume and ":ro" not in volume:
                            has_readonly_configs = False
                            break
                
                security_results["readonly_configs"] = has_readonly_configs
                
                print(f"  📊 حدود الموارد: {'✅' if has_resource_limits else '❌'}")
                print(f"  🌐 عزل الشبكة: {'✅' if has_custom_network else '❌'}")
                print(f"  🔒 تكوينات للقراءة فقط: {'✅' if has_readonly_configs else '❌'}")
                
            except Exception as e:
                security_results["security_check_error"] = str(e)
                print(f"  ❌ خطأ في فحص الأمان: {e}")
        
        # فحص Dockerfiles للمستخدمين غير الجذر
        dockerfiles = [
            "services/anubis_enhanced/Dockerfile",
            "services/universal_ai/Dockerfile"
        ]
        
        non_root_users = True
        for dockerfile_path in dockerfiles:
            dockerfile = self.isolation_dir / dockerfile_path
            if dockerfile.exists():
                try:
                    with open(dockerfile, 'r') as f:
                        content = f.read()
                    
                    if "USER " not in content or "useradd" not in content:
                        non_root_users = False
                        break
                        
                except Exception:
                    non_root_users = False
                    break
        
        security_results["non_root_users"] = non_root_users
        print(f"  👤 مستخدمين غير جذر: {'✅' if non_root_users else '❌'}")
        
        self.test_results["security_tests"] = security_results
    
    def test_monitoring_system(self):
        """اختبار نظام المراقبة"""
        print("\n📊 اختبار نظام المراقبة...")
        
        monitoring_results = {}
        
        # فحص وجود خدمات المراقبة في docker-compose
        compose_file = self.isolation_dir / "docker-compose.yml"
        if compose_file.exists():
            try:
                with open(compose_file, 'r', encoding='utf-8') as f:
                    compose_data = yaml.safe_load(f)
                
                services = compose_data.get("services", {})
                
                has_prometheus = "prometheus" in services
                has_grafana = "grafana" in services
                
                monitoring_results["prometheus_service"] = has_prometheus
                monitoring_results["grafana_service"] = has_grafana
                
                print(f"  📈 Prometheus: {'✅' if has_prometheus else '❌'}")
                print(f"  📊 Grafana: {'✅' if has_grafana else '❌'}")
                
                # فحص المنافذ
                if has_prometheus:
                    prometheus_ports = services["prometheus"].get("ports", [])
                    has_prometheus_port = any("9090" in port for port in prometheus_ports)
                    monitoring_results["prometheus_port"] = has_prometheus_port
                    print(f"    📡 منفذ Prometheus: {'✅' if has_prometheus_port else '❌'}")
                
                if has_grafana:
                    grafana_ports = services["grafana"].get("ports", [])
                    has_grafana_port = any("3000" in port for port in grafana_ports)
                    monitoring_results["grafana_port"] = has_grafana_port
                    print(f"    📡 منفذ Grafana: {'✅' if has_grafana_port else '❌'}")
                
            except Exception as e:
                monitoring_results["monitoring_check_error"] = str(e)
                print(f"  ❌ خطأ في فحص المراقبة: {e}")
        
        # فحص وثائق المراقبة
        monitoring_docs = self.isolation_dir / "docs/monitoring.md"
        docs_exist = monitoring_docs.exists()
        monitoring_results["monitoring_docs"] = docs_exist
        print(f"  📚 وثائق المراقبة: {'✅' if docs_exist else '❌'}")
        
        self.test_results["monitoring_tests"] = monitoring_results
    
    def calculate_overall_score(self):
        """حساب النقاط الإجمالية"""
        total_tests = 0
        passed_tests = 0
        
        # حساب نتائج كل فئة
        for category_name, category_results in self.test_results.items():
            if category_name.endswith("_tests") and isinstance(category_results, dict):
                for test_name, result in category_results.items():
                    total_tests += 1
                    if result is True:
                        passed_tests += 1
        
        # حساب النسبة المئوية
        if total_tests > 0:
            score = (passed_tests / total_tests) * 100
            self.test_results["overall_score"] = round(score, 1)
        else:
            self.test_results["overall_score"] = 0
        
        # إضافة التوصيات
        self.generate_recommendations()
    
    def generate_recommendations(self):
        """إنشاء التوصيات"""
        recommendations = []
        
        score = self.test_results["overall_score"]
        
        if score >= 90:
            recommendations.append("🎉 نظام العزل ممتاز - جاهز للإنتاج!")
        elif score >= 75:
            recommendations.append("🟢 نظام العزل جيد - يحتاج تحسينات طفيفة")
        elif score >= 50:
            recommendations.append("🟡 نظام العزل مقبول - يحتاج تحسينات")
        else:
            recommendations.append("🔴 نظام العزل يحتاج تحسينات كبيرة")
        
        # توصيات محددة
        if not self.test_results.get("docker_tests", {}).get("docker_compose_services", True):
            recommendations.append("🐳 تحقق من خدمات docker-compose.yml")
        
        if not self.test_results.get("security_tests", {}).get("resource_limits", True):
            recommendations.append("🛡️ أضف حدود موارد لجميع الخدمات")
        
        if not self.test_results.get("monitoring_tests", {}).get("prometheus_service", True):
            recommendations.append("📊 أضف خدمة Prometheus للمراقبة")
        
        self.test_results["recommendations"] = recommendations
    
    def print_test_report(self):
        """طباعة تقرير الاختبار"""
        print("\n" + "="*60)
        print("🧪 تقرير اختبار نظام العزل الشامل")
        print("🏺 نظام أنوبيس المعزول")
        print("="*60)
        
        print(f"📊 النقاط الإجمالية: {self.test_results['overall_score']}/100")
        print(f"📁 مجلد العزل: {self.test_results['isolation_dir']}")
        
        # نتائج كل فئة
        categories = [
            ("structure_tests", "📁 هيكل المجلدات"),
            ("docker_tests", "🐳 ملفات Docker"),
            ("config_tests", "⚙️ التكوينات"),
            ("security_tests", "🛡️ الأمان"),
            ("monitoring_tests", "📊 المراقبة")
        ]
        
        for category_key, category_name in categories:
            if category_key in self.test_results:
                category_results = self.test_results[category_key]
                if isinstance(category_results, dict):
                    passed = sum(1 for result in category_results.values() if result is True)
                    total = len(category_results)
                    percentage = (passed / total * 100) if total > 0 else 0
                    
                    print(f"\n{category_name}: {passed}/{total} ({percentage:.1f}%)")
                    
                    for test_name, result in category_results.items():
                        status = "✅" if result else "❌"
                        print(f"  {status} {test_name}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        for recommendation in self.test_results["recommendations"]:
            print(f"  {recommendation}")
        
        print("\n🤖 تم تطوير هذا الاختبار بالتعاون مع Gemini CLI و Ollama")
        print("="*60)
    
    def save_test_report(self, filename: str = None) -> str:
        """حفظ تقرير الاختبار"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_isolation_test_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الاختبار في: {filename}")
        return filename


def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام العزل الشامل لأنوبيس")
    print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
    
    # إنشاء الفاحص
    tester = AnubisIsolationTester()
    
    # تشغيل الاختبار الشامل
    results = tester.run_comprehensive_isolation_test()
    
    # حفظ التقرير
    report_file = tester.save_test_report()
    
    # تحديد كود الخروج
    if results["overall_score"] >= 75:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
