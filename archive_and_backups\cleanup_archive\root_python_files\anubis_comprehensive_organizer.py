#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ منظم نظام أنوبيس الشامل
Anubis Comprehensive System Organizer
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

class AnubisSystemOrganizer:
    def __init__(self):
        self.base_path = Path(".")
        self.organization_log = {
            "timestamp": datetime.now().isoformat(),
            "organizer": "Anubis Comprehensive System Organizer",
            "organization_type": "complete_system_restructure",
            "actions_taken": [],
            "files_moved": [],
            "directories_created": [],
            "services_catalogued": [],
            "completion_status": "in_progress"
        }
        
        # هيكل التنظيم المطلوب
        self.target_structure = {
            "scripts": {
                "description": "سكريبتات الفحص والتحليل",
                "patterns": ["*_inspector*.py", "*_checker*.py", "*_analyzer*.py"]
            },
            "reports": {
                "description": "التقارير والتحليلات",
                "patterns": ["*_report_*.json", "*_inspection_*.json", "*_analysis_*.json"]
            },
            "logs": {
                "description": "سجلات العمليات",
                "patterns": ["*_log*.json", "*_completed*.json"]
            },
            "isolation_configs": {
                "description": "ملفات العزل والحاويات",
                "patterns": ["*isolation*.py", "create_*_isolation*.py", "Dockerfile*", "docker-compose*.yml"]
            },
            "documentation": {
                "description": "التوثيق والأدلة",
                "patterns": ["*.md", "README*", "*_GUIDE*", "*_SUMMARY*"]
            },
            "utilities": {
                "description": "أدوات مساعدة",
                "patterns": ["*_fixes*.py", "*optimizer*.py", "setup_*.bat"]
            }
        }
    
    def analyze_current_structure(self):
        """تحليل الهيكل الحالي"""
        print("🔍 تحليل الهيكل الحالي للمشروع...")
        
        current_structure = {
            "root_files": [],
            "directories": {},
            "total_files": 0,
            "total_directories": 0
        }
        
        # فحص الملفات في المجلد الرئيسي
        for item in self.base_path.iterdir():
            if item.is_file() and not item.name.startswith('.'):
                current_structure["root_files"].append({
                    "name": item.name,
                    "size": item.stat().st_size,
                    "type": item.suffix,
                    "category": self._categorize_file(item.name)
                })
                current_structure["total_files"] += 1
            elif item.is_dir() and not item.name.startswith('.') and item.name != '__pycache__':
                dir_info = self._analyze_directory(item)
                current_structure["directories"][item.name] = dir_info
                current_structure["total_directories"] += 1
        
        print(f"📊 تم العثور على {current_structure['total_files']} ملف في المجلد الرئيسي")
        print(f"📁 تم العثور على {current_structure['total_directories']} مجلد")
        
        return current_structure
    
    def _categorize_file(self, filename):
        """تصنيف الملف حسب النوع"""
        filename_lower = filename.lower()
        
        if any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["scripts"]["patterns"]):
            return "scripts"
        elif any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["reports"]["patterns"]):
            return "reports"
        elif any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["logs"]["patterns"]):
            return "logs"
        elif any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["isolation_configs"]["patterns"]):
            return "isolation_configs"
        elif any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["documentation"]["patterns"]):
            return "documentation"
        elif any(pattern.replace("*", "") in filename_lower for pattern in self.target_structure["utilities"]["patterns"]):
            return "utilities"
        else:
            return "miscellaneous"
    
    def _analyze_directory(self, dir_path):
        """تحليل محتويات المجلد"""
        dir_info = {
            "files_count": 0,
            "subdirs_count": 0,
            "total_size": 0,
            "file_types": {},
            "has_isolation_system": False,
            "has_documentation": False
        }
        
        try:
            for item in dir_path.rglob("*"):
                if item.is_file():
                    dir_info["files_count"] += 1
                    dir_info["total_size"] += item.stat().st_size
                    
                    ext = item.suffix.lower()
                    if ext not in dir_info["file_types"]:
                        dir_info["file_types"][ext] = 0
                    dir_info["file_types"][ext] += 1
                    
                    # فحص وجود نظام العزل
                    if "docker" in item.name.lower() or "isolation" in item.name.lower():
                        dir_info["has_isolation_system"] = True
                    
                    # فحص وجود التوثيق
                    if item.name.lower() in ["readme.md", "readme.txt"] or "guide" in item.name.lower():
                        dir_info["has_documentation"] = True
                
                elif item.is_dir():
                    dir_info["subdirs_count"] += 1
        
        except PermissionError:
            dir_info["error"] = "Permission denied"
        
        return dir_info
    
    def create_organized_structure(self):
        """إنشاء الهيكل المنظم"""
        print("🏗️ إنشاء الهيكل المنظم...")
        
        # إنشاء المجلدات الجديدة
        for category, info in self.target_structure.items():
            category_path = self.base_path / category
            if not category_path.exists():
                category_path.mkdir(exist_ok=True)
                self.organization_log["directories_created"].append(category)
                print(f"📁 تم إنشاء مجلد: {category}")
        
        # إنشاء مجلدات فرعية مخصصة
        specialized_dirs = {
            "scripts/inspectors": "سكريبتات الفحص",
            "scripts/analyzers": "سكريبتات التحليل",
            "scripts/checkers": "سكريبتات التحقق",
            "reports/inspection": "تقارير الفحص",
            "reports/analysis": "تقارير التحليل", 
            "reports/system": "تقارير النظام",
            "logs/operations": "سجلات العمليات",
            "logs/isolation": "سجلات العزل",
            "isolation_configs/containers": "حاويات Docker",
            "isolation_configs/networks": "إعدادات الشبكات",
            "isolation_configs/security": "إعدادات الأمان",
            "documentation/guides": "الأدلة والإرشادات",
            "documentation/reports": "تقارير التوثيق",
            "utilities/helpers": "أدوات مساعدة",
            "utilities/optimizers": "محسنات النظام"
        }
        
        for dir_path, description in specialized_dirs.items():
            full_path = self.base_path / dir_path
            if not full_path.exists():
                full_path.mkdir(parents=True, exist_ok=True)
                self.organization_log["directories_created"].append(dir_path)
    
    def organize_root_files(self):
        """تنظيم الملفات في المجلد الرئيسي"""
        print("📋 تنظيم الملفات في المجلد الرئيسي...")
        
        # قائمة الملفات التي يجب أن تبقى في المجلد الرئيسي
        keep_in_root = {
            ".env.template", ".gitignore", "docker-compose.yml", 
            "Dockerfile", "start_anubis_isolated.sh"
        }
        
        moved_files = 0
        
        for item in self.base_path.iterdir():
            if item.is_file() and item.name not in keep_in_root and not item.name.startswith('.'):
                category = self._categorize_file(item.name)
                
                if category != "miscellaneous":
                    # تحديد المجلد الفرعي المناسب
                    target_subdir = self._get_target_subdir(item.name, category)
                    target_path = self.base_path / category / target_subdir
                    
                    target_file = target_path / item.name
                    
                    try:
                        if not target_file.exists():
                            shutil.move(str(item), str(target_file))
                            moved_files += 1
                            self.organization_log["files_moved"].append({
                                "file": item.name,
                                "from": "root",
                                "to": f"{category}/{target_subdir}",
                                "reason": f"Categorized as {category}"
                            })
                            print(f"📦 نُقل {item.name} إلى {category}/{target_subdir}")
                    except Exception as e:
                        print(f"❌ خطأ في نقل {item.name}: {e}")
        
        print(f"✅ تم نقل {moved_files} ملف")
        return moved_files
    
    def _get_target_subdir(self, filename, category):
        """تحديد المجلد الفرعي المناسب للملف"""
        filename_lower = filename.lower()
        
        if category == "scripts":
            if "inspector" in filename_lower:
                return "inspectors"
            elif "analyzer" in filename_lower:
                return "analyzers"
            elif "checker" in filename_lower:
                return "checkers"
            else:
                return "."
        
        elif category == "reports":
            if "inspection" in filename_lower:
                return "inspection"
            elif "analysis" in filename_lower:
                return "analysis"
            else:
                return "system"
        
        elif category == "logs":
            if "isolation" in filename_lower:
                return "isolation"
            else:
                return "operations"
        
        elif category == "isolation_configs":
            if "docker" in filename_lower:
                return "containers"
            elif "network" in filename_lower:
                return "networks"
            else:
                return "security"
        
        elif category == "documentation":
            if "guide" in filename_lower:
                return "guides"
            else:
                return "reports"
        
        elif category == "utilities":
            if "optimizer" in filename_lower:
                return "optimizers"
            else:
                return "helpers"
        
        return "."
    
    def catalog_all_services(self):
        """فهرسة جميع الخدمات الموجودة"""
        print("📋 فهرسة جميع الخدمات الموجودة...")
        
        services_catalog = {
            "main_systems": {},
            "ai_systems": {},
            "workflow_systems": {},
            "database_systems": {},
            "monitoring_systems": {},
            "isolation_systems": {},
            "utility_systems": {}
        }
        
        # فحص الأنظمة الرئيسية
        main_system_path = self.base_path / "anubis_main_system"
        if main_system_path.exists():
            services_catalog["main_systems"]["anubis_core"] = {
                "path": "anubis_main_system",
                "status": "active",
                "components": self._scan_system_components(main_system_path),
                "isolation": self._check_isolation_system(main_system_path),
                "description": "النظام الرئيسي لأنوبيس"
            }
        
        # فحص أنظمة الذكاء الاصطناعي
        ai_system_path = self.base_path / "universal_ai_system"
        if ai_system_path.exists():
            services_catalog["ai_systems"]["universal_ai"] = {
                "path": "universal_ai_system",
                "status": "active",
                "components": self._scan_system_components(ai_system_path),
                "isolation": self._check_isolation_system(ai_system_path),
                "description": "نظام الذكاء الاصطناعي الشامل"
            }
        
        # فحص أنظمة سير العمل
        workflow_path = self.base_path / "workflows_and_automation"
        if workflow_path.exists():
            services_catalog["workflow_systems"]["workflows_automation"] = {
                "path": "workflows_and_automation",
                "status": "active",
                "components": self._scan_system_components(workflow_path),
                "isolation": self._check_isolation_system(workflow_path),
                "description": "نظام سير العمل والأتمتة"
            }
        
        # فحص أنظمة قواعد البيانات
        database_path = self.base_path / "database"
        if database_path.exists():
            services_catalog["database_systems"]["anubis_database"] = {
                "path": "database",
                "status": "active",
                "components": self._scan_system_components(database_path),
                "isolation": self._check_isolation_system(database_path),
                "description": "نظام قواعد البيانات"
            }
        
        # فحص أنظمة المراقبة
        workspace_path = self.base_path / "workspace"
        if workspace_path.exists():
            services_catalog["monitoring_systems"]["workspace"] = {
                "path": "workspace",
                "status": "active",
                "components": self._scan_system_components(workspace_path),
                "isolation": self._check_isolation_system(workspace_path),
                "description": "بيئة العمل والمراقبة"
            }
        
        # فحص أنظمة العزل
        isolation_path = self.base_path / "isolation_systems"
        if isolation_path.exists():
            services_catalog["isolation_systems"]["security_isolation"] = {
                "path": "isolation_systems",
                "status": "active",
                "components": self._scan_system_components(isolation_path),
                "isolation": self._check_isolation_system(isolation_path),
                "description": "أنظمة العزل والأمان"
            }
        
        # فحص الأدوات المساعدة
        tools_path = self.base_path / "tools_and_utilities"
        if tools_path.exists():
            services_catalog["utility_systems"]["tools_utilities"] = {
                "path": "tools_and_utilities",
                "status": "active",
                "components": self._scan_system_components(tools_path),
                "isolation": self._check_isolation_system(tools_path),
                "description": "الأدوات والمرافق المساعدة"
            }
        
        self.organization_log["services_catalogued"] = services_catalog
        return services_catalog
    
    def _scan_system_components(self, system_path):
        """مسح مكونات النظام"""
        components = {
            "config_files": 0,
            "python_scripts": 0,
            "docker_files": 0,
            "documentation": 0,
            "data_files": 0
        }
        
        try:
            for item in system_path.rglob("*"):
                if item.is_file():
                    ext = item.suffix.lower()
                    name = item.name.lower()
                    
                    if ext in [".json", ".yml", ".yaml", ".cfg", ".ini"]:
                        components["config_files"] += 1
                    elif ext == ".py":
                        components["python_scripts"] += 1
                    elif "docker" in name or "compose" in name:
                        components["docker_files"] += 1
                    elif ext in [".md", ".txt", ".rst"]:
                        components["documentation"] += 1
                    else:
                        components["data_files"] += 1
        
        except PermissionError:
            components["error"] = "Permission denied"
        
        return components
    
    def _check_isolation_system(self, system_path):
        """فحص نظام العزل"""
        isolation_info = {
            "has_dockerfile": False,
            "has_compose": False,
            "has_isolation_script": False,
            "isolation_level": "none"
        }
        
        try:
            for item in system_path.rglob("*"):
                if item.is_file():
                    name = item.name.lower()
                    
                    if "dockerfile" in name:
                        isolation_info["has_dockerfile"] = True
                    elif "docker-compose" in name or "compose" in name:
                        isolation_info["has_compose"] = True
                    elif "isolation" in name and item.suffix == ".py":
                        isolation_info["has_isolation_script"] = True
            
            # تحديد مستوى العزل
            if isolation_info["has_dockerfile"] and isolation_info["has_compose"]:
                isolation_info["isolation_level"] = "advanced"
            elif isolation_info["has_dockerfile"] or isolation_info["has_compose"]:
                isolation_info["isolation_level"] = "basic"
            elif isolation_info["has_isolation_script"]:
                isolation_info["isolation_level"] = "script"
        
        except PermissionError:
            isolation_info["error"] = "Permission denied"
        
        return isolation_info
    
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        print("📊 إنشاء التقرير الشامل...")
        
        current_structure = self.analyze_current_structure()
        services_catalog = self.catalog_all_services()
        
        comprehensive_report = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "generator": "Anubis Comprehensive System Organizer",
                "version": "1.0"
            },
            
            "organization_summary": {
                "total_files_organized": len(self.organization_log["files_moved"]),
                "directories_created": len(self.organization_log["directories_created"]),
                "actions_taken": len(self.organization_log["actions_taken"])
            },
            
            "system_structure": {
                "current_structure": current_structure,
                "organized_categories": list(self.target_structure.keys()),
                "target_structure": self.target_structure
            },
            
            "services_inventory": services_catalog,
            
            "isolation_systems_status": self._summarize_isolation_status(services_catalog),
            
            "recommendations": self._generate_recommendations(current_structure, services_catalog),
            
            "organization_log": self.organization_log
        }
        
        return comprehensive_report
    
    def _summarize_isolation_status(self, services_catalog):
        """تلخيص حالة أنظمة العزل"""
        isolation_summary = {
            "total_systems": 0,
            "systems_with_advanced_isolation": 0,
            "systems_with_basic_isolation": 0,
            "systems_without_isolation": 0,
            "isolation_coverage": 0
        }
        
        for category, systems in services_catalog.items():
            for system_name, system_info in systems.items():
                isolation_summary["total_systems"] += 1
                
                isolation_level = system_info.get("isolation", {}).get("isolation_level", "none")
                
                if isolation_level == "advanced":
                    isolation_summary["systems_with_advanced_isolation"] += 1
                elif isolation_level in ["basic", "script"]:
                    isolation_summary["systems_with_basic_isolation"] += 1
                else:
                    isolation_summary["systems_without_isolation"] += 1
        
        if isolation_summary["total_systems"] > 0:
            isolation_summary["isolation_coverage"] = (
                (isolation_summary["systems_with_advanced_isolation"] + 
                 isolation_summary["systems_with_basic_isolation"]) / 
                isolation_summary["total_systems"] * 100
            )
        
        return isolation_summary
    
    def _generate_recommendations(self, current_structure, services_catalog):
        """إنشاء التوصيات"""
        recommendations = []
        
        # توصيات التنظيم
        if len(current_structure["root_files"]) > 10:
            recommendations.append("🗂️ نقل المزيد من الملفات من المجلد الرئيسي إلى مجلدات فرعية")
        
        # توصيات العزل
        isolation_summary = self._summarize_isolation_status(services_catalog)
        if isolation_summary["isolation_coverage"] < 80:
            recommendations.append("🐳 تحسين أنظمة العزل للخدمات المتبقية")
        
        # توصيات التوثيق
        systems_without_docs = 0
        for category, systems in services_catalog.items():
            for system_name, system_info in systems.items():
                if system_info.get("components", {}).get("documentation", 0) == 0:
                    systems_without_docs += 1
        
        if systems_without_docs > 0:
            recommendations.append(f"📚 إضافة توثيق لـ {systems_without_docs} نظام")
        
        # توصيات الأمان
        recommendations.extend([
            "🔒 تطبيق سياسات أمان موحدة على جميع الخدمات",
            "📊 تحسين أنظمة المراقبة والتقارير",
            "🔄 تطوير نظام نسخ احتياطي موحد",
            "⚡ تحسين الأداء العام للنظام"
        ])
        
        return recommendations
    
    def run_complete_organization(self):
        """تشغيل التنظيم الشامل"""
        print("🗂️ بدء التنظيم الشامل لنظام أنوبيس")
        print("=" * 60)
        
        # تحليل الهيكل الحالي
        current_structure = self.analyze_current_structure()
        
        # إنشاء الهيكل المنظم
        self.create_organized_structure()
        
        # تنظيم الملفات
        moved_files = self.organize_root_files()
        
        # فهرسة الخدمات
        services_catalog = self.catalog_all_services()
        
        # إنشاء التقرير الشامل
        comprehensive_report = self.generate_comprehensive_report()
        
        # إكمال السجل
        self.organization_log["completion_status"] = "completed"
        self.organization_log["actions_taken"].extend([
            f"تحليل {current_structure['total_files']} ملف",
            f"إنشاء {len(self.organization_log['directories_created'])} مجلد",
            f"نقل {moved_files} ملف",
            f"فهرسة {len(services_catalog)} فئة خدمات"
        ])
        
        return comprehensive_report
    
    def save_organization_report(self, report):
        """حفظ تقرير التنظيم"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_comprehensive_organization_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير الشامل في: {filename}")
        return filename
    
    def print_organization_summary(self, report):
        """طباعة ملخص التنظيم"""
        print("\n" + "="*60)
        print("📋 ملخص التنظيم الشامل لنظام أنوبيس")
        print("="*60)
        
        summary = report["organization_summary"]
        print(f"\n📊 إحصائيات التنظيم:")
        print(f"   📦 ملفات منظمة: {summary['total_files_organized']}")
        print(f"   📁 مجلدات منشأة: {summary['directories_created']}")
        print(f"   ⚡ إجراءات منفذة: {summary['actions_taken']}")
        
        services = report["services_inventory"]
        total_services = sum(len(systems) for systems in services.values())
        print(f"\n🛠️ الخدمات المفهرسة:")
        print(f"   📊 إجمالي الخدمات: {total_services}")
        
        for category, systems in services.items():
            if systems:
                print(f"   📂 {category}: {len(systems)} خدمة")
        
        isolation = report["isolation_systems_status"]
        print(f"\n🐳 حالة أنظمة العزل:")
        print(f"   📊 التغطية: {isolation['isolation_coverage']:.1f}%")
        print(f"   🔒 عزل متقدم: {isolation['systems_with_advanced_isolation']}")
        print(f"   🔧 عزل أساسي: {isolation['systems_with_basic_isolation']}")
        print(f"   ⚠️ بدون عزل: {isolation['systems_without_isolation']}")
        
        print(f"\n💡 التوصيات الرئيسية:")
        for rec in report["recommendations"][:5]:
            print(f"   {rec}")
        
        print("\n" + "="*60)
        print("✅ انتهى التنظيم الشامل لنظام أنوبيس")
        print("="*60)

def main():
    """الدالة الرئيسية"""
    organizer = AnubisSystemOrganizer()
    
    # تشغيل التنظيم الشامل
    report = organizer.run_complete_organization()
    
    # طباعة الملخص
    organizer.print_organization_summary(report)
    
    # حفظ التقرير
    organizer.save_organization_report(report)
    
    return report

if __name__ == "__main__":
    main()
