#!/usr/bin/env python3
"""
🏺 قاعدة بيانات نظام أنوبيس المحسنة
Enhanced Anubis System Database

مطور بالتعاون مع Gemini CLI مع تحسينات الأمان والأداء
"""

import json
import logging
import os
import re
import sqlite3
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import mysql.connector
from mysql.connector import pooling


class DatabaseSecurityError(Exception):
    """استثناء مخصص لأخطاء الأمان"""
    pass


class DatabaseConnectionError(Exception):
    """استثناء مخصص لأخطاء الاتصال"""
    pass


class EnhancedAnubisDatabase:
    """فئة إدارة قاعدة بيانات نظام أنوبيس المحسنة"""

    def __init__(self, db_type: str = "sqlite", config: Dict[str, Any] = None):
        """
        تهيئة قاعدة البيانات المحسنة

        Args:
            db_type: نوع قاعدة البيانات (sqlite, mysql)
            config: إعدادات الاتصال
        """
        self.db_type = db_type.lower()
        self.config = config or {}
        self.connection_pool = None
        self.connection = None
        self.cursor = None
        
        # إعداد نظام السجلات
        self.logger = logging.getLogger(__name__)
        
        # التحقق من صحة نوع قاعدة البيانات
        if self.db_type not in ["sqlite", "mysql"]:
            raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {self.db_type}")

        # إعدادات افتراضية آمنة
        if self.db_type == "sqlite":
            self.db_path = self.config.get("db_path", "database/anubis_enhanced.db")
            # التأكد من أن المسار آمن
            self._validate_sqlite_path(self.db_path)
            
        elif self.db_type == "mysql":
            self.mysql_config = self._prepare_mysql_config()
            self._setup_connection_pool()

        # إنشاء مجلد قاعدة البيانات
        Path("database").mkdir(exist_ok=True)
        
        self.logger.info(f"🏺 تم تهيئة قاعدة بيانات أنوبيس المحسنة - النوع: {self.db_type}")

    def _validate_sqlite_path(self, path: str):
        """التحقق من صحة مسار SQLite"""
        # منع Path Traversal attacks
        if ".." in path or path.startswith("/"):
            raise DatabaseSecurityError("مسار قاعدة البيانات غير آمن")
        
        # التأكد من أن المسار ينتهي بـ .db
        if not path.endswith('.db'):
            raise DatabaseSecurityError("امتداد قاعدة البيانات يجب أن يكون .db")

    def _prepare_mysql_config(self) -> Dict[str, Any]:
        """إعداد تكوين MySQL آمن"""
        # التحقق من صحة اسم قاعدة البيانات
        database_name = self.config.get("database", "anubis_system")
        if not self._is_valid_identifier(database_name):
            raise DatabaseSecurityError(f"اسم قاعدة البيانات غير آمن: {database_name}")
        
        return {
            "host": self.config.get("host", "localhost"),
            "port": int(self.config.get("port", 3306)),
            "user": self.config.get("user", "root"),
            "password": self.config.get("password", ""),
            "database": database_name,
            "charset": "utf8mb4",
            "collation": "utf8mb4_unicode_ci",
            "autocommit": False,  # للتحكم في المعاملات
            "raise_on_warnings": True
        }

    def _is_valid_identifier(self, identifier: str) -> bool:
        """التحقق من صحة معرف قاعدة البيانات"""
        # يجب أن يحتوي فقط على أحرف وأرقام وشرطة سفلية
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, identifier)) and len(identifier) <= 64

    def _setup_connection_pool(self):
        """إعداد مجموعة الاتصالات لـ MySQL"""
        try:
            pool_config = self.mysql_config.copy()
            pool_config.update({
                "pool_name": "anubis_pool",
                "pool_size": self.config.get("pool_size", 5),
                "pool_reset_session": True
            })
            
            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            self.logger.info("✅ تم إعداد مجموعة اتصالات MySQL")
            
        except Exception as e:
            self.logger.error(f"❌ فشل في إعداد مجموعة الاتصالات: {e}")
            raise DatabaseConnectionError(f"فشل في إعداد مجموعة الاتصالات: {e}")

    @contextmanager
    def get_connection(self):
        """الحصول على اتصال من المجموعة مع إدارة تلقائية"""
        connection = None
        try:
            if self.db_type == "sqlite":
                connection = sqlite3.connect(self.db_path)
                connection.row_factory = sqlite3.Row
            elif self.db_type == "mysql":
                if self.connection_pool:
                    connection = self.connection_pool.get_connection()
                else:
                    connection = mysql.connector.connect(**self.mysql_config)
            
            yield connection
            
        except Exception as e:
            if connection:
                connection.rollback()
            self.logger.error(f"❌ خطأ في الاتصال: {e}")
            raise DatabaseConnectionError(f"خطأ في الاتصال: {e}")
        finally:
            if connection:
                connection.close()

    def connect(self) -> bool:
        """إنشاء اتصال مع قاعدة البيانات"""
        try:
            if self.db_type == "sqlite":
                self.connection = sqlite3.connect(self.db_path)
                self.connection.row_factory = sqlite3.Row
                self.cursor = self.connection.cursor()
                self.logger.info(f"✅ تم الاتصال بقاعدة بيانات SQLite: {self.db_path}")

            elif self.db_type == "mysql":
                # إنشاء قاعدة البيانات بشكل آمن
                self._create_mysql_database_safely()
                
                # الاتصال بقاعدة البيانات
                if self.connection_pool:
                    self.connection = self.connection_pool.get_connection()
                else:
                    self.connection = mysql.connector.connect(**self.mysql_config)
                    
                self.cursor = self.connection.cursor(dictionary=True)
                self.logger.info(f"✅ تم الاتصال بقاعدة بيانات MySQL: {self.mysql_config['database']}")

            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            raise DatabaseConnectionError(f"فشل الاتصال: {e}")

    def _create_mysql_database_safely(self):
        """إنشاء قاعدة بيانات MySQL بشكل آمن"""
        database_name = self.mysql_config['database']
        
        # التحقق مرة أخرى من صحة الاسم
        if not self._is_valid_identifier(database_name):
            raise DatabaseSecurityError(f"اسم قاعدة البيانات غير آمن: {database_name}")
        
        # إنشاء اتصال مؤقت بدون تحديد قاعدة البيانات
        temp_config = self.mysql_config.copy()
        del temp_config['database']
        
        temp_connection = mysql.connector.connect(**temp_config)
        temp_cursor = temp_connection.cursor()
        
        # استخدام استعلام آمن مع معرف محقق
        # نستخدم backticks للمعرفات في MySQL
        safe_query = f"CREATE DATABASE IF NOT EXISTS `{database_name}`"
        temp_cursor.execute(safe_query)
        temp_connection.commit()
        temp_cursor.close()
        temp_connection.close()

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات مع فهارس محسنة"""
        try:
            # تحميل مخططات الجداول من ملفات منفصلة
            schema_dir = Path(__file__).parent / "schemas"
            
            if schema_dir.exists():
                self._create_tables_from_schema_files(schema_dir)
            else:
                self._create_tables_inline()
                
            # إنشاء الفهارس
            self._create_indexes()
            
            self.connection.commit()
            self.logger.info("🏺 تم إنشاء جميع جداول قاعدة بيانات أنوبيس المحسنة بنجاح!")

        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            if self.connection:
                self.connection.rollback()
            raise

    def _create_tables_inline(self):
        """إنشاء الجداول مع التعريفات المدمجة"""
        tables = {
            "projects": """
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY AUTO_INCREMENT,
                    name VARCHAR(255) NOT NULL,
                    path TEXT NOT NULL,
                    type VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    status VARCHAR(50) DEFAULT 'active',
                    INDEX idx_projects_name (name),
                    INDEX idx_projects_type (type),
                    INDEX idx_projects_status (status)
                )
            """,
            "analyses": """
                CREATE TABLE IF NOT EXISTS analyses (
                    id INTEGER PRIMARY KEY AUTO_INCREMENT,
                    project_id INTEGER NOT NULL,
                    agent_type VARCHAR(100) NOT NULL,
                    analysis_data JSON,
                    results JSON,
                    score DECIMAL(5,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                    INDEX idx_analyses_project_id (project_id),
                    INDEX idx_analyses_agent_type (agent_type),
                    INDEX idx_analyses_score (score)
                )
            """,
            "errors": """
                CREATE TABLE IF NOT EXISTS errors (
                    id INTEGER PRIMARY KEY AUTO_INCREMENT,
                    project_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    line_number INTEGER,
                    error_type VARCHAR(100) NOT NULL,
                    severity VARCHAR(50) NOT NULL,
                    message TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                    INDEX idx_errors_project_id (project_id),
                    INDEX idx_errors_type (error_type),
                    INDEX idx_errors_severity (severity)
                )
            """
        }
        
        # تعديل للـ SQLite
        if self.db_type == "sqlite":
            for table_name, table_sql in tables.items():
                # تعديل SQL للـ SQLite
                table_sql = table_sql.replace("AUTO_INCREMENT", "AUTOINCREMENT")
                table_sql = table_sql.replace("JSON", "TEXT")
                table_sql = table_sql.replace("DECIMAL(5,2)", "REAL")
                table_sql = table_sql.replace("ON UPDATE CURRENT_TIMESTAMP", "")
                
                # إزالة الفهارس من تعريف الجدول للـ SQLite
                lines = table_sql.split('\n')
                filtered_lines = [line for line in lines if not line.strip().startswith('INDEX')]
                table_sql = '\n'.join(filtered_lines)
                
                self.cursor.execute(table_sql)
                self.logger.info(f"✅ تم إنشاء جدول: {table_name}")
        else:
            for table_name, table_sql in tables.items():
                self.cursor.execute(table_sql)
                self.logger.info(f"✅ تم إنشاء جدول: {table_name}")

    def _create_indexes(self):
        """إنشاء فهارس منفصلة للـ SQLite"""
        if self.db_type == "sqlite":
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name)",
                "CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(type)",
                "CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)",
                "CREATE INDEX IF NOT EXISTS idx_analyses_project_id ON analyses(project_id)",
                "CREATE INDEX IF NOT EXISTS idx_analyses_agent_type ON analyses(agent_type)",
                "CREATE INDEX IF NOT EXISTS idx_analyses_score ON analyses(score)",
                "CREATE INDEX IF NOT EXISTS idx_errors_project_id ON errors(project_id)",
                "CREATE INDEX IF NOT EXISTS idx_errors_type ON errors(error_type)",
                "CREATE INDEX IF NOT EXISTS idx_errors_severity ON errors(severity)"
            ]
            
            for index_sql in indexes:
                self.cursor.execute(index_sql)
            
            self.logger.info("✅ تم إنشاء جميع الفهارس")

    def add_project(self, name: str, path: str, project_type: str, description: str = "") -> int:
        """إضافة مشروع جديد بشكل آمن"""
        # التحقق من صحة المدخلات
        if not name or not path or not project_type:
            raise ValueError("جميع الحقول المطلوبة يجب أن تكون مملوءة")
        
        if len(name) > 255:
            raise ValueError("اسم المشروع طويل جداً")
            
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                INSERT INTO projects (name, path, type, description)
                VALUES (?, ?, ?, ?)
                """
                
                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")
                
                cursor.execute(sql, (name, path, project_type, description))
                conn.commit()
                
                project_id = cursor.lastrowid
                self.logger.info(f"✅ تم إضافة المشروع: {name} (ID: {project_id})")
                
                # تسجيل النشاط
                self.log_activity(project_id, "project_created", f"تم إنشاء المشروع: {name}")
                
                return project_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إضافة المشروع: {e}")
            raise

    def add_analysis(self, project_id: int, agent_type: str, analysis_data: Dict[str, Any],
                    results: Dict[str, Any], score: float) -> int:
        """إضافة تحليل جديد بشكل آمن"""
        # التحقق من صحة المدخلات
        if not isinstance(project_id, int) or project_id <= 0:
            raise ValueError("معرف المشروع غير صحيح")

        if not agent_type or len(agent_type) > 100:
            raise ValueError("نوع الوكيل غير صحيح")

        if not isinstance(score, (int, float)) or score < 0 or score > 100:
            raise ValueError("النقاط يجب أن تكون بين 0 و 100")

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO analyses (project_id, agent_type, analysis_data, results, score)
                VALUES (?, ?, ?, ?, ?)
                """

                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")

                # تحويل البيانات إلى JSON
                analysis_json = json.dumps(analysis_data, ensure_ascii=False)
                results_json = json.dumps(results, ensure_ascii=False)

                cursor.execute(sql, (project_id, agent_type, analysis_json, results_json, score))
                conn.commit()

                analysis_id = cursor.lastrowid
                self.logger.info(f"✅ تم إضافة التحليل: {agent_type} للمشروع {project_id}")

                return analysis_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إضافة التحليل: {e}")
            raise

    def add_error(self, project_id: int, file_path: str, line_number: int,
                 error_type: str, severity: str, message: str) -> int:
        """إضافة خطأ جديد بشكل آمن"""
        # التحقق من صحة المدخلات
        if not isinstance(project_id, int) or project_id <= 0:
            raise ValueError("معرف المشروع غير صحيح")

        if not file_path:
            raise ValueError("مسار الملف مطلوب")

        if severity not in ["low", "medium", "high", "critical"]:
            raise ValueError("مستوى الخطورة غير صحيح")

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO errors (project_id, file_path, line_number, error_type, severity, message)
                VALUES (?, ?, ?, ?, ?, ?)
                """

                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")

                cursor.execute(sql, (project_id, file_path, line_number, error_type, severity, message))
                conn.commit()

                error_id = cursor.lastrowid
                self.logger.info(f"✅ تم إضافة الخطأ: {error_type} في {file_path}")

                return error_id

        except Exception as e:
            self.logger.error(f"❌ خطأ في إضافة الخطأ: {e}")
            raise

    def get_projects(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """الحصول على قائمة المشاريع مع pagination"""
        if not isinstance(limit, int) or limit <= 0 or limit > 1000:
            raise ValueError("الحد الأقصى يجب أن يكون بين 1 و 1000")

        if not isinstance(offset, int) or offset < 0:
            raise ValueError("الإزاحة يجب أن تكون رقم موجب")

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = "SELECT * FROM projects ORDER BY created_at DESC LIMIT ? OFFSET ?"

                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")

                cursor.execute(sql, (limit, offset))

                if self.db_type == "sqlite":
                    projects = [dict(row) for row in cursor.fetchall()]
                else:
                    projects = cursor.fetchall()

                return projects

        except Exception as e:
            self.logger.error(f"❌ خطأ في جلب المشاريع: {e}")
            raise

    def get_project_analyses(self, project_id: int, agent_type: str = None) -> List[Dict[str, Any]]:
        """الحصول على تحليلات مشروع محدد"""
        if not isinstance(project_id, int) or project_id <= 0:
            raise ValueError("معرف المشروع غير صحيح")

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if agent_type:
                    sql = """
                    SELECT * FROM analyses
                    WHERE project_id = ? AND agent_type = ?
                    ORDER BY created_at DESC
                    """
                    params = (project_id, agent_type)
                else:
                    sql = "SELECT * FROM analyses WHERE project_id = ? ORDER BY created_at DESC"
                    params = (project_id,)

                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")

                cursor.execute(sql, params)

                if self.db_type == "sqlite":
                    analyses = [dict(row) for row in cursor.fetchall()]
                else:
                    analyses = cursor.fetchall()

                # تحويل JSON strings إلى objects
                for analysis in analyses:
                    if analysis.get('analysis_data'):
                        try:
                            analysis['analysis_data'] = json.loads(analysis['analysis_data'])
                        except:
                            pass
                    if analysis.get('results'):
                        try:
                            analysis['results'] = json.loads(analysis['results'])
                        except:
                            pass

                return analyses

        except Exception as e:
            self.logger.error(f"❌ خطأ في جلب التحليلات: {e}")
            raise

    def log_activity(self, project_id: int, activity_type: str, description: str,
                    user_agent: str = "anubis_system", metadata: Optional[Dict[str, Any]] = None):
        """تسجيل نشاط بشكل آمن"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO activities (project_id, activity_type, description, user_agent, metadata)
                VALUES (?, ?, ?, ?, ?)
                """

                if self.db_type == "mysql":
                    sql = sql.replace("?", "%s")

                metadata_json = json.dumps(metadata or {}, ensure_ascii=False)

                cursor.execute(sql, (project_id, activity_type, description, user_agent, metadata_json))
                conn.commit()

        except Exception as e:
            self.logger.error(f"❌ خطأ في تسجيل النشاط: {e}")

    def get_database_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                stats = {}

                # إحصائيات الجداول
                tables = ["projects", "analyses", "errors", "activities"]

                for table in tables:
                    sql = f"SELECT COUNT(*) as count FROM {table}"
                    cursor.execute(sql)

                    if self.db_type == "sqlite":
                        result = cursor.fetchone()
                        stats[f"{table}_count"] = result[0] if result else 0
                    else:
                        result = cursor.fetchone()
                        stats[f"{table}_count"] = result['count'] if result else 0

                # متوسط النقاط
                sql = "SELECT AVG(score) as avg_score FROM analyses WHERE score IS NOT NULL"
                cursor.execute(sql)
                result = cursor.fetchone()

                if self.db_type == "sqlite":
                    stats["average_score"] = round(result[0], 2) if result and result[0] else 0
                else:
                    stats["average_score"] = round(result['avg_score'], 2) if result and result['avg_score'] else 0

                return stats

        except Exception as e:
            self.logger.error(f"❌ خطأ في جلب الإحصائيات: {e}")
            raise

    def close(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            self.logger.info("✅ تم إغلاق اتصال قاعدة البيانات")
        except Exception as e:
            self.logger.error(f"❌ خطأ في إغلاق الاتصال: {e}")

    def __enter__(self):
        """دعم context manager"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق تلقائي عند الخروج من context"""
        self.close()


# دالة مساعدة لإنشاء قاعدة بيانات محسنة
def create_enhanced_database(config_path: str = None) -> EnhancedAnubisDatabase:
    """إنشاء قاعدة بيانات محسنة مع التكوين"""
    if config_path:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        # تكوين افتراضي
        config = {
            "db_type": "sqlite",
            "db_path": "database/anubis_enhanced.db"
        }

    db = EnhancedAnubisDatabase(
        db_type=config.get("db_type", "sqlite"),
        config=config
    )

    return db
