{"status": "completed", "files_scanned": 87, "total_errors": 574, "error_summary": {"syntax_errors": 0, "logic_errors": 208, "style_errors": 322, "security_issues": 26, "performance_issues": 18, "by_severity": {"critical": 26, "warning": 239, "info": 309}}, "file_analysis": {"C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\main.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\main.py", "file_size": 7934, "line_count": 292, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'description' غير مستخدم", "line": 60, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'formatter_class' غير مستخدم", "line": 61, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'epilog' غير مستخدم", "line": 62, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'choices' غير مستخدم", "line": 89, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'help' غير مستخدم", "line": 110, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\__init__.py", "file_size": 35, "line_count": 1, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\database_agent.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\database_agent.py", "file_size": 8244, "line_count": 227, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 31, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 38, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 41, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 46, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 50, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 60, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 63, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 66, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 78, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 81, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 90, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 94, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 98, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 104, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 108, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 114, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 116, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 126, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 137, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 142, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 146, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 149, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 154, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 157, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 165, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 168, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 172, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 175, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 185, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 188, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 191, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 193, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 196, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 206, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 210, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 212, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 214, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 221, "severity": "info"}], "security_issues": [], "performance_issues": []}, "total_errors": 38, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py", "file_size": 16988, "line_count": 452, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "NoneComparison", "message": "استخدم 'is None' بدلاً من '== None'", "line": 284, "severity": "warning"}, {"type": "NoneComparison", "message": "استخدم 'is None' بدلاً من '== None'", "line": 287, "severity": "warning"}], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 40, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 47, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 50, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 54, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 59, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 62, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 73, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 76, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 79, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 91, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 94, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 100, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 108, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 124, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 132, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 135, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 137, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 148, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 159, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 163, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 167, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 171, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 175, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 182, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 192, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 201, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 210, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 217, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 225, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 236, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 243, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 249, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 260, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 267, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 282, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 291, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 296, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 300, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 304, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 311, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 321, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 330, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 337, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 343, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 353, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 358, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 361, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 371, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 388, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 394, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 400, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 406, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 409, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 416, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 420, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 423, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 426, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 429, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 435, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 439, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 451, "severity": "info"}], "security_issues": [{"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 219, "severity": "critical", "code": "(r'eval\\s*\\(', \"استخدام eval() خطير أمنياً\"),"}, {"type": "SecurityIssue", "message": "استخدام exec() خطير أمنياً", "line": 220, "severity": "critical", "code": "(r'exec\\s*\\(', \"استخدام exec() خطير أمنياً\"),"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 223, "severity": "critical", "code": "(r'input\\s*\\(.*\\)', \"استخدام input() قد يكون خطير\"),"}, {"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 339, "severity": "critical", "code": "(r'eval\\s*\\(', \"استخدام eval() خطير أمنياً\"),"}], "performance_issues": []}, "total_errors": 67, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_file_organizer.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_file_organizer.py", "file_size": 9667, "line_count": 233, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'buf' غير مستخدم", "line": 106, "severity": "warning"}], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 30, "severity": "info"}, {"type": "LineLength", "message": "السطر طويل جداً (129 حرف)", "line": 57, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\__init__.py", "file_size": 35, "line_count": 1, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api\\anubis_api_server.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api\\anubis_api_server.py", "file_size": 14057, "line_count": 444, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'title' غير مستخدم", "line": 46, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'openapi_url' غير مستخدم", "line": 49, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'docs_url' غير مستخدم", "line": 50, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'redoc_url' غير مستخدم", "line": 51, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'allow_origins' غير مستخدم", "line": 57, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'allow_credentials' غير مستخدم", "line": 58, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'allow_methods' غير مستخدم", "line": 59, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'allow_headers' غير مستخدم", "line": 60, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'regex' غير مستخدم", "line": 80, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'tokens_generated' غير مستخدم", "line": 214, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 234, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'text' غير مستخدم", "line": 235, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'prompt' غير مستخدم", "line": 244, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'response_time' غير مستخدم", "line": 246, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'config' غير مستخدم", "line": 337, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'verbose' غير مستخدم", "line": 338, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'task' غير مستخدم", "line": 367, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'results' غير مستخدم", "line": 368, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'consensus' غير مستخدم", "line": 369, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'path' غير مستخدم", "line": 402, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'project_type' غير مستخدم", "line": 403, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'description' غير مستخدم", "line": 404, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'result' غير مستخدم", "line": 424, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'host' غير مستخدم", "line": 440, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'port' غير مستخدم", "line": 441, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'reload' غير مستخدم", "line": 442, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'log_level' غير مستخدم", "line": 443, "severity": "warning"}], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (126 حرف)", "line": 360, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 28, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\api\\__init__.py", "file_size": 35, "line_count": 1, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\ai_integration.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\ai_integration.py", "file_size": 11127, "line_count": 331, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'host' غير مستخدم", "line": 254, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'port' غير مستخدم", "line": 255, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'api_key' غير مستخدم", "line": 270, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'model_name' <PERSON>ير مستخدم", "line": 271, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'ai_manager' غير مستخدم", "line": 331, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\assistant_system.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\assistant_system.py", "file_size": 13554, "line_count": 388, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'config' غير مستخدم", "line": 223, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\base_agent.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\base_agent.py", "file_size": 11430, "line_count": 341, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (124 حرف)", "line": 312, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\config_manager.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\config_manager.py", "file_size": 12683, "line_count": 335, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\enhanced_integrations.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\enhanced_integrations.py", "file_size": 13085, "line_count": 362, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'trace_data' غير مستخدم", "line": 273, "severity": "warning"}], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 43, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 54, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 58, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 64, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 79, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 86, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 92, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 97, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 105, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 118, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 127, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 141, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 145, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 153, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 158, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 165, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 171, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 176, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 177, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 180, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 193, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 201, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 207, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 217, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 220, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 223, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 228, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 242, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 248, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 251, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 264, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 266, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 271, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 280, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 283, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 291, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 297, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 299, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 306, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 309, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 315, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 321, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 323, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 330, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 334, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 338, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 340, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 349, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 351, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 357, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 358, "severity": "info"}], "security_issues": [], "performance_issues": []}, "total_errors": 52, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\langsmith_wrapper.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\langsmith_wrapper.py", "file_size": 3590, "line_count": 120, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'performance_data' غير مستخدم", "line": 81, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'langsmith_wrapper' غير مستخدم", "line": 120, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\logger.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\logger.py", "file_size": 9019, "line_count": 286, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 237, "severity": "warning", "code": "message += f\" - خطأ: {result['error']}\""}]}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core\\__init__.py", "file_size": 479, "line_count": 15, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير '__version__' غير مستخدم", "line": 6, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__author__' غير مستخدم", "line": 7, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__description__' غير مستخدم", "line": 8, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__all__' غير مستخدم", "line": 15, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 4, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\anubis_database.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\anubis_database.py", "file_size": 14494, "line_count": 435, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\enhanced_anubis_database.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\enhanced_anubis_database.py", "file_size": 23146, "line_count": 607, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'db_type' غير مستخدم", "line": 603, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'config' غير مستخدم", "line": 604, "severity": "warning"}], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 48, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 51, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 61, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 68, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 76, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 87, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 115, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 118, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 136, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 138, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 160, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 166, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 179, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 183, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 187, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 190, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 204, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 209, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 212, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 272, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 281, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 286, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 308, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 311, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 319, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 322, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 326, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 331, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 334, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 337, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 340, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 343, "severity": "info"}], "security_issues": [], "performance_issues": []}, "total_errors": 34, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\simple_validation.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\simple_validation.py", "file_size": 10867, "line_count": 358, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 51, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\__init__.py", "file_size": 35, "line_count": 1, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\base_plugin.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\base_plugin.py", "file_size": 2538, "line_count": 93, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\example_plugin.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\example_plugin.py", "file_size": 3336, "line_count": 99, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\plugin_manager.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\plugin_manager.py", "file_size": 6696, "line_count": 228, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\plugins\\__init__.py", "file_size": 306, "line_count": 12, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير '__version__' غير مستخدم", "line": 6, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__description__' غير مستخدم", "line": 7, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__all__' غير مستخدم", "line": 12, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\activate_real_langsmith.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\activate_real_langsmith.py", "file_size": 14726, "line_count": 449, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'tags' غير مستخدم", "line": 249, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\agents_cleanup.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\agents_cleanup.py", "file_size": 14255, "line_count": 499, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير '__all__' غير مستخدم", "line": 140, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__version__' غير مستخدم", "line": 154, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__author__' غير مستخدم", "line": 155, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__description__' غير مستخدم", "line": 156, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'result' غير مستخدم", "line": 489, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ollama.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ollama.py", "file_size": 8013, "line_count": 255, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'report' غير مستخدم", "line": 235, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ready.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ready.py", "file_size": 1015, "line_count": 50, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 50, "severity": "critical", "code": "input(\"\\nاضغط Enter للخروج...\")"}], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\code_quality_checker.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\code_quality_checker.py", "file_size": 12038, "line_count": 361, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 272, "severity": "warning", "code": "total_issues += result[\"issues_count\"]"}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 313, "severity": "warning", "code": "content += f\"❌ **خطأ:** {result['error']}\\n\\n\""}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 316, "severity": "warning", "code": "content += f\"📊 **النقاط:** {result['score']}/10\\n\""}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 318, "severity": "warning", "code": "content += f\"⚠️ **المشاكل:** {result['issues_count']}\\n\""}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 319, "severity": "warning", "code": "content += f\"✅ **حالة الخروج:** {result['exit_code']}\\n\\n\""}]}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\complete_file_organizer.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\complete_file_organizer.py", "file_size": 11950, "line_count": 346, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'result' غير مستخدم", "line": 336, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\create_all_readmes.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\create_all_readmes.py", "file_size": 9356, "line_count": 331, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 80, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'text' غير مستخدم", "line": 81, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 82, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'report' غير مستخدم", "line": 322, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 4, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\emergency_vscode_check.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\emergency_vscode_check.py", "file_size": 5282, "line_count": 173, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'danger_level' غير مستخدم", "line": 141, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\fix_agents_with_gemini.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\fix_agents_with_gemini.py", "file_size": 18753, "line_count": 547, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (126 حرف)", "line": 320, "severity": "warning"}, {"type": "LineLength", "message": "السطر طويل جداً (122 حرف)", "line": 321, "severity": "warning"}], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 315, "severity": "warning", "code": "project_errors['total_errors'] += file_errors['total_errors']"}]}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_cli_helper.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_cli_helper.py", "file_size": 7914, "line_count": 271, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 72, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 74, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_integration_system.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\gemini_integration_system.py", "file_size": 12701, "line_count": 395, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 129, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'text' غير مستخدم", "line": 130, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 131, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\langsmith_integration_demo.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\langsmith_integration_demo.py", "file_size": 13982, "line_count": 367, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'project_path' غير مستخدم", "line": 310, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'orchestrator' غير مستخدم", "line": 358, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_all_files.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_all_files.py", "file_size": 10516, "line_count": 317, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'structure' غير مستخدم", "line": 296, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_project_files.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\organize_project_files.py", "file_size": 12158, "line_count": 368, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\process_monitor.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\process_monitor.py", "file_size": 9872, "line_count": 300, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'handlers' غير مستخدم", "line": 35, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'help' غير مستخدم", "line": 288, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "file_size": 4470, "line_count": 161, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 126, "severity": "critical", "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 131, "severity": "critical", "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 138, "severity": "critical", "code": "response = input(\"هل تريد المتابعة؟ (y/n): \")"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 144, "severity": "critical", "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 161, "severity": "critical", "code": "input(\"اضغط Enter للخروج...\")"}], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_vscode_check.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_vscode_check.py", "file_size": 6533, "line_count": 205, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 59, "severity": "warning", "code": "total_memory += proc_info[\"memory_percent\"]"}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 60, "severity": "warning", "code": "total_cpu += proc_info[\"cpu_percent\"]"}]}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\run_vscode_monitor.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\run_vscode_monitor.py", "file_size": 7340, "line_count": 255, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 219, "severity": "critical", "code": "choice = input(\"\\nEnter your choice (0-6): \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 251, "severity": "critical", "code": "input(\"\\nPress Enter to continue...\")"}], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\setup_langsmith.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\setup_langsmith.py", "file_size": 15141, "line_count": 526, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 53, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'performance_data' غير مستخدم", "line": 285, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'response' غير مستخدم", "line": 411, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\simple_langsmith_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\simple_langsmith_test.py", "file_size": 5856, "line_count": 214, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'client' غير مستخدم", "line": 51, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'result' غير مستخدم", "line": 181, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\smart_workflow_demo.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\smart_workflow_demo.py", "file_size": 18483, "line_count": 438, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'verbose' غير مستخدم", "line": 63, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'max_files' غير مستخدم", "line": 132, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'analysis_result' غير مستخدم", "line": 413, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'fixing_result' غير مستخدم", "line": 416, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'orchestrator' غير مستخدم", "line": 436, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\start_anubis_n8n_system.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\start_anubis_n8n_system.py", "file_size": 9553, "line_count": 336, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'cwd' غير مستخدم", "line": 129, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'headers' غير مستخدم", "line": 214, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 215, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'requirements' غير مستخدم", "line": 269, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'test_results' غير مستخدم", "line": 297, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'report' غير مستخدم", "line": 300, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 6, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_langsmith_integration.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_langsmith_integration.py", "file_size": 14127, "line_count": 413, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'client' غير مستخدم", "line": 51, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'results' غير مستخدم", "line": 404, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_ollama_langsmith.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\test_ollama_langsmith.py", "file_size": 2447, "line_count": 86, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py", "file_size": 12141, "line_count": 352, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 155, "severity": "critical", "code": "choice = input(\"\\nEnter your choice (1-5): \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 180, "severity": "critical", "code": "confirm = input(\"Type 'FORCE' to confirm: \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 256, "severity": "critical", "code": "input(f\"\\nClose these {len(high_memory_processes)} processes? (y/n): \").strip().lower()"}], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 44, "severity": "warning", "code": "self.total_cpu_percent += proc.info[\"cpu_percent\"]"}]}, "total_errors": 4, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_heavy_load_analyzer.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_heavy_load_analyzer.py", "file_size": 14431, "line_count": 408, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 251, "severity": "warning", "code": "language_servers += len(self.process_groups.get(proc_type, []))"}]}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_alerts.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_alerts.py", "file_size": 12946, "line_count": 372, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_monitor.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_process_monitor.py", "file_size": 12843, "line_count": 364, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'handlers' غير مستخدم", "line": 38, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 160, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'text' غير مستخدم", "line": 161, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 162, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 201, "severity": "warning", "code": "analysis[\"process_breakdown\"][proc_type][\"memory_usage\"] += proc[\"memory_percent\"]"}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 202, "severity": "warning", "code": "analysis[\"process_breakdown\"][proc_type][\"cpu_usage\"] += proc[\"cpu_percent\"]"}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 204, "severity": "warning", "code": "analysis[\"total_memory_usage\"] += proc[\"memory_percent\"]"}, {"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 205, "severity": "warning", "code": "analysis[\"total_cpu_usage\"] += proc[\"cpu_percent\"]"}]}, "total_errors": 8, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\__init__.py", "file_size": 344, "line_count": 14, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير '__version__' غير مستخدم", "line": 8, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__description__' غير مستخدم", "line": 9, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__all__' غير مستخدم", "line": 14, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py", "file_size": 10587, "line_count": 320, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'config' غير مستخدم", "line": 208, "severity": "warning"}], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 45, "severity": "critical", "code": "choice = input(\"🏺 اختر الخدمة المطلوبة (0-7): \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 81, "severity": "critical", "code": "project_path = input(\"📁 أدخل مسار المشروع (اتركه فارغاً للمشروع الحالي): \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 156, "severity": "critical", "code": "project_path = input(\"📁 أدخل مسار المشروع: \").strip()"}, {"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 197, "severity": "critical", "code": "project_path = input(\"📁 أدخل مسار المشروع: \").strip()"}], "performance_issues": []}, "total_errors": 5, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_agents_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_agents_test.py", "file_size": 13011, "line_count": 398, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 80, "severity": "warning"}], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 81, "severity": "critical", "code": "eval(\"print('test')\")  # استخدام eval خطر"}], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم enumerate بدلاً من range(len())", "line": 84, "severity": "warning", "code": "for i in range(len(items)):  # استخدام range(len())"}]}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_system_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_system_test.py", "file_size": 13747, "line_count": 422, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'project_path' غير مستخدم", "line": 273, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\quick_ai_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\quick_ai_test.py", "file_size": 4380, "line_count": 158, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\run_all_tests.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\run_all_tests.py", "file_size": 8882, "line_count": 320, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'timeout' غير مستخدم", "line": 208, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_agents.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_agents.py", "file_size": 2449, "line_count": 88, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_fixed.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_fixed.py", "file_size": 11066, "line_count": 341, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (134 حرف)", "line": 327, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_integration.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_ai_integration.py", "file_size": 11883, "line_count": 351, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (134 حرف)", "line": 337, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_anubis_system.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_anubis_system.py", "file_size": 9790, "line_count": 274, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'project_path' غير مستخدم", "line": 156, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_database.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_database.py", "file_size": 14613, "line_count": 386, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'analysis_id' غير مستخدم", "line": 298, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'error_id' غير مستخدم", "line": 304, "severity": "warning"}], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 31, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 38, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 43, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 48, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 55, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 60, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 65, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 71, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 75, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 80, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 84, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 89, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 93, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 99, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 103, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 106, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 112, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 116, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 120, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 128, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 138, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 143, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 145, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 149, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 155, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 164, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 167, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 173, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 177, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 181, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 185, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 188, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 192, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 195, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 203, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 209, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 213, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 217, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 221, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 225, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 231, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 236, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 240, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 246, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 250, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 253, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 264, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 267, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 271, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 275, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 283, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 288, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 293, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 296, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 299, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 302, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 307, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 314, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 318, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 325, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 334, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 347, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 350, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 356, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 360, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 367, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 372, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 377, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 380, "severity": "info"}], "security_issues": [], "performance_issues": []}, "total_errors": 71, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_error_detector.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_error_detector.py", "file_size": 8358, "line_count": 264, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'project_path' غير مستخدم", "line": 34, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'config' غير مستخدم", "line": 35, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'verbose' غير مستخدم", "line": 36, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 53, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'api_key' غير مستخدم", "line": 54, "severity": "warning"}], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (125 حرف)", "line": 147, "severity": "warning"}], "security_issues": [{"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 57, "severity": "critical", "code": "eval(\"print('hello')\")  # استخدام eval خطر"}, {"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 107, "severity": "critical", "code": "eval(\"console.log('clicked')\"); // استخدام eval"}], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم enumerate بدلاً من range(len())", "line": 58, "severity": "warning", "code": "for i in range(len(items)):  # استخدام range(len())"}]}, "total_errors": 9, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_integrations.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_integrations.py", "file_size": 12439, "line_count": 354, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [{"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 32, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 35, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 50, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 53, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 59, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 65, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 68, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 72, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 77, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 80, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 90, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 93, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 96, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 98, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 106, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 112, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 115, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 119, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 127, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 131, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 137, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 144, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 153, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 163, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 166, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 170, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 179, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 182, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 184, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 190, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 195, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 198, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 205, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 208, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 213, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 220, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 228, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 234, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 236, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 239, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 244, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 246, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 249, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 254, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 261, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 266, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 275, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 282, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 288, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 294, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 298, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 302, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 312, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 315, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 335, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 339, "severity": "info"}, {"type": "TrailingWhitespace", "message": "مسافات زائدة في نهاية السطر", "line": 348, "severity": "info"}], "security_issues": [], "performance_issues": []}, "total_errors": 57, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_error_detector.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_error_detector.py", "file_size": 10175, "line_count": 336, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'api_key' غير مستخدم", "line": 123, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 288, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_fixed_agents.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_fixed_agents.py", "file_size": 699, "line_count": 25, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'analyzer' غير مستخدم", "line": 15, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'organizer' <PERSON>ي<PERSON> مستخدم", "line": 18, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'memory' غير مستخدم", "line": 21, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_database.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_database.py", "file_size": 10300, "line_count": 321, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'phone' غير مستخدم", "line": 65, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'customer_type' غير مستخدم", "line": 66, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'credit_limit_usd' غير مستخدم", "line": 67, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'credit_limit_egp' غير مستخدم", "line": 68, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'order_date' غير مستخدم", "line": 113, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'priority' غير مستخدم", "line": 115, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'total_pieces' غير مستخدم", "line": 116, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'total_gold_weight' غير مستخدم", "line": 117, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'total_diamond_weight' غير مستخدم", "line": 118, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'item_number' غير مستخدم", "line": 129, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'piece_type' غير مستخدم", "line": 130, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'description' غير مستخدم", "line": 131, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'quantity' غير مستخدم", "line": 132, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'gold_weight_per_piece' غير مستخدم", "line": 134, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'gold_source' غير مستخدم", "line": 135, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_type' غير مستخدم", "line": 137, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_size' غير مستخدم", "line": 138, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_weight_per_piece' غير مستخدم", "line": 139, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_count_per_piece' غير مستخدم", "line": 140, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_source' غير مستخدم", "line": 141, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'diamond_value_usd' غير مستخدم", "line": 142, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'manufacturing_cost_usd' غير مستخدم", "line": 143, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'stone_setting_cost_egp' غير مستخدم", "line": 144, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'has_plating' غير مستخدم", "line": 145, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'plating_type' غير مستخدم", "line": 146, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'plating_cost_egp' غير مستخدم", "line": 147, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'has_stamping' غير مستخدم", "line": 148, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'stamping_cost_egp' غير مستخدم", "line": 149, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'has_3d_design' غير مستخدم", "line": 150, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'design_complexity' غير مستخدم", "line": 151, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'design_cost_egp' غير مستخدم", "line": 152, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'item_total_usd' غير مستخدم", "line": 153, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'item_total_egp' غير مستخدم", "line": 154, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'customer_id' غير مستخدم", "line": 192, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'order_id' غير مستخدم", "line": 193, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'payment_date' غير مستخدم", "line": 194, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'payment_type' غير مستخدم", "line": 195, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'amount_usd' غير مستخدم", "line": 196, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'amount_egp' غير مستخدم", "line": 197, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'gold_value_usd' غير مستخدم", "line": 200, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'exchange_rate_used' غير مستخدم", "line": 203, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'payment_method' غير مستخدم", "line": 204, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'notes' غير مستخدم", "line": 205, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 43, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_logic.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_jewelry_logic.py", "file_size": 10802, "line_count": 328, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'price_per_carat' غير مستخدم", "line": 90, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_plugins.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_plugins.py", "file_size": 12499, "line_count": 380, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 303, "severity": "critical", "code": "def test_plugin_info_retrieval(self):"}], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py", "file_size": 13210, "line_count": 431, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 252, "severity": "warning"}], "style_errors": [], "security_issues": [{"type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 249, "severity": "critical", "code": "user_input = input(\"Enter command: \")"}, {"type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 250, "severity": "critical", "code": "eval(user_input)  # خطر أمني عالي"}, {"type": "SecurityIssue", "message": "استخدام exec() خطير أمنياً", "line": 255, "severity": "critical", "code": "exec(code)  # خطر أمني عالي"}], "performance_issues": []}, "total_errors": 4, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_smart_analyzer.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_smart_analyzer.py", "file_size": 6634, "line_count": 193, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'report' غير مستخدم", "line": 168, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_system.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_system.py", "file_size": 8307, "line_count": 289, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'project_path' غير مستخدم", "line": 158, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\__init__.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\__init__.py", "file_size": 582, "line_count": 25, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير '__version__' غير مستخدم", "line": 8, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__description__' غير مستخدم", "line": 9, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير '__all__' غير مستخدم", "line": 25, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates\\streamlit_template\\main.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\templates\\streamlit_template\\main.py", "file_size": 6512, "line_count": 241, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'page_title' غير مستخدم", "line": 20, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'page_icon' غير مستخدم", "line": 21, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'initial_sidebar_state' غير مستخدم", "line": 23, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'color_discrete_sequence' غير مستخدم", "line": 103, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'title' غير مستخدم", "line": 127, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'size' غير مستخدم", "line": 129, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'color_continuous_scale' غير مستخدم", "line": 130, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'min_value' غير مستخدم", "line": 143, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'max_value' غير مستخدم", "line": 144, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'label' غير مستخدم", "line": 210, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'file_name' غير مستخدم", "line": 212, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'mime' غير مستخدم", "line": 213, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 12, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\database_validator.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\database_validator.py", "file_size": 19034, "line_count": 549, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 87, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 1, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\final_validation_runner.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\final_validation_runner.py", "file_size": 10710, "line_count": 341, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'json_file' غير مستخدم", "line": 320, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'html_file' غير مستخدم", "line": 323, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_connector.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_connector.py", "file_size": 10063, "line_count": 317, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_manager.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\core\\mysql_manager.py", "file_size": 9764, "line_count": 285, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 62, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'result' غير مستخدم", "line": 237, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\direct_setup.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\direct_setup.py", "file_size": 11107, "line_count": 347, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'host' غير مستخدم", "line": 312, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'port' غير مستخدم", "line": 313, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'user' غير مستخدم", "line": 314, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 315, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'charset' غير مستخدم", "line": 317, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 318, "severity": "warning"}], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (133 حرف)", "line": 210, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 7, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\setup_database.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\setup\\setup_database.py", "file_size": 5733, "line_count": 188, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\comprehensive_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\comprehensive_test.py", "file_size": 19963, "line_count": 579, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 54, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'extra_tables' غير مستخدم", "line": 119, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 2, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\final_db_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\final_db_test.py", "file_size": 2038, "line_count": 74, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 0, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\run_all_tests.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\run_all_tests.py", "file_size": 11242, "line_count": 309, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'cwd' غير مستخدم", "line": 40, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'capture_output' غير مستخدم", "line": 41, "severity": "warning"}], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (121 حرف)", "line": 201, "severity": "warning"}, {"type": "LineLength", "message": "السطر طويل جداً (154 حرف)", "line": 202, "severity": "warning"}, {"type": "LineLength", "message": "السطر طويل جداً (189 حرف)", "line": 226, "severity": "warning"}], "security_issues": [], "performance_issues": [{"type": "PerformanceIssue", "message": "استخدم list.extend() بدلاً من += للقوائم", "line": 257, "severity": "warning", "code": "html_content += f\"<p><strong>الخطأ:</strong> {result['error']}</p>\""}]}, "total_errors": 6, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\stress_test.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\stress_test.py", "file_size": 13429, "line_count": 408, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 58, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'target' غير مستخدم", "line": 204, "severity": "warning"}], "style_errors": [{"type": "LineLength", "message": "السطر طويل جداً (129 حرف)", "line": 232, "severity": "warning"}], "security_issues": [], "performance_issues": []}, "total_errors": 3, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_connection.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_connection.py", "file_size": 2292, "line_count": 79, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'host' غير مستخدم", "line": 31, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'port' غير مستخدم", "line": 32, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'user' غير مستخدم", "line": 33, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 34, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'database' غير مستخدم", "line": 35, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'charset' غير مستخدم", "line": 36, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 6, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_db_connection.py": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\database\\tests\\test_db_connection.py", "file_size": 4288, "line_count": 151, "extension": ".py", "errors": {"syntax_errors": [], "logic_errors": [{"type": "UnusedVariable", "message": "المتغير 'user' غير مستخدم", "line": 60, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'password' غير مستخدم", "line": 61, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'charset' غير مستخدم", "line": 63, "severity": "warning"}, {"type": "UnusedVariable", "message": "المتغير 'autocommit' غير مستخدم", "line": 64, "severity": "warning"}], "style_errors": [], "security_issues": [], "performance_issues": []}, "total_errors": 4, "status": "success"}}, "recommendations": ["مراجعة المشاكل الأمنية فوراً - أولوية عالية", "استخدام أدوات تنسيق الكود مثل Black أو Prettier", "إجراء مراجعة دورية للكود", "استخدام أدوات التحليل الثابت مثل pylint أو ESLint"], "critical_issues": [{"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 219, "code": "(r'eval\\s*\\(', \"استخدام eval() خطير أمنياً\"),"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام exec() خطير أمنياً", "line": 220, "code": "(r'exec\\s*\\(', \"استخدام exec() خطير أمنياً\"),"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 223, "code": "(r'input\\s*\\(.*\\)', \"استخدام input() قد يكون خطير\"),"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\agents\\enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 339, "code": "(r'eval\\s*\\(', \"استخدام eval() خطير أمنياً\"),"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\check_ready.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 50, "code": "input(\"\\nاضغط Enter للخروج...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 126, "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 131, "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 138, "code": "response = input(\"هل تريد المتابعة؟ (y/n): \")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 144, "code": "input(\"\\n❌ اضغط Enter للخروج...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\quick_start.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 161, "code": "input(\"اضغط Enter للخروج...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\run_vscode_monitor.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 219, "code": "choice = input(\"\\nEnter your choice (0-6): \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\run_vscode_monitor.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 251, "code": "input(\"\\nPress Enter to continue...\")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 155, "code": "choice = input(\"\\nEnter your choice (1-5): \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 180, "code": "confirm = input(\"Type 'FORCE' to confirm: \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\scripts\\vscode_emergency_cleanup.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 256, "code": "input(f\"\\nClose these {len(high_memory_processes)} processes? (y/n): \").strip().lower()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 45, "code": "choice = input(\"🏺 اختر الخدمة المطلوبة (0-7): \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 81, "code": "project_path = input(\"📁 أدخل مسار المشروع (اتركه فارغاً للمشروع الحالي): \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 156, "code": "project_path = input(\"📁 أدخل مسار المشروع: \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\ask_anubis.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 197, "code": "project_path = input(\"📁 أدخل مسار المشروع: \").strip()"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\comprehensive_agents_test.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 81, "code": "eval(\"print('test')\")  # استخدام eval خطر"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 57, "code": "eval(\"print('hello')\")  # استخدام eval خطر"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_enhanced_error_detector.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 107, "code": "eval(\"console.log('clicked')\"); // استخدام eval"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_plugins.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 303, "code": "def test_plugin_info_retrieval(self):"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py", "type": "SecurityIssue", "message": "استخدام input() قد يكون خطير", "line": 249, "code": "user_input = input(\"Enter command: \")"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py", "type": "SecurityIssue", "message": "استخدام eval() خطير أمنياً", "line": 250, "code": "eval(user_input)  # خطر أمني عالي"}, {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\tests\\test_project_analyzer.py", "type": "SecurityIssue", "message": "استخدام exec() خطير أمنياً", "line": 255, "code": "exec(code)  # خطر أمني عالي"}], "timestamp": "2025-07-19T07:25:22.391449"}