{"name": "anubis-horus-universal-ai-assistants", "version": "1.0.0", "description": "Universal AI Assistants with Anubis System and Horus Team - Complete Node.js dependencies", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "webpack --mode production", "test": "jest", "lint": "eslint .", "format": "prettier --write .", "install-all": "npm install && npm run install-global", "install-global": "npm install -g <PERSON><PERSON> webpack-cli eslint prettier jest"}, "keywords": ["ai", "assistants", "anubis", "horus", "mcp", "automation", "machine-learning", "nlp", "chatbot", "universal"], "author": "Universal AI Assistants Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "ws": "^8.16.0", "axios": "^1.6.7", "lodash": "^4.17.21", "moment": "^2.30.1", "uuid": "^9.0.1", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.4.1", "joi": "^17.12.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.2", "pdf-parse": "^1.1.1", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.11.0", "playwright": "^1.41.2", "selenium-webdriver": "^4.17.0", "mongodb": "^6.3.0", "mongoose": "^8.1.1", "redis": "^4.6.12", "mysql2": "^3.9.1", "pg": "^8.11.3", "sqlite3": "^5.1.7", "sequelize": "^6.36.0", "typeorm": "^0.3.20", "prisma": "^5.9.1", "@prisma/client": "^5.9.1", "openai": "^4.26.0", "anthropic": "^0.17.1", "@google-ai/generativelanguage": "^2.5.0", "langchain": "^0.1.25", "@langchain/openai": "^0.0.14", "@langchain/anthropic": "^0.1.6", "@langchain/google-genai": "^0.0.8", "tensorflow": "^4.17.0", "@tensorflow/tfjs-node": "^4.17.0", "brain.js": "^2.0.0-beta.23", "ml-matrix": "^6.10.7", "natural": "^6.12.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "franc": "^6.1.0", "node-nlp": "^4.27.0", "react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.1.0", "vue": "^3.4.15", "nuxt": "^3.10.0", "angular": "^17.1.0", "@angular/core": "^17.1.0", "svelte": "^4.2.9", "sveltekit": "^2.0.0", "fastify": "^4.26.0", "koa": "^2.15.0", "hapi": "^21.3.2", "nest": "^0.1.6", "@nestjs/core": "^10.3.0", "@nestjs/common": "^10.3.0", "apollo-server-express": "^3.12.1", "graphql": "^16.8.1", "type-graphql": "^1.1.1", "socket.io-client": "^4.7.5", "ws-client": "^1.0.0", "mqtt": "^5.3.5", "amqplib": "^0.10.3", "kafka-node": "^5.0.0", "bull": "^4.12.2", "agenda": "^5.0.0", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "pm2": "^5.3.1", "forever": "^4.0.3", "nodemailer": "^6.9.9", "twilio": "^4.20.1", "discord.js": "^14.14.1", "telegraf": "^4.15.6", "slack-bolt": "^3.17.1", "whatsapp-web.js": "^1.23.0", "node-telegram-bot-api": "^0.64.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "passport-github2": "^0.1.12", "oauth2-server": "^4.0.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.6", "body-parser": "^1.20.2", "multer-s3": "^3.0.1", "aws-sdk": "^2.1543.0", "@aws-sdk/client-s3": "^3.496.0", "@google-cloud/storage": "^7.7.0", "azure-storage": "^2.10.7", "cloudinary": "^1.41.3", "firebase-admin": "^12.0.0", "firebase": "^10.7.2", "supabase": "^2.38.6", "@supabase/supabase-js": "^2.38.6", "stripe": "^14.17.0", "paypal-rest-sdk": "^1.8.1", "square": "^34.0.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "bcryptjs": "^2.4.3", "argon2": "^0.31.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "node-2fa": "^2.0.3", "winston": "^3.11.0", "bunyan": "^1.8.15", "pino": "^8.17.2", "debug": "^4.3.4", "chalk": "^5.3.0", "colors": "^1.4.0", "inquirer": "^9.2.12", "commander": "^11.1.0", "yargs": "^17.7.2", "minimist": "^1.2.8", "ora": "^8.0.1", "progress": "^2.0.3", "cli-progress": "^3.12.0", "figlet": "^1.7.0", "boxen": "^7.1.1", "table": "^6.8.1", "cli-table3": "^0.6.3", "node-fetch": "^3.3.2", "got": "^14.0.0", "superagent": "^8.1.2", "request": "^2.88.2", "request-promise": "^4.2.6", "cheerio-httpcli": "^0.7.4", "jsdom": "^24.0.0", "xml2js": "^0.6.2", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "xlsx": "^0.18.5", "pdf2pic": "^3.1.1", "jimp": "^0.22.10", "canvas": "^2.11.2", "d3": "^7.8.5", "chart.js": "^4.4.1", "plotly.js": "^2.29.1", "three": "^0.160.1", "babylon.js": "^6.42.0", "p5": "^1.8.0", "matter-js": "^0.19.0", "phaser": "^3.70.0", "pixi.js": "^8.0.0", "fabric": "^5.3.0", "konva": "^9.2.0", "leaflet": "^1.9.4", "mapbox-gl": "^3.1.0", "google-maps": "^4.3.3", "geolib": "^3.3.4", "turf": "^3.0.14", "moment-timezone": "^0.5.45", "date-fns": "^3.3.1", "dayjs": "^1.11.10", "luxon": "^3.4.4", "validator": "^13.11.0", "joi-objectid": "^4.0.2", "ajv": "^8.12.0", "yup": "^1.4.0", "zod": "^3.22.4", "superstruct": "^1.0.3", "class-validator": "^0.14.1", "class-transformer": "^0.5.1", "reflect-metadata": "^0.2.1", "inversify": "^6.0.2", "tsyringe": "^4.8.0", "awilix": "^10.0.1", "bottlejs": "^2.0.1", "ramda": "^0.29.1", "immutable": "^4.3.5", "immer": "^10.0.3", "rxjs": "^7.8.1", "most": "^1.8.0", "highland": "^2.13.5", "async": "^3.2.5", "bluebird": "^3.7.2", "p-queue": "^8.0.1", "p-limit": "^5.0.0", "bottleneck": "^2.19.5", "cluster": "^0.7.7", "worker-threads": "^1.0.2", "child-process-promise": "^2.2.1", "execa": "^8.0.1", "shelljs": "^0.8.5", "cross-spawn": "^7.0.3", "node-pty": "^1.0.0", "ssh2": "^1.15.0", "node-ssh": "^13.1.0", "ftp": "^0.3.10", "basic-ftp": "^5.0.4", "archiver": "^6.0.1", "unzipper": "^0.10.14", "tar": "^6.2.0", "7zip-bin": "^5.2.0", "node-7z": "^3.0.0", "chokidar": "^3.5.3", "nodemon": "^3.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "dotenv-cli": "^7.3.0", "env-cmd": "^10.1.0", "config": "^3.3.11", "nconf": "^0.12.1", "convict": "^6.2.4", "rc": "^1.2.8", "cosmiconfig": "^9.0.0", "find-up": "^7.0.0", "pkg-up": "^5.0.0", "read-pkg": "^9.0.1", "write-pkg": "^7.0.0", "semver": "^7.5.4", "compare-versions": "^6.1.0", "update-notifier": "^7.0.0", "latest-version": "^9.0.0", "package-json": "^10.0.0", "npm-check-updates": "^16.14.12", "depcheck": "^1.4.7", "license-checker": "^25.0.1", "audit-ci": "^6.6.1", "snyk": "^1.1275.0", "retire": "^4.0.3", "nsp": "^3.2.1"}, "devDependencies": {"webpack": "^5.90.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "typescript": "^5.3.3", "@types/node": "^20.11.16", "@types/express": "^4.17.21", "@types/socket.io": "^3.0.2", "@types/ws": "^8.5.10", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/moment": "^2.13.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.2", "supertest": "^6.3.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "cypress": "^13.6.4", "playwright-test": "^14.1.1", "mocha": "^10.2.0", "chai": "^5.0.3", "sinon": "^17.0.1", "nyc": "^15.1.0", "codecov": "^3.8.3", "husky": "^9.0.10", "lint-staged": "^15.2.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "semantic-release": "^23.0.2", "standard-version": "^9.5.0", "release-it": "^17.0.3", "auto-changelog": "^2.4.0", "jsdoc": "^4.0.2", "typedoc": "^0.25.7", "documentation": "^14.0.3", "api-documenter": "^7.23.35", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "redoc-cli": "^0.13.21", "madge": "^6.1.0", "dependency-cruiser": "^16.2.1", "bundlesize": "^0.18.2", "size-limit": "^11.0.2", "webpack-bundle-analyzer": "^4.10.1", "source-map-explorer": "^2.5.3", "lighthouse": "^11.4.0", "puppeteer-lighthouse": "^1.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/universal-ai-assistants.git"}, "bugs": {"url": "https://github.com/your-username/universal-ai-assistants/issues"}, "homepage": "https://github.com/your-username/universal-ai-assistants#readme"}