#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 نظام تدوير المفاتيح التلقائي
Automatic Key Rotation System

نظام متقدم لتدوير مفاتيح API تلقائياً مع فريق حورس
Advanced system for automatic API key rotation with Horus team
"""

import os
import json
import schedule
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('key_rotation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HorusKeyRotationSystem:
    """🔄 نظام تدوير المفاتيح مع فريق حورس"""
    
    def __init__(self):
        """تهيئة نظام التدوير"""
        self.vault_dir = Path(__file__).parent / "vault"
        self.rotation_dir = self.vault_dir / "rotation"
        self.rotation_dir.mkdir(exist_ok=True)
        
        self.rotation_schedule = {
            "github": 30,      # كل 30 يوم
            "google_gemini": 60,  # كل 60 يوم
            "openrouter": 45,     # كل 45 يوم
            "anthropic": 90,      # كل 90 يوم
            "deepseek": 60,       # كل 60 يوم
            "mistral": 45,        # كل 45 يوم
            "default": 30         # افتراضي
        }
        
        self.rotation_log = []
        logger.info("🔄 تم تهيئة نظام تدوير المفاتيح")
    
    def check_key_expiry(self, key_info: dict) -> bool:
        """فحص انتهاء صلاحية المفتاح"""
        try:
            created_at = datetime.fromisoformat(key_info.get("created_at", datetime.now().isoformat()))
            platform = key_info.get("platform", "default")
            rotation_days = self.rotation_schedule.get(platform, self.rotation_schedule["default"])
            
            expiry_date = created_at + timedelta(days=rotation_days)
            return datetime.now() >= expiry_date
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص انتهاء الصلاحية: {e}")
            return True  # في حالة الخطأ، اعتبر المفتاح منتهي الصلاحية
    
    def generate_rotation_alert(self, key_info: dict, days_until_expiry: int) -> dict:
        """إنشاء تنبيه تدوير المفتاح"""
        alert = {
            "alert_id": f"rotation_{key_info.get('key_id', 'unknown')}_{int(time.time())}",
            "timestamp": datetime.now().isoformat(),
            "key_id": key_info.get("key_id"),
            "platform": key_info.get("platform"),
            "key_name": key_info.get("name"),
            "days_until_expiry": days_until_expiry,
            "priority": "high" if days_until_expiry <= 7 else "medium",
            "action_required": "rotate_key",
            "message": f"مفتاح {key_info.get('name')} يحتاج للتدوير خلال {days_until_expiry} يوم"
        }
        
        logger.warning(f"⚠️ تنبيه تدوير: {alert['message']}")
        return alert
    
    def create_rotation_plan(self, keys_collection: dict) -> dict:
        """إنشاء خطة تدوير المفاتيح"""
        rotation_plan = {
            "created_at": datetime.now().isoformat(),
            "total_keys": 0,
            "keys_to_rotate": [],
            "upcoming_rotations": [],
            "alerts": []
        }
        
        for platform, platform_data in keys_collection.get("encrypted_keys", {}).items():
            for key_info in platform_data.get("keys", []):
                rotation_plan["total_keys"] += 1
                
                # فحص انتهاء الصلاحية
                if self.check_key_expiry(key_info.get("encrypted_data", {})):
                    rotation_plan["keys_to_rotate"].append({
                        "key_id": key_info.get("key_id"),
                        "platform": platform,
                        "name": key_info.get("name"),
                        "priority": "immediate"
                    })
                else:
                    # فحص التدوير القادم
                    created_at = datetime.fromisoformat(
                        key_info.get("encrypted_data", {}).get("created_at", datetime.now().isoformat())
                    )
                    rotation_days = self.rotation_schedule.get(platform, self.rotation_schedule["default"])
                    expiry_date = created_at + timedelta(days=rotation_days)
                    days_until_expiry = (expiry_date - datetime.now()).days
                    
                    if days_until_expiry <= 14:  # تنبيه قبل أسبوعين
                        alert = self.generate_rotation_alert(key_info, days_until_expiry)
                        rotation_plan["alerts"].append(alert)
                        
                        rotation_plan["upcoming_rotations"].append({
                            "key_id": key_info.get("key_id"),
                            "platform": platform,
                            "name": key_info.get("name"),
                            "days_until_expiry": days_until_expiry,
                            "scheduled_date": expiry_date.isoformat()
                        })
        
        # حفظ خطة التدوير
        plan_file = self.rotation_dir / f"rotation_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(plan_file, 'w', encoding='utf-8') as f:
            json.dump(rotation_plan, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 تم إنشاء خطة التدوير: {len(rotation_plan['keys_to_rotate'])} مفتاح للتدوير الفوري")
        return rotation_plan
    
    def simulate_key_rotation(self, key_info: dict) -> dict:
        """محاكاة تدوير المفتاح (للاختبار)"""
        # في التطبيق الحقيقي، هذا سيتصل بـ API المنصة لإنشاء مفتاح جديد
        new_key_data = {
            "old_key_id": key_info.get("key_id"),
            "new_key_id": f"rotated_{int(time.time())}",
            "platform": key_info.get("platform"),
            "name": key_info.get("name"),
            "rotation_date": datetime.now().isoformat(),
            "status": "rotated",
            "new_key": f"new_simulated_key_{int(time.time())}",  # مفتاح محاكي
            "rotation_method": "automatic"
        }
        
        # تسجيل التدوير
        rotation_log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": "key_rotated",
            "old_key_id": key_info.get("key_id"),
            "new_key_id": new_key_data["new_key_id"],
            "platform": key_info.get("platform"),
            "success": True
        }
        
        self.rotation_log.append(rotation_log_entry)
        
        logger.info(f"🔄 تم تدوير المفتاح: {key_info.get('name')} -> {new_key_data['new_key_id']}")
        return new_key_data
    
    def execute_rotation_plan(self, rotation_plan: dict) -> dict:
        """تنفيذ خطة التدوير"""
        execution_results = {
            "started_at": datetime.now().isoformat(),
            "total_rotations": len(rotation_plan.get("keys_to_rotate", [])),
            "successful_rotations": 0,
            "failed_rotations": 0,
            "rotation_details": []
        }
        
        for key_to_rotate in rotation_plan.get("keys_to_rotate", []):
            try:
                # محاكاة تدوير المفتاح
                rotation_result = self.simulate_key_rotation(key_to_rotate)
                
                execution_results["successful_rotations"] += 1
                execution_results["rotation_details"].append({
                    "key_id": key_to_rotate.get("key_id"),
                    "platform": key_to_rotate.get("platform"),
                    "status": "success",
                    "new_key_id": rotation_result.get("new_key_id")
                })
                
            except Exception as e:
                logger.error(f"❌ فشل في تدوير المفتاح {key_to_rotate.get('key_id')}: {e}")
                execution_results["failed_rotations"] += 1
                execution_results["rotation_details"].append({
                    "key_id": key_to_rotate.get("key_id"),
                    "platform": key_to_rotate.get("platform"),
                    "status": "failed",
                    "error": str(e)
                })
        
        execution_results["completed_at"] = datetime.now().isoformat()
        
        # حفظ نتائج التنفيذ
        results_file = self.rotation_dir / f"rotation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(execution_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ تم تنفيذ التدوير: {execution_results['successful_rotations']} نجح، {execution_results['failed_rotations']} فشل")
        return execution_results
    
    def setup_automatic_rotation(self) -> str:
        """إعداد التدوير التلقائي"""
        scheduler_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⏰ جدولة التدوير التلقائي للمفاتيح
Automatic Key Rotation Scheduler
"""

import schedule
import time
import json
from datetime import datetime
from pathlib import Path

def daily_rotation_check():
    """فحص يومي للمفاتيح التي تحتاج تدوير"""
    print(f"🔍 فحص يومي للمفاتيح - {datetime.now()}")
    
    # هنا يمكن إضافة منطق الفحص اليومي
    # مثل تحميل المفاتيح وفحص تواريخ انتهاء الصلاحية
    
    rotation_check = {
        "timestamp": datetime.now().isoformat(),
        "check_type": "daily",
        "status": "completed"
    }
    
    # حفظ سجل الفحص
    log_file = Path(__file__).parent / "rotation_checks.log"
    with open(log_file, 'a') as f:
        f.write(json.dumps(rotation_check) + "\\n")

def weekly_rotation_report():
    """تقرير أسبوعي للتدوير"""
    print(f"📊 تقرير أسبوعي للتدوير - {datetime.now()}")
    
    # إنشاء تقرير أسبوعي
    weekly_report = {
        "timestamp": datetime.now().isoformat(),
        "report_type": "weekly",
        "period": "last_7_days"
    }
    
    report_file = Path(__file__).parent / f"weekly_report_{datetime.now().strftime('%Y%m%d')}.json"
    with open(report_file, 'w') as f:
        json.dump(weekly_report, f, indent=2)

# جدولة المهام
schedule.every().day.at("09:00").do(daily_rotation_check)
schedule.every().week.at("09:00").do(weekly_rotation_report)

print("⏰ تم تشغيل جدولة التدوير التلقائي")
print("📅 فحص يومي: 09:00 صباحاً")
print("📊 تقرير أسبوعي: كل أسبوع 09:00 صباحاً")

# تشغيل الجدولة
while True:
    schedule.run_pending()
    time.sleep(60)  # فحص كل دقيقة
'''
        
        scheduler_file = self.rotation_dir / "rotation_scheduler.py"
        with open(scheduler_file, 'w', encoding='utf-8') as f:
            f.write(scheduler_script)
        
        os.chmod(scheduler_file, 0o755)
        
        logger.info(f"⏰ تم إعداد جدولة التدوير التلقائي: {scheduler_file}")
        return str(scheduler_file)
    
    def start_rotation_system(self, keys_collection_file: str) -> dict:
        """بدء نظام التدوير"""
        logger.info("🚀 بدء نظام تدوير المفاتيح")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "actions_completed": [],
            "files_created": []
        }
        
        try:
            # تحميل مجموعة المفاتيح
            with open(keys_collection_file, 'r', encoding='utf-8') as f:
                keys_collection = json.load(f)
            
            # إنشاء خطة التدوير
            rotation_plan = self.create_rotation_plan(keys_collection)
            results["actions_completed"].append(f"📋 تم إنشاء خطة التدوير لـ {rotation_plan['total_keys']} مفتاح")
            
            # تنفيذ التدوير للمفاتيح المنتهية الصلاحية
            if rotation_plan["keys_to_rotate"]:
                execution_results = self.execute_rotation_plan(rotation_plan)
                results["actions_completed"].append(f"🔄 تم تدوير {execution_results['successful_rotations']} مفتاح")
            
            # إعداد الجدولة التلقائية
            scheduler_file = self.setup_automatic_rotation()
            results["files_created"].append(scheduler_file)
            results["actions_completed"].append("⏰ تم إعداد الجدولة التلقائية")
            
            # حفظ سجل التدوير
            log_file = self.rotation_dir / f"rotation_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.rotation_log, f, indent=2, ensure_ascii=False)
            
            results["files_created"].append(str(log_file))
            results["actions_completed"].append("📝 تم حفظ سجل التدوير")
            
            logger.info("✅ تم بدء نظام التدوير بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء نظام التدوير: {e}")
            results["status"] = "error"
            results["error"] = str(e)
        
        return results

def main():
    """الدالة الرئيسية"""
    print("🔄 نظام تدوير المفاتيح التلقائي")
    print("=" * 50)
    
    rotation_system = HorusKeyRotationSystem()
    
    # بدء نظام التدوير
    keys_file = Path(__file__).parent / "vault" / "secure"
    secure_files = list(keys_file.glob("encrypted_keys_*.json"))
    
    if secure_files:
        latest_file = max(secure_files, key=os.path.getctime)
        results = rotation_system.start_rotation_system(str(latest_file))
        
        print("\n✅ تم بدء نظام التدوير!")
        print("\n✅ الإجراءات المكتملة:")
        for action in results.get("actions_completed", []):
            print(f"   {action}")
        
        print("\n📁 الملفات المنشأة:")
        for file_path in results.get("files_created", []):
            print(f"   📄 {file_path}")
    else:
        print("❌ لم يتم العثور على ملفات المفاتيح المشفرة")

if __name__ == "__main__":
    main()
