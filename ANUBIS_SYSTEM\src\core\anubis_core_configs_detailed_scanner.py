#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فاحص مفصل لمجلدي core و configs
Anubis Core & Configs Detailed Scanner

فحص مفصل وعميق لمجلدي core و configs مع:
- تحليل محتوى الملفات
- فحص التبعيات والاستيرادات
- تحليل التكوينات
- اقتراحات العزل المخصصة

مطور بالتعاون مع الوكلاء الذكيين
"""

import json
import os
import sys
import ast
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class AnubisCoreConfigsScanner:
    """فاحص مفصل لمجلدي core و configs"""
    
    def __init__(self):
        self.project_root = Path(".").resolve()
        self.anubis_path = self.project_root / "anubis"
        self.core_path = self.anubis_path / "core"
        self.configs_path = self.anubis_path / "configs"
        
        self.scan_results = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "scanner_type": "core_configs_detailed",
                "version": "1.0"
            },
            "core_analysis": {},
            "configs_analysis": {},
            "dependencies_map": {},
            "isolation_recommendations": {}
        }
    
    def run_detailed_scan(self):
        """تشغيل الفحص المفصل"""
        print("🔍 بدء الفحص المفصل لمجلدي core و configs")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("=" * 60)
        
        # فحص مجلد core
        self.analyze_core_directory()
        
        # فحص مجلد configs
        self.analyze_configs_directory()
        
        # تحليل التبعيات
        self.analyze_dependencies()
        
        # إنشاء توصيات العزل
        self.create_isolation_recommendations()
        
        # طباعة التقرير
        self.print_detailed_report()
        
        return self.scan_results
    
    def analyze_core_directory(self):
        """تحليل مجلد core بالتفصيل"""
        print("\n🏺 تحليل مجلد core بالتفصيل...")
        
        if not self.core_path.exists():
            print("  ❌ مجلد core غير موجود")
            return
        
        core_analysis = {
            "path": str(self.core_path),
            "files_count": 0,
            "python_files": {},
            "imports_analysis": {},
            "classes_analysis": {},
            "functions_analysis": {},
            "complexity_score": 0
        }
        
        python_files = list(self.core_path.glob("*.py"))
        core_analysis["files_count"] = len(python_files)
        
        for py_file in python_files:
            if py_file.name.startswith("__"):
                continue
                
            file_analysis = self.analyze_python_file(py_file)
            core_analysis["python_files"][py_file.name] = file_analysis
        
        # حساب نقاط التعقيد
        total_classes = sum(len(f.get("classes", [])) for f in core_analysis["python_files"].values())
        total_functions = sum(len(f.get("functions", [])) for f in core_analysis["python_files"].values())
        total_imports = sum(len(f.get("imports", [])) for f in core_analysis["python_files"].values())
        
        core_analysis["complexity_score"] = total_classes * 3 + total_functions * 2 + total_imports
        
        self.scan_results["core_analysis"] = core_analysis
        
        print(f"  ✅ تم تحليل {len(python_files)} ملف Python")
        print(f"  📊 نقاط التعقيد: {core_analysis['complexity_score']}")
    
    def analyze_python_file(self, file_path: Path) -> Dict[str, Any]:
        """تحليل ملف Python بالتفصيل"""
        analysis = {
            "size_kb": round(file_path.stat().st_size / 1024, 2),
            "lines_count": 0,
            "imports": [],
            "classes": [],
            "functions": [],
            "docstring": "",
            "complexity": "low"
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                analysis["lines_count"] = len(lines)
            
            # تحليل AST
            tree = ast.parse(content)
            
            # استخراج الاستيرادات
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        analysis["imports"].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        analysis["imports"].append(f"{module}.{alias.name}")
            
            # استخراج الكلاسات
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "methods": [],
                        "docstring": ast.get_docstring(node) or ""
                    }
                    
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            class_info["methods"].append(item.name)
                    
                    analysis["classes"].append(class_info)
            
            # استخراج الدوال
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and not any(node in cls.body for cls in ast.walk(tree) if isinstance(cls, ast.ClassDef)):
                    func_info = {
                        "name": node.name,
                        "args_count": len(node.args.args),
                        "docstring": ast.get_docstring(node) or ""
                    }
                    analysis["functions"].append(func_info)
            
            # استخراج docstring الرئيسي
            analysis["docstring"] = ast.get_docstring(tree) or ""
            
            # تحديد مستوى التعقيد
            total_elements = len(analysis["classes"]) + len(analysis["functions"]) + len(analysis["imports"])
            if total_elements > 20:
                analysis["complexity"] = "high"
            elif total_elements > 10:
                analysis["complexity"] = "medium"
            else:
                analysis["complexity"] = "low"
                
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def analyze_configs_directory(self):
        """تحليل مجلد configs بالتفصيل"""
        print("\n⚙️ تحليل مجلد configs بالتفصيل...")
        
        if not self.configs_path.exists():
            print("  ❌ مجلد configs غير موجود")
            return
        
        configs_analysis = {
            "path": str(self.configs_path),
            "files_count": 0,
            "json_files": {},
            "config_types": {},
            "sensitive_data": [],
            "validation_status": {}
        }
        
        config_files = list(self.configs_path.glob("*.json"))
        configs_analysis["files_count"] = len(config_files)
        
        for config_file in config_files:
            file_analysis = self.analyze_config_file(config_file)
            configs_analysis["json_files"][config_file.name] = file_analysis
            
            # تصنيف نوع التكوين
            config_type = self.determine_config_type(config_file.name, file_analysis)
            configs_analysis["config_types"][config_file.name] = config_type
            
            # فحص البيانات الحساسة
            sensitive_items = self.check_sensitive_data(file_analysis)
            if sensitive_items:
                configs_analysis["sensitive_data"].extend([
                    {"file": config_file.name, "items": sensitive_items}
                ])
        
        self.scan_results["configs_analysis"] = configs_analysis
        
        print(f"  ✅ تم تحليل {len(config_files)} ملف تكوين")
        print(f"  🔐 ملفات تحتوي على بيانات حساسة: {len(configs_analysis['sensitive_data'])}")
    
    def analyze_config_file(self, file_path: Path) -> Dict[str, Any]:
        """تحليل ملف تكوين JSON"""
        analysis = {
            "size_kb": round(file_path.stat().st_size / 1024, 2),
            "valid_json": False,
            "keys_count": 0,
            "nested_levels": 0,
            "structure": {},
            "content_summary": {}
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                analysis["valid_json"] = True
                analysis["keys_count"] = len(data) if isinstance(data, dict) else 0
                analysis["nested_levels"] = self.calculate_json_depth(data)
                analysis["structure"] = self.get_json_structure(data)
                analysis["content_summary"] = self.summarize_json_content(data)
                
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def calculate_json_depth(self, obj, depth=0):
        """حساب عمق JSON"""
        if isinstance(obj, dict):
            return max([self.calculate_json_depth(v, depth + 1) for v in obj.values()], default=depth)
        elif isinstance(obj, list):
            return max([self.calculate_json_depth(item, depth + 1) for item in obj], default=depth)
        else:
            return depth
    
    def get_json_structure(self, obj):
        """الحصول على هيكل JSON"""
        if isinstance(obj, dict):
            return {k: self.get_json_structure(v) for k, v in list(obj.items())[:5]}  # أول 5 مفاتيح
        elif isinstance(obj, list):
            return f"array[{len(obj)}]"
        else:
            return type(obj).__name__
    
    def summarize_json_content(self, obj):
        """ملخص محتوى JSON"""
        summary = {
            "total_keys": 0,
            "data_types": {},
            "key_patterns": []
        }
        
        def count_elements(data, prefix=""):
            if isinstance(data, dict):
                summary["total_keys"] += len(data)
                for k, v in data.items():
                    key_type = type(v).__name__
                    summary["data_types"][key_type] = summary["data_types"].get(key_type, 0) + 1
                    
                    if len(summary["key_patterns"]) < 10:  # أول 10 مفاتيح
                        summary["key_patterns"].append(f"{prefix}{k}")
                    
                    if isinstance(v, (dict, list)):
                        count_elements(v, f"{prefix}{k}.")
        
        count_elements(obj)
        return summary
    
    def determine_config_type(self, filename: str, analysis: Dict) -> str:
        """تحديد نوع ملف التكوين"""
        filename_lower = filename.lower()
        
        if "database" in filename_lower:
            return "database_config"
        elif "ai" in filename_lower:
            return "ai_config"
        elif "langsmith" in filename_lower:
            return "integration_config"
        elif "default" in filename_lower:
            return "default_config"
        elif "project" in filename_lower:
            return "project_config"
        elif "system" in filename_lower:
            return "system_config"
        else:
            return "general_config"
    
    def check_sensitive_data(self, analysis: Dict) -> List[str]:
        """فحص البيانات الحساسة"""
        sensitive_items = []
        
        structure = analysis.get("structure", {})
        
        def check_keys(obj, path=""):
            if isinstance(obj, dict):
                for k, v in obj.items():
                    key_lower = k.lower()
                    if any(sensitive in key_lower for sensitive in ["password", "key", "secret", "token", "credential"]):
                        sensitive_items.append(f"{path}{k}")
                    
                    if isinstance(v, dict):
                        check_keys(v, f"{path}{k}.")
        
        check_keys(structure)
        return sensitive_items
    
    def analyze_dependencies(self):
        """تحليل التبعيات بين الملفات"""
        print("\n🔗 تحليل التبعيات...")
        
        dependencies_map = {
            "core_internal_deps": {},
            "core_external_deps": {},
            "configs_usage": {},
            "circular_deps": []
        }
        
        # تحليل التبعيات الداخلية في core
        core_files = self.scan_results["core_analysis"].get("python_files", {})
        
        for file_name, file_info in core_files.items():
            imports = file_info.get("imports", [])
            
            # التبعيات الداخلية (ملفات core أخرى)
            internal_deps = [imp for imp in imports if any(core_file.replace(".py", "") in imp for core_file in core_files.keys())]
            dependencies_map["core_internal_deps"][file_name] = internal_deps
            
            # التبعيات الخارجية
            external_deps = [imp for imp in imports if not any(core_file.replace(".py", "") in imp for core_file in core_files.keys())]
            dependencies_map["core_external_deps"][file_name] = external_deps[:10]  # أول 10
        
        # تحليل استخدام ملفات التكوين
        for file_name, file_info in core_files.items():
            config_usage = []
            # البحث عن مراجع لملفات التكوين في الكود
            # هذا تحليل مبسط - يمكن تحسينه
            if "config" in file_name.lower():
                config_usage.append("config_manager")
            dependencies_map["configs_usage"][file_name] = config_usage
        
        self.scan_results["dependencies_map"] = dependencies_map
        
        print(f"  ✅ تم تحليل التبعيات")
    
    def create_isolation_recommendations(self):
        """إنشاء توصيات العزل المخصصة"""
        print("\n💡 إنشاء توصيات العزل المخصصة...")
        
        recommendations = {
            "core_isolation": {
                "strategy": "microservices",
                "components": [],
                "priority": "critical"
            },
            "configs_isolation": {
                "strategy": "shared_volume",
                "security_level": "high",
                "encryption": True
            },
            "deployment_plan": {
                "phase1": [],
                "phase2": [],
                "phase3": []
            }
        }
        
        # تحليل ملفات core للعزل
        core_files = self.scan_results["core_analysis"].get("python_files", {})
        
        for file_name, file_info in core_files.items():
            complexity = file_info.get("complexity", "low")
            classes_count = len(file_info.get("classes", []))
            
            component_rec = {
                "file": file_name,
                "isolation_type": "container" if complexity == "high" else "module",
                "resource_requirements": {
                    "memory": "512M" if complexity == "high" else "256M",
                    "cpu": "0.5" if complexity == "high" else "0.2"
                },
                "dependencies": self.scan_results["dependencies_map"]["core_internal_deps"].get(file_name, [])
            }
            
            recommendations["core_isolation"]["components"].append(component_rec)
        
        # تخطيط مراحل النشر
        high_priority = [c for c in recommendations["core_isolation"]["components"] if c["isolation_type"] == "container"]
        medium_priority = [c for c in recommendations["core_isolation"]["components"] if c["isolation_type"] == "module"]
        
        recommendations["deployment_plan"]["phase1"] = ["configs_setup", "base_infrastructure"]
        recommendations["deployment_plan"]["phase2"] = [c["file"] for c in high_priority]
        recommendations["deployment_plan"]["phase3"] = [c["file"] for c in medium_priority]
        
        self.scan_results["isolation_recommendations"] = recommendations
        
        print(f"  ✅ تم إنشاء توصيات العزل")
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("🔍 تقرير الفحص المفصل لمجلدي core و configs")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("="*60)
        
        # تقرير core
        core_analysis = self.scan_results["core_analysis"]
        print(f"\n🏺 مجلد core:")
        print(f"  📄 عدد الملفات: {core_analysis.get('files_count', 0)}")
        print(f"  📊 نقاط التعقيد: {core_analysis.get('complexity_score', 0)}")
        
        print(f"\n  📋 الملفات الرئيسية:")
        for file_name, file_info in core_analysis.get("python_files", {}).items():
            complexity = file_info.get("complexity", "low")
            lines = file_info.get("lines_count", 0)
            classes = len(file_info.get("classes", []))
            print(f"    🔹 {file_name}: {lines} lines, {classes} classes, {complexity} complexity")
        
        # تقرير configs
        configs_analysis = self.scan_results["configs_analysis"]
        print(f"\n⚙️ مجلد configs:")
        print(f"  📄 عدد الملفات: {configs_analysis.get('files_count', 0)}")
        print(f"  🔐 ملفات حساسة: {len(configs_analysis.get('sensitive_data', []))}")
        
        print(f"\n  📋 ملفات التكوين:")
        for file_name, config_type in configs_analysis.get("config_types", {}).items():
            file_info = configs_analysis.get("json_files", {}).get(file_name, {})
            keys_count = file_info.get("keys_count", 0)
            print(f"    🔹 {file_name}: {config_type}, {keys_count} keys")
        
        # توصيات العزل
        recommendations = self.scan_results["isolation_recommendations"]
        print(f"\n💡 توصيات العزل:")
        print(f"  🏺 استراتيجية core: {recommendations['core_isolation']['strategy']}")
        print(f"  ⚙️ استراتيجية configs: {recommendations['configs_isolation']['strategy']}")
        
        core_components = recommendations["core_isolation"]["components"]
        container_components = [c for c in core_components if c["isolation_type"] == "container"]
        print(f"  🐳 مكونات تحتاج حاويات منفصلة: {len(container_components)}")
        
        print("\n🎯 الخلاصة: تم فحص وتحليل مجلدي core و configs بالتفصيل")
        print("="*60)
    
    def save_detailed_report(self, filename: str = None) -> str:
        """حفظ التقرير المفصل"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_core_configs_detailed_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ التقرير المفصل في: {filename}")
        return filename


def main():
    """الدالة الرئيسية"""
    print("🔍 فاحص مفصل لمجلدي core و configs")
    print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
    
    # إنشاء الفاحص
    scanner = AnubisCoreConfigsScanner()
    
    # تشغيل الفحص المفصل
    scanner.run_detailed_scan()
    
    # حفظ التقرير
    report_file = scanner.save_detailed_report()
    
    print(f"\n📊 تقرير مفصل متاح في: {report_file}")


if __name__ == "__main__":
    main()
