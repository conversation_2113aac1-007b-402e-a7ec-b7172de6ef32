#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 عرض توضيحي للـ Workflow الذكي مع LangSmith
Smart Workflow Demo with LangSmith Integration

مثال عملي لكيفية ربط الوكلاء معاً بذكاء باستخدام LangSmith
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# إضافة المسارات
sys.path.append(str(Path(__file__).parent.parent))

try:
    from anubis.agents.enhanced_error_detector import EnhancedErrorDetectorAgent
    from anubis.agents.enhanced_file_organizer import EnhancedFileOrganizerAgent
    from anubis.agents.enhanced_memory_agent import EnhancedMemoryAgent
    from anubis.agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
    from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer
    from anubis.core.langsmith_wrapper import langsmith_wrapper
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    sys.exit(1)


class SmartWorkflowOrchestrator:
    """🔗 منسق الـ Workflows الذكية"""

    def __init__(self, project_path: str = "."):
        self.project_path = project_path
        self.langsmith = langsmith_wrapper
        self.agents = {}
        self.workflow_history = []

        print("🔗 تهيئة منسق الـ Workflows الذكية")
        self.initialize_agents()

    def initialize_agents(self):
        """تهيئة جميع الوكلاء"""

        with self.langsmith.trace_agent_operation("orchestrator", "initialize_agents"):
            agents_config = [
                ("error_detector", EnhancedErrorDetectorAgent),
                ("project_analyzer", EnhancedProjectAnalyzerAgent),
                ("file_organizer", EnhancedFileOrganizerAgent),
                ("memory_agent", EnhancedMemoryAgent),
                ("code_analyzer", SmartCodeAnalyzer),
            ]

            for agent_name, agent_class in agents_config:
                try:
                    with self.langsmith.trace_agent_operation("initialization", agent_name):
                        self.agents[agent_name] = agent_class(
                            self.project_path,
                            {"langsmith_enabled": True},
                            verbose=False,
                        )
                        print(f"   ✅ تم تهيئة {agent_name}")
                except Exception as e:
                    print(f"   ❌ فشل تهيئة {agent_name}: {e}")

    def smart_project_analysis_workflow(self) -> Dict[str, Any]:
        """سير عمل ذكي للتحليل الشامل للمشروع"""

        print("\n🚀 بدء سير العمل الذكي للتحليل الشامل")

        with self.langsmith.trace_agent_operation(
            "workflow", "smart_project_analysis"
        ) as main_trace:
            workflow_results = {
                "workflow_id": f"analysis_{int(time.time())}",
                "start_time": datetime.now().isoformat(),
                "steps": {},
            }

            # الخطوة 1: تحليل المشروع الأساسي
            print("   🔍 الخطوة 1: تحليل المشروع الأساسي...")
            with self.langsmith.trace_agent_operation("step_1", "project_analysis"):
                try:
                    project_info = self.agents["project_analyzer"].analyze_project()
                    workflow_results["steps"]["project_analysis"] = {
                        "status": "success",
                        "data": project_info,
                        "timestamp": datetime.now().isoformat(),
                    }
                    print(f"      ✅ نوع المشروع: {project_info.get('project_type', 'غير محدد')}")
                except Exception as e:
                    workflow_results["steps"]["project_analysis"] = {
                        "status": "failed",
                        "error": str(e),
                    }
                    print(f"      ❌ فشل: {e}")

            # الخطوة 2: اختيار الوكلاء المناسبين
            print("   🎯 الخطوة 2: اختيار الوكلاء المناسبين...")
            with self.langsmith.trace_agent_operation("step_2", "agent_selection"):
                project_type = (
                    workflow_results["steps"]["project_analysis"]
                    .get("data", {})
                    .get("project_type", "unknown")
                )
                selected_agents = self.select_agents_for_project_type(project_type)
                workflow_results["steps"]["agent_selection"] = {
                    "status": "success",
                    "selected_agents": selected_agents,
                    "reasoning": f"اختيار بناءً على نوع المشروع: {project_type}",
                }
                print(f"      ✅ تم اختيار {len(selected_agents)} وكلاء")

            # الخطوة 3: تشغيل الوكلاء المختارين بالتوازي
            print("   ⚡ الخطوة 3: تشغيل الوكلاء المختارين...")
            with self.langsmith.trace_agent_operation("step_3", "parallel_execution"):
                parallel_results = {}

                for agent_name in selected_agents:
                    print(f"      🔄 تشغيل {agent_name}...")
                    with self.langsmith.trace_agent_operation("parallel_agent", agent_name):
                        try:
                            if agent_name == "error_detector":
                                result = self.agents["error_detector"].scan_entire_project()
                            elif agent_name == "file_organizer":
                                result = self.agents["file_organizer"].organize_files()
                            elif agent_name == "code_analyzer":
                                result = self.agents["code_analyzer"].analyze_project_files(
                                    max_files=3
                                )
                            else:
                                result = {
                                    "status": "skipped",
                                    "reason": "no specific task defined",
                                }

                            parallel_results[agent_name] = {
                                "status": "success",
                                "data": result,
                            }
                            print(f"         ✅ {agent_name} - نجح")

                        except Exception as e:
                            parallel_results[agent_name] = {
                                "status": "failed",
                                "error": str(e),
                            }
                            print(f"         ❌ {agent_name} - فشل: {e}")

                workflow_results["steps"]["parallel_execution"] = parallel_results

            # الخطوة 4: تجميع النتائج وإنتاج رؤى ذكية
            print("   🧠 الخطوة 4: تجميع النتائج وإنتاج رؤى ذكية...")
            with self.langsmith.trace_agent_operation("step_4", "intelligent_aggregation"):
                smart_insights = self.generate_smart_insights(workflow_results)
                workflow_results["steps"]["intelligent_aggregation"] = {
                    "status": "success",
                    "insights": smart_insights,
                }
                print(f"      ✅ تم إنتاج {len(smart_insights)} رؤية ذكية")

            # الخطوة 5: حفظ النتائج في الذاكرة
            print("   💾 الخطوة 5: حفظ النتائج في الذاكرة...")
            with self.langsmith.trace_agent_operation("step_5", "memory_storage"):
                try:
                    memory_key = f"workflow_analysis_{workflow_results['workflow_id']}"
                    self.agents["memory_agent"].store_memory(
                        memory_key, workflow_results, "workflow_results"
                    )
                    workflow_results["steps"]["memory_storage"] = {
                        "status": "success",
                        "memory_key": memory_key,
                    }
                    print(f"      ✅ تم حفظ النتائج بالمفتاح: {memory_key}")
                except Exception as e:
                    workflow_results["steps"]["memory_storage"] = {
                        "status": "failed",
                        "error": str(e),
                    }
                    print(f"      ❌ فشل الحفظ: {e}")

            # إنهاء الـ workflow
            workflow_results["end_time"] = datetime.now().isoformat()
            workflow_results["duration"] = (
                datetime.fromisoformat(workflow_results["end_time"])
                - datetime.fromisoformat(workflow_results["start_time"])
            ).total_seconds()

            self.workflow_history.append(workflow_results)

            print(f"\n🏆 تم إكمال سير العمل في {workflow_results['duration']:.1f} ثانية")

            return workflow_results

    def select_agents_for_project_type(self, project_type: str) -> List[str]:
        """اختيار الوكلاء المناسبين حسب نوع المشروع"""

        # قواعد الاختيار الذكي
        base_agents = ["error_detector"]  # أساسي لجميع المشاريع

        if project_type in ["react", "vue", "angular", "frontend"]:
            return base_agents + ["file_organizer"]  # تنظيم مهم للفرونت إند
        elif project_type in ["django", "fastapi", "flask", "backend"]:
            return base_agents + ["code_analyzer"]  # تحليل كود مهم للباك إند
        elif project_type in ["python", "general"]:
            return base_agents + ["code_analyzer", "file_organizer"]  # شامل
        else:
            return base_agents + ["file_organizer"]  # افتراضي

    def generate_smart_insights(self, workflow_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """إنتاج رؤى ذكية من نتائج الـ workflow"""

        insights = []

        # تحليل نتائج كشف الأخطاء
        if "error_detector" in workflow_results["steps"]["parallel_execution"]:
            error_data = workflow_results["steps"]["parallel_execution"]["error_detector"]
            if error_data["status"] == "success":
                errors_found = error_data["data"].get("total_issues", 0)
                if errors_found > 0:
                    insights.append(
                        {
                            "type": "quality_concern",
                            "priority": "high" if errors_found > 10 else "medium",
                            "title": f"تم العثور على {errors_found} مشكلة في الكود",
                            "recommendation": "يُنصح بإصلاح الأخطاء الحرجة أولاً",
                            "action": "review_errors",
                        }
                    )
                else:
                    insights.append(
                        {
                            "type": "quality_good",
                            "priority": "info",
                            "title": "جودة الكود ممتازة - لا توجد أخطاء",
                            "recommendation": "استمر في الممارسات الجيدة",
                            "action": "maintain_quality",
                        }
                    )

        # تحليل نتائج تنظيم الملفات
        if "file_organizer" in workflow_results["steps"]["parallel_execution"]:
            org_data = workflow_results["steps"]["parallel_execution"]["file_organizer"]
            if org_data["status"] == "success":
                insights.append(
                    {
                        "type": "organization",
                        "priority": "medium",
                        "title": "تم تحسين تنظيم الملفات",
                        "recommendation": "راجع التغييرات المقترحة",
                        "action": "review_organization",
                    }
                )

        # تحليل نتائج تحليل الكود
        if "code_analyzer" in workflow_results["steps"]["parallel_execution"]:
            code_data = workflow_results["steps"]["parallel_execution"]["code_analyzer"]
            if code_data["status"] == "success":
                insights.append(
                    {
                        "type": "code_analysis",
                        "priority": "medium",
                        "title": "تم تحليل الكود بالذكاء الاصطناعي",
                        "recommendation": "راجع التوصيات المقترحة للتحسين",
                        "action": "implement_suggestions",
                    }
                )

        # رؤى عامة حول الـ workflow
        total_steps = len(workflow_results["steps"])
        successful_steps = sum(
            1
            for step in workflow_results["steps"].values()
            if isinstance(step, dict) and step.get("status") == "success"
        )

        if successful_steps == total_steps:
            insights.append(
                {
                    "type": "workflow_success",
                    "priority": "info",
                    "title": f"تم إكمال جميع خطوات التحليل ({successful_steps}/{total_steps})",
                    "recommendation": "النظام يعمل بكفاءة عالية",
                    "action": "continue_monitoring",
                }
            )
        else:
            insights.append(
                {
                    "type": "workflow_partial",
                    "priority": "medium",
                    "title": f"تم إكمال {successful_steps}/{total_steps} من خطوات التحليل",
                    "recommendation": "راجع الخطوات الفاشلة وأصلحها",
                    "action": "fix_failed_steps",
                }
            )

        return insights

    def adaptive_error_fixing_workflow(self) -> Dict[str, Any]:
        """سير عمل تكيفي لإصلاح الأخطاء"""

        print("\n🔧 بدء سير العمل التكيفي لإصلاح الأخطاء")

        with self.langsmith.trace_agent_operation("workflow", "adaptive_error_fixing"):
            # 1. كشف الأخطاء أولاً
            print("   🔍 كشف الأخطاء...")
            with self.langsmith.trace_agent_operation("step_1", "error_detection"):
                errors = self.agents["error_detector"].scan_entire_project()

            if errors.get("total_issues", 0) == 0:
                print("   ✅ لا توجد أخطاء للإصلاح")
                return {"status": "no_errors", "message": "المشروع خالي من الأخطاء"}

            # 2. تصنيف الأخطاء حسب الأولوية
            print("   📊 تصنيف الأخطاء...")
            with self.langsmith.trace_agent_operation("step_2", "error_classification"):
                classified_errors = self.classify_errors_by_priority(errors)

            # 3. إصلاح تدريجي بناءً على الأولوية
            print("   🔧 إصلاح تدريجي...")
            fixing_results = {}

            for priority in ["critical", "high", "medium"]:
                if priority in classified_errors:
                    with self.langsmith.trace_agent_operation("fixing", f"priority_{priority}"):
                        print(f"      🔄 إصلاح أخطاء {priority}...")
                        # هنا يمكن إضافة منطق الإصلاح التلقائي
                        fixing_results[priority] = {
                            "errors_count": len(classified_errors[priority]),
                            "status": "analyzed",  # في المستقبل: "fixed"
                            "suggestions": self.generate_fixing_suggestions(
                                classified_errors[priority]
                            ),
                        }

            return {
                "status": "completed",
                "errors_found": errors.get("total_issues", 0),
                "classification": classified_errors,
                "fixing_results": fixing_results,
            }

    def classify_errors_by_priority(self, errors: Dict[str, Any]) -> Dict[str, List]:
        """تصنيف الأخطاء حسب الأولوية"""

        classified = {"critical": [], "high": [], "medium": [], "low": []}

        # منطق التصنيف (مبسط)
        for file_path, file_errors in errors.get("files", {}).items():
            for error in file_errors.get("issues", []):
                error_type = error.get("type", "").lower()

                if any(keyword in error_type for keyword in ["security", "sql", "xss"]):
                    classified["critical"].append(error)
                elif any(keyword in error_type for keyword in ["syntax", "import"]):
                    classified["high"].append(error)
                elif any(keyword in error_type for keyword in ["style", "format"]):
                    classified["low"].append(error)
                else:
                    classified["medium"].append(error)

        return classified

    def generate_fixing_suggestions(self, errors: List[Dict[str, Any]]) -> List[str]:
        """إنتاج اقتراحات الإصلاح"""

        suggestions = []

        for error in errors[:5]:  # أول 5 أخطاء
            error_type = error.get("type", "")
            suggestions.append(f"إصلاح {error_type} في السطر {error.get('line', 'غير محدد')}")

        return suggestions

    def get_workflow_analytics(self) -> Dict[str, Any]:
        """الحصول على تحليلات الـ workflows"""

        if not self.workflow_history:
            return {"message": "لا توجد workflows مسجلة"}

        total_workflows = len(self.workflow_history)
        successful_workflows = sum(
            1
            for w in self.workflow_history
            if w.get("steps", {}).get("intelligent_aggregation", {}).get("status") == "success"
        )

        avg_duration = sum(w.get("duration", 0) for w in self.workflow_history) / total_workflows

        return {
            "total_workflows": total_workflows,
            "successful_workflows": successful_workflows,
            "success_rate": f"{successful_workflows/total_workflows*100:.1f}%",
            "average_duration": f"{avg_duration:.1f} seconds",
            "langsmith_traces": self.langsmith.get_traces_summary(),
        }


def demo_smart_workflows():
    """عرض توضيحي للـ workflows الذكية"""

    print("🔗 عرض توضيحي للـ Workflows الذكية مع LangSmith")
    print("=" * 60)

    # إنشاء المنسق
    orchestrator = SmartWorkflowOrchestrator(".")

    # تشغيل سير العمل الذكي للتحليل
    analysis_result = orchestrator.smart_project_analysis_workflow()

    # تشغيل سير العمل التكيفي للإصلاح
    fixing_result = orchestrator.adaptive_error_fixing_workflow()

    # عرض التحليلات
    print("\n📈 تحليلات الـ Workflows:")
    analytics = orchestrator.get_workflow_analytics()
    for key, value in analytics.items():
        print(f"   📊 {key}: {value}")

    print("\n🏆 انتهى العرض التوضيحي للـ Workflows الذكية!")
    print("\n💡 الميزات المحققة:")
    print("   🔗 ربط ذكي بين الوكلاء")
    print("   📊 تتبع شامل مع LangSmith")
    print("   🧠 رؤى ذكية من النتائج")
    print("   🔧 إصلاح تكيفي للأخطاء")
    print("   📈 تحليلات الأداء")

    return orchestrator


if __name__ == "__main__":
    orchestrator = demo_smart_workflows()

    print(f"\n🏺 نظام أنوبيس + LangSmith = workflows ذكية! 🚀")
