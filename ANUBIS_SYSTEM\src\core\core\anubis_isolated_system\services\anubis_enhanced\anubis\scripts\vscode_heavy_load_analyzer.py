#!/usr/bin/env python3
"""
VS Code Heavy Load Analyzer
محلل الأحمال الثقيلة لـ VS Code

Specialized script for analyzing high number of VS Code processes (like 72+ processes)
and providing optimization recommendations.
"""

import json
import os
import subprocess
import time
from collections import Counter, defaultdict
from datetime import datetime

import psutil


class VSCodeHeavyLoadAnalyzer:
    def __init__(self):
        self.processes = []
        self.process_groups = defaultdict(list)
        self.extensions_info = {}
        self.workspace_info = {}
        self.performance_issues = []

    def collect_all_vscode_processes(self):
        """جمع جميع عمليات VS Code"""
        print("🔍 Collecting all VS Code processes...")

        vscode_keywords = [
            "code",
            "electron",
            "extensionhost",
            "node",
            "rg",
            "git",
            "typescript",
            "eslint",
            "prettier",
            "python",
            "java",
        ]

        for proc in psutil.process_iter(["pid", "name", "cmdline", "memory_info", "cpu_percent"]):
            try:
                proc_info = proc.info
                proc_name = proc_info["name"].lower()
                cmdline = " ".join(proc_info["cmdline"] or []).lower()

                # تحديد إذا كانت العملية مرتبطة بـ VS Code
                is_vscode_related = False
                process_type = "Unknown"

                if any(keyword in proc_name or keyword in cmdline for keyword in vscode_keywords):
                    is_vscode_related = True
                    process_type = self.classify_process(proc_info, cmdline)

                if is_vscode_related:
                    memory_mb = proc_info["memory_info"].rss / (1024 * 1024)

                    process_data = {
                        "pid": proc_info["pid"],
                        "name": proc_info["name"],
                        "cmdline": " ".join(proc_info["cmdline"] or []),
                        "memory_mb": memory_mb,
                        "cpu_percent": proc_info["cpu_percent"],
                        "type": process_type,
                        "create_time": proc.create_time(),
                    }

                    self.processes.append(process_data)
                    self.process_groups[process_type].append(process_data)

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        print(f"✅ Found {len(self.processes)} VS Code related processes")
        return len(self.processes)

    def classify_process(self, proc_info, cmdline):
        """تصنيف نوع العملية"""
        name = proc_info["name"].lower()

        if "code.exe" in name or "code" == name:
            if "--type=renderer" in cmdline:
                return "Renderer Process"
            elif "--type=gpu-process" in cmdline:
                return "GPU Process"
            elif "--type=utility" in cmdline:
                return "Utility Process"
            else:
                return "Main Process"
        elif "extensionhost" in cmdline or "extension-host" in cmdline:
            return "Extension Host"
        elif "node.exe" in name or "node" in name:
            if "typescript" in cmdline:
                return "TypeScript Server"
            elif "eslint" in cmdline:
                return "ESLint Server"
            elif "prettier" in cmdline:
                return "Prettier Server"
            elif "python" in cmdline:
                return "Python Language Server"
            else:
                return "Node.js Process"
        elif "rg.exe" in name or "rg" in name:
            return "Search Process (ripgrep)"
        elif "git" in name:
            return "Git Process"
        elif "electron" in name:
            return "Electron Process"
        else:
            return "Other VS Code Process"

    def analyze_memory_usage(self):
        """تحليل استهلاك الذاكرة"""
        total_memory = sum(p["memory_mb"] for p in self.processes)

        memory_by_type = {}
        for proc_type, procs in self.process_groups.items():
            memory_by_type[proc_type] = {
                "count": len(procs),
                "total_memory": sum(p["memory_mb"] for p in procs),
                "avg_memory": sum(p["memory_mb"] for p in procs) / len(procs),
                "max_memory": max(p["memory_mb"] for p in procs) if procs else 0,
            }

        return total_memory, memory_by_type

    def analyze_cpu_usage(self):
        """تحليل استهلاك المعالج"""
        total_cpu = sum(p["cpu_percent"] for p in self.processes)

        cpu_by_type = {}
        for proc_type, procs in self.process_groups.items():
            cpu_by_type[proc_type] = {
                "count": len(procs),
                "total_cpu": sum(p["cpu_percent"] for p in procs),
                "avg_cpu": sum(p["cpu_percent"] for p in procs) / len(procs),
                "max_cpu": max(p["cpu_percent"] for p in procs) if procs else 0,
            }

        return total_cpu, cpu_by_type

    def identify_performance_issues(self):
        """تحديد مشاكل الأداء"""
        issues = []

        # فحص العمليات عالية الاستهلاك
        for proc in self.processes:
            if proc["memory_mb"] > 500:  # أكثر من 500 MB
                issues.append(
                    {
                        "type": "High Memory Process",
                        "process": proc["name"],
                        "pid": proc["pid"],
                        "value": f"{proc['memory_mb']:.1f} MB",
                        "severity": "High",
                    }
                )

            if proc["cpu_percent"] > 10:  # أكثر من 10% CPU
                issues.append(
                    {
                        "type": "High CPU Process",
                        "process": proc["name"],
                        "pid": proc["pid"],
                        "value": f"{proc['cpu_percent']:.1f}%",
                        "severity": "Medium",
                    }
                )

        # فحص العمليات المكررة
        process_names = [p["name"] for p in self.processes]
        name_counts = Counter(process_names)

        for name, count in name_counts.items():
            if count > 10:  # أكثر من 10 عمليات من نفس النوع
                issues.append(
                    {
                        "type": "Too Many Duplicate Processes",
                        "process": name,
                        "value": f"{count} instances",
                        "severity": "High",
                    }
                )

        # فحص أنواع العمليات
        for proc_type, procs in self.process_groups.items():
            if len(procs) > 15:  # أكثر من 15 عملية من نفس النوع
                issues.append(
                    {
                        "type": "Too Many Processes of Same Type",
                        "process": proc_type,
                        "value": f"{len(procs)} processes",
                        "severity": "Medium",
                    }
                )

        return issues

    def get_optimization_recommendations(self):
        """الحصول على توصيات التحسين"""
        recommendations = []
        total_processes = len(self.processes)

        # توصيات عامة
        if total_processes > 50:
            recommendations.append(
                {
                    "priority": "High",
                    "action": "Restart VS Code",
                    "reason": f"Too many processes ({total_processes}). Restart will consolidate processes.",
                    "impact": "High",
                }
            )

        # توصيات خاصة بالإضافات
        extension_hosts = len(self.process_groups.get("Extension Host", []))
        if extension_hosts > 5:
            recommendations.append(
                {
                    "priority": "High",
                    "action": "Disable unused extensions",
                    "reason": f"Multiple extension hosts ({extension_hosts}) detected.",
                    "impact": "High",
                }
            )

        # توصيات خاصة بالنوافذ
        renderers = len(self.process_groups.get("Renderer Process", []))
        if renderers > 10:
            recommendations.append(
                {
                    "priority": "Medium",
                    "action": "Close unused VS Code windows",
                    "reason": f"Multiple renderer processes ({renderers}) suggest many open windows.",
                    "impact": "Medium",
                }
            )

        # توصيات خاصة بخوادم اللغة
        language_servers = 0
        for proc_type in [
            "TypeScript Server",
            "Python Language Server",
            "ESLint Server",
        ]:
            language_servers += len(self.process_groups.get(proc_type, []))

        if language_servers > 8:
            recommendations.append(
                {
                    "priority": "Medium",
                    "action": "Configure language server settings",
                    "reason": f"Multiple language servers ({language_servers}) running.",
                    "impact": "Medium",
                }
            )

        # توصيات خاصة بالبحث
        search_processes = len(self.process_groups.get("Search Process (ripgrep)", []))
        if search_processes > 5:
            recommendations.append(
                {
                    "priority": "Low",
                    "action": "Limit search scope",
                    "reason": f"Multiple search processes ({search_processes}) detected.",
                    "impact": "Low",
                }
            )

        return recommendations

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل"""
        total_memory, memory_by_type = self.analyze_memory_usage()
        total_cpu, cpu_by_type = self.analyze_cpu_usage()
        issues = self.identify_performance_issues()
        recommendations = self.get_optimization_recommendations()

        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_processes": len(self.processes),
                "total_memory_mb": total_memory,
                "total_cpu_percent": total_cpu,
                "process_types": len(self.process_groups),
                "performance_issues_count": len(issues),
            },
            "process_breakdown": {
                "by_type": dict(self.process_groups),
                "memory_analysis": memory_by_type,
                "cpu_analysis": cpu_by_type,
            },
            "performance_issues": issues,
            "optimization_recommendations": recommendations,
            "all_processes": self.processes,
        }

        return report

    def print_analysis_summary(self, report):
        """طباعة ملخص التحليل"""
        print("\n" + "=" * 60)
        print("🚨 VS CODE HEAVY LOAD ANALYSIS REPORT")
        print("=" * 60)

        summary = report["summary"]
        print(f"📊 Summary:")
        print(f"   Total Processes: {summary['total_processes']}")
        print(f"   Total Memory Usage: {summary['total_memory_mb']:.1f} MB")
        print(f"   Total CPU Usage: {summary['total_cpu_percent']:.1f}%")
        print(f"   Process Types: {summary['process_types']}")

        print(f"\n📋 Process Breakdown:")
        for proc_type, data in report["process_breakdown"]["memory_analysis"].items():
            print(f"   {proc_type}: {data['count']} processes, {data['total_memory']:.1f} MB")

        print(f"\n⚠️  Performance Issues ({len(report['performance_issues'])}):")
        for issue in report["performance_issues"][:10]:  # أعلى 10 مشاكل
            print(
                f"   [{issue['severity']}] {issue['type']}: {issue.get('process', 'N/A')} - {issue['value']}"
            )

        print(f"\n💡 Optimization Recommendations:")
        for rec in report["optimization_recommendations"]:
            print(f"   [{rec['priority']}] {rec['action']}")
            print(f"       Reason: {rec['reason']}")
            print(f"       Impact: {rec['impact']}")
            print()

        print("=" * 60)

    def save_report(self, report):
        """حفظ التقرير"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Universal-AI-Assistants/reports/vscode_heavy_load_analysis_{timestamp}.json"

        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Detailed report saved: {filename}")
        return filename

    def run_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🔥 Starting Heavy Load Analysis for VS Code...")
        print("This analysis is designed for systems with 50+ VS Code processes")
        print("-" * 60)

        # جمع العمليات
        process_count = self.collect_all_vscode_processes()

        if process_count == 0:
            print("❌ No VS Code processes found!")
            return None

        if process_count < 20:
            print(
                f"ℹ️  Only {process_count} processes found. This tool is optimized for heavy loads (50+ processes)"
            )

        # إنشاء التقرير
        report = self.generate_detailed_report()

        # طباعة الملخص
        self.print_analysis_summary(report)

        # حفظ التقرير
        filename = self.save_report(report)

        return report, filename


def main():
    """الدالة الرئيسية"""
    analyzer = VSCodeHeavyLoadAnalyzer()

    try:
        result = analyzer.run_analysis()

        if result:
            report, filename = result
            print(f"\n🎯 Analysis completed!")
            print(f"📄 Full report: {filename}")

            # اقتراح إجراءات فورية
            if report["summary"]["total_processes"] > 70:
                print(f"\n🚨 URGENT: {report['summary']['total_processes']} processes detected!")
                print("   Immediate action recommended:")
                print("   1. Save your work")
                print("   2. Close VS Code completely")
                print("   3. Restart VS Code")
                print("   4. Open only essential workspaces")

    except KeyboardInterrupt:
        print("\n👋 Analysis cancelled by user")
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")


if __name__ == "__main__":
    main()
