{"project_info": {"name": "Universal AI Assistants (Anubis)", "root_path": "C:\\Users\\<USER>", "created_at": "2025-07-20T09:57:53.758603", "version": "1.0.0"}, "main_system": {"description": "النظام الأساسي", "paths": {"main_file": "main.py", "readme": "README.md", "requirements": "requirements.txt", "docker_compose": "docker-compose.yml", "dockerfile": "Dockerfile", "system_directory": "anubis_main_system"}}, "isolation_system": {"description": "النظام المعزول", "paths": {"root_directory": "anubis_isolation_system", "api_service": "anubis_isolation_system/api", "worker_service": "anubis_isolation_system/worker", "monitor_service": "anubis_isolation_system/monitor", "docker_compose": "anubis_isolation_system/docker-compose-isolation.yml", "readme": "anubis_isolation_system/README.md", "launcher": "anubis_isolation_system/anubis_docker_isolation_launcher.py", "manager": "anubis_isolation_system/anubis_isolation_system_manager.py"}}, "ai_team": {"description": "فريق الذكاء الاصطناعي", "paths": {"root_directory": "anubis_ai_team", "workflow_manager": "anubis_ai_team/team_workflow_manager.py", "collaboration_system": "anubis_ai_team/anubis_ai_team_collaboration_system.py", "collaboration_helper": "anubis_ai_team/anubis_ai_collaboration_helper.py", "team_config": "anubis_ai_team/anubis_ai_team_collaboration_plan.json", "gemini_helper": "anubis_ai_team/anubis_gemini_cli_helper.py", "readme": "anubis_ai_team/README.md"}}, "project_paths": {"description": "إدارة مسارات المشروع", "paths": {"root_directory": "anubis_project_paths", "paths_manager": "anubis_project_paths/project_paths_manager.py", "quick_access": "anubis_project_paths/quick_access_shortcuts.py", "navigation_helper": "anubis_project_paths/project_navigation_helper.py", "readme": "anubis_project_paths/README.md"}}, "data_directories": {"description": "مجلدات البيانات والتخزين", "paths": {"configs": "configs", "database": "database", "data": "data", "logs": "logs", "documentation": "documentation", "reports": "reports", "scripts": "scripts", "utilities": "utilities", "archive_and_backups": "archive_and_backups", "workspace": "workspace"}}, "specialized_systems": {"description": "الأنظمة المتخصصة", "paths": {"universal_ai_system": "universal_ai_system", "workflows_and_automation": "workflows_and_automation", "tools_and_utilities": "tools_and_utilities", "reports_and_analysis": "reports_and_analysis", "isolation_systems": "isolation_systems", "isolation_configs": "isolation_configs"}}, "service_endpoints": {"description": "نقاط الخدمات", "endpoints": {"main_system": {"base_url": "http://localhost:8000", "health": "http://localhost:8000/health", "docs": "http://localhost:8000/docs"}, "isolation_api": {"base_url": "http://localhost:8080", "health": "http://localhost:8080/health", "docs": "http://localhost:8080/docs", "status": "http://localhost:8080/status"}, "isolation_monitor": {"base_url": "http://localhost:9090", "health": "http://localhost:9090/health", "services": "http://localhost:9090/monitor/services", "system": "http://localhost:9090/monitor/system"}}}, "important_files": {"description": "الملفات المهمة", "files": {"project_analyzer": "anubis_project_analyzer.py", "system_tester": "anubis_complete_system_test.py", "api_tester": "anubis_api_comprehensive_test.py", "move_isolation": "move_isolation_files.py", "organize_team": "organize_ai_team.py"}}}