#!/usr/bin/env python3
"""
VS Code Emergency Cleanup
تنظيف طوارئ لـ VS Code

Emergency script for when VS Code has too many processes (like 72+ processes)
Provides immediate solutions and cleanup options.
"""

import os
import subprocess
import time
from datetime import datetime

import psutil


class VSCodeEmergencyCleanup:
    def __init__(self):
        self.vscode_processes = []
        self.total_memory_mb = 0
        self.total_cpu_percent = 0

    def scan_vscode_processes(self):
        """فحص سريع لعمليات VS Code"""
        print("🔍 Scanning VS Code processes...")

        for proc in psutil.process_iter(["pid", "name", "memory_info", "cpu_percent"]):
            try:
                if "code" in proc.info["name"].lower():
                    memory_mb = proc.info["memory_info"].rss / (1024 * 1024)

                    self.vscode_processes.append(
                        {
                            "pid": proc.info["pid"],
                            "name": proc.info["name"],
                            "memory_mb": memory_mb,
                            "cpu_percent": proc.info["cpu_percent"],
                            "process": proc,
                        }
                    )

                    self.total_memory_mb += memory_mb
                    self.total_cpu_percent += proc.info["cpu_percent"]

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        print(f"✅ Found {len(self.vscode_processes)} VS Code processes")
        print(f"📊 Total Memory: {self.total_memory_mb:.1f} MB")
        print(f"📊 Total CPU: {self.total_cpu_percent:.1f}%")

        return len(self.vscode_processes)

    def show_emergency_status(self):
        """عرض حالة الطوارئ"""
        process_count = len(self.vscode_processes)

        print("\n" + "🚨" * 20)
        print("EMERGENCY STATUS REPORT")
        print("🚨" * 20)

        if process_count > 70:
            status = "CRITICAL"
            color = "🔴"
        elif process_count > 50:
            status = "HIGH"
            color = "🟠"
        elif process_count > 30:
            status = "MEDIUM"
            color = "🟡"
        else:
            status = "NORMAL"
            color = "🟢"

        print(f"{color} Status: {status}")
        print(f"📊 Process Count: {process_count}")
        print(
            f"💾 Memory Usage: {self.total_memory_mb:.1f} MB ({self.total_memory_mb/1024:.1f} GB)"
        )
        print(f"⚡ CPU Usage: {self.total_cpu_percent:.1f}%")

        if process_count > 50:
            print(f"\n⚠️  WARNING: VS Code is running {process_count} processes!")
            print("This is significantly higher than normal (usually 5-15 processes)")
            print("Immediate action is recommended to prevent system slowdown")

        print("🚨" * 20)

    def get_emergency_recommendations(self):
        """الحصول على توصيات الطوارئ"""
        process_count = len(self.vscode_processes)
        recommendations = []

        if process_count > 70:
            recommendations = [
                "🚨 IMMEDIATE: Save all your work NOW",
                "🚨 IMMEDIATE: Close VS Code completely (all windows)",
                "🚨 IMMEDIATE: Restart VS Code with minimal extensions",
                "🔧 Check for runaway extensions",
                "🔧 Consider restarting your computer if VS Code won't close",
            ]
        elif process_count > 50:
            recommendations = [
                "⚠️  HIGH: Save your work",
                "⚠️  HIGH: Close unnecessary VS Code windows",
                "⚠️  HIGH: Disable non-essential extensions",
                "🔧 Restart VS Code when convenient",
                "🔧 Check workspace settings",
            ]
        elif process_count > 30:
            recommendations = [
                "⚡ MEDIUM: Close unused VS Code windows",
                "⚡ MEDIUM: Review active extensions",
                "🔧 Consider workspace optimization",
                "🔧 Monitor process count regularly",
            ]
        else:
            recommendations = [
                "✅ Process count is normal",
                "🔧 Regular monitoring recommended",
            ]

        return recommendations

    def show_top_memory_processes(self, limit=10):
        """عرض العمليات الأكثر استهلاكاً للذاكرة"""
        if not self.vscode_processes:
            return

        print(f"\n📊 Top {limit} Memory-Consuming Processes:")
        print("-" * 50)
        print(f"{'PID':<8} {'Name':<20} {'Memory (MB)':<12} {'CPU %':<8}")
        print("-" * 50)

        sorted_processes = sorted(self.vscode_processes, key=lambda x: x["memory_mb"], reverse=True)

        for proc in sorted_processes[:limit]:
            print(
                f"{proc['pid']:<8} {proc['name']:<20} {proc['memory_mb']:<12.1f} {proc['cpu_percent']:<8.1f}"
            )

    def emergency_cleanup_options(self):
        """خيارات التنظيف الطارئ"""
        print(f"\n🛠️  EMERGENCY CLEANUP OPTIONS:")
        print("=" * 40)
        print("1. 🔴 Force close all VS Code processes (DANGEROUS - may lose work)")
        print("2. 🟠 Graceful shutdown (recommended)")
        print("3. 🟡 Close specific high-memory processes")
        print("4. 🔵 Generate detailed report only")
        print("5. ❌ Cancel")

        while True:
            try:
                choice = input("\nEnter your choice (1-5): ").strip()

                if choice == "1":
                    return self.force_close_all()
                elif choice == "2":
                    return self.graceful_shutdown()
                elif choice == "3":
                    return self.selective_cleanup()
                elif choice == "4":
                    return self.generate_report_only()
                elif choice == "5":
                    print("❌ Cleanup cancelled")
                    return False
                else:
                    print("Invalid choice. Please enter 1-5.")

            except KeyboardInterrupt:
                print("\n❌ Cleanup cancelled")
                return False

    def force_close_all(self):
        """إغلاق قسري لجميع العمليات"""
        print("\n🔴 WARNING: This will force close ALL VS Code processes!")
        print("🔴 You may lose unsaved work!")

        confirm = input("Type 'FORCE' to confirm: ").strip()

        if confirm != "FORCE":
            print("❌ Force close cancelled")
            return False

        print("🔴 Force closing all VS Code processes...")

        closed_count = 0
        for proc_data in self.vscode_processes:
            try:
                proc_data["process"].terminate()
                closed_count += 1
                print(f"✅ Closed PID {proc_data['pid']}")
            except:
                try:
                    proc_data["process"].kill()
                    closed_count += 1
                    print(f"⚡ Force killed PID {proc_data['pid']}")
                except:
                    print(f"❌ Failed to close PID {proc_data['pid']}")

        print(f"\n✅ Closed {closed_count} processes")
        return True

    def graceful_shutdown(self):
        """إغلاق تدريجي"""
        print("\n🟠 Attempting graceful shutdown...")
        print("This will try to close VS Code windows properly")

        try:
            # محاولة إغلاق VS Code بشكل طبيعي
            if os.name == "nt":  # Windows
                subprocess.run(["taskkill", "/IM", "Code.exe", "/T"], capture_output=True)
            else:  # Linux/Mac
                subprocess.run(["pkill", "-f", "code"], capture_output=True)

            print("✅ Graceful shutdown command sent")
            print("⏳ Waiting 10 seconds for processes to close...")

            time.sleep(10)

            # فحص ما إذا كانت العمليات ما زالت موجودة
            remaining = self.scan_vscode_processes()

            if remaining == 0:
                print("✅ All VS Code processes closed successfully")
                return True
            else:
                print(f"⚠️  {remaining} processes still running")
                print("You may need to close VS Code windows manually")
                return False

        except Exception as e:
            print(f"❌ Error during graceful shutdown: {e}")
            return False

    def selective_cleanup(self):
        """تنظيف انتقائي"""
        print("\n🟡 Selective cleanup - targeting high-memory processes")

        # ترتيب العمليات حسب استهلاك الذاكرة
        sorted_processes = sorted(self.vscode_processes, key=lambda x: x["memory_mb"], reverse=True)

        # إغلاق العمليات التي تستهلك أكثر من 200 MB
        high_memory_processes = [p for p in sorted_processes if p["memory_mb"] > 200]

        if not high_memory_processes:
            print("✅ No high-memory processes found (>200MB)")
            return True

        print(f"🎯 Found {len(high_memory_processes)} high-memory processes:")
        for proc in high_memory_processes:
            print(f"   PID {proc['pid']}: {proc['memory_mb']:.1f} MB")

        confirm = (
            input(f"\nClose these {len(high_memory_processes)} processes? (y/n): ").strip().lower()
        )

        if confirm != "y":
            print("❌ Selective cleanup cancelled")
            return False

        closed_count = 0
        for proc_data in high_memory_processes:
            try:
                proc_data["process"].terminate()
                closed_count += 1
                print(f"✅ Closed high-memory process PID {proc_data['pid']}")
                time.sleep(1)  # انتظار قصير بين العمليات
            except:
                print(f"❌ Failed to close PID {proc_data['pid']}")

        print(f"\n✅ Closed {closed_count} high-memory processes")
        return True

    def generate_report_only(self):
        """إنشاء تقرير فقط"""
        print("\n🔵 Generating detailed report...")

        report = {
            "timestamp": datetime.now().isoformat(),
            "emergency_status": {
                "total_processes": len(self.vscode_processes),
                "total_memory_mb": self.total_memory_mb,
                "total_cpu_percent": self.total_cpu_percent,
                "status_level": ("CRITICAL" if len(self.vscode_processes) > 70 else "HIGH"),
            },
            "processes": self.vscode_processes,
            "recommendations": self.get_emergency_recommendations(),
        }

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Universal-AI-Assistants/reports/vscode_emergency_report_{timestamp}.json"

        os.makedirs(os.path.dirname(filename), exist_ok=True)

        import json

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"✅ Emergency report saved: {filename}")
        return True

    def run_emergency_analysis(self):
        """تشغيل تحليل الطوارئ"""
        print("🚨 VS CODE EMERGENCY CLEANUP TOOL")
        print("=" * 40)

        # فحص العمليات
        process_count = self.scan_vscode_processes()

        if process_count == 0:
            print("✅ No VS Code processes found - system is clean!")
            return

        # عرض حالة الطوارئ
        self.show_emergency_status()

        # عرض العمليات الأكثر استهلاكاً
        self.show_top_memory_processes()

        # عرض التوصيات
        recommendations = self.get_emergency_recommendations()
        print(f"\n💡 EMERGENCY RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")

        # خيارات التنظيف
        if process_count > 20:
            print(f"\n🛠️  Emergency cleanup options available...")
            return self.emergency_cleanup_options()
        else:
            print(f"\n✅ Process count is manageable - no emergency action needed")
            return True


def main():
    """الدالة الرئيسية"""
    cleanup = VSCodeEmergencyCleanup()

    try:
        cleanup.run_emergency_analysis()

    except KeyboardInterrupt:
        print("\n👋 Emergency cleanup cancelled")
    except Exception as e:
        print(f"\n❌ Error during emergency cleanup: {e}")


if __name__ == "__main__":
    main()
