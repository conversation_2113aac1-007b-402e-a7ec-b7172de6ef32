@echo off
color 0A
title VS Code Control Center Pro - Task Manager متقدم

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █        🚀 VS Code Control Center Pro 🚀                    █
echo █                                                              █
echo █  Task Manager متقدم مع محادثة تفاعلية ووكلاء ذكيين         █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 تشغيل الواجهة المتقدمة...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تحميل وتثبيت Python من: https://python.org
    echo 📋 تأكد من إضافة Python إلى PATH
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من المكتبات الأساسية
echo 📦 التحقق من المكتبات المطلوبة...
python -c "import tkinter, psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 تثبيت المكتبات الأساسية...
    pip install psutil
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات الأساسية!
        echo 💡 جرب تشغيل الأمر يدوياً: pip install psutil
        pause
        exit /b 1
    )
)

REM التحقق من مكتبات الوكلاء الذكيين (اختيارية)
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🤖 تثبيت مكتبات الوكلاء الذكيين...
    pip install requests >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️ تحذير: سيعمل التطبيق بوضع أساسي (بدون وكلاء ذكيين)
    ) else (
        echo ✅ تم تثبيت مكتبات الوكلاء الذكيين
    )
) else (
    echo ✅ مكتبات الوكلاء الذكيين متوفرة
)

echo.
echo 🎯 المميزات المتقدمة المتاحة:
echo    📊 عرض العمليات مثل Task Manager الحقيقي
echo    💬 محادثة تفاعلية مع النظام
echo    🎛️ تحكم كامل في العمليات (إيقاف/تشغيل/إنهاء)
echo    🔍 بحث وفلترة متقدمة للعمليات
echo    📈 مراقبة الشبكة والأمان في الوقت الفعلي
echo    🧹 أدوات تنظيف وتحسين متقدمة
echo    💾 تقارير مفصلة وتصدير البيانات

python -c "import sys; sys.path.append('agents'); from agents.agent_coordinator import AgentCoordinator" >nul 2>&1
if %errorlevel% equ 0 (
    echo    🤖 6 وكلاء ذكيين للتحليل المتقدم
    echo    💬 محادثة مع Gemini و Ollama
    echo    🔍 توصيات مخصصة وذكية
    echo    📊 تحليل متقاطع من عدة وكلاء
)

echo.
echo 🎛️ تشغيل VS Code Control Center Pro...
echo.

REM تشغيل التطبيق المتقدم
python vscode_control_center_pro.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق!
    echo 💡 تأكد من وجود الملف: vscode_control_center_pro.py
    echo 🔧 أو تحقق من تثبيت المكتبات المطلوبة
    echo 🔄 يمكنك تجربة الواجهة الأساسية: run.bat
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق VS Code Control Center Pro
echo 💡 شكراً لاستخدام الواجهة المتقدمة!
pause
