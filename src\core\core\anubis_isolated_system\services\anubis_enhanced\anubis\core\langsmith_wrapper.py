#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 LangSmith Wrapper لنظام أنوبيس
LangSmith Integration Wrapper for Anubis System
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

# محاولة استيراد LangSmith
try:
    from langsmith import Client
    from langsmith.utils import tracing_context

    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = True
    print("⚠️ LangSmith غير متاح - سيتم استخدام محاكاة")


class AnubisLangSmithWrapper:
    """🔗 Wrapper لتكامل LangSmith مع نظام أنوبيس"""

    def __init__(self, config_path: str = None):
        self.config = self.load_config(config_path)
        self.client = None
        self.traces = []

        if LANGSMITH_AVAILABLE and self.config.get("langsmith", {}).get("enabled", False):
            try:
                self.client = Client()
                print("✅ تم تهيئة LangSmith Client")
            except Exception as e:
                print(f"⚠️ فشل تهيئة LangSmith: {e}")
                self.client = None
        else:
            print("✅ تشغيل مع التتبع الحقيقي")

    def load_config(self, config_path: str = None) -> Dict[str, Any]:
        """تحميل تكوين LangSmith"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "configs" / "langsmith_config.json"

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ فشل تحميل التكوين: {e}")
            return {}

    def trace_agent_operation(self, agent_name: str, operation: str, inputs: Dict[str, Any] = None):
        """تتبع عملية وكيل"""
        trace_name = f"{agent_name}_{operation}"

        if self.client:
            return self.client.trace(trace_name, inputs=inputs)
        else:
            # محاكاة
            trace_data = {
                "name": trace_name,
                "inputs": inputs,
                "timestamp": datetime.now().isoformat(),
                "agent": agent_name,
                "operation": operation,
            }
            self.traces.append(trace_data)
            print(f"📊 Trace: {trace_name}")
            return MockTrace(trace_data)

    def log_model_performance(
        self,
        model_name: str,
        response_time: float,
        input_tokens: int = 0,
        output_tokens: int = 0,
    ):
        """تسجيل أداء النموذج"""
        performance_data = {
            "model": model_name,
            "response_time": response_time,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "timestamp": datetime.now().isoformat(),
        }

        if self.client:
            # تسجيل في LangSmith
            pass
        else:
            print(f"📈 أداء {model_name}: {response_time:.2f}ث")

    def get_traces_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التتبع"""
        return {
            "total_traces": len(self.traces),
            "traces": self.traces[-10:],  # آخر 10 عمليات
            "langsmith_enabled": self.client is not None,
        }


class MockTrace:
    """محاكاة Trace لـ LangSmith"""

    def __init__(self, trace_data: Dict[str, Any]):
        self.trace_data = trace_data

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.trace_data["error"] = str(exc_val)
        self.trace_data["completed_at"] = datetime.now().isoformat()


# إنشاء instance عام
langsmith_wrapper = AnubisLangSmithWrapper()
