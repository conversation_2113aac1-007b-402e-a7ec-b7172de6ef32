<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 حورس موبايل - إدارة مفاتيح API</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .mobile-header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .mobile-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .mobile-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-box {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .mobile-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }
        
        .alert-badge {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            position: absolute;
            top: -5px;
            right: -5px;
        }
        
        .notification-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="mobile-header">
        <h1><i class="fas fa-eye" style="color: #f39c12;"></i> حورس موبايل</h1>
        <p>إدارة مفاتيح API</p>
    </div>
    
    <div class="mobile-grid">
        <div class="mobile-card">
            <h3 style="margin-bottom: 15px;"><i class="fas fa-chart-bar"></i> نظرة سريعة</h3>
            <div class="quick-stats">
                <div class="stat-box">
                    <div class="stat-number status-good">726</div>
                    <div class="stat-label">إجمالي المفاتيح</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number status-good">95%</div>
                    <div class="stat-label">نقاط الأمان</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number status-warning">12</div>
                    <div class="stat-label">تحتاج تدوير</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number status-good">15</div>
                    <div class="stat-label">نسخ احتياطية</div>
                </div>
            </div>
        </div>
        
        <div class="mobile-card">
            <h3 style="margin-bottom: 15px;"><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
            <div class="action-buttons">
                <button class="mobile-btn">
                    <i class="fas fa-sync-alt"></i>
                    تدوير المفاتيح
                </button>
                <button class="mobile-btn" style="background: linear-gradient(135deg, #27ae60, #229954);">
                    <i class="fas fa-download"></i>
                    نسخة احتياطية
                </button>
                <button class="mobile-btn" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                    <i class="fas fa-search"></i>
                    فحص المفاتيح
                </button>
            </div>
        </div>
        
        <div class="mobile-card">
            <h3 style="margin-bottom: 15px;">
                <i class="fas fa-bell"></i> 
                التنبيهات
                <span class="alert-badge" style="position: relative; margin-right: 10px;">3</span>
            </h3>
            <div>
                <div class="notification-item">
                    <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
                    <div>
                        <div>مفتاح GitHub ينتهي قريباً</div>
                        <small style="color: #666;">منذ ساعة</small>
                    </div>
                </div>
                <div class="notification-item">
                    <i class="fas fa-info-circle" style="color: #3498db;"></i>
                    <div>
                        <div>تم اكتشاف 3 مفاتيح جديدة</div>
                        <small style="color: #666;">منذ ساعتين</small>
                    </div>
                </div>
                <div class="notification-item">
                    <i class="fas fa-check-circle" style="color: #27ae60;"></i>
                    <div>
                        <div>تم إنشاء نسخة احتياطية</div>
                        <small style="color: #666;">منذ 3 ساعات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // تحديث البيانات للهاتف المحمول
        setInterval(() => {
            console.log('تحديث بيانات الهاتف المحمول...');
        }, 60000);
        
        // إضافة تأثيرات اللمس
        document.querySelectorAll('.mobile-btn').forEach(btn => {
            btn.addEventListener('touchstart', () => {
                btn.style.transform = 'scale(0.95)';
            });
            
            btn.addEventListener('touchend', () => {
                btn.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>