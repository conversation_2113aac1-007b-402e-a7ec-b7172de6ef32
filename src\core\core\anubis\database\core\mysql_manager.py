#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير قاعدة بيانات MySQL لنظام أنوبيس
Anubis AI Assistants System - MySQL Database Manager
"""

import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

import mysql.connector
from mysql.connector import Error

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class MySQLManager:
    """🗄️ مدير قاعدة بيانات MySQL لنظام أنوبيس"""

    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة مدير قاعدة البيانات"""
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = self._setup_logger()

    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config["database"]["mysql"]
        except Exception as e:
            raise Exception(f"خطأ في تحميل الإعدادات: {e}")

    def _setup_logger(self) -> logging.Logger:
        """إعداد نظام التسجيل"""
        logger = logging.getLogger("anubis_mysql_manager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config["host"],
                port=self.config["port"],
                user=self.config["user"],
                password=self.config["password"],
                database=self.config["database"],
                charset=self.config.get("charset", "utf8mb4"),
                autocommit=True,
            )
            return connection
        except Error as e:
            self.logger.error(f"خطأ في الاتصال: {e}")
            raise

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """تنفيذ استعلام SELECT"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            results = cursor.fetchall()
            cursor.close()
            return results
        except Error as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    def execute_update(self, query: str, params: Optional[tuple] = None) -> int:
        """تنفيذ استعلام INSERT/UPDATE/DELETE"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount
            cursor.close()
            return affected_rows
        except Error as e:
            self.logger.error(f"خطأ في تنفيذ التحديث: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    # === إدارة المشاريع ===

    def create_project(self, name: str, path: str, project_type: str, description: str = "") -> int:
        """إنشاء مشروع جديد"""
        query = """
        INSERT INTO projects (name, path, type, description)
        VALUES (%s, %s, %s, %s)
        """
        self.execute_update(query, (name, path, project_type, description))

        # الحصول على ID المشروع الجديد
        result = self.execute_query("SELECT LAST_INSERT_ID() as id")
        project_id = result[0]["id"]

        self.logger.info(f"تم إنشاء مشروع جديد: {name} (ID: {project_id})")
        return project_id

    def get_projects(self, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على المشاريع"""
        query = "SELECT * FROM projects ORDER BY created_at DESC LIMIT %s"
        return self.execute_query(query, (limit,))

    def get_project_by_id(self, project_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على مشروع بالمعرف"""
        query = "SELECT * FROM projects WHERE id = %s"
        results = self.execute_query(query, (project_id,))
        return results[0] if results else None

    def get_dashboard_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات لوحة التحكم"""
        stats = {}

        # إحصائيات المشاريع
        projects_stats = self.execute_query(
            """
        SELECT
            COUNT(*) as total_projects,
            COUNT(CASE WHEN type = 'python' THEN 1 END) as python_projects,
            COUNT(CASE WHEN type = 'javascript' THEN 1 END) as js_projects,
            COUNT(CASE WHEN type = 'java' THEN 1 END) as java_projects
        FROM projects
        """
        )[0]

        # إحصائيات التحليلات
        analyses_stats = self.execute_query(
            """
        SELECT
            COUNT(*) as total_analyses,
            AVG(score) as avg_score,
            COUNT(CASE WHEN score >= 90 THEN 1 END) as excellent_analyses,
            COUNT(CASE WHEN score < 70 THEN 1 END) as poor_analyses
        FROM analyses
        """
        )[0]

        # إحصائيات الأخطاء
        errors_stats = self.execute_query(
            """
        SELECT
            COUNT(*) as total_errors,
            COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_errors,
            COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_errors,
            COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_errors,
            COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_errors
        FROM errors
        """
        )[0]

        # إحصائيات الأنشطة الأخيرة
        recent_activities = self.execute_query(
            """
        SELECT COUNT(*) as recent_activities
        FROM activities
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        """
        )[0]

        stats.update(
            {
                "projects": projects_stats,
                "analyses": analyses_stats,
                "errors": errors_stats,
                "recent_activities": recent_activities["recent_activities"],
            }
        )

        return stats

    def get_recent_activities(self, limit: int = 10) -> List[Dict[str, Any]]:
        """الحصول على الأنشطة الأخيرة"""
        query = """
        SELECT a.*, p.name as project_name
        FROM activities a
        LEFT JOIN projects p ON a.project_id = p.id
        ORDER BY a.created_at DESC
        LIMIT %s
        """
        return self.execute_query(query, (limit,))

    def search_projects(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث في المشاريع"""
        query = """
        SELECT * FROM projects
        WHERE name LIKE %s OR description LIKE %s OR type LIKE %s
        ORDER BY created_at DESC
        """
        search_pattern = f"%{search_term}%"
        return self.execute_query(query, (search_pattern, search_pattern, search_pattern))

    def get_project_errors(self, project_id: int) -> List[Dict[str, Any]]:
        """الحصول على أخطاء مشروع معين"""
        query = """
        SELECT * FROM errors
        WHERE project_id = %s
        ORDER BY severity DESC, created_at DESC
        """
        return self.execute_query(query, (project_id,))

    def get_project_analyses(self, project_id: int) -> List[Dict[str, Any]]:
        """الحصول على تحليلات مشروع معين"""
        query = """
        SELECT * FROM analyses
        WHERE project_id = %s
        ORDER BY created_at DESC
        """
        return self.execute_query(query, (project_id,))

    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()

            self.logger.info("✅ اختبار الاتصال نجح")
            return True

        except Error as e:
            self.logger.error(f"❌ فشل اختبار الاتصال: {e}")
            return False


# مثال على الاستخدام
if __name__ == "__main__":
    mysql_manager = MySQLManager()

    print("🏺 مدير قاعدة بيانات MySQL لنظام أنوبيس")
    print("=" * 50)

    # اختبار الاتصال
    if mysql_manager.test_connection():
        print("✅ الاتصال بقاعدة البيانات نجح!")

        # عرض إحصائيات لوحة التحكم
        stats = mysql_manager.get_dashboard_stats()
        print(f"\n📊 إحصائيات النظام:")
        print(f"  📁 المشاريع: {stats['projects']['total_projects']}")
        print(f"  🔍 التحليلات: {stats['analyses']['total_analyses']}")
        print(f"  ❌ الأخطاء: {stats['errors']['total_errors']}")
        if stats["analyses"]["avg_score"]:
            print(f"  📈 متوسط النقاط: {stats['analyses']['avg_score']:.1f}")

        # عرض المشاريع
        projects = mysql_manager.get_projects(limit=5)
        print(f"\n📁 المشاريع الأخيرة ({len(projects)}):")
        for project in projects:
            print(f"  - {project['name']} ({project['type']})")

        # عرض الأنشطة الأخيرة
        activities = mysql_manager.get_recent_activities(limit=5)
        print(f"\n📈 الأنشطة الأخيرة ({len(activities)}):")
        for activity in activities:
            project_name = activity.get("project_name", "غير محدد")
            print(f"  - {activity['activity_type']}: {activity['description']} ({project_name})")

        print("\n🎉 جميع الاختبارات نجحت!")

    else:
        print("❌ فشل الاتصال بقاعدة البيانات!")
