version: '3.8'

services:
  anubis-tools:
    build: .
    container_name: anubis-tools-isolated
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد للأدوات
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # الشبكة المعزولة
    networks:
      - anubis-tools-net
    
    # الأحجام المعزولة
    volumes:
      - anubis-tools-data:/app/tools/data
      - anubis-tools-logs:/app/tools/logs:rw
      - anubis-tools-backups:/app/tools/backups:rw
      - anubis-tools-temp:/app/tools/temp:rw,noexec,nosuid,nodev
    
    # متغيرات البيئة
    environment:
      - TOOLS_MODE=isolated
      - MONITORING_ENABLED=true
      - AUTO_MAINTENANCE=true
      - LOG_LEVEL=INFO
    
    # حماية النظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=200m
      - /var/tmp:rw,noexec,nosuid,size=100m
    
    # إزالة الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - FOWNER
    
    # المنافذ المحدودة
    ports:
      - "127.0.0.1:8080:8080"
    
    # تسميات للمراقبة
    labels:
      - "anubis.component=tools"
      - "anubis.isolation.level=advanced"
      - "anubis.monitoring.enabled=true"
  
  anubis-tools-monitor:
    image: prom/prometheus:latest
    container_name: anubis-tools-monitor
    restart: unless-stopped
    networks:
      - anubis-tools-net
    volumes:
      - ./monitoring/prometheus-tools.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-tools-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9091:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
  
  anubis-tools-scheduler:
    build: .
    container_name: anubis-tools-scheduler
    restart: unless-stopped
    networks:
      - anubis-tools-net
    volumes:
      - anubis-tools-data:/app/tools/data:ro
      - anubis-tools-logs:/app/tools/logs:rw
    environment:
      - TOOLS_MODE=scheduler
      - SCHEDULE_MAINTENANCE=daily
    security_opt:
      - no-new-privileges:true
    command: ["python", "-c", "import schedule; import time; schedule.every().day.at('02:00').do(lambda: print('Maintenance')); time.sleep(86400)"]

# الشبكة المعزولة
networks:
  anubis-tools-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"

# الأحجام المعزولة
volumes:
  anubis-tools-data:
    driver: local
  anubis-tools-logs:
    driver: local
  anubis-tools-backups:
    driver: local
  anubis-tools-temp:
    driver: local
  anubis-tools-monitor-data:
    driver: local
