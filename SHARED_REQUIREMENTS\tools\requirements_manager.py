#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 مدير المتطلبات المشترك
Shared Requirements Manager

يدير متطلبات جميع المشاريع في النظام بطريقة منظمة ومشتركة
Manages requirements for all projects in the system in an organized and shared way
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
import subprocess

class SharedRequirementsManager:
    def __init__(self, base_dir=None):
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent.parent
        self.shared_dir = self.base_dir / "SHARED_REQUIREMENTS"
        self.shared_dir.mkdir(exist_ok=True)
        
        # مسارات المشاريع
        self.projects = {
            'ANUBIS_SYSTEM': self.base_dir / 'ANUBIS_SYSTEM',
            'HORUS_AI_TEAM': self.base_dir / 'HORUS_AI_TEAM',
            'PROJECT_DOCUMENTATION': self.base_dir / 'PROJECT_DOCUMENTATION'
        }
        
        # ملفات المتطلبات المشتركة
        self.shared_files = {
            'master': self.shared_dir / 'requirements_master.txt',
            'core': self.shared_dir / 'requirements_core.txt',
            'ai_ml': self.shared_dir / 'requirements_ai_ml.txt',
            'web': self.shared_dir / 'requirements_web_development.txt',
            'dev': self.shared_dir / 'requirements_development_tools.txt',
            'system': self.shared_dir / 'requirements_system_tools.txt',
            'optional': self.shared_dir / 'requirements_optional.txt'
        }

    def collect_existing_requirements(self):
        """جمع ملفات المتطلبات الموجودة من جميع المشاريع"""
        print("📋 جمع ملفات المتطلبات الموجودة...")
        
        collected_requirements = {}
        
        for project_name, project_path in self.projects.items():
            if project_path.exists():
                print(f"🔍 فحص مشروع: {project_name}")
                
                # البحث عن ملفات requirements
                req_files = list(project_path.glob("*requirements*.txt"))
                req_files.extend(list(project_path.glob("requirements*.txt")))
                
                project_reqs = {}
                for req_file in req_files:
                    try:
                        with open(req_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            project_reqs[req_file.name] = {
                                'path': str(req_file),
                                'content': content,
                                'packages': self.parse_requirements(content)
                            }
                        print(f"   ✅ وجد: {req_file.name}")
                    except Exception as e:
                        print(f"   ❌ خطأ في قراءة {req_file.name}: {e}")
                
                collected_requirements[project_name] = project_reqs
        
        return collected_requirements

    def parse_requirements(self, content):
        """تحليل محتوى ملف requirements"""
        packages = {}
        
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-'):
                if '==' in line:
                    name, version = line.split('==', 1)
                    packages[name.strip()] = version.strip()
                elif '>=' in line:
                    name, version = line.split('>=', 1)
                    packages[name.strip()] = f">={version.strip()}"
                elif line:
                    packages[line] = "latest"
        
        return packages

    def create_master_requirements(self):
        """إنشاء ملف المتطلبات الرئيسي"""
        print("📝 إنشاء ملف المتطلبات الرئيسي...")
        
        # قراءة النتائج الشاملة
        scan_results_file = self.shared_dir / 'comprehensive_scan_results.json'
        if scan_results_file.exists():
            with open(scan_results_file, 'r', encoding='utf-8') as f:
                scan_results = json.load(f)
        else:
            scan_results = {'all_packages': {}}
        
        # جمع المتطلبات الموجودة
        existing_reqs = self.collect_existing_requirements()
        
        # دمج جميع المكتبات
        all_packages = {}
        
        # إضافة المكتبات المكتشفة
        for package, info in scan_results.get('all_packages', {}).items():
            all_packages[package] = info.get('version', 'latest')
        
        # إضافة المكتبات من ملفات المشاريع
        for project_name, project_reqs in existing_reqs.items():
            for req_file, req_info in project_reqs.items():
                for package, version in req_info['packages'].items():
                    if package not in all_packages:
                        all_packages[package] = version
        
        # إنشاء ملف المتطلبات الرئيسي
        with open(self.shared_files['master'], 'w', encoding='utf-8') as f:
            f.write("# 📦 ملف المتطلبات الرئيسي المشترك\n")
            f.write("# Shared Master Requirements File\n")
            f.write(f"# تم إنشاؤه في: {datetime.now().isoformat()}\n")
            f.write(f"# إجمالي المكتبات: {len(all_packages)}\n")
            f.write("# يحتوي على جميع المكتبات المطلوبة لجميع المشاريع\n\n")
            
            for package, version in sorted(all_packages.items()):
                f.write(f"{package}=={version}\n")
        
        print(f"✅ تم إنشاء ملف المتطلبات الرئيسي: {self.shared_files['master']}")
        return all_packages

    def create_categorized_requirements(self):
        """إنشاء ملفات متطلبات مصنفة"""
        print("📂 إنشاء ملفات المتطلبات المصنفة...")
        
        # تعريف فئات المكتبات
        categories = {
            'core': [
                'python', 'pip', 'setuptools', 'wheel', 'packaging',
                'six', 'certifi', 'urllib3', 'charset-normalizer'
            ],
            'ai_ml': [
                'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
                'scikit-learn', 'tensorflow', 'torch', 'transformers',
                'openai', 'anthropic', 'langchain', 'ollama'
            ],
            'web': [
                'requests', 'flask', 'django', 'fastapi', 'aiohttp',
                'httpx', 'websockets', 'socketio', 'tornado'
            ],
            'dev': [
                'pytest', 'black', 'flake8', 'pylint', 'mypy',
                'pre-commit', 'tox', 'coverage', 'jupyter'
            ],
            'system': [
                'psutil', 'pathlib', 'shutil', 'subprocess',
                'threading', 'multiprocessing', 'asyncio'
            ]
        }
        
        # قراءة المكتبات المكتشفة
        scan_results_file = self.shared_dir / 'comprehensive_scan_results.json'
        if scan_results_file.exists():
            with open(scan_results_file, 'r', encoding='utf-8') as f:
                scan_results = json.load(f)
                all_packages = scan_results.get('all_packages', {})
        else:
            all_packages = {}
        
        # إنشاء ملفات مصنفة
        for category, keywords in categories.items():
            category_packages = {}
            
            for package, info in all_packages.items():
                if any(keyword in package.lower() for keyword in keywords):
                    category_packages[package] = info.get('version', 'latest')
            
            if category_packages:
                file_path = self.shared_files.get(category, self.shared_dir / f'requirements_{category}.txt')
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 📦 متطلبات {category.upper()}\n")
                    f.write(f"# {category.title()} Requirements\n")
                    f.write(f"# عدد المكتبات: {len(category_packages)}\n\n")
                    
                    for package, version in sorted(category_packages.items()):
                        f.write(f"{package}=={version}\n")
                
                print(f"   ✅ {category}: {len(category_packages)} مكتبة")

    def sync_project_requirements(self, project_name):
        """مزامنة متطلبات مشروع محدد"""
        print(f"🔄 مزامنة متطلبات مشروع: {project_name}")
        
        if project_name not in self.projects:
            print(f"❌ مشروع غير معروف: {project_name}")
            return False
        
        project_path = self.projects[project_name]
        if not project_path.exists():
            print(f"❌ مسار المشروع غير موجود: {project_path}")
            return False
        
        # نسخ ملفات المتطلبات المشتركة
        for req_type, shared_file in self.shared_files.items():
            if shared_file.exists():
                target_file = project_path / f"requirements_{req_type}.txt"
                try:
                    shutil.copy2(shared_file, target_file)
                    print(f"   ✅ نسخ {req_type}: {target_file.name}")
                except Exception as e:
                    print(f"   ❌ خطأ في نسخ {req_type}: {e}")
        
        return True

    def install_requirements(self, category='master', project=None):
        """تثبيت المتطلبات"""
        print(f"📦 تثبيت متطلبات {category}...")
        
        req_file = self.shared_files.get(category)
        if not req_file or not req_file.exists():
            print(f"❌ ملف المتطلبات غير موجود: {category}")
            return False
        
        try:
            # تثبيت المتطلبات
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(req_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت متطلبات {category} بنجاح")
                return True
            else:
                print(f"❌ خطأ في تثبيت {category}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تثبيت {category}: {e}")
            return False

    def generate_report(self):
        """إنشاء تقرير شامل"""
        print("📊 إنشاء تقرير إدارة المتطلبات...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'shared_files': {},
            'projects': {},
            'statistics': {}
        }
        
        # إحصائيات الملفات المشتركة
        for req_type, file_path in self.shared_files.items():
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    packages = self.parse_requirements(content)
                    report['shared_files'][req_type] = {
                        'file': str(file_path),
                        'package_count': len(packages),
                        'packages': list(packages.keys())
                    }
        
        # إحصائيات المشاريع
        for project_name, project_path in self.projects.items():
            if project_path.exists():
                req_files = list(project_path.glob("*requirements*.txt"))
                report['projects'][project_name] = {
                    'path': str(project_path),
                    'requirements_files': len(req_files),
                    'files': [f.name for f in req_files]
                }
        
        # إحصائيات عامة
        total_shared_packages = sum(
            info['package_count'] for info in report['shared_files'].values()
        )
        report['statistics'] = {
            'total_shared_files': len(report['shared_files']),
            'total_shared_packages': total_shared_packages,
            'total_projects': len([p for p in report['projects'].values() if p['requirements_files'] > 0])
        }
        
        # حفظ التقرير
        report_file = self.shared_dir / 'requirements_management_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ تم حفظ التقرير: {report_file}")
        return report

def main():
    """الدالة الرئيسية"""
    print("📦 مدير المتطلبات المشترك")
    print("=" * 50)
    
    manager = SharedRequirementsManager()
    
    # إنشاء ملفات المتطلبات
    manager.create_master_requirements()
    manager.create_categorized_requirements()
    
    # مزامنة المشاريع
    for project_name in manager.projects.keys():
        manager.sync_project_requirements(project_name)
    
    # إنشاء التقرير
    report = manager.generate_report()
    
    # عرض الإحصائيات
    print("\n📊 إحصائيات إدارة المتطلبات:")
    print(f"   📁 ملفات مشتركة: {report['statistics']['total_shared_files']}")
    print(f"   📦 مكتبات مشتركة: {report['statistics']['total_shared_packages']}")
    print(f"   🏗️ مشاريع متزامنة: {report['statistics']['total_projects']}")
    
    print("\n🎯 تم إكمال إدارة المتطلبات بنجاح!")

if __name__ == "__main__":
    main()
