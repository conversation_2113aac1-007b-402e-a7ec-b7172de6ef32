# -*- coding: utf-8 -*-
"""
🤖 وكيل Gemini - Gemini Agent
============================

وكيل ذكي يستخدم Gemini CLI للتحليل المتقدم والتوصيات الذكية
"""

import subprocess
import json
import time
from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent

class GeminiAgent(BaseAgent):
    """وكيل Gemini للتحليل المتقدم"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("GeminiAgent", config)
        self.gemini_available = self._check_gemini_availability()
        self.model = config.get('gemini_model', 'gemini-pro') if config else 'gemini-pro'
        
    def _check_gemini_availability(self) -> bool:
        """التحقق من توفر Gemini CLI"""
        try:
            result = subprocess.run(['gemini', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.logger.warning("⚠️ Gemini CLI غير متوفر")
            return False
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل متقدم باستخدام Gemini"""
        if not self.gemini_available:
            return {
                'error': 'Gemini CLI غير متوفر',
                'fallback_analysis': self._fallback_analysis(data)
            }
        
        try:
            # إعداد البيانات للتحليل
            analysis_prompt = self._create_analysis_prompt(data)
            
            # استدعاء Gemini
            gemini_response = self._call_gemini(analysis_prompt)
            
            # معالجة الاستجابة
            analysis = self._process_gemini_response(gemini_response)
            
            # إضافة معلومات إضافية
            analysis.update({
                'timestamp': time.time(),
                'agent': 'GeminiAgent',
                'model_used': self.model,
                'data_source': 'gemini_cli'
            })
            
            self.save_analysis(analysis)
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل Gemini: {e}")
            return {
                'error': str(e),
                'fallback_analysis': self._fallback_analysis(data)
            }
    
    def _create_analysis_prompt(self, data: Dict[str, Any]) -> str:
        """إنشاء prompt للتحليل"""
        prompt = f"""
أنت خبير في تحليل أداء الأنظمة وتحسين VS Code. قم بتحليل البيانات التالية وقدم توصيات ذكية:

البيانات:
{json.dumps(data, ensure_ascii=False, indent=2)}

المطلوب:
1. تحليل شامل لأداء النظام
2. تحديد المشاكل والاختناقات
3. توصيات محددة للتحسين
4. تقييم حالة VS Code
5. اقتراحات للصيانة الوقائية

قدم الإجابة بصيغة JSON مع الحقول التالية:
- system_analysis: تحليل النظام
- performance_issues: المشاكل المكتشفة
- recommendations: التوصيات
- priority_actions: الإجراءات ذات الأولوية
- health_score: نقاط الصحة (من 100)
"""
        return prompt
    
    def _call_gemini(self, prompt: str) -> str:
        """استدعاء Gemini CLI"""
        try:
            # إنشاء ملف مؤقت للـ prompt
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(prompt)
                prompt_file = f.name
            
            # استدعاء Gemini
            cmd = ['gemini', 'chat', '--file', prompt_file, '--model', self.model]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # تنظيف الملف المؤقت
            import os
            os.unlink(prompt_file)
            
            if result.returncode == 0:
                return result.stdout
            else:
                raise Exception(f"Gemini error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("انتهت مهلة انتظار Gemini")
        except Exception as e:
            raise Exception(f"خطأ في استدعاء Gemini: {e}")
    
    def _process_gemini_response(self, response: str) -> Dict[str, Any]:
        """معالجة استجابة Gemini"""
        try:
            # محاولة استخراج JSON من الاستجابة
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # إذا لم يكن JSON، قم بتحليل النص
                return self._parse_text_response(response)
                
        except json.JSONDecodeError:
            return self._parse_text_response(response)
    
    def _parse_text_response(self, response: str) -> Dict[str, Any]:
        """تحليل الاستجابة النصية"""
        lines = response.strip().split('\n')
        
        analysis = {
            'system_analysis': 'تحليل متقدم بواسطة Gemini',
            'performance_issues': [],
            'recommendations': [],
            'priority_actions': [],
            'health_score': 75,  # افتراضي
            'raw_response': response
        }
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # تحديد الأقسام
            if 'تحليل' in line or 'analysis' in line.lower():
                current_section = 'system_analysis'
            elif 'مشاكل' in line or 'issues' in line.lower():
                current_section = 'performance_issues'
            elif 'توصيات' in line or 'recommendations' in line.lower():
                current_section = 'recommendations'
            elif 'أولوية' in line or 'priority' in line.lower():
                current_section = 'priority_actions'
            elif line.startswith('-') or line.startswith('•'):
                # نقطة في قائمة
                item = line.lstrip('-•').strip()
                if current_section and current_section in analysis:
                    if isinstance(analysis[current_section], list):
                        analysis[current_section].append(item)
        
        return analysis
    
    def _fallback_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل احتياطي عند عدم توفر Gemini"""
        return {
            'system_analysis': 'تحليل أساسي (Gemini غير متوفر)',
            'performance_issues': ['لا يمكن التحليل المتقدم بدون Gemini'],
            'recommendations': [
                'تثبيت Gemini CLI للحصول على تحليل متقدم',
                'استخدام الوكلاء الأخرى للتحليل الأساسي'
            ],
            'priority_actions': ['تثبيت Gemini CLI'],
            'health_score': 50,
            'note': 'هذا تحليل أساسي فقط'
        }
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """استخراج التوصيات من التحليل"""
        if 'error' in analysis:
            return ['❌ خطأ في الحصول على توصيات Gemini']
        
        recommendations = []
        
        # توصيات من Gemini
        if 'recommendations' in analysis:
            recommendations.extend(analysis['recommendations'])
        
        # إجراءات ذات أولوية
        if 'priority_actions' in analysis:
            priority_actions = analysis['priority_actions']
            for action in priority_actions:
                recommendations.append(f"🔥 أولوية عالية: {action}")
        
        # تقييم الصحة
        health_score = analysis.get('health_score', 0)
        if health_score < 50:
            recommendations.append('🚨 النظام يحتاج تدخل فوري!')
        elif health_score < 75:
            recommendations.append('⚠️ النظام يحتاج تحسينات')
        else:
            recommendations.append('✅ النظام في حالة جيدة')
        
        return recommendations or ['لا توجد توصيات محددة']
    
    def ask_gemini(self, question: str) -> str:
        """سؤال مباشر لـ Gemini"""
        if not self.gemini_available:
            return "❌ Gemini CLI غير متوفر"
        
        try:
            prompt = f"""
أنت خبير في تحسين أداء VS Code والأنظمة. أجب على السؤال التالي:

السؤال: {question}

قدم إجابة مفصلة ومفيدة باللغة العربية.
"""
            response = self._call_gemini(prompt)
            return response.strip()
            
        except Exception as e:
            return f"❌ خطأ في السؤال: {e}"
