# 🔧 دليل إصلاح مشاكل MCP Server

## ✅ تم إصلاح المشاكل

### 1. مشكلة Node.js غير موجود
**الحل**: تم تثبيت Node.js v24.4.1 بنجاح
```bash
node --version  # v24.4.1
npm --version   # 11.1.0
```

### 2. مشكلة MCP Server غير موجود
**الحل**: تم إنشاء MCP server كامل مع:
- ✅ `package.json` - ملف التكوين الأساسي
- ✅ `src/index.js` - خادم MCP كامل
- ✅ `node_modules/` - التبعيات المثبتة
- ✅ `start-mcp-server.bat` - سكريبت تشغيل سهل

## 🚀 كيفية تشغيل MCP Server

### الطريقة 1: npm
```bash
npm start
```

### الطريقة 2: Node.js مباشرة
```bash
node src/index.js
```

### الطريقة 3: Windows Batch File
```bash
start-mcp-server.bat
```

## 🔧 تكوين VS Code

### 1. إعدادات VS Code
أضف هذا التكوين إلى إعدادات VS Code:

```json
{
  "mcp": {
    "servers": {
      "anubis": {
        "command": "node",
        "args": ["src/index.js"],
        "cwd": "C:\\Users\\<USER>\\Universal-AI-Assistants"
      }
    }
  }
}
```

### 2. ملف التكوين الجاهز
استخدم الملف: `vscode-mcp-settings.json`

## 🛠️ الأدوات المتاحة في MCP Server

### 1. anubis_status
```json
{
  "name": "anubis_status",
  "description": "Get Anubis system status"
}
```

### 2. anubis_info
```json
{
  "name": "anubis_info",
  "description": "Get Anubis system information",
  "parameters": {
    "component": "database|agents|api|all"
  }
}
```

## 🐛 استكشاف الأخطاء الشائعة

### خطأ: "node is not recognized"
**السبب**: Node.js غير مثبت أو غير موجود في PATH
**الحل**: 
```bash
# تحقق من التثبيت
node --version

# إذا لم يعمل، أعد تشغيل Terminal
# أو استخدم المسار الكامل
"C:\Program Files\nodejs\node.exe" --version
```

### خطأ: "Cannot find module '@modelcontextprotocol/sdk'"
**السبب**: التبعيات غير مثبتة
**الحل**:
```bash
npm install
```

### خطأ: "ENOENT: no such file or directory"
**السبب**: تشغيل الأمر من مجلد خاطئ
**الحل**: تأكد من أنك في مجلد المشروع الصحيح
```bash
cd C:\Users\<USER>\Universal-AI-Assistants
```

### خطأ: "Port already in use"
**السبب**: خادم آخر يعمل على نفس المنفذ
**الحل**: أوقف العمليات الأخرى أو غير المنفذ

## 📋 فحص سريع للنظام

### 1. تحقق من Node.js
```bash
node --version
npm --version
```

### 2. تحقق من الملفات
```bash
# يجب أن تكون موجودة
ls package.json
ls src/index.js
ls node_modules/
```

### 3. تحقق من التبعيات
```bash
npm list
```

### 4. اختبار MCP Server
```bash
npm start
# يجب أن ترى: "Anubis MCP Server running on stdio"
```

## 🔄 إعادة التثبيت الكامل

إذا استمرت المشاكل:

```bash
# 1. احذف node_modules
rm -rf node_modules

# 2. احذف package-lock.json
rm package-lock.json

# 3. أعد تثبيت التبعيات
npm install

# 4. اختبر الخادم
npm start
```

## 📞 الحصول على المساعدة

### ملفات مفيدة:
- `MCP_SERVER_README.md` - دليل شامل
- `package.json` - تكوين المشروع
- `fix-nodejs-mcp.ps1` - سكريبت إصلاح تلقائي

### أوامر مفيدة:
```bash
# معلومات النظام
node --version
npm --version
npm list

# تشغيل الخادم
npm start
node src/index.js

# إصلاح المشاكل
npm install
npm audit fix
```

## ✅ حالة النظام الحالية

- ✅ Node.js v24.4.1 مثبت ويعمل
- ✅ npm 11.1.0 مثبت ويعمل  
- ✅ MCP Server تم إنشاؤه ويعمل
- ✅ التبعيات مثبتة بنجاح
- ✅ ملفات التكوين جاهزة
- ✅ سكريبتات التشغيل جاهزة

**النظام جاهز للاستخدام! 🎉**
