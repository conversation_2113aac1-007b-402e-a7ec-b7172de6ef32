#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فاحص المشروع الشامل لأنوبيس
Anubis Comprehensive Project Scanner

فحص شامل لجميع مكونات المشروع مع:
- فحص جميع المجلدات والأدوات
- تحليل الهيكل والتبعيات
- إنشاء خطة عزل مفصلة
- استخدام الوكلاء الذكيين

مطور بالتعاون مع الوكلاء الذكيين
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class AnubisProjectScanner:
    """فاحص المشروع الشامل لأنوبيس"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.scan_timestamp = datetime.now().isoformat()
        
        # نتائج الفحص الشامل
        self.scan_results = {
            "scan_info": {
                "timestamp": self.scan_timestamp,
                "project_root": str(self.project_root),
                "scanner_version": "3.0",
                "scan_type": "comprehensive_project_scan"
            },
            "project_structure": {},
            "tools_discovered": {},
            "components_analysis": {},
            "isolation_recommendations": {},
            "containerization_plan": {}
        }
        
    def run_comprehensive_scan(self):
        """تشغيل الفحص الشامل للمشروع"""
        print("🔍 بدء الفحص الشامل للمشروع")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
        print("=" * 70)
        
        # فحص هيكل المشروع
        self.analyze_project_structure()
        
        # اكتشاف الأدوات والمكونات
        self.discover_tools_and_components()
        
        # تحليل المكونات بالتفصيل
        self.analyze_components_in_detail()
        
        # إنشاء خطة العزل
        self.create_isolation_plan()
        
        # إنشاء خطة الحاويات
        self.create_containerization_plan()
        
        print("\n🎉 تم إكمال الفحص الشامل بنجاح!")
        return self.scan_results
    
    def analyze_project_structure(self):
        """تحليل هيكل المشروع"""
        print("\n📁 تحليل هيكل المشروع...")
        
        structure = {}
        
        # فحص المجلدات الرئيسية
        for item in self.project_root.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                structure[item.name] = self.analyze_directory(item)
        
        self.scan_results["project_structure"] = structure
        
        total_dirs = len(structure)
        total_files = sum(dir_info.get("file_count", 0) for dir_info in structure.values())
        
        print(f"  ✅ تم تحليل {total_dirs} مجلد رئيسي")
        print(f"  📄 إجمالي الملفات: {total_files}")
    
    def analyze_directory(self, directory: Path) -> Dict[str, Any]:
        """تحليل مجلد محدد"""
        analysis = {
            "path": str(directory),
            "type": self.determine_directory_type(directory),
            "file_count": 0,
            "subdirectories": [],
            "main_files": [],
            "technologies": [],
            "isolation_priority": "medium"
        }
        
        try:
            # عد الملفات
            files = list(directory.rglob("*"))
            analysis["file_count"] = len([f for f in files if f.is_file()])
            
            # المجلدات الفرعية
            subdirs = [d.name for d in directory.iterdir() if d.is_dir() and not d.name.startswith('.')]
            analysis["subdirectories"] = subdirs[:10]  # أول 10 مجلدات
            
            # الملفات الرئيسية
            main_files = [f.name for f in directory.iterdir() if f.is_file() and not f.name.startswith('.')]
            analysis["main_files"] = main_files[:15]  # أول 15 ملف
            
            # تحديد التقنيات
            analysis["technologies"] = self.detect_technologies(directory)
            
            # تحديد أولوية العزل
            analysis["isolation_priority"] = self.calculate_isolation_priority(directory, analysis)
            
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def determine_directory_type(self, directory: Path) -> str:
        """تحديد نوع المجلد"""
        dir_name = directory.name.lower()
        
        # أنواع المجلدات المعروفة
        if dir_name in ["anubis", "universal-ai-assistants"]:
            return "main_system"
        elif dir_name in ["tools", "utilities", "scripts"]:
            return "tools"
        elif dir_name in ["tests", "test", "testing"]:
            return "testing"
        elif dir_name in ["docs", "documentation", "readme"]:
            return "documentation"
        elif dir_name in ["api", "server", "backend"]:
            return "api_service"
        elif dir_name in ["frontend", "ui", "web"]:
            return "frontend"
        elif dir_name in ["database", "db", "data"]:
            return "database"
        elif dir_name in ["config", "configs", "settings"]:
            return "configuration"
        elif dir_name.endswith("-optimizer") or "optimizer" in dir_name:
            return "optimizer_tool"
        elif "vscode" in dir_name:
            return "vscode_tool"
        elif dir_name in ["n8n", "workflows"]:
            return "workflow_tool"
        else:
            return "unknown"
    
    def detect_technologies(self, directory: Path) -> List[str]:
        """اكتشاف التقنيات المستخدمة في المجلد"""
        technologies = []
        
        try:
            files = list(directory.rglob("*"))
            
            # فحص أنواع الملفات
            extensions = set()
            for file in files:
                if file.is_file():
                    extensions.add(file.suffix.lower())
            
            # تحديد التقنيات بناءً على الامتدادات
            if ".py" in extensions:
                technologies.append("Python")
            if any(ext in extensions for ext in [".js", ".jsx"]):
                technologies.append("JavaScript")
            if any(ext in extensions for ext in [".ts", ".tsx"]):
                technologies.append("TypeScript")
            if ".java" in extensions:
                technologies.append("Java")
            if any(ext in extensions for ext in [".cpp", ".c", ".h"]):
                technologies.append("C/C++")
            if any(ext in extensions for ext in [".html", ".css"]):
                technologies.append("Web")
            if ".sql" in extensions:
                technologies.append("SQL")
            if ".json" in extensions:
                technologies.append("JSON")
            if ".yml" in extensions or ".yaml" in extensions:
                technologies.append("YAML")
            if ".md" in extensions:
                technologies.append("Markdown")
            
            # فحص ملفات التكوين الخاصة
            config_files = [f.name.lower() for f in files if f.is_file()]
            
            if "package.json" in config_files:
                technologies.append("Node.js")
            if "requirements.txt" in config_files or "setup.py" in config_files:
                technologies.append("Python Package")
            if "dockerfile" in config_files or "docker-compose.yml" in config_files:
                technologies.append("Docker")
            if "pom.xml" in config_files:
                technologies.append("Maven")
            if "build.gradle" in config_files:
                technologies.append("Gradle")
            
        except Exception:
            pass
        
        return technologies
    
    def calculate_isolation_priority(self, directory: Path, analysis: Dict) -> str:
        """حساب أولوية العزل للمجلد"""
        dir_type = analysis.get("type", "unknown")
        file_count = analysis.get("file_count", 0)
        technologies = analysis.get("technologies", [])
        
        # أولوية حرجة
        if dir_type in ["main_system", "api_service", "database"]:
            return "critical"
        
        # أولوية عالية
        if dir_type in ["tools", "optimizer_tool"] or file_count > 100:
            return "high"
        
        # أولوية متوسطة
        if dir_type in ["frontend", "workflow_tool"] or len(technologies) > 3:
            return "medium"
        
        # أولوية منخفضة
        return "low"
    
    def discover_tools_and_components(self):
        """اكتشاف الأدوات والمكونات"""
        print("\n🔧 اكتشاف الأدوات والمكونات...")
        
        tools_discovered = {}
        
        # فحص المجلدات المكتشفة
        for dir_name, dir_info in self.scan_results["project_structure"].items():
            if dir_info.get("type") in ["tools", "optimizer_tool", "vscode_tool", "workflow_tool", "main_system"]:
                tools_discovered[dir_name] = {
                    "type": dir_info.get("type"),
                    "path": dir_info.get("path"),
                    "technologies": dir_info.get("technologies", []),
                    "file_count": dir_info.get("file_count", 0),
                    "isolation_priority": dir_info.get("isolation_priority"),
                    "container_candidate": True,
                    "dependencies": self.analyze_dependencies(Path(dir_info.get("path", "")))
                }
        
        self.scan_results["tools_discovered"] = tools_discovered
        
        print(f"  ✅ تم اكتشاف {len(tools_discovered)} أداة/مكون")
        
        for tool_name, tool_info in tools_discovered.items():
            priority = tool_info.get("isolation_priority", "medium")
            tech_count = len(tool_info.get("technologies", []))
            print(f"    🔹 {tool_name}: {priority} priority, {tech_count} technologies")
    
    def analyze_dependencies(self, directory: Path) -> List[str]:
        """تحليل التبعيات للمجلد"""
        dependencies = []
        
        try:
            # فحص ملفات التبعيات
            dep_files = {
                "requirements.txt": "Python",
                "package.json": "Node.js",
                "pom.xml": "Java",
                "Cargo.toml": "Rust",
                "go.mod": "Go"
            }
            
            for dep_file, tech in dep_files.items():
                dep_path = directory / dep_file
                if dep_path.exists():
                    dependencies.append(f"{tech} dependencies")
            
            # فحص استيرادات Python
            if (directory / "requirements.txt").exists() or any(directory.rglob("*.py")):
                py_files = list(directory.rglob("*.py"))[:5]  # أول 5 ملفات
                for py_file in py_files:
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if "import " in content:
                                dependencies.append("Python imports")
                                break
                    except:
                        pass
            
        except Exception:
            pass
        
        return dependencies
    
    def analyze_components_in_detail(self):
        """تحليل المكونات بالتفصيل"""
        print("\n🔍 تحليل المكونات بالتفصيل...")
        
        components_analysis = {}
        
        for tool_name, tool_info in self.scan_results["tools_discovered"].items():
            analysis = {
                "detailed_structure": self.get_detailed_structure(Path(tool_info["path"])),
                "entry_points": self.find_entry_points(Path(tool_info["path"])),
                "configuration_files": self.find_configuration_files(Path(tool_info["path"])),
                "isolation_requirements": self.determine_isolation_requirements(tool_info),
                "container_specs": self.generate_container_specs(tool_info)
            }
            
            components_analysis[tool_name] = analysis
        
        self.scan_results["components_analysis"] = components_analysis
        
        print(f"  ✅ تم تحليل {len(components_analysis)} مكون بالتفصيل")
    
    def get_detailed_structure(self, directory: Path) -> Dict[str, Any]:
        """الحصول على هيكل مفصل للمجلد"""
        structure = {
            "subdirectories": [],
            "key_files": [],
            "total_size_mb": 0
        }
        
        try:
            # المجلدات الفرعية
            subdirs = [d.name for d in directory.iterdir() if d.is_dir() and not d.name.startswith('.')]
            structure["subdirectories"] = subdirs
            
            # الملفات المهمة
            key_extensions = ['.py', '.js', '.ts', '.json', '.yml', '.yaml', '.md', '.txt']
            key_files = []
            total_size = 0
            
            for file in directory.rglob("*"):
                if file.is_file():
                    total_size += file.stat().st_size
                    if file.suffix.lower() in key_extensions:
                        key_files.append(str(file.relative_to(directory)))
            
            structure["key_files"] = key_files[:20]  # أول 20 ملف مهم
            structure["total_size_mb"] = round(total_size / (1024 * 1024), 2)
            
        except Exception:
            pass
        
        return structure
    
    def find_entry_points(self, directory: Path) -> List[str]:
        """العثور على نقاط الدخول للمكون"""
        entry_points = []
        
        # ملفات الدخول الشائعة
        common_entries = [
            "main.py", "app.py", "server.py", "index.py",
            "main.js", "app.js", "server.js", "index.js",
            "main.ts", "app.ts", "server.ts", "index.ts"
        ]
        
        for entry in common_entries:
            if (directory / entry).exists():
                entry_points.append(entry)
        
        return entry_points
    
    def find_configuration_files(self, directory: Path) -> List[str]:
        """العثور على ملفات التكوين"""
        config_files = []
        
        # ملفات التكوين الشائعة
        common_configs = [
            "config.json", "config.yml", "config.yaml",
            "settings.json", "settings.yml", "settings.yaml",
            ".env", "environment.yml", "docker-compose.yml",
            "Dockerfile", "requirements.txt", "package.json"
        ]
        
        for config in common_configs:
            if (directory / config).exists():
                config_files.append(config)
        
        return config_files
    
    def determine_isolation_requirements(self, tool_info: Dict) -> Dict[str, Any]:
        """تحديد متطلبات العزل"""
        requirements = {
            "network_isolation": False,
            "file_system_isolation": True,
            "resource_limits": {},
            "security_level": "medium"
        }
        
        tool_type = tool_info.get("type", "unknown")
        technologies = tool_info.get("technologies", [])
        
        # تحديد متطلبات الشبكة
        if tool_type in ["api_service", "main_system"] or "Node.js" in technologies:
            requirements["network_isolation"] = True
        
        # تحديد حدود الموارد
        file_count = tool_info.get("file_count", 0)
        if file_count > 500:
            requirements["resource_limits"] = {"memory": "1G", "cpu": "1.0"}
        elif file_count > 100:
            requirements["resource_limits"] = {"memory": "512M", "cpu": "0.5"}
        else:
            requirements["resource_limits"] = {"memory": "256M", "cpu": "0.2"}
        
        # تحديد مستوى الأمان
        if tool_type in ["main_system", "database"]:
            requirements["security_level"] = "high"
        elif tool_type in ["api_service", "tools"]:
            requirements["security_level"] = "medium"
        else:
            requirements["security_level"] = "low"
        
        return requirements
    
    def generate_container_specs(self, tool_info: Dict) -> Dict[str, Any]:
        """إنشاء مواصفات الحاوية"""
        technologies = tool_info.get("technologies", [])
        
        # تحديد الصورة الأساسية
        if "Python" in technologies:
            base_image = "python:3.11-slim"
        elif "Node.js" in technologies:
            base_image = "node:18-alpine"
        elif "Java" in technologies:
            base_image = "openjdk:17-jre-slim"
        else:
            base_image = "alpine:latest"
        
        specs = {
            "base_image": base_image,
            "working_dir": "/app",
            "ports": [],
            "volumes": ["/app/data", "/app/logs"],
            "environment": ["NODE_ENV=production"] if "Node.js" in technologies else [],
            "build_commands": self.generate_build_commands(technologies),
            "run_command": self.generate_run_command(tool_info)
        }
        
        return specs
    
    def generate_build_commands(self, technologies: List[str]) -> List[str]:
        """إنشاء أوامر البناء"""
        commands = []
        
        if "Python" in technologies:
            commands.extend([
                "COPY requirements.txt .",
                "RUN pip install --no-cache-dir -r requirements.txt"
            ])
        
        if "Node.js" in technologies:
            commands.extend([
                "COPY package*.json .",
                "RUN npm ci --only=production"
            ])
        
        commands.append("COPY . .")
        
        return commands
    
    def generate_run_command(self, tool_info: Dict) -> str:
        """إنشاء أمر التشغيل"""
        technologies = tool_info.get("technologies", [])
        
        if "Python" in technologies:
            return "python main.py"
        elif "Node.js" in technologies:
            return "npm start"
        else:
            return "sh"
    
    def create_isolation_plan(self):
        """إنشاء خطة العزل"""
        print("\n🔧 إنشاء خطة العزل...")
        
        isolation_plan = {
            "critical_components": [],
            "high_priority_components": [],
            "medium_priority_components": [],
            "low_priority_components": [],
            "isolation_strategy": "docker_containers",
            "network_design": "bridge_network",
            "data_isolation": "separate_volumes"
        }
        
        # تصنيف المكونات حسب الأولوية
        for tool_name, tool_info in self.scan_results["tools_discovered"].items():
            priority = tool_info.get("isolation_priority", "medium")
            
            component_info = {
                "name": tool_name,
                "type": tool_info.get("type"),
                "path": tool_info.get("path"),
                "technologies": tool_info.get("technologies", []),
                "estimated_isolation_time": self.estimate_isolation_time(tool_info)
            }
            
            if priority == "critical":
                isolation_plan["critical_components"].append(component_info)
            elif priority == "high":
                isolation_plan["high_priority_components"].append(component_info)
            elif priority == "medium":
                isolation_plan["medium_priority_components"].append(component_info)
            else:
                isolation_plan["low_priority_components"].append(component_info)
        
        self.scan_results["isolation_recommendations"] = isolation_plan
        
        # إحصائيات
        total_components = sum(len(components) for key, components in isolation_plan.items() 
                             if key.endswith("_components"))
        
        print(f"  ✅ تم إنشاء خطة عزل لـ {total_components} مكون")
        print(f"    🔴 حرج: {len(isolation_plan['critical_components'])}")
        print(f"    🟠 عالي: {len(isolation_plan['high_priority_components'])}")
        print(f"    🟡 متوسط: {len(isolation_plan['medium_priority_components'])}")
        print(f"    🟢 منخفض: {len(isolation_plan['low_priority_components'])}")
    
    def estimate_isolation_time(self, tool_info: Dict) -> str:
        """تقدير وقت العزل"""
        file_count = tool_info.get("file_count", 0)
        tech_count = len(tool_info.get("technologies", []))
        
        if file_count > 500 or tech_count > 5:
            return "4-6 hours"
        elif file_count > 100 or tech_count > 3:
            return "2-4 hours"
        elif file_count > 50:
            return "1-2 hours"
        else:
            return "30-60 minutes"
    
    def create_containerization_plan(self):
        """إنشاء خطة الحاويات"""
        print("\n🐳 إنشاء خطة الحاويات...")
        
        containerization_plan = {
            "total_containers": 0,
            "containers": {},
            "docker_compose_structure": {},
            "network_configuration": {
                "network_name": "anubis_isolation_network",
                "subnet": "172.30.0.0/16",
                "driver": "bridge"
            },
            "volume_configuration": {
                "shared_volumes": ["logs", "data", "configs"],
                "persistent_volumes": ["database_data", "user_data"]
            }
        }
        
        # إنشاء حاوية لكل مكون
        for tool_name, analysis in self.scan_results["components_analysis"].items():
            container_config = {
                "container_name": f"{tool_name.lower().replace('-', '_')}_container",
                "build_context": f"./containers/{tool_name}",
                "dockerfile": f"./containers/{tool_name}/Dockerfile",
                "ports": self.determine_ports(tool_name, analysis),
                "volumes": self.determine_volumes(tool_name, analysis),
                "environment": self.determine_environment(tool_name, analysis),
                "depends_on": self.determine_dependencies(tool_name),
                "restart_policy": "unless-stopped",
                "resource_limits": analysis.get("isolation_requirements", {}).get("resource_limits", {})
            }
            
            containerization_plan["containers"][tool_name] = container_config
        
        containerization_plan["total_containers"] = len(containerization_plan["containers"])
        
        self.scan_results["containerization_plan"] = containerization_plan
        
        print(f"  ✅ تم إنشاء خطة حاويات لـ {containerization_plan['total_containers']} مكون")
    
    def determine_ports(self, tool_name: str, analysis: Dict) -> List[str]:
        """تحديد المنافذ للحاوية"""
        # منافذ افتراضية بناءً على نوع الأداة
        port_mapping = {
            "anubis": ["8000:8000"],
            "universal-ai-assistants": ["8001:8001"],
            "vscode": ["3000:3000"],
            "n8n": ["5678:5678"]
        }
        
        return port_mapping.get(tool_name.lower(), [])
    
    def determine_volumes(self, tool_name: str, analysis: Dict) -> List[str]:
        """تحديد الأحجام للحاوية"""
        volumes = [
            f"./volumes/{tool_name}/data:/app/data",
            f"./volumes/{tool_name}/logs:/app/logs"
        ]
        
        # إضافة أحجام خاصة بناءً على نوع الأداة
        if "database" in tool_name.lower():
            volumes.append(f"./volumes/{tool_name}/db:/var/lib/mysql")
        
        return volumes
    
    def determine_environment(self, tool_name: str, analysis: Dict) -> List[str]:
        """تحديد متغيرات البيئة"""
        env_vars = [
            "PYTHONPATH=/app",
            f"COMPONENT_NAME={tool_name}",
            "LOG_LEVEL=INFO"
        ]
        
        return env_vars
    
    def determine_dependencies(self, tool_name: str) -> List[str]:
        """تحديد التبعيات للحاوية"""
        # تبعيات افتراضية
        if tool_name.lower() in ["anubis", "universal-ai-assistants"]:
            return ["database"]
        
        return []
    
    def save_scan_results(self, filename: Optional[str] = None) -> str:
        """حفظ نتائج الفحص"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_comprehensive_scan_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الفحص الشامل في: {filename}")
        return filename
    
    def print_summary_report(self):
        """طباعة ملخص التقرير"""
        print("\n" + "="*70)
        print("🔍 تقرير الفحص الشامل للمشروع")
        print("🏺 نظام أنوبيس للذكاء الاصطناعي")
        print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
        print("="*70)
        
        # معلومات المشروع
        structure = self.scan_results["project_structure"]
        tools = self.scan_results["tools_discovered"]
        isolation_plan = self.scan_results["isolation_recommendations"]
        containers = self.scan_results["containerization_plan"]
        
        print(f"📁 المجلدات الرئيسية: {len(structure)}")
        print(f"🔧 الأدوات المكتشفة: {len(tools)}")
        print(f"🐳 الحاويات المخططة: {containers.get('total_containers', 0)}")
        
        # خطة العزل
        print(f"\n🔧 خطة العزل:")
        print(f"  🔴 مكونات حرجة: {len(isolation_plan.get('critical_components', []))}")
        print(f"  🟠 أولوية عالية: {len(isolation_plan.get('high_priority_components', []))}")
        print(f"  🟡 أولوية متوسطة: {len(isolation_plan.get('medium_priority_components', []))}")
        print(f"  🟢 أولوية منخفضة: {len(isolation_plan.get('low_priority_components', []))}")
        
        # أهم الأدوات
        print(f"\n🔧 أهم الأدوات المكتشفة:")
        for tool_name, tool_info in list(tools.items())[:5]:
            priority = tool_info.get("isolation_priority", "medium")
            tech_count = len(tool_info.get("technologies", []))
            print(f"  🔹 {tool_name}: {priority} priority, {tech_count} technologies")
        
        print("\n🎉 الفحص الشامل مكتمل - جاهز لبدء العزل!")
        print("="*70)


def main():
    """الدالة الرئيسية"""
    print("🔍 فاحص المشروع الشامل لأنوبيس")
    print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
    
    # إنشاء الفاحص
    scanner = AnubisProjectScanner()
    
    # تشغيل الفحص الشامل
    scanner.run_comprehensive_scan()
    
    # طباعة الملخص
    scanner.print_summary_report()
    
    # حفظ النتائج
    report_file = scanner.save_scan_results()
    
    print(f"\n📊 تقرير مفصل متاح في: {report_file}")


if __name__ == "__main__":
    main()
