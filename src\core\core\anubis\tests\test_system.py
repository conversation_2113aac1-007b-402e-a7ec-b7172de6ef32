#!/usr/bin/env python3
"""
🧪 اختبار نظام المساعدين الذكيين العالمي
Test Universal AI Assistants System

اختبار سريع للتأكد من عمل النظام مع مشروع Crestal Diamond
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "core"))

try:
    from anubis.core.assistant_system import UniversalAssistantSystem
    from anubis.core.config_manager import ConfigManager
    from anubis.core.logger import SystemLogger

    print("✅ تم تحميل النواة الأساسية بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل النواة الأساسية: {e}")
    sys.exit(1)


def test_config_manager():
    """اختبار مدير التكوين"""
    print("\n🔧 اختبار مدير التكوين...")

    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()

        print(f"✅ تم تحميل التكوين: {len(config)} أقسام")
        print(f"📊 الوكلاء المتاحين: {len(config.get('agents', {}))}")

        # عرض ملخص التكوين
        summary = config_manager.get_config_summary()
        print(f"📋 ملخص التكوين:")
        for key, value in summary.items():
            print(f"   {key}: {value}")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار مدير التكوين: {e}")
        return False


def test_logger():
    """اختبار نظام السجلات"""
    print("\n📝 اختبار نظام السجلات...")

    try:
        logger = SystemLogger("TestLogger")

        logger.info("اختبار رسالة معلومات")
        logger.warning("اختبار رسالة تحذير")
        logger.log_agent_action("TestAgent", "اختبار عمل الوكيل", "تفاصيل الاختبار")

        # فحص إحصائيات السجل
        stats = logger.get_log_stats()
        print(f"✅ تم إنشاء ملف السجل: {stats['file_exists']}")
        print(f"📊 حجم ملف السجل: {stats['file_size_mb']} MB")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار نظام السجلات: {e}")
        return False


def test_agents_loading():
    """اختبار تحميل الوكلاء"""
    print("\n🤖 اختبار تحميل الوكلاء...")

    try:
        # فحص ملفات الوكلاء
        agents_dir = Path("../agents")
        if not agents_dir.exists():
            print("❌ مجلد الوكلاء غير موجود")
            return False

        agent_files = list(agents_dir.glob("*_agent.py"))
        print(f"📁 تم العثور على {len(agent_files)} ملف وكيل:")

        for agent_file in agent_files:
            print(f"   - {agent_file.name}")

        # محاولة استيراد الوكلاء
        sys.path.append(str(agents_dir))

        try:
            from database_agent import DatabaseAgent

            print("✅ تم تحميل DatabaseAgent")
        except ImportError as e:
            print(f"⚠️ لا يمكن تحميل DatabaseAgent: {e}")

        try:
            from file_organizer_agent import FileOrganizerAgent

            print("✅ تم تحميل FileOrganizerAgent")
        except ImportError as e:
            print(f"⚠️ لا يمكن تحميل FileOrganizerAgent: {e}")

        try:
            from memory_agent import MemoryAgent

            print("✅ تم تحميل MemoryAgent")
        except ImportError as e:
            print(f"⚠️ لا يمكن تحميل MemoryAgent: {e}")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل الوكلاء: {e}")
        return False


def test_with_crestal_diamond():
    """اختبار النظام مع مشروع Crestal Diamond"""
    print("\n💎 اختبار النظام مع مشروع Crestal Diamond...")

    # مسار مشروع Crestal Diamond
    crestal_path = Path("../Crestal Diamond")

    if not crestal_path.exists():
        print("⚠️ مشروع Crestal Diamond غير موجود في المسار المتوقع")
        print("🔍 البحث عن مشاريع أخرى للاختبار...")

        # البحث عن مشاريع Python أخرى
        current_dir = Path("..")
        python_projects = []

        for item in current_dir.iterdir():
            if item.is_dir() and list(item.glob("*.py")):
                python_projects.append(item)

        if python_projects:
            crestal_path = python_projects[0]
            print(f"📁 تم العثور على مشروع للاختبار: {crestal_path.name}")
        else:
            print("❌ لا توجد مشاريع Python للاختبار")
            return False

    try:
        # تحميل التكوين
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # إنشاء النظام
        system = UniversalAssistantSystem(
            project_path=str(crestal_path), config=config, verbose=True
        )

        print(f"✅ تم تحميل المشروع: {crestal_path.name}")
        print(f"📊 نوع المشروع: {system.project_type}")
        print(f"🤖 الوكلاء النشطين: {len(system.active_agents)}")

        # عرض حالة النظام
        status = system.get_system_status()
        print(f"📋 حالة النظام:")
        for key, value in status.items():
            if key not in ["available_agents", "active_agents"]:  # تجنب القوائم الطويلة
                print(f"   {key}: {value}")

        # اختبار فحص الصحة
        print("\n🏥 اختبار فحص الصحة...")
        health = system.health_check()
        print(f"✅ حالة المشروع: {health.get('project_health', 'unknown')}")
        print(f"📊 عدد المشاكل: {len(health.get('issues', []))}")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار النظام مع Crestal Diamond: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_workspace_creation():
    """اختبار إنشاء مساحة العمل"""
    print("\n📊 اختبار إنشاء مساحة العمل...")

    try:
        workspace_dir = Path("../workspace")

        # فحص المجلدات المطلوبة
        required_dirs = [
            "logs",
            "reports",
            "backups",
            "shared_memory",
            "collaboration_logs",
        ]

        for dir_name in required_dirs:
            dir_path = workspace_dir / dir_name
            if dir_path.exists():
                print(f"✅ مجلد موجود: {dir_name}")
            else:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"🆕 تم إنشاء مجلد: {dir_name}")

        # إنشاء ملف اختبار
        test_file = workspace_dir / "test_file.txt"
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(f"ملف اختبار تم إنشاؤه في {datetime.now()}")

        print(f"✅ تم إنشاء ملف اختبار: {test_file}")

        # حذف ملف الاختبار
        test_file.unlink()
        print("🗑️ تم حذف ملف الاختبار")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء مساحة العمل: {e}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 ═══════════════════════════════════════════════════════════════")
    print("   اختبار نظام المساعدين الذكيين العالمي")
    print("   Universal AI Assistants System Test")
    print("═══════════════════════════════════════════════════════════════ 🧪")

    tests = [
        ("مدير التكوين", test_config_manager),
        ("نظام السجلات", test_logger),
        ("تحميل الوكلاء", test_agents_loading),
        ("إنشاء مساحة العمل", test_workspace_creation),
        ("النظام مع Crestal Diamond", test_with_crestal_diamond),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 اختبار: {test_name}")
        print(f"{'='*60}")

        try:
            result = test_func()
            results[test_name] = result

            if result:
                print(f"✅ نجح اختبار: {test_name}")
            else:
                print(f"❌ فشل اختبار: {test_name}")

        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
            results[test_name] = False

    # ملخص النتائج
    print(f"\n{'='*60}")
    print("📊 ملخص نتائج الاختبارات")
    print(f"{'='*60}")

    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")

    print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")

    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
