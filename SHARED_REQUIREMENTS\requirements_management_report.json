{"timestamp": "2025-07-23T02:57:26.877542", "shared_files": {"master": {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\SHARED_REQUIREMENTS\\requirements_master.txt", "package_count": 82, "packages": ["aiofiles", "aiosqlite", "annotated-types", "anthropic", "anyio", "bcrypt", "black", "certifi", "chardet", "charset-normalizer", "chromadb", "click", "colorama", "colorlog", "cryptography", "docker", "faiss-cpu", "<PERSON><PERSON><PERSON>", "flake8", "google-generativeai", "greenlet", "h11", "httpcore", "httpx", "idna", "iniconfig", "jinja2", "jup<PERSON><PERSON>", "jupyterlab", "langchain", "markupsafe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mysql-connector-python", "numpy", "openai", "packaging", "pandas", "passlib", "passlib[bcrypt]", "pathlib2", "pathspec", "pip", "plotly", "pluggy", "prometheus-client", "psutil", "psycopg2-binary", "pydantic", "pydantic_core", "pygments", "pylint", "pytest", "pytest-cov", "python-dateutil", "python-dotenv", "python-jose", "python-jose[cryptography]", "python-multipart", "pywin32", "pyyaml", "redis", "requests", "rich", "scikit-learn", "scipy", "seaborn", "sentence-transformers", "sniffio", "sqlalchemy", "starlette", "streamlit", "tabulate", "tiktoken", "torch", "tqdm", "transformers", "typing-extensions", "typing-inspection", "typing_extensions", "urllib3", "u<PERSON><PERSON>", "uvicorn[standard]"]}, "core": {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\SHARED_REQUIREMENTS\\requirements_core.txt", "package_count": 8, "packages": ["certifi", "charset-normalizer", "mysql-connector-python", "packaging", "pip", "python-dotenv", "python-multipart", "urllib3"]}, "web": {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\SHARED_REQUIREMENTS\\requirements_web_development.txt", "package_count": 3, "packages": ["<PERSON><PERSON><PERSON>", "httpx", "requests"]}, "dev": {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\SHARED_REQUIREMENTS\\requirements_development_tools.txt", "package_count": 1, "packages": ["pytest"]}, "system": {"file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\SHARED_REQUIREMENTS\\requirements_system_tools.txt", "package_count": 1, "packages": ["psutil"]}}, "projects": {"ANUBIS_SYSTEM": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM", "requirements_files": 7, "files": ["requirements.txt", "requirements_core.txt", "requirements_dev.txt", "requirements_master.txt", "requirements_minimal.txt", "requirements_system.txt", "requirements_web.txt"]}, "HORUS_AI_TEAM": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM", "requirements_files": 5, "files": ["requirements_core.txt", "requirements_dev.txt", "requirements_master.txt", "requirements_system.txt", "requirements_web.txt"]}, "PROJECT_DOCUMENTATION": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\PROJECT_DOCUMENTATION", "requirements_files": 5, "files": ["requirements_core.txt", "requirements_dev.txt", "requirements_master.txt", "requirements_system.txt", "requirements_web.txt"]}}, "statistics": {"total_shared_files": 5, "total_shared_packages": 95, "total_projects": 3}}