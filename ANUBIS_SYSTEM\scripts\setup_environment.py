#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 سكريبت إعداد البيئة لنظام أنوبيس
Anubis System Environment Setup Script

سكريبت شامل لإعداد البيئة وتهيئة النظام
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

class AnubisEnvironmentSetup:
    """فئة إعداد البيئة لنظام أنوبيس"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / ".venv"
        self.env_file = self.project_root / ".env"
        self.env_example = self.project_root / ".env.example"
        
    def print_header(self):
        """طباعة رأس السكريبت"""
        print("🏺" + "=" * 60)
        print("    نظام أنوبيس - إعداد البيئة الشامل")
        print("    Anubis System - Comprehensive Environment Setup")
        print("=" * 60)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 مجلد المشروع: {self.project_root}")
        print("=" * 60)
    
    def check_python_version(self):
        """فحص إصدار Python"""
        print("\n🐍 فحص إصدار Python...")
        
        if sys.version_info < (3, 8):
            print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
            print(f"   الإصدار الحالي: {sys.version}")
            return False
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    
    def create_virtual_environment(self):
        """إنشاء البيئة الافتراضية"""
        print("\n🔧 إعداد البيئة الافتراضية...")
        
        if self.venv_path.exists():
            print("⚠️ البيئة الافتراضية موجودة بالفعل")
            response = input("هل تريد إعادة إنشائها؟ (y/N): ")
            if response.lower() == 'y':
                print("🗑️ حذف البيئة الافتراضية القديمة...")
                shutil.rmtree(self.venv_path)
            else:
                print("⏭️ تخطي إنشاء البيئة الافتراضية")
                return True
        
        try:
            print("📦 إنشاء البيئة الافتراضية...")
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], 
                         check=True, capture_output=True)
            print("✅ تم إنشاء البيئة الافتراضية بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في إنشاء البيئة الافتراضية: {e}")
            return False
    
    def get_pip_command(self):
        """الحصول على أمر pip للبيئة الافتراضية"""
        if os.name == 'nt':  # Windows
            return str(self.venv_path / "Scripts" / "pip")
        else:  # Linux/Mac
            return str(self.venv_path / "bin" / "pip")
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        print("\n📦 تثبيت المتطلبات...")
        
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            print("❌ ملف requirements.txt غير موجود")
            return False
        
        pip_cmd = self.get_pip_command()
        
        try:
            print("⬇️ تحديث pip...")
            subprocess.run([pip_cmd, "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            print("📥 تثبيت المتطلبات...")
            subprocess.run([pip_cmd, "install", "-r", str(requirements_file)], 
                         check=True)
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def setup_environment_file(self):
        """إعداد ملف متغيرات البيئة"""
        print("\n⚙️ إعداد ملف متغيرات البيئة...")
        
        if self.env_file.exists():
            print("⚠️ ملف .env موجود بالفعل")
            response = input("هل تريد إعادة إنشائه؟ (y/N): ")
            if response.lower() != 'y':
                print("⏭️ تخطي إنشاء ملف .env")
                return True
        
        if not self.env_example.exists():
            print("❌ ملف .env.example غير موجود")
            return False
        
        try:
            shutil.copy2(self.env_example, self.env_file)
            print("✅ تم إنشاء ملف .env من النموذج")
            print("⚠️ تذكر تحديث القيم في ملف .env حسب بيئتك")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف .env: {e}")
            return False
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("\n📁 إنشاء المجلدات المطلوبة...")
        
        directories = [
            "data/database",
            "data/storage",
            "data/backups",
            "logs/system",
            "logs/api",
            "logs/ai",
            "logs/automation",
            "ssl",
            "temp"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
        
        print("✅ تم إنشاء جميع المجلدات المطلوبة")
        return True
    
    def check_docker(self):
        """فحص Docker"""
        print("\n🐳 فحص Docker...")
        
        try:
            result = subprocess.run(["docker", "--version"], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {result.stdout.strip()}")
            
            result = subprocess.run(["docker-compose", "--version"], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ Docker غير مثبت أو غير متاح")
            print("   يمكنك تشغيل النظام بدون Docker")
            return False
    
    def run_initial_tests(self):
        """تشغيل اختبارات أولية"""
        print("\n🧪 تشغيل اختبارات أولية...")
        
        # فحص استيراد المكتبات الأساسية
        try:
            import fastapi
            import uvicorn
            import pydantic
            print("✅ المكتبات الأساسية متاحة")
        except ImportError as e:
            print(f"❌ خطأ في استيراد المكتبات: {e}")
            return False
        
        # فحص ملف الإعدادات
        if self.env_file.exists():
            print("✅ ملف .env متاح")
        else:
            print("⚠️ ملف .env غير موجود")
        
        return True
    
    def print_next_steps(self):
        """طباعة الخطوات التالية"""
        print("\n🎯 الخطوات التالية:")
        print("=" * 60)
        print("1. 📝 قم بتحديث ملف .env بالقيم المناسبة لبيئتك")
        print("2. 🔑 أضف مفاتيح API للذكاء الاصطناعي")
        print("3. 🗄️ قم بإعداد قاعدة البيانات (MySQL اختياري)")
        print("4. 🚀 شغل النظام باستخدام: python main.py")
        print("5. 🌐 افتح المتصفح على: http://localhost:8000")
        print("\n🔧 أوامر مفيدة:")
        print("   - تفعيل البيئة الافتراضية:")
        if os.name == 'nt':
            print("     .venv\\Scripts\\activate")
        else:
            print("     source .venv/bin/activate")
        print("   - تشغيل الاختبارات: python scripts/checkers/quick_test.py")
        print("   - فحص المكتبات: python scripts/checkers/check_installed_packages.py")
        print("=" * 60)
    
    def run_setup(self):
        """تشغيل الإعداد الكامل"""
        self.print_header()
        
        steps = [
            ("فحص إصدار Python", self.check_python_version),
            ("إنشاء البيئة الافتراضية", self.create_virtual_environment),
            ("تثبيت المتطلبات", self.install_requirements),
            ("إعداد ملف البيئة", self.setup_environment_file),
            ("إنشاء المجلدات", self.create_directories),
            ("فحص Docker", self.check_docker),
            ("تشغيل اختبارات أولية", self.run_initial_tests)
        ]
        
        failed_steps = []
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            try:
                if not step_func():
                    failed_steps.append(step_name)
            except Exception as e:
                print(f"❌ خطأ في {step_name}: {e}")
                failed_steps.append(step_name)
        
        print("\n" + "=" * 60)
        if failed_steps:
            print("⚠️ تم الإعداد مع بعض التحذيرات:")
            for step in failed_steps:
                print(f"   - {step}")
        else:
            print("🎉 تم إعداد البيئة بنجاح!")
        
        self.print_next_steps()

def main():
    """الدالة الرئيسية"""
    setup = AnubisEnvironmentSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
