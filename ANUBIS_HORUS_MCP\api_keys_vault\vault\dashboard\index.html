<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 لوحة تحكم حورس - إدارة مفاتيح API</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #27ae60;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .icon-blue { background: linear-gradient(135deg, #3498db, #2980b9); }
        .icon-green { background: linear-gradient(135deg, #27ae60, #229954); }
        .icon-orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .icon-red { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .icon-purple { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
        }
        
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }
        
        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .activity-time {
            font-size: 12px;
            color: #7f8c8d;
            min-width: 50px;
        }
        
        .activity-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success { background: linear-gradient(135deg, #27ae60, #229954); }
        .btn-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        
        .wide-card {
            grid-column: span 2;
        }
        
        @media (max-width: 768px) {
            .wide-card { grid-column: span 1; }
            .metric-grid { grid-template-columns: 1fr; }
            .header-content { flex-direction: column; gap: 15px; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-eye" style="color: #f39c12;"></i>
                <span>لوحة تحكم حورس</span>
                <span style="font-size: 14px; color: #7f8c8d;">إدارة مفاتيح API</span>
            </div>
            <div class="status-indicator pulse">
                <i class="fas fa-circle"></i>
                <span>النظام يعمل بشكل طبيعي</span>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="dashboard-grid">
            <!-- إحصائيات عامة -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-blue">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="card-title">إحصائيات عامة</div>
                </div>
                <div class="metric-grid">
                    <div class="metric">
                        <div class="metric-value status-good">726</div>
                        <div class="metric-label">إجمالي المفاتيح</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value status-good">680</div>
                        <div class="metric-label">مفاتيح صحية</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value status-warning">40</div>
                        <div class="metric-label">تحتاج انتباه</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value status-critical">6</div>
                        <div class="metric-label">حرجة</div>
                    </div>
                </div>
            </div>
            
            <!-- حالة الأمان -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-green">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="card-title">حالة الأمان</div>
                </div>
                <div style="text-align: center;">
                    <div class="metric-value status-good" style="font-size: 48px;">95%</div>
                    <div class="metric-label">نقاط الأمان</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="margin-top: 15px;">
                        <small>🔐 جميع المفاتيح مشفرة</small><br>
                        <small>👁️ مراقبة نشطة 24/7</small>
                    </div>
                </div>
            </div>
            
            <!-- حالة التدوير -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-orange">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="card-title">حالة التدوير</div>
                </div>
                <div class="metric-grid">
                    <div class="metric">
                        <div class="metric-value status-warning">12</div>
                        <div class="metric-label">مجدولة للتدوير</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value status-good">3</div>
                        <div class="metric-label">تم تدويرها اليوم</div>
                    </div>
                </div>
                <button class="btn btn-warning" style="width: 100%; margin-top: 15px;">
                    <i class="fas fa-sync-alt"></i> تدوير فوري
                </button>
            </div>
            
            <!-- النسخ الاحتياطية -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-purple">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="card-title">النسخ الاحتياطية</div>
                </div>
                <div style="text-align: center;">
                    <div class="metric-value status-good">15</div>
                    <div class="metric-label">نسخ متاحة</div>
                    <div style="margin: 15px 0;">
                        <small>آخر نسخة: منذ ساعة</small><br>
                        <small>الحجم الإجمالي: 2.3 GB</small>
                    </div>
                    <button class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-download"></i> نسخة احتياطية الآن
                    </button>
                </div>
            </div>
            
            <!-- الأنشطة الحديثة -->
            <div class="card wide-card">
                <div class="card-header">
                    <div class="card-icon icon-blue">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="card-title">الأنشطة الحديثة</div>
                </div>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-time">10:30</div>
                        <div class="activity-icon icon-green">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>تم إنشاء نسخة احتياطية</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">09:15</div>
                        <div class="activity-icon icon-blue">
                            <i class="fas fa-sync"></i>
                        </div>
                        <div>تم تدوير مفتاح GitHub</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">08:45</div>
                        <div class="activity-icon icon-orange">
                            <i class="fas fa-exclamation"></i>
                        </div>
                        <div>تنبيه: مفتاح OpenAI ينتهي قريباً</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">07:30</div>
                        <div class="activity-icon icon-green">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div>فحص صحة المفاتيح مكتمل</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">06:00</div>
                        <div class="activity-icon icon-blue">
                            <i class="fas fa-search"></i>
                        </div>
                        <div>تم اكتشاف 3 مفاتيح جديدة</div>
                    </div>
                </div>
            </div>
            
            <!-- توزيع المنصات -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-purple">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="card-title">توزيع المنصات</div>
                </div>
                <div class="chart-container">
                    <canvas id="platformsChart"></canvas>
                </div>
            </div>
            
            <!-- الأداء -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon icon-green">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="card-title">مؤشرات الأداء</div>
                </div>
                <div style="space-y: 15px;">
                    <div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>استخدام المعالج</span>
                            <span class="status-good">12%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 12%; background: linear-gradient(90deg, #3498db, #2980b9);"></div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>استخدام الذاكرة</span>
                            <span class="status-good">45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%; background: linear-gradient(90deg, #27ae60, #2ecc71);"></div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>استخدام القرص</span>
                            <span class="status-warning">67%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 67%; background: linear-gradient(90deg, #f39c12, #e67e22);"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // رسم بياني لتوزيع المنصات
        const ctx = document.getElementById('platformsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Generic', 'Mistral', 'Google Gemini', 'OpenRouter', 'DeepSeek', 'GitHub', 'أخرى'],
                datasets: [{
                    data: [524, 162, 10, 11, 6, 7, 6],
                    backgroundColor: [
                        '#3498db', '#9b59b6', '#e74c3c', '#f39c12', 
                        '#27ae60', '#34495e', '#95a5a6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        
        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            console.log('تحديث البيانات...');
            // هنا يمكن إضافة استدعاءات AJAX لتحديث البيانات
        }, 30000);
        
        // تأثيرات تفاعلية
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>