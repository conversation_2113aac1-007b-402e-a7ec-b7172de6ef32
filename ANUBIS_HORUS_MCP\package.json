{"name": "anubis-mcp-server", "version": "1.0.0", "description": "MCP Server for Anubis AI System", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "build": "echo 'No build step required'", "test": "echo 'No tests specified'"}, "keywords": ["mcp", "server", "anubis", "ai", "assistant"], "author": "Anubis AI Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}}