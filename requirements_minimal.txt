# متطلبات أنوبيس المبسطة للتشغيل السريع
# Anubis Minimal Requirements for Quick Start

# ═══════════════════════════════════════════════════════════════
# المكتبات الأساسية الحرجة - Essential Critical Libraries
# ═══════════════════════════════════════════════════════════════

# إطار العمل الأساسي - Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# قوالب وواجهات - Templates and UI
jinja2==3.1.2
python-multipart==0.0.6

# ═══════════════════════════════════════════════════════════════
# قاعدة البيانات - Database
# ═══════════════════════════════════════════════════════════════

# ORM وقاعدة البيانات - ORM and Database
sqlalchemy==2.0.23
# ملاحظة: SQLite مدمجة في Python - Note: SQLite is built into Python

# ═══════════════════════════════════════════════════════════════
# الأمان الأساسي - Basic Security
# ═══════════════════════════════════════════════════════════════

# JWT والتشفير - JWT and Encryption
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7

# ═══════════════════════════════════════════════════════════════
# أدوات مساعدة أساسية - Basic Utilities
# ═══════════════════════════════════════════════════════════════

# طلبات HTTP - HTTP Requests
requests==2.31.0

# معالجة JSON وYAML - JSON and YAML Processing
pyyaml==6.0.1

# التاريخ والوقت - Date and Time
python-dateutil==2.8.2

# متغيرات البيئة - Environment Variables
python-dotenv==1.0.0

# ═══════════════════════════════════════════════════════════════
# اختيارية للتطوير - Optional for Development
# ═══════════════════════════════════════════════════════════════

# اختبارات - Testing (اختياري)
# pytest==7.4.3
# pytest-cov==4.1.0

# فحص الكود - Code Linting (اختياري)
# flake8==6.1.0
# black==23.11.0

# ═══════════════════════════════════════════════════════════════
# ملاحظات مهمة - Important Notes
# ═══════════════════════════════════════════════════════════════

# 1. هذا ملف متطلبات مبسط للتشغيل السريع فقط
#    This is a simplified requirements file for quick start only

# 2. للحصول على جميع الميزات، استخدم requirements.txt الكامل
#    For full features, use the complete requirements.txt

# 3. بعض المكتبات قد تحتاج إعدادات إضافية:
#    Some libraries may need additional setup:
#    - مفاتيح API للذكاء الاصطناعي
#    - إعدادات قاعدة البيانات
#    - شهادات SSL

# 4. لتثبيت هذه المتطلبات:
#    To install these requirements:
#    pip install -r requirements_minimal.txt

# ═══════════════════════════════════════════════════════════════
# إصدارات بديلة للتوافق - Alternative Versions for Compatibility
# ═══════════════════════════════════════════════════════════════

# إذا واجهت مشاكل في التثبيت، جرب هذه الإصدارات:
# If you encounter installation issues, try these versions:

# fastapi>=0.100.0,<0.105.0
# uvicorn>=0.20.0,<0.25.0
# pydantic>=2.0.0,<3.0.0
# sqlalchemy>=2.0.0,<2.1.0
