#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فحص نماذج Ollama المثبتة
Ollama Models Checker

فحص شامل لنماذج Ollama المتاحة واختبار الاتصال
"""

import json
import subprocess
from datetime import datetime

import requests


def check_ollama_service():
    """فحص خدمة Ollama"""
    print("🔍 فحص خدمة Ollama...")

    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ خدمة Ollama تعمل بشكل طبيعي")
            return True, response.json()
        else:
            print(f"❌ خدمة Ollama ترد بكود خطأ: {response.status_code}")
            return False, None
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بخدمة Ollama - تأكد من تشغيلها")
        return False, None
    except Exception as e:
        print(f"❌ خطأ في فحص خدمة Ollama: {e}")
        return False, None


def get_installed_models():
    """الحصول على النماذج المثبتة"""
    print("\n📋 النماذج المثبتة:")

    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            lines = result.stdout.strip().split("\n")
            models = []

            for i, line in enumerate(lines):
                if i == 0:  # العنوان
                    print(f"   {line}")
                    continue

                if line.strip():
                    parts = line.split()
                    if len(parts) >= 3:
                        model_name = parts[0]
                        model_id = parts[1] if len(parts) > 1 else "N/A"
                        size = parts[2] if len(parts) > 2 else "N/A"
                        modified = " ".join(parts[3:]) if len(parts) > 3 else "N/A"

                        models.append(
                            {
                                "name": model_name,
                                "id": model_id,
                                "size": size,
                                "modified": modified,
                            }
                        )

                        print(f"   📦 {model_name}")
                        print(f"      🆔 ID: {model_id}")
                        print(f"      📏 الحجم: {size}")
                        print(f"      📅 آخر تعديل: {modified}")
                        print()

            return models
        else:
            print(f"❌ خطأ في تشغيل ollama list: {result.stderr}")
            return []

    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة تشغيل الأمر")
        return []
    except FileNotFoundError:
        print("❌ أمر ollama غير موجود - تأكد من تثبيت Ollama")
        return []
    except Exception as e:
        print(f"❌ خطأ في الحصول على النماذج: {e}")
        return []


def test_model(model_name):
    """اختبار نموذج محدد"""
    print(f"\n🧪 اختبار النموذج: {model_name}")

    try:
        test_prompt = "مرحبا، كيف حالك؟"

        data = {"model": model_name, "prompt": test_prompt, "stream": False}

        print(f"   📤 إرسال: {test_prompt}")

        response = requests.post("http://localhost:11434/api/generate", json=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            ai_response = result.get("response", "لا توجد استجابة")

            print(f"   📥 الاستجابة: {ai_response[:100]}...")
            print(f"   ⏱️ وقت التوليد: {result.get('total_duration', 0) / 1e9:.2f} ثانية")
            print(f"   🔢 الرموز المولدة: {result.get('eval_count', 0)}")

            return True, ai_response
        else:
            print(f"   ❌ فشل الاختبار - كود الخطأ: {response.status_code}")
            return False, None

    except requests.exceptions.Timeout:
        print("   ❌ انتهت مهلة الاختبار")
        return False, None
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        return False, None


def get_running_models():
    """الحصول على النماذج قيد التشغيل"""
    print("\n🏃 النماذج قيد التشغيل:")

    try:
        result = subprocess.run(["ollama", "ps"], capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            lines = result.stdout.strip().split("\n")

            if len(lines) <= 1 or not any(line.strip() for line in lines[1:]):
                print("   📭 لا توجد نماذج قيد التشغيل حالياً")
                return []

            running_models = []
            for i, line in enumerate(lines):
                if i == 0:  # العنوان
                    print(f"   {line}")
                    continue

                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        model_name = parts[0]
                        model_id = parts[1]

                        running_models.append({"name": model_name, "id": model_id})

                        print(f"   🟢 {model_name} ({model_id})")

            return running_models
        else:
            print(f"   ❌ خطأ في تشغيل ollama ps: {result.stderr}")
            return []

    except Exception as e:
        print(f"   ❌ خطأ في فحص النماذج قيد التشغيل: {e}")
        return []


def generate_report(service_status, models, running_models, test_results):
    """إنتاج تقرير شامل"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "ollama_service": {
            "status": "running" if service_status else "not_running",
            "accessible": service_status,
        },
        "installed_models": {"count": len(models), "models": models},
        "running_models": {"count": len(running_models), "models": running_models},
        "test_results": test_results,
        "summary": {
            "total_models": len(models),
            "working_models": len([r for r in test_results if r["success"]]),
            "failed_models": len([r for r in test_results if not r["success"]]),
        },
    }

    # حفظ التقرير
    report_file = f"ollama_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n📄 تم حفظ التقرير في: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")

    return report


def main():
    """الدالة الرئيسية"""
    print("🏺 فحص نماذج Ollama - نظام أنوبيس")
    print("=" * 50)

    # فحص خدمة Ollama
    service_running, api_data = check_ollama_service()

    # الحصول على النماذج المثبتة
    models = get_installed_models()

    # الحصول على النماذج قيد التشغيل
    running_models = get_running_models()

    # اختبار النماذج
    test_results = []

    if service_running and models:
        print("\n🧪 اختبار النماذج:")
        print("-" * 30)

        # اختبار أول 3 نماذج فقط لتوفير الوقت
        models_to_test = models[:3]

        for model in models_to_test:
            model_name = model["name"]
            success, response = test_model(model_name)

            test_results.append(
                {
                    "model": model_name,
                    "success": success,
                    "response_preview": response[:100] if response else None,
                }
            )

    # إنتاج التقرير
    report = generate_report(service_running, models, running_models, test_results)

    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   🔧 خدمة Ollama: {'✅ تعمل' if service_running else '❌ لا تعمل'}")
    print(f"   📦 النماذج المثبتة: {len(models)}")
    print(f"   🏃 النماذج قيد التشغيل: {len(running_models)}")
    print(f"   ✅ النماذج التي تعمل: {len([r for r in test_results if r['success']])}")
    print(f"   ❌ النماذج التي لا تعمل: {len([r for r in test_results if not r['success']])}")

    if models:
        print("\n🎯 النماذج المتاحة للاستخدام:")
        for model in models:
            print(f"   • {model['name']} ({model['size']})")

    print("\n🏺 انتهى فحص نماذج Ollama!")


if __name__ == "__main__":
    main()
