#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 الوصول السريع لمشروع أنوبيس
Anubis Quick Access
"""

import os
import subprocess
import webbrowser
from pathlib import Path

class AnubisQuickAccess:
    """الوصول السريع لمشروع أنوبيس"""
    
    def __init__(self):
        self.project_root = Path("..").resolve()
    
    def open_main_system(self):
        """فتح النظام الأساسي"""
        main_file = self.project_root / "main.py"
        if main_file.exists():
            os.system(f'code "{main_file}"')
            print("🏺 تم فتح النظام الأساسي")
        else:
            print("❌ النظام الأساسي غير موجود")
    
    def open_isolation_system(self):
        """فتح النظام المعزول"""
        isolation_dir = self.project_root / "anubis_isolation_system"
        if isolation_dir.exists():
            os.system(f'code "{isolation_dir}"')
            print("🔒 تم فتح النظام المعزول")
        else:
            print("❌ النظام المعزول غير موجود")
    
    def open_ai_team(self):
        """فتح فريق الذكاء الاصطناعي"""
        team_dir = self.project_root / "anubis_ai_team"
        if team_dir.exists():
            os.system(f'code "{team_dir}"')
            print("🤖 تم فتح فريق الذكاء الاصطناعي")
        else:
            print("❌ فريق الذكاء الاصطناعي غير موجود")
    
    def open_service_urls(self):
        """فتح روابط الخدمات"""
        urls = [
            "http://localhost:8000",  # النظام الأساسي
            "http://localhost:8080",  # النظام المعزول
            "http://localhost:9090"   # المراقبة
        ]
        
        for url in urls:
            webbrowser.open(url)
            print(f"🌐 تم فتح: {url}")
    
    def run_system_tests(self):
        """تشغيل اختبارات النظام"""
        test_files = [
            "anubis_api_comprehensive_test.py",
            "anubis_complete_system_test.py"
        ]
        
        for test_file in test_files:
            test_path = self.project_root / test_file
            if test_path.exists():
                print(f"🧪 تشغيل: {test_file}")
                subprocess.run(["python", str(test_path)])
    
    def show_menu(self):
        """عرض القائمة"""
        print("🏺 الوصول السريع لمشروع أنوبيس")
        print("=" * 40)
        print("1. فتح النظام الأساسي")
        print("2. فتح النظام المعزول") 
        print("3. فتح فريق الذكاء الاصطناعي")
        print("4. فتح روابط الخدمات")
        print("5. تشغيل اختبارات النظام")
        print("0. خروج")
        
        choice = input("\nاختر رقم: ")
        
        if choice == "1":
            self.open_main_system()
        elif choice == "2":
            self.open_isolation_system()
        elif choice == "3":
            self.open_ai_team()
        elif choice == "4":
            self.open_service_urls()
        elif choice == "5":
            self.run_system_tests()
        elif choice == "0":
            print("👋 وداعاً!")
            return False
        else:
            print("❌ اختيار غير صحيح")
        
        return True

def main():
    """الدالة الرئيسية"""
    access = AnubisQuickAccess()
    
    while True:
        if not access.show_menu():
            break
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
