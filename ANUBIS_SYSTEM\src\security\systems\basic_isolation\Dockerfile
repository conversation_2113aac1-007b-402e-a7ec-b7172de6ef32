# نظام العزل الأساسي لأنوبيس
FROM python:3.11-slim

# إعداد متغيرات البيئة الآمنة
ENV PYTHONUNBUFFERED=1
ENV ANUBIS_ISOLATION_MODE=basic
ENV USER_ID=1000
ENV GROUP_ID=1000

# إنشاء مستخدم غير مميز للأمان
RUN groupadd -g $GROUP_ID anubis_basic && \
    useradd -u $USER_ID -g $GROUP_ID -m -s /bin/bash anubis_basic

# تثبيت الأدوات الأساسية بشكل آمن
RUN apt-get update && apt-get install -y --no-install-recommends \
    sqlite3 \
    curl \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل
WORKDIR /app

# إنشاء المجلدات المطلوبة
RUN mkdir -p /app/data /app/logs /app/temp /app/configs \
    && chown -R anubis_basic:anubis_basic /app

# نسخ متطلبات Python
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# نسخ التطبيق
COPY --chown=anubis_basic:anubis_basic . .

# التبديل للمستخدم غير المميز
USER anubis_basic

# تكوين الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sqlite3; print('OK')" || exit 1

# المنفذ المكشوف
EXPOSE 8000

# نقطة الدخول الآمنة
ENTRYPOINT ["python", "-m", "anubis.basic_mode"]
