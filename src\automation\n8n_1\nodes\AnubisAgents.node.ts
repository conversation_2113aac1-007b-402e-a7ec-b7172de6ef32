import {
	IExecuteFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	NodeOperationError,
} from 'n8n-workflow';

export class AnubisAgents implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Anubis Agents',
		name: 'anubisAgents',
		icon: 'file:anubis.svg',
		group: ['anubis'],
		version: 1,
		subtitle: '={{$parameter["agentType"]}}',
		description: 'Execute Anubis intelligent agents',
		defaults: {
			name: 'Anubis Agents',
		},
		inputs: ['main'],
		outputs: ['main'],
		credentials: [
			{
				name: 'anubis<PERSON><PERSON>',
				required: true,
			},
		],
		properties: [
			{
				displayName: 'Agent Type',
				name: 'agentType',
				type: 'options',
				options: [
					{
						name: 'Error Detector',
						value: 'error_detector',
						description: 'Detect and analyze errors in code',
					},
					{
						name: 'Project Analyzer',
						value: 'project_analyzer',
						description: 'Analyze project structure and quality',
					},
					{
						name: 'File Organizer',
						value: 'file_organizer',
						description: 'Organize and clean up project files',
					},
					{
						name: 'Memory Agent',
						value: 'memory_agent',
						description: 'Manage and retrieve project memory',
					},
					{
						name: 'Database Agent',
						value: 'database_agent',
						description: 'Interact with project database',
					},
					{
						name: 'Smart AI Agent',
						value: 'smart_ai_agent',
						description: 'General purpose intelligent agent',
					},
					{
						name: 'Code Analyzer',
						value: 'code_analyzer',
						description: 'Analyze code quality and patterns',
					},
				],
				default: 'error_detector',
				description: 'The type of agent to execute',
			},
			{
				displayName: 'Project Path',
				name: 'projectPath',
				type: 'string',
				default: '.',
				placeholder: '/path/to/project',
				description: 'Path to the project for analysis',
			},
			{
				displayName: 'Verbose Output',
				name: 'verbose',
				type: 'boolean',
				default: true,
				description: 'Enable detailed output from the agent',
			},
			{
				displayName: 'Configuration',
				name: 'config',
				type: 'json',
				default: '{}',
				description: 'JSON configuration for the agent',
			},
			{
				displayName: 'Additional Options',
				name: 'additionalFields',
				type: 'collection',
				placeholder: 'Add Field',
				default: {},
				options: [
					{
						displayName: 'Timeout (seconds)',
						name: 'timeout',
						type: 'number',
						default: 300,
						description: 'Maximum execution time for the agent',
					},
					{
						displayName: 'Include Files Pattern',
						name: 'includePattern',
						type: 'string',
						default: '',
						placeholder: '*.py,*.js,*.ts',
						description: 'File patterns to include in analysis',
					},
					{
						displayName: 'Exclude Files Pattern',
						name: 'excludePattern',
						type: 'string',
						default: '',
						placeholder: 'node_modules,*.log',
						description: 'File patterns to exclude from analysis',
					},
					{
						displayName: 'Save Results to Database',
						name: 'saveToDatabase',
						type: 'boolean',
						default: true,
						description: 'Whether to save results to Anubis database',
					},
				],
			},
		],
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const returnData: INodeExecutionData[] = [];

		for (let i = 0; i < items.length; i++) {
			try {
				const agentType = this.getNodeParameter('agentType', i) as string;
				const projectPath = this.getNodeParameter('projectPath', i) as string;
				const verbose = this.getNodeParameter('verbose', i) as boolean;
				const configString = this.getNodeParameter('config', i) as string;
				const additionalFields = this.getNodeParameter('additionalFields', i) as any;

				// تحليل التكوين
				let config: any = {};
				try {
					config = JSON.parse(configString);
				} catch (error) {
					throw new NodeOperationError(this.getNode(), `Invalid JSON configuration: ${error.message}`);
				}

				// إضافة الخيارات الإضافية إلى التكوين
				if (additionalFields.includePattern) {
					config.includePattern = additionalFields.includePattern;
				}
				if (additionalFields.excludePattern) {
					config.excludePattern = additionalFields.excludePattern;
				}
				if (additionalFields.timeout) {
					config.timeout = additionalFields.timeout;
				}

				// إعداد طلب API
				const requestBody = {
					agent_type: agentType,
					project_path: projectPath,
					config,
					verbose,
				};

				// استدعاء Anubis API
				const response = await this.helpers.httpRequestWithAuthentication.call(
					this,
					'anubisApi',
					{
						method: 'POST',
						url: '/api/v1/agents/run',
						body: requestBody,
						json: true,
						timeout: (additionalFields.timeout || 300) * 1000,
					},
				);

				// إعداد البيانات المرجعة
				const resultData: any = {
					agentType: response.agent_type,
					projectPath: response.project_path,
					result: response.result,
					executionTime: response.execution_time,
					status: response.status,
					timestamp: new Date().toISOString(),
				};

				// حفظ النتائج في قاعدة البيانات إذا كان مطلوباً
				if (additionalFields.saveToDatabase !== false) {
					try {
						await this.helpers.httpRequestWithAuthentication.call(
							this,
							'anubisApi',
							{
								method: 'POST',
								url: '/api/v1/database/projects',
								body: {
									name: `Agent Analysis - ${agentType}`,
									path: projectPath,
									type: 'agent_analysis',
									description: `Analysis by ${agentType} agent`,
								},
								json: true,
							},
						);
					} catch (dbError) {
						// لا نفشل العملية إذا فشل حفظ قاعدة البيانات
						resultData.databaseSaveError = dbError.message;
					}
				}

				returnData.push({
					json: resultData,
				});

			} catch (error) {
				if (this.continueOnFail()) {
					returnData.push({
						json: {
							error: error.message,
							status: 'error',
							timestamp: new Date().toISOString(),
						},
					});
					continue;
				}
				throw new NodeOperationError(this.getNode(), error);
			}
		}

		return [returnData];
	}
}
