# 🏺 نظام أنوبيس - Universal AI Assistants

> **نظام شامل للذكاء الاصطناعي والأتمتة مع دعم متعدد المقدمين**
>
> *A comprehensive AI assistant system with multiple AI providers and automation capabilities*

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🌟 **المميزات الرئيسية**

### 🤖 **خدمات الذكاء الاصطناعي المتعددة**
- **OpenAI GPT** - نماذج GPT-3.5 و GPT-4
- **Google Gemini** - نماذج Gemini Pro و Ultra
- **Anthropic Claude** - نماذج Claude 3
- **Ollama** - نماذج محلية مفتوحة المصدر

### 🔄 **أتمتة سير العمل**
- **N8N Integration** - أتمتة المهام والعمليات
- **Workflow Management** - إدارة سير العمل المعقدة
- **API Automation** - ربط الخدمات المختلفة

### 🛡️ **الأمان والعزل**
- **Docker Isolation** - عزل الخدمات في حاويات آمنة
- **Network Security** - شبكات معزولة ومحمية
- **Access Control** - نظام صلاحيات متقدم

### 📊 **المراقبة والتحليل**
- **Prometheus** - جمع المقاييس والإحصائيات
- **Grafana** - لوحات مراقبة تفاعلية
- **Health Checks** - فحص صحة النظام المستمر

## 🚀 **البدء السريع**

### 📋 **المتطلبات**
- Python 3.8+
- Docker & Docker Compose (اختياري)
- 4GB RAM (الحد الأدنى)
- 10GB مساحة تخزين

### ⚡ **التثبيت السريع**

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# 2. تشغيل الإعداد السريع
python scripts/quick_setup.py

# 3. تفعيل البيئة الافتراضية
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 4. تشغيل النظام
python main.py
```

### 🖥️ **استخدام CLI الموحد**

```bash
# عرض المساعدة
python src/cli/anubis_cli.py --help

# تشغيل النظام
python src/cli/anubis_cli.py system start

# عرض حالة النظام
python src/cli/anubis_cli.py system status

# تشغيل مع Docker
python src/cli/anubis_cli.py docker up
```

## 🏗️ **هيكل المشروع الجديد**

```
Universal-AI-Assistants/
├── 📁 src/                      # كود المصدر الرئيسي
│   ├── 🏛️ core/                 # النظام الأساسي (FastAPI)
│   │   ├── main.py              # الملف الرئيسي
│   │   ├── api/                 # واجهات API
│   │   ├── models/              # نماذج البيانات
│   │   └── utils/               # أدوات مساعدة
│   ├── 🤖 ai_services/          # خدمات الذكاء الاصطناعي
│   │   ├── openai/              # خدمات OpenAI
│   │   ├── gemini/              # خدمات Google Gemini
│   │   ├── claude/              # خدمات Anthropic Claude
│   │   └── ollama/              # خدمات Ollama المحلية
│   ├── 🔄 automation/           # أتمتة سير العمل
│   │   ├── n8n/                 # تكامل N8N
│   │   ├── workflows/           # سير العمل
│   │   └── triggers/            # محفزات الأتمتة
│   ├── 🛡️ security/             # نظام الأمان والعزل
│   │   ├── isolation/           # أنظمة العزل
│   │   ├── auth/                # المصادقة والتخويل
│   │   └── encryption/          # التشفير
│   ├── 📊 monitoring/           # أدوات المراقبة
│   │   ├── prometheus/          # إعدادات Prometheus
│   │   ├── grafana/             # لوحات Grafana
│   │   └── health/              # فحص الصحة
│   ├── 🗄️ data_management/      # إدارة قواعد البيانات
│   │   ├── models/              # نماذج قواعد البيانات
│   │   ├── migrations/          # ترحيل البيانات
│   │   └── backup/              # النسخ الاحتياطية
│   ├── 🛠️ dev_environment/      # بيئات التطوير
│   │   ├── jupyter/             # Jupyter Lab
│   │   ├── streamlit/           # تطبيقات Streamlit
│   │   └── tools/               # أدوات التطوير
│   └── 🖥️ cli/                  # واجهة سطر الأوامر
│       └── anubis_cli.py        # CLI الموحد
├── ⚙️ config/                   # ملفات الإعدادات
│   ├── ai_config.json           # إعدادات الذكاء الاصطناعي
│   ├── database_config.json     # إعدادات قاعدة البيانات
│   └── security/                # إعدادات الأمان
├── 📊 data/                     # البيانات وقواعد البيانات
│   └── database/                # ملفات قاعدة البيانات
├── 📚 docs/                     # التوثيق والأدلة
│   ├── api/                     # توثيق API
│   ├── guides/                  # أدلة الاستخدام
│   └── reports/                 # التقارير
├── 🧪 tests/                    # ملفات الاختبار
│   ├── unit/                    # اختبارات الوحدة
│   ├── integration/             # اختبارات التكامل
│   └── e2e/                     # اختبارات شاملة
├── 📝 logs/                     # سجلات النظام
├── 🔧 scripts/                  # نصوص برمجية مساعدة
│   ├── quick_setup.py           # إعداد سريع
│   ├── analyzers/               # محللات النظام
│   └── organizers/              # منظمات المشروع
├── 🐳 docker-compose.yml        # إعدادات Docker Compose
├── 📦 requirements.txt          # متطلبات Python الموحدة
├── 🔐 .env.example              # مثال متغيرات البيئة
└── 🏺 main.py                   # نقطة الدخول الرئيسية
```

## 🚀 التشغيل السريع

### الخدمات المعزولة المتاحة:
```bash
# النظام الرئيسي
bash anubis_main_system/start_isolated_main_system.sh

# نظام الذكاء الاصطناعي
bash universal_ai_system/start_isolated_ai_system.sh

# سير العمل والأتمتة
bash workflows_and_automation/start_isolated_workflows.sh

# بيئة العمل
bash workspace/start_isolated_workspace.sh

# الأدوات والمرافق
bash tools_and_utilities/start_isolated_tools.sh
```

### نظام النسخ الاحتياطي الموحد:
```bash
# إنشاء نسخة احتياطية كاملة
python utilities/helpers/unified_backup_system.py
```

## 🐳 أنظمة العزل

جميع الخدمات معزولة باستخدام Docker مع:
- 🔒 شبكات منفصلة وآمنة
- 🛡️ أمان متعدد الطبقات
- 📊 مراقبة مستمرة
- 💾 نسخ احتياطية آلية

### تغطية العزل: 85.7%
- ✅ 6 أنظمة بعزل متقدم
- ⚠️ 1 نظام يحتاج تحسين

## 📊 الخدمات المتاحة

| الخدمة | المنفذ | الوصف |
|--------|-------|-------|
| 🏠 النظام الرئيسي | 8080 | الواجهة الرئيسية |
| 🤖 الذكاء الاصطناعي | 8090-8091 | خدمات الذكاء الاصطناعي |
| 🔄 سير العمل | 5678 | n8n للأتمتة |
| 💼 بيئة العمل | 8888 | Jupyter Lab |
| 📊 المراقبة | 9090-9094 | Prometheus |

## 🛠️ أدوات التطوير

### سكريبتات الفحص:
- `scripts/inspectors/` - فحص النظام والمكونات
- `scripts/analyzers/` - تحليل الأداء والبيانات
- `scripts/organizers/` - تنظيم وإدارة النظام

### التقارير:
- `reports/system/` - تقارير النظام العامة
- `reports/inspection/` - تقارير الفحص المفصلة
- `reports/analysis/` - تقارير التحليل المتقدم

## 🔒 الأمان

- 🛡️ عزل كامل للخدمات
- 🔐 تشفير البيانات الحساسة
- 📊 مراقبة أمنية مستمرة
- 💾 نسخ احتياطية مشفرة
- 🚫 صلاحيات محدودة

## 📚 التوثيق المفصل

- `documentation/guides/` - أدلة الاستخدام
- `documentation/reports/` - تقارير التوثيق
- كل مجلد يحتوي على `README.md` خاص به

## 🤝 المساهمة

يتم تطوير النظام باستمرار مع:
- ✅ اختبارات شاملة
- 📊 مراقبة الأداء
- 🔄 تحديثات دورية
- 🛡️ أمان متقدم

---
💡 **تطوير**: نظام أنوبيس - نظام شامل ومنظم للذكاء الاصطناعي والأتمتة
