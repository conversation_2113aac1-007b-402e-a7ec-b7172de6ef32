#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ اختبار سريع للذكاء الاصطناعي
Quick AI Test

اختبار سريع لحالة الذكاء الاصطناعي بعد الإصلاحات
"""

import json
import os
import sys

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))


def test_ollama_quick():
    """اختبار سريع لـ Ollama"""
    print("⚡ اختبار سريع لـ Ollama...")

    try:
        from anubis.core.ai_integration import OllamaProvider

        # اختبار النموذج الأساسي
        provider = OllamaProvider(model_name="llama3:8b")

        if provider.is_available():
            print("   ✅ Ollama متاح")

            # اختبار بسيط
            response = provider.generate_response("قل مرحبا")

            if response and not response.startswith("خطأ"):
                print(f"   ✅ يعمل: {response[:50]}...")
                return True
            else:
                print(f"   ❌ لا يعمل: {response}")
                return False
        else:
            print("   ❌ Ollama غير متاح")
            return False

    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False


def test_ai_manager_quick():
    """اختبار سريع لمدير الذكاء الاصطناعي"""
    print("\n⚡ اختبار سريع لمدير الذكاء الاصطناعي...")

    try:
        from anubis.core.ai_integration import AIIntegrationManager

        ai_manager = AIIntegrationManager()

        # فحص الحالة
        status = ai_manager.get_status()
        available_providers = ai_manager.get_available_providers()

        print(f"   📊 الموفرين المتاحين: {available_providers}")

        if available_providers:
            # اختبار توليد بسيط
            response = ai_manager.generate_ai_response("مرحبا")

            if response and not response.startswith("خطأ"):
                print(f"   ✅ يعمل: {response[:50]}...")
                return True
            else:
                print(f"   ❌ لا يعمل: {response}")
                return False
        else:
            print("   ❌ لا توجد موفرين متاحين")
            return False

    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False


def test_agent_quick():
    """اختبار سريع للوكيل"""
    print("\n⚡ اختبار سريع للوكيل...")

    try:
        from anubis.core.base_agent import BaseAgent

        # إنشاء وكيل بسيط
        class SimpleTestAgent(BaseAgent):
            def get_agent_type(self):
                return "simple_test"

            def initialize_agent(self):
                pass

        agent = SimpleTestAgent(project_path=".", config={"test": True}, verbose=False)

        # فحص حالة الذكاء الاصطناعي
        ai_status = agent.get_ai_status()

        print(f"   📊 الذكاء الاصطناعي متاح: {ai_status.get('available', False)}")

        if agent.is_ai_enabled():
            # اختبار تحليل بسيط
            analysis = agent.get_ai_analysis("اكتب كلمة واحدة")

            if analysis and not analysis.startswith("خطأ"):
                print(f"   ✅ التحليل يعمل: {analysis[:30]}...")
                return True
            else:
                print(f"   ❌ التحليل لا يعمل: {analysis}")
                return False
        else:
            print("   ❌ الذكاء الاصطناعي غير مفعل في الوكيل")
            return False

    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("⚡ اختبار سريع للذكاء الاصطناعي - نظام أنوبيس")
    print("=" * 50)

    # اختبار المكونات
    ollama_works = test_ollama_quick()
    manager_works = test_ai_manager_quick()
    agent_works = test_agent_quick()

    # النتائج
    print("\n" + "=" * 50)
    print("📊 النتائج السريعة:")
    print(f"   🤖 Ollama: {'✅ يعمل' if ollama_works else '❌ لا يعمل'}")
    print(f"   🧠 مدير AI: {'✅ يعمل' if manager_works else '❌ لا يعمل'}")
    print(f"   🔗 دمج الوكلاء: {'✅ يعمل' if agent_works else '❌ لا يعمل'}")

    # الخلاصة
    if ollama_works and manager_works and agent_works:
        print("\n🎉 جميع المكونات تعمل! الذكاء الاصطناعي مفعل بالكامل!")
        return 0
    elif ollama_works and manager_works:
        print("\n⚠️ Ollama ومدير AI يعملان، لكن دمج الوكلاء يحتاج إصلاح")
        return 1
    elif ollama_works:
        print("\n⚠️ Ollama يعمل فقط، باقي المكونات تحتاج إصلاح")
        return 2
    else:
        print("\n❌ جميع المكونات تحتاج إصلاح")
        return 3


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
