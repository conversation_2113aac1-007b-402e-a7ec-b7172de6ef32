# Anubis Node.js & MCP Fix Script
# يقوم بإصلاح مشاكل Node.js و MCP Server

Write-Host "🔧 Anubis Node.js & MCP Fix Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# 1. Check Node.js installation
Write-Host "1️⃣ Checking Node.js installation..." -ForegroundColor Yellow

if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js not found!" -ForegroundColor Red
    Write-Host "Installing Node.js using winget..." -ForegroundColor Yellow
    
    try {
        winget install OpenJS.NodeJS
        Write-Host "✅ Node.js installed successfully!" -ForegroundColor Green
        Write-Host "⚠️ Please restart your terminal and run this script again." -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit
    } catch {
        Write-Host "❌ Failed to install Node.js automatically." -ForegroundColor Red
        Write-Host "Please install Node.js manually from https://nodejs.org/" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit
    }
}

# 2. Check npm
Write-Host ""
Write-Host "2️⃣ Checking npm..." -ForegroundColor Yellow

if (Test-Command "npm") {
    $npmVersion = npm --version
    Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "❌ npm not found!" -ForegroundColor Red
    Write-Host "npm should come with Node.js. Please reinstall Node.js." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

# 3. Check if package.json exists
Write-Host ""
Write-Host "3️⃣ Checking package.json..." -ForegroundColor Yellow

if (Test-Path "package.json") {
    Write-Host "✅ package.json found" -ForegroundColor Green
} else {
    Write-Host "❌ package.json not found!" -ForegroundColor Red
    Write-Host "This script should be run from the project root directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

# 4. Install dependencies
Write-Host ""
Write-Host "4️⃣ Installing dependencies..." -ForegroundColor Yellow

try {
    npm install
    Write-Host "✅ Dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# 5. Check MCP server file
Write-Host ""
Write-Host "5️⃣ Checking MCP server file..." -ForegroundColor Yellow

if (Test-Path "src/index.js") {
    Write-Host "✅ MCP server file found" -ForegroundColor Green
} else {
    Write-Host "❌ MCP server file not found!" -ForegroundColor Red
    Write-Host "Creating src directory and index.js..." -ForegroundColor Yellow
    
    if (!(Test-Path "src")) {
        New-Item -ItemType Directory -Path "src"
    }
    
    # Create basic MCP server file would go here
    Write-Host "⚠️ Please ensure src/index.js exists with proper MCP server code." -ForegroundColor Yellow
}

# 6. Test MCP server
Write-Host ""
Write-Host "6️⃣ Testing MCP server..." -ForegroundColor Yellow

try {
    $job = Start-Job -ScriptBlock { 
        Set-Location $using:PWD
        node src/index.js 
    }
    
    Start-Sleep -Seconds 3
    
    if ($job.State -eq "Running") {
        Write-Host "✅ MCP server started successfully!" -ForegroundColor Green
        Stop-Job $job
        Remove-Job $job
    } else {
        Write-Host "❌ MCP server failed to start!" -ForegroundColor Red
        $error = Receive-Job $job
        Write-Host "Error: $error" -ForegroundColor Red
        Remove-Job $job
    }
} catch {
    Write-Host "❌ Error testing MCP server: $_" -ForegroundColor Red
}

# 7. Summary
Write-Host ""
Write-Host "🎉 Fix Script Complete!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run: npm start" -ForegroundColor White
Write-Host "2. Or run: node src/index.js" -ForegroundColor White
Write-Host "3. Or double-click: start-mcp-server.bat" -ForegroundColor White
Write-Host ""
Write-Host "For VS Code integration, see MCP_SERVER_README.md" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
