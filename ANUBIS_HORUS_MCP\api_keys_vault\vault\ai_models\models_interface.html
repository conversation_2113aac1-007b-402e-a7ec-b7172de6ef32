<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 واجهة استدعاء نماذج الذكاء الاصطناعي</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .model-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .model-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        
        .model-card.available {
            border-left: 5px solid #27ae60;
        }
        
        .model-card.unavailable {
            border-left: 5px solid #e74c3c;
            opacity: 0.7;
        }
        
        .prompt-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .prompt-input {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            font-family: inherit;
        }
        
        .prompt-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .buttons-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .btn-primary { background: linear-gradient(135deg, #667eea, #764ba2); }
        .btn-success { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .btn-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .btn-info { background: linear-gradient(135deg, #3498db, #2980b9); }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .results-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .response-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-available { background: #27ae60; }
        .status-unavailable { background: #e74c3c; }
        
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 واجهة استدعاء نماذج الذكاء الاصطناعي</h1>
            <p>استدعِ نماذج الذكاء الاصطناعي من جميع المنصات المتاحة</p>
        </div>
        
        <div class="models-grid">
            <div class="model-card available">
                <h3>🤖 Google Gemini <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> gemini-pro, gemini-pro-vision</p>
                <p><strong>المفاتيح المتاحة:</strong> 4 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.002 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🚀 OpenRouter <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> GPT-4, Claude-3, Llama-2</p>
                <p><strong>المفاتيح المتاحة:</strong> 4 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.005 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🧠 Anthropic Claude <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> Claude-3-Opus, Sonnet, Haiku</p>
                <p><strong>المفاتيح المتاحة:</strong> 1 مفتاح</p>
                <p><strong>التكلفة:</strong> ~$0.008 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🔍 DeepSeek <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> deepseek-chat, deepseek-coder</p>
                <p><strong>المفاتيح المتاحة:</strong> 3 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.003 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🌪️ Mistral AI <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> mistral-large, mistral-medium</p>
                <p><strong>المفاتيح المتاحة:</strong> 1 مفتاح</p>
                <p><strong>التكلفة:</strong> ~$0.004 لكل استعلام</p>
            </div>
            
            <div class="model-card available">
                <h3>🤗 Hugging Face <span class="status-indicator status-available"></span></h3>
                <p><strong>النماذج:</strong> GPT-2, BERT, T5, Llama</p>
                <p><strong>المفاتيح المتاحة:</strong> 5 مفاتيح</p>
                <p><strong>التكلفة:</strong> ~$0.001 لكل استعلام</p>
            </div>
        </div>
        
        <div class="prompt-section">
            <h3>💬 اكتب طلبك هنا:</h3>
            <textarea id="promptInput" class="prompt-input" placeholder="مثال: اكتب لي قصة قصيرة عن الذكاء الاصطناعي..."></textarea>
            
            <div class="buttons-section">
                <button class="btn btn-primary" onclick="callSingleModel('google_gemini')">
                    🤖 Google Gemini
                </button>
                <button class="btn btn-success" onclick="callSingleModel('openrouter')">
                    🚀 OpenRouter
                </button>
                <button class="btn btn-warning" onclick="callSingleModel('anthropic')">
                    🧠 Claude
                </button>
                <button class="btn btn-info" onclick="callSingleModel('deepseek')">
                    🔍 DeepSeek
                </button>
                <button class="btn btn-primary" onclick="callSingleModel('mistral')">
                    🌪️ Mistral
                </button>
                <button class="btn btn-success" onclick="callSingleModel('huggingface')">
                    🤗 Hugging Face
                </button>
                <button class="btn btn-warning" onclick="callAllModels()" style="font-size: 18px; padding: 15px 30px;">
                    🚀 استدعاء جميع النماذج
                </button>
            </div>
        </div>
        
        <div id="resultsSection" class="results-section">
            <h3>📊 النتائج:</h3>
            <div id="resultsContainer"></div>
        </div>
    </div>
    
    <script>
        function callSingleModel(platform) {
            const prompt = document.getElementById('promptInput').value;
            if (!prompt.trim()) {
                alert('يرجى كتابة طلبك أولاً');
                return;
            }
            
            showLoading();
            
            // محاكاة استدعاء النموذج
            setTimeout(() => {
                const response = generateMockResponse(platform, prompt);
                displayResults([response]);
            }, 2000);
        }
        
        function callAllModels() {
            const prompt = document.getElementById('promptInput').value;
            if (!prompt.trim()) {
                alert('يرجى كتابة طلبك أولاً');
                return;
            }
            
            showLoading();
            
            // محاكاة استدعاء جميع النماذج
            setTimeout(() => {
                const platforms = ['google_gemini', 'openrouter', 'anthropic', 'deepseek', 'mistral', 'huggingface'];
                const responses = platforms.map(platform => generateMockResponse(platform, prompt));
                displayResults(responses);
            }, 4000);
        }
        
        function generateMockResponse(platform, prompt) {
            const responses = {
                google_gemini: {
                    platform: "🤖 Google Gemini",
                    response: "استجابة ذكية ومفصلة من Google Gemini مع تحليل عميق للطلب المقدم.",
                    tokens: 45,
                    cost: "$0.002"
                },
                openrouter: {
                    platform: "🚀 OpenRouter",
                    response: "استجابة متقدمة من OpenRouter باستخدام أفضل النماذج المتاحة.",
                    tokens: 52,
                    cost: "$0.005"
                },
                anthropic: {
                    platform: "🧠 Anthropic Claude",
                    response: "استجابة مدروسة ومتوازنة من Claude مع مراعاة الأخلاقيات والدقة.",
                    tokens: 48,
                    cost: "$0.008"
                },
                deepseek: {
                    platform: "🔍 DeepSeek",
                    response: "تحليل عميق ومفصل من DeepSeek مع حلول مبتكرة ومدروسة.",
                    tokens: 41,
                    cost: "$0.003"
                },
                mistral: {
                    platform: "🌪️ Mistral AI",
                    response: "استجابة سريعة وذكية من Mistral مع فهم ممتاز للسياق.",
                    tokens: 38,
                    cost: "$0.004"
                },
                huggingface: {
                    platform: "🤗 Hugging Face",
                    response: "استجابة من نماذج مفتوحة المصدر عبر Hugging Face.",
                    tokens: 35,
                    cost: "$0.001"
                }
            };
            
            return {
                ...responses[platform],
                prompt: prompt.substring(0, 100) + (prompt.length > 100 ? "..." : ""),
                timestamp: new Date().toLocaleString('ar-SA')
            };
        }
        
        function showLoading() {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');
            
            resultsSection.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading">🤖 جاري استدعاء النماذج... يرجى الانتظار</div>';
        }
        
        function displayResults(responses) {
            const resultsContainer = document.getElementById('resultsContainer');
            
            let html = '';
            responses.forEach(response => {
                html += `
                    <div class="response-card">
                        <h4>${response.platform}</h4>
                        <p><strong>الطلب:</strong> ${response.prompt}</p>
                        <p><strong>الاستجابة:</strong> ${response.response}</p>
                        <div style="margin-top: 10px; font-size: 14px; color: #666;">
                            <span>⏱️ ${response.timestamp}</span> |
                            <span>🔤 ${response.tokens} رمز</span> |
                            <span>💰 ${response.cost}</span>
                        </div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = html;
        }
        
        // إضافة مثال تلقائي
        document.getElementById('promptInput').value = 'اكتب لي قصة قصيرة عن مستقبل الذكاء الاصطناعي';
    </script>
</body>
</html>