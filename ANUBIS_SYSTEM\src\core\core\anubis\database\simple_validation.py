#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏆 التحقق المبسط من قاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Simple Database Validation
"""

import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict

import mysql.connector
from mysql.connector import Error

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class SimpleValidator:
    """🔍 فئة التحقق المبسط من قاعدة البيانات"""

    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة التحقق"""
        self.config_path = config_path
        self.config = self._load_config()
        self.results = []

    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config["database"]["mysql"]
        except Exception as e:
            print(f"❌ خطأ في تحميل الإعدادات: {e}")
            return {}

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config["host"],
                port=self.config["port"],
                user=self.config["user"],
                password=self.config["password"],
                database=self.config["database"],
                charset=self.config.get("charset", "utf8mb4"),
                autocommit=True,
            )
            return connection
        except Error as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return None

    def test_connection(self) -> bool:
        """اختبار الاتصال"""
        print("🔄 اختبار الاتصال...")
        connection = self.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()
                print("✅ الاتصال نجح")
                return True
            except Error as e:
                print(f"❌ فشل الاتصال: {e}")
                return False
        return False

    def check_database_info(self) -> Dict[str, Any]:
        """فحص معلومات قاعدة البيانات"""
        print("🔄 فحص معلومات قاعدة البيانات...")
        connection = self.get_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)

            # معلومات الخادم
            cursor.execute("SELECT VERSION() as version")
            server_info = cursor.fetchone()

            # معلومات قاعدة البيانات
            cursor.execute("SELECT DATABASE() as current_db")
            db_info = cursor.fetchone()

            # حجم قاعدة البيانات
            cursor.execute(
                """
            SELECT
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = %s
            """,
                (self.config["database"],),
            )
            size_info = cursor.fetchone()

            # عدد الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            cursor.close()
            connection.close()

            info = {
                "mysql_version": server_info["version"],
                "database_name": db_info["current_db"],
                "size_mb": float(size_info["size_mb"]) if size_info["size_mb"] else 0,
                "tables_count": len(tables),
            }

            print(f"✅ MySQL {info['mysql_version']}, قاعدة البيانات: {info['database_name']}")
            print(f"   📊 الحجم: {info['size_mb']}MB, الجداول: {info['tables_count']}")

            return info

        except Error as e:
            print(f"❌ خطأ في فحص معلومات قاعدة البيانات: {e}")
            return {}

    def check_tables(self) -> Dict[str, Any]:
        """فحص الجداول"""
        print("🔄 فحص الجداول...")
        connection = self.get_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)

            # الجداول المطلوبة
            required_tables = [
                "projects",
                "analyses",
                "errors",
                "reports",
                "plugins",
                "activities",
            ]

            # فحص وجود الجداول
            cursor.execute("SHOW TABLES")
            existing_tables = [list(table.values())[0] for table in cursor.fetchall()]

            missing_tables = set(required_tables) - set(existing_tables)

            if missing_tables:
                print(f"❌ جداول مفقودة: {', '.join(missing_tables)}")
                return {"status": "fail", "missing_tables": list(missing_tables)}

            print(f"✅ جميع الجداول موجودة ({len(required_tables)})")

            cursor.close()
            connection.close()

            return {"status": "pass", "tables_count": len(existing_tables)}

        except Error as e:
            print(f"❌ خطأ في فحص الجداول: {e}")
            return {"status": "error", "error": str(e)}

    def check_data(self) -> Dict[str, Any]:
        """فحص البيانات"""
        print("🔄 فحص البيانات...")
        connection = self.get_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)

            # عد البيانات في كل جدول
            tables_data = {}
            tables = [
                "projects",
                "analyses",
                "errors",
                "reports",
                "plugins",
                "activities",
            ]

            for table in tables:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = cursor.fetchone()["count"]
                tables_data[table] = count

            # فحص متوسط النقاط
            cursor.execute("SELECT AVG(score) as avg_score FROM analyses WHERE score IS NOT NULL")
            avg_score_result = cursor.fetchone()
            avg_score = float(avg_score_result["avg_score"]) if avg_score_result["avg_score"] else 0

            cursor.close()
            connection.close()

            total_records = sum(tables_data.values())

            print(f"✅ إجمالي السجلات: {total_records}")
            for table, count in tables_data.items():
                print(f"   📋 {table}: {count}")
            if avg_score > 0:
                print(f"   📈 متوسط النقاط: {avg_score:.1f}")

            return {
                "status": "pass",
                "tables_data": tables_data,
                "total_records": total_records,
                "avg_score": avg_score,
            }

        except Error as e:
            print(f"❌ خطأ في فحص البيانات: {e}")
            return {"status": "error", "error": str(e)}

    def check_performance(self) -> Dict[str, Any]:
        """فحص الأداء"""
        print("🔄 فحص الأداء...")
        connection = self.get_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)

            # اختبار سرعة الاستعلامات
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM projects")
            cursor.fetchone()
            simple_query_time = time.time() - start_time

            start_time = time.time()
            cursor.execute(
                """
            SELECT p.name, COUNT(a.id) as analyses_count
            FROM projects p
            LEFT JOIN analyses a ON p.id = a.project_id
            GROUP BY p.id, p.name
            LIMIT 10
            """
            )
            cursor.fetchall()
            complex_query_time = time.time() - start_time

            cursor.close()
            connection.close()

            avg_time = (simple_query_time + complex_query_time) / 2

            if avg_time < 0.1:
                status = "ممتاز"
                icon = "✅"
            elif avg_time < 0.5:
                status = "جيد"
                icon = "⚠️"
            else:
                status = "بطيء"
                icon = "❌"

            print(f"{icon} الأداء {status} - متوسط وقت الاستعلام: {avg_time:.4f}s")

            return {
                "status": status.lower(),
                "avg_query_time": avg_time,
                "simple_query_time": simple_query_time,
                "complex_query_time": complex_query_time,
            }

        except Error as e:
            print(f"❌ خطأ في فحص الأداء: {e}")
            return {"status": "error", "error": str(e)}

    def run_validation(self) -> Dict[str, Any]:
        """تشغيل جميع عمليات التحقق"""
        print("🏆 بدء التحقق من قاعدة بيانات نظام أنوبيس")
        print("=" * 50)

        start_time = time.time()

        # تشغيل الاختبارات
        results = {}

        # اختبار الاتصال
        connection_ok = self.test_connection()
        results["connection"] = connection_ok

        if connection_ok:
            # فحص معلومات قاعدة البيانات
            results["database_info"] = self.check_database_info()

            # فحص الجداول
            results["tables"] = self.check_tables()

            # فحص البيانات
            results["data"] = self.check_data()

            # فحص الأداء
            results["performance"] = self.check_performance()

        total_time = time.time() - start_time

        # تلخيص النتائج
        print("\n" + "=" * 50)
        print("📊 ملخص التحقق")
        print("=" * 50)

        if connection_ok:
            print("🎉 قاعدة البيانات تعمل بشكل صحيح!")
        else:
            print("❌ مشكلة في الاتصال بقاعدة البيانات")

        print(f"⏱️ وقت التحقق: {total_time:.3f} ثانية")

        # إنشاء التقرير
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_time": total_time,
            "results": results,
            "database_ready": connection_ok and results.get("tables", {}).get("status") == "pass",
        }

        return report


def main():
    """الدالة الرئيسية"""
    validator = SimpleValidator()
    report = validator.run_validation()

    # حفظ التقرير
    report_file = (
        f"database/simple_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    os.makedirs(os.path.dirname(report_file), exist_ok=True)

    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📄 تم حفظ التقرير في: {report_file}")
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير: {e}")

    # تحديد كود الخروج
    exit_code = 0 if report.get("database_ready", False) else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
