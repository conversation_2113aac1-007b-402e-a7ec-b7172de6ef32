#!/usr/bin/env python3
"""
🧪 اختبار منطق الأعمال لتطبيق إدارة ورشة تصنيع الذهب والماس
Test Jewelry Workshop Business Logic

اختبارات شاملة للتأكد من صحة جميع الحسابات والعمليات
"""

import sys
from decimal import Decimal

from jewelry_workshop_business_logic import (
    CurrencyManager,
    DiamondInventoryManager,
    ExpenseManager,
    GoldInventoryManager,
    PricingCalculator,
)


def test_currency_manager():
    """اختبار مدير العملات"""
    print("🧪 اختبار مدير العملات...")

    currency_manager = CurrencyManager()

    # اختبار تحديث سعر الصرف
    currency_manager.set_daily_exchange_rate(30.75)
    assert currency_manager.get_exchange_rate() == Decimal("30.75")

    # اختبار التحويل من دولار إلى جنيه
    usd_amount = 100.0
    egp_amount = currency_manager.convert_usd_to_egp(usd_amount)
    expected_egp = Decimal("3075.0")
    assert egp_amount == expected_egp, f"متوقع {expected_egp}, حصلت على {egp_amount}"

    # اختبار التحويل من جنيه إلى دولار
    egp_amount = 3075.0
    usd_amount = currency_manager.convert_egp_to_usd(egp_amount)
    expected_usd = Decimal("100.0")
    assert usd_amount == expected_usd, f"متوقع {expected_usd}, حصلت على {usd_amount}"

    print("✅ نجح اختبار مدير العملات")


def test_gold_inventory():
    """اختبار مدير مخزون الذهب"""
    print("🧪 اختبار مدير مخزون الذهب...")

    gold_manager = GoldInventoryManager()

    # اختبار إضافة ذهب للمخزون
    assert gold_manager.add_gold_stock(100.0, 18) == True
    assert gold_manager.add_gold_stock(50.0, 21) == True

    # اختبار رصيد الذهب
    balance = gold_manager.get_gold_balance()
    assert balance[18] == Decimal("100.0")
    assert balance[21] == Decimal("50.0")

    # اختبار خصم ذهب من المخزون
    assert gold_manager.remove_gold_stock(20.0, 18) == True
    assert gold_manager.get_gold_balance()[18] == Decimal("80.0")

    # اختبار خصم كمية أكبر من المتاح
    assert gold_manager.remove_gold_stock(100.0, 18) == False

    # اختبار حساب قيمة الذهب
    value = gold_manager.calculate_gold_value(10.0, 18)
    expected_value = Decimal("350.0")  # 10 * 35
    assert value == expected_value, f"متوقع {expected_value}, حصلت على {value}"

    print("✅ نجح اختبار مدير مخزون الذهب")


def test_diamond_inventory():
    """اختبار مدير مخزون الماس"""
    print("🧪 اختبار مدير مخزون الماس...")

    diamond_manager = DiamondInventoryManager()

    # اختبار إضافة ماس للمخزون
    assert (
        diamond_manager.add_diamond_stock(
            weight=2.5,
            count=25,
            size="medium",
            diamond_type="round",
            source="ours",
            price_per_carat=700.0,
        )
        == True
    )

    # اختبار خصم ماس من المخزون
    assert (
        diamond_manager.remove_diamond_stock(
            weight=0.5, count=5, size="medium", diamond_type="round", source="ours"
        )
        == True
    )

    # اختبار الرصيد
    balance = diamond_manager.get_diamond_balance()
    remaining_weight = balance["round"]["medium"]["ours"]["weight"]
    remaining_count = balance["round"]["medium"]["ours"]["count"]

    assert remaining_weight == Decimal("2.0"), f"متوقع 2.0, حصلت على {remaining_weight}"
    assert remaining_count == 20, f"متوقع 20, حصلت على {remaining_count}"

    # اختبار حساب قيمة الماس
    value = diamond_manager.calculate_diamond_value(1.0, 700.0)
    expected_value = Decimal("700.0")
    assert value == expected_value, f"متوقع {expected_value}, حصلت على {value}"

    print("✅ نجح اختبار مدير مخزون الماس")


def test_pricing_calculator():
    """اختبار حاسبة التسعير"""
    print("🧪 اختبار حاسبة التسعير...")

    currency_manager = CurrencyManager()
    pricing_calculator = PricingCalculator(currency_manager)

    # اختبار حساب تكلفة تركيب الأحجار
    stone_cost = pricing_calculator.calculate_stone_setting_cost(50)
    expected_cost = Decimal("500.0")  # 50 * 10
    assert stone_cost == expected_cost, f"متوقع {expected_cost}, حصلت على {stone_cost}"

    # اختبار حساب المصنعية
    manufacturing_cost = pricing_calculator.calculate_manufacturing_cost(5.0)
    expected_cost = Decimal("50.0")  # 5 * 10
    assert (
        manufacturing_cost == expected_cost
    ), f"متوقع {expected_cost}, حصلت على {manufacturing_cost}"

    # اختبار حساب تكلفة الطلاء
    plating_cost = pricing_calculator.calculate_plating_cost(1)
    expected_cost = Decimal("200.0")
    assert plating_cost == expected_cost, f"متوقع {expected_cost}, حصلت على {plating_cost}"

    # اختبار حساب تكلفة الدمغة
    stamping_cost = pricing_calculator.calculate_stamping_cost(1)
    expected_cost = Decimal("20.0")
    assert stamping_cost == expected_cost, f"متوقع {expected_cost}, حصلت على {stamping_cost}"

    print("✅ نجح اختبار حاسبة التسعير")


def test_complete_piece_calculation():
    """اختبار حساب تكلفة قطعة كاملة"""
    print("🧪 اختبار حساب تكلفة قطعة كاملة...")

    currency_manager = CurrencyManager()
    pricing_calculator = PricingCalculator(currency_manager)

    # بيانات الخاتم المثال
    piece_data = {
        "gold_weight": 5.0,
        "gold_karat": 18,
        "diamond_weight": 0.5,
        "diamond_count": 50,
        "diamond_source": "ours",
        "services": {
            "plating": True,
            "stamping": True,
            "3d_design": "medium",
            "casting_weight": 0.0,
        },
    }

    costs = pricing_calculator.calculate_piece_total_cost(piece_data)

    # التحقق من النتائج
    expected_gold_value = Decimal("175.0")  # 5 * 35 (سعر الذهب عيار 18)
    expected_diamond_value = Decimal("350.0")  # 0.5 * 700
    expected_manufacturing = Decimal("50.0")  # 5 * 10
    expected_stone_setting = Decimal("500.0")  # 50 * 10
    expected_services = Decimal("445.0")  # 200 (طلاء) + 20 (دمغة) + 225 (تصميم متوسط)

    assert costs["gold_value_usd"] == expected_gold_value
    assert costs["diamond_value_usd"] == expected_diamond_value
    assert costs["manufacturing_usd"] == expected_manufacturing
    assert costs["stone_setting_egp"] == expected_stone_setting
    assert costs["services_egp"] == expected_services

    expected_total_usd = expected_gold_value + expected_diamond_value + expected_manufacturing
    expected_total_egp = expected_stone_setting + expected_services

    assert costs["total_usd"] == expected_total_usd
    assert costs["total_egp"] == expected_total_egp

    print("✅ نجح اختبار حساب تكلفة قطعة كاملة")
    print(f"   الإجمالي: {costs['total_usd']:.2f}$ + {costs['total_egp']:.2f} ج.م")


def test_expense_manager():
    """اختبار مدير المصروفات"""
    print("🧪 اختبار مدير المصروفات...")

    currency_manager = CurrencyManager()
    expense_manager = ExpenseManager(currency_manager)

    # اختبار حساب الرواتب الشهرية
    monthly_salaries = expense_manager.calculate_monthly_salaries_usd()
    expected_salaries = Decimal("1200.0")  # 500 + 700
    assert (
        monthly_salaries == expected_salaries
    ), f"متوقع {expected_salaries}, حصلت على {monthly_salaries}"

    # اختبار حساب العمل بالساعة
    hourly_cost = expense_manager.calculate_hourly_work_cost("polishing_worker", 8.0)
    expected_cost = Decimal("800.0")  # 100 * 8
    assert hourly_cost == expected_cost, f"متوقع {expected_cost}, حصلت على {hourly_cost}"

    # اختبار المصروفات الثابتة
    fixed_expenses = expense_manager.calculate_monthly_fixed_expenses()
    expected_expenses = Decimal("12000.0")  # 11000 + 1000
    assert (
        fixed_expenses == expected_expenses
    ), f"متوقع {expected_expenses}, حصلت على {fixed_expenses}"

    print("✅ نجح اختبار مدير المصروفات")


def run_comprehensive_example():
    """تشغيل مثال شامل"""
    print("\n" + "=" * 60)
    print("🎯 مثال شامل: حساب تكلفة طلب 10 خواتم")
    print("=" * 60)

    currency_manager = CurrencyManager()
    currency_manager.set_daily_exchange_rate(30.75)

    pricing_calculator = PricingCalculator(currency_manager)

    # بيانات الطلب: 10 خواتم
    order_data = {
        "pieces_count": 10,
        "piece_data": {
            "gold_weight": 5.0,  # لكل قطعة
            "gold_karat": 18,
            "diamond_weight": 0.5,  # لكل قطعة
            "diamond_count": 50,  # لكل قطعة
            "diamond_source": "ours",
            "services": {
                "plating": True,
                "stamping": True,
                "3d_design": "medium",
                "casting_weight": 0.0,
            },
        },
    }

    # حساب تكلفة القطعة الواحدة
    piece_costs = pricing_calculator.calculate_piece_total_cost(order_data["piece_data"])

    # حساب إجمالي الطلب
    total_pieces = order_data["pieces_count"]
    total_usd = piece_costs["total_usd"] * total_pieces
    total_egp = piece_costs["total_egp"] * total_pieces

    # عرض النتائج
    print(f"📊 تفاصيل الطلب:")
    print(f"   عدد القطع: {total_pieces}")
    print(f"   الوزن الإجمالي للذهب: {order_data['piece_data']['gold_weight'] * total_pieces} جرام")
    print(
        f"   الوزن الإجمالي للماس: {order_data['piece_data']['diamond_weight'] * total_pieces} قيراط"
    )

    print(f"\n💰 التكلفة لكل قطعة:")
    print(f"   قيمة الذهب: {piece_costs['gold_value_usd']:.2f}$")
    print(f"   قيمة الماس: {piece_costs['diamond_value_usd']:.2f}$")
    print(f"   المصنعية: {piece_costs['manufacturing_usd']:.2f}$")
    print(f"   تركيب الأحجار: {piece_costs['stone_setting_egp']:.2f} ج.م")
    print(f"   الخدمات الإضافية: {piece_costs['services_egp']:.2f} ج.م")

    print(f"\n🎯 الإجمالي النهائي:")
    print(f"   {total_usd:.2f}$ + {total_egp:.2f} ج.م")

    # تحويل للعرض بعملة واحدة
    total_egp_equivalent = total_egp + currency_manager.convert_usd_to_egp(float(total_usd))
    total_usd_equivalent = total_usd + currency_manager.convert_egp_to_usd(float(total_egp))

    print(f"   المعادل بالجنيه المصري: {total_egp_equivalent:.2f} ج.م")
    print(f"   المعادل بالدولار: {total_usd_equivalent:.2f}$")


def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 ═══════════════════════════════════════════════════════════════")
    print("   اختبار منطق الأعمال - ورشة تصنيع الذهب والماس")
    print("   Jewelry Workshop Business Logic Tests")
    print("═══════════════════════════════════════════════════════════════ 🧪")

    try:
        # تشغيل جميع الاختبارات
        test_currency_manager()
        test_gold_inventory()
        test_diamond_inventory()
        test_pricing_calculator()
        test_complete_piece_calculation()
        test_expense_manager()

        print("\n🎉 جميع الاختبارات نجحت!")

        # تشغيل المثال الشامل
        run_comprehensive_example()

        print("\n✅ النظام جاهز للاستخدام!")

    except AssertionError as e:
        print(f"\n❌ فشل في الاختبار: {e}")
        return 1
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
