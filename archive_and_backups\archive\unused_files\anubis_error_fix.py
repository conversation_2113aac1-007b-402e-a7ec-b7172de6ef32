#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام أنوبيس لتصحيح الأخطاء الذكي
Anubis Smart Error Correction System

استخدام وكلاء أنوبيس الذكيين لتصحيح أخطاء ملف final_validation.py
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import json

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

try:
    from agents.error_detector_agent import ErrorDetectorAgent
    from agents.project_analyzer_agent import ProjectAnalyzerAgent
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"❌ خطأ في استيراد وكلاء أنوبيس: {e}")
    sys.exit(1)


class AnubisErrorFixer:
    """🏺 نظام أنوبيس لتصحيح الأخطاء الذكي"""
    
    def __init__(self, target_file: str = "database/final_validation.py"):
        """تهيئة نظام تصحيح الأخطاء"""
        self.target_file = Path(target_file)
        self.project_root = Path(".")
        self.config_manager = ConfigManager()
        self.results = {}
        
        print("🏺 مرحباً بك في نظام أنوبيس لتصحيح الأخطاء الذكي")
        print("=" * 60)
        
    def initialize_agents(self):
        """تهيئة وكلاء أنوبيس"""
        print("🤖 تهيئة وكلاء أنوبيس الذكيين...")
        
        # تهيئة وكيل كشف الأخطاء
        try:
            self.error_detector = ErrorDetectorAgent(
                project_path=str(self.project_root),
                config=self.config_manager.get_agent_config('error_detector_agent')
            )
            print("✅ تم تهيئة وكيل كشف الأخطاء")
        except Exception as e:
            print(f"❌ خطأ في تهيئة وكيل كشف الأخطاء: {e}")
            return False
        
        # تهيئة وكيل تحليل المشاريع
        try:
            self.project_analyzer = ProjectAnalyzerAgent(
                project_path=str(self.project_root),
                config=self.config_manager.get_agent_config('project_analyzer_agent')
            )
            print("✅ تم تهيئة وكيل تحليل المشاريع")
        except Exception as e:
            print(f"❌ خطأ في تهيئة وكيل تحليل المشاريع: {e}")
            return False
        
        return True
    
    def analyze_target_file(self):
        """تحليل الملف المستهدف"""
        print(f"\n🔍 تحليل الملف: {self.target_file}")
        print("-" * 40)
        
        if not self.target_file.exists():
            print(f"❌ الملف غير موجود: {self.target_file}")
            return False
        
        # قراءة محتوى الملف
        try:
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 حجم الملف: {len(content)} حرف")
            print(f"📄 عدد الأسطر: {len(content.splitlines())}")
            
            return True
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            return False
    
    def run_error_detection(self):
        """تشغيل وكيل كشف الأخطاء"""
        print("\n🔍 تشغيل وكيل كشف الأخطاء...")
        print("-" * 40)
        
        try:
            # تشغيل تحليل كشف الأخطاء
            error_results = self.error_detector.run()
            
            if error_results.get('success', False):
                self.results['error_detection'] = error_results
                self._display_error_results(error_results)
                return True
            else:
                print(f"❌ فشل في تحليل الأخطاء: {error_results.get('error', 'خطأ غير معروف')}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل وكيل كشف الأخطاء: {e}")
            return False
    
    def run_project_analysis(self):
        """تشغيل وكيل تحليل المشاريع"""
        print("\n📊 تشغيل وكيل تحليل المشاريع...")
        print("-" * 40)
        
        try:
            # تشغيل تحليل المشروع
            analysis_results = self.project_analyzer.run()
            
            if analysis_results.get('success', False):
                self.results['project_analysis'] = analysis_results
                self._display_analysis_results(analysis_results)
                return True
            else:
                print(f"❌ فشل في تحليل المشروع: {analysis_results.get('error', 'خطأ غير معروف')}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل وكيل تحليل المشاريع: {e}")
            return False
    
    def _display_error_results(self, results):
        """عرض نتائج كشف الأخطاء"""
        error_analysis = results.get('error_analysis', {})
        
        print("🔍 نتائج كشف الأخطاء:")
        print(f"   📁 الملفات المحللة: {error_analysis.get('total_files_analyzed', 0)}")
        print(f"   ⚠️ الملفات بها أخطاء: {error_analysis.get('files_with_errors', 0)}")
        
        # عرض أنواع الأخطاء
        error_types = ['syntax_errors', 'import_errors', 'style_issues', 'logic_warnings', 'security_issues', 'performance_issues']
        
        for error_type in error_types:
            errors = error_analysis.get(error_type, [])
            if errors:
                print(f"   🔴 {error_type}: {len(errors)} خطأ")
                
                # عرض أول 3 أخطاء
                for i, error in enumerate(errors[:3]):
                    file_name = error.get('file', 'غير محدد')
                    line = error.get('line', 0)
                    message = error.get('error', 'رسالة غير محددة')
                    severity = error.get('severity', 'medium')
                    
                    severity_icon = "🔴" if severity == "high" else "🟡" if severity == "medium" else "🟢"
                    print(f"      {severity_icon} {file_name}:{line} - {message}")
                
                if len(errors) > 3:
                    print(f"      ... و {len(errors) - 3} أخطاء أخرى")
        
        # عرض جودة الكود
        quality_analysis = results.get('quality_analysis', {})
        if quality_analysis:
            print("\n📊 تحليل جودة الكود:")
            print(f"   🧮 التعقيد: {quality_analysis.get('complexity_score', 0):.1f}/100")
            print(f"   🔧 القابلية للصيانة: {quality_analysis.get('maintainability_score', 0):.1f}/100")
            print(f"   📖 القابلية للقراءة: {quality_analysis.get('readability_score', 0):.1f}/100")
            print(f"   📚 التوثيق: {quality_analysis.get('documentation_score', 0):.1f}/100")
    
    def _display_analysis_results(self, results):
        """عرض نتائج تحليل المشروع"""
        print("📊 نتائج تحليل المشروع:")
        
        # تحليل حجم المشروع
        size_analysis = results.get('project_size_analysis', {})
        if size_analysis:
            print(f"   📏 إجمالي الأسطر: {size_analysis.get('total_lines', 0):,}")
            print(f"   📄 إجمالي الملفات: {size_analysis.get('total_files', 0)}")
            print(f"   🐍 ملفات Python: {size_analysis.get('python_files', 0)}")
        
        # تحليل التعقيد
        complexity_analysis = results.get('complexity_analysis', {})
        if complexity_analysis:
            print(f"   🧮 متوسط التعقيد: {complexity_analysis.get('average_complexity', 0):.1f}")
            print(f"   📊 نقاط التعقيد: {complexity_analysis.get('complexity_score', 0):.1f}/100")
        
        # تحليل الأداء
        performance_analysis = results.get('performance_analysis', {})
        if performance_analysis:
            print(f"   ⚡ نقاط الأداء: {performance_analysis.get('performance_score', 0):.1f}/100")
        
        # تحليل الأمان
        security_analysis = results.get('security_analysis', {})
        if security_analysis:
            print(f"   🔒 نقاط الأمان: {security_analysis.get('security_score', 0):.1f}/100")
    
    def generate_recommendations(self):
        """إنتاج توصيات التحسين"""
        print("\n💡 توصيات أنوبيس للتحسين:")
        print("-" * 40)
        
        recommendations = []
        
        # توصيات من كشف الأخطاء
        if 'error_detection' in self.results:
            error_recs = self.results['error_detection'].get('recommendations', [])
            recommendations.extend(error_recs)
        
        # توصيات من تحليل المشروع
        if 'project_analysis' in self.results:
            project_recs = self.results['project_analysis'].get('recommendations', [])
            recommendations.extend(project_recs)
        
        # توصيات مخصصة لملف final_validation.py
        custom_recommendations = self._generate_custom_recommendations()
        recommendations.extend(custom_recommendations)
        
        # عرض التوصيات
        if recommendations:
            for i, rec in enumerate(recommendations[:10], 1):
                priority = rec.get('priority', 'medium')
                priority_icon = "🔴" if priority == "high" else "🟡" if priority == "medium" else "🟢"
                
                print(f"{i:2d}. {priority_icon} {rec.get('title', 'توصية')}")
                print(f"     📝 {rec.get('description', 'لا يوجد وصف')}")
                
                if rec.get('action'):
                    print(f"     🔧 الإجراء: {rec['action']}")
                print()
        else:
            print("✅ لا توجد توصيات - الملف في حالة ممتازة!")
    
    def _generate_custom_recommendations(self):
        """إنتاج توصيات مخصصة لملف final_validation.py"""
        recommendations = []
        
        # فحص محتوى الملف
        try:
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # توصيات مخصصة
            if 'import mysql.connector' in content and 'from mysql.connector import Error' in content:
                recommendations.append({
                    'title': 'تحسين ترتيب الاستيرادات',
                    'description': 'ترتيب الاستيرادات حسب معايير PEP 8',
                    'priority': 'medium',
                    'action': 'إعادة ترتيب الاستيرادات: standard library أولاً، ثم third-party'
                })
            
            if 'Exception' in content and content.count('Exception') > 2:
                recommendations.append({
                    'title': 'تحسين معالجة الأخطاء',
                    'description': 'استخدام أنواع أخطاء محددة بدلاً من Exception العامة',
                    'priority': 'high',
                    'action': 'استبدال Exception بأنواع أخطاء محددة مثل ValueError, ConnectionError'
                })
            
            if content.count('if') > 10:
                recommendations.append({
                    'title': 'تبسيط الشروط المعقدة',
                    'description': 'تبسيط الشروط المتداخلة لتحسين القابلية للقراءة',
                    'priority': 'medium',
                    'action': 'تقسيم الشروط المعقدة إلى دوال منفصلة'
                })
            
            if 'print(' in content:
                recommendations.append({
                    'title': 'تحسين نظام التسجيل',
                    'description': 'استخدام نظام logging بدلاً من print للرسائل',
                    'priority': 'low',
                    'action': 'استبدال print بـ logging.info/warning/error'
                })
                
        except Exception as e:
            print(f"⚠️ خطأ في قراءة الملف للتوصيات المخصصة: {e}")
        
        return recommendations
    
    def save_results(self):
        """حفظ نتائج التحليل"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"workspace/reports/anubis_error_analysis_{timestamp}.json"
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        # إعداد التقرير النهائي
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'target_file': str(self.target_file),
            'anubis_version': '1.0.0',
            'analysis_results': self.results,
            'summary': {
                'total_errors': 0,
                'total_warnings': 0,
                'quality_score': 0,
                'recommendations_count': 0
            }
        }
        
        # حساب الملخص
        if 'error_detection' in self.results:
            error_analysis = self.results['error_detection'].get('error_analysis', {})
            final_report['summary']['total_errors'] = sum(
                len(error_analysis.get(error_type, [])) 
                for error_type in ['syntax_errors', 'import_errors', 'security_issues']
            )
            final_report['summary']['total_warnings'] = sum(
                len(error_analysis.get(error_type, [])) 
                for error_type in ['style_issues', 'logic_warnings', 'performance_issues']
            )
            
            quality_analysis = self.results['error_detection'].get('quality_analysis', {})
            if quality_analysis:
                scores = [
                    quality_analysis.get('complexity_score', 0),
                    quality_analysis.get('maintainability_score', 0),
                    quality_analysis.get('readability_score', 0),
                    quality_analysis.get('documentation_score', 0)
                ]
                final_report['summary']['quality_score'] = sum(scores) / len(scores) if scores else 0
        
        # حفظ التقرير
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(final_report, f, ensure_ascii=False, indent=2)
            
            print(f"\n📄 تم حفظ تقرير أنوبيس في: {results_file}")
            return results_file
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None
    
    def run_complete_analysis(self):
        """تشغيل التحليل الشامل"""
        print("🏺 بدء التحليل الشامل بنظام أنوبيس")
        print("=" * 60)
        
        # تهيئة الوكلاء
        if not self.initialize_agents():
            print("❌ فشل في تهيئة وكلاء أنوبيس")
            return False
        
        # تحليل الملف المستهدف
        if not self.analyze_target_file():
            print("❌ فشل في تحليل الملف المستهدف")
            return False
        
        # تشغيل كشف الأخطاء
        error_success = self.run_error_detection()
        
        # تشغيل تحليل المشروع
        analysis_success = self.run_project_analysis()
        
        # إنتاج التوصيات
        self.generate_recommendations()
        
        # حفظ النتائج
        report_file = self.save_results()
        
        # الملخص النهائي
        print("\n🏆 ملخص تحليل أنوبيس:")
        print("=" * 60)
        
        if error_success:
            print("✅ تم تشغيل وكيل كشف الأخطاء بنجاح")
        else:
            print("❌ فشل في تشغيل وكيل كشف الأخطاء")
        
        if analysis_success:
            print("✅ تم تشغيل وكيل تحليل المشاريع بنجاح")
        else:
            print("❌ فشل في تشغيل وكيل تحليل المشاريع")
        
        if report_file:
            print(f"✅ تم حفظ التقرير الشامل")
        
        success_rate = sum([error_success, analysis_success]) / 2 * 100
        print(f"\n🎯 معدل نجاح التحليل: {success_rate:.1f}%")
        
        if success_rate >= 50:
            print("🎉 تم تحليل الملف بنجاح باستخدام نظام أنوبيس!")
            return True
        else:
            print("⚠️ تم التحليل مع بعض المشاكل")
            return False


def main():
    """الدالة الرئيسية"""
    # إنشاء نظام أنوبيس لتصحيح الأخطاء
    anubis_fixer = AnubisErrorFixer("database/final_validation.py")
    
    # تشغيل التحليل الشامل
    success = anubis_fixer.run_complete_analysis()
    
    # تحديد كود الخروج
    exit_code = 0 if success else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
