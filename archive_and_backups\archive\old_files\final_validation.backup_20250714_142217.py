#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏆 التحقق النهائي من قاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Final Database Validation

التحقق الشامل من جميع مكونات قاعدة البيانات وجاهزيتها للإنتاج
"""

import json
import os
import sys
import time
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any

import mysql.connector
from mysql.connector import Error

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# ثوابت للرسائل المتكررة
DB_INFO_CHECK = "معلومات قاعدة البيانات"
TABLE_STRUCTURE_CHECK = "هيكل الجداول"
FOREIGN_KEYS_CHECK = "المفاتيح الخارجية"
DATA_QUALITY_CHECK = "جودة البيانات"
PERFORMANCE_CHECK = "الأداء"

class FinalValidator:
    """🔍 فئة التحقق النهائي من قاعدة البيانات"""

    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة التحقق النهائي"""
        self.config_path = config_path
        self.config = self._load_config()
        self.validation_results = []

    def _convert_to_json_serializable(self, obj):
        """تحويل البيانات إلى تنسيق قابل للتحويل إلى JSON"""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, dict):
            return {
                key: self._convert_to_json_serializable(value)
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [
                self._convert_to_json_serializable(item)
                for item in obj
            ]
        else:
            return obj

    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config['database']['mysql']
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"خطأ في تحميل الإعدادات: {e}")
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config.get('charset', 'utf8mb4'),
                autocommit=True
            )
            return connection
        except Error as e:
            raise ConnectionError(f"خطأ في الاتصال: {e}")
    
    def log_validation(self, check_name: str, status: str, details: str = "", data: Any = None):
        """تسجيل نتيجة التحقق"""
        result = {
            'check_name': check_name,
            'status': status,
            'details': details,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        self.validation_results.append(result)
        
        if status == "PASS":
            status_icon = "✅"
        elif status == "FAIL":
            status_icon = "❌"
        else:
            status_icon = "⚠️"
        print(f"{status_icon} {check_name}")
        if details:
            print(f"   📝 {details}")
    
    def validate_database_info(self) -> bool:
        """التحقق من معلومات قاعدة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # معلومات الخادم
            cursor.execute("SELECT VERSION() as version")
            server_info = cursor.fetchone()
            
            # معلومات قاعدة البيانات
            cursor.execute("SELECT DATABASE() as current_db")
            db_info = cursor.fetchone()
            
            # حجم قاعدة البيانات
            cursor.execute("""
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = %s
            """, (self.config['database'],))
            size_info = cursor.fetchone()
            
            # عدد الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            details = f"MySQL {server_info['version']}, قاعدة البيانات: {db_info['current_db']}, الحجم: {size_info['size_mb']}MB, الجداول: {len(tables)}"
            
            self.log_validation("معلومات قاعدة البيانات", "PASS", details, {
                'mysql_version': server_info['version'],
                'database_name': db_info['current_db'],
                'size_mb': size_info['size_mb'],
                'tables_count': len(tables)
            })
            
            return True
            
        except Exception as e:
            self.log_validation("معلومات قاعدة البيانات", "FAIL", f"خطأ: {e}")
            return False
    
    def validate_table_structure(self) -> bool:
        """التحقق من هيكل الجداول"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # التحقق من الجداول المطلوبة
            required_tables = {
                'projects': ['id', 'name', 'path', 'type', 'description', 'created_at', 'updated_at'],
                'analyses': ['id', 'project_id', 'agent_type', 'analysis_data', 'results', 'score', 'created_at'],
                'errors': ['id', 'project_id', 'file_path', 'line_number', 'error_type', 'severity', 'message', 'created_at'],
                'reports': ['id', 'project_id', 'report_type', 'title', 'content', 'file_path', 'created_at'],
                'plugins': ['id', 'name', 'version', 'description', 'config', 'enabled', 'created_at', 'updated_at'],
                'activities': ['id', 'project_id', 'activity_type', 'description', 'metadata', 'created_at']
            }
            
            table_status = {}
            
            for table_name, required_columns in required_tables.items():
                cursor.execute(f"DESCRIBE {table_name}")
                existing_columns = [col['Field'] for col in cursor.fetchall()]
                
                missing_columns = set(required_columns) - set(existing_columns)
                extra_columns = set(existing_columns) - set(required_columns)
                
                table_status[table_name] = {
                    'exists': True,
                    'required_columns': len(required_columns),
                    'existing_columns': len(existing_columns),
                    'missing_columns': list(missing_columns),
                    'extra_columns': list(extra_columns)
                }
            
            cursor.close()
            connection.close()
            
            # تحليل النتائج
            all_tables_valid = True
            issues = []
            
            for table_name, status in table_status.items():
                if status['missing_columns']:
                    all_tables_valid = False
                    issues.append(f"{table_name}: أعمدة مفقودة {status['missing_columns']}")
            
            if all_tables_valid:
                details = f"جميع الجداول ({len(required_tables)}) صحيحة"
                self.log_validation("هيكل الجداول", "PASS", details, table_status)
            else:
                details = f"مشاكل في الهيكل: {'; '.join(issues)}"
                self.log_validation("هيكل الجداول", "FAIL", details, table_status)
            
            return all_tables_valid
            
        except Exception as e:
            self.log_validation("هيكل الجداول", "FAIL", f"خطأ: {e}")
            return False
    
    def validate_foreign_keys(self) -> bool:
        """التحقق من المفاتيح الخارجية"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # فحص المفاتيح الخارجية
            cursor.execute("""
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE REFERENCED_TABLE_SCHEMA = %s
            AND REFERENCED_TABLE_NAME IS NOT NULL
            """, (self.config['database'],))
            
            foreign_keys = cursor.fetchall()
            
            # فحص التحليلات اليتيمة
            cursor.execute("""
            SELECT COUNT(*) as orphaned_count
            FROM analyses a
            LEFT JOIN projects p ON a.project_id = p.id
            WHERE p.id IS NULL
            """)
            orphaned_analyses = cursor.fetchone()['orphaned_count']
            
            # فحص الأخطاء اليتيمة
            cursor.execute("""
            SELECT COUNT(*) as orphaned_count
            FROM errors e
            LEFT JOIN projects p ON e.project_id = p.id
            WHERE p.id IS NULL
            """)
            orphaned_errors = cursor.fetchone()['orphaned_count']
            
            # فحص التقارير اليتيمة
            cursor.execute("""
            SELECT COUNT(*) as orphaned_count
            FROM reports r
            LEFT JOIN projects p ON r.project_id = p.id
            WHERE p.id IS NULL
            """)
            orphaned_reports = cursor.fetchone()['orphaned_count']
            
            cursor.close()
            connection.close()
            
            total_orphaned = orphaned_analyses + orphaned_errors + orphaned_reports
            
            if total_orphaned == 0:
                details = f"جميع المفاتيح الخارجية ({len(foreign_keys)}) سليمة"
                self.log_validation("المفاتيح الخارجية", "PASS", details, {
                    'foreign_keys_count': len(foreign_keys),
                    'orphaned_records': 0
                })
                return True
            else:
                details = f"سجلات يتيمة: تحليلات={orphaned_analyses}, أخطاء={orphaned_errors}, تقارير={orphaned_reports}"
                self.log_validation("المفاتيح الخارجية", "WARN", details, {
                    'foreign_keys_count': len(foreign_keys),
                    'orphaned_analyses': orphaned_analyses,
                    'orphaned_errors': orphaned_errors,
                    'orphaned_reports': orphaned_reports
                })
                return False
            
        except Exception as e:
            self.log_validation("المفاتيح الخارجية", "FAIL", f"خطأ: {e}")
            return False
    
    def validate_data_quality(self) -> bool:
        """التحقق من جودة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            quality_checks = {}
            
            # فحص المشاريع
            cursor.execute("SELECT COUNT(*) as total, COUNT(CASE WHEN name IS NULL OR name = '' THEN 1 END) as empty_names FROM projects")
            projects_check = cursor.fetchone()
            quality_checks['projects'] = {
                'total': projects_check['total'],
                'empty_names': projects_check['empty_names']
            }
            
            # فحص التحليلات
            cursor.execute("SELECT COUNT(*) as total, COUNT(CASE WHEN score IS NULL THEN 1 END) as null_scores, AVG(score) as avg_score FROM analyses")
            analyses_check = cursor.fetchone()
            quality_checks['analyses'] = {
                'total': analyses_check['total'],
                'null_scores': analyses_check['null_scores'],
                'avg_score': float(analyses_check['avg_score']) if analyses_check['avg_score'] is not None else 0
            }
            
            # فحص الأخطاء
            cursor.execute("SELECT COUNT(*) as total, COUNT(CASE WHEN severity NOT IN ('low', 'medium', 'high', 'critical') THEN 1 END) as invalid_severity FROM errors")
            errors_check = cursor.fetchone()
            quality_checks['errors'] = {
                'total': errors_check['total'],
                'invalid_severity': errors_check['invalid_severity']
            }
            
            cursor.close()
            connection.close()
            
            # تحليل جودة البيانات
            issues = []
            
            if quality_checks['projects']['empty_names'] > 0:
                issues.append(f"مشاريع بأسماء فارغة: {quality_checks['projects']['empty_names']}")
            
            if quality_checks['analyses']['null_scores'] > 0:
                issues.append(f"تحليلات بدون نقاط: {quality_checks['analyses']['null_scores']}")
            
            if quality_checks['errors']['invalid_severity'] > 0:
                issues.append(f"أخطاء بمستوى خطورة غير صحيح: {quality_checks['errors']['invalid_severity']}")
            
            if not issues:
                details = f"جودة البيانات ممتازة - مشاريع: {quality_checks['projects']['total']}, تحليلات: {quality_checks['analyses']['total']}, متوسط النقاط: {quality_checks['analyses']['avg_score']:.1f}"
                self.log_validation("جودة البيانات", "PASS", details, quality_checks)
                return True
            else:
                details = f"مشاكل في جودة البيانات: {'; '.join(issues)}"
                self.log_validation("جودة البيانات", "WARN", details, quality_checks)
                return False
            
        except Exception as e:
            self.log_validation("جودة البيانات", "FAIL", f"خطأ: {e}")
            return False
    
    def validate_performance(self) -> bool:
        """التحقق من الأداء"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            performance_metrics = {}
            
            # اختبار سرعة الاستعلامات
            queries = [
                ("استعلام بسيط", "SELECT COUNT(*) as count FROM projects"),
                ("استعلام معقد", """
                SELECT p.name, COUNT(a.id) as analyses_count, AVG(a.score) as avg_score
                FROM projects p
                LEFT JOIN analyses a ON p.id = a.project_id
                GROUP BY p.id, p.name
                ORDER BY avg_score DESC
                """),
                ("استعلام JSON", "SELECT * FROM analyses WHERE JSON_EXTRACT(analysis_data, '$.files_analyzed') > 0")
            ]
            
            for query_name, query_sql in queries:
                start_time = time.time()
                cursor.execute(query_sql)
                cursor.fetchall()
                duration = time.time() - start_time
                performance_metrics[query_name] = duration
            
            cursor.close()
            connection.close()
            
            # تحليل الأداء
            max_acceptable_time = 0.5  # نصف ثانية
            slow_queries = [name for name, duration in performance_metrics.items() if duration > max_acceptable_time]
            
            if not slow_queries:
                avg_time = sum(performance_metrics.values()) / len(performance_metrics)
                details = f"الأداء ممتاز - متوسط وقت الاستعلام: {avg_time:.4f}s"
                self.log_validation("الأداء", "PASS", details, performance_metrics)
                return True
            else:
                details = f"استعلامات بطيئة: {', '.join(slow_queries)}"
                self.log_validation("الأداء", "WARN", details, performance_metrics)
                return False
            
        except Exception as e:
            self.log_validation("الأداء", "FAIL", f"خطأ: {e}")
            return False
    
    def run_final_validation(self) -> Dict[str, Any]:
        """تشغيل التحقق النهائي الشامل"""
        print("🏆 بدء التحقق النهائي من قاعدة بيانات نظام أنوبيس")
        print("=" * 60)
        
        start_time = time.time()
        
        # تشغيل جميع عمليات التحقق
        validations = [
            ("معلومات قاعدة البيانات", self.validate_database_info),
            ("هيكل الجداول", self.validate_table_structure),
            ("المفاتيح الخارجية", self.validate_foreign_keys),
            ("جودة البيانات", self.validate_data_quality),
            ("الأداء", self.validate_performance)
        ]
        
        passed_validations = 0
        total_validations = len(validations)
        
        for validation_name, validation_function in validations:
            try:
                if validation_function():
                    passed_validations += 1
            except Exception as e:
                self.log_validation(validation_name, "FAIL", f"خطأ غير متوقع: {e}")
        
        total_duration = time.time() - start_time
        
        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("🎯 ملخص التحقق النهائي")
        print("=" * 60)
        
        success_rate = passed_validations / total_validations * 100
        
        if success_rate == 100:
            status_icon = "🎉"
            status_text = "قاعدة البيانات جاهزة للإنتاج!"
        elif success_rate >= 80:
            status_icon = "⚠️"
            status_text = "قاعدة البيانات جيدة مع بعض التحذيرات"
        else:
            status_icon = "❌"
            status_text = "قاعدة البيانات تحتاج إلى إصلاحات"
        
        print(f"{status_icon} النتيجة النهائية: {passed_validations}/{total_validations} فحص نجح ({success_rate:.1f}%)")
        print(f"⏱️ وقت التحقق: {total_duration:.3f} ثانية")
        print(f"📋 الحالة: {status_text}")
        
        # إنشاء التقرير النهائي
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'total_validations': total_validations,
            'passed_validations': passed_validations,
            'success_rate': success_rate,
            'total_duration': total_duration,
            'status': status_text,
            'ready_for_production': success_rate == 100,
            'validation_results': self.validation_results,
            'database_config': {
                'host': self.config['host'],
                'port': self.config['port'],
                'database': self.config['database']
            }
        }
        
        return final_report


def main():
    """الدالة الرئيسية"""
    validator = FinalValidator()
    report = validator.run_final_validation()
    
    # حفظ التقرير
    report_file = f"database/final_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        # تحويل البيانات إلى تنسيق قابل للتحويل إلى JSON
        json_safe_report = validator._convert_to_json_serializable(report)
        json.dump(json_safe_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 تم حفظ التقرير النهائي في: {report_file}")
    
    # تحديد كود الخروج
    exit_code = 0 if report['ready_for_production'] else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
