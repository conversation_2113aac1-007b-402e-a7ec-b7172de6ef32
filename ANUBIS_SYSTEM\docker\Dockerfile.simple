# 🏺 نظام أنوبيس - Dockerfile بسيط وسريع
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV ANUBIS_ENV=development

# إنشاء مجلد العمل
WORKDIR /app

# تثبيت المتطلبات الأساسية فقط (سريع جداً)
RUN pip install --no-cache-dir fastapi uvicorn pydantic requests

# نسخ الملفات الأساسية فقط
COPY main.py .
COPY src/core/ ./src/core/

# إنشاء المجلدات المطلوبة
RUN mkdir -p data logs config

# فتح المنفذ
EXPOSE 8000

# تشغيل التطبيق
CMD ["python", "main.py"]
