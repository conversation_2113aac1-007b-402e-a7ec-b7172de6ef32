#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 نظام التكامل المحسن للخدمات الخارجية
Enhanced External Services Integration System

مطور بالتعاون مع Gemini CLI لتحسين موثوقية التكاملات
"""

import json
import time
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import requests
import subprocess
from functools import wraps


class IntegrationError(Exception):
    """استثناء مخصص لأخطاء التكامل"""
    pass


class ServiceUnavailableError(IntegrationError):
    """استثناء عندما تكون الخدمة غير متاحة"""
    pass


def retry_with_backoff(max_retries: int = 3, base_delay: float = 1.0):
    """مُزخرف لإعادة المحاولة مع تراجع أسي"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (requests.RequestException, ConnectionError, ServiceUnavailableError) as e:
                    if attempt == max_retries - 1:
                        raise IntegrationError(f"فشل في {func.__name__} بعد {max_retries} محاولات: {str(e)}")
                    
                    delay = base_delay * (2 ** attempt)
                    logging.warning(f"المحاولة {attempt + 1} فشلت، إعادة المحاولة خلال {delay} ثانية...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator


class HealthChecker:
    """فاحص صحة الخدمات الخارجية"""
    
    def __init__(self):
        self.services_status = {}
        self.last_check = None
        
    def check_ollama_health(self, host: str = "localhost", port: int = 11434) -> Dict[str, Any]:
        """فحص صحة خدمة Ollama"""
        try:
            url = f"http://{host}:{port}/api/tags"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                return {
                    "status": "healthy",
                    "available_models": [model.get("name", "") for model in models],
                    "model_count": len(models),
                    "response_time": response.elapsed.total_seconds()
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "response_time": response.elapsed.total_seconds()
                }
                
        except Exception as e:
            return {
                "status": "unavailable",
                "error": str(e),
                "response_time": None
            }
    
    def check_langsmith_health(self, api_key: str = None) -> Dict[str, Any]:
        """فحص صحة خدمة LangSmith"""
        try:
            # محاولة استيراد LangSmith
            from langsmith import Client
            
            if api_key:
                client = Client(api_key=api_key)
            else:
                client = Client()  # سيستخدم متغير البيئة
                
            # محاولة بسيطة للتحقق من الاتصال
            # يمكن تحسين هذا بناءً على API المتاح
            return {
                "status": "healthy",
                "client_initialized": True,
                "api_key_provided": bool(api_key)
            }
            
        except ImportError:
            return {
                "status": "unavailable",
                "error": "LangSmith library not installed",
                "client_initialized": False
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "client_initialized": False
            }
    
    def check_gemini_health(self, api_key: str = None) -> Dict[str, Any]:
        """فحص صحة خدمة Gemini"""
        if not api_key:
            return {
                "status": "unavailable",
                "error": "No API key provided",
                "configured": False
            }
            
        try:
            # يمكن إضافة فحص حقيقي لـ Gemini API هنا
            return {
                "status": "configured",
                "api_key_provided": True,
                "configured": True
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "configured": False
            }
    
    def run_comprehensive_health_check(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """تشغيل فحص صحة شامل لجميع الخدمات"""
        self.last_check = datetime.now()
        
        # فحص Ollama
        ollama_config = config.get("providers", {}).get("ollama", {})
        if ollama_config.get("enabled", False):
            self.services_status["ollama"] = self.check_ollama_health(
                ollama_config.get("host", "localhost"),
                ollama_config.get("port", 11434)
            )
        
        # فحص LangSmith
        langsmith_config = config.get("langsmith", {})
        if langsmith_config.get("enabled", False):
            self.services_status["langsmith"] = self.check_langsmith_health()
        
        # فحص Gemini
        gemini_config = config.get("providers", {}).get("gemini", {})
        if gemini_config.get("enabled", False):
            self.services_status["gemini"] = self.check_gemini_health(
                gemini_config.get("api_key")
            )
        
        return {
            "check_timestamp": self.last_check.isoformat(),
            "services": self.services_status,
            "overall_health": self._calculate_overall_health()
        }
    
    def _calculate_overall_health(self) -> str:
        """حساب الحالة الصحية الإجمالية"""
        if not self.services_status:
            return "unknown"
            
        healthy_count = sum(1 for status in self.services_status.values() 
                          if status.get("status") == "healthy")
        total_count = len(self.services_status)
        
        if healthy_count == total_count:
            return "excellent"
        elif healthy_count >= total_count * 0.7:
            return "good"
        elif healthy_count >= total_count * 0.5:
            return "fair"
        else:
            return "poor"


class EnhancedOllamaProvider:
    """موفر Ollama محسن مع معالجة أخطاء متقدمة"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.host = config.get("host", "localhost")
        self.port = config.get("port", 11434)
        self.model = config.get("model", "llama3:8b")
        self.base_url = f"http://{self.host}:{self.port}"
        self.timeout = config.get("timeout", 60)
        
    @retry_with_backoff(max_retries=3, base_delay=1.0)
    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """توليد استجابة مع إعادة المحاولة التلقائية"""
        try:
            url = f"{self.base_url}/api/generate"
            
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": context.get("temperature", 0.7) if context else 0.7,
                    "max_tokens": context.get("max_tokens", 1000) if context else 1000
                }
            }
            
            response = requests.post(url, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except requests.RequestException as e:
            raise ServiceUnavailableError(f"Ollama service error: {str(e)}")
        except Exception as e:
            raise IntegrationError(f"Unexpected error in Ollama provider: {str(e)}")
    
    @retry_with_backoff(max_retries=2, base_delay=0.5)
    def is_available(self) -> bool:
        """فحص توفر الخدمة مع إعادة المحاولة"""
        try:
            url = f"{self.base_url}/api/tags"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False


class EnhancedLangSmithWrapper:
    """LangSmith wrapper محسن مع معالجة أخطاء متقدمة"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = None
        self.enabled = config.get("langsmith", {}).get("enabled", False)
        self.project_name = config.get("langsmith", {}).get("project_name", "anubis-ai-system")
        
        if self.enabled:
            self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل LangSmith مع معالجة الأخطاء"""
        try:
            from langsmith import Client
            self.client = Client()
            logging.info("✅ تم تهيئة LangSmith Client بنجاح")
        except ImportError:
            logging.warning("⚠️ LangSmith library غير متاح")
            self.enabled = False
        except Exception as e:
            logging.error(f"❌ فشل في تهيئة LangSmith: {str(e)}")
            self.enabled = False
    
    @retry_with_backoff(max_retries=2, base_delay=1.0)
    def trace_agent_run(self, agent_name: str, input_data: Dict[str, Any], 
                       output_data: Dict[str, Any], metadata: Dict[str, Any] = None):
        """تتبع تشغيل الوكيل مع إعادة المحاولة"""
        if not self.enabled or not self.client:
            return
            
        try:
            trace_data = {
                "name": f"anubis_{agent_name}",
                "inputs": input_data,
                "outputs": output_data,
                "metadata": metadata or {},
                "project_name": self.project_name
            }
            
            # هنا يمكن إضافة الكود الفعلي لـ LangSmith tracing
            logging.info(f"📊 تم تتبع تشغيل {agent_name}")
            
        except Exception as e:
            logging.error(f"❌ فشل في تتبع {agent_name}: {str(e)}")
            raise IntegrationError(f"LangSmith tracing failed: {str(e)}")


class IntegrationsManager:
    """مدير التكاملات الرئيسي"""
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.health_checker = HealthChecker()
        self.ollama_provider = None
        self.langsmith_wrapper = None
        
        self._initialize_providers()
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """تحميل التكوين من ملفات متعددة"""
        if config_path is None:
            config_dir = Path(__file__).parent.parent / "configs"
        else:
            config_dir = Path(config_path)
            
        # دمج ملفات التكوين
        combined_config = {}
        
        # تحميل AI config
        ai_config_path = config_dir / "ai_config.json"
        if ai_config_path.exists():
            with open(ai_config_path, 'r', encoding='utf-8') as f:
                combined_config.update(json.load(f))
        
        # تحميل LangSmith config
        langsmith_config_path = config_dir / "langsmith_config.json"
        if langsmith_config_path.exists():
            with open(langsmith_config_path, 'r', encoding='utf-8') as f:
                combined_config.update(json.load(f))
        
        return combined_config
    
    def _initialize_providers(self):
        """تهيئة جميع الموفرين"""
        # تهيئة Ollama
        ollama_config = self.config.get("providers", {}).get("ollama", {})
        if ollama_config.get("enabled", False):
            self.ollama_provider = EnhancedOllamaProvider(ollama_config)
        
        # تهيئة LangSmith
        if self.config.get("langsmith", {}).get("enabled", False):
            self.langsmith_wrapper = EnhancedLangSmithWrapper(self.config)
    
    def run_startup_health_check(self) -> Dict[str, Any]:
        """تشغيل فحص صحة عند بدء التشغيل"""
        logging.info("🔍 تشغيل فحص صحة الخدمات الخارجية...")
        
        health_report = self.health_checker.run_comprehensive_health_check(self.config)
        
        # طباعة النتائج
        overall_health = health_report["overall_health"]
        if overall_health == "excellent":
            logging.info("✅ جميع الخدمات تعمل بشكل مثالي")
        elif overall_health == "good":
            logging.info("🟡 معظم الخدمات تعمل بشكل جيد")
        else:
            logging.warning("🔴 بعض الخدمات تواجه مشاكل")
        
        return health_report
    
    def get_available_provider(self) -> Optional[EnhancedOllamaProvider]:
        """الحصول على موفر متاح"""
        if self.ollama_provider and self.ollama_provider.is_available():
            return self.ollama_provider
        return None
    
    def trace_operation(self, operation_name: str, input_data: Dict[str, Any], 
                       output_data: Dict[str, Any], metadata: Dict[str, Any] = None):
        """تتبع عملية في LangSmith"""
        if self.langsmith_wrapper:
            self.langsmith_wrapper.trace_agent_run(operation_name, input_data, output_data, metadata)
