#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فئة التحقق من قاعدة البيانات - نظام أنوبيس
Anubis Database Validator Core Class

فئة أساسية للتحقق من قاعدة البيانات وإجراء الفحوصات المختلفة
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from typing import Any, Dict

import mysql.connector
from mysql.connector import Error

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# إعدادات التسجيل
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# ثوابت للرسائل المتكررة
DB_INFO_CHECK = "معلومات قاعدة البيانات"
TABLE_STRUCTURE_CHECK = "هيكل الجداول"
FOREIGN_KEYS_CHECK = "المفاتيح الخارجية"
DATA_QUALITY_CHECK = "جودة البيانات"
PERFORMANCE_CHECK = "الأداء"


class DatabaseValidator:
    """🔍 فئة التحقق الأساسية من قاعدة البيانات"""

    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة التحقق من قاعدة البيانات"""
        self.config_path = config_path
        self.config = self._load_config()
        self.validation_results = []
        self.logger = logging.getLogger(__name__)

    def _convert_to_json_serializable(self, obj: Any) -> Any:
        """تحويل البيانات إلى تنسيق قابل للتحويل إلى JSON"""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        else:
            return obj

    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"ملف الإعدادات غير موجود: {self.config_path}")

            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            if "database" not in config or "mysql" not in config["database"]:
                raise KeyError("إعدادات قاعدة البيانات MySQL غير موجودة في ملف الإعدادات")

            return config["database"]["mysql"]

        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {e}")
            raise ValueError(f"خطأ في تحميل الإعدادات: {e}")

    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config["host"],
                port=self.config["port"],
                user=self.config["user"],
                password=self.config["password"],
                database=self.config["database"],
                charset=self.config.get("charset", "utf8mb4"),
                autocommit=True,
            )
            return connection
        except Error as e:
            self.logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise ConnectionError(f"خطأ في الاتصال: {e}")

    def log_validation(self, check_name: str, status: str, details: str = "", data: Any = None):
        """تسجيل نتيجة التحقق"""
        result = {
            "check_name": check_name,
            "status": status,
            "details": details,
            "data": data,
            "timestamp": datetime.now().isoformat(),
        }
        self.validation_results.append(result)

        if status == "PASS":
            status_icon = "✅"
            log_level = logging.INFO
        elif status == "FAIL":
            status_icon = "❌"
            log_level = logging.ERROR
        else:
            status_icon = "⚠️"
            log_level = logging.WARNING

        print(f"{status_icon} {check_name}")
        if details:
            print(f"   📝 {details}")

        self.logger.log(log_level, f"{check_name}: {status} - {details}")

    def validate_database_info(self) -> bool:
        """التحقق من معلومات قاعدة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            # معلومات الخادم
            cursor.execute("SELECT VERSION() as version")
            server_info = cursor.fetchone()

            # معلومات قاعدة البيانات
            cursor.execute("SELECT DATABASE() as current_db")
            db_info = cursor.fetchone()

            # حجم قاعدة البيانات
            cursor.execute(
                """
            SELECT
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = %s
            """,
                (self.config["database"],),
            )
            size_info = cursor.fetchone()

            # عدد الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            cursor.close()
            connection.close()

            details = (
                f"MySQL {server_info['version']}, "
                f"قاعدة البيانات: {db_info['current_db']}, "
                f"الحجم: {size_info['size_mb']}MB, "
                f"الجداول: {len(tables)}"
            )

            self.log_validation(
                DB_INFO_CHECK,
                "PASS",
                details,
                {
                    "mysql_version": server_info["version"],
                    "database_name": db_info["current_db"],
                    "size_mb": size_info["size_mb"],
                    "tables_count": len(tables),
                },
            )

            return True

        except Exception as e:
            self.log_validation(DB_INFO_CHECK, "FAIL", f"خطأ: {e}")
            return False

    def validate_table_structure(self) -> bool:
        """التحقق من هيكل الجداول"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            # التحقق من الجداول المطلوبة
            required_tables = {
                "projects": [
                    "id",
                    "name",
                    "path",
                    "type",
                    "description",
                    "created_at",
                    "updated_at",
                ],
                "analyses": [
                    "id",
                    "project_id",
                    "agent_type",
                    "analysis_data",
                    "results",
                    "score",
                    "created_at",
                ],
                "errors": [
                    "id",
                    "project_id",
                    "file_path",
                    "line_number",
                    "error_type",
                    "severity",
                    "message",
                    "created_at",
                ],
                "reports": [
                    "id",
                    "project_id",
                    "report_type",
                    "title",
                    "content",
                    "file_path",
                    "created_at",
                ],
                "plugins": [
                    "id",
                    "name",
                    "version",
                    "description",
                    "config",
                    "enabled",
                    "created_at",
                    "updated_at",
                ],
                "activities": [
                    "id",
                    "project_id",
                    "activity_type",
                    "description",
                    "metadata",
                    "created_at",
                ],
            }

            table_status = {}

            for table_name, required_columns in required_tables.items():
                try:
                    cursor.execute(f"DESCRIBE {table_name}")
                    existing_columns = [col["Field"] for col in cursor.fetchall()]

                    missing_columns = set(required_columns) - set(existing_columns)
                    extra_columns = set(existing_columns) - set(required_columns)

                    table_status[table_name] = {
                        "exists": True,
                        "required_columns": len(required_columns),
                        "existing_columns": len(existing_columns),
                        "missing_columns": list(missing_columns),
                        "extra_columns": list(extra_columns),
                    }
                except Error:
                    table_status[table_name] = {
                        "exists": False,
                        "required_columns": len(required_columns),
                        "existing_columns": 0,
                        "missing_columns": required_columns,
                        "extra_columns": [],
                    }

            cursor.close()
            connection.close()

            # تحليل النتائج
            all_tables_valid = True
            issues = []

            for table_name, status in table_status.items():
                if not status["exists"]:
                    all_tables_valid = False
                    issues.append(f"{table_name}: الجدول غير موجود")
                elif status["missing_columns"]:
                    all_tables_valid = False
                    issues.append(f"{table_name}: أعمدة مفقودة {status['missing_columns']}")

            if all_tables_valid:
                details = f"جميع الجداول ({len(required_tables)}) صحيحة"
                self.log_validation(TABLE_STRUCTURE_CHECK, "PASS", details, table_status)
            else:
                details = f"مشاكل في الهيكل: {'; '.join(issues)}"
                self.log_validation(TABLE_STRUCTURE_CHECK, "FAIL", details, table_status)

            return all_tables_valid

        except Exception as e:
            self.log_validation(TABLE_STRUCTURE_CHECK, "FAIL", f"خطأ: {e}")
            return False

    def validate_foreign_keys(self) -> bool:
        """التحقق من المفاتيح الخارجية"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            # فحص المفاتيح الخارجية
            cursor.execute(
                """
            SELECT
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE REFERENCED_TABLE_SCHEMA = %s
            AND REFERENCED_TABLE_NAME IS NOT NULL
            """,
                (self.config["database"],),
            )

            foreign_keys = cursor.fetchall()

            # فحص التحليلات اليتيمة
            cursor.execute(
                """
            SELECT COUNT(*) as orphaned_count
            FROM analyses a
            LEFT JOIN projects p ON a.project_id = p.id
            WHERE p.id IS NULL
            """
            )
            orphaned_analyses = cursor.fetchone()["orphaned_count"]

            # فحص الأخطاء اليتيمة
            cursor.execute(
                """
            SELECT COUNT(*) as orphaned_count
            FROM errors e
            LEFT JOIN projects p ON e.project_id = p.id
            WHERE p.id IS NULL
            """
            )
            orphaned_errors = cursor.fetchone()["orphaned_count"]

            # فحص التقارير اليتيمة
            cursor.execute(
                """
            SELECT COUNT(*) as orphaned_count
            FROM reports r
            LEFT JOIN projects p ON r.project_id = p.id
            WHERE p.id IS NULL
            """
            )
            orphaned_reports = cursor.fetchone()["orphaned_count"]

            cursor.close()
            connection.close()

            total_orphaned = orphaned_analyses + orphaned_errors + orphaned_reports

            if total_orphaned == 0:
                details = f"جميع المفاتيح الخارجية ({len(foreign_keys)}) سليمة"
                self.log_validation(
                    FOREIGN_KEYS_CHECK,
                    "PASS",
                    details,
                    {"foreign_keys_count": len(foreign_keys), "orphaned_records": 0},
                )
                return True
            else:
                details = (
                    f"سجلات يتيمة: تحليلات={orphaned_analyses}, "
                    f"أخطاء={orphaned_errors}, تقارير={orphaned_reports}"
                )
                self.log_validation(
                    FOREIGN_KEYS_CHECK,
                    "WARN",
                    details,
                    {
                        "foreign_keys_count": len(foreign_keys),
                        "orphaned_analyses": orphaned_analyses,
                        "orphaned_errors": orphaned_errors,
                        "orphaned_reports": orphaned_reports,
                    },
                )
                return False

        except Exception as e:
            self.log_validation(FOREIGN_KEYS_CHECK, "FAIL", f"خطأ: {e}")
            return False

    def validate_data_quality(self) -> bool:
        """التحقق من جودة البيانات"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            quality_checks = {}

            # فحص المشاريع
            cursor.execute(
                """
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN name IS NULL OR name = ''
                         THEN 1 END) as empty_names
            FROM projects
            """
            )
            projects_check = cursor.fetchone()
            quality_checks["projects"] = {
                "total": projects_check["total"],
                "empty_names": projects_check["empty_names"],
            }

            # فحص التحليلات
            cursor.execute(
                """
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN score IS NULL THEN 1 END) as null_scores,
                   AVG(score) as avg_score
            FROM analyses
            """
            )
            analyses_check = cursor.fetchone()
            avg_score = analyses_check["avg_score"]
            quality_checks["analyses"] = {
                "total": analyses_check["total"],
                "null_scores": analyses_check["null_scores"],
                "avg_score": float(avg_score) if avg_score is not None else 0,
            }

            # فحص الأخطاء
            cursor.execute(
                """
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN severity NOT IN
                         ('low', 'medium', 'high', 'critical')
                         THEN 1 END) as invalid_severity
            FROM errors
            """
            )
            errors_check = cursor.fetchone()
            quality_checks["errors"] = {
                "total": errors_check["total"],
                "invalid_severity": errors_check["invalid_severity"],
            }

            cursor.close()
            connection.close()

            # تحليل جودة البيانات
            issues = []

            # فحص المشاكل في البيانات
            empty_names = quality_checks["projects"]["empty_names"]
            null_scores = quality_checks["analyses"]["null_scores"]
            invalid_severity = quality_checks["errors"]["invalid_severity"]

            if empty_names > 0:
                issues.append(f"مشاريع بأسماء فارغة: {empty_names}")

            if null_scores > 0:
                issues.append(f"تحليلات بدون نقاط: {null_scores}")

            if invalid_severity > 0:
                issues.append(f"أخطاء بمستوى خطورة غير صحيح: {invalid_severity}")

            if not issues:
                projects_count = quality_checks["projects"]["total"]
                analyses_count = quality_checks["analyses"]["total"]
                avg_score = quality_checks["analyses"]["avg_score"]

                details = (
                    f"جودة البيانات ممتازة - مشاريع: {projects_count}, "
                    f"تحليلات: {analyses_count}, "
                    f"متوسط النقاط: {avg_score:.1f}"
                )
                self.log_validation(DATA_QUALITY_CHECK, "PASS", details, quality_checks)
                return True
            else:
                details = f"مشاكل في جودة البيانات: {'; '.join(issues)}"
                self.log_validation(DATA_QUALITY_CHECK, "WARN", details, quality_checks)
                return False

        except Exception as e:
            self.log_validation(DATA_QUALITY_CHECK, "FAIL", f"خطأ: {e}")
            return False

    def validate_performance(self) -> bool:
        """التحقق من الأداء"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            performance_metrics = {}

            # اختبار سرعة الاستعلامات
            queries = [
                ("استعلام بسيط", "SELECT COUNT(*) as count FROM projects"),
                (
                    "استعلام معقد",
                    """
                SELECT p.name, COUNT(a.id) as analyses_count, AVG(a.score) as avg_score
                FROM projects p
                LEFT JOIN analyses a ON p.id = a.project_id
                GROUP BY p.id, p.name
                ORDER BY avg_score DESC
                """,
                ),
                (
                    "استعلام JSON",
                    "SELECT * FROM analyses WHERE JSON_EXTRACT(analysis_data, '$.files_analyzed') > 0",
                ),
            ]

            for query_name, query_sql in queries:
                start_time = time.time()
                try:
                    cursor.execute(query_sql)
                    cursor.fetchall()
                    duration = time.time() - start_time
                    performance_metrics[query_name] = duration
                except Error:
                    # إذا فشل استعلام JSON، نتجاهله
                    performance_metrics[query_name] = 0

            cursor.close()
            connection.close()

            # تحليل الأداء
            max_acceptable_time = 0.5  # نصف ثانية
            slow_queries = [
                name
                for name, duration in performance_metrics.items()
                if duration > max_acceptable_time
            ]

            if not slow_queries:
                avg_time = sum(performance_metrics.values()) / len(performance_metrics)
                details = f"الأداء ممتاز - متوسط وقت الاستعلام: {avg_time:.4f}s"
                self.log_validation(PERFORMANCE_CHECK, "PASS", details, performance_metrics)
                return True
            else:
                details = f"استعلامات بطيئة: {', '.join(slow_queries)}"
                self.log_validation(PERFORMANCE_CHECK, "WARN", details, performance_metrics)
                return False

        except Exception as e:
            self.log_validation(PERFORMANCE_CHECK, "FAIL", f"خطأ: {e}")
            return False
