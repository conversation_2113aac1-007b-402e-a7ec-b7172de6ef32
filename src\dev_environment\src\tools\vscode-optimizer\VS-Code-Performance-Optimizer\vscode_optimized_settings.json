{
  "editor.semanticHighlighting.enabled": false,
  "editor.bracketPairColorization.enabled": false,
  "editor.renderWhitespace": "none",
  "editor.minimap.enabled": false,
  "editor.codeLens": false,
  "editor.lightbulb.enabled": false,
  "editor.hover.enabled": false,
  "editor.parameterHints.enabled": false,
  "editor.quickSuggestions": false,
  "editor.suggestOnTriggerCharacters": false,
  "editor.wordBasedSuggestions": false,
  "editor.acceptSuggestionOnEnter": "off",
  "editor.acceptSuggestionOnCommitCharacter": false,
  
  "// تحسينات الملفات والمراقبة",
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.git/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/out/**": true,
    "**/.vscode/**": true,
    "**/coverage/**": true,
    "**/.nyc_output/**": true,
    "**/tmp/**": true,
    "**/temp/**": true
  },
  
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.git": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true,
    "**/.nyc_output": true
  },
  
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/bower_components": true
  },
  
  "// تحسينات TypeScript/JavaScript",
  "typescript.disableAutomaticTypeAcquisition": true,
  "typescript.suggest.autoImports": false,
  "typescript.updateImportsOnFileMove.enabled": "never",
  "javascript.suggest.autoImports": false,
  "javascript.updateImportsOnFileMove.enabled": "never",
  
  "// تحسينات الإضافات والتحديثات",
  "extensions.autoUpdate": false,
  "extensions.autoCheckUpdates": false,
  "update.mode": "none",
  
  "// تحسينات الخصوصية والتتبع",
  "telemetry.telemetryLevel": "off",
  "workbench.enableExperiments": false,
  "workbench.settings.enableNaturalLanguageSearch": false,
  
  "// تحسينات الواجهة",
  "workbench.startupEditor": "none",
  "workbench.tips.enabled": false,
  "workbench.welcome.enabled": false,
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": null,
  
  "// تحسينات Git",
  "git.enabled": true,
  "git.autorefresh": false,
  "git.autofetch": false,
  "git.decorations.enabled": false,
  
  "// تحسينات المحرر",
  "editor.formatOnSave": false,
  "editor.formatOnType": false,
  "editor.formatOnPaste": false,
  "editor.autoIndent": "none",
  "editor.trimAutoWhitespace": false,
  
  "// تحسينات الذاكرة",
  "editor.maxTokenizationLineLength": 1000,
  "editor.largeFileOptimizations": true,
  
  "// تعطيل الميزات غير الضرورية",
  "breadcrumbs.enabled": false,
  "outline.showVariables": false,
  "outline.showFunctions": false,
  "problems.decorations.enabled": false,
  
  "// تحسينات الشبكة",
  "http.proxyStrictSSL": false,
  "extensions.ignoreRecommendations": true,
  
  "// تحسينات الأمان (اختيارية)",
  "security.workspace.trust.enabled": false,
  
  "// إعدادات خاصة بـ Python (إذا كنت تستخدمه)",
  "python.analysis.autoImportCompletions": false,
  "python.analysis.autoSearchPaths": false,
  "python.analysis.diagnosticMode": "openFilesOnly",
  "python.linting.enabled": false,
  
  "// إعدادات خاصة بـ C# (إذا كنت تستخدمه)",
  "omnisharp.enableEditorConfigSupport": false,
  "omnisharp.enableImportCompletion": false,
  "omnisharp.enableRoslynAnalyzers": false
}
