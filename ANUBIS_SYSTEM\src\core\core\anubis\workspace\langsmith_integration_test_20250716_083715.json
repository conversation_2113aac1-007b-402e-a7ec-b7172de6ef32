{"test_info": {"timestamp": "2025-07-16T08:35:58.253323", "duration": 76.913924, "test_type": "langsmith_integration"}, "langsmith_status": {"available": true, "wrapper_functional": true, "traces_recorded": 19}, "test_results": {"langsmith_availability": true, "wrapper_functionality": true, "agents_test": {"enhanced_error_detector": {"status": "success", "info": {"status": "loaded"}}, "enhanced_project_analyzer": {"status": "success", "info": {"status": "loaded"}}, "enhanced_file_organizer": {"status": "success", "info": {"status": "loaded"}}, "enhanced_memory_agent": {"status": "success", "info": {"status": "loaded"}}, "smart_code_analyzer": {"status": "success", "info": {"status": "loaded"}}}, "models_test": {"llama3:8b": {"status": "success", "response_time": 15.56, "response_length": 149, "available": true}, "mistral:7b": {"status": "success", "response_time": 24.96, "response_length": 147, "available": true}, "phi3:mini": {"status": "success", "response_time": 19.01, "response_length": 316, "available": true}}, "coordination_test": {"status": "success", "steps_completed": 4, "results": {"analyzer": "تم تحليل المشروع", "detector": "تم كشف الأخطاء", "organizer": "تم تنظيم النتائج", "memory": "تم حفظ البيانات"}}}, "traces_summary": {"total_traces": 19, "traces": [{"name": "smart_code_analyzer_load_agent", "inputs": null, "timestamp": "2025-07-16T08:36:06.957792", "agent": "smart_code_analyzer", "operation": "load_agent", "completed_at": "2025-07-16T08:36:09.035176"}, {"name": "smart_code_analyzer_test_operation", "inputs": null, "timestamp": "2025-07-16T08:36:09.035207", "agent": "smart_code_analyzer", "operation": "test_operation", "completed_at": "2025-07-16T08:36:09.035672"}, {"name": "ollama_provider_test_llama3:8b", "inputs": null, "timestamp": "2025-07-16T08:36:09.037646", "agent": "ollama_provider", "operation": "test_llama3:8b", "completed_at": "2025-07-16T08:36:26.663875"}, {"name": "ollama_provider_test_mistral:7b", "inputs": null, "timestamp": "2025-07-16T08:36:26.664040", "agent": "ollama_provider", "operation": "test_mistral:7b", "completed_at": "2025-07-16T08:36:53.686099"}, {"name": "ollama_provider_test_phi3:mini", "inputs": null, "timestamp": "2025-07-16T08:36:53.686184", "agent": "ollama_provider", "operation": "test_phi3:mini", "completed_at": "2025-07-16T08:37:14.759128"}, {"name": "orchestrator_multi_agent_test", "inputs": null, "timestamp": "2025-07-16T08:37:14.759774", "agent": "orchestrator", "operation": "multi_agent_test", "completed_at": "2025-07-16T08:37:15.166276"}, {"name": "coordination_analyzer", "inputs": null, "timestamp": "2025-07-16T08:37:14.760014", "agent": "coordination", "operation": "analyzer", "completed_at": "2025-07-16T08:37:14.860739"}, {"name": "coordination_detector", "inputs": null, "timestamp": "2025-07-16T08:37:14.860790", "agent": "coordination", "operation": "detector", "completed_at": "2025-07-16T08:37:14.961488"}, {"name": "coordination_organizer", "inputs": null, "timestamp": "2025-07-16T08:37:14.961539", "agent": "coordination", "operation": "organizer", "completed_at": "2025-07-16T08:37:15.062359"}, {"name": "coordination_memory", "inputs": null, "timestamp": "2025-07-16T08:37:15.062382", "agent": "coordination", "operation": "memory", "completed_at": "2025-07-16T08:37:15.165556"}], "langsmith_enabled": false}, "recommendations": [{"type": "api_key", "priority": "high", "description": "تعيين LANGCHAIN_API_KEY للاستفادة الكاملة من LangSmith"}, {"type": "monitoring", "priority": "medium", "description": "إعداد مراقبة مستمرة للأداء"}, {"type": "optimization", "priority": "low", "description": "تحسين prompts والتفاعلات بناءً على البيانات المجمعة"}]}