#!/usr/bin/env python3
"""
🏺 قاعدة بيانات نظام أنوبيس
Anubis System Database

نظام إدارة قاعدة البيانات لنظام المساعدين الذكيين أنوبيس
"""

import json
import os
import sqlite3
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import mysql.connector


class AnubisDatabase:
    """فئة إدارة قاعدة بيانات نظام أنوبيس"""

    def __init__(self, db_type: str = "sqlite", config: Dict[str, Any] = None):
        """
        تهيئة قاعدة البيانات

        Args:
            db_type: نوع قاعدة البيانات (sqlite, mysql)
            config: إعدادات الاتصال
        """
        self.db_type = db_type.lower()
        self.config = config or {}
        self.connection = None
        self.cursor = None

        # إعدادات افتراضية
        if self.db_type == "sqlite":
            self.db_path = self.config.get("db_path", "database/anubis.db")
        elif self.db_type == "mysql":
            self.mysql_config = {
                "host": self.config.get("host", "localhost"),
                "port": self.config.get("port", 3306),
                "user": self.config.get("user", "root"),
                "password": self.config.get("password", ""),
                "database": self.config.get("database", "anubis_system"),
            }

        # إنشاء مجلد قاعدة البيانات
        Path("database").mkdir(exist_ok=True)

        print(f"🏺 تم تهيئة قاعدة بيانات أنوبيس - النوع: {self.db_type}")

    def connect(self) -> bool:
        """إنشاء اتصال مع قاعدة البيانات"""
        try:
            if self.db_type == "sqlite":
                self.connection = sqlite3.connect(self.db_path)
                self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
                self.cursor = self.connection.cursor()
                print(f"✅ تم الاتصال بقاعدة بيانات SQLite: {self.db_path}")

            elif self.db_type == "mysql":
                # إنشاء قاعدة البيانات إذا لم تكن موجودة
                temp_connection = mysql.connector.connect(
                    host=self.mysql_config["host"],
                    port=self.mysql_config["port"],
                    user=self.mysql_config["user"],
                    password=self.mysql_config["password"],
                )
                temp_cursor = temp_connection.cursor()
                temp_cursor.execute(
                    f"CREATE DATABASE IF NOT EXISTS {self.mysql_config['database']}"
                )
                temp_connection.commit()
                temp_cursor.close()
                temp_connection.close()

                # الاتصال بقاعدة البيانات
                self.connection = mysql.connector.connect(**self.mysql_config)
                self.cursor = self.connection.cursor(dictionary=True)
                print(f"✅ تم الاتصال بقاعدة بيانات MySQL: {self.mysql_config['database']}")

            return True

        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # جدول المشاريع
            projects_table = """
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                path TEXT NOT NULL,
                type VARCHAR(100),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(50) DEFAULT 'active'
            )
            """

            # جدول التحليلات
            analyses_table = """
            CREATE TABLE IF NOT EXISTS analyses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                agent_type VARCHAR(100) NOT NULL,
                analysis_data TEXT,
                results TEXT,
                score DECIMAL(5,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id)
            )
            """

            # جدول الأخطاء
            errors_table = """
            CREATE TABLE IF NOT EXISTS errors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                file_path TEXT,
                line_number INTEGER,
                error_type VARCHAR(100),
                severity VARCHAR(20),
                message TEXT,
                fixed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id)
            )
            """

            # جدول التقارير
            reports_table = """
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                report_type VARCHAR(100),
                report_data TEXT,
                file_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id)
            )
            """

            # جدول الإضافات
            plugins_table = """
            CREATE TABLE IF NOT EXISTS plugins (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                version VARCHAR(50),
                description TEXT,
                enabled BOOLEAN DEFAULT TRUE,
                config TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """

            # جدول سجل النشاطات
            activities_table = """
            CREATE TABLE IF NOT EXISTS activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                activity_type VARCHAR(100),
                description TEXT,
                user_agent VARCHAR(100),
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id)
            )
            """

            # تنفيذ إنشاء الجداول
            tables = [
                ("projects", projects_table),
                ("analyses", analyses_table),
                ("errors", errors_table),
                ("reports", reports_table),
                ("plugins", plugins_table),
                ("activities", activities_table),
            ]

            for table_name, table_sql in tables:
                if self.db_type == "mysql":
                    # تعديل SQL لـ MySQL
                    table_sql = table_sql.replace(
                        "INTEGER PRIMARY KEY AUTOINCREMENT",
                        "INT AUTO_INCREMENT PRIMARY KEY",
                    )
                    table_sql = table_sql.replace(
                        "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                        "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                    )

                self.cursor.execute(table_sql)
                print(f"✅ تم إنشاء جدول: {table_name}")

            self.connection.commit()
            print("🏺 تم إنشاء جميع جداول قاعدة بيانات أنوبيس بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
            if self.connection:
                self.connection.rollback()

    def add_project(
        self, name: str, path: str, project_type: str = None, description: str = None
    ) -> int:
        """إضافة مشروع جديد"""
        try:
            sql = """
            INSERT INTO projects (name, path, type, description)
            VALUES (?, ?, ?, ?)
            """
            if self.db_type == "mysql":
                sql = sql.replace("?", "%s")

            self.cursor.execute(sql, (name, path, project_type, description))
            self.connection.commit()

            project_id = self.cursor.lastrowid
            print(f"✅ تم إضافة المشروع: {name} (ID: {project_id})")

            # تسجيل النشاط
            self.log_activity(project_id, "project_created", f"تم إنشاء المشروع: {name}")

            return project_id

        except Exception as e:
            print(f"❌ خطأ في إضافة المشروع: {e}")
            return -1

    def add_analysis(
        self,
        project_id: int,
        agent_type: str,
        analysis_data: Dict[str, Any],
        results: Dict[str, Any],
        score: float = None,
    ) -> int:
        """إضافة تحليل جديد"""
        try:
            sql = """
            INSERT INTO analyses (project_id, agent_type, analysis_data, results, score)
            VALUES (?, ?, ?, ?, ?)
            """
            if self.db_type == "mysql":
                sql = sql.replace("?", "%s")

            self.cursor.execute(
                sql,
                (
                    project_id,
                    agent_type,
                    json.dumps(analysis_data, ensure_ascii=False),
                    json.dumps(results, ensure_ascii=False),
                    score,
                ),
            )
            self.connection.commit()

            analysis_id = self.cursor.lastrowid
            print(f"✅ تم إضافة التحليل: {agent_type} للمشروع {project_id}")

            return analysis_id

        except Exception as e:
            print(f"❌ خطأ في إضافة التحليل: {e}")
            return -1

    def add_error(
        self,
        project_id: int,
        file_path: str,
        line_number: int,
        error_type: str,
        severity: str,
        message: str,
    ) -> int:
        """إضافة خطأ جديد"""
        try:
            sql = """
            INSERT INTO errors (project_id, file_path, line_number, error_type, severity, message)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            if self.db_type == "mysql":
                sql = sql.replace("?", "%s")

            self.cursor.execute(
                sql, (project_id, file_path, line_number, error_type, severity, message)
            )
            self.connection.commit()

            error_id = self.cursor.lastrowid
            return error_id

        except Exception as e:
            print(f"❌ خطأ في إضافة الخطأ: {e}")
            return -1

    def get_projects(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المشاريع"""
        try:
            self.cursor.execute("SELECT * FROM projects ORDER BY created_at DESC")
            projects = []

            for row in self.cursor.fetchall():
                if self.db_type == "sqlite":
                    projects.append(dict(row))
                else:
                    projects.append(row)

            return projects

        except Exception as e:
            print(f"❌ خطأ في جلب المشاريع: {e}")
            return []

    def get_project_analyses(self, project_id: int) -> List[Dict[str, Any]]:
        """الحصول على تحليلات مشروع محدد"""
        try:
            sql = "SELECT * FROM analyses WHERE project_id = ? ORDER BY created_at DESC"
            if self.db_type == "mysql":
                sql = sql.replace("?", "%s")

            self.cursor.execute(sql, (project_id,))
            analyses = []

            for row in self.cursor.fetchall():
                if self.db_type == "sqlite":
                    analysis = dict(row)
                else:
                    analysis = row

                # تحويل JSON strings إلى objects
                if analysis["analysis_data"]:
                    analysis["analysis_data"] = json.loads(analysis["analysis_data"])
                if analysis["results"]:
                    analysis["results"] = json.loads(analysis["results"])

                analyses.append(analysis)

            return analyses

        except Exception as e:
            print(f"❌ خطأ في جلب التحليلات: {e}")
            return []

    def log_activity(
        self,
        project_id: int,
        activity_type: str,
        description: str,
        user_agent: str = "anubis_system",
        metadata: Dict[str, Any] = None,
    ):
        """تسجيل نشاط"""
        try:
            sql = """
            INSERT INTO activities (project_id, activity_type, description, user_agent, metadata)
            VALUES (?, ?, ?, ?, ?)
            """
            if self.db_type == "mysql":
                sql = sql.replace("?", "%s")

            metadata_json = json.dumps(metadata, ensure_ascii=False) if metadata else None

            self.cursor.execute(
                sql, (project_id, activity_type, description, user_agent, metadata_json)
            )
            self.connection.commit()

        except Exception as e:
            print(f"❌ خطأ في تسجيل النشاط: {e}")

    def get_database_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            stats = {}

            # عدد المشاريع
            self.cursor.execute("SELECT COUNT(*) as count FROM projects")
            stats["total_projects"] = (
                self.cursor.fetchone()["count"]
                if self.db_type == "mysql"
                else self.cursor.fetchone()[0]
            )

            # عدد التحليلات
            self.cursor.execute("SELECT COUNT(*) as count FROM analyses")
            stats["total_analyses"] = (
                self.cursor.fetchone()["count"]
                if self.db_type == "mysql"
                else self.cursor.fetchone()[0]
            )

            # عدد الأخطاء
            self.cursor.execute("SELECT COUNT(*) as count FROM errors")
            stats["total_errors"] = (
                self.cursor.fetchone()["count"]
                if self.db_type == "mysql"
                else self.cursor.fetchone()[0]
            )

            # عدد التقارير
            self.cursor.execute("SELECT COUNT(*) as count FROM reports")
            stats["total_reports"] = (
                self.cursor.fetchone()["count"]
                if self.db_type == "mysql"
                else self.cursor.fetchone()[0]
            )

            return stats

        except Exception as e:
            print(f"❌ خطأ في جلب الإحصائيات: {e}")
            return {}

    def close(self):
        """إغلاق الاتصال"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🏺 تم إغلاق اتصال قاعدة بيانات أنوبيس")

    def __enter__(self):
        """دعم context manager"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق تلقائي عند انتهاء context"""
        self.close()
