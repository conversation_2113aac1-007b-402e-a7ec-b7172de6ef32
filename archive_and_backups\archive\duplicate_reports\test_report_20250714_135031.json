{"timestamp": "2025-07-14T13:50:31.043321", "total_tests": 6, "passed_tests": 6, "success_rate": 100.0, "total_duration": 0.1666111946105957, "test_results": [{"test_name": "اختبار الاتصال", "status": "PASS", "message": "الاتصال بقاعدة البيانات نجح", "duration": 0.1063699722290039, "timestamp": "2025-07-14T13:50:30.983076"}, {"test_name": "اختبار هيكل قاعدة البيانات", "status": "PASS", "message": "جميع الجداول والأعمدة موجودة (6 جداول)", "duration": 0.01079106330871582, "timestamp": "2025-07-14T13:50:30.993940"}, {"test_name": "اختبار سلامة البيانات", "status": "PASS", "message": "البيانات سليمة - المشاريع: 9, التحليلات: 9, الأخطاء: 9, الإضافات: 6, الأنشطة: 9", "duration": 0.009768486022949219, "timestamp": "2025-07-14T13:50:31.003763"}, {"test_name": "اختبار عمليات CRUD", "status": "PASS", "message": "جميع عمليات الإنشاء والقراءة والتحديث والحذف نجحت", "duration": 0.022137165069580078, "timestamp": "2025-07-14T13:50:31.025936"}, {"test_name": "اختبار الأداء", "status": "PASS", "message": "أوقات الاستعلامات: استعلام بسيط: 0.000s, استعلام معقد: 0.001s, استعلام JSON: 0.000s", "duration": 0.008226156234741211, "timestamp": "2025-07-14T13:50:31.034202"}, {"test_name": "اختبار الأمان", "status": "PASS", "message": "الحماية من SQL Injection تعمل بشكل صحيح", "duration": 0.009014606475830078, "timestamp": "2025-07-14T13:50:31.043254"}], "errors_found": [], "database_config": {"host": "localhost", "port": 3306, "database": "anubis_system"}}