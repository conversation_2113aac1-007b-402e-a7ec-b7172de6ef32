#!/usr/bin/env python3
"""
🗄️ محسن قاعدة بيانات أنوبيس المتقدم
Advanced Anubis Database Optimizer
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime

class AnubisDBOptimizer:
    def __init__(self, db_path="database/anubis.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        
    def optimize_database(self):
        """تحسين قاعدة البيانات بالكامل"""
        print("🔧 بدء تحسين قاعدة البيانات...")
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # إنشاء الجداول المحسنة
            self._create_optimized_tables(cursor)
            
            # إنشاء الفهارس
            self._create_indexes(cursor)
            
            # تحسين الأداء
            self._optimize_performance(cursor)
            
            # إدراج بيانات تجريبية
            self._insert_sample_data(cursor)
            
            conn.commit()
        
        print("✅ تم تحسين قاعدة البيانات بنجاح")
        return True
    
    def _create_optimized_tables(self, cursor):
        """إنشاء جداول محسنة"""
        
        # جدول المشاريع المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            type TEXT NOT NULL,
            description TEXT,
            path TEXT NOT NULL,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSON,
            analysis_count INTEGER DEFAULT 0,
            last_analysis TIMESTAMP,
            quality_score REAL DEFAULT 0.0
        )
        """)
        
        # جدول التحليلات المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS analyses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            agent_name TEXT NOT NULL,
            analysis_type TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP,
            duration_seconds REAL,
            results JSON,
            metrics JSON,
            score REAL DEFAULT 0.0,
            errors_count INTEGER DEFAULT 0,
            warnings_count INTEGER DEFAULT 0,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
        )
        """)
        
        # جدول الأخطاء المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS errors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER,
            analysis_id INTEGER,
            error_type TEXT NOT NULL,
            severity TEXT NOT NULL,
            message TEXT NOT NULL,
            file_path TEXT,
            line_number INTEGER,
            column_number INTEGER,
            detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            resolved BOOLEAN DEFAULT FALSE,
            resolution_notes TEXT,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (analysis_id) REFERENCES analyses(id) ON DELETE CASCADE
        )
        """)
        
        # جدول التقارير المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            report_type TEXT NOT NULL,
            title TEXT NOT NULL,
            content JSON NOT NULL,
            format TEXT DEFAULT 'json',
            generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            size_bytes INTEGER,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
        )
        """)
        
        # جدول الإضافات المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS plugins (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            version TEXT NOT NULL,
            description TEXT,
            author TEXT,
            enabled BOOLEAN DEFAULT TRUE,
            config JSON,
            installed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP,
            usage_count INTEGER DEFAULT 0
        )
        """)
        
        # جدول الأنشطة المحسن
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS activities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            activity_type TEXT NOT NULL,
            description TEXT NOT NULL,
            entity_type TEXT,
            entity_id INTEGER,
            metadata JSON,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_indexes(self, cursor):
        """إنشاء فهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name)",
            "CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(type)",
            "CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)",
            "CREATE INDEX IF NOT EXISTS idx_analyses_project_id ON analyses(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_analyses_agent_name ON analyses(agent_name)",
            "CREATE INDEX IF NOT EXISTS idx_analyses_status ON analyses(status)",
            "CREATE INDEX IF NOT EXISTS idx_errors_project_id ON errors(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_errors_severity ON errors(severity)",
            "CREATE INDEX IF NOT EXISTS idx_errors_resolved ON errors(resolved)",
            "CREATE INDEX IF NOT EXISTS idx_reports_project_id ON reports(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type)",
            "CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _optimize_performance(self, cursor):
        """تحسين أداء قاعدة البيانات"""
        optimizations = [
            "PRAGMA journal_mode = WAL",
            "PRAGMA synchronous = NORMAL", 
            "PRAGMA cache_size = 10000",
            "PRAGMA temp_store = MEMORY",
            "PRAGMA mmap_size = 268435456"
        ]
        
        for optimization in optimizations:
            cursor.execute(optimization)
    
    def _insert_sample_data(self, cursor):
        """إدراج بيانات تجريبية محسنة"""
        
        # مشاريع تجريبية
        projects = [
            ("Anubis AI System", "AI/ML", "نظام أنوبيس للذكاء الاصطناعي", "/path/to/anubis", "active"),
            ("Universal Assistants", "Python", "نظام المساعدين الشاملين", "/path/to/assistants", "active"),
            ("Database Optimizer", "Database", "محسن قواعد البيانات", "/path/to/optimizer", "active")
        ]
        
        for project in projects:
            cursor.execute("""
            INSERT OR IGNORE INTO projects (name, type, description, path, status, quality_score) 
            VALUES (?, ?, ?, ?, ?, ?)
            """, (*project, 85.5))
        
        # تحليلات تجريبية
        cursor.execute("""
        INSERT OR IGNORE INTO analyses (project_id, agent_name, analysis_type, status, score, duration_seconds)
        VALUES (1, 'ErrorDetector', 'code_analysis', 'completed', 92.3, 2.5)
        """)
        
        # أخطاء تجريبية
        cursor.execute("""
        INSERT OR IGNORE INTO errors (project_id, analysis_id, error_type, severity, message, file_path)
        VALUES (1, 1, 'syntax_error', 'medium', 'Unused import statement', 'main.py')
        """)

if __name__ == "__main__":
    optimizer = AnubisDBOptimizer()
    optimizer.optimize_database()
