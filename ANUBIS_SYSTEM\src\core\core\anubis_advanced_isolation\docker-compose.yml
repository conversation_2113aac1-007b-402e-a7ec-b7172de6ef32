networks:
  anubis_main_network:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
  anubis_monitoring_network:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
secrets:
  anubis_api_secrets:
    external: true
  anubis_db_secret:
    external: true
services:
  anubis:
    build: ./containers/anubis
    container_name: anubis_container
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
    - COMPONENT_NAME=anubis
    - LOG_LEVEL=INFO
    - PYTHONPATH=/app
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 10s
    networks:
    - anubis_main_network
    ports:
    - 8000:8000
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - anubis_data:/app/data
    - shared_logs:/app/logs
  grafana:
    container_name: grafana_container
    environment:
    - GF_SECURITY_ADMIN_PASSWORD=anubis2024
    image: grafana/grafana:latest
    networks:
    - anubis_monitoring_network
    ports:
    - 3000:3000
    restart: unless-stopped
    volumes:
    - ./containers/monitoring/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro
    - monitoring_data:/var/lib/grafana
  n8n:
    build: ./containers/n8n
    container_name: n8n_container
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
    - COMPONENT_NAME=n8n
    - LOG_LEVEL=INFO
    - PYTHONPATH=/app
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8001/health
      timeout: 10s
    networks:
    - anubis_main_network
    ports:
    - 8001:8001
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - n8n_data:/app/data
    - shared_logs:/app/logs
  prometheus:
    container_name: prometheus_container
    image: prom/prometheus:latest
    networks:
    - anubis_monitoring_network
    ports:
    - 9090:9090
    restart: unless-stopped
    volumes:
    - ./containers/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    - monitoring_data:/prometheus
  tools:
    build: ./containers/tools
    container_name: tools_container
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
    - COMPONENT_NAME=tools
    - LOG_LEVEL=INFO
    - PYTHONPATH=/app
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8002/health
      timeout: 10s
    networks:
    - anubis_main_network
    ports:
    - 8002:8002
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - tools_data:/app/data
    - shared_logs:/app/logs
  universal_ai_assistants:
    build: ./containers/universal_ai_assistants
    container_name: universal_ai_assistants_container
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
    - COMPONENT_NAME=Universal-AI-Assistants
    - LOG_LEVEL=INFO
    - PYTHONPATH=/app
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8003/health
      timeout: 10s
    networks:
    - anubis_main_network
    ports:
    - 8003:8003
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - universal_ai_assistants_data:/app/data
    - shared_logs:/app/logs
version: '3.8'
volumes:
  anubis_main_data:
    driver: local
  database_data:
    driver: local
  monitoring_data:
    driver: local
  shared_logs:
    driver: local
  tools_data:
    driver: local
  universal_ai_data:
    driver: local
