version: '3.8'

# 🏺 نظام أنوبيس - حاوية أساسية بسيطة
# Anubis System - Basic Simple Container

services:
  # ===== النظام الأساسي فقط - Core System Only =====
  anubis-core:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: anubis-core
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - ANUBIS_ENV=development
      - DEBUG=true
    command: python main.py
