{"langsmith": {"enabled": true, "project_name": "anubis-ai-system", "tracing_enabled": true, "api_url": "https://api.smith.langchain.com", "environment": "development", "tags": ["anubis", "ai-agents", "ollama"], "metadata": {"system_version": "2.0", "created_at": "2025-07-16T08:25:25.847351", "description": "نظام أنوبيس للذكاء الاصطناعي"}}, "agents_integration": {"enhanced_error_detector": {"trace_enabled": true, "performance_monitoring": true}, "enhanced_project_analyzer": {"trace_enabled": true, "performance_monitoring": true}, "enhanced_file_organizer": {"trace_enabled": true, "performance_monitoring": true}, "enhanced_memory_agent": {"trace_enabled": true, "performance_monitoring": true}, "smart_ai_agent": {"trace_enabled": true, "performance_monitoring": true}, "smart_code_analyzer": {"trace_enabled": true, "performance_monitoring": true}}, "models_integration": {"ollama_models": ["llama3:8b", "mistral:7b", "phi3:mini"], "model_selection": {"auto_select": true, "performance_based": true, "fallback_model": "llama3:8b"}, "performance_tracking": {"response_time": true, "quality_metrics": true, "usage_statistics": true}}}