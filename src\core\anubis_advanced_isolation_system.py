#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام العزل المتقدم لأنوبيس
Anubis Advanced Isolation System

نظام عزل متقدم يستخدم نتائج الفحص الشامل والوكلاء لإنشاء:
- حاويات منفصلة لكل مكون
- شبكات معزولة
- أحجام منفصلة
- مراقبة متقدمة
- أمان محسن

مطور بالتعاون مع الوكلاء الذكيين
"""

import json
import os
import shutil
import sys
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class AnubisAdvancedIsolationSystem:
    """نظام العزل المتقدم لأنوبيس"""
    
    def __init__(self, scan_results_file: str = None):
        self.project_root = Path(".").resolve()
        self.isolation_timestamp = datetime.now().isoformat()
        
        # تحميل نتائج الفحص الشامل
        self.scan_results = self.load_scan_results(scan_results_file)
        
        # مجلد نظام العزل المتقدم
        self.isolation_dir = self.project_root / "anubis_advanced_isolation"
        
        # إعدادات العزل
        self.isolation_config = {
            "system_info": {
                "timestamp": self.isolation_timestamp,
                "version": "4.0",
                "type": "advanced_isolation_system",
                "based_on_scan": scan_results_file
            },
            "components": {},
            "networks": {},
            "volumes": {},
            "monitoring": {},
            "security": {}
        }
    
    def load_scan_results(self, scan_file: str = None) -> Dict:
        """تحميل نتائج الفحص الشامل"""
        if scan_file and Path(scan_file).exists():
            try:
                with open(scan_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ خطأ في تحميل نتائج الفحص: {e}")
        
        # البحث عن أحدث ملف فحص
        scan_files = list(Path(".").glob("anubis_comprehensive_scan_*.json"))
        if scan_files:
            latest_scan = max(scan_files, key=lambda x: x.stat().st_mtime)
            try:
                with open(latest_scan, 'r', encoding='utf-8') as f:
                    print(f"📄 تم تحميل نتائج الفحص من: {latest_scan}")
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ خطأ في تحميل {latest_scan}: {e}")
        
        print("⚠️ لم يتم العثور على نتائج فحص - سيتم إنشاء نظام عزل أساسي")
        return {}
    
    def create_advanced_isolation_system(self):
        """إنشاء نظام العزل المتقدم"""
        print("🏺 بدء إنشاء نظام العزل المتقدم")
        print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
        print("🔍 مبني على نتائج الفحص الشامل")
        print("=" * 70)
        
        # إنشاء هيكل المجلدات
        self.create_directory_structure()
        
        # إنشاء حاويات للمكونات المكتشفة
        self.create_component_containers()
        
        # إنشاء شبكات العزل
        self.create_isolation_networks()
        
        # إنشاء أحجام البيانات
        self.create_data_volumes()
        
        # إنشاء نظام المراقبة المتقدم
        self.create_advanced_monitoring()
        
        # إنشاء نظام الأمان
        self.create_security_system()
        
        # إنشاء ملفات Docker المتقدمة
        self.create_advanced_docker_files()
        
        # إنشاء سكريبتات الإدارة
        self.create_management_scripts()
        
        # إنشاء التوثيق
        self.create_documentation()
        
        print(f"\n✅ تم إنشاء نظام العزل المتقدم في: {self.isolation_dir}")
        return self.isolation_dir
    
    def create_directory_structure(self):
        """إنشاء هيكل المجلدات المتقدم"""
        print("\n📁 إنشاء هيكل المجلدات المتقدم...")
        
        directories = [
            # مجلدات الحاويات
            "containers",
            "containers/anubis_main",
            "containers/universal_ai",
            "containers/tools_suite",
            "containers/n8n_workflows",
            "containers/database",
            "containers/monitoring",
            "containers/security",
            
            # مجلدات الشبكات
            "networks/configs",
            "networks/security",
            "networks/monitoring",
            
            # مجلدات الأحجام
            "volumes/anubis_data",
            "volumes/universal_ai_data",
            "volumes/tools_data",
            "volumes/n8n_data",
            "volumes/database_data",
            "volumes/monitoring_data",
            "volumes/security_data",
            "volumes/shared_logs",
            "volumes/shared_configs",
            
            # مجلدات الإدارة
            "management/scripts",
            "management/configs",
            "management/monitoring",
            "management/security",
            "management/backups",
            
            # مجلدات التوثيق
            "docs/architecture",
            "docs/deployment",
            "docs/monitoring",
            "docs/security",
            "docs/troubleshooting"
        ]
        
        for directory in directories:
            (self.isolation_dir / directory).mkdir(parents=True, exist_ok=True)
            
        print(f"  ✅ تم إنشاء {len(directories)} مجلد")
    
    def create_component_containers(self):
        """إنشاء حاويات للمكونات المكتشفة"""
        print("\n🐳 إنشاء حاويات للمكونات المكتشفة...")
        
        # الحصول على المكونات من نتائج الفحص
        tools_discovered = self.scan_results.get("tools_discovered", {})
        components_analysis = self.scan_results.get("components_analysis", {})
        
        containers_created = 0
        
        for component_name, component_info in tools_discovered.items():
            container_dir = self.isolation_dir / "containers" / component_name.lower().replace("-", "_")
            container_dir.mkdir(exist_ok=True)
            
            # إنشاء Dockerfile
            self.create_component_dockerfile(component_name, component_info, container_dir)
            
            # إنشاء ملفات التكوين
            self.create_component_config(component_name, component_info, container_dir)
            
            # إنشاء سكريبت البدء
            self.create_component_startup_script(component_name, component_info, container_dir)
            
            containers_created += 1
        
        print(f"  ✅ تم إنشاء {containers_created} حاوية")
    
    def create_component_dockerfile(self, component_name: str, component_info: Dict, container_dir: Path):
        """إنشاء Dockerfile للمكون"""
        technologies = component_info.get("technologies", [])
        
        # تحديد الصورة الأساسية
        if "Python" in technologies:
            base_image = "python:3.11-slim"
            install_commands = [
                "RUN apt-get update && apt-get install -y gcc default-libmysqlclient-dev pkg-config && rm -rf /var/lib/apt/lists/*",
                "COPY requirements.txt .",
                "RUN pip install --no-cache-dir -r requirements.txt"
            ]
        elif "Node.js" in technologies:
            base_image = "node:18-alpine"
            install_commands = [
                "COPY package*.json .",
                "RUN npm ci --only=production"
            ]
        else:
            base_image = "alpine:latest"
            install_commands = ["RUN apk add --no-cache bash"]
        
        dockerfile_content = f"""# Dockerfile for {component_name}
# Generated by Anubis Advanced Isolation System
FROM {base_image}

WORKDIR /app

# Install system dependencies
{chr(10).join(install_commands)}

# Copy application code
COPY . .

# Create non-root user
RUN adduser -D -s /bin/bash {component_name.lower()[:8]}user
RUN chown -R {component_name.lower()[:8]}user:users /app
USER {component_name.lower()[:8]}user

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "main.py"]
"""
        
        with open(container_dir / "Dockerfile", 'w') as f:
            f.write(dockerfile_content)
    
    def create_component_config(self, component_name: str, component_info: Dict, container_dir: Path):
        """إنشاء ملفات التكوين للمكون"""
        
        # إنشاء requirements.txt للمكونات Python
        if "Python" in component_info.get("technologies", []):
            requirements = [
                "fastapi==0.104.1",
                "uvicorn==0.24.0",
                "mysql-connector-python==8.2.0",
                "pydantic==2.5.0",
                "requests==2.31.0",
                "prometheus-client==0.19.0",
                "python-multipart==0.0.6"
            ]
            
            with open(container_dir / "requirements.txt", 'w') as f:
                f.write('\n'.join(requirements))
        
        # إنشاء ملف التكوين العام
        config = {
            "component_name": component_name,
            "version": "1.0.0",
            "environment": "production",
            "logging": {
                "level": "INFO",
                "format": "json",
                "output": "/app/logs/app.log"
            },
            "monitoring": {
                "enabled": True,
                "metrics_port": 9090,
                "health_check_path": "/health"
            },
            "security": {
                "enable_cors": True,
                "allowed_origins": ["http://localhost"],
                "rate_limiting": True
            }
        }
        
        with open(container_dir / "config.json", 'w') as f:
            json.dump(config, f, indent=2)
    
    def create_component_startup_script(self, component_name: str, component_info: Dict, container_dir: Path):
        """إنشاء سكريبت البدء للمكون"""
        
        startup_script = f"""#!/bin/bash
# Startup script for {component_name}
# Generated by Anubis Advanced Isolation System

echo "🚀 Starting {component_name}..."

# Wait for dependencies
echo "⏳ Waiting for dependencies..."
sleep 10

# Start the application
echo "🏺 Starting {component_name} application..."
if [ -f "main.py" ]; then
    python main.py
elif [ -f "app.py" ]; then
    python app.py
elif [ -f "server.py" ]; then
    python server.py
else
    echo "⚠️ No main application file found"
    sleep infinity
fi
"""
        
        startup_file = container_dir / "start.sh"
        with open(startup_file, 'w') as f:
            f.write(startup_script)
        
        # جعل السكريبت قابل للتنفيذ
        startup_file.chmod(0o755)
    
    def create_isolation_networks(self):
        """إنشاء شبكات العزل"""
        print("\n🌐 إنشاء شبكات العزل...")
        
        networks_config = {
            "anubis_main_network": {
                "driver": "bridge",
                "subnet": "**********/16",
                "gateway": "**********",
                "isolation": "strict"
            },
            "anubis_monitoring_network": {
                "driver": "bridge", 
                "subnet": "**********/16",
                "gateway": "**********",
                "isolation": "monitoring_only"
            },
            "anubis_security_network": {
                "driver": "bridge",
                "subnet": "**********/16", 
                "gateway": "**********",
                "isolation": "security_only"
            }
        }
        
        # حفظ تكوين الشبكات
        networks_file = self.isolation_dir / "networks/configs/networks.yml"
        with open(networks_file, 'w') as f:
            yaml.dump(networks_config, f, default_flow_style=False)
        
        self.isolation_config["networks"] = networks_config
        print(f"  ✅ تم إنشاء {len(networks_config)} شبكة معزولة")
    
    def create_data_volumes(self):
        """إنشاء أحجام البيانات"""
        print("\n💾 إنشاء أحجام البيانات...")
        
        volumes_config = {
            "anubis_main_data": {
                "driver": "local",
                "type": "persistent",
                "backup_enabled": True
            },
            "universal_ai_data": {
                "driver": "local",
                "type": "persistent", 
                "backup_enabled": True
            },
            "tools_data": {
                "driver": "local",
                "type": "persistent",
                "backup_enabled": False
            },
            "database_data": {
                "driver": "local",
                "type": "persistent",
                "backup_enabled": True,
                "encryption": True
            },
            "monitoring_data": {
                "driver": "local",
                "type": "persistent",
                "backup_enabled": True
            },
            "shared_logs": {
                "driver": "local",
                "type": "temporary",
                "backup_enabled": False
            }
        }
        
        # إنشاء مجلدات الأحجام
        for volume_name in volumes_config.keys():
            volume_dir = self.isolation_dir / "volumes" / volume_name
            volume_dir.mkdir(exist_ok=True)
            
            # إنشاء ملف README لكل حجم
            readme_content = f"""# {volume_name}

هذا الحجم مخصص لبيانات {volume_name}.

## المعلومات:
- النوع: {volumes_config[volume_name]['type']}
- النسخ الاحتياطي: {volumes_config[volume_name]['backup_enabled']}
- التشفير: {volumes_config[volume_name].get('encryption', False)}

## الاستخدام:
يتم تركيب هذا الحجم تلقائياً في الحاوية المناسبة.
"""
            
            with open(volume_dir / "README.md", 'w', encoding='utf-8') as f:
                f.write(readme_content)
        
        self.isolation_config["volumes"] = volumes_config
        print(f"  ✅ تم إنشاء {len(volumes_config)} حجم بيانات")
    
    def create_advanced_monitoring(self):
        """إنشاء نظام المراقبة المتقدم"""
        print("\n📊 إنشاء نظام المراقبة المتقدم...")
        
        # تكوين Prometheus المتقدم
        prometheus_config = {
            "global": {
                "scrape_interval": "15s",
                "evaluation_interval": "15s"
            },
            "rule_files": [
                "rules/*.yml"
            ],
            "scrape_configs": [
                {
                    "job_name": "anubis_main",
                    "static_configs": [{"targets": ["anubis_main:8000"]}],
                    "metrics_path": "/metrics",
                    "scrape_interval": "10s"
                },
                {
                    "job_name": "universal_ai",
                    "static_configs": [{"targets": ["universal_ai:8001"]}],
                    "metrics_path": "/metrics",
                    "scrape_interval": "10s"
                },
                {
                    "job_name": "tools_suite",
                    "static_configs": [{"targets": ["tools_suite:8002"]}],
                    "metrics_path": "/metrics",
                    "scrape_interval": "15s"
                }
            ],
            "alerting": {
                "alertmanagers": [
                    {
                        "static_configs": [{"targets": ["alertmanager:9093"]}]
                    }
                ]
            }
        }
        
        monitoring_dir = self.isolation_dir / "containers/monitoring"
        with open(monitoring_dir / "prometheus.yml", 'w') as f:
            yaml.dump(prometheus_config, f, default_flow_style=False)
        
        # تكوين Grafana
        grafana_config = {
            "apiVersion": 1,
            "datasources": [
                {
                    "name": "Prometheus",
                    "type": "prometheus",
                    "url": "http://prometheus:9090",
                    "access": "proxy",
                    "isDefault": True
                }
            ]
        }
        
        with open(monitoring_dir / "grafana-datasources.yml", 'w') as f:
            yaml.dump(grafana_config, f, default_flow_style=False)
        
        print("  ✅ تم إنشاء نظام المراقبة المتقدم")
    
    def create_security_system(self):
        """إنشاء نظام الأمان"""
        print("\n🛡️ إنشاء نظام الأمان...")
        
        security_config = {
            "network_policies": {
                "default_deny": True,
                "allowed_connections": [
                    {"from": "anubis_main", "to": "database", "port": 3306},
                    {"from": "universal_ai", "to": "database", "port": 3306},
                    {"from": "monitoring", "to": "*", "port": "metrics"}
                ]
            },
            "container_security": {
                "non_root_users": True,
                "read_only_filesystem": True,
                "no_new_privileges": True,
                "security_opt": ["no-new-privileges:true"]
            },
            "secrets_management": {
                "database_password": "anubis_db_secret",
                "api_keys": "anubis_api_secrets",
                "certificates": "anubis_tls_certs"
            }
        }
        
        security_dir = self.isolation_dir / "containers/security"
        with open(security_dir / "security-config.yml", 'w') as f:
            yaml.dump(security_config, f, default_flow_style=False)
        
        self.isolation_config["security"] = security_config
        print("  ✅ تم إنشاء نظام الأمان")
    
    def create_advanced_docker_files(self):
        """إنشاء ملفات Docker المتقدمة"""
        print("\n🐳 إنشاء ملفات Docker المتقدمة...")
        
        # الحصول على المكونات من نتائج الفحص
        tools_discovered = self.scan_results.get("tools_discovered", {})
        
        # إنشاء docker-compose.yml متقدم
        compose_config = {
            "version": "3.8",
            "services": {},
            "networks": {
                "anubis_main_network": {
                    "driver": "bridge",
                    "ipam": {
                        "config": [{"subnet": "**********/16"}]
                    }
                },
                "anubis_monitoring_network": {
                    "driver": "bridge",
                    "ipam": {
                        "config": [{"subnet": "**********/16"}]
                    }
                }
            },
            "volumes": {
                "anubis_main_data": {"driver": "local"},
                "universal_ai_data": {"driver": "local"},
                "tools_data": {"driver": "local"},
                "database_data": {"driver": "local"},
                "monitoring_data": {"driver": "local"},
                "shared_logs": {"driver": "local"}
            },
            "secrets": {
                "anubis_db_secret": {
                    "external": True
                },
                "anubis_api_secrets": {
                    "external": True
                }
            }
        }
        
        # إضافة خدمات للمكونات المكتشفة
        port_counter = 8000
        for component_name, component_info in tools_discovered.items():
            service_name = component_name.lower().replace("-", "_")
            
            service_config = {
                "build": f"./containers/{service_name}",
                "container_name": f"{service_name}_container",
                "ports": [f"{port_counter}:{port_counter}"],
                "volumes": [
                    f"{service_name}_data:/app/data",
                    "shared_logs:/app/logs"
                ],
                "networks": ["anubis_main_network"],
                "restart": "unless-stopped",
                "deploy": {
                    "resources": {
                        "limits": {"cpus": "1.0", "memory": "1G"},
                        "reservations": {"cpus": "0.5", "memory": "512M"}
                    }
                },
                "security_opt": ["no-new-privileges:true"],
                "read_only": False,
                "environment": [
                    f"COMPONENT_NAME={component_name}",
                    "LOG_LEVEL=INFO",
                    "PYTHONPATH=/app"
                ],
                "healthcheck": {
                    "test": [f"CMD", "curl", "-f", f"http://localhost:{port_counter}/health"],
                    "interval": "30s",
                    "timeout": "10s",
                    "retries": 3,
                    "start_period": "40s"
                }
            }
            
            compose_config["services"][service_name] = service_config
            port_counter += 1
        
        # إضافة خدمات المراقبة
        compose_config["services"]["prometheus"] = {
            "image": "prom/prometheus:latest",
            "container_name": "prometheus_container",
            "ports": ["9090:9090"],
            "volumes": [
                "./containers/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro",
                "monitoring_data:/prometheus"
            ],
            "networks": ["anubis_monitoring_network"],
            "restart": "unless-stopped"
        }
        
        compose_config["services"]["grafana"] = {
            "image": "grafana/grafana:latest",
            "container_name": "grafana_container",
            "ports": ["3000:3000"],
            "volumes": [
                "./containers/monitoring/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro",
                "monitoring_data:/var/lib/grafana"
            ],
            "networks": ["anubis_monitoring_network"],
            "environment": ["GF_SECURITY_ADMIN_PASSWORD=anubis2024"],
            "restart": "unless-stopped"
        }
        
        # حفظ docker-compose.yml
        with open(self.isolation_dir / "docker-compose.yml", 'w') as f:
            yaml.dump(compose_config, f, default_flow_style=False)
        
        print(f"  ✅ تم إنشاء docker-compose.yml مع {len(compose_config['services'])} خدمة")
    
    def create_management_scripts(self):
        """إنشاء سكريبتات الإدارة"""
        print("\n📜 إنشاء سكريبتات الإدارة...")
        
        scripts_dir = self.isolation_dir / "management/scripts"
        
        # سكريبت البدء المتقدم
        start_script = """#!/bin/bash
# Advanced Anubis Isolation System - Start Script
echo "🏺 بدء تشغيل نظام العزل المتقدم لأنوبيس..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت"
    exit 1
fi

# Create secrets
echo "🔐 إنشاء الأسرار..."
echo "**********" | docker secret create anubis_db_secret - 2>/dev/null || true
echo "anubis_api_key_2024" | docker secret create anubis_api_secrets - 2>/dev/null || true

# Start services
echo "🚀 بدء تشغيل الخدمات..."
docker-compose up -d

# Wait for services
echo "⏳ انتظار بدء الخدمات..."
sleep 30

# Check health
echo "🏥 فحص صحة الخدمات..."
docker-compose ps

echo "✅ تم تشغيل نظام العزل المتقدم بنجاح!"
echo "🌐 الخدمات متاحة على:"
echo "  - Grafana: http://localhost:3000 (admin/anubis2024)"
echo "  - Prometheus: http://localhost:9090"
"""
        
        with open(scripts_dir / "start.sh", 'w') as f:
            f.write(start_script)
        
        # سكريبت الإيقاف
        stop_script = """#!/bin/bash
# Advanced Anubis Isolation System - Stop Script
echo "🛑 إيقاف نظام العزل المتقدم لأنوبيس..."

docker-compose down
echo "✅ تم إيقاف جميع الخدمات"
"""
        
        with open(scripts_dir / "stop.sh", 'w') as f:
            f.write(stop_script)
        
        # سكريبت المراقبة
        monitor_script = """#!/bin/bash
# Advanced Anubis Isolation System - Monitor Script
echo "📊 حالة نظام العزل المتقدم:"
echo "================================"

echo "🐳 حالة الحاويات:"
docker-compose ps

echo ""
echo "📈 استهلاك الموارد:"
docker stats --no-stream

echo ""
echo "🌐 الشبكات:"
docker network ls | grep anubis

echo ""
echo "💾 الأحجام:"
docker volume ls | grep anubis
"""
        
        with open(scripts_dir / "monitor.sh", 'w') as f:
            f.write(monitor_script)
        
        # جعل السكريبتات قابلة للتنفيذ
        for script in ["start.sh", "stop.sh", "monitor.sh"]:
            (scripts_dir / script).chmod(0o755)
        
        print("  ✅ تم إنشاء سكريبتات الإدارة")
    
    def create_documentation(self):
        """إنشاء التوثيق"""
        print("\n📚 إنشاء التوثيق...")
        
        # دليل المعمارية
        architecture_doc = """# 🏺 معمارية نظام العزل المتقدم لأنوبيس

## نظرة عامة
نظام عزل متقدم يستخدم Docker لعزل كل مكون في حاوية منفصلة مع شبكات وأحجام معزولة.

## المكونات الرئيسية

### 🐳 الحاويات
- **anubis_main**: النظام الرئيسي لأنوبيس
- **universal_ai**: نظام Universal AI Assistants
- **tools_suite**: مجموعة الأدوات المساعدة
- **n8n_workflows**: أدوات سير العمل
- **prometheus**: مراقبة المقاييس
- **grafana**: لوحات التحكم

### 🌐 الشبكات
- **anubis_main_network**: الشبكة الرئيسية (**********/16)
- **anubis_monitoring_network**: شبكة المراقبة (**********/16)
- **anubis_security_network**: شبكة الأمان (**********/16)

### 💾 الأحجام
- **anubis_main_data**: بيانات النظام الرئيسي
- **universal_ai_data**: بيانات Universal AI
- **tools_data**: بيانات الأدوات
- **database_data**: بيانات قاعدة البيانات (مشفرة)
- **monitoring_data**: بيانات المراقبة
- **shared_logs**: السجلات المشتركة

## الأمان
- مستخدمين غير جذر في جميع الحاويات
- شبكات معزولة مع سياسات وصول محددة
- أسرار مشفرة لكلمات المرور والمفاتيح
- نظام ملفات للقراءة فقط حيث أمكن

## المراقبة
- Prometheus لجمع المقاييس
- Grafana للوحات التحكم
- فحص صحة تلقائي لجميع الخدمات
- تنبيهات للمشاكل الحرجة
"""
        
        docs_dir = self.isolation_dir / "docs"
        with open(docs_dir / "architecture/README.md", 'w', encoding='utf-8') as f:
            f.write(architecture_doc)
        
        print("  ✅ تم إنشاء التوثيق")
    
    def save_isolation_config(self, filename: str = None) -> str:
        """حفظ تكوين العزل"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"anubis_advanced_isolation_config_{timestamp}.json"
        
        config_file = self.isolation_dir / filename
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.isolation_config, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تكوين العزل في: {config_file}")
        return str(config_file)


def main():
    """الدالة الرئيسية"""
    print("🏺 نظام العزل المتقدم لأنوبيس")
    print("🤖 مطور بالتعاون مع الوكلاء الذكيين")
    
    # إنشاء نظام العزل المتقدم
    isolation_system = AnubisAdvancedIsolationSystem()
    
    # إنشاء النظام
    isolation_dir = isolation_system.create_advanced_isolation_system()
    
    # حفظ التكوين
    config_file = isolation_system.save_isolation_config()
    
    print("\n" + "="*70)
    print("🎉 تم إنشاء نظام العزل المتقدم بنجاح!")
    print(f"📁 المجلد: {isolation_dir}")
    print(f"⚙️ التكوين: {config_file}")
    print("\n🚀 للبدء:")
    print(f"  cd {isolation_dir}")
    print("  ./management/scripts/start.sh")
    print("="*70)


if __name__ == "__main__":
    main()
