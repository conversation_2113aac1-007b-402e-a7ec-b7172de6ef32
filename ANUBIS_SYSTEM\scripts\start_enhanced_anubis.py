#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 بدء تشغيل نظام أنوبيس المحسن
Anubis Enhanced System Startup

سكريبت شامل لبدء تشغيل النظام المحسن مع قاعدة البيانات
"""

import os
import sys
import time
import json
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime

class AnubisEnhancedStarter:
    """فئة بدء تشغيل النظام المحسن"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.docker_available = False
        self.mysql_available = False
        
    def print_banner(self):
        """طباعة شعار النظام"""
        banner = """
🏺 ═══════════════════════════════════════════════════════════════════════════════
🏺                          نظام أنوبيس المحسن v2.0                           
🏺                      Anubis Enhanced System v2.0                          
🏺 ═══════════════════════════════════════════════════════════════════════════════
🏺 
🏺 الميزات الجديدة:
🏺 ✅ قاعدة بيانات MySQL مع pool الاتصالات
🏺 ✅ API محسن v2.0 مع endpoints جديدة  
🏺 ✅ إدارة المستخدمين والمصادقة
🏺 ✅ جلسات الذكاء الاصطناعي المتقدمة
🏺 ✅ مراقبة وإحصائيات شاملة
🏺 ✅ نظام السجلات المحسن
🏺 
🏺 ═══════════════════════════════════════════════════════════════════════════════
        """
        print(banner)
    
    def check_requirements(self):
        """فحص المتطلبات"""
        print("🔍 فحص المتطلبات...")
        
        # فحص Python
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        else:
            print(f"❌ Python {python_version.major}.{python_version.minor} - مطلوب Python 3.8+")
            return False
        
        # فحص Docker
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Docker متاح")
                self.docker_available = True
            else:
                print("⚠️ Docker غير متاح - سيتم استخدام SQLite فقط")
        except:
            print("⚠️ Docker غير متاح - سيتم استخدام SQLite فقط")
        
        # فحص MySQL (إذا كان Docker متاحاً)
        if self.docker_available:
            try:
                result = subprocess.run(['docker', 'ps'], 
                                      capture_output=True, text=True, timeout=10)
                if 'mysql' in result.stdout.lower():
                    print("✅ MySQL container يعمل")
                    self.mysql_available = True
                else:
                    print("⚠️ MySQL container غير متاح")
            except:
                print("⚠️ لا يمكن فحص حالة MySQL")
        
        # فحص ملفات المشروع
        required_files = [
            'main.py',
            'src/core/main.py',
            'src/data_management/database_manager.py',
            'src/core/enhanced_endpoints.py',
            'requirements.txt'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            return False
        else:
            print("✅ جميع الملفات المطلوبة موجودة")
        
        return True
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء المجلدات...")
        
        directories = [
            'data',
            'data/database',
            'data/mysql_init',
            'logs',
            'logs/mysql',
            'logs/nginx',
            'config',
            'ssl'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
    
    def install_dependencies(self):
        """تثبيت المتطلبات"""
        print("📦 تثبيت المتطلبات...")
        
        try:
            # تحديث pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                         check=True, capture_output=True)
            
            # تثبيت المتطلبات
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         check=True, capture_output=True)
            
            print("✅ تم تثبيت جميع المتطلبات")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def start_docker_services(self):
        """بدء خدمات Docker"""
        if not self.docker_available:
            print("⚠️ تخطي خدمات Docker - غير متاح")
            return True
        
        print("🐳 بدء خدمات Docker...")
        
        # فحص وجود docker-compose-enhanced.yml
        compose_file = self.project_root / 'docker-compose-enhanced.yml'
        if not compose_file.exists():
            print("⚠️ ملف docker-compose-enhanced.yml غير موجود")
            return False
        
        try:
            # بدء خدمة MySQL فقط أولاً
            subprocess.run([
                'docker-compose', '-f', 'docker-compose-enhanced.yml',
                'up', '-d', 'anubis-mysql'
            ], check=True, capture_output=True)
            
            print("✅ تم بدء خدمة MySQL")
            
            # انتظار تهيئة MySQL
            print("⏳ انتظار تهيئة MySQL...")
            time.sleep(30)
            
            self.mysql_available = True
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في بدء خدمات Docker: {e}")
            return False
    
    def start_anubis_system(self):
        """بدء نظام أنوبيس"""
        print("🏺 بدء نظام أنوبيس المحسن...")
        
        # إعداد متغيرات البيئة
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.project_root)
        
        if self.mysql_available:
            env['MYSQL_HOST'] = 'localhost'
            env['MYSQL_PORT'] = '3306'
            env['MYSQL_USER'] = 'anubis_user'
            env['MYSQL_PASSWORD'] = '2452329511'
            env['MYSQL_DATABASE'] = 'anubis_system'
        
        try:
            # بدء النظام
            print("🚀 تشغيل النظام على http://localhost:8000")
            
            # تشغيل النظام في الخلفية
            process = subprocess.Popen([
                sys.executable, 'main.py'
            ], env=env, cwd=self.project_root)
            
            # انتظار بدء التشغيل
            time.sleep(5)
            
            # فحص حالة العملية
            if process.poll() is None:
                print("✅ النظام يعمل بنجاح!")
                return process
            else:
                print("❌ فشل في بدء النظام")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في بدء النظام: {e}")
            return None
    
    async def test_system(self):
        """اختبار النظام"""
        print("🧪 اختبار النظام...")
        
        try:
            # استيراد واستخدام أداة الاختبار
            from test_enhanced_system import AnubisEnhancedTester
            
            async with AnubisEnhancedTester() as tester:
                # اختبار أساسي سريع
                await tester.test_basic_endpoints()
                
                # فحص النتائج
                successful_tests = sum(1 for result in tester.test_results if result['success'])
                total_tests = len(tester.test_results)
                
                if successful_tests == total_tests:
                    print(f"✅ جميع الاختبارات الأساسية نجحت ({successful_tests}/{total_tests})")
                    return True
                else:
                    print(f"⚠️ بعض الاختبارات فشلت ({successful_tests}/{total_tests})")
                    return False
                    
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def create_startup_info(self):
        """إنشاء ملف معلومات بدء التشغيل"""
        startup_info = {
            "system_name": "Anubis Enhanced System",
            "version": "2.0.0",
            "startup_time": datetime.now().isoformat(),
            "features": {
                "mysql_database": self.mysql_available,
                "docker_services": self.docker_available,
                "enhanced_api": True,
                "user_management": True,
                "ai_sessions": True,
                "monitoring": True
            },
            "endpoints": {
                "main": "http://localhost:8000",
                "docs": "http://localhost:8000/docs",
                "health": "http://localhost:8000/health",
                "status": "http://localhost:8000/status",
                "database_status": "http://localhost:8000/api/v2/database/status",
                "system_stats": "http://localhost:8000/api/v2/system/stats"
            },
            "database": {
                "mysql": {
                    "available": self.mysql_available,
                    "host": "localhost",
                    "port": 3306,
                    "database": "anubis_system"
                },
                "sqlite": {
                    "available": True,
                    "path": "data/anubis.db"
                }
            }
        }
        
        with open('startup_info.json', 'w', encoding='utf-8') as f:
            json.dump(startup_info, f, ensure_ascii=False, indent=2)
        
        print("📝 تم حفظ معلومات بدء التشغيل في startup_info.json")
    
    def print_success_message(self):
        """طباعة رسالة النجاح"""
        success_message = f"""
🎉 ═══════════════════════════════════════════════════════════════════════════════
🎉                        تم بدء النظام بنجاح!                               
🎉                     System Started Successfully!                          
🎉 ═══════════════════════════════════════════════════════════════════════════════

🌐 الروابط المهمة:
   📱 الصفحة الرئيسية: http://localhost:8000
   📚 توثيق API: http://localhost:8000/docs
   💚 فحص الصحة: http://localhost:8000/health
   📊 حالة النظام: http://localhost:8000/status
   🗄️ حالة قاعدة البيانات: http://localhost:8000/api/v2/database/status
   📈 إحصائيات النظام: http://localhost:8000/api/v2/system/stats

🔧 الميزات المتاحة:
   {'✅' if self.mysql_available else '❌'} قاعدة بيانات MySQL
   {'✅' if self.docker_available else '❌'} خدمات Docker
   ✅ API محسن v2.0
   ✅ إدارة المستخدمين
   ✅ جلسات الذكاء الاصطناعي
   ✅ مراقبة وإحصائيات

📝 لإيقاف النظام: Ctrl+C
📋 لاختبار شامل: python test_enhanced_system.py

🏺 ═══════════════════════════════════════════════════════════════════════════════
        """
        print(success_message)
    
    async def run_startup_sequence(self):
        """تشغيل تسلسل بدء التشغيل"""
        self.print_banner()
        
        # فحص المتطلبات
        if not self.check_requirements():
            print("❌ فشل في فحص المتطلبات")
            return False
        
        # إنشاء المجلدات
        self.setup_directories()
        
        # تثبيت المتطلبات
        if not self.install_dependencies():
            print("❌ فشل في تثبيت المتطلبات")
            return False
        
        # بدء خدمات Docker
        self.start_docker_services()
        
        # بدء نظام أنوبيس
        process = self.start_anubis_system()
        if not process:
            print("❌ فشل في بدء النظام")
            return False
        
        # اختبار النظام
        await self.test_system()
        
        # إنشاء ملف معلومات بدء التشغيل
        self.create_startup_info()
        
        # طباعة رسالة النجاح
        self.print_success_message()
        
        return True

async def main():
    """الدالة الرئيسية"""
    starter = AnubisEnhancedStarter()
    
    try:
        success = await starter.run_startup_sequence()
        
        if success:
            print("\n⏳ النظام يعمل... اضغط Ctrl+C للإيقاف")
            
            # انتظار إيقاف النظام
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("\n🔒 إيقاف النظام...")
                return 0
        else:
            print("\n❌ فشل في بدء النظام")
            return 1
            
    except KeyboardInterrupt:
        print("\n🔒 تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
