#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Quick Start - VS Code Process Control Dashboard
================================================
تشغيل سريع للوحة التحكم مع فحص المتطلبات

الاستخدام:
python quick_start.py
"""

import importlib
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")

    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False

    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_and_install_packages():
    """فحص وتثبيت المكتبات المطلوبة"""
    required_packages = {"tkinter": "tkinter", "psutil": "psutil"}

    missing_packages = []

    print("\n📦 فحص المكتبات المطلوبة...")

    for package_name, import_name in required_packages.items():
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - غير مثبت")
            missing_packages.append(package_name)

    if missing_packages:
        print(f"\n📥 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")

        for package in missing_packages:
            if package == "tkinter":
                print("⚠️  tkinter يأتي مع Python عادة")
                print("   إذا لم يعمل، قم بإعادة تثبيت Python مع tkinter")
                continue

            try:
                print(f"📥 تثبيت {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False

    return True


def check_vscode():
    """فحص وجود VS Code"""
    print("\n🔍 فحص VS Code...")

    try:
        result = subprocess.run(["code", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip().split("\n")[0]
            print(f"✅ VS Code {version}")
            return True
        else:
            print("❌ VS Code غير متاح في PATH")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ VS Code غير مثبت أو غير متاح")
        print("💡 تأكد من:")
        print("   - تثبيت VS Code")
        print("   - إضافة VS Code إلى PATH")
        return False


def check_dashboard_file():
    """فحص وجود ملف لوحة التحكم"""
    print("\n📄 فحص ملف لوحة التحكم...")

    dashboard_file = Path("process_control_dashboard.py")

    if dashboard_file.exists():
        print("✅ ملف لوحة التحكم موجود")
        return True
    else:
        print("❌ ملف لوحة التحكم غير موجود")
        print("💡 تأكد من وجود: process_control_dashboard.py")
        return False


def run_dashboard():
    """تشغيل لوحة التحكم"""
    print("\n🎛️ تشغيل لوحة التحكم...")
    print("=" * 50)

    try:
        # تشغيل لوحة التحكم
        subprocess.run([sys.executable, "process_control_dashboard.py"])
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")


def main():
    """الدالة الرئيسية"""
    print("🎛️ VS Code Process Control Dashboard")
    print("=" * 50)
    print("🚀 فحص المتطلبات وتشغيل لوحة التحكم")
    print("=" * 50)

    # فحص Python
    if not check_python_version():
        input("\n❌ اضغط Enter للخروج...")
        return

    # فحص المكتبات
    if not check_and_install_packages():
        input("\n❌ اضغط Enter للخروج...")
        return

    # فحص VS Code (اختياري)
    vscode_available = check_vscode()
    if not vscode_available:
        print("⚠️  بعض المميزات قد لا تعمل بدون VS Code")
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() not in ["y", "yes", "نعم"]:
            return

    # فحص ملف لوحة التحكم
    if not check_dashboard_file():
        input("\n❌ اضغط Enter للخروج...")
        return

    print("\n✅ جميع المتطلبات متوفرة!")
    print("🎛️ بدء تشغيل لوحة التحكم...")

    # تشغيل لوحة التحكم
    run_dashboard()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
