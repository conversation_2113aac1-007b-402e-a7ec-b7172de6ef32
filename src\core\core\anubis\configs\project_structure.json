{"project_name": "Anubis AI System", "version": "1.0.0", "root_path": ".", "main_directories": {"anubis": {"path": "anubis/", "description": "المجلد الرئيسي لنظام أنوبيس", "subdirectories": {"agents": {"path": "anubis/agents/", "description": "وكلاء الذكاء الاصطناعي", "files": ["__init__.py", "README.md"]}, "api": {"path": "anubis/api/", "description": "خادم API وواجهات التطبيق", "files": ["__init__.py", "anubis_api_server.py", "anubis_openapi.json"]}, "core": {"path": "anubis/core/", "description": "المكونات الأساسية للنظام", "files": ["__init__.py", "ai_integration.py", "assistant_system.py", "base_agent.py", "config_manager.py", "langsmith_wrapper.py", "logger.py", "README.md"]}, "database": {"path": "anubis/database/", "description": "نظام قاعدة البيانات", "subdirectories": {"core": "anubis/database/core/", "docs": "anubis/database/docs/", "setup": "anubis/database/setup/", "tests": "anubis/database/tests/"}, "files": ["__init__.py", "anubis_database.py", "simple_validation.py", "README.md"]}, "configs": {"path": "anubis/configs/", "description": "ملفات التكوين", "files": ["ai_config.json", "database_config.json", "default_config.json", "langsmith_config.json", "memory.json", "openapi.json", "system_paths.json", "README.md"]}, "docs": {"path": "anubis/docs/", "description": "التوثيق والأدلة", "files": ["README.md", "user_guide.md", "developer_guide.md", "installation_guide.md", "requirements.txt", "requirements_database.txt"]}, "scripts": {"path": "anubis/scripts/", "description": "سكريبتات المساعدة والأدوات", "files": ["__init__.py", "quick_start.py", "check_ready.py", "README.md"]}, "tests": {"path": "anubis/tests/", "description": "اختبارات النظام", "subdirectories": {"configs": "anubis/tests/configs/", "workspace": "anubis/tests/workspace/"}, "files": ["__init__.py", "run_all_tests.py", "test_anubis_system.py", "README.md"]}, "workspace": {"path": "anubis/workspace/", "description": "مساحة العمل والتقارير", "subdirectories": {"backups": "anubis/workspace/backups/", "collaboration_logs": "anubis/workspace/collaboration_logs/", "knowledge_base": "anubis/workspace/knowledge_base/", "logs": "anubis/workspace/logs/", "reports": "anubis/workspace/reports/", "shared_memory": "anubis/workspace/shared_memory/"}, "files": ["README.md"]}}, "main_files": ["__init__.py", "main.py", "README.md", "INDEX.md", "PROJECT_README.md"]}, "tools": {"path": "tools/", "description": "أدوات مساعدة خارجية", "subdirectories": {"vscode-optimizer": "tools/vscode-optimizer/", "emergency": "tools/emergency/"}}, "n8n": {"path": "n8n/", "description": "تكامل N8N", "subdirectories": {"credentials": "n8n/credentials/", "nodes": "n8n/nodes/", "workflows": "n8n/workflows/"}}}, "entry_points": {"main_system": "anubis/main.py", "api_server": "anubis/api/anubis_api_server.py", "quick_client": "anubis_quick_client.py", "tests": "anubis/tests/run_all_tests.py"}, "configuration_files": {"main_config": "anubis/configs/default_config.json", "ai_config": "anubis/configs/ai_config.json", "database_config": "anubis/configs/database_config.json", "langsmith_config": "anubis/configs/langsmith_config.json", "project_structure": "anubis/configs/project_structure.json"}, "requirements": {"main": "anubis/docs/requirements.txt", "database": "anubis/docs/requirements_database.txt"}, "missing_components": {"agents": ["anubis/agents/database_agent.py", "anubis/agents/enhanced_error_detector.py", "anubis/agents/enhanced_file_organizer.py", "anubis/agents/enhanced_memory_agent.py", "anubis/agents/enhanced_project_analyzer.py", "anubis/agents/smart_ai_agent.py", "anubis/agents/smart_code_analyzer.py"], "core_missing": [], "api_missing": [], "database_missing": []}, "backup_directories": ["anubis/api.backup_20250718_160241/", "anubis/core.backup_20250718_160241/", "anubis/database.backup_20250718_160241/", "anubis/examples.backup_20250718_160241/", "anubis/plugins.backup_20250718_160241/", "anubis/templates.backup_20250718_160241/", "anubis/tests.backup_20250718_160241/"], "status": {"api_ready": true, "core_ready": true, "database_ready": true, "agents_missing": true, "tests_ready": true, "docs_ready": true}}