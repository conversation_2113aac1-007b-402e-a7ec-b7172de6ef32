#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 الوكيل الذكي المحسن بالذكاء الاصطناعي
Smart AI-Enhanced Agent

وكيل ذكي يستخدم نماذج الذكاء الاصطناعي لتحليل وتحسين المشاريع
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# إضافة مجلد core إلى المسار
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
    from ai_integration import ai_manager
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent
    from core.ai_integration import ai_manager


class SmartAIAgent(BaseAgent):
    """وكيل ذكي محسن بالذكاء الاصطناعي"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "smart_ai"
    
    def initialize_agent(self):
        """تهيئة الوكيل الذكي"""
        # إعدادات الذكاء الاصطناعي
        self.ai_features = {
            'code_analysis': True,
            'project_insights': True,
            'smart_suggestions': True,
            'auto_optimization': False,
            'context_learning': True
        }
        
        # ذاكرة السياق
        self.context_memory = {}
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'ai_requests': 0,
            'successful_analyses': 0,
            'suggestions_generated': 0,
            'errors': 0
        }
        
        self.log_action("تهيئة الوكيل الذكي", "تم تفعيل جميع الميزات الذكية")
    
    def analyze_project_with_ai(self, project_path: str = None) -> Dict[str, Any]:
        """تحليل المشروع باستخدام الذكاء الاصطناعي"""
        if not self.is_ai_enabled():
            return {
                'status': 'error',
                'message': 'الذكاء الاصطناعي غير متاح',
                'analysis': {}
            }
        
        target_path = Path(project_path) if project_path else self.project_path
        
        # جمع معلومات المشروع
        project_info = self._gather_project_info(target_path)
        
        # تحليل ذكي للمشروع
        analysis_prompt = f"""
        قم بتحليل هذا المشروع البرمجي بشكل شامل:
        
        معلومات المشروع:
        - المسار: {project_info['path']}
        - النوع: {project_info['type']}
        - عدد الملفات: {project_info['files_count']}
        - اللغات المستخدمة: {', '.join(project_info['languages'])}
        
        هيكل المشروع:
        {json.dumps(project_info['structure'], ensure_ascii=False, indent=2)}
        
        قدم تحليلاً شاملاً يتضمن:
        1. تقييم جودة الكود
        2. نقاط القوة والضعف
        3. اقتراحات للتحسين
        4. أفضل الممارسات المفقودة
        5. توصيات للأمان والأداء
        """
        
        ai_analysis = self.get_ai_analysis(analysis_prompt, project_info)
        
        # تحديث الإحصائيات
        self.usage_stats['ai_requests'] += 1
        if ai_analysis and not ai_analysis.startswith('خطأ'):
            self.usage_stats['successful_analyses'] += 1
        else:
            self.usage_stats['errors'] += 1
        
        return {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'project_info': project_info,
            'ai_analysis': ai_analysis,
            'usage_stats': self.usage_stats.copy()
        }
    
    def get_smart_code_review(self, file_path: str) -> Dict[str, Any]:
        """مراجعة ذكية للكود"""
        if not self.is_ai_enabled():
            return {'error': 'الذكاء الاصطناعي غير متاح'}
        
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return {'error': f'الملف غير موجود: {file_path}'}
            
            # قراءة الكود
            with open(file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
            
            # تحليل الكود
            review_prompt = f"""
            قم بمراجعة هذا الكود بشكل شامل:
            
            اسم الملف: {file_path.name}
            نوع الملف: {file_path.suffix}
            حجم الملف: {len(code_content)} حرف
            
            الكود:
            ```{file_path.suffix[1:] if file_path.suffix else 'text'}
            {code_content}
            ```
            
            قدم مراجعة شاملة تتضمن:
            1. تحليل جودة الكود
            2. الأخطاء المحتملة
            3. مشاكل الأمان
            4. اقتراحات للتحسين
            5. أفضل الممارسات
            6. تحسينات الأداء
            """
            
            ai_review = self.get_ai_analysis(review_prompt, {
                'file_path': str(file_path),
                'file_size': len(code_content),
                'file_type': file_path.suffix
            })
            
            return {
                'status': 'success',
                'file_path': str(file_path),
                'file_info': {
                    'name': file_path.name,
                    'size': len(code_content),
                    'type': file_path.suffix,
                    'lines': len(code_content.split('\n'))
                },
                'ai_review': ai_review,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.usage_stats['errors'] += 1
            return {'error': f'خطأ في مراجعة الكود: {e}'}
    
    def generate_optimization_plan(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """إنتاج خطة تحسين ذكية"""
        if not self.is_ai_enabled():
            return {'error': 'الذكاء الاصطناعي غير متاح'}
        
        optimization_prompt = f"""
        بناءً على تحليل المشروع التالي، قم بإنتاج خطة تحسين شاملة:
        
        بيانات التحليل:
        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}
        
        قدم خطة تحسين مفصلة تتضمن:
        1. الأولويات (عالية، متوسطة، منخفضة)
        2. خطوات التنفيذ المحددة
        3. الأدوات المطلوبة
        4. التوقيت المقدر
        5. المخاطر المحتملة
        6. مقاييس النجاح
        
        اجعل الخطة عملية وقابلة للتنفيذ.
        """
        
        optimization_plan = self.get_ai_analysis(optimization_prompt, analysis_data)
        
        return {
            'status': 'success',
            'optimization_plan': optimization_plan,
            'generated_at': datetime.now().isoformat(),
            'based_on': 'ai_analysis'
        }
    
    def learn_from_context(self, context_data: Dict[str, Any]):
        """تعلم من السياق لتحسين الأداء المستقبلي"""
        context_key = f"context_{datetime.now().strftime('%Y%m%d')}"
        
        if context_key not in self.context_memory:
            self.context_memory[context_key] = []
        
        self.context_memory[context_key].append({
            'timestamp': datetime.now().isoformat(),
            'data': context_data
        })
        
        # الاحتفاظ بآخر 100 سياق فقط
        if len(self.context_memory[context_key]) > 100:
            self.context_memory[context_key] = self.context_memory[context_key][-100:]
        
        self.log_action("تعلم السياق", f"تم حفظ سياق جديد: {context_key}")
    
    def get_contextual_suggestions(self, current_task: str) -> List[str]:
        """الحصول على اقتراحات بناءً على السياق المحفوظ"""
        if not self.is_ai_enabled():
            return ["الذكاء الاصطناعي غير متاح"]
        
        # جمع السياق ذي الصلة
        relevant_context = []
        for contexts in self.context_memory.values():
            relevant_context.extend(contexts[-10:])  # آخر 10 سياقات من كل يوم
        
        if not relevant_context:
            return self.get_smart_suggestions({'task': current_task})
        
        context_prompt = f"""
        بناءً على المهمة الحالية والسياق السابق، قدم اقتراحات ذكية:
        
        المهمة الحالية: {current_task}
        
        السياق السابق:
        {json.dumps(relevant_context[-20:], ensure_ascii=False, indent=2)}
        
        قدم اقتراحات محددة وعملية تأخذ في الاعتبار الخبرة السابقة.
        """
        
        suggestions_text = self.get_ai_analysis(context_prompt)
        
        # استخراج الاقتراحات
        suggestions = []
        lines = suggestions_text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith(('-', '•', '*', '1.', '2.', '3.')):
                clean_suggestion = line.lstrip('-•*123456789. ').strip()
                if clean_suggestion and len(clean_suggestion) > 5:
                    suggestions.append(clean_suggestion)
        
        self.usage_stats['suggestions_generated'] += len(suggestions)
        return suggestions[:15]  # أقصى 15 اقتراح
    
    def _gather_project_info(self, project_path: Path) -> Dict[str, Any]:
        """جمع معلومات شاملة عن المشروع"""
        info = {
            'path': str(project_path),
            'name': project_path.name,
            'type': 'unknown',
            'files_count': 0,
            'languages': [],
            'structure': {},
            'size_mb': 0
        }
        
        try:
            # حساب الملفات واللغات
            file_extensions = {}
            total_size = 0
            
            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    info['files_count'] += 1
                    total_size += file_path.stat().st_size
                    
                    ext = file_path.suffix.lower()
                    if ext:
                        file_extensions[ext] = file_extensions.get(ext, 0) + 1
            
            info['size_mb'] = round(total_size / (1024 * 1024), 2)
            
            # تحديد اللغات
            language_map = {
                '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
                '.java': 'Java', '.cpp': 'C++', '.c': 'C', '.cs': 'C#',
                '.php': 'PHP', '.rb': 'Ruby', '.go': 'Go', '.rs': 'Rust',
                '.html': 'HTML', '.css': 'CSS', '.sql': 'SQL',
                '.json': 'JSON', '.xml': 'XML', '.yaml': 'YAML', '.yml': 'YAML'
            }
            
            for ext, count in file_extensions.items():
                if ext in language_map:
                    info['languages'].append(f"{language_map[ext]} ({count})")
            
            # تحديد نوع المشروع
            if '.py' in file_extensions:
                info['type'] = 'Python Project'
            elif '.js' in file_extensions or '.ts' in file_extensions:
                info['type'] = 'JavaScript/TypeScript Project'
            elif '.java' in file_extensions:
                info['type'] = 'Java Project'
            
            # هيكل المشروع (مبسط)
            info['structure'] = {
                'directories': len([p for p in project_path.rglob('*') if p.is_dir()]),
                'files_by_type': file_extensions
            }
            
        except Exception as e:
            self.log_action("خطأ في جمع معلومات المشروع", str(e))
        
        return info
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        return {
            'agent_type': self.agent_type,
            'ai_enabled': self.is_ai_enabled(),
            'usage_stats': self.usage_stats.copy(),
            'context_memory_size': sum(len(contexts) for contexts in self.context_memory.values()),
            'features': self.ai_features.copy(),
            'last_activity': datetime.now().isoformat()
        }
