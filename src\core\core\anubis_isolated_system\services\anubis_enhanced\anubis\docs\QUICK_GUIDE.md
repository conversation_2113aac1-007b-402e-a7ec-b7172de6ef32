# 🚀 دليل التشغيل السريع

## 🎯 التشغيل السريع (3 طرق)

### 1️⃣ الطريقة الأسهل - Windows Batch
```bash
# انقر مرتين على الملف
run_dashboard.bat
```

### 2️⃣ الطريقة المتقدمة - PowerShell
```powershell
# تشغيل عادي
.\Start-Dashboard.ps1

# تشغيل مع تثبيت المكتبات تلقائياً
.\Start-Dashboard.ps1 -InstallDeps

# تشغيل مع معلومات مفصلة
.\Start-Dashboard.ps1 -Verbose

# تخطي فحص المتطلبات (للتشغيل السريع)
.\Start-Dashboard.ps1 -SkipChecks
```

### 3️⃣ الطريقة اليدوية - Python
```bash
# فحص وتثبيت المتطلبات
python quick_start.py

# أو تشغيل مباشر
python process_control_dashboard.py
```

## ⚡ التشغيل السريع بدون فحص

إذا كنت متأكد من توفر جميع المتطلبات:

```bash
# Windows
python process_control_dashboard.py

# PowerShell
.\Start-Dashboard.ps1 -SkipChecks
```

## 🔧 حل المشاكل الشائعة

### المشكلة: "Python غير موجود"
**الحل:**
1. تثبيت Python من [python.org](https://python.org)
2. تأكد من إضافة Python للـ PATH
3. إعادة تشغيل Terminal

### المشكلة: "psutil غير مثبت"
**الحل:**
```bash
pip install psutil
```

### المشكلة: "tkinter غير متاح"
**الحل:**
- إعادة تثبيت Python مع tkinter
- أو استخدام: `sudo apt-get install python3-tk` (Linux)

### المشكلة: "VS Code غير متاح"
**الحل:**
1. تثبيت VS Code
2. إضافة VS Code للـ PATH
3. إعادة تشغيل Terminal

## 📊 الواجهة الرئيسية

### الإحصائيات العلوية (ملونة)
- 🔴 **العمليات النشطة**: عدد عمليات VS Code
- 🔵 **استهلاك الذاكرة**: بالميجابايت
- 🟢 **استهلاك المعالج**: نسبة مئوية
- 🟡 **الإضافات المفعلة**: عدد الإضافات

### الجانب الأيسر - العمليات
- **🔄 تحديث**: تحديث قائمة العمليات
- **🚫 إغلاق المحدد**: إغلاق عملية محددة
- **🧹 تنظيف شامل**: إغلاق جميع العمليات

### الجانب الأيمن - الإضافات
- **🔄 تحديث الإضافات**: تحديث قائمة الإضافات
- **❌ تعطيل المحدد**: تعطيل إضافة محددة
- **✅ تفعيل المحدد**: تفعيل إضافة محددة

### الأسفل - أدوات إضافية
- **🚀 إعادة تشغيل VS Code**: إعادة تشغيل نظيفة
- **⚡ تحسين الأداء**: تحسين شامل
- **💾 حفظ التقرير**: حفظ تقرير مفصل

## ⚠️ تحذيرات مهمة

### قبل إغلاق العمليات:
- ✅ احفظ جميع الملفات المفتوحة
- ✅ أغلق المشاريع المهمة
- ✅ تأكد من عدم وجود عمليات بناء قيد التشغيل

### عند تعطيل الإضافات:
- ⚠️ قد تحتاج إعادة تشغيل VS Code
- ⚠️ تأكد من عدم اعتماد مشروعك على الإضافة
- ⚠️ احفظ إعدادات المشروع

## 🎛️ المميزات المتقدمة

### المراقبة التلقائية
- فعل "مراقبة تلقائية" للتحديث كل 5 ثواني
- مراقبة مستمرة للأداء
- تنبيهات عند تجاوز الحدود

### تحسين الأداء
- إغلاق العمليات الخاملة
- إعادة تشغيل مع إعدادات محسنة
- تنظيف ذاكرة التخزين المؤقت

### التقارير
- تقارير مفصلة عن الأداء
- حفظ تلقائي للسجلات
- تحليل استهلاك الموارد

## 🔧 التخصيص

### تعديل الإعدادات
عدل ملف `dashboard_config.json` لتخصيص:
- الألوان والخطوط
- فترات التحديث
- حدود الأداء
- اختصارات لوحة المفاتيح

### إضافة مرشحات العمليات
```json
"process_filters": [
  "code",
  "node", 
  "electron",
  "typescript",
  "eslint"
]
```

## 📱 اختصارات لوحة المفاتيح

- **F5**: تحديث العمليات
- **Delete**: إغلاق العملية المحددة
- **Ctrl+Shift+K**: تنظيف شامل
- **Ctrl+M**: تفعيل/إلغاء المراقبة التلقائية
- **Ctrl+S**: حفظ التقرير
- **Ctrl+O**: تحسين الأداء

## 🆘 الحصول على المساعدة

### إذا واجهت مشاكل:
1. راجع قسم "حل المشاكل الشائعة" أعلاه
2. تحقق من ملف السجل في التطبيق
3. تأكد من تحديث جميع المكتبات
4. جرب إعادة تشغيل Terminal كمدير

### معلومات إضافية:
- راجع `DASHBOARD_README.md` للتفاصيل الكاملة
- تحقق من `dashboard_config.json` للإعدادات المتقدمة

---

**🎛️ استمتع بالتحكم الكامل في VS Code! 🎛️**
