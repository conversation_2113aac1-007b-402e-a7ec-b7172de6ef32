{"created_at": "2025-07-20T12:00:52.262701", "backup_location": "archive_and_backups\\structure_backup_20250720_120052", "migrations": [{"from": "anubis_main_system", "to": "src/core", "status": "completed", "files_count": 699}, {"from": "universal_ai_system", "to": "src/ai_services", "status": "completed", "files_count": 17}, {"from": "workflows_and_automation", "to": "src/automation", "status": "completed", "files_count": 35}, {"from": "anubis_isolation_system", "to": "src/security", "status": "completed", "files_count": 21}, {"from": "tools_and_utilities", "to": "src/dev_environment", "status": "completed", "files_count": 109}, {"from": "configs", "to": "config", "status": "completed", "files_count": 4}, {"from": "documentation", "to": "docs", "status": "completed", "files_count": 13}, {"from": "reports", "to": "docs/reports", "status": "completed", "files_count": 15}, {"from": "database", "to": "data/database", "status": "completed", "files_count": 1}, {"from": "isolation_configs", "to": "config/security", "status": "completed", "files_count": 9}, {"from": "isolation_systems", "to": "src/security/systems", "status": "completed", "files_count": 14}]}