#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 واجهة سطر الأوامر الموحدة لنظام أنوبيس
Anubis Unified CLI - Advanced Management System
"""

import click
import subprocess
import sys
import json
import time
import requests
from pathlib import Path
from datetime import datetime
try:
    import docker as docker_lib
except ImportError:
    docker_lib = None
import psutil

# إعدادات النظام
PROJECT_ROOT = Path(__file__).parent.parent.parent
SERVICES_CONFIG = {
    "core": {"port": 8000, "path": "src/core", "command": "python main.py"},
    "ai_services": {"port": 8001, "path": "src/ai_services", "status": "development"},
    "automation": {"port": 5678, "path": "src/automation", "service": "n8n"},
    "monitoring": {"port": 3000, "path": "src/monitoring", "service": "grafana"},
    "security": {"port": 9090, "path": "src/security", "status": "development"}
}

class AnubisManager:
    def __init__(self):
        self.project_root = PROJECT_ROOT
        self.docker_client = None
        try:
            if docker_lib:
                self.docker_client = docker_lib.from_env()
        except:
            pass

    def check_service_health(self, service_name, port):
        """فحص صحة الخدمة"""
        try:
            response = requests.get(f"http://localhost:{port}", timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_system_status(self):
        """الحصول على حالة النظام الشاملة"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent
            },
            "services": {},
            "docker": {"available": self.docker_client is not None, "containers": []}
        }

        # فحص الخدمات
        for service_name, config in SERVICES_CONFIG.items():
            if "port" in config:
                status["services"][service_name] = {
                    "port": config["port"],
                    "healthy": self.check_service_health(service_name, config["port"]),
                    "status": config.get("status", "active")
                }

        # فحص حاويات Docker
        if self.docker_client:
            try:
                containers = self.docker_client.containers.list(all=True)
                for container in containers:
                    if "anubis" in container.name.lower():
                        status["docker"]["containers"].append({
                            "name": container.name,
                            "status": container.status,
                            "image": container.image.tags[0] if container.image.tags else "unknown"
                        })
            except:
                pass

        return status

manager = AnubisManager()

@click.group()
@click.version_option(version="2.0.0")
def anubis():
    """🏺 نظام أنوبيس - واجهة سطر الأوامر الموحدة المتقدمة"""
    pass

# ===== أوامر النظام الأساسي =====
@anubis.group()
def system():
    """🖥️ إدارة النظام الأساسي"""
    pass

@system.command()
@click.option("--port", default=8000, help="منفذ الخادم")
@click.option("--reload", is_flag=True, help="تفعيل إعادة التحميل التلقائي")
def start(port, reload):
    """🚀 تشغيل النظام الأساسي"""
    click.echo(f"🏺 بدء تشغيل نظام أنوبيس على المنفذ {port}...")

    cmd = [sys.executable, "main.py"]
    if reload:
        cmd.extend(["--reload"])

    subprocess.run(cmd, cwd=manager.project_root)

@system.command()
def stop():
    """🛑 إيقاف النظام"""
    click.echo("🛑 إيقاف نظام أنوبيس...")

    # إيقاف العمليات المحلية
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'main.py' in ' '.join(proc.info['cmdline'] or []):
                proc.terminate()
                click.echo(f"✅ تم إيقاف العملية {proc.info['pid']}")
        except:
            pass

@system.command()
@click.option("--format", type=click.Choice(['table', 'json']), default='table')
def status(format):
    """📊 عرض حالة النظام الشاملة"""
    status_data = manager.get_system_status()

    if format == 'json':
        click.echo(json.dumps(status_data, ensure_ascii=False, indent=2))
        return

    # عرض جدولي
    click.echo("🏺 حالة نظام أنوبيس")
    click.echo("=" * 50)

    # حالة النظام
    sys_info = status_data["system"]
    click.echo(f"💻 النظام:")
    click.echo(f"   CPU: {sys_info['cpu_percent']:.1f}%")
    click.echo(f"   الذاكرة: {sys_info['memory_percent']:.1f}%")
    click.echo(f"   القرص: {sys_info['disk_percent']:.1f}%")

    # حالة الخدمات
    click.echo(f"\n🔧 الخدمات:")
    for service, info in status_data["services"].items():
        status_icon = "✅" if info["healthy"] else "❌"
        click.echo(f"   {status_icon} {service}: {info['port']} ({info['status']})")

    # حالة Docker
    if status_data["docker"]["available"]:
        click.echo(f"\n🐳 Docker:")
        containers = status_data["docker"]["containers"]
        if containers:
            for container in containers:
                status_icon = "✅" if container["status"] == "running" else "⚠️"
                click.echo(f"   {status_icon} {container['name']}: {container['status']}")
        else:
            click.echo("   📦 لا توجد حاويات أنوبيس")
    else:
        click.echo("\n🐳 Docker: غير متوفر")

@system.command()
def restart():
    """🔄 إعادة تشغيل النظام"""
    click.echo("🔄 إعادة تشغيل نظام أنوبيس...")

    # إيقاف النظام
    ctx = click.get_current_context()
    ctx.invoke(stop)

    # انتظار قصير
    time.sleep(2)

    # تشغيل النظام
    ctx.invoke(start)

# ===== أوامر Docker =====
@anubis.group()
def docker():
    """🐳 إدارة حاويات Docker"""
    pass

@docker.command()
@click.option("--service", help="خدمة محددة لتشغيلها")
def up(service):
    """🚀 تشغيل الخدمات باستخدام Docker Compose"""
    if service:
        click.echo(f"🐳 تشغيل خدمة {service}...")
        cmd = ["docker-compose", "up", "-d", service]
    else:
        click.echo("🐳 تشغيل جميع الخدمات...")
        cmd = ["docker-compose", "up", "-d"]

    subprocess.run(cmd, cwd=manager.project_root)

@docker.command()
@click.option("--service", help="خدمة محددة لإيقافها")
def down(service):
    """🛑 إيقاف الخدمات"""
    if service:
        click.echo(f"🛑 إيقاف خدمة {service}...")
        cmd = ["docker-compose", "stop", service]
    else:
        click.echo("🛑 إيقاف جميع الخدمات...")
        cmd = ["docker-compose", "down"]

    subprocess.run(cmd, cwd=manager.project_root)

@docker.command()
def logs():
    """📋 عرض سجلات الحاويات"""
    click.echo("📋 سجلات الحاويات:")
    subprocess.run(["docker-compose", "logs", "-f"], cwd=manager.project_root)

@docker.command()
def ps():
    """📦 عرض الحاويات النشطة"""
    click.echo("📦 الحاويات النشطة:")
    subprocess.run(["docker-compose", "ps"], cwd=manager.project_root)

# ===== أوامر التطوير =====
@anubis.group()
def dev():
    """🛠️ أدوات التطوير"""
    pass

@dev.command()
@click.option("--path", default=".", help="مسار الكود للفحص")
def lint(path):
    """🔍 فحص جودة الكود"""
    click.echo(f"🔍 فحص جودة الكود في {path}...")

    # تشغيل flake8
    click.echo("📝 تشغيل flake8...")
    subprocess.run(["flake8", path], cwd=manager.project_root)

    # تشغيل pylint
    click.echo("📝 تشغيل pylint...")
    subprocess.run(["pylint", path], cwd=manager.project_root)

@dev.command()
@click.option("--path", default=".", help="مسار الكود للتنسيق")
def format(path):
    """✨ تنسيق الكود"""
    click.echo(f"✨ تنسيق الكود في {path}...")
    subprocess.run(["black", path], cwd=manager.project_root)

@dev.command()
@click.option("--coverage", is_flag=True, help="تشغيل مع تقرير التغطية")
def test(coverage):
    """🧪 تشغيل الاختبارات"""
    click.echo("🧪 تشغيل اختبارات النظام...")

    if coverage:
        cmd = ["pytest", "--cov=src", "--cov-report=html"]
    else:
        cmd = ["pytest"]

    subprocess.run(cmd, cwd=manager.project_root)

# ===== أوامر قواعد البيانات =====
@anubis.group()
def db():
    """🗄️ إدارة قواعد البيانات"""
    pass

@db.command()
def init():
    """🏗️ تهيئة قاعدة البيانات"""
    click.echo("🏗️ تهيئة قاعدة البيانات...")
    # سيتم إضافة منطق تهيئة قاعدة البيانات

@db.command()
def migrate():
    """🔄 تشغيل migrations"""
    click.echo("🔄 تشغيل migrations...")
    # سيتم إضافة منطق migrations

@db.command()
def backup():
    """💾 إنشاء نسخة احتياطية"""
    click.echo("💾 إنشاء نسخة احتياطية من قاعدة البيانات...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"backup_{timestamp}.sql"
    click.echo(f"📁 النسخة الاحتياطية: {backup_file}")

# ===== أوامر المراقبة =====
@anubis.group()
def monitor():
    """📊 أدوات المراقبة"""
    pass

@monitor.command()
@click.option("--interval", default=5, help="فترة التحديث بالثواني")
def watch(interval):
    """👀 مراقبة النظام في الوقت الفعلي"""
    click.echo(f"👀 مراقبة النظام (تحديث كل {interval} ثانية)")
    click.echo("اضغط Ctrl+C للإيقاف")

    try:
        while True:
            click.clear()
            ctx = click.get_current_context()
            ctx.invoke(status)
            time.sleep(interval)
    except KeyboardInterrupt:
        click.echo("\n👋 تم إيقاف المراقبة")

@monitor.command()
def health():
    """🏥 فحص صحة شامل للنظام"""
    click.echo("🏥 فحص صحة شامل للنظام...")

    status_data = manager.get_system_status()

    # فحص الموارد
    cpu = status_data["system"]["cpu_percent"]
    memory = status_data["system"]["memory_percent"]
    disk = status_data["system"]["disk_percent"]

    click.echo("\n💻 فحص الموارد:")
    click.echo(f"   CPU: {cpu:.1f}% {'✅' if cpu < 80 else '⚠️'}")
    click.echo(f"   الذاكرة: {memory:.1f}% {'✅' if memory < 80 else '⚠️'}")
    click.echo(f"   القرص: {disk:.1f}% {'✅' if disk < 90 else '⚠️'}")

    # فحص الخدمات
    click.echo("\n🔧 فحص الخدمات:")
    healthy_services = 0
    total_services = len(status_data["services"])

    for service, info in status_data["services"].items():
        if info["healthy"]:
            healthy_services += 1
            click.echo(f"   ✅ {service}: صحي")
        else:
            click.echo(f"   ❌ {service}: غير متاح")

    # تقرير عام
    click.echo(f"\n📊 التقرير العام:")
    click.echo(f"   الخدمات الصحية: {healthy_services}/{total_services}")

    if healthy_services == total_services and cpu < 80 and memory < 80:
        click.echo("   🎉 النظام في حالة ممتازة!")
    elif healthy_services > 0:
        click.echo("   ⚠️ النظام يعمل مع بعض المشاكل")
    else:
        click.echo("   🚨 النظام يحتاج صيانة فورية")

if __name__ == "__main__":
    anubis()
