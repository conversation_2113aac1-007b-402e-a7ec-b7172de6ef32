#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبارات قاعدة البيانات المحسنة
Enhanced Database Tests

مطور بالتعاون مع Gemini CLI لاختبار التحسينات الأمنية والأداء
"""

import json
import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent.parent))

from database.enhanced_anubis_database import (
    EnhancedAnubisDatabase,
    DatabaseSecurityError,
    DatabaseConnectionError,
    create_enhanced_database
)


class TestDatabaseSecurity(unittest.TestCase):
    """اختبارات الأمان"""
    
    def test_sqlite_path_validation(self):
        """اختبار التحقق من مسار SQLite"""
        # مسار آمن
        safe_config = {"db_path": "database/test.db"}
        db = EnhancedAnubisDatabase("sqlite", safe_config)
        self.assertEqual(db.db_path, "database/test.db")
        
        # مسار غير آمن - Path Traversal
        with self.assertRaises(DatabaseSecurityError):
            unsafe_config = {"db_path": "../../../etc/passwd"}
            EnhancedAnubisDatabase("sqlite", unsafe_config)
        
        # مسار غير آمن - امتداد خاطئ
        with self.assertRaises(DatabaseSecurityError):
            unsafe_config = {"db_path": "database/test.txt"}
            EnhancedAnubisDatabase("sqlite", unsafe_config)
    
    def test_mysql_identifier_validation(self):
        """اختبار التحقق من معرفات MySQL"""
        # اسم قاعدة بيانات آمن
        safe_config = {"database": "anubis_test_db"}
        db = EnhancedAnubisDatabase("mysql", safe_config)
        self.assertEqual(db.mysql_config["database"], "anubis_test_db")
        
        # اسم قاعدة بيانات غير آمن - SQL injection attempt
        with self.assertRaises(DatabaseSecurityError):
            unsafe_config = {"database": "test; DROP TABLE users; --"}
            EnhancedAnubisDatabase("mysql", unsafe_config)
        
        # اسم قاعدة بيانات غير آمن - أحرف خاصة
        with self.assertRaises(DatabaseSecurityError):
            unsafe_config = {"database": "test-db@#$"}
            EnhancedAnubisDatabase("mysql", unsafe_config)
    
    def test_input_validation_add_project(self):
        """اختبار التحقق من المدخلات في add_project"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                db.connect()
                db.create_tables()
                
                # مدخلات صحيحة
                project_id = db.add_project("Test Project", "/path/to/project", "python", "Test description")
                self.assertIsInstance(project_id, int)
                self.assertGreater(project_id, 0)
                
                # مدخلات غير صحيحة - اسم فارغ
                with self.assertRaises(ValueError):
                    db.add_project("", "/path", "python", "desc")
                
                # مدخلات غير صحيحة - اسم طويل جداً
                long_name = "x" * 300
                with self.assertRaises(ValueError):
                    db.add_project(long_name, "/path", "python", "desc")
                    
            finally:
                db.close()
                os.unlink(tmp.name)
    
    def test_input_validation_add_analysis(self):
        """اختبار التحقق من المدخلات في add_analysis"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                db.connect()
                db.create_tables()
                
                # إضافة مشروع أولاً
                project_id = db.add_project("Test", "/path", "python", "desc")
                
                # مدخلات صحيحة
                analysis_id = db.add_analysis(
                    project_id, "test_agent", {"test": "data"}, {"result": "good"}, 85.5
                )
                self.assertIsInstance(analysis_id, int)
                
                # مدخلات غير صحيحة - project_id خاطئ
                with self.assertRaises(ValueError):
                    db.add_analysis(-1, "agent", {}, {}, 50)
                
                # مدخلات غير صحيحة - نقاط خارج النطاق
                with self.assertRaises(ValueError):
                    db.add_analysis(project_id, "agent", {}, {}, 150)
                    
            finally:
                db.close()
                os.unlink(tmp.name)


class TestDatabasePerformance(unittest.TestCase):
    """اختبارات الأداء"""
    
    def test_connection_pooling_mysql(self):
        """اختبار مجموعة الاتصالات لـ MySQL"""
        config = {
            "host": "localhost",
            "user": "test",
            "password": "test",
            "database": "test_db",
            "pool_size": 3
        }
        
        # محاكاة MySQL connection pooling
        with patch('mysql.connector.pooling.MySQLConnectionPool') as mock_pool:
            mock_pool_instance = Mock()
            mock_pool.return_value = mock_pool_instance
            
            db = EnhancedAnubisDatabase("mysql", config)
            
            # التحقق من إنشاء مجموعة الاتصالات
            mock_pool.assert_called_once()
            self.assertEqual(db.connection_pool, mock_pool_instance)
    
    def test_context_manager_connection(self):
        """اختبار إدارة الاتصالات مع context manager"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                # اختبار context manager
                with db.get_connection() as conn:
                    self.assertIsNotNone(conn)
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    self.assertIsNotNone(result)
                    
            finally:
                os.unlink(tmp.name)
    
    def test_pagination_get_projects(self):
        """اختبار pagination في get_projects"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                db.connect()
                db.create_tables()
                
                # إضافة عدة مشاريع
                for i in range(5):
                    db.add_project(f"Project {i}", f"/path/{i}", "python", f"Description {i}")
                
                # اختبار pagination
                projects_page1 = db.get_projects(limit=2, offset=0)
                self.assertEqual(len(projects_page1), 2)
                
                projects_page2 = db.get_projects(limit=2, offset=2)
                self.assertEqual(len(projects_page2), 2)
                
                # اختبار validation
                with self.assertRaises(ValueError):
                    db.get_projects(limit=0)  # حد أقصى غير صحيح
                
                with self.assertRaises(ValueError):
                    db.get_projects(limit=10, offset=-1)  # إزاحة سالبة
                    
            finally:
                db.close()
                os.unlink(tmp.name)


class TestDatabaseReliability(unittest.TestCase):
    """اختبارات الموثوقية"""
    
    def test_transaction_rollback(self):
        """اختبار rollback عند حدوث خطأ"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                db.connect()
                db.create_tables()
                
                # محاولة إضافة بيانات غير صحيحة
                with self.assertRaises(ValueError):
                    db.add_project("", "", "", "")  # بيانات فارغة
                
                # التحقق من أن قاعدة البيانات لم تتأثر
                projects = db.get_projects()
                self.assertEqual(len(projects), 0)
                
            finally:
                db.close()
                os.unlink(tmp.name)
    
    def test_error_handling_and_logging(self):
        """اختبار معالجة الأخطاء والتسجيل"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                # محاولة عملية بدون اتصال
                with self.assertRaises(DatabaseConnectionError):
                    db.add_project("Test", "/path", "python", "desc")
                    
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)
    
    def test_json_data_handling(self):
        """اختبار معالجة بيانات JSON"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            db = EnhancedAnubisDatabase("sqlite", config)
            
            try:
                db.connect()
                db.create_tables()
                
                # إضافة مشروع
                project_id = db.add_project("Test", "/path", "python", "desc")
                
                # إضافة تحليل مع بيانات JSON معقدة
                analysis_data = {
                    "files_analyzed": 10,
                    "errors_found": ["error1", "error2"],
                    "metrics": {"complexity": 5.5, "coverage": 85.2}
                }
                results = {
                    "status": "completed",
                    "recommendations": ["fix error1", "improve coverage"]
                }
                
                analysis_id = db.add_analysis(project_id, "test_agent", analysis_data, results, 88.5)
                self.assertIsInstance(analysis_id, int)
                
                # استرجاع التحليل والتحقق من JSON
                analyses = db.get_project_analyses(project_id)
                self.assertEqual(len(analyses), 1)
                
                retrieved_analysis = analyses[0]
                self.assertEqual(retrieved_analysis['analysis_data']['files_analyzed'], 10)
                self.assertEqual(retrieved_analysis['results']['status'], "completed")
                
            finally:
                db.close()
                os.unlink(tmp.name)


class TestDatabaseIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def test_complete_workflow(self):
        """اختبار سير عمل كامل"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            config = {"db_path": tmp.name}
            
            try:
                # استخدام context manager
                with EnhancedAnubisDatabase("sqlite", config) as db:
                    db.create_tables()
                    
                    # إضافة مشروع
                    project_id = db.add_project("Complete Test", "/test/path", "python", "Full workflow test")
                    
                    # إضافة تحليل
                    analysis_id = db.add_analysis(
                        project_id, "full_test_agent", 
                        {"test": "complete"}, {"result": "success"}, 95.0
                    )
                    
                    # إضافة خطأ
                    error_id = db.add_error(
                        project_id, "test.py", 10, "syntax_error", "high", "Missing semicolon"
                    )
                    
                    # الحصول على الإحصائيات
                    stats = db.get_database_stats()
                    self.assertEqual(stats['projects_count'], 1)
                    self.assertEqual(stats['analyses_count'], 1)
                    self.assertEqual(stats['errors_count'], 1)
                    self.assertEqual(stats['average_score'], 95.0)
                    
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)
    
    def test_create_enhanced_database_function(self):
        """اختبار دالة إنشاء قاعدة البيانات المحسنة"""
        # اختبار مع تكوين افتراضي
        db = create_enhanced_database()
        self.assertIsInstance(db, EnhancedAnubisDatabase)
        self.assertEqual(db.db_type, "sqlite")
        
        # اختبار مع ملف تكوين
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            config = {
                "db_type": "sqlite",
                "db_path": "test_enhanced.db"
            }
            json.dump(config, tmp)
            tmp.flush()
            
            try:
                db = create_enhanced_database(tmp.name)
                self.assertEqual(db.db_path, "test_enhanced.db")
            finally:
                os.unlink(tmp.name)


def run_enhanced_database_tests():
    """تشغيل جميع اختبارات قاعدة البيانات المحسنة"""
    print("🧪 تشغيل اختبارات قاعدة البيانات المحسنة")
    print("🤖 مطور بالتعاون مع Gemini CLI")
    print("-" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات الأمان
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDatabaseSecurity))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDatabasePerformance))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDatabaseReliability))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDatabaseIntegration))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # طباعة النتائج
    print("\n" + "="*60)
    print("📊 نتائج اختبارات قاعدة البيانات المحسنة:")
    print(f"✅ نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ فشل: {len(result.failures)}")
    print(f"🔴 أخطاء: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n🔴 الأخطاء:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    print("🤖 تم تطوير هذه الاختبارات بالتعاون مع Gemini CLI")
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_enhanced_database_tests()
    sys.exit(0 if success else 1)
