#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مشغل النظام المعزول بـ Docker - أنوبيس
Anubis Docker Isolation Launcher
"""

import subprocess
import time
import requests
import json
from pathlib import Path
from datetime import datetime

class AnubisIsolationLauncher:
    """مشغل النظام المعزول"""
    
    def __init__(self):
        self.compose_file = "docker-compose-anubis-isolation.yml"
        self.services = [
            "anubis-database-isolated",
            "anubis-redis-isolated", 
            "anubis-api-isolated",
            "anubis-worker-isolated",
            "anubis-monitor-isolated",
            "anubis-nginx-isolated"
        ]
        self.service_ports = {
            "anubis-api-isolated": 8080,
            "anubis-monitor-isolated": 9090,
            "anubis-nginx-isolated": 80
        }
    
    def check_docker(self):
        """فحص توفر Docker"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Docker متوفر: {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker غير متوفر")
                return False
        except Exception as e:
            print(f"❌ خطأ في فحص Docker: {e}")
            return False
    
    def check_docker_compose(self):
        """فحص توفر Docker Compose"""
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Docker Compose متوفر: {result.stdout.strip()}")
                return True
            else:
                # محاولة docker compose (الإصدار الجديد)
                result = subprocess.run(['docker', 'compose', 'version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ Docker Compose متوفر: {result.stdout.strip()}")
                    return True
                else:
                    print("❌ Docker Compose غير متوفر")
                    return False
        except Exception as e:
            print(f"❌ خطأ في فحص Docker Compose: {e}")
            return False
    
    def build_services(self):
        """بناء الخدمات"""
        print("🔨 بناء خدمات النظام المعزول...")
        
        try:
            # بناء جميع الخدمات
            result = subprocess.run([
                'docker-compose', '-f', self.compose_file, 'build'
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ تم بناء جميع الخدمات بنجاح")
                return True
            else:
                print(f"❌ فشل بناء الخدمات: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة بناء الخدمات")
            return False
        except Exception as e:
            print(f"❌ خطأ في بناء الخدمات: {e}")
            return False
    
    def start_services(self):
        """تشغيل الخدمات"""
        print("🚀 تشغيل خدمات النظام المعزول...")
        
        try:
            # تشغيل الخدمات في الخلفية
            result = subprocess.run([
                'docker-compose', '-f', self.compose_file, 'up', '-d'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ تم تشغيل جميع الخدمات بنجاح")
                return True
            else:
                print(f"❌ فشل تشغيل الخدمات: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة تشغيل الخدمات")
            return False
        except Exception as e:
            print(f"❌ خطأ في تشغيل الخدمات: {e}")
            return False
    
    def check_service_health(self, service_name, port, path="/health"):
        """فحص صحة خدمة"""
        try:
            url = f"http://localhost:{port}{path}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {service_name} صحية")
                return True
            else:
                print(f"⚠️  {service_name} تستجيب لكن بحالة: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {service_name} غير متاحة")
            return False
        except Exception as e:
            print(f"⚠️  خطأ في فحص {service_name}: {e}")
            return False
    
    def wait_for_services(self, max_wait=180):
        """انتظار جاهزية الخدمات"""
        print("⏳ انتظار جاهزية الخدمات...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            all_healthy = True
            
            for service_name, port in self.service_ports.items():
                if not self.check_service_health(service_name, port):
                    all_healthy = False
                    break
            
            if all_healthy:
                elapsed = int(time.time() - start_time)
                print(f"✅ جميع الخدمات جاهزة! ({elapsed} ثانية)")
                return True
            
            print("⏳ انتظار الخدمات...")
            time.sleep(10)
        
        print(f"❌ انتهت مهلة انتظار الخدمات ({max_wait} ثانية)")
        return False
    
    def show_services_status(self):
        """عرض حالة الخدمات"""
        print("\n📊 حالة خدمات النظام المعزول:")
        print("=" * 50)
        
        try:
            # الحصول على حالة الحاويات
            result = subprocess.run([
                'docker-compose', '-f', self.compose_file, 'ps'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(result.stdout)
            else:
                print(f"❌ خطأ في الحصول على حالة الخدمات: {result.stderr}")
                
        except Exception as e:
            print(f"❌ خطأ في عرض حالة الخدمات: {e}")
    
    def show_service_urls(self):
        """عرض روابط الخدمات"""
        print("\n🌐 روابط الخدمات:")
        print("=" * 30)
        print("📱 API الرئيسي: http://localhost:8080")
        print("📊 واجهة المراقبة: http://localhost:9090")
        print("🌐 Nginx Proxy: http://localhost:80")
        print("📚 API Docs: http://localhost:8080/docs")
        print("📋 API Redoc: http://localhost:8080/redoc")
        
        print("\n🔍 نقاط الفحص:")
        print("🏥 صحة API: http://localhost:8080/health")
        print("📊 حالة API: http://localhost:8080/status")
        print("📈 مقاييس API: http://localhost:8080/metrics")
    
    def stop_services(self):
        """إيقاف الخدمات"""
        print("🛑 إيقاف خدمات النظام المعزول...")
        
        try:
            result = subprocess.run([
                'docker-compose', '-f', self.compose_file, 'down'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ تم إيقاف جميع الخدمات")
                return True
            else:
                print(f"❌ خطأ في إيقاف الخدمات: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إيقاف الخدمات: {e}")
            return False
    
    def cleanup_system(self):
        """تنظيف النظام"""
        print("🧹 تنظيف النظام المعزول...")
        
        try:
            # إيقاف وحذف الحاويات والشبكات والأحجام
            result = subprocess.run([
                'docker-compose', '-f', self.compose_file, 'down', '-v', '--remove-orphans'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ تم تنظيف النظام")
                return True
            else:
                print(f"❌ خطأ في تنظيف النظام: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تنظيف النظام: {e}")
            return False
    
    def run_isolation_system(self):
        """تشغيل النظام المعزول الكامل"""
        print("🏺 مشغل النظام المعزول - أنوبيس")
        print("=" * 50)
        
        # فحص المتطلبات
        if not self.check_docker():
            return False
        
        if not self.check_docker_compose():
            return False
        
        # فحص وجود ملف docker-compose
        if not Path(self.compose_file).exists():
            print(f"❌ ملف {self.compose_file} غير موجود")
            return False
        
        # بناء الخدمات
        if not self.build_services():
            return False
        
        # تشغيل الخدمات
        if not self.start_services():
            return False
        
        # انتظار جاهزية الخدمات
        if not self.wait_for_services():
            print("⚠️  بعض الخدمات قد لا تكون جاهزة")
        
        # عرض الحالة والروابط
        self.show_services_status()
        self.show_service_urls()
        
        print("\n🎉 تم تشغيل النظام المعزول بنجاح!")
        print("⚠️  اضغط Ctrl+C لإيقاف النظام")
        
        return True

def main():
    """الدالة الرئيسية"""
    launcher = AnubisIsolationLauncher()
    
    try:
        # تشغيل النظام
        success = launcher.run_isolation_system()
        
        if success:
            # انتظار إشارة الإيقاف
            while True:
                time.sleep(10)
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        launcher.stop_services()
        
        # سؤال عن التنظيف
        try:
            cleanup = input("هل تريد تنظيف النظام (حذف الأحجام)؟ (y/n): ").lower()
            if cleanup in ['y', 'yes', 'نعم']:
                launcher.cleanup_system()
        except:
            pass
            
    except Exception as e:
        print(f"❌ خطأ في النظام: {e}")
        launcher.stop_services()

if __name__ == "__main__":
    main()
