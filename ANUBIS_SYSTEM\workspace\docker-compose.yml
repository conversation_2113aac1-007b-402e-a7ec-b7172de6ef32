version: '3.8'

services:
  anubis-workspace:
    build: .
    container_name: anubis-workspace-isolated
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد لبيئة العمل
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    
    # الشبكات المعزولة
    networks:
      - anubis-workspace-net
      - anubis-workspace-data-net
    
    # الأحجام المعزولة
    volumes:
      - anubis-workspace-data:/app/workspace/data
      - anubis-workspace-logs:/app/workspace/logs:rw
      - anubis-workspace-reports:/app/workspace/reports:rw
      - anubis-workspace-projects:/app/workspace/projects:rw
      - anubis-workspace-notebooks:/app/workspace/notebooks:rw
      - anubis-workspace-configs:/app/workspace/configs:ro
    
    # متغيرات البيئة
    environment:
      - WORKSPACE_MODE=isolated_development
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-anubis_workspace_token}
      - WORKSPACE_SECURITY_ENABLED=true
      - LOG_LEVEL=INFO
    
    # حماية النظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=500m
      - /var/tmp:rw,noexec,nosuid,size=200m
      - /home/<USER>/.local:rw,size=200m
    
    # إزالة الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - FOWNER
    
    # المنافذ المحمية
    ports:
      - "127.0.0.1:8888:8888"  # Jupyter Lab
      - "127.0.0.1:8501:8501"  # Streamlit
      - "127.0.0.1:8000:8000"  # FastAPI
    
    # تسميات للمراقبة
    labels:
      - "anubis.component=workspace"
      - "anubis.isolation.level=advanced"
      - "anubis.development.enabled=true"
    
    # التبعيات
    depends_on:
      - anubis-workspace-db
      - anubis-workspace-monitor
  
  anubis-workspace-db:
    image: postgres:15-alpine
    container_name: anubis-workspace-db
    restart: unless-stopped
    networks:
      - anubis-workspace-data-net
    volumes:
      - anubis-workspace-db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=workspace_db
      - POSTGRES_USER=anubis_workspace
      - POSTGRES_PASSWORD=${WORKSPACE_DB_PASSWORD:-anubis_workspace_secure_2024}
    security_opt:
      - no-new-privileges:true
    
  anubis-workspace-monitor:
    image: prom/prometheus:latest
    container_name: anubis-workspace-monitor
    restart: unless-stopped
    networks:
      - anubis-workspace-net
    volumes:
      - ./monitoring/prometheus-workspace.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-workspace-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9094:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
  
  anubis-workspace-redis:
    image: redis:7-alpine
    container_name: anubis-workspace-redis
    restart: unless-stopped
    networks:
      - anubis-workspace-data-net
    volumes:
      - anubis-workspace-redis-data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    security_opt:
      - no-new-privileges:true
  
  anubis-workspace-backup:
    image: alpine:latest
    container_name: anubis-workspace-backup
    restart: unless-stopped
    networks:
      - anubis-workspace-data-net
    volumes:
      - anubis-workspace-data:/backup/data:ro
      - anubis-workspace-logs:/backup/logs:ro
      - anubis-workspace-reports:/backup/reports:ro
      - anubis-workspace-backups:/backup/output:rw
    environment:
      - BACKUP_SCHEDULE=0 3 * * *
      - BACKUP_RETENTION_DAYS=30
    security_opt:
      - no-new-privileges:true
    command: |
      sh -c "
      apk add --no-cache tar gzip openssl &&
      while true; do
        echo 'Creating workspace backup...' &&
        tar -czf /backup/output/workspace_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /backup data logs reports &&
        find /backup/output -name '*.tar.gz' -mtime +30 -delete &&
        sleep 86400
      done
      "

# الشبكات المعزولة لبيئة العمل
networks:
  anubis-workspace-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
  
  anubis-workspace-data-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# الأحجام المعزولة لبيئة العمل
volumes:
  anubis-workspace-data:
    driver: local
  anubis-workspace-logs:
    driver: local
  anubis-workspace-reports:
    driver: local
  anubis-workspace-projects:
    driver: local
  anubis-workspace-notebooks:
    driver: local
  anubis-workspace-configs:
    driver: local
  anubis-workspace-db-data:
    driver: local
  anubis-workspace-monitor-data:
    driver: local
  anubis-workspace-redis-data:
    driver: local
  anubis-workspace-backups:
    driver: local
