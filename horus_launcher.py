#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
𓅃 مشغل فريق حورس للذكاء الاصطناعي
Horus AI Team Launcher

واجهة بسيطة لتشغيل فريق حورس من المجلد الرئيسي
Simple interface to launch Horus team from the main directory
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل فريق حورس"""
    print("𓅃 تشغيل فريق حورس للذكاء الاصطناعي")
    print("=" * 60)
    
    # التحقق من وجود مجلد فريق حورس
    current_dir = Path(__file__).parent
    horus_dir = current_dir / "HORUS_AI_TEAM"
    
    if not horus_dir.exists():
        print("❌ خطأ: مجلد HORUS_AI_TEAM غير موجود")
        print("💡 تأكد من وجود مجلد فريق حورس في نفس مجلد هذا الملف")
        return
    
    # إضافة مسار فريق حورس
    sys.path.insert(0, str(horus_dir))
    
    try:
        # استيراد وتشغيل واجهة حورس
        from horus_interface import horus
        
        print("✅ تم تحميل فريق حورس بنجاح!")
        print("👁️ استخدم الأوامر التالية:")
        print()
        
        # عرض الأوامر الأساسية
        print("🎯 الأوامر الأساسية:")
        print("   horus.ask('سؤالك')                    - طرح سؤال")
        print("   horus.summon('اسم_الوكيل')             - استدعاء وكيل")
        print("   horus.agents()                        - عرض الوكلاء")
        print("   horus.help()                          - المساعدة الكاملة")
        print("   horus.status()                        - حالة الفريق")
        print()
        
        print("🤖 الوكلاء المتاحون:")
        print("   ⚡ THOTH  - المحلل السريع")
        print("   🔧 PTAH   - المطور الخبير")
        print("   🎯 RA     - المستشار الاستراتيجي")
        print("   💡 KHNUM  - المبدع والمبتكر")
        print("   👁️ SESHAT - المحللة البصرية")
        print("   𓅃 HORUS  - المنسق الأعلى")
        print()
        
        print("💡 مثال سريع:")
        print('   horus.ask("مرحباً يا حورس!")')
        print()
        print("🎯 فريق حورس جاهز للعمل!")
        
        # إرجاع كائن horus للاستخدام
        return horus
        
    except ImportError as e:
        print(f"❌ خطأ في تحميل فريق حورس: {e}")
        print("💡 تأكد من وجود ملف horus_interface.py في مجلد HORUS_AI_TEAM")
        return None
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return None

if __name__ == "__main__":
    horus = main()
    
    if horus:
        print("\n" + "="*60)
        print("🧪 اختبار سريع:")
        try:
            result = horus.ask("مرحباً يا حورس!")
            print(f"📝 الرد: {result}")
        except Exception as e:
            print(f"⚠️ خطأ في الاختبار: {e}")
