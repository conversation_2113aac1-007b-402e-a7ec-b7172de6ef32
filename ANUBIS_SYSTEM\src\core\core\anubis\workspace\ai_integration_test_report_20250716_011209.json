{"timestamp": "2025-07-16T01:12:09.406103", "test_summary": {"ollama_models_tested": 3, "working_models": 3, "ai_manager_working": true, "agent_integration_working": false, "use_cases_tested": 3, "successful_use_cases": 0}, "detailed_results": {"ollama_providers": {"llama3:8b": {"available": true, "working": true, "response_length": 171, "response_preview": "\"الذكاء الاصطناعي هو تقنية كمية تعمل على تكرار وتعلم من السلوكيات والبيانات لتنفيذ مهام محددة دون التدخل البشري، مما يخلق فرصاً جديدة في الأعمال والتعليم والرعاية الصحية.\""}, "mistral:7b": {"available": true, "working": true, "response_length": 233, "response_preview": " \"الذكاء الاصطناعي هو فرق الإدارة والتحكم في أجزاء من العقل البشري بوسائل غير بشرية، مثل الروبوتات والجهازات البيئية.\"\n\nوتعد الذكاء الاصطناعي القاعدة الأساسية في العلم الجديد (AI) وهو منطقة مشغلة حالي"}, "phi3:mini": {"available": true, "working": true, "response_length": 117, "response_preview": "الذكاء الاصطناعي هو خير فى قسم العلم الحضري، الذي يمكن أن تقدم الشخص المخانجة بالحياة الإنسانية المعتاد الإفتحة الآن."}}, "ai_manager": {"manager_working": true, "available_providers": ["ollama"], "response_test": true, "response_length": 98}, "agent_integration": {"agent_ai_enabled": false, "integration_successful": false, "error": "Can't instantiate abstract class AITestAgent without an implementation for abstract method 'run_analysis'"}, "use_cases": {"تحليل كود Python": {"success": false, "error": "خطأ في الاتصال بـ Ollama: 404 Client Error: Not Found for url: http://localhost:11434/api/generate"}, "اقتراحات تحسين المشروع": {"success": false, "error": "خطأ في الاتصال بـ Ollama: 404 Client Error: Not Found for url: http://localhost:11434/api/generate"}, "نصائح الأمان": {"success": false, "error": "خطأ في الاتصال بـ Ollama: 404 Client Error: Not Found for url: http://localhost:11434/api/generate"}}}, "recommendations": ["✅ نماذج Ollama تعمل بشكل جيد - يمكن استخدام الذكاء الاصطناعي", "✅ مدير الذكاء الاصطناعي يعمل بشكل صحيح", "❌ دمج الذكاء الاصطناعي مع الوكلاء فاشل"]}