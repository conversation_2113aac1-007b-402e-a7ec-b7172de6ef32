# VS Code Process Monitor
# مراقب عمليات VS Code

مجموعة شاملة من السكريبتات لمراقبة وتحليل عمليات VS Code والنظام.

## الملفات المتوفرة

### 1. `vscode_process_monitor.py`
السكريبت الرئيسي لمراقبة عمليات VS Code
- مراقبة جميع عمليات VS Code
- تحليل استهلاك الموارد
- إنشاء تقارير مفصلة
- تحديد أنواع العمليات المختلفة

### 2. `vscode_process_alerts.py`
نظام التنبيهات والمراقبة المستمرة
- مراقبة مستمرة للنظام
- تنبيهات عند تجاوز العتبات
- حفظ سجل التنبيهات
- إعدادات قابلة للتخصيص

### 3. `run_vscode_monitor.py`
واجهة سهلة الاستخدام للتشغيل
- قائمة تفاعلية
- فحص التبعيات
- تثبيت المكتبات المطلوبة
- عرض التقارير الحديثة

### 4. `run_vscode_monitor.ps1`
سكريبت PowerShell للمستخدمين على Windows
- واجهة ملونة
- معلومات النظام
- تشغيل سريع للمراقبة

## المتطلبات

### Python Libraries
```bash
pip install psutil
```

### نظام التشغيل
- Windows 10/11
- Python 3.7+
- PowerShell 5.1+ (للسكريبت PowerShell)

## طرق التشغيل

### 1. التشغيل السريع
```bash
# فحص واحد
python scripts/vscode_process_monitor.py

# مراقبة مستمرة
python scripts/vscode_process_alerts.py --monitor

# واجهة تفاعلية
python scripts/run_vscode_monitor.py
```

### 2. PowerShell (Windows)
```powershell
# تشغيل القائمة التفاعلية
.\scripts\run_vscode_monitor.ps1

# فحص واحد
.\scripts\run_vscode_monitor.ps1 -Check

# مراقبة مستمرة
.\scripts\run_vscode_monitor.ps1 -Monitor

# معلومات النظام
.\scripts\run_vscode_monitor.ps1 -Info

# تثبيت التبعيات
.\scripts\run_vscode_monitor.ps1 -Install
```

### 3. من خلال Task Manager في VS Code
1. افتح Command Palette (`Ctrl+Shift+P`)
2. اكتب "Tasks: Run Task"
3. اختر "Monitor VS Code Processes"

## الإعدادات

### ملف الإعدادات: `configs/alert_config.json`
```json
{
  "thresholds": {
    "cpu_warning": 70,
    "cpu_critical": 90,
    "memory_warning": 70,
    "memory_critical": 90,
    "vscode_memory_warning": 20,
    "vscode_memory_critical": 40
  },
  "monitoring": {
    "interval": 60,
    "alert_cooldown": 300,
    "max_alerts_per_hour": 10
  },
  "notifications": {
    "console": true,
    "file": true,
    "email": false
  }
}
```

## أنواع العمليات المراقبة

### عمليات VS Code
- **Main Process**: العملية الرئيسية لـ VS Code
- **Extension Host**: عمليات الإضافات
- **Search Process**: عمليات البحث (rg.exe)
- **Node Process**: عمليات Node.js المرتبطة
- **Tunnel Process**: عمليات الأنفاق

### معايير المراقبة
- استهلاك CPU
- استهلاك الذاكرة
- عدد الخيوط (Threads)
- وقت إنشاء العملية
- مسار العملية

## التقارير

### مواقع التقارير
- `reports/vscode_monitor_report_YYYYMMDD_HHMMSS.json`
- `logs/vscode_process_monitor.log`
- `logs/vscode_alerts.log`

### محتويات التقرير
```json
{
  "timestamp": "2025-01-16T10:30:00",
  "system_stats": {
    "cpu_usage": 45.2,
    "memory_percent": 67.8,
    "platform": "Windows"
  },
  "vscode_analysis": {
    "total_vscode_processes": 8,
    "total_memory_usage": 15.6,
    "total_cpu_usage": 12.3,
    "process_breakdown": {
      "Main Process": {"count": 1, "memory_usage": 8.2},
      "Extension Host": {"count": 3, "memory_usage": 5.1}
    },
    "performance_issues": [],
    "recommendations": []
  }
}
```

## التنبيهات

### مستويات التنبيه
- **INFO**: معلومات عامة
- **WARNING**: تحذيرات (استهلاك عالي)
- **CRITICAL**: حرج (استهلاك خطير)

### أنواع التنبيهات
- استهلاك CPU النظام
- استهلاك ذاكرة النظام
- استهلاك ذاكرة VS Code
- مشاكل الأداء المتعددة

## الاستكشاف والإصلاح

### مشاكل شائعة

#### 1. "psutil not found"
```bash
pip install psutil
```

#### 2. "Permission denied"
- تشغيل كمدير (Run as Administrator)
- التحقق من صلاحيات الملفات

#### 3. "No VS Code processes found"
- التأكد من تشغيل VS Code
- التحقق من أسماء العمليات

#### 4. "Config file error"
- حذف ملف الإعدادات وإعادة إنشاؤه
- التحقق من صيغة JSON

### تحسين الأداء

#### لتقليل استهلاك VS Code:
1. إغلاق النوافذ غير المستخدمة
2. تعطيل الإضافات غير الضرورية
3. تقليل عدد الملفات المفتوحة
4. استخدام Workspace بدلاً من مجلدات متعددة

#### لتحسين المراقبة:
1. زيادة فترة المراقبة للأنظمة البطيئة
2. تقليل عدد التنبيهات المسموحة
3. استخدام الحفظ في الملفات بدلاً من وحدة التحكم

## أمثلة الاستخدام

### مراقبة يومية
```bash
# تشغيل فحص صباحي
python scripts/vscode_process_monitor.py

# مراقبة أثناء العمل (كل 5 دقائق)
python scripts/vscode_process_alerts.py --monitor --interval 300
```

### تحليل مشاكل الأداء
```bash
# فحص مفصل مع حفظ التقرير
python scripts/run_vscode_monitor.py
# اختر الخيار 1 للفحص الواحد

# مراجعة التقارير السابقة
python scripts/run_vscode_monitor.py
# اختر الخيار 6 لعرض التقارير
```

### مراقبة مستمرة للخادم
```bash
# مراقبة مستمرة مع تنبيهات
nohup python scripts/vscode_process_alerts.py --monitor &

# فحص سجل التنبيهات
tail -f logs/vscode_alerts.log
```

## الدعم والمساعدة

### للحصول على المساعدة:
1. تشغيل `python scripts/run_vscode_monitor.py` واختيار الخيار 3 لمعلومات النظام
2. مراجعة ملفات السجل في مجلد `logs/`
3. التحقق من التقارير في مجلد `reports/`

### للإبلاغ عن مشاكل:
- تضمين معلومات النظام
- إرفاق ملفات السجل
- وصف الخطوات المتبعة

---

**ملاحظة**: هذه السكريبتات مصممة لمراقبة عمليات VS Code المحلية وقد تحتاج تعديل للبيئات المختلفة.
