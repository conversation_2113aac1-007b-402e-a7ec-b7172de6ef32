# Quick VS Code Process Count Check
# فحص سريع لعدد عمليات VS Code

Write-Host "🚨 QUICK VS CODE PROCESS CHECK" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red
Write-Host ""

# البحث عن عمليات VS Code
$vscodeProcesses = Get-Process | Where-Object { $_.ProcessName -like "*code*" }
$processCount = $vscodeProcesses.Count

Write-Host "📊 VS Code Process Count: $processCount" -ForegroundColor Yellow

# تحديد مستوى الخطر
if ($processCount -ge 70) {
    Write-Host "🔴 CRITICAL EMERGENCY!" -ForegroundColor Red
    Write-Host "You have $processCount processes - this matches your 72+ situation!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🚨 IMMEDIATE ACTIONS REQUIRED:" -ForegroundColor Red
    Write-Host "1. 💾 SAVE ALL YOUR WORK RIGHT NOW!" -ForegroundColor Yellow
    Write-Host "2. 🔴 Close VS Code completely (all windows)" -ForegroundColor Yellow
    Write-Host "3. ⏳ Wait 30 seconds for processes to terminate" -ForegroundColor Yellow
    Write-Host "4. ⚡ Restart VS Code with: code --disable-extensions" -ForegroundColor Yellow
    Write-Host "5. 📊 Open only ONE workspace" -ForegroundColor Yellow
}
elseif ($processCount -gt 50) {
    Write-Host "🟠 HIGH DANGER!" -ForegroundColor Red
    Write-Host "Very high process count - action needed!" -ForegroundColor Yellow
}
elseif ($processCount -gt 30) {
    Write-Host "🟡 MODERATE LOAD" -ForegroundColor Yellow
    Write-Host "Elevated process count - monitoring needed" -ForegroundColor Yellow
}
elseif ($processCount -gt 15) {
    Write-Host "🟢 NORMAL LOAD" -ForegroundColor Green
    Write-Host "Process count is acceptable" -ForegroundColor Green
}
else {
    Write-Host "✅ OPTIMAL" -ForegroundColor Green
    Write-Host "VS Code is running efficiently" -ForegroundColor Green
}

Write-Host ""

# عرض أعلى العمليات استهلاكاً للذاكرة
if ($processCount -gt 0) {
    Write-Host "🔥 Top Memory-Consuming VS Code Processes:" -ForegroundColor Cyan
    Write-Host "PID`t`tName`t`t`tMemory (MB)" -ForegroundColor White
    Write-Host "--------------------------------------------" -ForegroundColor White
    
    $vscodeProcesses | Sort-Object WorkingSet -Descending | Select-Object -First 10 | ForEach-Object {
        $memoryMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "$($_.Id)`t`t$($_.ProcessName)`t`t$memoryMB MB" -ForegroundColor White
    }
}

Write-Host ""

# أدوات الطوارئ المتاحة
if ($processCount -gt 30) {
    Write-Host "🛠️ EMERGENCY TOOLS AVAILABLE:" -ForegroundColor Magenta
    Write-Host "• Run emergency_vscode_fix.bat" -ForegroundColor White
    Write-Host "• python scripts/vscode_emergency_cleanup.py" -ForegroundColor White
    Write-Host "• python scripts/vscode_heavy_load_analyzer.py" -ForegroundColor White
}

Write-Host ""
Write-Host "================================" -ForegroundColor Red
Write-Host "Scan completed at $(Get-Date)" -ForegroundColor Gray

# إذا كانت الحالة حرجة، اعرض تحذير إضافي
if ($processCount -ge 70) {
    Write-Host ""
    Write-Host "🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨" -ForegroundColor Red
    Write-Host "   EMERGENCY SITUATION DETECTED!" -ForegroundColor Red
    Write-Host "   $processCount processes = CRITICAL LOAD!" -ForegroundColor Red
    Write-Host "   Take action immediately!" -ForegroundColor Red
    Write-Host "🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨" -ForegroundColor Red
}

# انتظار إدخال المستخدم
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
