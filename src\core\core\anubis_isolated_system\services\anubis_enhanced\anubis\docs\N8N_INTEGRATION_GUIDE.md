# 🔗 دليل تكامل نظام أنوبيس مع n8n
## Anubis AI System n8n Integration Guide

**تاريخ الإنشاء**: 2025-07-16  
**الإصدار**: 1.0.0  
**الحالة**: ✅ **جاهز للاستخدام الفوري!**  

---

## 🎯 نظرة عامة

### **ما هو هذا التكامل؟**
تكامل شامل بين نظام أنوبيس للذكاء الاصطناعي و n8n لأتمتة سير العمل، يتيح:
- **تنسيق مرئي** لسير العمل بين النماذج والوكلاء
- **تشغيل تلقائي** للمهام المعقدة
- **مراقبة مباشرة** للعمليات
- **تكامل كامل** مع LangSmith وقاعدة البيانات

### **المكونات الرئيسية:**
- 🏺 **Anubis API Server** - خادم API موحد
- 🔗 **n8n Custom Nodes** - عقد مخصصة لأنوبيس
- 🤖 **Multi-Model Support** - دعم Gemini + Ollama
- 📊 **LangSmith Integration** - تتبع متقدم
- 🗄️ **Database Integration** - حفظ النتائج

---

## 🚀 التشغيل السريع (5 دقائق)

### **الخطوة 1: تشغيل النظام**
```bash
# تشغيل النظام المتكامل
python scripts/start_anubis_n8n_system.py
```

### **الخطوة 2: الوصول للواجهات**
- **n8n Interface**: http://localhost:5678
  - المستخدم: `anubis`
  - كلمة المرور: `anubis2025`
- **Anubis API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### **الخطوة 3: إعداد Credentials في n8n**
1. اذهب إلى Settings → Credentials
2. أضف "Anubis API" credential:
   - API URL: `http://localhost:8000`
   - API Key: `anubis-api-key-2025`

### **الخطوة 4: استيراد Workflow**
1. اذهب إلى Workflows → Import
2. استورد `n8n/workflows/anubis-project-analysis.json`
3. شغل الـ workflow

---

## 📊 الـ Nodes المتاحة

### **🤖 Anubis Ollama Node**
**الوظيفة**: تشغيل نماذج Ollama المحلية

**المعاملات**:
- **Model**: اختيار النموذج (llama3:8b, mistral:7b, phi3:mini)
- **Prompt**: النص المراد معالجته
- **Temperature**: مستوى العشوائية (0-2)
- **Max Tokens**: أقصى عدد رموز
- **Use Input Data**: استخدام بيانات العقدة السابقة

**مثال الاستخدام**:
```json
{
  "model": "llama3:8b",
  "prompt": "حلل هذا الكود وأعط توصيات",
  "temperature": 0.7,
  "maxTokens": 1000,
  "useInputData": true
}
```

### **🧠 Anubis Gemini Node**
**الوظيفة**: تشغيل Gemini كقائد ومنسق

**المعاملات**:
- **Prompt**: المهمة المطلوبة
- **Role**: الدور (Commander, Analyst, Reviewer, Planner)
- **Temperature**: مستوى العشوائية (0-1)
- **Use Input Data**: استخدام السياق السابق
- **Include Metadata**: إضافة معلومات إضافية

**الأدوار المتاحة**:
- **Commander**: المنسق الرئيسي
- **Analyst**: المحلل المفصل
- **Reviewer**: المراجع والمدقق
- **Planner**: واضع الخطط

### **🔧 Anubis Agents Node**
**الوظيفة**: تشغيل وكلاء أنوبيس الذكيين

**الوكلاء المتاحة**:
- **Error Detector**: كشف الأخطاء
- **Project Analyzer**: تحليل المشاريع
- **File Organizer**: تنظيم الملفات
- **Memory Agent**: إدارة الذاكرة
- **Database Agent**: إدارة قاعدة البيانات
- **Smart AI Agent**: الوكيل الذكي العام
- **Code Analyzer**: تحليل الكود

**المعاملات**:
- **Agent Type**: نوع الوكيل
- **Project Path**: مسار المشروع
- **Configuration**: تكوين JSON
- **Verbose**: تفاصيل الإخراج

---

## 🔄 أمثلة Workflows

### **Workflow 1: تحليل مشروع شامل**
```
Manual Trigger
    ↓
Gemini Commander (تحليل وتخطيط)
    ↓
Switch Node (توزيع المهام)
    ↓
Parallel Execution:
├── Error Detection Agent
├── Code Analysis Agent
├── File Organization Agent
├── Llama3 Analysis (عربي)
└── Mistral Code Review (إنجليزي)
    ↓
Merge Results
    ↓
Gemini Final Review
    ↓
Save to Database + LangSmith Tracking
```

### **Workflow 2: إصلاح خطأ تعاوني**
```
Webhook Trigger (خطأ مكتشف)
    ↓
Error Analysis Agent
    ↓
Gemini Problem Analysis
    ↓
Parallel Solutions:
├── Mistral Code Solution
├── Llama3 Explanation
└── Phi3 Quick Test
    ↓
Vote on Best Solution
    ↓
Apply Fix + Document
```

### **Workflow 3: مراجعة كود تلقائية**
```
File Change Trigger
    ↓
Gemini Code Review Plan
    ↓
Parallel Analysis:
├── Code Analyzer Agent
├── Mistral Technical Review
└── Llama3 Documentation Check
    ↓
Consensus Building
    ↓
Generate Report + Notifications
```

---

## 🔧 التكوين المتقدم

### **إعداد متغيرات البيئة**
```bash
# LangSmith Integration
export LANGCHAIN_API_KEY="your-langsmith-key"
export LANGCHAIN_TRACING_V2="true"
export LANGCHAIN_PROJECT="anubis-ai-system"

# n8n Configuration
export N8N_BASIC_AUTH_ACTIVE="true"
export N8N_BASIC_AUTH_USER="anubis"
export N8N_BASIC_AUTH_PASSWORD="anubis2025"
export N8N_HOST="0.0.0.0"
export N8N_PORT="5678"
```

### **تخصيص API Endpoints**
```python
# في anubis_api_server.py
@app.post("/api/v1/custom/workflow")
async def custom_workflow(request: CustomRequest):
    # تنفيذ مخصص
    pass
```

### **إضافة Nodes جديدة**
```typescript
// في n8n/nodes/CustomNode.node.ts
export class CustomAnubisNode implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'Custom Anubis Node',
        name: 'customAnubisNode',
        // ... التكوين
    };
}
```

---

## 📊 المراقبة والتتبع

### **LangSmith Dashboard**
- **الرابط**: https://smith.langchain.com/
- **المشروع**: anubis-ai-system
- **المراقبة**: جميع العمليات مُتتبعة تلقائياً

### **Anubis API Monitoring**
```bash
# فحص صحة النظام
curl http://localhost:8000/health

# مراقبة الأداء
curl http://localhost:8000/api/v1/database/projects
```

### **n8n Execution Logs**
- **الوصول**: n8n Interface → Executions
- **التفاصيل**: عرض تفصيلي لكل تنفيذ
- **الأخطاء**: تتبع وحل المشاكل

---

## 🛠️ استكشاف الأخطاء

### **مشاكل شائعة وحلولها**

#### **1. خطأ في الاتصال بـ API**
```
Error: Connection refused to localhost:8000
```
**الحل**:
```bash
# تأكد من تشغيل Anubis API
python api/anubis_api_server.py

# فحص الحالة
curl http://localhost:8000/health
```

#### **2. خطأ في نماذج Ollama**
```
Error: Model not available
```
**الحل**:
```bash
# فحص النماذج المثبتة
ollama list

# تشغيل النموذج
ollama run llama3:8b
```

#### **3. خطأ في Credentials**
```
Error: Invalid API Key
```
**الحل**:
- تأكد من API Key: `anubis-api-key-2025`
- تحقق من URL: `http://localhost:8000`

#### **4. خطأ في n8n Authentication**
```
Error: Unauthorized
```
**الحل**:
- المستخدم: `anubis`
- كلمة المرور: `anubis2025`

---

## 🔄 التطوير والتخصيص

### **إضافة نموذج جديد**
1. **في API Server**:
```python
@app.post("/api/v1/models/new-model/generate")
async def new_model_generate(request: NewModelRequest):
    # تنفيذ النموذج الجديد
    pass
```

2. **في n8n Node**:
```typescript
// إضافة خيار جديد في AnubisOllama.node.ts
{
    name: 'New Model',
    value: 'new-model',
    description: 'Description of new model'
}
```

### **إضافة وكيل جديد**
1. **إنشاء الوكيل**:
```python
# في agents/new_agent.py
class NewAgent:
    def run_analysis(self):
        # تنفيذ الوكيل
        pass
```

2. **إضافة للـ API**:
```python
# في anubis_api_server.py
elif request.agent_type == "new_agent":
    agent = NewAgent(request.project_path, request.config, request.verbose)
```

### **إنشاء Workflow مخصص**
1. **تصميم في n8n Interface**
2. **تصدير JSON**
3. **حفظ في `n8n/workflows/`**
4. **توثيق الاستخدام**

---

## 📈 أفضل الممارسات

### **لتحسين الأداء**:
- استخدم **phi3:mini** للمهام السريعة
- استخدم **llama3:8b** للنصوص العربية
- استخدم **mistral:7b** للكود الإنجليزي
- فعل **caching** للنتائج المتكررة

### **للأمان**:
- غير **API Key** الافتراضي
- استخدم **HTTPS** في الإنتاج
- فعل **rate limiting**
- راقب **الوصول غير المصرح**

### **للصيانة**:
- راقب **استهلاك الموارد**
- نظف **السجلات القديمة**
- حدث **النماذج** بانتظام
- اعمل **نسخ احتياطية** للـ workflows

---

## 🎯 حالات الاستخدام المتقدمة

### **1. CI/CD Integration**
```yaml
# في .github/workflows/anubis-analysis.yml
- name: Run Anubis Analysis
  run: |
    curl -X POST http://localhost:8000/api/v1/collaboration/analyze \
      -H "X-API-Key: anubis-api-key-2025" \
      -d '{"task": "CI Analysis", "models": ["llama3:8b"], "agents": ["error_detector"]}'
```

### **2. Scheduled Analysis**
```json
// n8n Cron Trigger
{
  "mode": "everyHour",
  "hour": 1,
  "workflow": "anubis-project-analysis"
}
```

### **3. Webhook Integration**
```javascript
// استقبال webhooks من GitHub/GitLab
{
  "trigger": "webhook",
  "path": "/anubis-webhook",
  "method": "POST"
}
```

---

## 📚 الموارد الإضافية

### **الوثائق**:
- **API Documentation**: http://localhost:8000/docs
- **n8n Documentation**: https://docs.n8n.io/
- **LangSmith Guide**: https://docs.langchain.com/langsmith

### **الأمثلة**:
- **Workflow Examples**: `n8n/workflows/`
- **API Examples**: `api/examples/`
- **Integration Tests**: `tests/integration/`

### **الدعم**:
- **GitHub Issues**: للمشاكل والاقتراحات
- **Community Forum**: للنقاش والمساعدة
- **Documentation**: للمراجع التفصيلية

---

## 🎉 الخلاصة

### **✅ ما تم تحقيقه:**
- **تكامل كامل** بين أنوبيس و n8n
- **واجهة مرئية** لسير العمل
- **دعم متعدد النماذج** (Gemini + Ollama)
- **تتبع متقدم** مع LangSmith
- **قاعدة بيانات متكاملة**

### **🚀 الاستخدام الفوري:**
1. **شغل النظام**: `python scripts/start_anubis_n8n_system.py`
2. **افتح n8n**: http://localhost:5678
3. **استورد workflow**: من `n8n/workflows/`
4. **شغل التحليل**: بضغطة زر واحدة!

### **🎯 القيمة المضافة:**
- **أتمتة كاملة** للمهام المعقدة
- **تعاون ذكي** بين النماذج
- **مراقبة شاملة** للعمليات
- **قابلية توسع** لا محدودة

---

<div align="center">

**🔗 نظام أنوبيس + n8n = قوة أتمتة لا محدودة!**

**تكامل مثالي للذكاء الاصطناعي مع أتمتة سير العمل**

[![API](https://img.shields.io/badge/API-Ready-brightgreen.svg)](http://localhost:8000)
[![n8n](https://img.shields.io/badge/n8n-Integrated-blue.svg)](http://localhost:5678)
[![LangSmith](https://img.shields.io/badge/LangSmith-Tracked-gold.svg)](https://smith.langchain.com/)
[![Workflows](https://img.shields.io/badge/Workflows-Automated-success.svg)](README.md)

**🏺 نظام أنوبيس - الآن مع قوة n8n للأتمتة الذكية! 🚀**

</div>
