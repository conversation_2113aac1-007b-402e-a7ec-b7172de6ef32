@echo off
color 0A
title VS Code Control Center - الواجهة المثلى

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █           🎯 VS Code Control Center 🎯                     █
echo █                                                              █
echo █  الواجهة المثلى - مراقبة ذكية وتحسين متقدم لـ VS Code      █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.
echo 🚀 تشغيل الواجهة الموحدة المثلى...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تحميل وتثبيت Python من: https://python.org
    echo 📋 تأكد من إضافة Python إلى PATH
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من المكتبات الأساسية
echo 📦 التحقق من المكتبات المطلوبة...
python -c "import tkinter, psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 تثبيت المكتبات الأساسية...
    pip install psutil
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات الأساسية!
        echo 💡 جرب تشغيل الأمر يدوياً: pip install psutil
        pause
        exit /b 1
    )
)

REM التحقق من مكتبات الوكلاء الذكيين (اختيارية)
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🤖 تثبيت مكتبات الوكلاء الذكيين...
    pip install requests >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️ تحذير: سيعمل التطبيق بوضع أساسي (بدون وكلاء ذكيين)
    ) else (
        echo ✅ تم تثبيت مكتبات الوكلاء الذكيين
    )
) else (
    echo ✅ مكتبات الوكلاء الذكيين متوفرة
)

echo.
echo 🎯 المميزات المتاحة:
echo    📊 مراقبة حية للنظام وVS Code
echo    🎨 واجهة جميلة مع ألوان تحذيرية ذكية
echo    🔄 تحديث تلقائي كل 3 ثواني
echo    🧹 أدوات تنظيف وتحسين
echo    💾 حفظ التقارير التفصيلية

python -c "import sys; sys.path.append('agents'); from agents.agent_coordinator import AgentCoordinator" >nul 2>&1
if %errorlevel% equ 0 (
    echo    🤖 وكلاء ذكيين للتحليل المتقدم
    echo    💬 محادثة مع الوكلاء
    echo    🔍 توصيات مخصصة وذكية
)

echo.
echo 🎛️ تشغيل الواجهة المثلى...
echo.

REM تشغيل التطبيق الموحد
python vscode_control_center.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق!
    echo 💡 تأكد من وجود الملف: vscode_control_center.py
    echo 🔧 أو تحقق من تثبيت المكتبات المطلوبة
    pause
    exit /b 1
)

echo.
echo 👋 تم إغلاق VS Code Control Center
echo 💡 شكراً لاستخدام الواجهة المثلى!
pause
