{"test_timestamp": "2025-07-19T07:25:21.948656", "tests_run": 4, "tests_passed": 2, "tests_failed": 2, "agent_tests": {"error_detector_agent": {"status": "failed", "error": "النتائج يجب أن تحتوي على errors_found", "execution_time": 0}, "file_organizer_agent": {"status": "failed", "error": "النتائج يجب أن تحتوي على organization_suggestions", "execution_time": 0}, "database_agent": {"status": "passed", "execution_time": 0.04, "details": {"status": "completed", "databases_found": 3, "total_tables": 21, "total_records": 42, "database_analysis": {"C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 9, "table_analysis": {"projects": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 14, "table_analysis": {"projects": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 4, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 19, "table_analysis": {"projects": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 6, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}}, "recommendations": ["جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db"], "timestamp": "2025-07-19T07:25:23.309804"}}}, "system_tests": {"integrated_system": {"status": "passed", "execution_time": 0.04, "agents_run": 1, "details": {"project_info": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "name": "anubis", "type": "custom", "analysis_time": "2025-07-19T07:25:23.321612"}, "agents_results": {"database_agent": {"status": "completed", "databases_found": 3, "total_tables": 21, "total_records": 42, "database_analysis": {"C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 9, "table_analysis": {"projects": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 14, "table_analysis": {"projects": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 4, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 2, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}, "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db": {"file_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "file_size": 32768, "tables": ["projects", "sqlite_sequence", "analyses", "errors", "reports", "plugins", "activities"], "table_count": 7, "total_records": 19, "table_analysis": {"projects": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "path", "type": "TEXT", "not_null": true}, {"name": "type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false}, {"name": "status", "type": "VARCHAR(50)", "not_null": false}], "column_count": 8}, "sqlite_sequence": {"record_count": 4, "columns": [{"name": "name", "type": "", "not_null": false}, {"name": "seq", "type": "", "not_null": false}], "column_count": 2}, "analyses": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "agent_type", "type": "VARCHAR(100)", "not_null": true}, {"name": "analysis_data", "type": "TEXT", "not_null": false}, {"name": "results", "type": "TEXT", "not_null": false}, {"name": "score", "type": "DECIMAL(5,2)", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "errors": {"record_count": 6, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "line_number", "type": "INTEGER", "not_null": false}, {"name": "error_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "severity", "type": "VARCHAR(20)", "not_null": false}, {"name": "message", "type": "TEXT", "not_null": false}, {"name": "fixed", "type": "BOOLEAN", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 9}, "reports": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "report_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "report_data", "type": "TEXT", "not_null": false}, {"name": "file_path", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 6}, "plugins": {"record_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "name", "type": "VARCHAR(255)", "not_null": true}, {"name": "version", "type": "VARCHAR(50)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false}, {"name": "config", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}, "activities": {"record_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false}, {"name": "project_id", "type": "INTEGER", "not_null": false}, {"name": "activity_type", "type": "VARCHAR(100)", "not_null": false}, {"name": "description", "type": "TEXT", "not_null": false}, {"name": "user_agent", "type": "VARCHAR(100)", "not_null": false}, {"name": "metadata", "type": "TEXT", "not_null": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false}], "column_count": 7}}, "status": "success"}}, "recommendations": ["جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130707.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130746.db", "جدول فارغ: reports في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db", "جدول فارغ: plugins في C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\workspace\\backups\\anubis_backup_20250714_130858.db"], "timestamp": "2025-07-19T07:25:23.361386", "agent_info": {"agent_name": "DatabaseAgent", "agent_type": "database_agent", "version": "1.0.0", "status": "completed", "is_initialized": true, "last_run": "2025-07-19T07:25:23.361386", "project_path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis", "log_file": "workspace\\logs\\database_agent_agent.log"}, "success": true}}, "summary": {"total_agents": 1, "successful_agents": 1, "failed_agents": 0, "success_rate": 100.0, "successful_agents_list": ["database_agent"], "failed_agents_list": []}, "success": true}}}, "performance_metrics": {"total_execution_time": 0.08, "average_agent_time": 0.01, "fastest_agent": ["error_detector_agent", 0], "slowest_agent": ["database_agent", 0.04], "performance_rating": "مم<PERSON><PERSON><PERSON>"}, "overall_status": "mostly_failed"}