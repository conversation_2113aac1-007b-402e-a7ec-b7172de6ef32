# ═══════════════════════════════════════════════════════════════
# ملف إعدادات التشغيل السريع لنظام أنوبيس
# Anubis Quick Start Environment Configuration
# ═══════════════════════════════════════════════════════════════

# ⚠️ تحذير: هذه إعدادات للتشغيل السريع فقط!
# ⚠️ Warning: These are quick start settings only!
# 🔐 يجب تغيير كلمات المرور في بيئة الإنتاج
# 🔐 Must change passwords in production environment

# ═══════════════════════════════════════════════════════════════
# إعدادات التطبيق الأساسية - Basic Application Settings
# ═══════════════════════════════════════════════════════════════

APP_NAME=Anubis AI System - Quick Start
APP_VERSION=1.0.0-quickstart
APP_DESCRIPTION=نظام أنوبيس للذكاء الاصطناعي والأتمتة - تشغيل سريع
DEBUG=true
ENVIRONMENT=development

# إعدادات الخادم - Server Settings
HOST=localhost
PORT=8000
RELOAD=true

# وضع التشغيل السريع - Quick Start Mode
QUICK_START_MODE=true
MINIMAL_SETUP=true

# ═══════════════════════════════════════════════════════════════
# قاعدة البيانات - Database Configuration
# ═══════════════════════════════════════════════════════════════

# SQLite للتشغيل السريع - SQLite for Quick Start
DATABASE_TYPE=sqlite
DATABASE_URL=sqlite:///./data/anubis_quickstart.db
DATABASE_ECHO=false

# إعدادات اتصال قاعدة البيانات - Database Connection Settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# ═══════════════════════════════════════════════════════════════
# الأمان الأساسي - Basic Security
# ═══════════════════════════════════════════════════════════════

# مفاتيح التشفير (يجب تغييرها!) - Encryption Keys (Must Change!)
SECRET_KEY=anubis-quickstart-secret-key-CHANGE-IN-PRODUCTION-123456789
JWT_SECRET_KEY=anubis-jwt-secret-CHANGE-IN-PRODUCTION-987654321
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# إعدادات كلمات المرور - Password Settings
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=false
PASSWORD_REQUIRE_LOWERCASE=false
PASSWORD_REQUIRE_NUMBERS=false
PASSWORD_REQUIRE_SYMBOLS=false

# ═══════════════════════════════════════════════════════════════
# المصادقة والتخويل - Authentication and Authorization
# ═══════════════════════════════════════════════════════════════

# إعدادات المصادقة - Authentication Settings
AUTH_ENABLED=true
REGISTRATION_ENABLED=true
EMAIL_VERIFICATION_REQUIRED=false
TWO_FACTOR_AUTH_ENABLED=false

# المستخدم الافتراضي - Default User
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=anubis123
DEFAULT_ADMIN_USERNAME=admin

# ═══════════════════════════════════════════════════════════════
# خدمات الذكاء الاصطناعي - AI Services
# ═══════════════════════════════════════════════════════════════

# تفعيل خدمات الذكاء الاصطناعي - Enable AI Services
AI_SERVICES_ENABLED=false
AI_SERVICES_MOCK_MODE=true

# OpenAI (اختياري للتشغيل السريع) - OpenAI (Optional for Quick Start)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL_DEFAULT=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Google Gemini (اختياري) - Google Gemini (Optional)
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL_DEFAULT=gemini-pro

# Anthropic Claude (اختياري) - Anthropic Claude (Optional)
CLAUDE_API_KEY=your-claude-api-key-here

# Ollama المحلي (اختياري) - Local Ollama (Optional)
OLLAMA_ENABLED=false
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL_DEFAULT=llama2

# ═══════════════════════════════════════════════════════════════
# Redis والتخزين المؤقت - Redis and Caching
# ═══════════════════════════════════════════════════════════════

# Redis (اختياري للتشغيل السريع) - Redis (Optional for Quick Start)
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_EXPIRE_SECONDS=3600

# التخزين المؤقت في الذاكرة - In-Memory Caching
MEMORY_CACHE_ENABLED=true
MEMORY_CACHE_SIZE=100

# ═══════════════════════════════════════════════════════════════
# أتمتة سير العمل - Workflow Automation
# ═══════════════════════════════════════════════════════════════

# N8N (اختياري للتشغيل السريع) - N8N (Optional for Quick Start)
N8N_ENABLED=false
N8N_HOST=localhost
N8N_PORT=5678
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# ═══════════════════════════════════════════════════════════════
# المراقبة والسجلات - Monitoring and Logging
# ═══════════════════════════════════════════════════════════════

# إعدادات السجلات - Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=detailed
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/anubis_quickstart.log
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5

# المراقبة (اختياري) - Monitoring (Optional)
MONITORING_ENABLED=false
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false

# ═══════════════════════════════════════════════════════════════
# إعدادات الأداء - Performance Settings
# ═══════════════════════════════════════════════════════════════

# إعدادات الأداء - Performance Configuration
MAX_WORKERS=1
WORKER_TIMEOUT=30
KEEP_ALIVE_TIMEOUT=5
MAX_REQUESTS_PER_MINUTE=100

# حدود الذاكرة - Memory Limits
MAX_MEMORY_USAGE_MB=512
MAX_REQUEST_SIZE_MB=10

# ═══════════════════════════════════════════════════════════════
# إعدادات التطوير - Development Settings
# ═══════════════════════════════════════════════════════════════

# أدوات التطوير - Development Tools
DEV_MODE=true
AUTO_RELOAD=true
SHOW_DOCS=true
SHOW_REDOC=true

# CORS للتطوير - CORS for Development
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:8000"]
CORS_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_HEADERS=["*"]

# ═══════════════════════════════════════════════════════════════
# إعدادات الملفات والتخزين - File and Storage Settings
# ═══════════════════════════════════════════════════════════════

# مجلدات النظام - System Directories
DATA_DIR=./data
LOGS_DIR=./logs
UPLOADS_DIR=./data/uploads
TEMP_DIR=./data/temp
BACKUP_DIR=./data/backups

# حدود الملفات - File Limits
MAX_UPLOAD_SIZE_MB=50
ALLOWED_FILE_TYPES=["txt", "json", "csv", "pdf", "docx", "xlsx"]

# ═══════════════════════════════════════════════════════════════
# إعدادات الشبكة - Network Settings
# ═══════════════════════════════════════════════════════════════

# إعدادات HTTP - HTTP Settings
HTTP_TIMEOUT=30
HTTP_RETRIES=3
HTTP_BACKOFF_FACTOR=0.3

# إعدادات SSL (للإنتاج) - SSL Settings (For Production)
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# ═══════════════════════════════════════════════════════════════
# إعدادات إضافية - Additional Settings
# ═══════════════════════════════════════════════════════════════

# المنطقة الزمنية - Timezone
TIMEZONE=Asia/Riyadh

# اللغة الافتراضية - Default Language
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=["ar", "en"]

# إعدادات البريد الإلكتروني (اختياري) - Email Settings (Optional)
EMAIL_ENABLED=false
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# ═══════════════════════════════════════════════════════════════
# ملاحظات مهمة - Important Notes
# ═══════════════════════════════════════════════════════════════

# 1. هذا ملف إعدادات للتشغيل السريع والتطوير فقط
#    This is a configuration file for quick start and development only

# 2. في بيئة الإنتاج، يجب:
#    In production environment, you must:
#    - تغيير جميع كلمات المرور والمفاتيح
#    - تفعيل SSL/HTTPS
#    - استخدام قاعدة بيانات خارجية (MySQL/PostgreSQL)
#    - تفعيل المراقبة والسجلات المتقدمة

# 3. لنسخ هذا الملف كإعدادات افتراضية:
#    To copy this file as default settings:
#    cp .env.quickstart .env

# 4. لتخصيص الإعدادات، عدل ملف .env
#    To customize settings, edit the .env file
