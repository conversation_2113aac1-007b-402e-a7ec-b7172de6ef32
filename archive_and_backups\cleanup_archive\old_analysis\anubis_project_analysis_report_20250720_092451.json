{"main_files": {"main.py": {"exists": true, "description": "الملف الرئيسي للنظام", "size": 13467, "modified": "2025-07-20T06:42:32.566975", "lines": 352}, "README.md": {"exists": true, "description": "التوثيق الأساسي", "size": 5715, "modified": "2025-07-19T17:56:07.868192", "lines": 131}, "requirements.txt": {"exists": true, "description": "متطلبات Python", "size": 195, "modified": "2025-07-20T06:36:57.794916", "lines": 10}, "docker-compose.yml": {"exists": true, "description": "Docker Compose الأساسي", "size": 767, "modified": "2025-07-20T06:44:49.712660", "lines": 34}, "Dockerfile": {"exists": true, "description": "<PERSON>er ال<PERSON><PERSON><PERSON><PERSON>ي", "size": 495, "modified": "2025-07-20T07:05:33.522899", "lines": null}}, "directories": {"anubis_main_system": {"exists": true, "description": "النظام الأساسي", "file_count": 699, "subdirs": ["agents", "api", "configs", "core", "database", "docs", "reports", "tests"]}, "configs": {"exists": true, "description": "ملفات الإعدادات", "file_count": 4, "subdirs": []}, "database": {"exists": true, "description": "قاعدة البيانات", "file_count": 1, "subdirs": []}, "data": {"exists": true, "description": "البيانات", "file_count": 0, "subdirs": []}, "logs": {"exists": true, "description": "ملفات السجلات", "file_count": 8, "subdirs": ["isolation", "operations"]}, "documentation": {"exists": true, "description": "التوثيق", "file_count": 13, "subdirs": ["api_docs", "docs", "guides", "reports", "technical_docs", "tutorials", "user_guides"]}, "reports": {"exists": true, "description": "التقارير", "file_count": 15, "subdirs": ["analysis", "inspection", "organization", "system"]}, "scripts": {"exists": true, "description": "السكريبتات", "file_count": 16, "subdirs": ["analyzers", "checkers", "inspectors", "organizers"]}, "utilities": {"exists": true, "description": "الأدوات المساعدة", "file_count": 5, "subdirs": ["helpers", "optimizers"]}, "archive_and_backups": {"exists": true, "description": "الأرشيف والنسخ الاحتياطية", "file_count": 98, "subdirs": ["archive", "backup", "backups", "deprecated", "old_versions", "temp", "temp_files", "unified_backups"]}, "isolation": {"exists": true, "description": "نظام العزل الجديد", "file_count": 13, "subdirs": ["api", "monitor", "worker"]}, "universal_ai_system": {"exists": true, "description": "نظام AI العام", "file_count": 17, "subdirs": ["configs", "docs", "monitoring", "security", "src", "tests", "Universal-AI-Assistants"]}, "workflows_and_automation": {"exists": true, "description": "سير العمل والأتمتة", "file_count": 35, "subdirs": ["configs", "data", "docs", "logs", "monitoring", "n8n", "n8n_1", "scripts", "security"]}, "tools_and_utilities": {"exists": true, "description": "الأدوات والمرافق", "file_count": 109, "subdirs": ["docs", "optimizers", "scripts", "src", "tests", "vscode_tools"]}, "workspace": {"exists": true, "description": "مساحة العمل", "file_count": 28, "subdirs": ["logs", "monitoring", "reports", "security"]}}, "new_files": [{"name": "anubis_project_analyzer.py", "size": 12204, "modified": "2025-07-20T09:24:36.332345", "lines": 299}, {"name": "anubis_quick_docker_diagnosis.py", "size": 1421, "modified": "2025-07-20T09:23:44.463396", "lines": 44}, {"name": "anubis_gemini_cli_helper.py", "size": 9756, "modified": "2025-07-20T09:23:17.311633", "lines": 264}, {"name": "anubis_gemini_cli_helper.py", "size": 9756, "modified": "2025-07-20T09:23:17.311633", "lines": 264}, {"name": "anubis_isolation_system_manager.py", "size": 9023, "modified": "2025-07-20T09:18:19.071250", "lines": 225}, {"name": "anubis_isolation_system_manager.py", "size": 9023, "modified": "2025-07-20T09:18:19.071250", "lines": 225}, {"name": "anubis_complete_system_test.py", "size": 9409, "modified": "2025-07-20T09:15:08.637280", "lines": 259}, {"name": "anubis_complete_system_test.py", "size": 9409, "modified": "2025-07-20T09:15:08.637280", "lines": 259}, {"name": "anubis_api_comprehensive_test.py", "size": 6851, "modified": "2025-07-20T09:09:35.037734", "lines": 188}, {"name": "anubis_api_comprehensive_test.py", "size": 6851, "modified": "2025-07-20T09:09:35.037734", "lines": 188}, {"name": "anubis_isolation_status_checker.py", "size": 6835, "modified": "2025-07-20T09:01:55.788375", "lines": 199}, {"name": "anubis_isolation_status_checker.py", "size": 6835, "modified": "2025-07-20T09:01:55.788375", "lines": 199}, {"name": "anubis_docker_isolation_launcher.py", "size": 11447, "modified": "2025-07-20T08:53:49.097816", "lines": 307}, {"name": "anubis_docker_isolation_launcher.py", "size": 11447, "modified": "2025-07-20T08:53:49.097816", "lines": 307}, {"name": "docker-compose-anubis-isolation.yml", "size": 8546, "modified": "2025-07-20T08:50:32.666909", "lines": null}, {"name": "anubis_docker_isolation_system.py", "size": 17274, "modified": "2025-07-20T08:49:45.331701", "lines": 484}, {"name": "anubis_docker_isolation_system.py", "size": 17274, "modified": "2025-07-20T08:49:45.331701", "lines": 484}, {"name": "anubis_n8n_monitor.py", "size": 4646, "modified": "2025-07-20T08:47:46.541018", "lines": 144}, {"name": "anubis_n8n_quick_start.py", "size": 5049, "modified": "2025-07-20T08:45:29.840925", "lines": 158}, {"name": "anubis_gemini_assistant_request.py", "size": 10730, "modified": "2025-07-20T07:26:22.466211", "lines": 274}, {"name": "anubis_gemini_assistant_request.py", "size": 10730, "modified": "2025-07-20T07:26:22.466211", "lines": 274}, {"name": "anubis_agents_cline_analyzer.py", "size": 18986, "modified": "2025-07-20T07:24:10.876678", "lines": 382}, {"name": "anubis_cline_conversation_analyzer.py", "size": 13220, "modified": "2025-07-20T07:21:55.817825", "lines": 327}, {"name": "anubis_simple_system_tester.py", "size": 37895, "modified": "2025-07-20T06:18:25.338742", "lines": 828}, {"name": "anubis_simple_system_tester.py", "size": 37895, "modified": "2025-07-20T06:18:25.338742", "lines": 828}, {"name": "anubis_comprehensive_system_tester.py", "size": 31003, "modified": "2025-07-20T06:15:00.760947", "lines": 682}, {"name": "anubis_comprehensive_system_tester.py", "size": 31003, "modified": "2025-07-20T06:15:00.760947", "lines": 682}, {"name": "anubis_services_comprehensive_guide.py", "size": 43964, "modified": "2025-07-19T18:03:29.570276", "lines": 903}, {"name": "final_anubis_organizer_with_gemini.py", "size": 31272, "modified": "2025-07-19T17:55:49.322680", "lines": 722}, {"name": "anubis_comprehensive_organizer.py", "size": 27666, "modified": "2025-07-19T17:49:18.445340", "lines": 627}], "docker_files": [{"name": "docker-compose-anubis-isolation.yml", "path": "docker-compose-anubis-isolation.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "tools_and_utilities\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "universal_ai_system\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose-simple.yml", "path": "workflows_and_automation\\docker-compose-simple.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "workflows_and_automation\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "workspace\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "isolation_systems\\advanced_isolation\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "isolation_systems\\basic_isolation\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.quick.yml", "path": "isolation_configs\\containers\\docker-compose.quick.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "anubis_main_system\\core\\anubis_advanced_isolation\\docker-compose.yml", "type": "docker-compose"}, {"name": "docker-compose.yml", "path": "anubis_main_system\\core\\anubis_isolated_system\\docker-compose.yml", "type": "docker-compose"}, {"name": "Dockerfile", "path": "Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "tools_and_utilities\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "universal_ai_system\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "workflows_and_automation\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "workspace\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "isolation_systems\\basic_isolation\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "isolation\\api\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "isolation\\monitor\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "isolation\\worker\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_isolated_system\\services\\anubis_enhanced\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_isolated_system\\services\\universal_ai\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_advanced_isolation\\containers\\anubis\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_advanced_isolation\\containers\\n8n\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_advanced_isolation\\containers\\tools\\Dockerfile", "type": "dockerfile"}, {"name": "Dockerfile", "path": "anubis_main_system\\core\\anubis_advanced_isolation\\containers\\universal_ai_assistants\\Dockerfile", "type": "dockerfile"}], "running_services": {"main_system": {"port": 8000, "status": "not_running"}, "isolation_system": {"port": 8080, "status": "running", "response": {"status": "healthy", "service": "api", "timestamp": "2025-07-20T06:24:51.264820", "version": "1.0.0", "isolated": true}}, "monitor_system": {"port": 9090, "status": "running", "response": {"status": "healthy", "service": "monitor", "timestamp": "2025-07-20T06:24:51.280555", "isolated": true}}}}