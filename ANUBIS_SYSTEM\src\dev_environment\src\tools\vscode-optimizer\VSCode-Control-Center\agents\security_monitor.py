# -*- coding: utf-8 -*-
"""
🛡️ وكيل مراقبة الأمان - Security Monitor Agent
============================================

وكيل ذكي متخصص في مراقبة أمان النظام وعمليات VS Code
"""

import psutil
import time
import hashlib
import os
from typing import Dict, List, Any
from .base_agent import BaseAgent

class SecurityMonitorAgent(BaseAgent):
    """وكيل مراقبة الأمان الذكي"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("SecurityMonitor", config)
        self.security_level = config.get('security_level', 'medium') if config else 'medium'
        self.known_safe_processes = self._load_safe_processes()
        self.security_alerts = []
        
    def _load_safe_processes(self) -> set:
        """تحميل قائمة العمليات الآمنة المعروفة"""
        safe_processes = {
            'code.exe', 'code', 'electron.exe', 'electron',
            'python.exe', 'python', 'node.exe', 'node',
            'git.exe', 'git', 'cmd.exe', 'powershell.exe',
            'explorer.exe', 'dwm.exe', 'winlogon.exe',
            'csrss.exe', 'wininit.exe', 'services.exe',
            'lsass.exe', 'svchost.exe', 'system'
        }
        return safe_processes
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل أمني شامل للنظام"""
        try:
            analysis = {
                'timestamp': time.time(),
                'security_score': 100,
                'threats_detected': [],
                'suspicious_processes': [],
                'network_connections': [],
                'file_system_alerts': [],
                'vscode_security': {},
                'system_integrity': {},
                'recommendations': []
            }
            
            # فحص العمليات المشبوهة
            analysis['suspicious_processes'] = self._scan_suspicious_processes()
            
            # فحص الاتصالات الشبكية
            analysis['network_connections'] = self._scan_network_connections()
            
            # فحص أمان VS Code
            analysis['vscode_security'] = self._analyze_vscode_security()
            
            # فحص سلامة النظام
            analysis['system_integrity'] = self._check_system_integrity()
            
            # تحديد التهديدات
            analysis['threats_detected'] = self._detect_threats(analysis)
            
            # حساب نقاط الأمان
            analysis['security_score'] = self._calculate_security_score(analysis)
            
            self.save_analysis(analysis)
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في التحليل الأمني: {e}")
            return {'error': str(e)}
    
    def _scan_suspicious_processes(self) -> List[Dict[str, Any]]:
        """فحص العمليات المشبوهة"""
        suspicious = []
        
        try:
            process_names = {}
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'create_time']):
                try:
                    pinfo = proc.info
                    name = pinfo['name'].lower()
                    
                    # عد العمليات بنفس الاسم
                    process_names[name] = process_names.get(name, 0) + 1
                    
                    # فحص العمليات غير المعروفة
                    if name not in self.known_safe_processes:
                        # فحص استهلاك عالي للموارد
                        cpu = proc.cpu_percent()
                        memory = proc.memory_percent()
                        
                        if cpu > 50 or memory > 20:
                            suspicious.append({
                                'pid': pinfo['pid'],
                                'name': pinfo['name'],
                                'cpu': cpu,
                                'memory': memory,
                                'reason': 'استهلاك عالي للموارد + غير معروف',
                                'risk_level': 'متوسط'
                            })
                    
                    # فحص العمليات ذات الأسماء المشبوهة
                    suspicious_patterns = ['temp', 'tmp', 'cache', 'update', 'install']
                    if any(pattern in name for pattern in suspicious_patterns):
                        if proc.cpu_percent() > 30:
                            suspicious.append({
                                'pid': pinfo['pid'],
                                'name': pinfo['name'],
                                'reason': 'اسم مشبوه + نشاط عالي',
                                'risk_level': 'منخفض'
                            })
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # فحص العمليات المتكررة بشكل مفرط
            for name, count in process_names.items():
                if count > 15 and name not in ['svchost.exe', 'cmd.exe']:
                    suspicious.append({
                        'name': name,
                        'count': count,
                        'reason': f'عدد مفرط من العمليات ({count})',
                        'risk_level': 'متوسط'
                    })
                    
        except Exception as e:
            self.logger.error(f"خطأ في فحص العمليات: {e}")
        
        return suspicious[:10]  # أول 10 عمليات مشبوهة
    
    def _scan_network_connections(self) -> List[Dict[str, Any]]:
        """فحص الاتصالات الشبكية"""
        connections = []
        
        try:
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'ESTABLISHED':
                    try:
                        proc = psutil.Process(conn.pid) if conn.pid else None
                        
                        connection_info = {
                            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
                            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else 'N/A',
                            'status': conn.status,
                            'pid': conn.pid,
                            'process_name': proc.name() if proc else 'Unknown'
                        }
                        
                        # فحص الاتصالات المشبوهة
                        if conn.raddr:
                            remote_ip = conn.raddr.ip
                            # فحص الـ IP المحلية المشبوهة
                            if not (remote_ip.startswith('127.') or 
                                   remote_ip.startswith('192.168.') or
                                   remote_ip.startswith('10.') or
                                   remote_ip.startswith('172.')):
                                connection_info['risk_level'] = 'متوسط'
                                connection_info['reason'] = 'اتصال خارجي'
                        
                        connections.append(connection_info)
                        
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
        except Exception as e:
            self.logger.error(f"خطأ في فحص الشبكة: {e}")
        
        return connections[:20]  # أول 20 اتصال
    
    def _analyze_vscode_security(self) -> Dict[str, Any]:
        """تحليل أمان VS Code"""
        vscode_security = {
            'process_count': 0,
            'extensions_risk': 'منخفض',
            'network_activity': [],
            'file_access': [],
            'security_status': 'آمن'
        }
        
        try:
            vscode_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        vscode_processes.append(proc)
                        vscode_security['process_count'] += 1
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # فحص عدد العمليات
            if vscode_security['process_count'] > 10:
                vscode_security['security_status'] = 'تحذير - عمليات كثيرة'
            elif vscode_security['process_count'] == 0:
                vscode_security['security_status'] = 'VS Code غير مفتوح'
            
            # فحص الاتصالات الشبكية لـ VS Code
            for conn in psutil.net_connections(kind='inet'):
                if conn.pid:
                    try:
                        proc = psutil.Process(conn.pid)
                        if 'code' in proc.name().lower():
                            vscode_security['network_activity'].append({
                                'local': f"{conn.laddr.ip}:{conn.laddr.port}",
                                'remote': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else 'N/A',
                                'status': conn.status
                            })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل أمان VS Code: {e}")
        
        return vscode_security
    
    def _check_system_integrity(self) -> Dict[str, Any]:
        """فحص سلامة النظام"""
        integrity = {
            'cpu_usage_normal': True,
            'memory_usage_normal': True,
            'disk_usage_normal': True,
            'process_count_normal': True,
            'overall_status': 'سليم'
        }
        
        try:
            # فحص استخدام المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                integrity['cpu_usage_normal'] = False
                integrity['overall_status'] = 'تحذير'
            
            # فحص استخدام الذاكرة
            memory = psutil.virtual_memory()
            if memory.percent > 95:
                integrity['memory_usage_normal'] = False
                integrity['overall_status'] = 'تحذير'
            
            # فحص استخدام القرص
            disk = psutil.disk_usage('/')
            if disk.percent > 95:
                integrity['disk_usage_normal'] = False
                integrity['overall_status'] = 'تحذير'
            
            # فحص عدد العمليات
            process_count = len(psutil.pids())
            if process_count > 500:  # حد تقريبي
                integrity['process_count_normal'] = False
                integrity['overall_status'] = 'تحذير'
                
        except Exception as e:
            self.logger.error(f"خطأ في فحص سلامة النظام: {e}")
            integrity['overall_status'] = 'خطأ في الفحص'
        
        return integrity
    
    def _detect_threats(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تحديد التهديدات المحتملة"""
        threats = []
        
        # تهديدات من العمليات المشبوهة
        suspicious_procs = analysis.get('suspicious_processes', [])
        for proc in suspicious_procs:
            if proc.get('risk_level') == 'عالي':
                threats.append({
                    'type': 'suspicious_process',
                    'description': f"عملية مشبوهة: {proc.get('name')}",
                    'severity': 'عالي',
                    'details': proc
                })
        
        # تهديدات من الشبكة
        network_conns = analysis.get('network_connections', [])
        risky_connections = [conn for conn in network_conns if conn.get('risk_level') == 'متوسط']
        if len(risky_connections) > 5:
            threats.append({
                'type': 'network_activity',
                'description': f"نشاط شبكي مشبوه ({len(risky_connections)} اتصالات)",
                'severity': 'متوسط',
                'details': risky_connections[:3]
            })
        
        # تهديدات من سلامة النظام
        system_integrity = analysis.get('system_integrity', {})
        if system_integrity.get('overall_status') == 'تحذير':
            threats.append({
                'type': 'system_integrity',
                'description': 'مشكلة في سلامة النظام',
                'severity': 'متوسط',
                'details': system_integrity
            })
        
        return threats
    
    def _calculate_security_score(self, analysis: Dict[str, Any]) -> int:
        """حساب نقاط الأمان (من 100)"""
        score = 100
        
        # خصم نقاط حسب التهديدات
        threats = analysis.get('threats_detected', [])
        for threat in threats:
            if threat.get('severity') == 'عالي':
                score -= 30
            elif threat.get('severity') == 'متوسط':
                score -= 15
            else:
                score -= 5
        
        # خصم نقاط حسب العمليات المشبوهة
        suspicious_count = len(analysis.get('suspicious_processes', []))
        score -= min(suspicious_count * 5, 25)
        
        # خصم نقاط حسب الاتصالات المشبوهة
        risky_connections = len([conn for conn in analysis.get('network_connections', []) 
                               if conn.get('risk_level') == 'متوسط'])
        score -= min(risky_connections * 3, 15)
        
        return max(0, score)
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات الأمان"""
        if 'error' in analysis:
            return ['❌ خطأ في الحصول على توصيات الأمان']
        
        recommendations = []
        
        # توصيات حسب النقاط
        score = analysis.get('security_score', 50)
        if score < 50:
            recommendations.append('🚨 النظام يواجه مخاطر أمنية عالية!')
        elif score < 75:
            recommendations.append('⚠️ النظام يحتاج تحسينات أمنية')
        else:
            recommendations.append('✅ النظام آمن نسبياً')
        
        # توصيات من التهديدات
        threats = analysis.get('threats_detected', [])
        for threat in threats:
            if threat.get('severity') == 'عالي':
                recommendations.append(f"🚨 تهديد عالي: {threat.get('description')}")
            else:
                recommendations.append(f"⚠️ تحذير: {threat.get('description')}")
        
        # توصيات عامة
        if analysis.get('suspicious_processes'):
            recommendations.append('🔍 فحص العمليات المشبوهة وإزالة غير الضروري')
        
        if analysis.get('network_connections'):
            recommendations.append('🌐 مراقبة الاتصالات الشبكية')
        
        return recommendations or ['لا توجد توصيات أمنية محددة']
