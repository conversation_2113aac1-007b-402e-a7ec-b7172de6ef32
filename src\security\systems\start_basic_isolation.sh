#!/bin/bash
# سكريبت تشغيل نظام العزل الأساسي

echo "🏺 بدء تشغيل نظام العزل الأساسي لأنوبيس..."

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً."
    exit 1
fi

# التحقق من وجود docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت. يرجى تثبيت docker-compose أولاً."
    exit 1
fi

# الانتقال لمجلد العزل الأساسي
cd isolation_systems/basic_isolation

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات..."
mkdir -p data logs configs

# تعيين الصلاحيات
echo "🔐 تعيين الصلاحيات الآمنة..."
chmod 750 data logs
chmod 740 configs

# بناء الحاوية
echo "🔨 بناء حاوية العزل الأساسي..."
docker-compose build

# تشغيل النظام
echo "🚀 تشغيل نظام العزل الأساسي..."
docker-compose up -d

# التحقق من الحالة
echo "🔍 فحص حالة النظام..."
sleep 10
docker-compose ps

echo "✅ تم تشغيل نظام العزل الأساسي بنجاح!"
echo "🌐 النظام متاح على: http://localhost:8001"
echo "📋 لعرض السجلات: docker-compose logs -f"
echo "🛑 لإيقاف النظام: docker-compose down"
