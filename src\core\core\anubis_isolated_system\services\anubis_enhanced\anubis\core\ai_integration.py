#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 نظام دمج الذكاء الاصطناعي مع وكلاء أنوبيس
Anubis AI Integration System

دعم لنماذج الذكاء الاصطناعي المختلفة مع الوكلاء
"""

import json
import os
import subprocess
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import requests


class AIProvider(ABC):
    """الفئة الأساسية لموفري الذكاء الاصطناعي"""

    @abstractmethod
    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """توليد استجابة من النموذج"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """فحص توفر النموذج"""
        pass


class OllamaProvider(AIProvider):
    """موفر نماذج Ollama المحلية"""

    def __init__(self, model_name: str = "llama3.2", host: str = "localhost", port: int = 11434):
        """تهيئة موفر Ollama"""
        self.model_name = model_name
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"

    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """توليد استجابة من نموذج Ollama"""
        try:
            url = f"{self.base_url}/api/generate"

            # إعداد البيانات
            data = {"model": self.model_name, "prompt": prompt, "stream": False}

            # إضافة السياق إذا كان متوفراً
            if context:
                enhanced_prompt = (
                    f"Context: {json.dumps(context, ensure_ascii=False)}\n\nPrompt: {prompt}"
                )
                data["prompt"] = enhanced_prompt

            # إرسال الطلب
            response = requests.post(url, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "")

        except Exception as e:
            return f"خطأ في الاتصال بـ Ollama: {e}"

    def is_available(self) -> bool:
        """فحص توفر Ollama"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False


class GeminiProvider(AIProvider):
    """موفر Google Gemini"""

    def __init__(self, api_key: str = None, model_name: str = "gemini-pro"):
        """تهيئة موفر Gemini"""
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        self.model_name = model_name
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"

    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """توليد استجابة من Gemini"""
        if not self.api_key:
            return "خطأ: مفتاح API غير متوفر لـ Gemini"

        try:
            url = f"{self.base_url}/models/{self.model_name}:generateContent"

            # إعداد البيانات
            enhanced_prompt = prompt
            if context:
                enhanced_prompt = (
                    f"Context: {json.dumps(context, ensure_ascii=False)}\n\nPrompt: {prompt}"
                )

            data = {"contents": [{"parts": [{"text": enhanced_prompt}]}]}

            headers = {"Content-Type": "application/json"}

            params = {"key": self.api_key}

            # إرسال الطلب
            response = requests.post(url, json=data, headers=headers, params=params, timeout=60)
            response.raise_for_status()

            result = response.json()
            candidates = result.get("candidates", [])
            if candidates:
                content = candidates[0].get("content", {})
                parts = content.get("parts", [])
                if parts:
                    return parts[0].get("text", "")

            return "لم يتم الحصول على استجابة من Gemini"

        except Exception as e:
            return f"خطأ في الاتصال بـ Gemini: {e}"

    def is_available(self) -> bool:
        """فحص توفر Gemini"""
        return self.api_key is not None


class OpenAIProvider(AIProvider):
    """موفر OpenAI"""

    def __init__(self, api_key: str = None, model_name: str = "gpt-3.5-turbo"):
        """تهيئة موفر OpenAI"""
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model_name = model_name
        self.base_url = "https://api.openai.com/v1"

    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """توليد استجابة من OpenAI"""
        if not self.api_key:
            return "خطأ: مفتاح API غير متوفر لـ OpenAI"

        try:
            url = f"{self.base_url}/chat/completions"

            # إعداد الرسائل
            messages = []
            if context:
                messages.append(
                    {
                        "role": "system",
                        "content": f"Context: {json.dumps(context, ensure_ascii=False)}",
                    }
                )

            messages.append({"role": "user", "content": prompt})

            data = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 1000,
                "temperature": 0.7,
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            # إرسال الطلب
            response = requests.post(url, json=data, headers=headers, timeout=60)
            response.raise_for_status()

            result = response.json()
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                return message.get("content", "")

            return "لم يتم الحصول على استجابة من OpenAI"

        except Exception as e:
            return f"خطأ في الاتصال بـ OpenAI: {e}"

    def is_available(self) -> bool:
        """فحص توفر OpenAI"""
        return self.api_key is not None


class AIIntegrationManager:
    """مدير دمج الذكاء الاصطناعي"""

    def __init__(self, config_path: str = "configs/ai_config.json"):
        """تهيئة مدير الذكاء الاصطناعي"""
        self.config_path = Path(config_path)
        self.providers = {}
        self.active_provider = None
        self.load_config()
        self.initialize_providers()

    def load_config(self):
        """تحميل إعدادات الذكاء الاصطناعي"""
        try:
            if self.config_path.exists():
                with open(self.config_path, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            else:
                # إعدادات افتراضية
                self.config = {
                    "default_provider": "ollama",
                    "providers": {
                        "ollama": {
                            "enabled": True,
                            "model": "llama3.2",
                            "host": "localhost",
                            "port": 11434,
                        },
                        "gemini": {
                            "enabled": False,
                            "model": "gemini-pro",
                            "api_key": "",
                        },
                        "openai": {
                            "enabled": False,
                            "model": "gpt-3.5-turbo",
                            "api_key": "",
                        },
                    },
                }
                self.save_config()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الذكاء الاصطناعي: {e}")
            self.config = {}

    def save_config(self):
        """حفظ إعدادات الذكاء الاصطناعي"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الذكاء الاصطناعي: {e}")

    def initialize_providers(self):
        """تهيئة موفري الذكاء الاصطناعي"""
        providers_config = self.config.get("providers", {})

        # تهيئة Ollama
        if providers_config.get("ollama", {}).get("enabled", True):  # افتراضي True
            ollama_config = providers_config.get("ollama", {})
            self.providers["ollama"] = OllamaProvider(
                model_name=ollama_config.get("model", "llama3:8b"),  # تصحيح اسم النموذج
                host=ollama_config.get("host", "localhost"),
                port=ollama_config.get("port", 11434),
            )

        # تهيئة Gemini
        if providers_config.get("gemini", {}).get("enabled", False):
            gemini_config = providers_config["gemini"]
            self.providers["gemini"] = GeminiProvider(
                api_key=gemini_config.get("api_key"),
                model_name=gemini_config.get("model", "gemini-pro"),
            )

        # تهيئة OpenAI
        if providers_config.get("openai", {}).get("enabled", False):
            openai_config = providers_config["openai"]
            self.providers["openai"] = OpenAIProvider(
                api_key=openai_config.get("api_key"),
                model_name=openai_config.get("model", "gpt-3.5-turbo"),
            )

        # تحديد الموفر النشط
        default_provider = self.config.get("default_provider", "ollama")
        if default_provider in self.providers:
            self.active_provider = default_provider

    def get_available_providers(self) -> List[str]:
        """الحصول على قائمة الموفرين المتاحين"""
        available = []
        for name, provider in self.providers.items():
            if provider.is_available():
                available.append(name)
        return available

    def set_active_provider(self, provider_name: str) -> bool:
        """تحديد الموفر النشط"""
        if provider_name in self.providers:
            if self.providers[provider_name].is_available():
                self.active_provider = provider_name
                return True
        return False

    def generate_ai_response(
        self, prompt: str, context: Dict[str, Any] = None, provider: str = None
    ) -> str:
        """توليد استجابة ذكية"""
        # تحديد الموفر
        provider_name = provider or self.active_provider

        if not provider_name or provider_name not in self.providers:
            return "لا يوجد موفر ذكاء اصطناعي متاح"

        provider_instance = self.providers[provider_name]

        if not provider_instance.is_available():
            return f"الموفر {provider_name} غير متاح حالياً"

        # توليد الاستجابة
        return provider_instance.generate_response(prompt, context)

    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        status = {
            "active_provider": self.active_provider,
            "available_providers": self.get_available_providers(),
            "providers_status": {},
        }

        for name, provider in self.providers.items():
            status["providers_status"][name] = {
                "available": provider.is_available(),
                "type": provider.__class__.__name__,
            }

        return status


# مثيل عام لمدير الذكاء الاصطناعي
ai_manager = AIIntegrationManager()
