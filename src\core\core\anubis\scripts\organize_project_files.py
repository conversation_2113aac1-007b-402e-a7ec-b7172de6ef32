#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ منظم ملفات المشروع
Project Files Organizer

تنظيم شامل لجميع ملفات نظام أنوبيس في المجلدات المناسبة
"""

import json
import os
import shutil
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List


class ProjectFilesOrganizer:
    """🗂️ منظم ملفات المشروع"""

    def __init__(self):
        self.base_path = Path(__file__).parent.absolute()
        self.organization_log = []

        # هيكل المجلدات المطلوب
        self.folder_structure = {
            "core": "الملفات الأساسية للنظام",
            "agents": "جميع الوكلاء المحسنة",
            "tests": "ملفات الاختبار",
            "scripts": "سكريبتات التشغيل والإعداد",
            "configs": "ملفات التكوين",
            "docs": "التوثيق والدلائل",
            "reports": "التقارير والنتائج",
            "logs": "ملفات السجلات",
            "backup": "النسخ الاحتياطية",
            "temp": "الملفات المؤقتة",
            "examples": "أمثلة الاستخدام",
            "tools": "أدوات مساعدة",
        }

        # قواعد تصنيف الملفات
        self.file_classification = {
            "agents": {
                "patterns": [
                    "*agent*.py",
                    "*detector*.py",
                    "*analyzer*.py",
                    "*organizer*.py",
                    "*memory*.py",
                ],
                "exclude": ["test_*", "*_test.py"],
            },
            "tests": {
                "patterns": ["test_*.py", "*_test.py", "*test*.py"],
                "exclude": [],
            },
            "scripts": {
                "patterns": [
                    "*fix*.py",
                    "*setup*.py",
                    "*install*.py",
                    "*run*.py",
                    "*start*.py",
                    "*integration*.py",
                    "*manager*.py",
                    "*organize*.py",
                ],
                "exclude": ["test_*"],
            },
            "configs": {
                "patterns": ["*.json", "*.yaml", "*.yml", "*.toml", "*.ini", "*.env"],
                "exclude": ["package*.json"],
            },
            "docs": {"patterns": ["*.md", "*.rst", "*.txt"], "exclude": []},
            "reports": {
                "patterns": ["*report*.json", "*log*.json", "*result*.json"],
                "exclude": [],
            },
        }

        print("🗂️ منظم ملفات نظام أنوبيس")
        print(f"📁 المسار الأساسي: {self.base_path}")

    def create_folder_structure(self):
        """إنشاء هيكل المجلدات"""
        print("\n📂 إنشاء هيكل المجلدات...")

        created_folders = []

        for folder_name, description in self.folder_structure.items():
            folder_path = self.base_path / folder_name

            try:
                folder_path.mkdir(exist_ok=True)
                created_folders.append(folder_name)
                print(f"   📁 {folder_name}: {description}")

                # إنشاء ملف README في كل مجلد
                readme_path = folder_path / "README.md"
                if not readme_path.exists():
                    readme_content = f"""# {folder_name.title()}

{description}

## المحتويات
هذا المجلد يحتوي على {description.lower()}.

## التحديث الأخير
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                    with open(readme_path, "w", encoding="utf-8") as f:
                        f.write(readme_content)

            except Exception as e:
                print(f"   ❌ خطأ في إنشاء {folder_name}: {e}")

        self.organization_log.append(
            {
                "action": "create_folders",
                "created_folders": created_folders,
                "timestamp": datetime.now().isoformat(),
            }
        )

        return created_folders

    def classify_and_move_files(self):
        """تصنيف ونقل الملفات"""
        print("\n🔄 تصنيف ونقل الملفات...")

        moved_files = {}

        # الحصول على جميع ملفات Python في المجلد الرئيسي
        python_files = list(self.base_path.glob("*.py"))

        for file_path in python_files:
            if file_path.name == __file__.split("/")[-1]:  # تجاهل هذا الملف
                continue

            target_folder = self._determine_target_folder(file_path)

            if target_folder:
                target_path = self.base_path / target_folder / file_path.name

                try:
                    # إنشاء نسخة احتياطية إذا كان الملف موجوداً
                    if target_path.exists():
                        backup_path = (
                            self.base_path
                            / "backup"
                            / f"{file_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
                        )
                        shutil.copy2(target_path, backup_path)
                        print(f"   📦 نسخة احتياطية: {backup_path.name}")

                    # نقل الملف
                    shutil.move(str(file_path), str(target_path))

                    if target_folder not in moved_files:
                        moved_files[target_folder] = []
                    moved_files[target_folder].append(file_path.name)

                    print(f"   ➡️ {file_path.name} → {target_folder}/")

                except Exception as e:
                    print(f"   ❌ خطأ في نقل {file_path.name}: {e}")

        # نقل ملفات JSON
        json_files = list(self.base_path.glob("*.json"))
        for file_path in json_files:
            if "report" in file_path.name.lower() or "log" in file_path.name.lower():
                target_folder = "reports"
            else:
                target_folder = "configs"

            target_path = self.base_path / target_folder / file_path.name

            try:
                shutil.move(str(file_path), str(target_path))

                if target_folder not in moved_files:
                    moved_files[target_folder] = []
                moved_files[target_folder].append(file_path.name)

                print(f"   ➡️ {file_path.name} → {target_folder}/")

            except Exception as e:
                print(f"   ❌ خطأ في نقل {file_path.name}: {e}")

        self.organization_log.append(
            {
                "action": "move_files",
                "moved_files": moved_files,
                "timestamp": datetime.now().isoformat(),
            }
        )

        return moved_files

    def _determine_target_folder(self, file_path: Path) -> str:
        """تحديد المجلد المناسب للملف"""
        file_name = file_path.name.lower()

        # فحص كل فئة
        for folder, rules in self.file_classification.items():
            # فحص الاستثناءات أولاً
            if any(
                file_name.startswith(exc.replace("*", ""))
                or file_name.endswith(exc.replace("*", ""))
                for exc in rules["exclude"]
            ):
                continue

            # فحص الأنماط
            for pattern in rules["patterns"]:
                pattern_clean = pattern.replace("*", "")
                if pattern.startswith("*") and pattern.endswith("*"):
                    if pattern_clean in file_name:
                        return folder
                elif pattern.startswith("*"):
                    if file_name.endswith(pattern_clean):
                        return folder
                elif pattern.endswith("*"):
                    if file_name.startswith(pattern_clean):
                        return folder
                else:
                    if file_name == pattern_clean:
                        return folder

        return None

    def update_imports_in_files(self):
        """تحديث المسارات في الملفات"""
        print("\n🔧 تحديث مسارات الاستيراد...")

        updated_files = []

        # تحديث ملفات الوكلاء
        agents_folder = self.base_path / "agents"
        if agents_folder.exists():
            for agent_file in agents_folder.glob("*.py"):
                try:
                    with open(agent_file, "r", encoding="utf-8") as f:
                        content = f.read()

                    # تحديث مسارات الاستيراد
                    updated_content = content.replace(
                        "sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))",
                        "sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))",
                    )

                    if updated_content != content:
                        with open(agent_file, "w", encoding="utf-8") as f:
                            f.write(updated_content)
                        updated_files.append(str(agent_file))
                        print(f"   ✅ تحديث: {agent_file.name}")

                except Exception as e:
                    print(f"   ❌ خطأ في تحديث {agent_file.name}: {e}")

        return updated_files

    def create_project_index(self):
        """إنشاء فهرس المشروع"""
        print("\n📋 إنشاء فهرس المشروع...")

        project_index = {
            "project_name": "نظام أنوبيس للذكاء الاصطناعي",
            "version": "2.0",
            "last_organized": datetime.now().isoformat(),
            "folder_structure": {},
            "file_counts": {},
            "total_files": 0,
        }

        for folder_name in self.folder_structure.keys():
            folder_path = self.base_path / folder_name

            if folder_path.exists():
                files = list(folder_path.glob("*"))
                file_list = [f.name for f in files if f.is_file()]

                project_index["folder_structure"][folder_name] = {
                    "description": self.folder_structure[folder_name],
                    "files": file_list,
                    "count": len(file_list),
                }

                project_index["file_counts"][folder_name] = len(file_list)
                project_index["total_files"] += len(file_list)

        # حفظ الفهرس
        index_file = self.base_path / "docs" / "project_index.json"

        try:
            with open(index_file, "w", encoding="utf-8") as f:
                json.dump(project_index, f, ensure_ascii=False, indent=2)

            print(f"   ✅ تم حفظ فهرس المشروع: {index_file}")
            return str(index_file)

        except Exception as e:
            print(f"   ❌ خطأ في حفظ الفهرس: {e}")
            return None

    def organize_project(self):
        """تنظيم المشروع بالكامل"""
        print("🚀 بدء تنظيم مشروع نظام أنوبيس")
        print("=" * 50)

        # إنشاء هيكل المجلدات
        created_folders = self.create_folder_structure()

        # تصنيف ونقل الملفات
        moved_files = self.classify_and_move_files()

        # تحديث مسارات الاستيراد
        updated_files = self.update_imports_in_files()

        # إنشاء فهرس المشروع
        index_file = self.create_project_index()

        # حفظ سجل التنظيم
        organization_report = {
            "timestamp": datetime.now().isoformat(),
            "created_folders": created_folders,
            "moved_files": moved_files,
            "updated_files": updated_files,
            "index_file": index_file,
            "organization_log": self.organization_log,
        }

        report_file = (
            self.base_path
            / "reports"
            / f'organization_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(organization_report, f, ensure_ascii=False, indent=2)

            print(f"\n📄 تم حفظ تقرير التنظيم: {report_file}")
        except Exception as e:
            print(f"\n❌ خطأ في حفظ تقرير التنظيم: {e}")

        return organization_report


def main():
    """الدالة الرئيسية"""
    organizer = ProjectFilesOrganizer()

    # تنظيم المشروع
    result = organizer.organize_project()

    print(f"\n🏆 تم تنظيم المشروع بنجاح!")
    print(f"📁 المجلدات المنشأة: {len(result['created_folders'])}")
    print(f"📄 الملفات المنقولة: {sum(len(files) for files in result['moved_files'].values())}")
    print(f"🔧 الملفات المحدثة: {len(result['updated_files'])}")

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
