{"backup_system_status": {"timestamp": "2025-07-23T11:38:50.636051", "status": "success", "actions_completed": ["🔑 تم تحميل مفتاح تشفير النسخ الاحتياطية", "💾 تم إنشاء نسخة احتياطية للمفاتيح الآمنة", "💾 تم إنشاء نسخة احتياطية لملفات التدوير", "📅 تم إعداد جدولة النسخ الاحتياطية"], "files_created": ["C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\backups\\backup_scheduler.py"], "backups_created": [{"backup_name": "keys_backup", "started_at": "2025-07-23T11:38:50.637924", "status": "completed", "source_dir": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure", "files_backed_up": 5, "total_size": 11748, "backup_id": "aaa67aaf81c0a9fd0be066822337e15b", "completed_at": "2025-07-23T11:38:50.691994", "encrypted_file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\backups\\local\\keys_backup.encrypted", "manifest": {"backup_id": "aaa67aaf81c0a9fd0be066822337e15b", "created_at": "2025-07-23T11:38:50.639402", "backup_type": "full", "encryption": "AES-256", "files": [{"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure\\access_monitor.py", "size": 1643, "checksum": "4996296a0f39115a111dfb8e6789d952cfcd82dcd9033653110f9b3e089decc1", "last_modified": "2025-07-23T11:38:09.914093"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure\\encrypted_keys_20250723_113809.json", "size": 27425, "checksum": "9703a6cdcbe678ee7d21515a9f3b12ec51309170c7a7f2e329ed29cff01de45f", "last_modified": "2025-07-23T11:38:09.911019"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure\\environment_template.env", "size": 2221, "checksum": "87ac47519d6e08dc9f9f5fd510d2f2bc14b6f3afd76a4d82657b28289a4f08eb", "last_modified": "2025-07-23T11:38:09.913122"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure\\master.key", "size": 60, "checksum": "424c11b5e9f5185163b5e64c4bde06d1a04f306225a48fb9c80843f0ecde6cbb", "last_modified": "2025-07-23T11:38:09.889995"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\secure\\security_report_20250723_113809.json", "size": 493, "checksum": "49e6942e58882010251e44b4e3c288924ff5a43ddf5f5a93b582ad86538def0b", "last_modified": "2025-07-23T11:38:09.916941"}], "total_size": 31842, "checksum": "a5481e5bd3c8fb7c89bc2ead30071ba63eece0d0bca2a9f4e6be7b2d11938644"}}, {"backup_name": "rotation_backup", "started_at": "2025-07-23T11:38:50.692599", "status": "completed", "source_dir": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\rotation", "files_backed_up": 3, "total_size": 2055, "backup_id": "779d1395c63b8b7bfdaaf716c4ae5b19", "completed_at": "2025-07-23T11:38:50.714193", "encrypted_file": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\backups\\local\\rotation_backup.encrypted", "manifest": {"backup_id": "779d1395c63b8b7bfdaaf716c4ae5b19", "created_at": "2025-07-23T11:38:50.693361", "backup_type": "full", "encryption": "AES-256", "files": [{"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\rotation\\rotation_log_20250723_113832.json", "size": 2, "checksum": "4f53cda18c2baa0c0354bb5f9a3ecbe5ed12ab4d8e11ba873c2f11161202b945", "last_modified": "2025-07-23T11:38:32.574175"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\rotation\\rotation_plan_20250723_113832.json", "size": 142, "checksum": "daa31a50761b6a3ed028f0804a0e61f7b4ff5edae9ce2f37a018d312dc7c9b2a", "last_modified": "2025-07-23T11:38:32.566561"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\api_keys_vault\\vault\\rotation\\rotation_scheduler.py", "size": 1961, "checksum": "c838d5de59853eb5bd18895b267556d5e112bc03b877d668a25bf4e32c9dc5c5", "last_modified": "2025-07-23T11:38:32.571955"}], "total_size": 2105, "checksum": "a4471e15389f1f4e20de4e146395bb3035071965ff87d375def2175318f96e12"}}]}, "backup_schedule": {"daily": true, "weekly": true, "monthly": true, "retention_days": 90}, "total_backups": 2}