#!/usr/bin/env python3
"""
🚀 نظام أنوبيس للذكاء الاصطناعي - النقطة الرئيسية
Anubis AI System - Main Entry Point

نظام متقدم للذكاء الاصطناعي مع وكلاء محسنين
تم إصلاح جميع الأخطاء بالتعاون مع Gemini CLI
"""

import argparse
import json
import os
import sys
import traceback
from datetime import datetime
from pathlib import Path


class SystemInitializationError(Exception):
    """استثناء مخصص لأخطاء تهيئة النظام"""

    pass


class ConfigurationError(Exception):
    """استثناء مخصص لأخطاء التكوين"""

    pass


# إضافة مجلد core إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))

try:
    from core.assistant_system import UniversalAssistantSystem
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"❌ خطأ في تحميل النواة الأساسية: {e}")
    print("🔧 تأكد من وجود ملفات النواة في مجلد core/")
    sys.exit(1)


def print_banner():
    """عرض شعار النظام"""
    banner = """
🏺 ═══════════════════════════════════════════════════════════════
   نظام أنوبيس للذكاء الاصطناعي
   Anubis AI Assistant System v2.0

   🎯 نظام متطور للوكلاء الذكيين مع تكامل Gemini CLI
   🚀 تم تطويره وإصلاحه بالتعاون مع الذكاء الاصطناعي
═══════════════════════════════════════════════════════════════ 🏺
"""
    print(banner)


def parse_arguments():
    """تحليل المعاملات من سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description="نظام أنوبيس للذكاء الاصطناعي",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python main.py --project "path/to/project"                    # تحليل شامل
  python main.py --agent analyzer --project "path/to/project"  # وكيل محدد
  python main.py --organize --project "path/to/project"        # تنظيم الملفات
  python main.py --health-check --project "path/to/project"    # فحص الصحة
        """,
    )

    # المعاملات الأساسية
    parser.add_argument(
        "--project", "-p", type=str, required=True, help="مسار المشروع المراد تحليله"
    )

    parser.add_argument(
        "--config",
        "-c",
        type=str,
        default="configs/default_config.json",
        help="ملف التكوين (افتراضي: configs/default_config.json)",
    )

    # أنواع العمليات
    parser.add_argument(
        "--agent",
        "-a",
        type=str,
        choices=["error", "analyzer", "organizer", "memory", "all"],
        default="all",
        help="الوكيل المراد تشغيله (افتراضي: all)",
    )

    parser.add_argument("--analyze", action="store_true", help="تحليل شامل للمشروع")

    parser.add_argument("--organize", action="store_true", help="تنظيم ملفات المشروع")

    parser.add_argument("--health-check", action="store_true", help="فحص صحة المشروع")

    parser.add_argument("--fix-issues", action="store_true", help="إصلاح المشاكل المكتشفة")

    # خيارات إضافية
    parser.add_argument("--verbose", "-v", action="store_true", help="عرض تفاصيل أكثر")

    parser.add_argument(
        "--output",
        "-o",
        type=str,
        default="reports",
        help="مجلد حفظ التقارير (افتراضي: reports)",
    )

    return parser.parse_args()


def validate_project_path(project_path):
    """التحقق من صحة مسار المشروع"""
    path = Path(project_path)

    if not path.exists():
        raise FileNotFoundError(f"المسار غير موجود: {project_path}")

    if not path.is_dir():
        raise NotADirectoryError(f"المسار ليس مجلد: {project_path}")

    return True


def setup_workspace(output_dir):
    """إعداد مساحة العمل"""
    try:
        reports_dir = Path(output_dir)
        reports_dir.mkdir(parents=True, exist_ok=True)
        return reports_dir
    except OSError as e:
        raise SystemInitializationError(f"فشل في إنشاء مجلد التقارير: {e}")


def initialize_system(args):
    """تهيئة النظام"""
    try:
        print("🔧 تهيئة نظام أنوبيس...")

        # تحميل التكوين
        config_manager = ConfigManager(args.config)
        config = config_manager.load_config()

        # تهيئة النظام
        system = UniversalAssistantSystem(
            project_path=args.project, config=config, verbose=args.verbose
        )

        print(f"✅ تم تحميل المشروع: {args.project}")
        print(f"📊 نوع المشروع: {system.project_type}")
        print(f"🤖 الوكلاء المفعلين: {len(system.active_agents)}")

        return system

    except (FileNotFoundError, json.JSONDecodeError) as e:
        raise ConfigurationError(f"خطأ في التكوين: {e}")
    except Exception as e:
        raise SystemInitializationError(f"خطأ في تهيئة النظام: {e}")


def run_operations(system, args):
    """تشغيل العمليات المطلوبة"""
    results = {}

    try:
        if args.analyze or args.agent == "all":
            print("\n🔍 بدء التحليل الشامل...")
            results["analysis"] = system.analyze_project()
            print("✅ تم التحليل الشامل")

        if args.organize:
            print("\n📁 بدء تنظيم الملفات...")
            results["organization"] = system.organize_files()
            print("✅ تم تنظيم الملفات")

        if args.health_check:
            print("\n🏥 بدء فحص الصحة...")
            results["health"] = system.health_check()
            print("✅ تم فحص الصحة")

        if args.fix_issues:
            print("\n🔧 بدء إصلاح المشاكل...")
            results["fixes"] = system.fix_issues()
            print("✅ تم إصلاح المشاكل")

        # تشغيل وكيل محدد
        if args.agent != "all" and not any(
            [args.analyze, args.organize, args.health_check, args.fix_issues]
        ):
            print(f"\n🤖 تشغيل الوكيل: {args.agent}")
            results[args.agent] = system.run_agent(args.agent)
            print(f"✅ تم تشغيل الوكيل: {args.agent}")

        return results

    except Exception as e:
        raise RuntimeError(f"خطأ أثناء تشغيل العمليات: {e}")


def save_report(results, reports_dir):
    """حفظ التقرير"""
    if not results:
        return None

    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"anubis_report_{timestamp}.json"

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📋 تم حفظ التقرير: {report_file}")
        return report_file

    except (OSError, json.JSONEncodeError) as e:
        print(f"⚠️ خطأ في حفظ التقرير: {e}")
        return None


def print_summary(results):
    """عرض ملخص العمليات"""
    if not results:
        print("\n📝 لم يتم تنفيذ أي عمليات")
        return

    print("\n" + "=" * 60)
    print("🎯 ملخص العمليات:")
    for operation, result in results.items():
        if isinstance(result, dict):
            status = "✅ نجح" if result.get("success", True) else "❌ فشل"
        else:
            status = "✅ نجح"
        print(f"   {operation}: {status}")

    print("\n🏺 تم إنجاز جميع العمليات بنجاح!")


def main():
    """الدالة الرئيسية"""
    print_banner()

    try:
        # تحليل المعاملات
        args = parse_arguments()

        # التحقق من مسار المشروع
        validate_project_path(args.project)

        # إعداد مساحة العمل
        reports_dir = setup_workspace(args.output)

        # تهيئة النظام
        system = initialize_system(args)

        # تنفيذ العمليات
        results = run_operations(system, args)

        # حفظ التقرير
        save_report(results, reports_dir)

        # عرض الملخص
        print_summary(results)

    except (FileNotFoundError, NotADirectoryError) as e:
        print(f"❌ خطأ في المسار: {e}")
        sys.exit(1)

    except ConfigurationError as e:
        print(f"❌ خطأ في التكوين: {e}")
        sys.exit(1)

    except SystemInitializationError as e:
        print(f"❌ خطأ في التهيئة: {e}")
        sys.exit(1)

    except RuntimeError as e:
        print(f"❌ خطأ في التنفيذ: {e}")
        sys.exit(1)

    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        if args.verbose:
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
