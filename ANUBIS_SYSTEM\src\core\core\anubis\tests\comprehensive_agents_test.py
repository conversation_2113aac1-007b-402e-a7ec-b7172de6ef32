#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار شامل للوكلاء المحسنة
Comprehensive Enhanced Agents Test

اختبار كامل لجميع الوكلاء المحسنة الأربعة
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# إضافة مسارات الوكلاء
sys.path.append("agents")
sys.path.append("core")

try:
    from enhanced_error_detector import EnhancedErrorDetectorAgent
    from enhanced_file_organizer import EnhancedFileOrganizerAgent
    from enhanced_memory_agent import EnhancedMemoryAgent
    from enhanced_project_analyzer import EnhancedProjectAnalyzerAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد الوكلاء: {e}")
    sys.exit(1)


class ComprehensiveAgentsTest:
    """🧪 اختبار شامل للوكلاء المحسنة"""

    def __init__(self):
        self.base_path = Path(__file__).parent.absolute()
        self.test_results = {}
        self.agents = {}

        print("🧪 اختبار شامل للوكلاء المحسنة - نظام أنوبيس")
        print("=" * 60)

    def initialize_agents(self):
        """تهيئة جميع الوكلاء"""
        print("🚀 تهيئة الوكلاء المحسنة...")

        agents_config = {"enhanced_mode": True, "ai_enabled": True, "verbose": True}

        try:
            # تهيئة الوكلاء
            self.agents = {
                "error_detector": EnhancedErrorDetectorAgent(
                    str(self.base_path), agents_config, True
                ),
                "project_analyzer": EnhancedProjectAnalyzerAgent(
                    str(self.base_path), agents_config, True
                ),
                "file_organizer": EnhancedFileOrganizerAgent(
                    str(self.base_path), agents_config, True
                ),
                "memory_agent": EnhancedMemoryAgent(str(self.base_path), agents_config, True),
            }

            print("✅ تم تهيئة جميع الوكلاء بنجاح")
            return True

        except Exception as e:
            print(f"❌ فشل في تهيئة الوكلاء: {e}")
            return False

    def test_error_detector(self):
        """اختبار وكيل كشف الأخطاء"""
        print("\n🔍 اختبار وكيل كشف الأخطاء المحسن...")

        try:
            agent = self.agents["error_detector"]

            # إنشاء ملف اختبار
            test_file = self.base_path / "test_error_sample.py"
            test_code = """
import os
password = "123456"  # كلمة مرور مكشوفة
eval("print('test')")  # استخدام eval خطر

def test_function():
    for i in range(len(items)):  # استخدام range(len())
        print(items[i])
"""

            with open(test_file, "w", encoding="utf-8") as f:
                f.write(test_code)

            # فحص الملف
            result = agent.scan_single_file(str(test_file))

            # تنظيف
            test_file.unlink()

            if "error" not in result:
                total_issues = result["summary"]["total_issues"]
                print(f"   ✅ تم فحص الملف - المشاكل المكتشفة: {total_issues}")

                self.test_results["error_detector"] = {
                    "status": "success",
                    "issues_found": total_issues,
                    "categories": result["summary"]["categories"],
                }
                return True
            else:
                print(f"   ❌ فشل الفحص: {result['error']}")
                self.test_results["error_detector"] = {
                    "status": "failed",
                    "error": result["error"],
                }
                return False

        except Exception as e:
            print(f"   ❌ خطأ في اختبار كاشف الأخطاء: {e}")
            self.test_results["error_detector"] = {"status": "error", "error": str(e)}
            return False

    def test_project_analyzer(self):
        """اختبار وكيل تحليل المشاريع"""
        print("\n📊 اختبار وكيل تحليل المشاريع المحسن...")

        try:
            agent = self.agents["project_analyzer"]

            # تحليل المشروع الحالي
            result = agent.analyze_project()

            if "error" not in result:
                project_type = result["project_type"]
                files_count = result["files_count"]

                print(f"   ✅ تم تحليل المشروع")
                print(f"   📂 نوع المشروع: {project_type}")
                print(f"   📄 عدد الملفات: {files_count}")
                print(f"   🏆 نقاط الجودة: {result['quality_score']}")

                self.test_results["project_analyzer"] = {
                    "status": "success",
                    "project_type": project_type,
                    "files_count": files_count,
                    "quality_score": result["quality_score"],
                }
                return True
            else:
                print(f"   ❌ فشل التحليل: {result['error']}")
                self.test_results["project_analyzer"] = {
                    "status": "failed",
                    "error": result["error"],
                }
                return False

        except Exception as e:
            print(f"   ❌ خطأ في اختبار محلل المشاريع: {e}")
            self.test_results["project_analyzer"] = {"status": "error", "error": str(e)}
            return False

    def test_file_organizer(self):
        """اختبار وكيل تنظيم الملفات"""
        print("\n📁 اختبار وكيل تنظيم الملفات المحسن...")

        try:
            agent = self.agents["file_organizer"]

            # إنشاء مجلد اختبار
            test_dir = self.base_path / "test_organize"
            test_dir.mkdir(exist_ok=True)

            # إنشاء ملفات اختبار
            test_files = ["test.jpg", "document.pdf", "script.py", "config.json"]

            for file_name in test_files:
                (test_dir / file_name).touch()

            # تنظيم الملفات
            result = agent.organize_files(str(test_dir))

            if "error" not in result:
                organized_count = result["organized_files"]
                categories = result["categories"]

                print(f"   ✅ تم تنظيم الملفات")
                print(f"   📦 الملفات المنظمة: {organized_count}")
                print(f"   📂 الفئات: {list(categories.keys())}")

                self.test_results["file_organizer"] = {
                    "status": "success",
                    "organized_files": organized_count,
                    "categories": categories,
                }

                # تنظيف
                import shutil

                shutil.rmtree(test_dir)

                return True
            else:
                print(f"   ❌ فشل التنظيم: {result['error']}")
                self.test_results["file_organizer"] = {
                    "status": "failed",
                    "error": result["error"],
                }
                return False

        except Exception as e:
            print(f"   ❌ خطأ في اختبار منظم الملفات: {e}")
            self.test_results["file_organizer"] = {"status": "error", "error": str(e)}
            return False

    def test_memory_agent(self):
        """اختبار وكيل الذاكرة"""
        print("\n🧠 اختبار وكيل الذاكرة المحسن...")

        try:
            agent = self.agents["memory_agent"]

            # تخزين ذكرى اختبار
            test_data = {
                "project_name": "نظام أنوبيس",
                "version": "2.0",
                "agents_count": 4,
            }

            store_result = agent.store_memory("test_project", test_data, "projects")

            if store_result["status"] == "stored":
                print(f"   ✅ تم تخزين الذكرى")

                # استرجاع الذكرى
                retrieved = agent.retrieve_memory("test_project")

                if retrieved:
                    print(f"   ✅ تم استرجاع الذكرى")
                    print(f"   📝 البيانات: {retrieved['data']['project_name']}")

                    # البحث في الذاكرة
                    search_results = agent.search_memory("أنوبيس")

                    print(f"   🔍 نتائج البحث: {len(search_results)}")

                    # إحصائيات الذاكرة
                    stats = agent.get_memory_stats()

                    print(f"   📊 إجمالي الذكريات: {stats['total_memories']}")

                    self.test_results["memory_agent"] = {
                        "status": "success",
                        "stored": True,
                        "retrieved": True,
                        "search_results": len(search_results),
                        "total_memories": stats["total_memories"],
                    }

                    return True
                else:
                    print(f"   ❌ فشل في استرجاع الذكرى")
                    self.test_results["memory_agent"] = {
                        "status": "failed",
                        "error": "فشل الاسترجاع",
                    }
                    return False
            else:
                print(f"   ❌ فشل في تخزين الذكرى")
                self.test_results["memory_agent"] = {
                    "status": "failed",
                    "error": "فشل التخزين",
                }
                return False

        except Exception as e:
            print(f"   ❌ خطأ في اختبار وكيل الذاكرة: {e}")
            self.test_results["memory_agent"] = {"status": "error", "error": str(e)}
            return False

    def test_project_creation(self):
        """اختبار إنشاء مشروع جديد"""
        print("\n🏗️ اختبار إنشاء مشروع جديد...")

        try:
            organizer = self.agents["file_organizer"]

            # إنشاء مشروع React
            result = organizer.create_project_structure("react", "test_react_app")

            if "error" not in result:
                print(f"   ✅ تم إنشاء مشروع React")
                print(f"   📂 المجلدات المنشأة: {len(result['created_folders'])}")
                print(f"   📄 الملفات المنشأة: {len(result['created_files'])}")

                # تنظيف
                import shutil

                project_path = Path(result["project_path"])
                if project_path.exists():
                    shutil.rmtree(project_path)

                return True
            else:
                print(f"   ❌ فشل في إنشاء المشروع: {result['error']}")
                return False

        except Exception as e:
            print(f"   ❌ خطأ في اختبار إنشاء المشروع: {e}")
            return False

    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        if not self.initialize_agents():
            return False

        tests = [
            ("error_detector", self.test_error_detector),
            ("project_analyzer", self.test_project_analyzer),
            ("file_organizer", self.test_file_organizer),
            ("memory_agent", self.test_memory_agent),
        ]

        passed_tests = 0

        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"❌ خطأ في اختبار {test_name}: {e}")

        # اختبار إضافي
        if self.test_project_creation():
            print("   ✅ اختبار إنشاء المشروع نجح")

        return passed_tests, len(tests)

    def generate_test_report(self):
        """إنتاج تقرير الاختبار"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "comprehensive_agents_test",
            "agents_tested": list(self.test_results.keys()),
            "results": self.test_results,
            "summary": {
                "total_agents": len(self.test_results),
                "successful_agents": len(
                    [r for r in self.test_results.values() if r["status"] == "success"]
                ),
                "failed_agents": len(
                    [r for r in self.test_results.values() if r["status"] != "success"]
                ),
            },
        }

        report_file = (
            self.base_path
            / f"comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"\n📄 تم حفظ تقرير الاختبار: {report_file}")
            return str(report_file)
        except Exception as e:
            print(f"\n❌ خطأ في حفظ التقرير: {e}")
            return None


def main():
    """الدالة الرئيسية"""
    tester = ComprehensiveAgentsTest()

    # تشغيل جميع الاختبارات
    passed, total = tester.run_all_tests()

    # عرض النتائج
    print(f"\n🏆 نتائج الاختبار الشامل:")
    print(f"   ✅ نجح: {passed}/{total} وكيل")
    print(f"   📊 معدل النجاح: {(passed/total)*100:.1f}%")

    # عرض تفاصيل النتائج
    print(f"\n📋 تفاصيل النتائج:")
    for agent_name, result in tester.test_results.items():
        status_icon = "✅" if result["status"] == "success" else "❌"
        print(f"   {status_icon} {agent_name}: {result['status']}")

    # إنتاج التقرير
    tester.generate_test_report()

    print(f"\n🎯 جميع الوكلاء المحسنة جاهزة للاستخدام!")
    print(f"🚀 نظام أنوبيس للذكاء الاصطناعي مكتمل!")

    return 0 if passed == total else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
