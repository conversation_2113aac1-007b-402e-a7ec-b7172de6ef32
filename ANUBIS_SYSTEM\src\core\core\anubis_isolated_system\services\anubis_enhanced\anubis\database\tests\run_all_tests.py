#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 تشغيل جميع اختبارات قاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Run All Database Tests

تشغيل جميع الاختبارات المتاحة وإنتاج تقرير شامل
"""

import json
import os
import subprocess
import sys
import time
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class DatabaseTestRunner:
    """🏃‍♂️ مشغل جميع اختبارات قاعدة البيانات"""

    def __init__(self):
        """تهيئة مشغل الاختبارات"""
        self.test_results = {}
        self.start_time = None

    def run_test_script(self, script_name: str, description: str) -> dict:
        """تشغيل سكريبت اختبار"""
        print(f"🔄 {description}...")

        script_path = f"database/{script_name}"
        start_time = time.time()

        try:
            # تشغيل السكريبت
            result = subprocess.run(
                [sys.executable, script_path],
                cwd=os.getcwd(),
                capture_output=True,
                text=True,
                encoding="utf-8",
            )

            duration = time.time() - start_time

            # تحليل النتيجة
            success = result.returncode == 0

            test_result = {
                "script": script_name,
                "description": description,
                "success": success,
                "return_code": result.returncode,
                "duration": duration,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "timestamp": datetime.now().isoformat(),
            }

            if success:
                print(f"✅ {description} - نجح ({duration:.2f}s)")
            else:
                print(f"❌ {description} - فشل ({duration:.2f}s)")
                if result.stderr:
                    print(f"   🔍 خطأ: {result.stderr[:200]}...")

            return test_result

        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {description} - خطأ في التشغيل: {e}")

            return {
                "script": script_name,
                "description": description,
                "success": False,
                "error": str(e),
                "duration": duration,
                "timestamp": datetime.now().isoformat(),
            }

    def run_all_tests(self) -> dict:
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء تشغيل جميع اختبارات قاعدة بيانات نظام أنوبيس")
        print("=" * 60)

        self.start_time = time.time()

        # قائمة الاختبارات
        tests = [
            ("test_connection.py", "اختبار الاتصال الأساسي"),
            ("simple_validation.py", "التحقق المبسط"),
            ("comprehensive_test.py", "الاختبار الشامل"),
            ("stress_test.py", "اختبار الضغط"),
            ("mysql_manager.py", "اختبار مدير قاعدة البيانات"),
        ]

        # تشغيل كل اختبار
        for script_name, description in tests:
            script_path = f"database/{script_name}"

            # التحقق من وجود الملف
            if os.path.exists(script_path):
                result = self.run_test_script(script_name, description)
                self.test_results[script_name] = result
            else:
                print(f"⚠️ {description} - الملف غير موجود: {script_path}")
                self.test_results[script_name] = {
                    "script": script_name,
                    "description": description,
                    "success": False,
                    "error": "File not found",
                    "timestamp": datetime.now().isoformat(),
                }

        total_duration = time.time() - self.start_time

        # تحليل النتائج الإجمالية
        total_tests = len(self.test_results)
        successful_tests = sum(
            1 for result in self.test_results.values() if result.get("success", False)
        )
        failed_tests = total_tests - successful_tests
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0

        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص جميع الاختبارات")
        print("=" * 60)

        if success_rate == 100:
            status_icon = "🎉"
            status_text = "جميع الاختبارات نجحت! قاعدة البيانات جاهزة تماماً"
        elif success_rate >= 80:
            status_icon = "⚠️"
            status_text = "معظم الاختبارات نجحت مع بعض المشاكل البسيطة"
        elif success_rate >= 50:
            status_icon = "🔧"
            status_text = "نصف الاختبارات نجح - تحتاج إلى إصلاحات"
        else:
            status_icon = "❌"
            status_text = "معظم الاختبارات فشلت - تحتاج إلى مراجعة شاملة"

        print(
            f"{status_icon} النتيجة الإجمالية: {successful_tests}/{total_tests} اختبار نجح ({success_rate:.1f}%)"
        )
        print(f"⏱️ إجمالي وقت الاختبار: {total_duration:.2f} ثانية")
        print(f"📋 الحالة: {status_text}")

        # عرض تفاصيل الاختبارات
        print(f"\n📝 تفاصيل الاختبارات:")
        for script_name, result in self.test_results.items():
            success = result.get("success", False)
            duration = result.get("duration", 0)
            icon = "✅" if success else "❌"
            print(f"  {icon} {result.get('description', script_name)} ({duration:.2f}s)")

            if not success and "error" in result:
                print(f"      🔍 {result['error']}")

        # إنشاء التقرير النهائي
        final_report = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "success_rate": success_rate,
            "total_duration": total_duration,
            "status": status_text,
            "database_ready": success_rate >= 80,
            "test_results": self.test_results,
            "summary": {
                "connection_test": self.test_results.get("test_connection.py", {}).get(
                    "success", False
                ),
                "validation_test": self.test_results.get("simple_validation.py", {}).get(
                    "success", False
                ),
                "comprehensive_test": self.test_results.get("comprehensive_test.py", {}).get(
                    "success", False
                ),
                "stress_test": self.test_results.get("stress_test.py", {}).get("success", False),
                "manager_test": self.test_results.get("mysql_manager.py", {}).get("success", False),
            },
        }

        return final_report

    def generate_html_report(self, report: dict, output_file: str):
        """إنتاج تقرير HTML"""
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير اختبارات قاعدة بيانات نظام أنوبيس</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .status-success {{ color: #28a745; }}
        .status-warning {{ color: #ffc107; }}
        .status-error {{ color: #dc3545; }}
        .test-result {{ margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .test-success {{ background-color: #d4edda; border-left: 4px solid #28a745; }}
        .test-failure {{ background-color: #f8d7da; border-left: 4px solid #dc3545; }}
        .summary {{ background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px 20px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; }}
        .metric-label {{ font-size: 0.9em; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏺 تقرير اختبارات قاعدة بيانات نظام أنوبيس</h1>
            <p>تم إنتاج التقرير في: {report['timestamp']}</p>
        </div>

        <div class="summary">
            <h2>📊 الملخص الإجمالي</h2>
            <div class="metric">
                <div class="metric-value status-{'success' if report['success_rate'] == 100 else 'warning' if report['success_rate'] >= 80 else 'error'}">{report['success_rate']:.1f}%</div>
                <div class="metric-label">معدل النجاح</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report['successful_tests']}/{report['total_tests']}</div>
                <div class="metric-label">الاختبارات الناجحة</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report['total_duration']:.2f}s</div>
                <div class="metric-label">إجمالي الوقت</div>
            </div>
            <p><strong>الحالة:</strong> {report['status']}</p>
        </div>

        <h2>📝 تفاصيل الاختبارات</h2>
"""

        for script_name, result in report["test_results"].items():
            success = result.get("success", False)
            css_class = "test-success" if success else "test-failure"
            icon = "✅" if success else "❌"
            duration = result.get("duration", 0)

            html_content += f"""
        <div class="test-result {css_class}">
            <h3>{icon} {result.get('description', script_name)}</h3>
            <p><strong>المدة:</strong> {duration:.2f} ثانية</p>
            <p><strong>الحالة:</strong> {'نجح' if success else 'فشل'}</p>
"""

            if not success and "error" in result:
                html_content += f"<p><strong>الخطأ:</strong> {result['error']}</p>"

            html_content += "</div>"

        html_content += """
    </div>
</body>
</html>
"""

        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"📄 تم إنتاج تقرير HTML: {output_file}")
        except Exception as e:
            print(f"❌ خطأ في إنتاج تقرير HTML: {e}")


def main():
    """الدالة الرئيسية"""
    runner = DatabaseTestRunner()
    report = runner.run_all_tests()

    # حفظ التقرير JSON
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_report_file = f"database/all_tests_report_{timestamp}.json"
    html_report_file = f"database/all_tests_report_{timestamp}.html"

    os.makedirs(os.path.dirname(json_report_file), exist_ok=True)

    try:
        with open(json_report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n📄 تم حفظ التقرير JSON: {json_report_file}")
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير JSON: {e}")

    # إنتاج تقرير HTML
    runner.generate_html_report(report, html_report_file)

    # تحديد كود الخروج
    exit_code = 0 if report.get("database_ready", False) else 1

    print(
        f"\n🎯 النتيجة النهائية: {'قاعدة البيانات جاهزة!' if exit_code == 0 else 'قاعدة البيانات تحتاج إلى مراجعة'}"
    )

    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
