#!/usr/bin/env python3
"""
🧪 إطار اختبارات الأدوات المتقدم
Advanced Tools Testing Framework
"""

import unittest
import sys
import json
from pathlib import Path
from datetime import datetime

class ToolsTestFramework:
    def __init__(self):
        self.test_results = []
        self.start_time = None
        
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء تشغيل اختبارات الأدوات...")
        self.start_time = datetime.now()
        
        # اختبار أدوات VSCode
        self.test_vscode_tools()
        
        # اختبار أدوات المراقبة
        self.test_monitoring_tools()
        
        # اختبار أدوات الأمان
        self.test_security_tools()
        
        # اختبار أدوات الطوارئ
        self.test_emergency_tools()
        
        # إنشاء التقرير
        self.generate_test_report()
        
        return self.test_results
    
    def test_vscode_tools(self):
        """اختبار أدوات VSCode"""
        print("🔧 اختبار أدوات VSCode...")
        
        vscode_path = Path("src/tools/vscode-optimizer")
        
        # اختبار وجود المشاريع
        projects = [
            "VS-Code-Performance-Optimizer",
            "VSCode-Control-Center"
        ]
        
        for project in projects:
            project_path = vscode_path / project
            if project_path.exists():
                self.add_test_result(f"vscode_{project}", "pass", 
                                   f"مشروع {project} موجود")
                
                # اختبار وجود الملفات المهمة
                important_files = ["README.md", "requirements.txt"]
                for file in important_files:
                    if (project_path / file).exists():
                        self.add_test_result(f"vscode_{project}_{file}", "pass",
                                           f"ملف {file} موجود في {project}")
                    else:
                        self.add_test_result(f"vscode_{project}_{file}", "warning",
                                           f"ملف {file} مفقود في {project}")
            else:
                self.add_test_result(f"vscode_{project}", "fail",
                                   f"مشروع {project} مفقود")
    
    def test_monitoring_tools(self):
        """اختبار أدوات المراقبة"""
        print("📊 اختبار أدوات المراقبة...")
        
        monitoring_path = Path("src/monitoring")
        
        if monitoring_path.exists():
            self.add_test_result("monitoring_dir", "pass", "مجلد المراقبة موجود")
            
            if (monitoring_path / "system_monitor.py").exists():
                self.add_test_result("system_monitor", "pass", "مراقب النظام موجود")
                
                # اختبار إمكانية الاستيراد
                try:
                    sys.path.insert(0, str(monitoring_path))
                    import system_monitor
                    self.add_test_result("system_monitor_import", "pass", 
                                       "يمكن استيراد مراقب النظام")
                except Exception as e:
                    self.add_test_result("system_monitor_import", "fail",
                                       f"خطأ في استيراد مراقب النظام: {e}")
            else:
                self.add_test_result("system_monitor", "fail", "مراقب النظام مفقود")
        else:
            self.add_test_result("monitoring_dir", "fail", "مجلد المراقبة مفقود")
    
    def test_security_tools(self):
        """اختبار أدوات الأمان"""
        print("🛡️ اختبار أدوات الأمان...")
        
        security_path = Path("src/security")
        
        if security_path.exists():
            self.add_test_result("security_dir", "pass", "مجلد الأمان موجود")
            
            if (security_path / "security_monitor.py").exists():
                self.add_test_result("security_monitor", "pass", "مراقب الأمان موجود")
            else:
                self.add_test_result("security_monitor", "fail", "مراقب الأمان مفقود")
        else:
            self.add_test_result("security_dir", "fail", "مجلد الأمان مفقود")
    
    def test_emergency_tools(self):
        """اختبار أدوات الطوارئ"""
        print("🚨 اختبار أدوات الطوارئ...")
        
        emergency_path = Path("src/tools/emergency")
        
        if emergency_path.exists():
            self.add_test_result("emergency_dir", "pass", "مجلد الطوارئ موجود")
            
            emergency_files = list(emergency_path.glob("*.bat"))
            if emergency_files:
                self.add_test_result("emergency_tools", "pass", 
                                   f"أدوات الطوارئ موجودة ({len(emergency_files)} أداة)")
            else:
                self.add_test_result("emergency_tools", "warning", 
                                   "لا توجد أدوات طوارئ")
        else:
            self.add_test_result("emergency_dir", "fail", "مجلد الطوارئ مفقود")
    
    def add_test_result(self, test_name, status, message):
        """إضافة نتيجة اختبار"""
        self.test_results.append({
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def generate_test_report(self):
        """إنشاء تقرير الاختبارات"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        passed = len([r for r in self.test_results if r["status"] == "pass"])
        failed = len([r for r in self.test_results if r["status"] == "fail"])
        warnings = len([r for r in self.test_results if r["status"] == "warning"])
        total = len(self.test_results)
        
        report = {
            "test_session": {
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration
            },
            "summary": {
                "total_tests": total,
                "passed": passed,
                "failed": failed,
                "warnings": warnings,
                "success_rate": (passed / total * 100) if total > 0 else 0
            },
            "results": self.test_results
        }
        
        report_file = Path("test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 تقرير الاختبارات:")
        print(f"   ✅ نجح: {passed}")
        print(f"   ❌ فشل: {failed}")
        print(f"   ⚠️ تحذيرات: {warnings}")
        print(f"   📈 معدل النجاح: {report['summary']['success_rate']:.1f}%")
        print(f"   💾 التقرير: {report_file}")

if __name__ == "__main__":
    framework = ToolsTestFramework()
    framework.run_all_tests()
