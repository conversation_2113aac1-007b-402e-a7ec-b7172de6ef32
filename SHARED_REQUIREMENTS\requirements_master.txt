# 📦 ملف المتطلبات الرئيسي المشترك
# Shared Master Requirements File
# تم إنشاؤه في: 2025-07-23T02:57:26.829828
# إجمالي المكتبات: 82
# يحتوي على جميع المكتبات المطلوبة لجميع المشاريع

aiofiles==24.1.0
aiosqlite==>=0.19.0
annotated-types==0.7.0
anthropic==>=0.25.0
anyio==4.9.0
bcrypt==>=4.0.0
black==>=22.0.0
certifi==2025.7.14
chardet==>=5.0.0
charset-normalizer==3.4.2
chromadb==>=0.4.0
click==8.2.1
colorama==0.4.6
colorlog==>=6.7.0
cryptography==>=41.0.0
docker==>=6.0.0
faiss-cpu==>=1.7.0
fastapi==0.116.1
flake8==>=5.0.0
google-generativeai==>=0.5.0
greenlet==3.2.3
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
jinja2==3.1.6
jupyter==>=1.0.0
jupyterlab==>=4.0.0
langchain==>=0.0.200
markupsafe==3.0.2
matplotlib==>=3.7.0
mysql-connector-python==9.3.0
numpy==>=1.24.0
openai==>=1.0.0
packaging==25.0
pandas==>=2.0.0
passlib==>=1.7.4
passlib[bcrypt]==1.7.4
pathlib2==>=2.3.7
pathspec==>=0.10.0
pip==25.1.1
plotly==>=5.15.0
pluggy==1.6.0
prometheus-client==>=0.16.0
psutil==7.0.0
psycopg2-binary==>=2.9.0
pydantic==2.11.7
pydantic_core==2.33.2
pygments==2.19.2
pylint==>=2.17.0
pytest==8.4.1
pytest-cov==>=4.0.0
python-dateutil==>=2.8.0
python-dotenv==1.1.1
python-jose==>=3.3.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.20
pywin32==311
pyyaml==6.0.2
redis==>=4.5.0
requests==2.32.4
rich==>=13.0.0
scikit-learn==>=1.3.0
scipy==>=1.10.0
seaborn==>=0.12.0
sentence-transformers==>=2.2.0
sniffio==1.3.1
sqlalchemy==2.0.41
starlette==0.47.1
streamlit==>=1.28.0
tabulate==>=0.9.0
tiktoken==>=0.5.0
torch==>=2.0.0
tqdm==>=4.64.0
transformers==>=4.30.0
typing-extensions==>=4.0.0
typing-inspection==0.4.1
typing_extensions==4.14.1
urllib3==2.5.0
uvicorn==0.35.0
uvicorn[standard]==0.24.0
