{"isolation_system": "Anubis Comprehensive Isolation", "created_at": "2025-07-19T07:43:30.020184", "architecture": "Docker-based Microservices", "components": 6, "isolation_features": ["Resource isolation (CPU, Memory)", "Network isolation (Bridge network)", "Data isolation (Separate volumes)", "Security isolation (Non-root users)", "Monitoring isolation (Dedicated services)"], "services": ["anubis_enhanced", "universal_ai", "ollama_service", "database_mysql", "api_gateway", "monitoring"], "networks": ["anubis_network"], "volumes": ["database_data", "ollama_models", "logs", "monitoring_data"], "ports": {"anubis_enhanced": 8000, "universal_ai": 8001, "ollama": 11434, "mysql": 3306, "nginx": 80, "prometheus": 9090, "grafana": 3000}, "security_measures": ["Non-root container users", "Resource limits", "Network segmentation", "Read-only configurations", "Encrypted database connections"]}