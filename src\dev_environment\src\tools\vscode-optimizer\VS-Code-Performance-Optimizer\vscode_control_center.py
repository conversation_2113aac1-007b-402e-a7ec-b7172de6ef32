# -*- coding: utf-8 -*-
"""
🎯 VS Code Control Center - الواجهة الموحدة المثلى
=================================================

واجهة واحدة تجمع أفضل ما في جميع الواجهات:
✨ تصميم جميل + وكلاء ذكيين + أداء سريع
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
import psutil
from datetime import datetime
import sys
import os

# إضافة مسار الوكلاء
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

try:
    from agents.agent_coordinator import AgentCoordinator
    AGENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: سيعمل التطبيق بوضع أساسي - {e}")
    AGENTS_AVAILABLE = False

class VSCodeControlCenter:
    """الواجهة الموحدة المثلى لـ VS Code Control Center"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # نظام الوكلاء
        self.agent_coordinator = None
        self.agents_running = False
        self.auto_analysis_enabled = False
        
        # متغيرات الواجهة
        self.status_vars = {}
        self.analysis_results = {}
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تهيئة نظام الوكلاء إذا كان متاحاً
        if AGENTS_AVAILABLE:
            self.initialize_agents()
        
        # بدء التحديث التلقائي
        self.start_auto_update()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎯 VS Code Control Center - الواجهة المثلى")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0d1117')
        self.root.resizable(True, True)
        
        # محاولة تعيين أيقونة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة الموحدة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#0d1117')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # العنوان الرئيسي
        self.create_header(main_frame)
        
        # الجزء العلوي - الإحصائيات والتحكم
        top_frame = tk.Frame(main_frame, bg='#0d1117')
        top_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.create_stats_section(top_frame)
        self.create_control_section(top_frame)
        
        # الجزء السفلي - التحليل والمحادثة
        bottom_frame = tk.Frame(main_frame, bg='#0d1117')
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        self.create_analysis_section(bottom_frame)
        
        if AGENTS_AVAILABLE:
            self.create_ai_chat_section(bottom_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان الرئيسي"""
        header_frame = tk.Frame(parent, bg='#0d1117')
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان
        title_label = tk.Label(
            header_frame,
            text="🎯 VS Code Control Center",
            font=('Segoe UI', 28, 'bold'),
            fg='#58a6ff',
            bg='#0d1117'
        )
        title_label.pack()
        
        # العنوان الفرعي
        subtitle_text = "الواجهة المثلى - مراقبة ذكية وتحسين متقدم لـ VS Code"
        if AGENTS_AVAILABLE:
            subtitle_text += " مع وكلاء ذكيين"
        
        subtitle_label = tk.Label(
            header_frame,
            text=subtitle_text,
            font=('Segoe UI', 12),
            fg='#8b949e',
            bg='#0d1117'
        )
        subtitle_label.pack()
        
        # شريط الحالة العامة
        status_frame = tk.Frame(header_frame, bg='#0d1117')
        status_frame.pack(pady=(10, 0))
        
        self.overall_status_label = tk.Label(
            status_frame,
            text="🔄 جاري التحليل...",
            font=('Segoe UI', 14, 'bold'),
            fg='#ffa657',
            bg='#0d1117'
        )
        self.overall_status_label.pack()
    
    def create_stats_section(self, parent):
        """إنشاء قسم الإحصائيات"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 إحصائيات النظام الحية",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        stats_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # شبكة البطاقات
        cards_frame = tk.Frame(stats_frame, bg='#161b22')
        cards_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # تكوين الشبكة
        for i in range(2):
            cards_frame.grid_rowconfigure(i, weight=1)
            cards_frame.grid_columnconfigure(i, weight=1)
        
        # البطاقات
        self.create_stat_card(cards_frame, "🖥️ العمليات", "processes", 0, 0)
        self.create_stat_card(cards_frame, "💾 الذاكرة", "memory", 0, 1)
        self.create_stat_card(cards_frame, "⚡ المعالج", "cpu", 1, 0)
        self.create_stat_card(cards_frame, "🧩 VS Code", "vscode", 1, 1)
    
    def create_stat_card(self, parent, title, key, row, col):
        """إنشاء بطاقة إحصائية محسنة"""
        card_frame = tk.Frame(parent, bg='#21262d', relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=8, pady=8, sticky='nsew')
        
        # العنوان
        title_label = tk.Label(
            card_frame,
            text=title,
            font=('Segoe UI', 12, 'bold'),
            fg='#8b949e',
            bg='#21262d'
        )
        title_label.pack(pady=(15, 5))
        
        # القيمة الرئيسية
        value_label = tk.Label(
            card_frame,
            text="--",
            font=('Segoe UI', 20, 'bold'),
            fg='#58a6ff',
            bg='#21262d'
        )
        value_label.pack()
        
        # الحالة
        status_label = tk.Label(
            card_frame,
            text="جاري التحميل...",
            font=('Segoe UI', 10),
            fg='#8b949e',
            bg='#21262d'
        )
        status_label.pack(pady=(5, 15))
        
        # شريط تقدم (للذاكرة والمعالج)
        if key in ['memory', 'cpu']:
            progress_frame = tk.Frame(card_frame, bg='#21262d')
            progress_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
            
            progress_bar = ttk.Progressbar(
                progress_frame,
                length=100,
                mode='determinate',
                style='Custom.Horizontal.TProgressbar'
            )
            progress_bar.pack(fill=tk.X)
            
            self.status_vars[key] = {
                'value': value_label,
                'status': status_label,
                'frame': card_frame,
                'progress': progress_bar
            }
        else:
            self.status_vars[key] = {
                'value': value_label,
                'status': status_label,
                'frame': card_frame
            }
    
    def create_control_section(self, parent):
        """إنشاء قسم التحكم"""
        control_frame = tk.LabelFrame(
            parent,
            text="🎛️ لوحة التحكم الذكية",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(control_frame, bg='#161b22')
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # الأزرار الرئيسية
        buttons = [
            ("🔄 تحديث فوري", self.manual_update, '#238636'),
            ("🧹 تنظيف النظام", self.cleanup_system, '#fb8500'),
            ("🚫 إغلاق العمليات", self.kill_processes, '#da3633'),
            ("💾 حفظ التقرير", self.save_report, '#8957e5')
        ]
        
        if AGENTS_AVAILABLE:
            buttons.insert(1, ("🤖 تحليل ذكي", self.run_ai_analysis, '#1f6feb'))
        
        for i, (text, command, color) in enumerate(buttons):
            self.create_control_button(buttons_frame, text, command, color, i)
        
        # مفتاح التحليل التلقائي
        if AGENTS_AVAILABLE:
            auto_frame = tk.Frame(control_frame, bg='#161b22')
            auto_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
            
            self.auto_analysis_var = tk.BooleanVar()
            auto_check = tk.Checkbutton(
                auto_frame,
                text="🔄 تحليل تلقائي",
                variable=self.auto_analysis_var,
                command=self.toggle_auto_analysis,
                font=('Segoe UI', 11, 'bold'),
                fg='#f0f6fc',
                bg='#161b22',
                selectcolor='#21262d',
                activebackground='#161b22',
                activeforeground='#58a6ff'
            )
            auto_check.pack()
    
    def create_control_button(self, parent, text, command, color, index):
        """إنشاء زر تحكم محسن"""
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=('Segoe UI', 11, 'bold'),
            fg='white',
            bg=color,
            activebackground=color,
            activeforeground='white',
            relief='flat',
            bd=0,
            padx=20,
            pady=12,
            cursor='hand2'
        )
        button.pack(fill=tk.X, pady=5)
        
        # تأثير hover
        def on_enter(e):
            button.configure(bg=self.lighten_color(color))
        
        def on_leave(e):
            button.configure(bg=color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
    
    def create_analysis_section(self, parent):
        """إنشاء قسم التحليل"""
        analysis_frame = tk.LabelFrame(
            parent,
            text="📊 نتائج التحليل والتوصيات" + (" الذكية" if AGENTS_AVAILABLE else ""),
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        
        if AGENTS_AVAILABLE:
            analysis_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        else:
            analysis_frame.pack(fill=tk.BOTH, expand=True)
        
        # منطقة النتائج
        self.analysis_text = scrolledtext.ScrolledText(
            analysis_frame,
            height=15,
            font=('Consolas', 11),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # شريط حالة التحليل
        status_frame = tk.Frame(analysis_frame, bg='#161b22')
        status_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        tk.Label(
            status_frame,
            text="حالة التحليل:",
            font=('Segoe UI', 11, 'bold'),
            fg='#f0f6fc',
            bg='#161b22'
        ).pack(side=tk.LEFT)
        
        self.analysis_status_label = tk.Label(
            status_frame,
            text="🔄 جاري التحليل..." if AGENTS_AVAILABLE else "📊 تحليل أساسي",
            font=('Segoe UI', 11),
            fg='#ffa657',
            bg='#161b22'
        )
        self.analysis_status_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_ai_chat_section(self, parent):
        """إنشاء قسم المحادثة مع AI"""
        chat_frame = tk.LabelFrame(
            parent,
            text="💬 اسأل الوكلاء الذكيين",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        chat_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # منطقة المحادثة
        self.ai_response_text = scrolledtext.ScrolledText(
            chat_frame,
            height=10,
            font=('Segoe UI', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.ai_response_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(15, 10))
        
        # إطار الإدخال
        input_frame = tk.Frame(chat_frame, bg='#161b22')
        input_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # حقل الإدخال
        self.ai_input = tk.Entry(
            input_frame,
            font=('Segoe UI', 12),
            bg='#21262d',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            relief='flat',
            bd=8
        )
        self.ai_input.pack(fill=tk.X, pady=(0, 10))
        self.ai_input.bind('<Return>', self.ask_ai_agents)
        
        # زر الإرسال
        send_button = tk.Button(
            input_frame,
            text="📤 اسأل الوكلاء",
            command=self.ask_ai_agents,
            font=('Segoe UI', 11, 'bold'),
            fg='white',
            bg='#238636',
            activebackground='#2ea043',
            relief='flat',
            bd=0,
            padx=25,
            pady=8,
            cursor='hand2'
        )
        send_button.pack()
        
        # رسالة ترحيبية
        self.log_to_ai("🤖 مرحباً! اسألني عن أي شيء متعلق بأداء نظامك أو VS Code")
        self.log_to_ai("💡 أمثلة: 'لماذا VS Code بطيء؟' أو 'كيف أحسن الأداء؟'")
    
    def initialize_agents(self):
        """تهيئة نظام الوكلاء"""
        try:
            self.agent_coordinator = AgentCoordinator()
            self.agent_coordinator.start_all_agents()
            self.agents_running = True
            
            self.analysis_status_label.configure(text="✅ الوكلاء نشطة", fg='#3fb950')
            self.log_to_analysis("✅ تم تهيئة نظام الوكلاء الذكيين بنجاح")
            self.log_to_analysis("🤖 6 وكلاء جاهزين للتحليل والمساعدة")
            
        except Exception as e:
            self.analysis_status_label.configure(text="❌ خطأ في الوكلاء", fg='#f85149')
            self.log_to_analysis(f"❌ خطأ في تهيئة الوكلاء: {e}")
    
    def log_to_analysis(self, message):
        """إضافة رسالة إلى منطقة التحليل"""
        self.analysis_text.configure(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.analysis_text.configure(state=tk.DISABLED)
        self.analysis_text.see(tk.END)
    
    def log_to_ai(self, message):
        """إضافة رسالة إلى منطقة AI"""
        if hasattr(self, 'ai_response_text'):
            self.ai_response_text.configure(state=tk.NORMAL)
            self.ai_response_text.insert(tk.END, f"{message}\n\n")
            self.ai_response_text.configure(state=tk.DISABLED)
            self.ai_response_text.see(tk.END)
    
    def lighten_color(self, color):
        """تفتيح لون للتأثير hover"""
        color_map = {
            '#238636': '#2ea043',
            '#1f6feb': '#388bfd',
            '#fb8500': '#fd7e14',
            '#da3633': '#f85149',
            '#8957e5': '#a475f9'
        }
        return color_map.get(color, color)
    
    def start_auto_update(self):
        """بدء التحديث التلقائي"""
        self.update_stats()
        self.root.after(3000, self.start_auto_update)  # كل 3 ثواني
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # تحديث العمليات
            process_count = len(psutil.pids())
            self.status_vars['processes']['value'].configure(text=str(process_count))
            
            # تحديث الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.status_vars['memory']['value'].configure(text=f"{memory_percent:.1f}%")
            if 'progress' in self.status_vars['memory']:
                self.status_vars['memory']['progress']['value'] = memory_percent
            
            # تحديث المعالج
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.status_vars['cpu']['value'].configure(text=f"{cpu_percent:.1f}%")
            if 'progress' in self.status_vars['cpu']:
                self.status_vars['cpu']['progress']['value'] = cpu_percent
            
            # تحديث VS Code
            vscode_count = 0
            vscode_memory = 0
            for proc in psutil.process_iter(['name', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        vscode_count += 1
                        vscode_memory += proc.info['memory_percent']
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.status_vars['vscode']['value'].configure(text=str(vscode_count))
            
            # تحديث ألوان الحالة
            self.update_status_colors(memory_percent, cpu_percent, vscode_count, vscode_memory)
            
            # تحديث الحالة العامة
            self.update_overall_status(memory_percent, cpu_percent, vscode_count)
            
        except Exception as e:
            self.log_to_analysis(f"❌ خطأ في تحديث الإحصائيات: {e}")
    
    def update_status_colors(self, memory_percent, cpu_percent, vscode_count, vscode_memory):
        """تحديث ألوان الحالة"""
        # ألوان الذاكرة
        if memory_percent > 85:
            color, status = '#f85149', "🔴 مرتفع جداً"
        elif memory_percent > 70:
            color, status = '#ffa657', "🟡 مرتفع"
        else:
            color, status = '#3fb950', "🟢 طبيعي"
        
        self.status_vars['memory']['value'].configure(fg=color)
        self.status_vars['memory']['status'].configure(text=status)
        
        # ألوان المعالج
        if cpu_percent > 80:
            color, status = '#f85149', "🔴 مرتفع جداً"
        elif cpu_percent > 60:
            color, status = '#ffa657', "🟡 مرتفع"
        else:
            color, status = '#3fb950', "🟢 طبيعي"
        
        self.status_vars['cpu']['value'].configure(fg=color)
        self.status_vars['cpu']['status'].configure(text=status)
        
        # حالة VS Code
        if vscode_count == 0:
            color, status = '#8b949e', "⚪ متوقف"
        elif vscode_count > 10 or vscode_memory > 25:
            color, status = '#f85149', "🔴 مشكلة"
        elif vscode_count > 5 or vscode_memory > 15:
            color, status = '#ffa657', "🟡 مراقبة"
        else:
            color, status = '#3fb950', "🟢 جيد"
        
        self.status_vars['vscode']['value'].configure(fg=color)
        self.status_vars['vscode']['status'].configure(text=status)
    
    def update_overall_status(self, memory_percent, cpu_percent, vscode_count):
        """تحديث الحالة العامة"""
        if memory_percent > 90 or cpu_percent > 90:
            status = "🚨 يحتاج تدخل فوري!"
            color = '#f85149'
        elif memory_percent > 75 or cpu_percent > 75:
            status = "⚠️ يحتاج مراقبة"
            color = '#ffa657'
        elif vscode_count > 10:
            status = "🔄 VS Code يحتاج إعادة تشغيل"
            color = '#ffa657'
        else:
            status = "✅ النظام يعمل بكفاءة"
            color = '#3fb950'
        
        self.overall_status_label.configure(text=status, fg=color)
    
    def manual_update(self):
        """تحديث يدوي"""
        self.log_to_analysis("🔄 تحديث يدوي للبيانات...")
        self.update_stats()
        self.log_to_analysis("✅ تم التحديث بنجاح")
    
    def run_ai_analysis(self):
        """تشغيل التحليل الذكي"""
        if not AGENTS_AVAILABLE or not self.agent_coordinator:
            messagebox.showwarning("تحذير", "نظام الوكلاء غير متوفر")
            return
        
        self.log_to_analysis("🤖 بدء التحليل الشامل بالوكلاء الذكيين...")
        self.analysis_status_label.configure(text="🔄 تحليل جاري...", fg='#ffa657')
        
        def run_analysis():
            try:
                results = self.agent_coordinator.run_comprehensive_analysis()
                self.analysis_results = results
                self.root.after(0, lambda: self.display_analysis_results(results))
            except Exception as e:
                self.root.after(0, lambda: self.log_to_analysis(f"❌ خطأ في التحليل: {e}"))
        
        threading.Thread(target=run_analysis, daemon=True).start()
    
    def display_analysis_results(self, results):
        """عرض نتائج التحليل"""
        try:
            summary = results.get('summary', {})
            overall_status = summary.get('overall_status', 'غير محدد')
            overall_score = results.get('overall_score', 0)
            
            self.analysis_status_label.configure(text=f"✅ مكتمل - {overall_score}/100", fg='#3fb950')
            self.log_to_analysis(f"📊 النتيجة النهائية: {overall_status} - النقاط: {overall_score}/100")
            
            # عرض التوصيات
            recommendations = results.get('combined_recommendations', [])
            if recommendations:
                self.log_to_analysis("💡 التوصيات الذكية:")
                for i, rec in enumerate(recommendations[:8], 1):
                    self.log_to_analysis(f"  {i}. {rec}")
            
            # عرض حالة الوكلاء
            agent_results = results.get('agent_results', {})
            successful_agents = len([r for r in agent_results.values() if 'error' not in r])
            total_agents = len(agent_results)
            self.log_to_analysis(f"🤖 حالة الوكلاء: {successful_agents}/{total_agents} نجح")
            
        except Exception as e:
            self.log_to_analysis(f"❌ خطأ في عرض النتائج: {e}")
    
    def ask_ai_agents(self, event=None):
        """سؤال الوكلاء الذكيين"""
        if not AGENTS_AVAILABLE:
            return
            
        question = self.ai_input.get().strip()
        if not question:
            return
        
        self.ai_input.delete(0, tk.END)
        self.log_to_ai(f"❓ أنت: {question}")
        self.log_to_ai("🤖 جاري البحث عن الإجابة...")
        
        def ask_agents():
            try:
                responses = self.agent_coordinator.ask_ai_agents(question)
                self.root.after(0, lambda: self.display_ai_responses(responses))
            except Exception as e:
                self.root.after(0, lambda: self.log_to_ai(f"❌ خطأ: {e}"))
        
        threading.Thread(target=ask_agents, daemon=True).start()
    
    def display_ai_responses(self, responses):
        """عرض إجابات الوكلاء"""
        for agent_name, response in responses.items():
            self.log_to_ai(f"🤖 {agent_name.upper()}:")
            self.log_to_ai(f"{response}")
    
    def toggle_auto_analysis(self):
        """تبديل التحليل التلقائي"""
        self.auto_analysis_enabled = self.auto_analysis_var.get()
        
        if self.auto_analysis_enabled:
            self.log_to_analysis("🔄 تم تفعيل التحليل التلقائي (كل 30 ثانية)")
            self.start_auto_analysis()
        else:
            self.log_to_analysis("⏹️ تم إيقاف التحليل التلقائي")
    
    def start_auto_analysis(self):
        """بدء التحليل التلقائي"""
        if self.auto_analysis_enabled and AGENTS_AVAILABLE and self.agent_coordinator:
            self.run_ai_analysis()
            self.root.after(30000, self.start_auto_analysis)
    
    def cleanup_system(self):
        """تنظيف النظام"""
        self.log_to_analysis("🧹 بدء تنظيف النظام...")
        
        try:
            # تنظيف أساسي
            import gc
            gc.collect()
            
            self.log_to_analysis("✅ تم تنظيف الذاكرة")
            self.log_to_analysis("💡 نصيحة: أعد تشغيل VS Code لتحسين أفضل")
            
        except Exception as e:
            self.log_to_analysis(f"❌ خطأ في التنظيف: {e}")
    
    def kill_processes(self):
        """إغلاق العمليات المعلقة"""
        result = messagebox.askyesno("تأكيد", "هل تريد إغلاق العمليات المعلقة؟\n⚠️ قد يؤثر على VS Code المفتوح")
        if result:
            self.log_to_analysis("🚫 البحث عن العمليات المعلقة...")
            
            try:
                killed_count = 0
                for proc in psutil.process_iter(['pid', 'name', 'status']):
                    try:
                        if proc.info['status'] == psutil.STATUS_ZOMBIE:
                            proc.kill()
                            killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                self.log_to_analysis(f"✅ تم إغلاق {killed_count} عملية معلقة")
                
            except Exception as e:
                self.log_to_analysis(f"❌ خطأ في إغلاق العمليات: {e}")
    
    def save_report(self):
        """حفظ التقرير"""
        try:
            # جمع البيانات الحالية
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'system_stats': {
                    'processes': len(psutil.pids()),
                    'memory_percent': psutil.virtual_memory().percent,
                    'cpu_percent': psutil.cpu_percent(),
                },
                'analysis_results': self.analysis_results if hasattr(self, 'analysis_results') else {}
            }
            
            filename = f"vscode_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.log_to_analysis(f"💾 تم حفظ التقرير: {filename}")
            messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}")
            
        except Exception as e:
            self.log_to_analysis(f"❌ خطأ في حفظ التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ التقرير:\n{e}")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.cleanup_and_exit()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التطبيق:\n{e}")
    
    def cleanup_and_exit(self):
        """تنظيف وإغلاق التطبيق"""
        if self.agent_coordinator:
            self.agent_coordinator.stop_all_agents()
        self.root.quit()

if __name__ == "__main__":
    app = VSCodeControlCenter()
    app.run()
