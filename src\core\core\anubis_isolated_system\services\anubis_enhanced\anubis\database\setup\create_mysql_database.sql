-- 🏺 إنشاء قاعدة بيانات نظام أنوبيس في MySQL
-- Anubis AI Assistants System MySQL Database Creation

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS anubis_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE anubis_system;

-- جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    path TEXT NOT NULL,
    type VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التحليلات
CREATE TABLE IF NOT EXISTS analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    agent_type VARCHAR(100) NOT NULL,
    analysis_data JSON,
    results JSON,
    score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_agent_type (agent_type),
    INDEX idx_score (score),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأخطاء
CREATE TABLE IF NOT EXISTS errors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    file_path TEXT,
    line_number INT DEFAULT 0,
    error_type VARCHAR(100),
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_error_type (error_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التقارير
CREATE TABLE IF NOT EXISTS reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    report_type VARCHAR(100),
    title VARCHAR(255),
    content LONGTEXT,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_report_type (report_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإضافات
CREATE TABLE IF NOT EXISTS plugins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50),
    description TEXT,
    config JSON,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأنشطة
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    activity_type VARCHAR(100),
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
    INDEX idx_project_id (project_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية
INSERT INTO projects (name, path, type, description) VALUES
('نظام أنوبيس للمساعدين الذكيين', '/path/to/anubis/system', 'python', 'نظام ذكي شامل للمساعدة في تطوير البرمجيات'),
('مشروع تجريبي 1', '/path/to/test/project1', 'javascript', 'مشروع تجريبي لاختبار النظام'),
('مشروع تجريبي 2', '/path/to/test/project2', 'java', 'مشروع آخر لاختبار الميزات');

-- إدراج تحليلات تجريبية
INSERT INTO analyses (project_id, agent_type, analysis_data, results, score) VALUES
(1, 'project_analyzer', '{"files_analyzed": 25, "lines_of_code": 2500}', '{"overall_score": 92.5, "issues_found": 3}', 92.5),
(1, 'error_detector', '{"files_scanned": 25, "patterns_checked": 50}', '{"errors_found": 2, "warnings": 5}', 88.0),
(2, 'project_analyzer', '{"files_analyzed": 10, "lines_of_code": 800}', '{"overall_score": 85.0, "issues_found": 1}', 85.0);

-- إدراج أخطاء تجريبية
INSERT INTO errors (project_id, file_path, line_number, error_type, severity, message) VALUES
(1, 'src/main.py', 42, 'syntax_error', 'high', 'خطأ نحوي في السطر 42'),
(1, 'src/utils.py', 15, 'logic_error', 'medium', 'خطأ منطقي محتمل'),
(2, 'app.js', 28, 'undefined_variable', 'medium', 'متغير غير معرف');

-- إدراج إضافات تجريبية
INSERT INTO plugins (name, version, description, config, enabled) VALUES
('Code Quality Checker', '1.0.0', 'فحص جودة الكود', '{"max_complexity": 10, "check_style": true}', TRUE),
('Security Scanner', '1.2.0', 'فحص الأمان', '{"scan_dependencies": true, "check_vulnerabilities": true}', TRUE);

-- إدراج أنشطة تجريبية
INSERT INTO activities (project_id, activity_type, description, metadata) VALUES
(1, 'analysis_completed', 'تم إكمال تحليل المشروع', '{"agent": "project_analyzer", "duration": "45s"}'),
(1, 'error_detected', 'تم اكتشاف خطأ جديد', '{"file": "src/main.py", "line": 42}'),
(2, 'project_created', 'تم إنشاء مشروع جديد', '{"type": "javascript", "files": 10}');

-- عرض إحصائيات قاعدة البيانات
SELECT 
    'المشاريع' as الجدول,
    COUNT(*) as العدد
FROM projects
UNION ALL
SELECT 
    'التحليلات' as الجدول,
    COUNT(*) as العدد
FROM analyses
UNION ALL
SELECT 
    'الأخطاء' as الجدول,
    COUNT(*) as العدد
FROM errors
UNION ALL
SELECT 
    'التقارير' as الجدول,
    COUNT(*) as العدد
FROM reports
UNION ALL
SELECT 
    'الإضافات' as الجدول,
    COUNT(*) as العدد
FROM plugins
UNION ALL
SELECT 
    'الأنشطة' as الجدول,
    COUNT(*) as العدد
FROM activities;

-- عرض معلومات المشاريع
SELECT 
    id as 'رقم المشروع',
    name as 'اسم المشروع',
    type as 'نوع المشروع',
    created_at as 'تاريخ الإنشاء'
FROM projects;

COMMIT;
