#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
𓅃 واجهة فريق حورس للذكاء الاصطناعي
Horus AI Team Interface

واجهة بسيطة وسهلة الاستخدام للوصول لجميع قدرات فريق حورس
Simple and user-friendly interface to access all Horus team capabilities
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# إضافة مسار فريق حورس
current_dir = Path(__file__).parent
horus_path = current_dir / "HORUS_AI_TEAM"
if horus_path.exists():
    sys.path.append(str(horus_path))

class HorusInterface:
    """𓅃 واجهة فريق حورس الرئيسية"""
    
    def __init__(self):
        """تهيئة واجهة حورس"""
        print("𓅃 إيقاظ فريق حورس...")
        print("=" * 60)
        
        self.team_available = False
        self.brain_available = False
        self.agents = {}
        
        # محاولة تحميل نظام الذاكرة والعقل
        try:
            from anubis_team_memory.anubis_team_brain import AnubisTeamBrain
            self.brain = AnubisTeamBrain()
            self.brain_available = True
            print("🧠 تم تفعيل العقل الجماعي لحورس")
        except ImportError as e:
            print(f"⚠️ العقل الجماعي غير متوفر: {e}")
            self.brain = None
        
        # محاولة تحميل مدير سير العمل
        try:
            from team_workflow_manager import AnubisTeamWorkflowManager
            self.workflow_manager = AnubisTeamWorkflowManager()
            self.team_available = True
            print("🔄 تم تفعيل مدير سير العمل")
        except ImportError as e:
            print(f"⚠️ مدير سير العمل غير متوفر: {e}")
            self.workflow_manager = None
        
        # تحميل إعدادات الوكلاء
        self.load_horus_agents()
        
        # عرض حالة التفعيل
        self.display_activation_status()
    
    def load_horus_agents(self):
        """تحميل إعدادات وكلاء حورس"""
        self.agents = {
            "THOTH": {
                "model": "phi3:mini",
                "name": "تحوت",
                "role": "إله الحكمة والكتابة - المحلل السريع",
                "symbol": "⚡",
                "specialties": ["التحليل السريع", "فحص الأخطاء", "الاستجابة الفورية"],
                "best_for": ["تحليل أولي", "فحص سريع", "مراجعة عاجلة"]
            },
            "PTAH": {
                "model": "mistral:7b",
                "name": "بتاح", 
                "role": "إله الحرف والبناء - المطور الخبير",
                "symbol": "🔧",
                "specialties": ["البرمجة المتقدمة", "الحلول التقنية", "الهندسة"],
                "best_for": ["كتابة الكود", "حل المشاكل التقنية", "التصميم"]
            },
            "RA": {
                "model": "llama3:8b",
                "name": "رع",
                "role": "إله الشمس والحكمة - المستشار الاستراتيجي",
                "symbol": "🎯",
                "specialties": ["التخطيط الاستراتيجي", "اتخاذ القرارات", "القيادة"],
                "best_for": ["التخطيط", "الاستراتيجية", "اتخاذ القرارات المهمة"]
            },
            "KHNUM": {
                "model": "strikegpt-r1-zero-8b",
                "name": "خنوم",
                "role": "إله الخلق والإبداع - المبدع والمبتكر", 
                "symbol": "💡",
                "specialties": ["الحلول الإبداعية", "الابتكار", "التفكير خارج الصندوق"],
                "best_for": ["العصف الذهني", "الحلول الإبداعية", "الابتكار"]
            },
            "SESHAT": {
                "model": "Qwen2.5-VL-7B",
                "name": "سشات",
                "role": "إلهة الكتابة والقياس - المحللة البصرية",
                "symbol": "👁️",
                "specialties": ["التحليل البصري", "التوثيق", "القياس"],
                "best_for": ["تحليل الواجهات", "فهم الرسوم", "التوثيق البصري"]
            },
            "HORUS": {
                "model": "gemini_cli",
                "name": "حورس",
                "role": "إله السماء والحكمة - المنسق الأعلى",
                "symbol": "𓅃",
                "specialties": ["التنسيق العام", "الإشراف", "ضمان الجودة"],
                "best_for": ["الإشراف العام", "المراجعة النهائية", "التنسيق"]
            }
        }
    
    def display_activation_status(self):
        """عرض حالة تفعيل فريق حورس"""
        print("\n👁️ حالة فريق حورس:")
        print(f"   🧠 العقل الجماعي: {'✅ نشط' if self.brain_available else '❌ غير متوفر'}")
        print(f"   🔄 مدير سير العمل: {'✅ نشط' if self.team_available else '❌ غير متوفر'}")
        print(f"   🤖 الوكلاء المتاحون: {len(self.agents)}")
        
        if self.team_available or self.brain_available:
            print("\n✅ فريق حورس جاهز للعمل!")
            print("👁️ استخدم horus.help() لعرض الأوامر المتاحة")
        else:
            print("\n⚠️ فريق حورس في الوضع الأساسي")
            print("💡 تأكد من وجود مجلد anubis_ai_team للحصول على الوظائف الكاملة")
    
    def help(self):
        """عرض دليل الاستخدام"""
        print("\n𓅃 دليل استخدام فريق حورس")
        print("=" * 60)
        
        print("\n🎯 الأوامر الأساسية:")
        print("   horus.ask(سؤالك)                    - طرح سؤال على الفريق")
        print("   horus.summon(اسم_الوكيل)             - استدعاء وكيل محدد")
        print("   horus.analyze(وصف_المهمة)            - تحليل مهمة")
        print("   horus.execute(نوع_المهمة, الوصف)     - تنفيذ مهمة كاملة")
        print("   horus.agents()                      - عرض الوكلاء المتاحين")
        print("   horus.wisdom(استفسار)               - طلب الحكمة من العقل الجماعي")
        print("   horus.memory(بحث)                   - البحث في الذاكرة")
        print("   horus.status()                      - عرض حالة الفريق")
        
        print("\n🤖 الوكلاء المتاحون:")
        for agent_id, agent in self.agents.items():
            print(f"   {agent['symbol']} {agent_id} ({agent['name']}) - {agent['role']}")
        
        print("\n💡 أمثلة للاستخدام:")
        print('   horus.ask("كيف يمكن تحسين أداء النظام؟")')
        print('   horus.summon("PTAH")  # للمساعدة في البرمجة')
        print('   horus.analyze("تطوير API جديد")')
        print('   horus.execute("development", "إنشاء نظام مصادقة")')
    
    def ask(self, question: str, agent: str = None) -> str:
        """طرح سؤال على فريق حورس"""
        print(f"\n🤔 سؤال لفريق حورس: {question}")
        
        if agent and agent in self.agents:
            return self.summon(agent, question)
        
        # إذا كان العقل الجماعي متوفر
        if self.brain_available:
            try:
                thinking_result = self.brain.think(question, "general")
                confidence = thinking_result.get('confidence_level', 0)
                recommendations = thinking_result.get('recommendations', {})
                
                response = f"🧠 رد العقل الجماعي (ثقة: {confidence:.1%}):\n"
                
                if recommendations:
                    response += "💡 التوصيات:\n"
                    for key, value in recommendations.items():
                        if isinstance(value, list):
                            response += f"   • {key}: {', '.join(map(str, value))}\n"
                        else:
                            response += f"   • {key}: {value}\n"
                
                return response
                
            except Exception as e:
                print(f"⚠️ خطأ في العقل الجماعي: {e}")
        
        # رد أساسي إذا لم تكن الأنظمة المتقدمة متوفرة
        return self.basic_response(question)
    
    def summon(self, agent_name: str, task: str = None) -> str:
        """استدعاء وكيل محدد"""
        if agent_name not in self.agents:
            available_agents = ", ".join(self.agents.keys())
            return f"❌ الوكيل {agent_name} غير موجود. الوكلاء المتاحون: {available_agents}"
        
        agent = self.agents[agent_name]
        print(f"\n{agent['symbol']} استدعاء {agent['name']} ({agent_name})")
        print(f"🎯 التخصص: {agent['role']}")
        
        if task:
            print(f"📋 المهمة: {task}")
            
            # إذا كان مدير سير العمل متوفر
            if self.team_available:
                try:
                    # تحديد نوع المهمة بناءً على الوكيل
                    task_type = self.determine_task_type(agent_name)
                    result = self.workflow_manager.execute_single_phase(agent['model'], task, task_type)
                    return f"✅ {agent['name']} أكمل المهمة:\n{result.get('response', 'تم الإنجاز')}"
                except Exception as e:
                    print(f"⚠️ خطأ في تنفيذ المهمة: {e}")
            
            return f"📝 {agent['name']} سيعمل على: {task}\n💡 التخصصات: {', '.join(agent['specialties'])}"
        
        return f"✅ {agent['name']} جاهز للعمل!\n🎯 أفضل استخدام: {', '.join(agent['best_for'])}"
    
    def analyze(self, task_description: str, task_type: str = "general") -> Dict:
        """تحليل مهمة باستخدام العقل الجماعي"""
        print(f"\n🔍 تحليل المهمة: {task_description}")
        
        if self.brain_available:
            try:
                analysis = self.brain.think(task_description, task_type)
                print(f"🧠 تحليل مكتمل (ثقة: {analysis.get('confidence_level', 0):.1%})")
                return analysis
            except Exception as e:
                print(f"⚠️ خطأ في التحليل: {e}")
        
        # تحليل أساسي
        return {
            "task_description": task_description,
            "task_type": task_type,
            "recommended_agent": self.recommend_agent_for_task(task_description),
            "analysis_timestamp": datetime.now().isoformat(),
            "confidence_level": 0.6
        }
    
    def execute(self, task_type: str, task_description: str, priority: str = "medium") -> Dict:
        """تنفيذ مهمة كاملة"""
        print(f"\n🚀 تنفيذ مهمة: {task_description}")
        print(f"🎯 النوع: {task_type} | الأولوية: {priority}")
        
        if self.team_available:
            try:
                result = self.workflow_manager.execute_full_workflow(task_type, task_description, priority)
                print("✅ تم إنجاز المهمة بنجاح!")
                return result
            except Exception as e:
                print(f"❌ خطأ في تنفيذ المهمة: {e}")
                return {"error": str(e), "status": "failed"}
        
        # تنفيذ مبسط
        recommended_agent = self.recommend_agent_for_task(task_description)
        return {
            "task_id": f"horus_{int(datetime.now().timestamp())}",
            "task_type": task_type,
            "task_description": task_description,
            "recommended_agent": recommended_agent,
            "status": "planned",
            "message": f"المهمة جاهزة للتنفيذ مع الوكيل {recommended_agent}"
        }
    
    def agents(self) -> Dict:
        """عرض معلومات الوكلاء"""
        print("\n🤖 وكلاء فريق حورس:")
        print("=" * 60)
        
        for agent_id, agent in self.agents.items():
            print(f"\n{agent['symbol']} {agent_id} - {agent['name']}")
            print(f"   🎯 الدور: {agent['role']}")
            print(f"   ⚡ التخصصات: {', '.join(agent['specialties'])}")
            print(f"   💡 أفضل استخدام: {', '.join(agent['best_for'])}")
        
        return self.agents
    
    def wisdom(self, query: str) -> str:
        """طلب الحكمة من العقل الجماعي"""
        print(f"\n🔮 طلب الحكمة: {query}")
        
        if self.brain_available:
            try:
                insights = self.brain.get_team_insights()
                wisdom_response = f"🔮 حكمة فريق حورس:\n"
                
                if insights.get('recommendations'):
                    wisdom_response += "💡 التوصيات الاستراتيجية:\n"
                    for rec in insights['recommendations'][:3]:
                        wisdom_response += f"   • {rec}\n"
                
                return wisdom_response
            except Exception as e:
                print(f"⚠️ خطأ في الوصول للحكمة: {e}")
        
        return "🔮 الحكمة تأتي مع التجربة. استمر في استخدام فريق حورس لتراكم المعرفة."
    
    def memory(self, search_query: str) -> List[Dict]:
        """البحث في ذاكرة فريق حورس"""
        print(f"\n🧠 البحث في الذاكرة: {search_query}")
        
        if self.brain_available:
            try:
                results = self.brain.search_knowledge(search_query)
                print(f"📚 تم العثور على {len(results)} نتيجة")
                return results
            except Exception as e:
                print(f"⚠️ خطأ في البحث: {e}")
        
        return [{"message": "الذاكرة الجماعية غير متوفرة حالياً"}]
    
    def status(self) -> Dict:
        """عرض حالة فريق حورس"""
        print("\n📊 حالة فريق حورس:")
        print("=" * 60)
        
        status = {
            "team_name": "فريق حورس للذكاء الاصطناعي",
            "symbol": "𓅃",
            "brain_available": self.brain_available,
            "workflow_available": self.team_available,
            "agents_count": len(self.agents),
            "status_timestamp": datetime.now().isoformat()
        }
        
        if self.brain_available:
            try:
                brain_stats = self.brain.get_brain_statistics()
                status["brain_stats"] = brain_stats
                print(f"🧠 إحصائيات العقل الجماعي:")
                for key, value in brain_stats.items():
                    print(f"   {key}: {value}")
            except Exception as e:
                print(f"⚠️ خطأ في إحصائيات العقل: {e}")
        
        print(f"\n✅ الحالة العامة: {'نشط بالكامل' if self.brain_available and self.team_available else 'نشط جزئياً'}")
        
        return status
    
    def determine_task_type(self, agent_name: str) -> str:
        """تحديد نوع المهمة بناءً على الوكيل"""
        task_types = {
            "THOTH": "analysis",
            "PTAH": "development", 
            "RA": "planning",
            "KHNUM": "innovation",
            "SESHAT": "review",
            "HORUS": "coordination"
        }
        return task_types.get(agent_name, "general")
    
    def recommend_agent_for_task(self, task_description: str) -> str:
        """توصية وكيل للمهمة"""
        task_lower = task_description.lower()
        
        if any(word in task_lower for word in ["كود", "برمجة", "تطوير", "code", "develop"]):
            return "PTAH"
        elif any(word in task_lower for word in ["تحليل", "فحص", "analysis", "check"]):
            return "THOTH"
        elif any(word in task_lower for word in ["خطة", "استراتيجية", "plan", "strategy"]):
            return "RA"
        elif any(word in task_lower for word in ["إبداع", "ابتكار", "creative", "innovation"]):
            return "KHNUM"
        elif any(word in task_lower for word in ["مراجعة", "توثيق", "review", "document"]):
            return "SESHAT"
        else:
            return "HORUS"
    
    def basic_response(self, question: str) -> str:
        """رد أساسي للأسئلة"""
        responses = {
            "مرحبا": "𓅃 مرحباً! أنا فريق حورس للذكاء الاصطناعي. كيف يمكنني مساعدتك؟",
            "كيف حالك": "👁️ فريق حورس في أفضل حالاته ومستعد للعمل!",
            "ما هو حورس": "𓅃 حورس هو فريق ذكاء اصطناعي جماعي مع ذاكرة وحكمة متراكمة.",
            "مساعدة": "💡 استخدم horus.help() لعرض جميع الأوامر المتاحة."
        }
        
        for key, response in responses.items():
            if key in question.lower():
                return response
        
        return f"🤔 سؤال مثير للاهتمام: '{question}'\n💡 استخدم horus.help() لمعرفة كيفية الحصول على إجابات أكثر تفصيلاً."

# إنشاء مثيل عام لفريق حورس
horus = HorusInterface()

def main():
    """دالة اختبار الواجهة"""
    print("\n🧪 اختبار واجهة فريق حورس")
    print("=" * 60)
    
    # اختبار الأوامر الأساسية
    horus.help()
    
    print("\n" + "="*60)
    print("🎯 جرب هذه الأوامر:")
    print('horus.ask("مرحبا")')
    print('horus.agents()')
    print('horus.summon("PTAH")')
    print('horus.status()')

if __name__ == "__main__":
    main()
