version: '3.8'

services:
  anubis-universal-ai:
    build: .
    container_name: anubis-universal-ai-isolated
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة للذكاء الاصطناعي
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    
    # قيود الموارد للذكاء الاصطناعي (موارد عالية)
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    
    # الشبكة المعزولة للذكاء الاصطناعي
    networks:
      - anubis-ai-net
      - anubis-ai-models-net
      - anubis-ai-data-net
    
    # الأحجام المعزولة للذكاء الاصطناعي
    volumes:
      - anubis-ai-models:/app/universal_ai/models
      - anubis-ai-data:/app/universal_ai/data
      - anubis-ai-configs:/app/universal_ai/configs:ro
      - anubis-ai-logs:/app/universal_ai/logs:rw
      - anubis-ai-cache:/app/universal_ai/cache:rw
      - anubis-ai-embeddings:/app/universal_ai/embeddings
    
    # متغيرات البيئة للذكاء الاصطناعي
    environment:
      - AI_SYSTEM_MODE=isolated_production
      - MODEL_CACHE_ENABLED=true
      - EMBEDDING_CACHE_ENABLED=true
      - AI_MONITORING_ENABLED=true
      - GPU_ACCELERATION=false
      - MAX_TOKENS_PER_REQUEST=4096
      - RATE_LIMITING_ENABLED=true
      - API_SECURITY_ENABLED=true
    
    # حماية النظام للذكاء الاصطناعي
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=500m
      - /var/tmp:rw,noexec,nosuid,size=200m
    
    # إزالة جميع الامتيازات
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - FOWNER
    
    # المنافذ المحمية للذكاء الاصطناعي
    ports:
      - "127.0.0.1:8090:8090"  # واجهة الذكاء الاصطناعي الرئيسية
      - "127.0.0.1:8091:8091"  # واجهة إدارة النماذج
    
    # تسميات للمراقبة
    labels:
      - "anubis.component=universal_ai"
      - "anubis.isolation.level=advanced"
      - "anubis.ai.models.enabled=true"
      - "anubis.monitoring.enabled=true"
    
    # التبعيات
    depends_on:
      - anubis-ai-monitor
      - anubis-ai-vector-db
  
  anubis-ai-monitor:
    image: prom/prometheus:latest
    container_name: anubis-ai-monitor
    restart: unless-stopped
    networks:
      - anubis-ai-net
    volumes:
      - ./monitoring/prometheus-ai.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-ai-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9092:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
  
  anubis-ai-vector-db:
    image: chromadb/chroma:latest
    container_name: anubis-ai-vector-db
    restart: unless-stopped
    networks:
      - anubis-ai-data-net
    volumes:
      - anubis-ai-vectordb:/chroma/chroma
    ports:
      - "127.0.0.1:8000:8000"
    environment:
      - CHROMA_SERVER_AUTH_ENABLED=true
      - CHROMA_SERVER_AUTH_CREDENTIALS_FILE=/chroma/auth.txt
    security_opt:
      - no-new-privileges:true
  
  anubis-ai-redis-cache:
    image: redis:7-alpine
    container_name: anubis-ai-redis-cache
    restart: unless-stopped
    networks:
      - anubis-ai-data-net
    volumes:
      - anubis-ai-redis-data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    security_opt:
      - no-new-privileges:true
  
  anubis-ai-model-server:
    image: ollama/ollama:latest
    container_name: anubis-ai-model-server
    restart: unless-stopped
    networks:
      - anubis-ai-models-net
    volumes:
      - anubis-ai-ollama-models:/root/.ollama
    ports:
      - "127.0.0.1:11434:11434"
    environment:
      - OLLAMA_MODELS=/root/.ollama/models
      - OLLAMA_HOST=0.0.0.0
    security_opt:
      - no-new-privileges:true

# الشبكات المعزولة للذكاء الاصطناعي
networks:
  anubis-ai-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
  
  anubis-ai-models-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16
  
  anubis-ai-data-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# الأحجام المعزولة للذكاء الاصطناعي
volumes:
  anubis-ai-models:
    driver: local
  anubis-ai-data:
    driver: local
  anubis-ai-configs:
    driver: local
  anubis-ai-logs:
    driver: local
  anubis-ai-cache:
    driver: local
  anubis-ai-embeddings:
    driver: local
  anubis-ai-monitor-data:
    driver: local
  anubis-ai-vectordb:
    driver: local
  anubis-ai-redis-data:
    driver: local
  anubis-ai-ollama-models:
    driver: local
