@echo off
chcp 65001 >nul
color 0A
title VS Code Performance Optimizer - Main Launcher

cls
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █        🚀 VS Code Performance Optimizer 🚀                  █
echo █                                                              █
echo █     نظام شامل لتحسين أداء VS Code والنظام                 █
echo █                                                              █
echo █     النتائج المحققة:                                         █
echo █     • المعالج: 95.9%% → 11.1%% (تحسن 84.8%%!)              █
echo █     • الذاكرة: 89.0%% → 62.2%% (تحسن 26.8%%!)              █
echo █     • VS Code: 56.2%% → 26.0%% (تحسن 30.2%%!)             █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 🎯 اختر الواجهة المناسبة لك:
echo.
echo 1️⃣  التشغيل السريع (موصى به للمبتدئين)
echo 2️⃣  الواجهة المتقدمة (Task Manager كامل)
echo 3️⃣  النسخة المستقرة (معالجة محسنة للأخطاء)
echo 4️⃣  الواجهة الموحدة (الأصلية)
echo 5️⃣  تطبيق تحسينات VS Code
echo 6️⃣  تحليل النظام
echo 7️⃣  عرض التوثيق
echo 0️⃣  خروج
echo.

set /p choice=اختر رقم الخيار (1-7): 

if "%choice%"=="1" goto quick_start
if "%choice%"=="2" goto pro_version
if "%choice%"=="3" goto stable_version
if "%choice%"=="4" goto unified_version
if "%choice%"=="5" goto apply_optimizations
if "%choice%"=="6" goto analyze_system
if "%choice%"=="7" goto show_docs
if "%choice%"=="0" goto exit
goto invalid_choice

:quick_start
echo.
echo 🚀 تشغيل الواجهة السريعة...
call quick_start.bat
goto end

:pro_version
echo.
echo 🚀 تشغيل الواجهة المتقدمة (Task Manager)...
call start_pro.bat
goto end

:stable_version
echo.
echo 🚀 تشغيل النسخة المستقرة...
call start_stable.bat
goto end

:unified_version
echo.
echo 🚀 تشغيل الواجهة الموحدة...
call start.bat
goto end

:apply_optimizations
echo.
echo 🔧 تطبيق تحسينات VS Code...
call apply_vscode_optimizations.bat
goto menu

:analyze_system
echo.
echo 🔍 تحليل النظام...
python test_system.py
echo.
pause
goto menu

:show_docs
echo.
echo 📚 التوثيق المتاح:
echo.
echo 📄 README_MAIN.md - الدليل الرئيسي
echo 📄 README_PRO.md - دليل الواجهة المتقدمة
echo 📄 FINAL_SOLUTION.md - الحل النهائي
echo 📄 HOW_TO_RUN.md - دليل التشغيل السريع
echo.
echo 💡 افتح أي ملف .md بـ Notepad أو VS Code لقراءته
echo.
pause
goto menu

:invalid_choice
echo.
echo ❌ خيار غير صحيح! يرجى اختيار رقم من 1-7 أو 0 للخروج
echo.
pause
goto menu

:menu
cls
echo.
echo 🔄 العودة للقائمة الرئيسية...
timeout /t 2 >nul
goto start

:exit
echo.
echo 👋 شكراً لاستخدام VS Code Performance Optimizer!
echo 💡 لا تنس تطبيق التحسينات للحصول على أفضل أداء
echo.
pause
exit

:end
echo.
echo ✅ انتهى التشغيل
echo 🔄 هل تريد العودة للقائمة الرئيسية؟ (Y/N)
set /p return_choice=

if /i "%return_choice%"=="Y" goto menu
if /i "%return_choice%"=="y" goto menu

echo.
echo 👋 شكراً لاستخدام VS Code Performance Optimizer!
pause
exit

:start
goto menu
