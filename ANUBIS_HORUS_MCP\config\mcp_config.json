{"server": {"name": "anubis-horus-mcp", "version": "1.0.0", "description": "Integrated MCP Server for Anubis and Horus Systems", "host": "localhost", "port": 8080, "protocol": "websocket", "max_concurrent_tools": 20, "timeout": 300, "debug": false, "log_level": "INFO"}, "horus_team": {"enabled": true, "collaborative_mode": true, "shared_memory": true, "auto_consultation": true, "team_members": {"THOTH": {"model": "phi3:mini", "enabled": true, "specialties": ["system_analysis", "error_detection", "quick_profiling"], "mcp_tools": ["system_analyzer", "error_detector", "quick_profiler"], "max_concurrent_tasks": 3}, "PTAH": {"model": "mistral:7b", "enabled": true, "specialties": ["code_generation", "technical_solutions", "architecture_design"], "mcp_tools": ["code_generator", "technical_solver", "architecture_designer"], "max_concurrent_tasks": 2}, "RA": {"model": "llama3:8b", "enabled": true, "specialties": ["strategic_planning", "decision_making", "project_management"], "mcp_tools": ["strategy_planner", "decision_maker", "project_manager"], "max_concurrent_tasks": 2}, "KHNUM": {"model": "strikegpt-r1-zero-8b", "enabled": true, "specialties": ["creative_solutions", "innovation", "brainstorming"], "mcp_tools": ["creative_generator", "innovation_engine", "brainstorm_facilitator"], "max_concurrent_tasks": 2}, "SESHAT": {"model": "Qwen2.5-VL-7B", "enabled": true, "specialties": ["visual_analysis", "documentation", "measurement"], "mcp_tools": ["visual_analyzer", "document_processor", "measurement_tools"], "max_concurrent_tasks": 2}}}, "tools": {"categories": {"system": {"enabled": true, "tools": ["disk_analyzer", "process_monitor", "service_manager", "network_analyzer", "environment_manager", "registry_scanner", "performance_profiler", "file_system_explorer", "system_info_collector", "log_analyzer", "startup_manager", "driver_manager", "security_scanner", "backup_manager", "cleanup_optimizer"]}, "development": {"enabled": true, "tools": ["python_env_manager", "nodejs_tools", "java_environment", "vscode_integration", "git_operations", "docker_manager", "kubernetes_controller", "package_manager", "code_analyzer", "test_runner", "build_automation", "deployment_manager"]}, "cloud": {"enabled": true, "tools": ["google_cloud_manager", "aws_integration", "azure_connector", "cloud_storage", "serverless_functions", "cloud_databases", "cdn_manager", "monitoring_services"]}, "ai": {"enabled": true, "tools": ["openai_connector", "anthropic_connector", "google_ai_connector", "local_ai_models", "vector_database", "embedding_generator", "text_processor", "image_analyzer", "speech_processor", "ml_pipeline"]}, "database": {"enabled": true, "tools": ["mysql_manager", "postgresql_manager", "redis_manager", "sqlite_manager", "mongodb_manager"]}, "web": {"enabled": true, "tools": ["http_client", "websocket_client", "api_tester", "web_scraper", "url_analyzer"]}, "integration": {"enabled": true, "tools": ["github_integration", "gitlab_integration", "slack_connector", "discord_connector", "email_sender"]}}, "discovery_sources": {"comprehensive_scan": {"drives": 4, "services": 316, "processes": 291, "registry_entries": 399, "environment_variables": 71, "network_interfaces": 7}, "development_tools_scan": {"python_installations": 12, "nodejs_tools": 13, "java_installations": 8, "vscode_extensions": 114, "browser_extensions": 80}, "cloud_tools_scan": {"google_cloud_sdk": true, "docker_desktop": true, "kubernetes": true, "wsl_distributions": 3}, "ai_libraries_scan": {"sema4ai": true, "uv_tools": true, "openai": true, "anthropic": true, "google_ai": true}}}, "security": {"encryption": {"enabled": true, "algorithm": "AES-256", "key_rotation": {"enabled": true, "interval_days": 30, "auto_rotation": true}}, "api_keys": {"vault_path": "./api_keys_vault/vault", "backup_enabled": true, "access_logging": true, "validation_enabled": true, "providers": ["openai", "anthropic", "google", "github", "langsmith", "azure", "aws", "google_cloud"]}, "access_control": {"rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 20}, "ip_whitelist": [], "authentication": {"required": false, "method": "api_key"}}, "audit": {"enabled": true, "log_file": "./logs/security_audit.log", "events": ["key_access", "tool_execution", "authentication", "authorization", "errors"]}}, "monitoring": {"performance_tracking": {"enabled": true, "metrics": ["response_time", "memory_usage", "cpu_usage", "tool_execution_time", "error_rate"], "collection_interval": 60, "retention_days": 30}, "health_checks": {"enabled": true, "interval": 30, "endpoints": ["/health", "/metrics", "/status"]}, "alerting": {"enabled": false, "thresholds": {"error_rate": 0.05, "response_time": 5000, "memory_usage": 0.8}, "channels": []}}, "protocols": {"websocket": {"enabled": true, "port": 8080, "max_connections": 100, "heartbeat_interval": 30}, "grpc": {"enabled": false, "port": 8081, "max_connections": 50}, "mqtt": {"enabled": false, "broker": "localhost", "port": 1883, "topics": ["anubis/tools", "horus/team", "mcp/events"]}}, "integrations": {"anubis_system": {"enabled": true, "path": "../ANUBIS_SYSTEM", "config_file": "config/default_config.json"}, "horus_team": {"enabled": true, "path": "../HORUS_AI_TEAM", "interface_file": "horus_interface.py"}, "shared_requirements": {"enabled": true, "path": "../SHARED_REQUIREMENTS", "requirements_file": "requirements_anubis_horus_unified.txt"}}, "development": {"debug_mode": false, "hot_reload": false, "test_mode": false, "mock_external_apis": false, "verbose_logging": false}, "features": {"experimental": {"enabled": false, "features": ["advanced_ai_routing", "predictive_caching", "auto_scaling"]}, "beta": {"enabled": true, "features": ["visual_tool_interface", "collaborative_debugging", "smart_recommendations"]}}}