#!/usr/bin/env python3
"""
🏺 سكريبت التشغيل السريع لنظام أنوبيس
Anubis Quick Start Script

هذا السكريبت يقوم بإعداد وتشغيل نظام أنوبيس بأسرع طريقة ممكنة
This script sets up and runs Anubis system as quickly as possible

الاستخدام / Usage:
    python quick_start_anubis.py

المتطلبات / Requirements:
    - Python 3.8+
    - pip
    - اتصال إنترنت / Internet connection
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

# ألوان للطباعة الملونة
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(text, color=Colors.WHITE):
    """طباعة نص ملون"""
    print(f"{color}{text}{Colors.END}")

def print_header(text):
    """طباعة عنوان مميز"""
    print_colored(f"\n{'='*60}", Colors.CYAN)
    print_colored(f"🏺 {text}", Colors.BOLD + Colors.YELLOW)
    print_colored(f"{'='*60}", Colors.CYAN)

def run_command(command, description, critical=True):
    """تشغيل أمر مع عرض الوصف والنتيجة"""
    print_colored(f"\n🔄 {description}...", Colors.BLUE)
    
    try:
        # تشغيل الأمر
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            timeout=300  # 5 دقائق timeout
        )
        
        print_colored(f"✅ {description} - مكتمل بنجاح", Colors.GREEN)
        if result.stdout.strip():
            print_colored(f"📄 النتيجة: {result.stdout.strip()}", Colors.WHITE)
        return True
        
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ {description} - فشل!", Colors.RED)
        print_colored(f"📄 الخطأ: {e.stderr if e.stderr else str(e)}", Colors.RED)
        
        if critical:
            print_colored("🚨 هذا خطأ حرج! يجب إصلاحه قبل المتابعة.", Colors.RED)
            return False
        else:
            print_colored("⚠️ هذا خطأ غير حرج، يمكن المتابعة.", Colors.YELLOW)
            return True
            
    except subprocess.TimeoutExpired:
        print_colored(f"⏰ {description} - انتهت المهلة الزمنية", Colors.YELLOW)
        return False
    except Exception as e:
        print_colored(f"💥 {description} - خطأ غير متوقع: {str(e)}", Colors.RED)
        return False

def check_python_version():
    """فحص إصدار Python"""
    print_header("فحص متطلبات النظام")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_colored("❌ Python 3.8+ مطلوب!", Colors.RED)
        print_colored(f"الإصدار الحالي: {version.major}.{version.minor}.{version.micro}", Colors.RED)
        return False
    
    print_colored(f"✅ Python {version.major}.{version.minor}.{version.micro} - مناسب", Colors.GREEN)
    return True

def setup_virtual_environment():
    """إعداد البيئة الافتراضية"""
    print_header("إعداد البيئة الافتراضية")
    
    venv_path = Path("anubis_env")
    
    if venv_path.exists():
        print_colored("📁 البيئة الافتراضية موجودة مسبقاً", Colors.YELLOW)
        return True
    
    # إنشاء البيئة الافتراضية
    if not run_command("python -m venv anubis_env", "إنشاء البيئة الافتراضية"):
        return False
    
    print_colored("✅ تم إنشاء البيئة الافتراضية بنجاح", Colors.GREEN)
    return True

def activate_and_upgrade_pip():
    """تفعيل البيئة وترقية pip"""
    print_header("تفعيل البيئة وترقية الأدوات")
    
    # تحديد أمر التفعيل حسب نظام التشغيل
    if os.name == 'nt':  # Windows
        activate_cmd = "anubis_env\\Scripts\\activate"
        pip_cmd = "anubis_env\\Scripts\\pip"
    else:  # Linux/Mac
        activate_cmd = "source anubis_env/bin/activate"
        pip_cmd = "anubis_env/bin/pip"
    
    # ترقية pip
    upgrade_cmd = f"{pip_cmd} install --upgrade pip"
    if not run_command(upgrade_cmd, "ترقية pip"):
        return False
    
    return True

def install_basic_packages():
    """تثبيت المكتبات الأساسية"""
    print_header("تثبيت المكتبات الأساسية")
    
    # تحديد أمر pip
    if os.name == 'nt':  # Windows
        pip_cmd = "anubis_env\\Scripts\\pip"
    else:  # Linux/Mac
        pip_cmd = "anubis_env/bin/pip"
    
    # قائمة المكتبات الأساسية بترتيب الأولوية
    essential_packages = [
        ("fastapi==0.104.1", "FastAPI - إطار العمل الأساسي"),
        ("uvicorn[standard]==0.24.0", "Uvicorn - خادم ASGI"),
        ("pydantic==2.5.0", "Pydantic - التحقق من البيانات"),
        ("python-multipart==0.0.6", "دعم رفع الملفات"),
        ("jinja2==3.1.2", "محرك القوالب"),
    ]
    
    database_packages = [
        ("sqlalchemy==2.0.23", "SQLAlchemy - ORM قاعدة البيانات"),
        ("sqlite3", "SQLite - قاعدة بيانات محلية", False),  # مدمجة في Python
    ]
    
    security_packages = [
        ("python-jose[cryptography]==3.3.0", "JWT والتشفير"),
        ("passlib[bcrypt]==1.7.4", "تشفير كلمات المرور"),
        ("cryptography==41.0.7", "مكتبة التشفير"),
    ]
    
    # تثبيت المكتبات الأساسية
    print_colored("📦 تثبيت المكتبات الأساسية...", Colors.BLUE)
    for package_info in essential_packages:
        package = package_info[0]
        description = package_info[1]
        
        install_cmd = f"{pip_cmd} install {package}"
        if not run_command(install_cmd, f"تثبيت {description}"):
            return False
    
    # تثبيت مكتبات قاعدة البيانات
    print_colored("🗄️ تثبيت مكتبات قاعدة البيانات...", Colors.BLUE)
    for package_info in database_packages:
        if len(package_info) == 3 and not package_info[2]:  # تخطي SQLite
            continue
            
        package = package_info[0]
        description = package_info[1]
        
        install_cmd = f"{pip_cmd} install {package}"
        run_command(install_cmd, f"تثبيت {description}", critical=False)
    
    # تثبيت مكتبات الأمان
    print_colored("🔐 تثبيت مكتبات الأمان...", Colors.BLUE)
    for package_info in security_packages:
        package = package_info[0]
        description = package_info[1]
        
        install_cmd = f"{pip_cmd} install {package}"
        run_command(install_cmd, f"تثبيت {description}", critical=False)
    
    return True

def setup_environment_file():
    """إعداد ملف البيئة"""
    print_header("إعداد ملف البيئة")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print_colored("📄 ملف .env موجود مسبقاً", Colors.YELLOW)
        return True
    
    if env_example.exists():
        # نسخ من المثال
        shutil.copy(env_example, env_file)
        print_colored("✅ تم إنشاء ملف .env من المثال", Colors.GREEN)
        
        # إضافة إعدادات أساسية آمنة
        with open(env_file, 'a', encoding='utf-8') as f:
            f.write(f"\n# إعدادات التشغيل السريع - تم إنشاؤها تلقائياً\n")
            f.write(f"QUICK_START_MODE=true\n")
            f.write(f"DATABASE_TYPE=sqlite\n")
            f.write(f"DATABASE_URL=sqlite:///./anubis_quickstart.db\n")
            f.write(f"DEBUG=true\n")
            f.write(f"HOST=localhost\n")
            f.write(f"PORT=8000\n")
        
        print_colored("✅ تم إضافة إعدادات التشغيل السريع", Colors.GREEN)
        return True
    else:
        print_colored("⚠️ ملف .env.example غير موجود، سيتم إنشاء ملف أساسي", Colors.YELLOW)
        
        # إنشاء ملف أساسي
        basic_env = """# ملف البيئة الأساسي لنظام أنوبيس
APP_NAME=Anubis AI System
APP_VERSION=1.0.0
DEBUG=true
HOST=localhost
PORT=8000

# قاعدة البيانات
DATABASE_TYPE=sqlite
DATABASE_URL=sqlite:///./anubis_quickstart.db

# الأمان (يجب تغييرها في الإنتاج!)
SECRET_KEY=anubis-quick-start-secret-key-change-in-production
JWT_SECRET_KEY=anubis-jwt-secret-change-in-production

# وضع التشغيل السريع
QUICK_START_MODE=true
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(basic_env)
        
        print_colored("✅ تم إنشاء ملف .env أساسي", Colors.GREEN)
        return True

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print_header("اختبار الاستيرادات الأساسية")
    
    # تحديد أمر Python
    if os.name == 'nt':  # Windows
        python_cmd = "anubis_env\\Scripts\\python"
    else:  # Linux/Mac
        python_cmd = "anubis_env/bin/python"
    
    # اختبار المكتبات الأساسية
    test_imports = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("sqlalchemy", "SQLAlchemy"),
    ]
    
    all_passed = True
    for module, name in test_imports:
        test_cmd = f'{python_cmd} -c "import {module}; print(\\"{name} OK\\")"'
        if not run_command(test_cmd, f"اختبار {name}", critical=False):
            all_passed = False
    
    if all_passed:
        print_colored("✅ جميع الاستيرادات تعمل بنجاح!", Colors.GREEN)
    else:
        print_colored("⚠️ بعض الاستيرادات فشلت، لكن يمكن المتابعة", Colors.YELLOW)
    
    return True

def start_server():
    """تشغيل الخادم"""
    print_header("تشغيل خادم أنوبيس")
    
    # تحديد أمر Python
    if os.name == 'nt':  # Windows
        python_cmd = "anubis_env\\Scripts\\python"
        uvicorn_cmd = "anubis_env\\Scripts\\uvicorn"
    else:  # Linux/Mac
        python_cmd = "anubis_env/bin/python"
        uvicorn_cmd = "anubis_env/bin/uvicorn"
    
    print_colored("🚀 بدء تشغيل خادم أنوبيس...", Colors.BLUE)
    print_colored("📱 ستتمكن من الوصول للنظام على:", Colors.CYAN)
    print_colored("   🌐 http://localhost:8000", Colors.WHITE)
    print_colored("   📚 http://localhost:8000/docs (API Documentation)", Colors.WHITE)
    print_colored("   ❤️ http://localhost:8000/health (Health Check)", Colors.WHITE)
    
    print_colored("\n⚠️ لإيقاف الخادم اضغط Ctrl+C", Colors.YELLOW)
    print_colored("🔄 جاري التشغيل...\n", Colors.BLUE)
    
    # محاولة تشغيل الخادم
    try:
        # أولاً جرب main.py
        if Path("main.py").exists():
            subprocess.run([python_cmd, "main.py"])
        # ثم جرب uvicorn مباشرة
        elif Path("src/core/main.py").exists():
            subprocess.run([
                uvicorn_cmd, "src.core.main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload"
            ])
        else:
            print_colored("❌ لم يتم العثور على ملف التشغيل الرئيسي!", Colors.RED)
            return False
            
    except KeyboardInterrupt:
        print_colored("\n👋 تم إيقاف الخادم بواسطة المستخدم", Colors.YELLOW)
        return True
    except Exception as e:
        print_colored(f"❌ خطأ في تشغيل الخادم: {str(e)}", Colors.RED)
        return False

def main():
    """الدالة الرئيسية"""
    print_colored("""
🏺 ═══════════════════════════════════════════════════════════════
   مرحباً بك في سكريبت التشغيل السريع لنظام أنوبيس
   Welcome to Anubis Quick Start Script
═══════════════════════════════════════════════════════════════
""", Colors.BOLD + Colors.CYAN)
    
    print_colored("🎯 هذا السكريبت سيقوم بإعداد وتشغيل نظام أنوبيس خلال دقائق", Colors.WHITE)
    print_colored("⏰ الوقت المتوقع: 5-10 دقائق", Colors.WHITE)
    print_colored("📋 المتطلبات: Python 3.8+ + اتصال إنترنت\n", Colors.WHITE)
    
    # التحقق من الموافقة
    try:
        response = input("🤔 هل تريد المتابعة؟ (y/n): ").lower().strip()
        if response not in ['y', 'yes', 'نعم', 'ن']:
            print_colored("👋 تم الإلغاء بواسطة المستخدم", Colors.YELLOW)
            return
    except KeyboardInterrupt:
        print_colored("\n👋 تم الإلغاء بواسطة المستخدم", Colors.YELLOW)
        return
    
    # بدء العملية
    start_time = time.time()
    
    # 1. فحص Python
    if not check_python_version():
        return
    
    # 2. إعداد البيئة الافتراضية
    if not setup_virtual_environment():
        return
    
    # 3. تفعيل البيئة وترقية pip
    if not activate_and_upgrade_pip():
        return
    
    # 4. تثبيت المكتبات
    if not install_basic_packages():
        return
    
    # 5. إعداد ملف البيئة
    if not setup_environment_file():
        return
    
    # 6. اختبار الاستيرادات
    test_basic_imports()
    
    # حساب الوقت المستغرق
    elapsed_time = time.time() - start_time
    minutes = int(elapsed_time // 60)
    seconds = int(elapsed_time % 60)
    
    print_header("🎉 اكتمل الإعداد بنجاح!")
    print_colored(f"⏰ الوقت المستغرق: {minutes} دقيقة و {seconds} ثانية", Colors.GREEN)
    print_colored("✅ نظام أنوبيس جاهز للتشغيل!", Colors.GREEN)
    
    # سؤال عن تشغيل الخادم
    try:
        response = input("\n🚀 هل تريد تشغيل الخادم الآن؟ (y/n): ").lower().strip()
        if response in ['y', 'yes', 'نعم', 'ن']:
            start_server()
        else:
            print_colored("\n📋 لتشغيل الخادم لاحقاً، استخدم:", Colors.CYAN)
            if os.name == 'nt':
                print_colored("   anubis_env\\Scripts\\activate", Colors.WHITE)
                print_colored("   python main.py", Colors.WHITE)
            else:
                print_colored("   source anubis_env/bin/activate", Colors.WHITE)
                print_colored("   python main.py", Colors.WHITE)
    except KeyboardInterrupt:
        print_colored("\n👋 شكراً لاستخدام نظام أنوبيس!", Colors.YELLOW)

if __name__ == "__main__":
    main()
