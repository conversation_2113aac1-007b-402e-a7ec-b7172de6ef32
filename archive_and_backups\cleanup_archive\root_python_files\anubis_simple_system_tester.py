#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار نظام أنوبيس المبسط
Anubis Simple System Tester
"""

import os
import json
import subprocess
import time
import requests
from pathlib import Path
from datetime import datetime
import logging
import shutil

class AnubisSimpleSystemTester:
    def __init__(self):
        self.base_path = Path(".")
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tester": "Anubis Simple System Tester",
            "test_type": "basic_system_functionality_test",
            "overall_status": "in_progress",
            "test_categories": {},
            "service_endpoints": {},
            "performance_metrics": {},
            "security_tests": {},
            "failures": [],
            "recommendations": []
        }
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🧪 بدء اختبار نظام أنوبيس الشامل")
        print("=" * 60)
        
        # اختبار البنية التحتية
        self.test_infrastructure()
        
        # اختبار الملفات والمجلدات
        self.test_file_structure()
        
        # اختبار الخدمات الأساسية
        self.test_core_services()
        
        # اختبار نماذج الذكاء الاصطناعي
        self.test_ai_components()
        
        # اختبار سير العمل
        self.test_workflow_components()
        
        # اختبار قواعد البيانات
        self.test_database_components()
        
        # اختبار الأمان
        self.test_security_components()
        
        # اختبار الأداء
        self.test_performance()
        
        # تقييم النتائج
        self.evaluate_results()
        
        return self.test_results
    
    def test_infrastructure(self):
        """اختبار البنية التحتية"""
        print("🏗️ اختبار البنية التحتية...")
        
        infrastructure_tests = {
            "docker_availability": False,
            "docker_compose_availability": False,
            "python_version": "",
            "network_connectivity": False,
            "disk_space": {}
        }
        
        # اختبار Docker
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                infrastructure_tests["docker_availability"] = True
                self.logger.info("✅ Docker متاح")
                print(f"   🐳 Docker: {result.stdout.strip()}")
            else:
                self.logger.error("❌ Docker غير متاح")
                print("   🐳 Docker: ❌ غير متاح")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار Docker: {e}")
            print("   🐳 Docker: ❌ غير متاح")
        
        # اختبار Docker Compose
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                infrastructure_tests["docker_compose_availability"] = True
                self.logger.info("✅ Docker Compose متاح")
                print(f"   🐳 Docker Compose: {result.stdout.strip()}")
            else:
                # محاولة docker compose (بدون شرطة)
                try:
                    result = subprocess.run(['docker', 'compose', 'version'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        infrastructure_tests["docker_compose_availability"] = True
                        self.logger.info("✅ Docker Compose متاح")
                        print(f"   🐳 Docker Compose: {result.stdout.strip()}")
                    else:
                        self.logger.error("❌ Docker Compose غير متاح")
                        print("   🐳 Docker Compose: ❌ غير متاح")
                except:
                    self.logger.error("❌ Docker Compose غير متاح")
                    print("   🐳 Docker Compose: ❌ غير متاح")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار Docker Compose: {e}")
            print("   🐳 Docker Compose: ❌ غير متاح")
        
        # اختبار Python
        try:
            import sys
            infrastructure_tests["python_version"] = sys.version
            print(f"   🐍 Python: {sys.version.split()[0]}")
        except Exception as e:
            print(f"   🐍 Python: ❌ خطأ - {e}")
        
        # اختبار الاتصال بالإنترنت
        try:
            response = requests.get("https://www.google.com", timeout=5)
            if response.status_code == 200:
                infrastructure_tests["network_connectivity"] = True
                self.logger.info("✅ الاتصال بالإنترنت متاح")
                print("   🌐 الإنترنت: ✅ متصل")
            else:
                self.logger.warning("⚠️ مشكلة في الاتصال بالإنترنت")
                print("   🌐 الإنترنت: ⚠️ مشكلة في الاتصال")
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
            print("   🌐 الإنترنت: ❌ غير متصل")
        
        # اختبار مساحة القرص
        try:
            total, used, free = shutil.disk_usage(self.base_path)
            infrastructure_tests["disk_space"] = {
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "usage_percent": round((used / total) * 100, 2)
            }
            print(f"   💾 مساحة القرص: {infrastructure_tests['disk_space']['free_gb']} GB متاحة")
        except Exception as e:
            self.logger.error(f"❌ خطأ في قياس مساحة القرص: {e}")
        
        self.test_results["test_categories"]["infrastructure"] = infrastructure_tests
    
    def test_file_structure(self):
        """اختبار هيكل الملفات والمجلدات"""
        print("📁 اختبار هيكل الملفات والمجلدات...")
        
        file_structure_tests = {
            "system_directories": {},
            "required_files": {},
            "script_files": {},
            "config_files": {}
        }
        
        # اختبار المجلدات الأساسية
        required_dirs = [
            "anubis_main_system", "universal_ai_system", "workflows_and_automation",
            "workspace", "database", "configs", "isolation_systems", 
            "tools_and_utilities", "archive_and_backups", "documentation",
            "scripts", "reports", "logs", "isolation_configs", "utilities"
        ]
        
        print("   📂 المجلدات الأساسية:")
        for dir_name in required_dirs:
            dir_path = self.base_path / dir_name
            exists = dir_path.exists()
            file_structure_tests["system_directories"][dir_name] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {dir_name}")
            if exists:
                self.logger.info(f"✅ مجلد {dir_name} موجود")
            else:
                self.logger.warning(f"⚠️ مجلد {dir_name} مفقود")
        
        # اختبار الملفات المطلوبة
        required_files = [
            "docker-compose.yml", "Dockerfile", "README.md",
            "start_anubis_isolated.sh", ".env.template"
        ]
        
        print("   📄 الملفات الأساسية:")
        for file_name in required_files:
            file_path = self.base_path / file_name
            exists = file_path.exists()
            file_structure_tests["required_files"][file_name] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {file_name}")
            if exists:
                self.logger.info(f"✅ ملف {file_name} موجود")
            else:
                self.logger.warning(f"⚠️ ملف {file_name} مفقود")
        
        # اختبار سكريبتات التشغيل
        script_files = [
            "anubis_main_system/start_isolated_main_system.sh",
            "universal_ai_system/start_isolated_ai_system.sh",
            "workflows_and_automation/start_isolated_workflows.sh",
            "workspace/start_isolated_workspace.sh",
            "tools_and_utilities/start_isolated_tools.sh"
        ]
        
        print("   🚀 سكريبتات التشغيل:")
        for script in script_files:
            script_path = self.base_path / script
            exists = script_path.exists()
            file_structure_tests["script_files"][script] = exists
            status = "✅" if exists else "❌"
            service_name = script.split('/')[0]
            print(f"      {status} {service_name}")
            if exists:
                self.logger.info(f"✅ سكريبت {script} موجود")
            else:
                self.logger.warning(f"⚠️ سكريبت {script} مفقود")
        
        self.test_results["test_categories"]["file_structure"] = file_structure_tests
    
    def test_core_services(self):
        """اختبار الخدمات الأساسية"""
        print("🏠 اختبار الخدمات الأساسية...")
        
        # قائمة الخدمات والمنافذ
        services = {
            "main_system": {"url": "http://localhost:8080", "name": "النظام الرئيسي"},
            "ai_system": {"url": "http://localhost:8090", "name": "نظام الذكاء الاصطناعي"},
            "workflows": {"url": "http://localhost:5678", "name": "سير العمل (n8n)"},
            "workspace": {"url": "http://localhost:8888", "name": "بيئة العمل (Jupyter)"},
            "monitoring": {"url": "http://localhost:9090", "name": "المراقبة (Prometheus)"}
        }
        
        core_services = {}
        print("   🌐 اختبار نقاط الخدمة:")
        
        for service_key, service_info in services.items():
            result = self._test_service_endpoint(service_info["url"], service_info["name"])
            core_services[service_key] = result
            
            status = "✅ متاح" if result.get("available") else "❌ غير متاح"
            response_time = f" ({result.get('response_time', 'N/A')}s)" if result.get("response_time") else ""
            print(f"      {status} {service_info['name']}{response_time}")
        
        self.test_results["test_categories"]["core_services"] = core_services
        self.test_results["service_endpoints"] = core_services
    
    def _test_service_endpoint(self, url, service_name):
        """اختبار نقطة خدمة"""
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                self.logger.info(f"✅ {service_name} متاح على {url}")
                return {
                    "available": True,
                    "status_code": response.status_code,
                    "response_time": round(response.elapsed.total_seconds(), 3),
                    "url": url
                }
            else:
                self.logger.warning(f"⚠️ {service_name} يستجيب بكود {response.status_code}")
                return {
                    "available": False,
                    "status_code": response.status_code,
                    "response_time": round(response.elapsed.total_seconds(), 3),
                    "url": url
                }
        except requests.exceptions.ConnectionError:
            self.logger.warning(f"⚠️ {service_name} غير متاح على {url}")
            return {
                "available": False,
                "error": "Connection refused",
                "url": url
            }
        except Exception as e:
            self.logger.error(f"❌ خطأ في اختبار {service_name}: {e}")
            return {
                "available": False,
                "error": str(e),
                "url": url
            }
    
    def test_ai_components(self):
        """اختبار مكونات الذكاء الاصطناعي"""
        print("🤖 اختبار مكونات الذكاء الاصطناعي...")
        
        ai_tests = {
            "ollama_server": self._test_service_endpoint("http://localhost:11434", "خادم Ollama"),
            "chroma_db": self._test_service_endpoint("http://localhost:8000", "قاعدة البيانات المتجهة"),
            "ai_configs": {},
            "model_directories": {}
        }
        
        print("   🤖 خدمات الذكاء الاصطناعي:")
        
        # اختبار خادم Ollama
        ollama_status = "✅ متاح" if ai_tests["ollama_server"].get("available") else "❌ غير متاح"
        print(f"      {ollama_status} خادم Ollama المحلي")
        
        # اختبار قاعدة البيانات المتجهة
        chroma_status = "✅ متاح" if ai_tests["chroma_db"].get("available") else "❌ غير متاح"
        print(f"      {chroma_status} قاعدة البيانات المتجهة")
        
        # اختبار مجلدات النماذج
        model_paths = [
            "universal_ai_system/configs",
            "universal_ai_system/src",
            "configs"
        ]
        
        print("   📁 مجلدات النماذج:")
        for path in model_paths:
            model_path = self.base_path / path
            exists = model_path.exists()
            ai_tests["model_directories"][path] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {path}")
        
        # اختبار ملفات التكوين
        config_files = [
            "configs/ai_config.json",
            "universal_ai_system/configs"
        ]
        
        print("   ⚙️ ملفات تكوين الذكاء الاصطناعي:")
        for config_file in config_files:
            config_path = self.base_path / config_file
            exists = config_path.exists()
            ai_tests["ai_configs"][config_file] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {config_file}")
        
        self.test_results["test_categories"]["ai_components"] = ai_tests
    
    def test_workflow_components(self):
        """اختبار مكونات سير العمل"""
        print("🔄 اختبار مكونات سير العمل...")
        
        workflow_tests = {
            "n8n_interface": self._test_service_endpoint("http://localhost:5678", "واجهة n8n"),
            "custom_nodes": {},
            "workflow_files": {},
            "workflow_configs": {}
        }
        
        print("   🔄 خدمات سير العمل:")
        
        # اختبار واجهة n8n
        n8n_status = "✅ متاح" if workflow_tests["n8n_interface"].get("available") else "❌ غير متاح"
        print(f"      {n8n_status} واجهة n8n")
        
        # اختبار العقد المخصصة
        custom_nodes_path = self.base_path / "workflows_and_automation" / "n8n_1" / "nodes"
        if custom_nodes_path.exists():
            node_files = list(custom_nodes_path.glob("*.ts"))
            workflow_tests["custom_nodes"]["total"] = len(node_files)
            workflow_tests["custom_nodes"]["nodes"] = [f.name for f in node_files]
            print(f"      ✅ العقد المخصصة: {len(node_files)} عقدة")
            
            # تفاصيل العقد
            for node_file in node_files:
                node_name = node_file.stem
                print(f"         🔧 {node_name}")
                
        else:
            workflow_tests["custom_nodes"]["total"] = 0
            print("      ❌ العقد المخصصة: غير موجودة")
        
        # اختبار ملفات التكوين
        workflow_config_paths = [
            "workflows_and_automation/docker-compose.yml",
            "workflows_and_automation/configs"
        ]
        
        print("   ⚙️ تكوينات سير العمل:")
        for config_path in workflow_config_paths:
            config_file = self.base_path / config_path
            exists = config_file.exists()
            workflow_tests["workflow_configs"][config_path] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {config_path.split('/')[-1]}")
        
        self.test_results["test_categories"]["workflow_components"] = workflow_tests
    
    def test_database_components(self):
        """اختبار مكونات قواعد البيانات"""
        print("🗄️ اختبار مكونات قواعد البيانات...")
        
        database_tests = {
            "sqlite_databases": {},
            "database_configs": {},
            "database_services": {}
        }
        
        # اختبار قواعد بيانات SQLite
        sqlite_paths = [
            "database/anubis.db",
            "anubis_main_system/database/anubis.db"
        ]
        
        print("   🗄️ قواعد بيانات SQLite:")
        for db_path in sqlite_paths:
            db_file = self.base_path / db_path
            if db_file.exists():
                size_mb = round(db_file.stat().st_size / (1024*1024), 2)
                database_tests["sqlite_databases"][db_path] = {
                    "exists": True,
                    "size_mb": size_mb
                }
                print(f"      ✅ {db_path} ({size_mb} MB)")
            else:
                database_tests["sqlite_databases"][db_path] = {
                    "exists": False,
                    "size_mb": 0
                }
                print(f"      ❌ {db_path}")
        
        # اختبار خدمات قواعد البيانات
        db_services = {
            "postgresql": "http://localhost:5432",
            "redis": "http://localhost:6379",
            "chroma_db": "http://localhost:8000"
        }
        
        print("   🌐 خدمات قواعد البيانات:")
        for service_name, url in db_services.items():
            result = self._test_service_endpoint(url, service_name)
            database_tests["database_services"][service_name] = result
            status = "✅ متاح" if result.get("available") else "❌ غير متاح"
            print(f"      {status} {service_name}")
        
        # اختبار ملفات التكوين
        db_config_files = [
            "configs/database_config.json",
            "database"
        ]
        
        print("   ⚙️ تكوينات قواعد البيانات:")
        for config_file in db_config_files:
            config_path = self.base_path / config_file
            exists = config_path.exists()
            database_tests["database_configs"][config_file] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {config_file}")
        
        self.test_results["test_categories"]["database_components"] = database_tests
    
    def test_security_components(self):
        """اختبار مكونات الأمان"""
        print("🛡️ اختبار مكونات الأمان...")
        
        security_tests = {
            "isolation_configs": {},
            "security_directories": {},
            "environment_files": {},
            "docker_security": {}
        }
        
        # اختبار مجلدات الأمان
        security_dirs = [
            "isolation_configs/security",
            "universal_ai_system/security",
            "workflows_and_automation/security",
            "workspace/security"
        ]
        
        print("   🔒 مجلدات الأمان:")
        for security_dir in security_dirs:
            security_path = self.base_path / security_dir
            exists = security_path.exists()
            files_count = len(list(security_path.glob("*"))) if exists else 0
            security_tests["security_directories"][security_dir] = {
                "exists": exists,
                "files": files_count
            }
            
            status = "✅" if exists else "❌"
            service_name = security_dir.split('/')[0]
            print(f"      {status} {service_name} ({files_count} ملف)")
        
        # اختبار ملفات البيئة
        env_files = [".env", ".env.template"]
        print("   🌍 ملفات البيئة:")
        for env_file in env_files:
            env_path = self.base_path / env_file
            exists = env_path.exists()
            security_tests["environment_files"][env_file] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {env_file}")
        
        # اختبار ملفات Docker
        docker_files = ["docker-compose.yml", "Dockerfile"]
        print("   🐳 ملفات Docker الأمنية:")
        for docker_file in docker_files:
            docker_path = self.base_path / docker_file
            exists = docker_path.exists()
            security_tests["docker_security"][docker_file] = exists
            status = "✅" if exists else "❌"
            print(f"      {status} {docker_file}")
        
        self.test_results["test_categories"]["security_components"] = security_tests
    
    def test_performance(self):
        """اختبار الأداء"""
        print("⚡ اختبار الأداء...")
        
        performance_metrics = {
            "disk_usage": {},
            "response_times": {},
            "file_counts": {},
            "system_info": {}
        }
        
        # مساحة القرص
        try:
            total, used, free = shutil.disk_usage(self.base_path)
            performance_metrics["disk_usage"] = {
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "usage_percent": round((used / total) * 100, 2)
            }
            print(f"   💾 استخدام القرص: {performance_metrics['disk_usage']['usage_percent']}%")
            print(f"      📊 المساحة المتاحة: {performance_metrics['disk_usage']['free_gb']} GB")
        except Exception as e:
            self.logger.error(f"❌ خطأ في قياس استخدام القرص: {e}")
        
        # أوقات الاستجابة
        if self.test_results.get("service_endpoints"):
            print("   ⏱️ أوقات الاستجابة:")
            for service, endpoint_data in self.test_results["service_endpoints"].items():
                if endpoint_data.get("available") and "response_time" in endpoint_data:
                    response_time = endpoint_data["response_time"]
                    performance_metrics["response_times"][service] = response_time
                    print(f"      🔹 {service}: {response_time}s")
        
        # عدد الملفات في المجلدات الرئيسية
        main_dirs = ["scripts", "reports", "logs", "configs"]
        print("   📁 عدد الملفات:")
        for dir_name in main_dirs:
            dir_path = self.base_path / dir_name
            if dir_path.exists():
                file_count = len(list(dir_path.rglob("*")))
                performance_metrics["file_counts"][dir_name] = file_count
                print(f"      📂 {dir_name}: {file_count} ملف")
            else:
                performance_metrics["file_counts"][dir_name] = 0
                print(f"      📂 {dir_name}: غير موجود")
        
        self.test_results["performance_metrics"] = performance_metrics
    
    def evaluate_results(self):
        """تقييم النتائج الإجمالية"""
        print("📊 تقييم النتائج الشاملة...")
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        # تقييم البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        
        # Docker
        if infrastructure.get("docker_availability"):
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("Docker غير متاح")
        total_tests += 1
        
        # Docker Compose
        if infrastructure.get("docker_compose_availability"):
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("Docker Compose غير متاح")
        total_tests += 1
        
        # الإنترنت
        if infrastructure.get("network_connectivity"):
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("الاتصال بالإنترنت غير متاح")
        total_tests += 1
        
        # الهيكل
        file_structure = self.test_results["test_categories"].get("file_structure", {})
        system_dirs = file_structure.get("system_directories", {})
        existing_dirs = sum(1 for exists in system_dirs.values() if exists)
        total_dirs = len(system_dirs)
        
        if existing_dirs >= total_dirs * 0.8:  # 80% من المجلدات موجودة
            passed_tests += 1
        else:
            failed_tests += 1
            missing_dirs = total_dirs - existing_dirs
            self.test_results["failures"].append(f"{missing_dirs} مجلدات مفقودة من أصل {total_dirs}")
        total_tests += 1
        
        # الخدمات الأساسية
        core_services = self.test_results["test_categories"].get("core_services", {})
        available_services = sum(1 for data in core_services.values() if data.get("available"))
        total_services = len(core_services)
        
        if available_services > 0:
            passed_tests += 1
        else:
            failed_tests += 1
            self.test_results["failures"].append("لا توجد خدمات متاحة")
        total_tests += 1
        
        # حساب معدل النجاح
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
        else:
            success_rate = 0
        
        # تحديد الحالة الإجمالية
        if success_rate >= 80:
            self.test_results["overall_status"] = "excellent"
            status_text = "ممتاز"
            emoji = "🟢"
        elif success_rate >= 60:
            self.test_results["overall_status"] = "good"
            status_text = "جيد"
            emoji = "🟡"
        elif success_rate >= 40:
            self.test_results["overall_status"] = "fair"
            status_text = "متوسط"
            emoji = "🟠"
        else:
            self.test_results["overall_status"] = "poor"
            status_text = "يحتاج تحسين"
            emoji = "🔴"
        
        self.test_results["test_summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": round(success_rate, 2),
            "status": status_text,
            "emoji": emoji,
            "available_services": available_services,
            "total_services": total_services,
            "existing_dirs": existing_dirs,
            "total_dirs": total_dirs
        }
        
        # توصيات التحسين
        self._generate_recommendations()
        
        print(f"\n{emoji} النتيجة الإجمالية: {status_text} ({success_rate:.1f}%)")
        print(f"✅ نجح: {passed_tests}/{total_tests} اختبار")
        print(f"🌐 الخدمات المتاحة: {available_services}/{total_services}")
        print(f"📁 المجلدات الموجودة: {existing_dirs}/{total_dirs}")
    
    def _generate_recommendations(self):
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        # توصيات البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        if not infrastructure.get("docker_availability"):
            recommendations.append("🐳 تثبيت Docker لتشغيل الحاويات المعزولة")
        if not infrastructure.get("docker_compose_availability"):
            recommendations.append("🐳 تثبيت Docker Compose لإدارة الخدمات المتعددة")
        if not infrastructure.get("network_connectivity"):
            recommendations.append("🌐 فحص اتصال الإنترنت لتحميل النماذج")
        
        # توصيات الخدمات
        core_services = self.test_results["test_categories"].get("core_services", {})
        unavailable_services = [name for name, data in core_services.items() if not data.get("available")]
        if unavailable_services:
            recommendations.append(f"🚀 تشغيل الخدمات: {', '.join(unavailable_services)}")
        
        # توصيات الهيكل
        file_structure = self.test_results["test_categories"].get("file_structure", {})
        system_dirs = file_structure.get("system_directories", {})
        missing_dirs = [name for name, exists in system_dirs.items() if not exists]
        if missing_dirs:
            recommendations.append(f"📁 إنشاء المجلدات المفقودة: {', '.join(missing_dirs[:3])}")
        
        # توصيات الأداء
        performance = self.test_results.get("performance_metrics", {})
        disk_usage = performance.get("disk_usage", {})
        if disk_usage.get("usage_percent", 0) > 85:
            recommendations.append("💾 تنظيف القرص - الاستخدام أكثر من 85%")
        elif disk_usage.get("free_gb", 0) < 5:
            recommendations.append("💾 تحرير مساحة إضافية - أقل من 5 GB متاحة")
        
        # توصيات عامة
        recommendations.extend([
            "🔧 تشغيل الخدمات الأساسية باستخدام سكريبتات التشغيل",
            "📊 مراجعة سجلات النظام للأخطاء",
            "🔒 تفعيل أنظمة الأمان والعزل",
            "🤖 اختبار نماذج الذكاء الاصطناعي",
            "📚 مراجعة التوثيق والأدلة"
        ])
        
        self.test_results["recommendations"] = recommendations[:10]  # أول 10 توصيات
    
    def save_test_report(self):
        """حفظ تقرير الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_system_test_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ تقرير الاختبار: {filename}")
        return filename
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*70)
        print("🧪 تقرير اختبار نظام أنوبيس الشامل")
        print("="*70)
        
        # الملخص الإجمالي
        summary = self.test_results.get("test_summary", {})
        print(f"\n{summary.get('emoji', '❓')} الحالة الإجمالية: {summary.get('status', 'غير معروف')}")
        print(f"📊 معدل النجاح: {summary.get('success_rate', 0)}%")
        print(f"✅ الاختبارات الناجحة: {summary.get('passed_tests', 0)}/{summary.get('total_tests', 0)}")
        print(f"🌐 الخدمات المتاحة: {summary.get('available_services', 0)}/{summary.get('total_services', 0)}")
        print(f"📁 المجلدات الموجودة: {summary.get('existing_dirs', 0)}/{summary.get('total_dirs', 0)}")
        
        # تفاصيل البنية التحتية
        infrastructure = self.test_results["test_categories"].get("infrastructure", {})
        print(f"\n🏗️ البنية التحتية:")
        print(f"   🐳 Docker: {'✅ متاح' if infrastructure.get('docker_availability') else '❌ غير متاح'}")
        print(f"   🐳 Docker Compose: {'✅ متاح' if infrastructure.get('docker_compose_availability') else '❌ غير متاح'}")
        print(f"   🌐 الإنترنت: {'✅ متصل' if infrastructure.get('network_connectivity') else '❌ غير متصل'}")
        
        disk_space = infrastructure.get("disk_space", {})
        if disk_space:
            print(f"   💾 مساحة القرص: {disk_space.get('free_gb', 0)} GB متاحة ({disk_space.get('usage_percent', 0)}% مستخدم)")
        
        # حالة الخدمات
        core_services = self.test_results["test_categories"].get("core_services", {})
        print(f"\n🏠 الخدمات الأساسية:")
        for service, data in core_services.items():
            status = "✅ متاح" if data.get("available") else "❌ غير متاح"
            response_time = f" ({data.get('response_time', 'N/A')}s)" if data.get("response_time") else ""
            print(f"   🔹 {service}: {status}{response_time}")
        
        # مكونات الذكاء الاصطناعي
        ai_components = self.test_results["test_categories"].get("ai_components", {})
        if ai_components:
            print(f"\n🤖 مكونات الذكاء الاصطناعي:")
            ollama_status = "✅ متاح" if ai_components.get("ollama_server", {}).get("available") else "❌ غير متاح"
            chroma_status = "✅ متاح" if ai_components.get("chroma_db", {}).get("available") else "❌ غير متاح"
            print(f"   🏠 خادم Ollama: {ollama_status}")
            print(f"   🔍 قاعدة البيانات المتجهة: {chroma_status}")
        
        # مكونات سير العمل
        workflow_components = self.test_results["test_categories"].get("workflow_components", {})
        if workflow_components:
            print(f"\n🔄 مكونات سير العمل:")
            n8n_status = "✅ متاح" if workflow_components.get("n8n_interface", {}).get("available") else "❌ غير متاح"
            custom_nodes_count = workflow_components.get("custom_nodes", {}).get("total", 0)
            print(f"   🔄 واجهة n8n: {n8n_status}")
            print(f"   🔧 العقد المخصصة: {custom_nodes_count} عقدة")
        
        # الأداء
        performance = self.test_results.get("performance_metrics", {})
        if performance.get("response_times"):
            print(f"\n⚡ أداء النظام:")
            avg_response_time = sum(performance["response_times"].values()) / len(performance["response_times"])
            print(f"   ⏱️ متوسط وقت الاستجابة: {avg_response_time:.3f}s")
        
        # الإخفاقات
        failures = self.test_results.get("failures", [])
        if failures:
            print(f"\n❌ المشاكل المكتشفة:")
            for i, failure in enumerate(failures, 1):
                print(f"   {i}. {failure}")
        
        # التوصيات
        recommendations = self.test_results.get("recommendations", [])
        if recommendations:
            print(f"\n💡 التوصيات:")
            for i, rec in enumerate(recommendations[:8], 1):
                print(f"   {i}. {rec}")
        
        print("\n" + "="*70)
        print("✅ انتهى تقرير اختبار النظام الشامل")
        print("="*70)

def main():
    """الدالة الرئيسية"""
    tester = AnubisSimpleSystemTester()
    
    print("🧪 مرحباً بك في اختبار نظام أنوبيس الشامل")
    print("سيتم اختبار جميع مكونات النظام والخدمات...")
    print()
    
    # تشغيل الاختبار الشامل
    test_results = tester.run_comprehensive_test()
    
    # طباعة التقرير المفصل
    tester.print_detailed_report()
    
    # حفظ التقرير
    tester.save_test_report()
    
    return test_results

if __name__ == "__main__":
    main()
