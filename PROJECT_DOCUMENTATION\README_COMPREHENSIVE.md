# 🏺 نظام أنوبيس - النظام الشامل للذكاء الاصطناعي والأتمتة
# Anubis System - Universal AI & Automation Platform

<div align="center">

![Anubis Logo](https://img.shields.io/badge/🏺-Anubis%20System-gold?style=for-the-badge)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg?style=for-the-badge&logo=python)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg?style=for-the-badge&logo=fastapi)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg?style=for-the-badge&logo=docker)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](LICENSE)

**نظام متطور ومتكامل للذكاء الاصطناعي مع دعم متعدد المقدمين وأتمتة شاملة**

*An advanced and integrated AI system with multi-provider support and comprehensive automation*

[🚀 البدء السريع](#-البدء-السريع) • [📖 التوثيق](#-التوثيق) • [🤖 الذكاء الاصطناعي](#-خدمات-الذكاء-الاصطناعي) • [🔄 الأتمتة](#-أتمتة-سير-العمل) • [🛡️ الأمان](#-الأمان-والعزل)

</div>

---

## 🌟 **نظرة عامة**

نظام أنوبيس هو منصة شاملة ومتطورة للذكاء الاصطناعي والأتمتة، مصممة لتوفير حلول متكاملة للمؤسسات والمطورين. يجمع النظام بين قوة الذكاء الاصطناعي المتقدم وأتمتة سير العمل المرنة مع أعلى معايير الأمان والعزل.

### 🎯 **الرؤية**
تمكين المؤسسات من الاستفادة الكاملة من تقنيات الذكاء الاصطناعي الحديثة من خلال منصة موحدة وآمنة وقابلة للتوسع.

### 🏆 **المهمة**
توفير نظام ذكي متكامل يدعم جميع احتياجات الذكاء الاصطناعي والأتمتة مع ضمان الأمان والموثوقية والأداء العالي.

---

## ✨ **المميزات الرئيسية**

### 🤖 **خدمات الذكاء الاصطناعي المتعددة**
- **🧠 OpenAI GPT** - نماذج GPT-4, GPT-3.5-turbo مع دعم كامل
- **🌟 Google Gemini** - نماذج Gemini Pro و Ultra للمهام المتقدمة
- **🎭 Anthropic Claude** - نماذج Claude 3 للمحادثات الذكية
- **🏠 Ollama المحلي** - نماذج مفتوحة المصدر محلية (phi3, mistral, llama3)
- **🔄 تبديل ذكي** - تبديل تلقائي بين النماذج حسب المهمة

### 🔄 **أتمتة سير العمل المتقدمة**
- **🎛️ N8N Integration** - منصة أتمتة قوية مع عقد مخصصة
- **🤝 فريق الذكاء الاصطناعي** - تعاون بين نماذج متعددة
- **📋 إدارة المهام** - تنظيم وتتبع المهام المعقدة
- **🔗 ربط الخدمات** - تكامل مع أنظمة خارجية

### 🛡️ **الأمان والعزل المتقدم**
- **🐳 Docker Isolation** - عزل كامل للخدمات في حاويات آمنة
- **🌐 شبكات معزولة** - شبكات منفصلة ومحمية
- **🔐 تشفير متقدم** - تشفير AES-256 للبيانات الحساسة
- **🔑 إدارة الصلاحيات** - نظام صلاحيات متدرج ومرن

### 📊 **المراقبة والتحليل الشامل**
- **📈 Prometheus** - جمع المقاييس والإحصائيات المتقدمة
- **📊 Grafana** - لوحات مراقبة تفاعلية وجذابة
- **🔍 Health Checks** - فحص صحة النظام المستمر
- **📝 سجلات متقدمة** - تسجيل شامل لجميع العمليات

### 🗄️ **إدارة البيانات المتطورة**
- **🔄 قواعد بيانات متعددة** - دعم MySQL, SQLite, PostgreSQL
- **🔗 Connection Pooling** - إدارة ذكية للاتصالات
- **💾 نسخ احتياطية تلقائية** - حماية البيانات المستمرة
- **🔍 البحث الدلالي** - ChromaDB للبحث الذكي

---

## 🏗️ **الهيكل المعماري**

```
🏺 نظام أنوبيس
├── 🏛️ النواة الأساسية (FastAPI Core)
│   ├── 🌐 API Gateway
│   ├── 🔐 Authentication & Authorization
│   └── 📊 Request/Response Management
│
├── 🤖 طبقة الذكاء الاصطناعي
│   ├── 🧠 Model Management
│   ├── 🔄 Load Balancing
│   └── 📈 Performance Optimization
│
├── 🔄 طبقة الأتمتة
│   ├── 🎛️ N8N Workflows
│   ├── 🤝 AI Team Collaboration
│   └── 📋 Task Management
│
├── 🛡️ طبقة الأمان
│   ├── 🐳 Container Isolation
│   ├── 🌐 Network Security
│   └── 🔐 Data Encryption
│
└── 📊 طبقة المراقبة
    ├── 📈 Metrics Collection
    ├── 📊 Visualization
    └── 🚨 Alerting
```

---

## 🚀 **البدء السريع**

### 📋 **المتطلبات الأساسية**

#### **متطلبات النظام:**
- 🐍 **Python 3.8+** (يُفضل 3.11+)
- 💾 **4GB RAM** (الحد الأدنى) / 8GB+ (مُوصى)
- 💿 **10GB مساحة تخزين** (للتثبيت الأساسي)
- 🌐 **اتصال إنترنت** (لتحميل النماذج والتحديثات)

#### **متطلبات اختيارية:**
- 🐳 **Docker & Docker Compose** (للنشر المعزول)
- 🗄️ **MySQL 8.0+** (قاعدة بيانات متقدمة)
- ☁️ **Redis** (للتخزين المؤقت)

### ⚡ **التثبيت السريع**

#### **الطريقة الأولى: الإعداد التلقائي (مُوصى)**

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# 2. تشغيل الإعداد التلقائي
python scripts/setup_environment.py

# 3. تفعيل البيئة الافتراضية
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 4. تشغيل النظام
python main.py
```

#### **الطريقة الثانية: الإعداد اليدوي**

```bash
# 1. إنشاء البيئة الافتراضية
python -m venv .venv

# 2. تفعيل البيئة
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 3. تثبيت المتطلبات
pip install -r requirements.txt

# 4. إعداد متغيرات البيئة
cp .env.example .env
# قم بتحديث القيم في ملف .env

# 5. تشغيل النظام
python main.py
```

### 🐳 **التشغيل مع Docker**

```bash
# تشغيل النظام الكامل
docker-compose up -d

# تشغيل خدمات محددة
docker-compose up -d anubis-core anubis-n8n

# مراقبة السجلات
docker-compose logs -f
```

---

## 🖥️ **واجهات النظام**

### 🌐 **الواجهات الرئيسية**

| الخدمة | الرابط | الوصف | بيانات الدخول |
|--------|--------|--------|----------------|
| 🏠 **النظام الرئيسي** | http://localhost:8000 | الواجهة الرئيسية والـ API | - |
| 🤖 **الذكاء الاصطناعي** | http://localhost:8000/ai | خدمات الذكاء الاصطناعي | - |
| 🔄 **N8N الأتمتة** | http://localhost:5678 | منصة الأتمتة | admin / anubis_n8n_password |
| 📊 **Grafana المراقبة** | http://localhost:3000 | لوحات المراقبة | admin / anubis_grafana_password |
| 📈 **Prometheus** | http://localhost:9090 | مقاييس النظام | - |

### 📱 **واجهة سطر الأوامر الموحدة**

```bash
# عرض المساعدة
python src/cli/anubis_cli.py --help

# تشغيل النظام
python src/cli/anubis_cli.py system start

# عرض حالة النظام
python src/cli/anubis_cli.py system status

# إدارة الذكاء الاصطناعي
python src/cli/anubis_cli.py ai list-models
python src/cli/anubis_cli.py ai generate "مرحبا بك في أنوبيس"

# إدارة Docker
python src/cli/anubis_cli.py docker up
python src/cli/anubis_cli.py docker status
```

---

## 🤖 **خدمات الذكاء الاصطناعي**

### 🧠 **النماذج المدعومة**

#### **🌐 النماذج السحابية:**
- **OpenAI GPT-4** - للمهام المعقدة والتحليل المتقدم
- **OpenAI GPT-3.5-turbo** - للمهام العامة والسريعة
- **Google Gemini Pro** - للمحادثات الذكية والتحليل
- **Anthropic Claude 3** - للكتابة الإبداعية والتحليل

#### **🏠 النماذج المحلية (Ollama):**
- **phi3:mini** - نموذج سريع وخفيف (2.7B parameters)
- **mistral:7b** - نموذج متوازن للمهام العامة
- **llama3:8b** - نموذج قوي للمهام المتقدمة
- **gemma2:2b** - نموذج Google المحلي
- **strikegpt-r1-zero-8b** - نموذج متخصص للبرمجة

### 🤝 **فريق الذكاء الاصطناعي التعاوني**

نظام أنوبيس يتميز بفريق ذكاء اصطناعي تعاوني فريد:

```python
# مثال على استخدام الفريق التعاوني
from anubis_ai_team import AnubisTeamWorkflowManager

manager = AnubisTeamWorkflowManager()

# تحليل مشروع برمجي
result = manager.execute_workflow(
    task_type="project_analysis",
    description="تحليل شامل لمشروع Python",
    team_members=["phi3:mini", "mistral:7b", "llama3:8b"]
)

# كتابة كود متقدم
code_result = manager.execute_workflow(
    task_type="code_development",
    description="إنشاء API REST باستخدام FastAPI",
    lead_model="mistral:7b",
    reviewers=["llama3:8b", "phi3:mini"]
)
```

---

## 🔄 **أتمتة سير العمل**

### 🎛️ **منصة N8N المحسنة**

نظام أنوبيس يتضمن منصة N8N محسنة مع عقد مخصصة:

#### **العقد المخصصة:**
- **🏺 Anubis Agents** - تشغيل وكلاء أنوبيس الذكية
- **🧠 Anubis Gemini** - تكامل مباشر مع Gemini
- **🏠 Anubis Ollama** - تشغيل النماذج المحلية
- **📊 Anubis Analytics** - تحليل البيانات والمقاييس

#### **سير العمل الجاهز:**
- **📋 تحليل المشاريع** - فحص وتحليل المشاريع البرمجية
- **🔍 كشف الأخطاء** - اكتشاف وإصلاح الأخطاء تلقائياً
- **📝 إنتاج المحتوى** - كتابة وتحرير المحتوى
- **🔄 معالجة البيانات** - تنظيف وتحليل البيانات

### 🤖 **إدارة المهام الذكية**

```python
# مثال على إدارة المهام
from anubis_ai_team import TaskManager

task_manager = TaskManager()

# إنشاء مهمة معقدة
task = task_manager.create_task(
    name="تطوير ميزة جديدة",
    description="إضافة نظام إشعارات للتطبيق",
    priority="high",
    assigned_models=["mistral:7b", "phi3:mini"],
    estimated_duration="2 hours"
)

# تتبع التقدم
progress = task_manager.get_progress(task.id)
print(f"التقدم: {progress.percentage}%")
```

---

## 🛡️ **الأمان والعزل**

### 🔒 **مستويات الأمان**

#### **🐳 عزل الحاويات:**
- **عزل كامل** للخدمات في حاويات Docker منفصلة
- **شبكات معزولة** مع قواعد جدار حماية صارمة
- **موارد محدودة** لكل خدمة لمنع استنزاف الموارد

#### **🔐 تشفير البيانات:**
- **تشفير AES-256** للبيانات الحساسة
- **HTTPS/TLS** لجميع الاتصالات الخارجية
- **مفاتيح دوارة** للأمان المتقدم

#### **🔑 إدارة الصلاحيات:**
- **نظام أدوار متدرج** (Admin, User, Viewer)
- **صلاحيات مخصصة** لكل مستخدم
- **مراجعة الأنشطة** وتسجيل جميع العمليات

### 🛡️ **أفضل الممارسات الأمنية**

```bash
# إعداد الأمان المتقدم
python scripts/security/setup_security.py

# فحص الثغرات الأمنية
python scripts/security/security_audit.py

# تحديث المفاتيح الأمنية
python scripts/security/rotate_keys.py
```

---

## 📊 **المراقبة والتحليل**

### 📈 **مقاييس النظام**

#### **🔍 مقاييس الأداء:**
- **استجابة API** - زمن الاستجابة والإنتاجية
- **استخدام الموارد** - CPU, RAM, Storage
- **حالة النماذج** - توفر وأداء نماذج الذكاء الاصطناعي
- **جودة البيانات** - دقة وموثوقية البيانات

#### **📊 لوحات المراقبة:**
- **لوحة النظام العامة** - نظرة شاملة على الحالة
- **لوحة الذكاء الاصطناعي** - أداء النماذج والاستخدام
- **لوحة الأتمتة** - حالة سير العمل والمهام
- **لوحة الأمان** - التهديدات والتنبيهات

### 🚨 **نظام التنبيهات**

```python
# إعداد التنبيهات المخصصة
from anubis_monitoring import AlertManager

alert_manager = AlertManager()

# تنبيه عند ارتفاع استخدام CPU
alert_manager.create_alert(
    name="high_cpu_usage",
    condition="cpu_usage > 80%",
    action="send_email",
    recipients=["<EMAIL>"]
)

# تنبيه عند فشل النماذج
alert_manager.create_alert(
    name="model_failure",
    condition="model_availability < 90%",
    action="restart_service",
    auto_resolve=True
)
```

---

## 🗄️ **إدارة البيانات**

### 💾 **قواعد البيانات المدعومة**

#### **🔄 قواعد البيانات الرئيسية:**
- **SQLite** - قاعدة بيانات محلية سريعة (افتراضي)
- **MySQL 8.0+** - قاعدة بيانات متقدمة للإنتاج
- **PostgreSQL** - قاعدة بيانات قوية للتطبيقات المعقدة

#### **🔍 قواعد البيانات المتخصصة:**
- **ChromaDB** - قاعدة بيانات متجهة للبحث الدلالي
- **Redis** - تخزين مؤقت عالي الأداء
- **InfluxDB** - قاعدة بيانات السلاسل الزمنية (اختياري)

### 💾 **النسخ الاحتياطية التلقائية**

```python
# إعداد النسخ الاحتياطية
from anubis_data import BackupManager

backup_manager = BackupManager()

# نسخة احتياطية يومية
backup_manager.schedule_backup(
    frequency="daily",
    time="02:00",
    retention_days=30,
    compression=True,
    encryption=True
)

# نسخة احتياطية فورية
backup_id = backup_manager.create_backup(
    include_ai_models=True,
    include_user_data=True,
    destination="cloud"
)
```

---

## 🔧 **التطوير والتخصيص**

### 🛠️ **بيئة التطوير**

#### **📦 أدوات التطوير المدمجة:**
- **Jupyter Lab** - بيئة تطوير تفاعلية
- **VS Code Integration** - تكامل مع محرر VS Code
- **API Testing** - أدوات اختبار API متقدمة
- **Code Quality** - فحص جودة الكود تلقائياً

#### **🧪 اختبارات شاملة:**
```bash
# تشغيل جميع الاختبارات
python -m pytest tests/

# اختبارات الوحدة
python -m pytest tests/unit/

# اختبارات التكامل
python -m pytest tests/integration/

# اختبارات الأداء
python scripts/performance/benchmark.py
```

### 🔌 **إضافة نماذج جديدة**

```python
# مثال على إضافة نموذج جديد
from anubis_ai import ModelRegistry

registry = ModelRegistry()

# تسجيل نموذج جديد
registry.register_model(
    name="custom_model",
    provider="huggingface",
    model_id="microsoft/DialoGPT-medium",
    capabilities=["conversation", "text_generation"],
    local=True
)

# استخدام النموذج
response = registry.generate(
    model="custom_model",
    prompt="مرحبا، كيف يمكنني مساعدتك؟",
    max_tokens=100
)
```

---

## 📚 **التوثيق والموارد**

### 📖 **الأدلة المتاحة**

| الدليل | الوصف | الرابط |
|--------|--------|--------|
| 🚀 **دليل البدء السريع** | خطوات التثبيت والتشغيل | [docs/guides/quick_start.md](docs/guides/quick_start.md) |
| 👤 **دليل المستخدم** | شرح شامل للاستخدام | [docs/USER_GUIDE_COMPLETE.md](docs/USER_GUIDE_COMPLETE.md) |
| 🔧 **دليل المطور** | تطوير وتخصيص النظام | [docs/guides/developer_guide.md](docs/guides/developer_guide.md) |
| 🏗️ **الهيكل التقني** | تفاصيل البنية والتصميم | [docs/TECHNICAL_ARCHITECTURE.md](docs/TECHNICAL_ARCHITECTURE.md) |
| 🔄 **خطة التطوير** | الميزات المستقبلية | [DEVELOPMENT_ROADMAP.md](DEVELOPMENT_ROADMAP.md) |

### 🎓 **أمثلة عملية**

```python
# مثال شامل لاستخدام النظام
from anubis_system import AnubisClient

# إنشاء عميل النظام
client = AnubisClient(api_key="your_api_key")

# استخدام الذكاء الاصطناعي
ai_response = client.ai.generate(
    model="gpt-4",
    prompt="اكتب مقال عن الذكاء الاصطناعي",
    language="arabic",
    max_tokens=500
)

# تشغيل سير عمل
workflow_result = client.automation.run_workflow(
    workflow_id="content_generation",
    inputs={"topic": "التكنولوجيا", "length": "medium"}
)

# تحليل البيانات
analysis = client.analytics.analyze_data(
    data_source="user_interactions",
    analysis_type="sentiment",
    time_range="last_week"
)
```

---

## 🤝 **المساهمة والدعم**

### 💡 **كيفية المساهمة**

نرحب بمساهماتكم في تطوير نظام أنوبيس:

1. **🍴 Fork** المشروع
2. **🌿 إنشاء فرع** للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. **💾 Commit** التغييرات (`git commit -m 'Add amazing feature'`)
4. **📤 Push** للفرع (`git push origin feature/amazing-feature`)
5. **🔄 إنشاء Pull Request**

### 🐛 **الإبلاغ عن الأخطاء**

لإبلاغ عن خطأ أو طلب ميزة جديدة:
- استخدم [GitHub Issues](https://github.com/your-username/Universal-AI-Assistants/issues)
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن
- حدد إصدار النظام المستخدم

### 📞 **الدعم التقني**

- **📧 البريد الإلكتروني:** <EMAIL>
- **💬 Discord:** [انضم لخادم Discord](https://discord.gg/anubis-system)
- **📱 Telegram:** [@AnubisSystemSupport](https://t.me/AnubisSystemSupport)

---

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 **شكر وتقدير**

نشكر جميع المساهمين والمطورين الذين ساعدوا في تطوير نظام أنوبيس:

- **فريق FastAPI** - للإطار الرائع
- **فريق N8N** - لمنصة الأتمتة القوية
- **مجتمع Ollama** - للنماذج المحلية
- **جميع المساهمين** - لجهودهم المستمرة

---

<div align="center">

**🏺 نظام أنوبيس - حيث يلتقي الذكاء الاصطناعي بالأتمتة المتقدمة**

*Anubis System - Where AI Meets Advanced Automation*

[![GitHub Stars](https://img.shields.io/github/stars/your-username/Universal-AI-Assistants?style=social)](https://github.com/your-username/Universal-AI-Assistants)
[![GitHub Forks](https://img.shields.io/github/forks/your-username/Universal-AI-Assistants?style=social)](https://github.com/your-username/Universal-AI-Assistants)
[![GitHub Issues](https://img.shields.io/github/issues/your-username/Universal-AI-Assistants)](https://github.com/your-username/Universal-AI-Assistants/issues)

</div>
