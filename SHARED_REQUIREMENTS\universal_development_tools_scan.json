{"scan_date": "2025-07-23T03:04:50.516417", "system_info": {"os": "Windows", "os_version": "10.0.26200", "architecture": ["64bit", "WindowsPE"], "processor": "Intel64 Family 6 Model 165 Stepping 2, GenuineIntel", "python_version": "3.13.5", "machine": "AMD64", "node": "ABO-ELDAHB"}, "development_tools": {}, "package_managers": {"npm": {"found": true, "installations": [{"path": "C:/Program Files/nodejs\\npm.cmd", "directory": "C:/Program Files/nodejs", "executable": "npm.cmd", "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file.\n'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Program Files/nodejs\\node_modules\\corepack\\shims\\npm.cmd", "directory": "C:/Program Files/nodejs\\node_modules\\corepack\\shims", "executable": "npm.cmd", "version": "'node' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Program Files/nodejs\\node_modules\\corepack\\shims\\nodewin\\npm.cmd", "directory": "C:/Program Files/nodejs\\node_modules\\corepack\\shims\\nodewin", "executable": "npm.cmd", "version": "'node' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Program Files/nodejs\\node_modules\\npm\\bin\\npm.cmd", "directory": "C:/Program Files/nodejs\\node_modules\\npm\\bin", "executable": "npm.cmd", "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file.\n'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\npm.cmd", "directory": "C:/Users\\mo_as\\AppData\\Roaming\\npm", "executable": "npm.cmd", "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm.cmd", "directory": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\node_modules\\npm\\bin", "executable": "npm.cmd", "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file.\n'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:\\Program Files\\nodejs\\npm.cmd", "directory": "C:\\Program Files\\nodejs", "executable": "npm.cmd", "found_in_path": true, "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file.\n'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}]}, "yarn": {"found": true, "installations": [{"path": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\yarn.cmd", "directory": "C:/Users\\mo_as\\AppData\\Roaming\\npm", "executable": "yarn.cmd", "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\node_modules\\yarn\\bin\\yarn.cmd", "directory": "C:/Users\\mo_as\\AppData\\Roaming\\npm\\node_modules\\yarn\\bin", "executable": "yarn.cmd", "version": "'node' is not recognized as an internal or external command,\noperable program or batch file."}, {"path": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\yarn.cmd", "directory": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "executable": "yarn.cmd", "found_in_path": true, "version": "'\"node\"' is not recognized as an internal or external command,\noperable program or batch file."}]}, "pip": {"found": true, "installations": [{"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\pip.exe", "directory": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts", "executable": "pip.exe", "found_in_path": true, "version": "pip 25.1.1 from c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Lib\\site-packages\\pip (python 3.13)"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\pip3.exe", "directory": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts", "executable": "pip3.exe", "found_in_path": true, "version": "pip 25.1.1 from c:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Lib\\site-packages\\pip (python 3.13)"}]}, "conda": {"found": false}, "uv": {"found": false}, "composer": {"found": false}}, "programming_languages": {"python": {"found": true, "installations": [{"path": "C:/Users\\mo_as\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "directory": "C:/Users\\mo_as\\AppData\\Local\\Programs\\Python\\Python313", "executable": "python.exe", "version": "Python 3.13.5"}, {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe", "directory": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts", "executable": "python.exe", "found_in_path": true, "version": "Python 3.13.5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\\python3.exe", "directory": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps", "executable": "python3.exe", "found_in_path": true, "version": "Python was not found; run without arguments to install from the Microsoft Store, or disable this shortcut from Settings > Apps > Advanced app settings > App execution aliases."}]}, "node": {"found": false}, "java": {"found": true, "installations": [{"path": "C:/Program Files/Java\\jdk-23\\bin\\java.exe", "directory": "C:/Program Files/Java\\jdk-23\\bin", "executable": "java.exe", "version": ""}, {"path": "C:/Program Files/Java\\jdk-23\\bin\\javac.exe", "directory": "C:/Program Files/Java\\jdk-23\\bin", "executable": "javac.exe", "version": "javac 23.0.2"}, {"path": "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath\\java.exe", "directory": "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath", "executable": "java.exe", "found_in_path": true, "version": ""}, {"path": "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath\\javac.exe", "directory": "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath", "executable": "javac.exe", "found_in_path": true, "version": "javac 23.0.2"}]}, "dotnet": {"found": true, "installations": [{"path": "C:/Program Files/dotnet\\dotnet.exe", "directory": "C:/Program Files/dotnet", "executable": "dotnet.exe", "version": "10.0.100-preview.2.25164.34"}, {"path": "C:\\Program Files\\dotnet\\dotnet.exe", "directory": "C:\\Program Files\\dotnet", "executable": "dotnet.exe", "found_in_path": true, "version": "10.0.100-preview.2.25164.34"}]}, "go": {"found": true, "installations": [{"path": "C:/Program Files/Go\\bin\\go.exe", "directory": "C:/Program Files/Go\\bin", "executable": "go.exe", "version": "go version go1.24.2 windows/amd64"}, {"path": "C:\\Program Files\\Go\\bin\\go.exe", "directory": "C:\\Program Files\\Go\\bin", "executable": "go.exe", "found_in_path": true, "version": "go version go1.24.2 windows/amd64"}]}, "rust": {"found": false}}, "databases": {"mysql": {"found": true, "installations": [{"path": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin\\mysql.exe", "directory": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin", "executable": "mysql.exe", "version": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin\\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)"}, {"path": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin\\mysqld.exe", "directory": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin", "executable": "mysqld.exe", "version": "C:/Program Files/MySQL\\MySQL Server 8.0\\bin\\mysqld.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)"}, {"path": "C:/Program Files/MySQL\\MySQL Workbench 8.0\\mysql.exe", "directory": "C:/Program Files/MySQL\\MySQL Workbench 8.0", "executable": "mysql.exe", "version": "C:/Program Files/MySQL\\MySQL Workbench 8.0\\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)"}, {"path": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe", "directory": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin", "executable": "mysql.exe", "found_in_path": true, "version": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)"}, {"path": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe", "directory": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin", "executable": "mysqld.exe", "found_in_path": true, "version": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe  Ver 8.0.42 for Win64 on x86_64 (MySQL Community Server - GPL)"}]}, "postgresql": {"found": true, "installations": [{"path": "C:/Program Files/PostgreSQL\\17\\bin\\psql.exe", "directory": "C:/Program Files/PostgreSQL\\17\\bin", "executable": "psql.exe", "version": "psql (PostgreSQL) 17.4"}, {"path": "C:/Program Files/PostgreSQL\\17\\pgAdmin 4\\runtime\\psql.exe", "directory": "C:/Program Files/PostgreSQL\\17\\pgAdmin 4\\runtime", "executable": "psql.exe", "version": "psql (PostgreSQL) 17.4"}, {"path": "C:/Program Files/PostgreSQL\\17\\bin\\postgres.exe", "directory": "C:/Program Files/PostgreSQL\\17\\bin", "executable": "postgres.exe", "version": "postgres (PostgreSQL) 17.4"}]}, "sqlite": {"found": false}}, "containerization": {"docker": {"found": true, "installations": [{"path": "C:/Program Files/Docker\\Docker\\resources\\bin\\docker.exe", "directory": "C:/Program Files/Docker\\Docker\\resources\\bin", "executable": "docker.exe", "version": "Docker version 28.3.2, build 578ccf6"}, {"path": "C:\\Program Files\\Docker\\Docker\\resources\\bin\\docker.exe", "directory": "C:\\Program Files\\Docker\\Docker\\resources\\bin", "executable": "docker.exe", "found_in_path": true, "version": "Docker version 28.3.2, build 578ccf6"}]}, "podman": {"found": false}}, "version_control": {"git": {"found": true, "installations": [{"path": "C:/Program Files/Git\\bin\\git.exe", "directory": "C:/Program Files/Git\\bin", "executable": "git.exe", "version": "git version 2.49.0.windows.1"}, {"path": "C:/Program Files/Git\\cmd\\git.exe", "directory": "C:/Program Files/Git\\cmd", "executable": "git.exe", "version": "git version 2.49.0.windows.1"}, {"path": "C:/Program Files/Git\\mingw64\\bin\\git.exe", "directory": "C:/Program Files/Git\\mingw64\\bin", "executable": "git.exe", "version": "git version 2.49.0.windows.1"}, {"path": "C:/Program Files/Git\\mingw64\\libexec\\git-core\\git.exe", "directory": "C:/Program Files/Git\\mingw64\\libexec\\git-core", "executable": "git.exe", "version": "git version 2.49.0.windows.1"}, {"path": "C:\\Program Files\\Git\\cmd\\git.exe", "directory": "C:\\Program Files\\Git\\cmd", "executable": "git.exe", "found_in_path": true, "version": "git version 2.49.0.windows.1"}]}, "svn": {"found": false}}, "editors_ides": {}, "build_tools": {"cmake": {"found": false}, "make": {"found": false}, "gradle": {"found": false}, "maven": {"found": false}}, "statistics": {"total_categories": 6, "tools_found": 11, "tools_not_found": 12, "total_installations": 38, "registry_tools": 57}, "registry_tools": {"Docker Desktop": {"version": "4.43.2", "install_location": "C:\\Program Files\\Docker\\Docker", "registry_key": "<PERSON><PERSON> Des<PERSON>"}, "Git": {"version": "2.49.0", "install_location": "C:\\Program Files\\Git\\", "registry_key": "Git_is1"}, "Microsoft Visual Studio 2010 Tools for Office Runtime (x64)": {"version": "10.0.60922", "install_location": "", "registry_key": "{610487D9-3460-328A-9333-219D43A75CC5}"}, "PostgreSQL 17 ": {"version": "17.4-1", "install_location": "C:\\Program Files\\PostgreSQL\\17", "registry_key": "PostgreSQL 17"}, "Python 3.13.5 Documentation (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{011563DC-4658-4F00-838A-BE003808BD5B}"}, "Microsoft Visual C++ 2010  x64 Redistributable - 10.0.40219": {"version": "10.0.40219", "install_location": "", "registry_key": "{1D8E6291-B0D5-35EC-8441-6616F567A0F7}"}, "Python 3.13.5 pip Bootstrap (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{20342031-463F-47B0-B7D8-56D0BA99FCCC}"}, "GitHub CLI": {"version": "2.69.0", "install_location": "", "registry_key": "{2988DB68-7354-4318-9AB4-002FDCF5F4CE}"}, "MySQL Shell": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Shell 8.0", "registry_key": "{2D35FC76-5688-4C8A-B790-94C3117F7F84}"}, "vs_communityx64msi": {"version": "17.14.36025", "install_location": "", "registry_key": "{3873679C-FA03-4101-97E9-107D67C568B8}"}, "Python 3.13.5 Executables (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{3B9AEBC3-8AFD-40C4-8204-A3CEA6F21058}"}, "Microsoft Visual C++ 2008 Redistributable - x64 9.0.30729.6161": {"version": "9.0.30729.6161", "install_location": "", "registry_key": "{5FCE6D76-F5DC-37AB-B2B8-22AB8CEDB1D4}"}, "Microsoft Visual Studio Installer": {"version": "3.14.2082.42463", "install_location": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\"", "registry_key": "{6F320B93-EE3C-4826-85E0-ADF79F8D4C61}"}, "Microsoft Visual C++ 2008 Redistributable - x64 9.0.30729.17": {"version": "9.0.30729", "install_location": "", "registry_key": "{8220EEFE-38CD-377E-8595-13398D740ACE}"}, "Microsoft Visual Studio Tools for Applications 2019 x64 Hosting Support": {"version": "16.0.31110", "install_location": "", "registry_key": "{8E7A3713-551D-333A-9271-10EF4D77A80F}"}, "Java(TM) SE Development Kit 23.0.2 (64-bit)": {"version": "********", "install_location": "C:\\Program Files\\Java\\jdk-23\\", "registry_key": "{8EFDE921-88A2-5D0A-A920-0AB07B2A3181}"}, "Python 3.13.5 Core Interpreter (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{970C5B01-B0C6-4312-82D8-809E04BACDD5}"}, "MySQL Server 8.0": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Server 8.0\\", "registry_key": "{A12B0204-7560-4821-9D34-A5F170287309}"}, "MySQL Router 8.0": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Router 8.0\\", "registry_key": "{A1C88C50-9EE5-45B8-88A3-ADFFC5EA5367}"}, "Python 3.13.5 Tcl/Tk Support (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{AC3F660A-8ABD-4E2A-9C2E-D9C1F734D7C9}"}, "Microsoft Visual C++ 2005 Redistributable (x64)": {"version": "8.0.61000", "install_location": "", "registry_key": "{ad8a2fa1-06e7-4b0d-927d-6e54b3d31028}"}, "Python 3.13.5 Development Libraries (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{B40BFBCD-3D0F-4608-B49A-3AA1030370A0}"}, "MySQL Workbench 8.0 CE": {"version": "8.0.42", "install_location": "C:\\Program Files\\MySQL\\MySQL Workbench 8.0", "registry_key": "{CB651F1E-4B3C-496C-88DA-54499A6F8C6B}"}, "Python 3.13.5 Standard Library (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{CCD10098-5E72-48F8-834E-AF2364A86653}"}, "Python 3.13.5 Add to Path (64-bit)": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{D556EDC0-3FAA-4124-B6EF-B3F5D1839EB3}"}, "Node.js": {"version": "22.14.0", "install_location": "", "registry_key": "{EA4A8E4A-F5BF-454F-B107-666BE3F30608}"}, "Visual Studio Community 2022": {"version": "17.14.8", "install_location": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community", "registry_key": "4b050061"}, "Visual Studio Build Tools 2022 (2)": {"version": "17.14.8", "install_location": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools", "registry_key": "6e64f870"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.40664": {"version": "12.0.40664.0", "install_location": "Unknown", "registry_key": "{042d26ef-3dbe-4c25-95d3-4c1b11b235a7}"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.30501": {"version": "12.0.30501.0", "install_location": "Unknown", "registry_key": "{050d4fc8-5d48-4b8f-8972-47c82c46020f}"}, "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.44.35211": {"version": "14.44.35211.0", "install_location": "Unknown", "registry_key": "{0b5169e3-39da-4313-808e-1f9c0407f3bf}"}, "MySQL Examples and Samples": {"version": "8.0.42", "install_location": "C:\\Program Files (x86)\\MySQL\\Samples and Examples 8.0", "registry_key": "{1C037A10-844B-462A-8E7A-6FE64A7FAC46}"}, "vs_communitymsires": {"version": "17.14.36015", "install_location": "", "registry_key": "{2817364B-A3DE-472C-BF19-F19B6997061F}"}, "vs_githubprotocolhandlermsi": {"version": "17.14.36015", "install_location": "", "registry_key": "{29297AFE-9D24-4DFE-ACAF-D90090D905CF}"}, "Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030": {"version": "11.0.61030.0", "install_location": "Unknown", "registry_key": "{33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}"}, "MySQL Installer": {"version": "1.6.14.0", "install_location": "", "registry_key": "{3E861290-D963-4AC5-9A0B-93226D47F0F8}"}, "Microsoft Visual Studio Setup WMI Provider": {"version": "3.12.2140.44225", "install_location": "", "registry_key": "{49727420-70BA-4495-9405-31F8D711CB5A}"}, "Universal CRT Redistributable": {"version": "10.1.22621.5040", "install_location": "", "registry_key": "{6B9089CB-51B0-1F55-CED4-B6E0DE9FA6F2}"}, "Windows SDK Redistributables": {"version": "10.1.26100.4188", "install_location": "", "registry_key": "{633414D2-A28B-536C-ECBF-E6CD7820447E}"}, "Python Launcher": {"version": "3.13.5150.0", "install_location": "", "registry_key": "{69012533-AE90-48A1-B7E5-7124E81C4B40}"}, "vs_communitysharedmsi": {"version": "17.14.36025", "install_location": "", "registry_key": "{71498EE6-F94E-4061-9DD5-55925CA8A74F}"}, "WPT Redistributables": {"version": "10.1.26100.3916", "install_location": "", "registry_key": "{7530A8A0-7EE1-AFF7-4BCA-D60EAFEEA63B}"}, "MySQL Documents": {"version": "8.0.42", "install_location": "C:\\Program Files (x86)\\MySQL\\MySQL Documentation 8.0", "registry_key": "{79936855-A2A1-4DD2-9CCC-7CF5962ADDCF}"}, "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.21005": {"version": "12.0.21005.1", "install_location": "Unknown", "registry_key": "{7f51bdb9-ee21-49ee-94d6-90afc321780e}"}, "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.40664": {"version": "12.0.40664.0", "install_location": "Unknown", "registry_key": "{9dff3540-fc85-4ed5-ac84-9e3c7fd8bece}"}, "SDK ARM64 Redistributables": {"version": "10.1.26100.4188", "install_location": "", "registry_key": "{BF29C4FF-249B-980F-0978-3B1B66D54E48}"}, "vcpp_crt.redist.clickonce": {"version": "14.44.35211", "install_location": "", "registry_key": "{C7FC6DE7-5C06-4311-837A-E0D774618D17}"}, "Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030": {"version": "11.0.61030.0", "install_location": "Unknown", "registry_key": "{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}"}, "Microsoft Visual Studio Setup Configuration": {"version": "3.12.2140.44225", "install_location": "", "registry_key": "{D07657AA-968C-4629-BD6C-1B52AF825EA7}"}, "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.44.35211": {"version": "14.44.35211.0", "install_location": "Unknown", "registry_key": "{d8bbe9f9-7c5b-42c6-b715-9ee898a2e515}"}, "Microsoft Visual Studio Tools for Applications 2019 x86 Hosting Support": {"version": "16.0.31110", "install_location": "", "registry_key": "{E7A0CD34-1F9B-3496-ADB3-2F180D302F6A}"}, "Microsoft Visual C++ 2010  x86 Redistributable - 10.0.40219": {"version": "10.0.40219", "install_location": "", "registry_key": "{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}"}, "Microsoft Visual Studio Tools for Applications 2019": {"version": "16.0.31110", "install_location": "Unknown", "registry_key": "{f3fbabb4-bcfb-45eb-8fff-9b784fd68c38}"}, "GitHub": {"version": "1.0", "install_location": "Unknown", "registry_key": "f192d94383e08e536b73cea4723c5d6e"}, "GitHub Desktop": {"version": "3.4.22-beta1", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop", "registry_key": "GitHubDesktop"}, "Python 3.13.5 (64-bit)": {"version": "3.13.5150.0", "install_location": "Unknown", "registry_key": "{2a612b01-6a34-408a-b31b-2fa0f048823f}"}, "Microsoft Visual Studio Code (User)": {"version": "1.102.1", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\", "registry_key": "{771FD6B0-FA20-440A-A002-3B3BAC16DC50}_is1"}}}