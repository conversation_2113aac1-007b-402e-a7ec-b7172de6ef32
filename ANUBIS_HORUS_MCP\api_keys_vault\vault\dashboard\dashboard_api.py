#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 واجهة برمجة التطبيقات للوحة التحكم
Dashboard API Interface
"""

from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import json
from datetime import datetime
from pathlib import Path

app = Flask(__name__)
CORS(app)

@app.route('/')
def dashboard():
    """عرض لوحة التحكم"""
    dashboard_file = Path(__file__).parent / "index.html"
    if dashboard_file.exists():
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            return f.read()
    return "لوحة التحكم غير متاحة"

@app.route('/api/stats')
def get_stats():
    """الحصول على الإحصائيات"""
    stats = {
        "timestamp": datetime.now().isoformat(),
        "total_keys": 726,
        "healthy_keys": 680,
        "warning_keys": 40,
        "critical_keys": 6,
        "platforms": 9,
        "security_score": 95,
        "uptime": "15 days, 8 hours"
    }
    return jsonify(stats)

@app.route('/api/platforms')
def get_platforms():
    """الحصول على بيانات المنصات"""
    platforms = {
        "Generic": {"count": 524, "status": "stable"},
        "Mistral": {"count": 162, "status": "good"},
        "Google Gemini": {"count": 10, "status": "excellent"},
        "OpenRouter": {"count": 11, "status": "good"},
        "DeepSeek": {"count": 6, "status": "good"},
        "GitHub": {"count": 7, "status": "critical"},
        "Anthropic": {"count": 1, "status": "excellent"},
        "Continue": {"count": 2, "status": "good"},
        "Nebius": {"count": 3, "status": "good"}
    }
    return jsonify(platforms)

@app.route('/api/activities')
def get_activities():
    """الحصول على الأنشطة الحديثة"""
    activities = [
        {"time": "10:30", "action": "تم إنشاء نسخة احتياطية", "status": "success"},
        {"time": "09:15", "action": "تم تدوير مفتاح GitHub", "status": "success"},
        {"time": "08:45", "action": "تنبيه: مفتاح OpenAI ينتهي قريباً", "status": "warning"},
        {"time": "07:30", "action": "فحص صحة المفاتيح مكتمل", "status": "success"},
        {"time": "06:00", "action": "تم اكتشاف 3 مفاتيح جديدة", "status": "info"}
    ]
    return jsonify(activities)

@app.route('/api/performance')
def get_performance():
    """الحصول على مؤشرات الأداء"""
    performance = {
        "cpu_usage": 12,
        "memory_usage": 45,
        "disk_usage": 67,
        "network_latency": 15,
        "response_time": 0.2
    }
    return jsonify(performance)

if __name__ == '__main__':
    print("🌐 تشغيل خادم لوحة التحكم...")
    print("📊 الوصول للوحة التحكم: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
