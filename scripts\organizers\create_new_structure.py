#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 إنشاء البنية الجديدة لمشروع أنوبيس
Anubis Project New Structure Creator

تطبيق توصيات Gemini CLI لتبسيط وتنظيم المشروع
"""

import os
import shutil
from pathlib import Path
import json
from datetime import datetime

class AnubisStructureCreator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "archive_and_backups" / f"structure_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # البنية الجديدة المقترحة من Gemini CLI
        self.new_structure = {
            "src": {
                "description": "كود المصدر الرئيسي للمشروع",
                "subdirs": {
                    "core": "نظام FastAPI الأساسي (main.py والملفات المرتبطة به)",
                    "ai_services": "خدمات الذكاء الاصطناعي (OpenAI, Gemini, Claude, Ollama)",
                    "automation": "إدارة n8n وسير العمل",
                    "security": "نظام العزل والأمان (Docker related)",
                    "monitoring": "أدوات المراقبة (Prometheus, Grafana)",
                    "data_management": "إدارة قواعد البيانات والتعامل مع البيانات",
                    "dev_environment": "بيئات التطوير (Jupyter Lab, Streamlit)",
                    "cli": "واجهة سطر الأوامر الموحدة"
                }
            },
            "config": "جميع ملفات الإعدادات",
            "data": "البيانات الخام أو المعالجة بواسطة التطبيق",
            "docs": "توثيق المشروع",
            "tests": "ملفات الاختبار",
            "logs": "سجلات التطبيق",
            "scripts": "نصوص برمجية مساعدة (للتثبيت، التشغيل، إلخ.)"
        }
        
        # خريطة نقل المجلدات القديمة للجديدة
        self.migration_map = {
            "anubis_main_system": "src/core",
            "universal_ai_system": "src/ai_services",
            "workflows_and_automation": "src/automation",
            "anubis_isolation_system": "src/security",
            "tools_and_utilities": "src/dev_environment",
            "configs": "config",
            "documentation": "docs",
            "reports": "docs/reports",
            "database": "data/database",
            "isolation_configs": "config/security",
            "isolation_systems": "src/security/systems"
        }

    def create_backup(self):
        """إنشاء نسخة احتياطية من البنية الحالية"""
        print("📦 إنشاء نسخة احتياطية من البنية الحالية...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # نسخ الملفات الرئيسية
        important_files = [
            "main.py", "requirements.txt", "docker-compose.yml", 
            "Dockerfile", "README.md"
        ]
        
        for file_name in important_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                shutil.copy2(file_path, self.backup_dir / file_name)
        
        # إنشاء تقرير البنية الحالية
        current_structure = self.analyze_current_structure()
        with open(self.backup_dir / "current_structure_report.json", "w", encoding="utf-8") as f:
            json.dump(current_structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء النسخة الاحتياطية في: {self.backup_dir}")

    def analyze_current_structure(self):
        """تحليل البنية الحالية"""
        structure = {}
        
        for item in self.project_root.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                structure[item.name] = {
                    "type": "directory",
                    "files_count": len(list(item.rglob("*"))),
                    "subdirs": [d.name for d in item.iterdir() if d.is_dir()][:10]
                }
            elif item.is_file():
                structure[item.name] = {
                    "type": "file",
                    "size": item.stat().st_size
                }
        
        return structure

    def create_new_structure(self):
        """إنشاء البنية الجديدة"""
        print("🏗️ إنشاء البنية الجديدة...")
        
        # إنشاء المجلدات الرئيسية
        for dir_name, info in self.new_structure.items():
            dir_path = self.project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            
            if isinstance(info, dict) and "subdirs" in info:
                # إنشاء المجلدات الفرعية
                for subdir, description in info["subdirs"].items():
                    subdir_path = dir_path / subdir
                    subdir_path.mkdir(exist_ok=True)
                    
                    # إنشاء ملف README في كل مجلد فرعي
                    readme_path = subdir_path / "README.md"
                    if not readme_path.exists():
                        with open(readme_path, "w", encoding="utf-8") as f:
                            f.write(f"# {subdir}\n\n{description}\n\n")
        
        print("✅ تم إنشاء البنية الجديدة")

    def create_migration_plan(self):
        """إنشاء خطة النقل"""
        migration_plan = {
            "created_at": datetime.now().isoformat(),
            "backup_location": str(self.backup_dir),
            "migrations": []
        }
        
        for old_path, new_path in self.migration_map.items():
            old_dir = self.project_root / old_path
            if old_dir.exists():
                migration_plan["migrations"].append({
                    "from": old_path,
                    "to": new_path,
                    "status": "pending",
                    "files_count": len(list(old_dir.rglob("*"))) if old_dir.is_dir() else 1
                })
        
        # حفظ خطة النقل
        plan_file = self.project_root / "migration_plan.json"
        with open(plan_file, "w", encoding="utf-8") as f:
            json.dump(migration_plan, f, ensure_ascii=False, indent=2)
        
        print(f"📋 تم إنشاء خطة النقل: {plan_file}")
        return migration_plan

    def create_unified_cli(self):
        """إنشاء CLI موحد أساسي"""
        cli_dir = self.project_root / "src" / "cli"
        cli_dir.mkdir(parents=True, exist_ok=True)
        
        cli_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 واجهة سطر الأوامر الموحدة لنظام أنوبيس
Anubis Unified CLI
"""

import click
import subprocess
import sys
from pathlib import Path

@click.group()
@click.version_option(version="1.0.0")
def anubis():
    """🏺 نظام أنوبيس - واجهة سطر الأوامر الموحدة"""
    pass

@anubis.command()
@click.option("--port", default=8000, help="منفذ الخادم")
def start(port):
    """🚀 تشغيل النظام الأساسي"""
    click.echo(f"🏺 بدء تشغيل نظام أنوبيس على المنفذ {port}...")
    subprocess.run([sys.executable, "main.py"], cwd=Path(__file__).parent.parent.parent)

@anubis.command()
def status():
    """📊 عرض حالة النظام"""
    click.echo("🏺 حالة نظام أنوبيس:")
    click.echo("✅ النظام الأساسي: جاهز")
    click.echo("⚠️ الخدمات الإضافية: قيد التطوير")

@anubis.command()
def test():
    """🧪 تشغيل الاختبارات"""
    click.echo("🧪 تشغيل اختبارات النظام...")
    # سيتم إضافة الاختبارات لاحقاً

if __name__ == "__main__":
    anubis()
'''
        
        cli_file = cli_dir / "anubis_cli.py"
        with open(cli_file, "w", encoding="utf-8") as f:
            f.write(cli_content)
        
        print(f"🖥️ تم إنشاء CLI موحد: {cli_file}")

    def run_restructure(self):
        """تشغيل عملية إعادة الهيكلة الكاملة"""
        print("🏺 بدء إعادة هيكلة مشروع أنوبيس...")
        print("=" * 50)
        
        # 1. إنشاء نسخة احتياطية
        self.create_backup()
        
        # 2. إنشاء البنية الجديدة
        self.create_new_structure()
        
        # 3. إنشاء خطة النقل
        migration_plan = self.create_migration_plan()
        
        # 4. إنشاء CLI موحد
        self.create_unified_cli()
        
        print("=" * 50)
        print("✅ تم إنشاء البنية الجديدة بنجاح!")
        print(f"📦 النسخة الاحتياطية: {self.backup_dir}")
        print("📋 الخطوة التالية: تشغيل نقل الملفات باستخدام migration_plan.json")
        
        return migration_plan

if __name__ == "__main__":
    creator = AnubisStructureCreator()
    creator.run_restructure()
