#!/usr/bin/env python3
"""
📊 وكيل تحليل المشاريع الذكي العالمي
Universal Intelligent Project Analyzer Agent

تم تطويره بناءً على توصيات نماذج Ollama لتحليل شامل للمشاريع البرمجية
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import re

# إضافة مجلد core إلى المسار
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent

class ProjectAnalyzerAgent(BaseAgent):
    """وكيل ذكي مسؤول عن التحليل الشامل للمشاريع البرمجية"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "project_analyzer"
    
    def initialize_agent(self):
        """تهيئة وكيل تحليل المشاريع"""
        # إعدادات التحليل من التكوين
        self.deep_analysis = self.config.get('deep_analysis', True)
        self.performance_check = self.config.get('performance_check', True)
        self.security_scan = self.config.get('security_scan', True)
        
        # مقاييس التحليل
        self.analysis_metrics = {
            'project_size': {},
            'complexity_metrics': {},
            'dependency_analysis': {},
            'architecture_analysis': {},
            'performance_metrics': {},
            'security_metrics': {},
            'maintainability_metrics': {}
        }
        
        self.log_action("تم تهيئة وكيل تحليل المشاريع")
    
    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل التحليل الشامل للمشروع"""
        self.log_action("بدء التحليل الشامل للمشروع")
        
        # الحصول على معلومات المشروع
        project_info = self.get_project_info()
        
        # تحليل شامل
        analysis_results = {
            'project_info': project_info,
            'project_size_analysis': self._analyze_project_size(),
            'complexity_analysis': self._analyze_complexity(),
            'dependency_analysis': self._analyze_dependencies(),
            'architecture_analysis': self._analyze_architecture(),
            'performance_analysis': self._analyze_performance(),
            'security_analysis': self._analyze_security(),
            'maintainability_analysis': self._analyze_maintainability(),
            'recommendations': self._generate_recommendations(),
            'summary': {}
        }
        
        # إنشاء الملخص
        analysis_results['summary'] = self._create_summary(analysis_results)
        
        # حفظ التقرير
        self.save_report(analysis_results)
        
        self.log_action("انتهاء التحليل الشامل للمشروع")
        return analysis_results
    
    def _analyze_project_size(self) -> Dict[str, Any]:
        """تحليل حجم المشروع"""
        size_analysis = {
            'total_files': 0,
            'total_lines': 0,
            'code_lines': 0,
            'comment_lines': 0,
            'blank_lines': 0,
            'file_types': {},
            'largest_files': [],
            'directory_structure': {}
        }
        
        # تحليل جميع الملفات
        for file_path in self.project_path.rglob('*'):
            if file_path.is_file():
                size_analysis['total_files'] += 1
                
                # تصنيف حسب نوع الملف
                suffix = file_path.suffix.lower()
                if suffix not in size_analysis['file_types']:
                    size_analysis['file_types'][suffix] = 0
                size_analysis['file_types'][suffix] += 1
                
                # تحليل ملفات Python
                if suffix == '.py':
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        lines = content.split('\n')
                        file_lines = len(lines)
                        size_analysis['total_lines'] += file_lines
                        
                        # تصنيف الأسطر
                        for line in lines:
                            line = line.strip()
                            if not line:
                                size_analysis['blank_lines'] += 1
                            elif line.startswith('#'):
                                size_analysis['comment_lines'] += 1
                            else:
                                size_analysis['code_lines'] += 1
                        
                        # تتبع أكبر الملفات
                        size_analysis['largest_files'].append({
                            'file': str(file_path.relative_to(self.project_path)),
                            'lines': file_lines
                        })
                        
                    except Exception:
                        pass
        
        # ترتيب أكبر الملفات
        size_analysis['largest_files'].sort(key=lambda x: x['lines'], reverse=True)
        size_analysis['largest_files'] = size_analysis['largest_files'][:10]
        
        return size_analysis
    
    def _analyze_complexity(self) -> Dict[str, Any]:
        """تحليل تعقيد المشروع"""
        complexity_analysis = {
            'cyclomatic_complexity': 0,
            'cognitive_complexity': 0,
            'function_complexity': [],
            'class_complexity': [],
            'module_complexity': [],
            'complexity_distribution': {'low': 0, 'medium': 0, 'high': 0}
        }
        
        python_files = list(self.project_path.rglob('*.py'))
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                file_complexity = self._calculate_file_complexity(file_path, content)
                
                complexity_analysis['module_complexity'].append({
                    'file': str(file_path.relative_to(self.project_path)),
                    'complexity': file_complexity
                })
                
                # تصنيف التعقيد
                if file_complexity < 10:
                    complexity_analysis['complexity_distribution']['low'] += 1
                elif file_complexity < 20:
                    complexity_analysis['complexity_distribution']['medium'] += 1
                else:
                    complexity_analysis['complexity_distribution']['high'] += 1
                
            except Exception:
                continue
        
        # حساب متوسط التعقيد
        if complexity_analysis['module_complexity']:
            total_complexity = sum(m['complexity'] for m in complexity_analysis['module_complexity'])
            complexity_analysis['average_complexity'] = total_complexity / len(complexity_analysis['module_complexity'])
        
        return complexity_analysis
    
    def _calculate_file_complexity(self, file_path: Path, content: str) -> int:
        """حساب تعقيد ملف واحد"""
        complexity = 1  # تعقيد أساسي
        
        # عدد الشروط والحلقات
        complexity += content.count('if ')
        complexity += content.count('elif ')
        complexity += content.count('for ')
        complexity += content.count('while ')
        complexity += content.count('try:')
        complexity += content.count('except ')
        complexity += content.count('and ')
        complexity += content.count('or ')
        
        return complexity
    
    def _analyze_dependencies(self) -> Dict[str, Any]:
        """تحليل التبعيات"""
        dependency_analysis = {
            'total_dependencies': 0,
            'external_dependencies': [],
            'internal_dependencies': [],
            'dependency_tree': {},
            'outdated_dependencies': [],
            'security_vulnerabilities': []
        }
        
        # فحص ملف requirements.txt
        req_file = self.project_path / 'requirements.txt'
        if req_file.exists():
            try:
                content = req_file.read_text(encoding='utf-8')
                dependencies = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
                dependency_analysis['external_dependencies'] = dependencies
                dependency_analysis['total_dependencies'] = len(dependencies)
            except Exception:
                pass
        
        # فحص الاستيرادات في ملفات Python
        python_files = list(self.project_path.rglob('*.py'))
        imports = set()
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                # البحث عن عبارات الاستيراد
                import_pattern = r'^(?:from\s+(\S+)\s+import|import\s+(\S+))'
                for line in content.split('\n'):
                    match = re.match(import_pattern, line.strip())
                    if match:
                        module = match.group(1) or match.group(2)
                        if module:
                            imports.add(module.split('.')[0])
            except Exception:
                continue
        
        dependency_analysis['internal_dependencies'] = list(imports)
        
        return dependency_analysis
    
    def _analyze_architecture(self) -> Dict[str, Any]:
        """تحليل هيكل المشروع"""
        architecture_analysis = {
            'project_type': self.detect_project_type(),
            'directory_structure': {},
            'design_patterns': [],
            'architectural_style': 'unknown',
            'modularity_score': 0,
            'coupling_analysis': {},
            'cohesion_analysis': {}
        }
        
        # تحليل هيكل المجلدات
        architecture_analysis['directory_structure'] = self._analyze_directory_structure()
        
        # تحديد النمط المعماري
        architecture_analysis['architectural_style'] = self._detect_architectural_style()
        
        # تحليل الوحدات
        architecture_analysis['modularity_score'] = self._calculate_modularity_score()
        
        return architecture_analysis
    
    def _analyze_directory_structure(self) -> Dict[str, Any]:
        """تحليل هيكل المجلدات"""
        structure = {
            'depth': 0,
            'directories': [],
            'organization_score': 0
        }
        
        # حساب عمق المجلدات
        max_depth = 0
        for path in self.project_path.rglob('*'):
            if path.is_dir():
                depth = len(path.relative_to(self.project_path).parts)
                max_depth = max(max_depth, depth)
                structure['directories'].append(str(path.relative_to(self.project_path)))
        
        structure['depth'] = max_depth
        
        # تقييم التنظيم
        common_dirs = ['src', 'tests', 'docs', 'config', 'data', 'static', 'templates']
        found_dirs = sum(1 for d in common_dirs if any(d in dir_name for dir_name in structure['directories']))
        structure['organization_score'] = (found_dirs / len(common_dirs)) * 100
        
        return structure
    
    def _detect_architectural_style(self) -> str:
        """اكتشاف النمط المعماري"""
        # فحص وجود ملفات معينة
        if (self.project_path / 'models.py').exists() or any(self.project_path.rglob('models.py')):
            return 'MVC'
        elif (self.project_path / 'views.py').exists() or any(self.project_path.rglob('views.py')):
            return 'MVT'
        elif any(self.project_path.rglob('*service*.py')):
            return 'Service-Oriented'
        elif any(self.project_path.rglob('*component*.py')):
            return 'Component-Based'
        else:
            return 'Monolithic'
    
    def _calculate_modularity_score(self) -> float:
        """حساب نقاط الوحدات"""
        python_files = list(self.project_path.rglob('*.py'))
        if not python_files:
            return 0
        
        # حساب متوسط حجم الملفات
        total_lines = 0
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                total_lines += len(content.split('\n'))
            except Exception:
                continue
        
        avg_file_size = total_lines / len(python_files) if python_files else 0
        
        # نقاط أفضل للملفات الأصغر (أكثر وحدوية)
        if avg_file_size < 100:
            return 90
        elif avg_file_size < 200:
            return 70
        elif avg_file_size < 500:
            return 50
        else:
            return 30
    
    def _analyze_performance(self) -> Dict[str, Any]:
        """تحليل الأداء"""
        performance_analysis = {
            'potential_bottlenecks': [],
            'optimization_opportunities': [],
            'resource_usage_patterns': {},
            'scalability_assessment': {}
        }
        
        if not self.performance_check:
            return performance_analysis
        
        python_files = list(self.project_path.rglob('*.py'))
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                
                # البحث عن مشاكل أداء محتملة
                if 'for ' in content and content.count('for ') > 3:
                    performance_analysis['potential_bottlenecks'].append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'issue': 'حلقات متعددة قد تؤثر على الأداء',
                        'severity': 'medium'
                    })
                
                if 'time.sleep' in content:
                    performance_analysis['potential_bottlenecks'].append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'issue': 'استخدام time.sleep قد يؤثر على الاستجابة',
                        'severity': 'low'
                    })
                
            except Exception:
                continue
        
        return performance_analysis
    
    def _analyze_security(self) -> Dict[str, Any]:
        """تحليل الأمان"""
        security_analysis = {
            'security_issues': [],
            'vulnerability_assessment': {},
            'security_score': 0,
            'recommendations': []
        }
        
        if not self.security_scan:
            return security_analysis
        
        python_files = list(self.project_path.rglob('*.py'))
        security_issues = []
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                
                # البحث عن مشاكل أمنية
                if 'eval(' in content:
                    security_issues.append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'issue': 'استخدام eval() خطر أمني',
                        'severity': 'high'
                    })
                
                if 'exec(' in content:
                    security_issues.append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'issue': 'استخدام exec() خطر أمني',
                        'severity': 'high'
                    })
                
                # البحث عن كلمات مرور مكشوفة
                if re.search(r'password\s*=\s*["\'][^"\']+["\']', content, re.IGNORECASE):
                    security_issues.append({
                        'file': str(file_path.relative_to(self.project_path)),
                        'issue': 'كلمة مرور مكشوفة في الكود',
                        'severity': 'high'
                    })
                
            except Exception:
                continue
        
        security_analysis['security_issues'] = security_issues
        
        # حساب نقاط الأمان
        if not security_issues:
            security_analysis['security_score'] = 100
        else:
            high_issues = sum(1 for issue in security_issues if issue['severity'] == 'high')
            medium_issues = sum(1 for issue in security_issues if issue['severity'] == 'medium')
            low_issues = sum(1 for issue in security_issues if issue['severity'] == 'low')
            
            security_analysis['security_score'] = max(0, 100 - (high_issues * 30 + medium_issues * 15 + low_issues * 5))
        
        return security_analysis
    
    def _analyze_maintainability(self) -> Dict[str, Any]:
        """تحليل قابلية الصيانة"""
        maintainability_analysis = {
            'maintainability_index': 0,
            'documentation_coverage': 0,
            'test_coverage_estimate': 0,
            'code_duplication': 0,
            'technical_debt': []
        }
        
        python_files = list(self.project_path.rglob('*.py'))
        if not python_files:
            return maintainability_analysis
        
        total_functions = 0
        documented_functions = 0
        
        for file_path in python_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                
                # حساب التوثيق
                function_count = content.count('def ')
                docstring_count = content.count('"""') + content.count("'''")
                
                total_functions += function_count
                documented_functions += min(function_count, docstring_count // 2)
                
            except Exception:
                continue
        
        # حساب تغطية التوثيق
        if total_functions > 0:
            maintainability_analysis['documentation_coverage'] = (documented_functions / total_functions) * 100
        
        # تقدير تغطية الاختبارات
        test_files = list(self.project_path.rglob('test_*.py')) + list(self.project_path.rglob('*_test.py'))
        if test_files and python_files:
            maintainability_analysis['test_coverage_estimate'] = min(100, (len(test_files) / len(python_files)) * 100)
        
        # حساب مؤشر قابلية الصيانة
        maintainability_analysis['maintainability_index'] = (
            maintainability_analysis['documentation_coverage'] * 0.4 +
            maintainability_analysis['test_coverage_estimate'] * 0.6
        )
        
        return maintainability_analysis
    
    def _generate_recommendations(self) -> List[Dict]:
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        # توصيات عامة
        recommendations.extend([
            {
                'category': 'architecture',
                'priority': 'high',
                'title': 'تحسين الهيكل المعماري',
                'description': 'فصل الاهتمامات وتطبيق أنماط التصميم',
                'action': 'إعادة تنظيم الكود حسب المسؤوليات'
            },
            {
                'category': 'performance',
                'priority': 'medium',
                'title': 'تحسين الأداء',
                'description': 'تحسين الخوارزميات وتقليل التعقيد',
                'action': 'مراجعة الحلقات والاستعلامات'
            },
            {
                'category': 'security',
                'priority': 'high',
                'title': 'تعزيز الأمان',
                'description': 'إضافة طبقات حماية وتشفير البيانات الحساسة',
                'action': 'مراجعة نقاط الضعف الأمنية'
            },
            {
                'category': 'maintainability',
                'priority': 'medium',
                'title': 'تحسين قابلية الصيانة',
                'description': 'إضافة توثيق واختبارات شاملة',
                'action': 'كتابة docstrings واختبارات وحدة'
            }
        ])
        
        return recommendations
    
    def _create_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء ملخص التحليل"""
        size_analysis = analysis_results['project_size_analysis']
        complexity_analysis = analysis_results['complexity_analysis']
        security_analysis = analysis_results['security_analysis']
        maintainability_analysis = analysis_results['maintainability_analysis']
        
        # حساب النقاط الإجمالية
        overall_score = (
            min(100, (size_analysis['total_files'] / 10) * 10) * 0.1 +
            max(0, 100 - complexity_analysis.get('average_complexity', 0) * 5) * 0.3 +
            security_analysis.get('security_score', 0) * 0.3 +
            maintainability_analysis.get('maintainability_index', 0) * 0.3
        )
        
        return {
            'overall_score': round(overall_score, 2),
            'project_size': f"{size_analysis['total_files']} ملف",
            'code_lines': size_analysis['code_lines'],
            'complexity_level': self._get_complexity_level(complexity_analysis.get('average_complexity', 0)),
            'security_status': self._get_security_status(security_analysis.get('security_score', 0)),
            'maintainability_level': self._get_maintainability_level(maintainability_analysis.get('maintainability_index', 0)),
            'recommendations_count': len(analysis_results['recommendations']),
            'analysis_time': datetime.now().isoformat()
        }
    
    def _get_complexity_level(self, complexity: float) -> str:
        """تحديد مستوى التعقيد"""
        if complexity < 10:
            return 'منخفض'
        elif complexity < 20:
            return 'متوسط'
        else:
            return 'عالي'
    
    def _get_security_status(self, score: float) -> str:
        """تحديد حالة الأمان"""
        if score >= 80:
            return 'جيد'
        elif score >= 60:
            return 'متوسط'
        else:
            return 'يحتاج تحسين'
    
    def _get_maintainability_level(self, score: float) -> str:
        """تحديد مستوى قابلية الصيانة"""
        if score >= 80:
            return 'ممتاز'
        elif score >= 60:
            return 'جيد'
        elif score >= 40:
            return 'متوسط'
        else:
            return 'ضعيف'
