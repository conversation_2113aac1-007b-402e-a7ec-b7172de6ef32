-- 🏺 سكريبت تهيئة قاعدة بيانات أنوبيس
-- Anubis Database Initialization Script

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS anubis_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE anubis_system;

-- إنشاء مستخدم أنوبيس
CREATE USER IF NOT EXISTS 'anubis_user'@'%' IDENTIFIED BY '2452329511';
GRANT ALL PRIVILEGES ON anubis_system.* TO 'anubis_user'@'%';
FLUSH PRIVILEGES;

-- ===== جدول المستخدمين =====
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(100),
    last_name VA<PERSON>HA<PERSON>(100),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول جلسات الذكاء الاصطناعي =====
CREATE TABLE IF NOT EXISTS ai_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    prompt TEXT NOT NULL,
    response TEXT,
    tokens_used INT DEFAULT 0,
    cost DECIMAL(10, 6) DEFAULT 0.000000,
    duration_ms INT DEFAULT 0,
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    error_message TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_model (model_name),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول سجلات النظام =====
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    message TEXT NOT NULL,
    component VARCHAR(100),
    user_id INT,
    session_id VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_id VARCHAR(255),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_component (component),
    INDEX idx_user_id (user_id),
    INDEX idx_created (created_at),
    INDEX idx_request_id (request_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إعدادات النظام =====
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول ملفات المستخدمين =====
CREATE TABLE IF NOT EXISTS user_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    file_hash VARCHAR(64),
    is_public BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_filename (filename),
    INDEX idx_hash (file_hash),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إحصائيات الاستخدام =====
CREATE TABLE IF NOT EXISTS usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INT NOT NULL,
    response_time_ms INT,
    request_size BIGINT,
    response_size BIGINT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_status (status_code),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== إدراج بيانات أولية =====

-- إعدادات النظام الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('system_name', 'نظام أنوبيس المحسن', 'string', 'اسم النظام', TRUE),
('system_version', '2.0.0', 'string', 'إصدار النظام', TRUE),
('max_file_size', '104857600', 'integer', 'الحد الأقصى لحجم الملف (100MB)', FALSE),
('session_timeout', '3600', 'integer', 'مهلة انتهاء الجلسة بالثواني', FALSE),
('enable_registration', 'true', 'boolean', 'تمكين التسجيل الجديد', FALSE),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', FALSE),
('default_ai_model', 'gpt-3.5-turbo', 'string', 'نموذج الذكاء الاصطناعي الافتراضي', FALSE),
('max_tokens_per_request', '4000', 'integer', 'الحد الأقصى للرموز في الطلب الواحد', FALSE);

-- مستخدم إداري افتراضي
INSERT INTO users (username, email, password_hash, first_name, last_name, is_admin) VALUES
('admin', '<EMAIL>', SHA2('admin123', 256), 'مدير', 'النظام', TRUE);

-- سجل تهيئة النظام
INSERT INTO system_logs (level, message, component) VALUES
('INFO', 'تم تهيئة قاعدة البيانات بنجاح', 'database_init');

-- إنشاء views مفيدة
CREATE VIEW user_activity_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(DISTINCT a.id) as ai_sessions_count,
    SUM(a.tokens_used) as total_tokens_used,
    COUNT(DISTINCT DATE(a.created_at)) as active_days,
    MAX(a.created_at) as last_ai_session,
    u.last_login,
    u.created_at as user_created_at
FROM users u
LEFT JOIN ai_sessions a ON u.id = a.user_id
GROUP BY u.id, u.username, u.email, u.last_login, u.created_at;

CREATE VIEW daily_usage_stats AS
SELECT 
    DATE(created_at) as usage_date,
    COUNT(*) as total_requests,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(response_time_ms) as avg_response_time,
    SUM(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 ELSE 0 END) as successful_requests,
    SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as failed_requests
FROM usage_stats
GROUP BY DATE(created_at)
ORDER BY usage_date DESC;

-- إنشاء stored procedures مفيدة
DELIMITER //

CREATE PROCEDURE GetUserStats(IN user_id_param INT)
BEGIN
    SELECT 
        u.username,
        u.email,
        u.created_at as user_since,
        COUNT(DISTINCT a.id) as total_ai_sessions,
        SUM(a.tokens_used) as total_tokens,
        AVG(a.tokens_used) as avg_tokens_per_session,
        COUNT(DISTINCT f.id) as total_files,
        SUM(f.file_size) as total_file_size
    FROM users u
    LEFT JOIN ai_sessions a ON u.id = a.user_id
    LEFT JOIN user_files f ON u.id = f.user_id
    WHERE u.id = user_id_param
    GROUP BY u.id, u.username, u.email, u.created_at;
END //

CREATE PROCEDURE CleanupOldLogs(IN days_to_keep INT)
BEGIN
    DELETE FROM system_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SELECT ROW_COUNT() as deleted_logs;
END //

DELIMITER ;

-- إنشاء triggers للتدقيق
DELIMITER //

CREATE TRIGGER user_update_trigger
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //

CREATE TRIGGER ai_session_completion_trigger
    BEFORE UPDATE ON ai_sessions
    FOR EACH ROW
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        SET NEW.completed_at = CURRENT_TIMESTAMP;
    END IF;
END //

DELIMITER ;

-- إنشاء فهارس إضافية للأداء
CREATE INDEX idx_ai_sessions_date_model ON ai_sessions(created_at, model_name);
CREATE INDEX idx_system_logs_date_level ON system_logs(created_at, level);
CREATE INDEX idx_usage_stats_date_endpoint ON usage_stats(created_at, endpoint);

-- تحسين إعدادات الجداول
ALTER TABLE ai_sessions ROW_FORMAT=COMPRESSED;
ALTER TABLE system_logs ROW_FORMAT=COMPRESSED;
ALTER TABLE usage_stats ROW_FORMAT=COMPRESSED;

-- رسالة إتمام التهيئة
SELECT 'تم إنشاء قاعدة بيانات أنوبيس بنجاح!' as message;
