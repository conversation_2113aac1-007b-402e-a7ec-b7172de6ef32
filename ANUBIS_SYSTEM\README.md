# 🏺 نظام أنوبيس - النظام الأساسي للذكاء الاصطناعي
# Anubis System - Core AI System

<div align="center">

![Anubis](https://img.shields.io/badge/🏺-Anubis%20System-blue?style=for-the-badge)
[![Python](https://img.shields.io/badge/Python-3.8+-blue?style=for-the-badge)](https://python.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue?style=for-the-badge)](https://docker.com)

**نظام أنوبيس - حارس الذكاء الاصطناعي ومرشد الأنظمة الذكية**

*Anubis System - Guardian of AI and Guide of Intelligent Systems*

**🏺 بحكمة أنوبيس وحمايته، نبني أنظمة ذكية آمنة وموثوقة**

</div>

---

## 🚀 **البدء السريع**

### ⚡ **تشغيل فوري:**
```bash
# الطريقة الأسرع
python quick_start_anubis.py

# أو التشغيل الكامل
python main.py

# أو باستخدام Docker
docker-compose up -d
```

### 📋 **المتطلبات:**
```bash
# Python 3.8 أو أحدث
python --version

# تثبيت المتطلبات
pip install -r ../requirements.txt

# أو للحد الأدنى
pip install -r ../requirements_minimal.txt
```

---

## 🏗️ **بنية النظام**

### 📁 **المكونات الأساسية:**
```
🏺 ANUBIS_SYSTEM/
├── 🚀 main.py                          # نقطة الدخول الرئيسية
├── ⚡ quick_start_anubis.py             # بدء سريع
├── 💻 src/                             # الكود المصدري
├── ⚙️ config/                          # الإعدادات
├── 📊 data/                            # البيانات
├── 🗄️ database/                        # قواعد البيانات
├── 🐳 docker/                          # ملفات Docker
├── 📝 logs/                            # السجلات
├── 🧪 tests/                           # الاختبارات
├── 🔐 ssl/                             # شهادات الأمان
└── 🏢 workspace/                       # مساحة العمل المعزولة
```

---

## 💻 **المكونات الأساسية (src/)**

### 🤖 **خدمات الذكاء الاصطناعي (ai_services/):**
- **🧠 معالجة اللغة الطبيعية:** فهم وتوليد النصوص
- **🔍 التحليل الذكي:** تحليل البيانات والأنماط
- **🤖 النماذج المتقدمة:** تكامل مع نماذج مختلفة
- **📊 التعلم الآلي:** خوارزميات التعلم والتنبؤ

### 🔄 **الأتمتة (automation/):**
- **⚙️ سير العمل التلقائي:** أتمتة المهام المتكررة
- **📋 إدارة المهام:** تنظيم وتتبع المهام
- **🔄 المعالجة المجمعة:** معالجة البيانات بكميات كبيرة
- **⏰ الجدولة الذكية:** تنفيذ المهام في أوقات محددة

### 💻 **واجهة سطر الأوامر (cli/):**
- **🖥️ أوامر تفاعلية:** تحكم كامل عبر سطر الأوامر
- **📋 إدارة النظام:** مراقبة وإدارة المكونات
- **🔧 أدوات التشخيص:** فحص وإصلاح المشاكل
- **📊 تقارير الحالة:** معلومات مفصلة عن النظام

### 🏗️ **النواة الأساسية (core/):**
- **⚡ محرك التنفيذ:** تشغيل العمليات الأساسية
- **🔗 إدارة الاتصالات:** ربط المكونات المختلفة
- **🛡️ نظام الحماية:** أمان وحماية البيانات
- **📈 مراقبة الأداء:** تتبع وتحسين الأداء

---

## ⚙️ **الإعدادات (config/)**

### 📋 **ملفات الإعدادات الرئيسية:**
```json
{
  "ai_config.json": "إعدادات الذكاء الاصطناعي",
  "database_config.json": "إعدادات قواعد البيانات", 
  "default_config.json": "الإعدادات الافتراضية",
  "langsmith_config.json": "إعدادات LangSmith",
  "security/": "إعدادات الأمان والحماية"
}
```

### 🔧 **تخصيص الإعدادات:**
```python
# تحميل الإعدادات
from src.core.config_manager import ConfigManager
config = ConfigManager()

# تعديل الإعدادات
config.update_ai_settings({
    "model": "gpt-4",
    "temperature": 0.7,
    "max_tokens": 2000
})

# حفظ التغييرات
config.save()
```

---

## 🗄️ **قواعد البيانات**

### 📊 **أنواع البيانات المدعومة:**
- **🗄️ SQLite:** قاعدة بيانات محلية سريعة
- **🐬 MySQL:** قاعدة بيانات متقدمة للإنتاج
- **📄 JSON:** تخزين البيانات المرنة
- **📝 ملفات نصية:** السجلات والتقارير

### 🔧 **إدارة قواعد البيانات:**
```python
# الاتصال بقاعدة البيانات
from src.data_management.database_manager import DatabaseManager
db = DatabaseManager()

# تنفيذ استعلام
results = db.query("SELECT * FROM users")

# إدراج بيانات جديدة
db.insert("users", {"name": "أحمد", "role": "admin"})
```

---

## 🐳 **النشر باستخدام Docker**

### 🚀 **التشغيل السريع:**
```bash
# تشغيل النظام الكامل
docker-compose up -d

# تشغيل مكونات محددة
docker-compose -f docker/docker-compose-main.yml up -d

# تشغيل النسخة المحسنة
docker-compose -f docker/docker-compose-enhanced.yml up -d
```

### 🔧 **إعدادات Docker المتقدمة:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  anubis-core:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ANUBIS_ENV=production
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
```

---

## 🏢 **مساحة العمل المعزولة (workspace/)**

### 🔒 **بيئة آمنة ومعزولة:**
```bash
# بدء مساحة العمل المعزولة
./workspace/start_isolated_workspace.sh

# مراقبة المساحة
docker-compose -f workspace/docker-compose.yml logs -f

# إيقاف المساحة
./workspace/stop_isolated_workspace.sh
```

### 🛡️ **ميزات الأمان:**
- **🔒 عزل كامل:** منع التداخل مع النظام الرئيسي
- **📊 مراقبة مستمرة:** تتبع جميع العمليات
- **🔐 تشفير البيانات:** حماية المعلومات الحساسة
- **📋 سجلات مفصلة:** تتبع كامل للأنشطة

---

## 🧪 **الاختبارات والجودة**

### ✅ **تشغيل الاختبارات:**
```bash
# اختبارات النظام الأساسي
python -m pytest tests/test_core_system.py

# اختبارات النظام المحسن
python -m pytest tests/test_enhanced_system.py

# جميع الاختبارات
python -m pytest tests/ -v
```

### 📊 **فحص جودة الكود:**
```bash
# فحص بـ pylint
pylint src/

# فحص بـ flake8
flake8 src/

# فحص الأمان
bandit -r src/
```

---

## 📊 **المراقبة والسجلات**

### 📝 **أنواع السجلات:**
```
📝 logs/
├── ⚙️ operations/          # سجلات العمليات العامة
├── 🔒 isolation/           # سجلات البيئة المعزولة
├── 🗄️ mysql/               # سجلات قاعدة البيانات
└── 🌐 nginx/               # سجلات خادم الويب
```

### 📊 **مراقبة الأداء:**
```python
# مراقبة استخدام الموارد
from src.monitoring.performance_monitor import PerformanceMonitor
monitor = PerformanceMonitor()

# الحصول على إحصائيات
stats = monitor.get_system_stats()
print(f"استخدام المعالج: {stats['cpu_usage']}%")
print(f"استخدام الذاكرة: {stats['memory_usage']}%")
```

---

## 🔐 **الأمان والحماية**

### 🛡️ **ميزات الأمان:**
- **🔐 تشفير البيانات:** حماية المعلومات الحساسة
- **🔑 إدارة المفاتيح:** نظام آمن للمفاتيح والرموز
- **🚫 التحكم في الوصول:** صلاحيات محددة للمستخدمين
- **📋 تدقيق الأنشطة:** تتبع جميع العمليات

### 🔧 **إعداد الأمان:**
```python
# تفعيل الحماية المتقدمة
from src.security.security_manager import SecurityManager
security = SecurityManager()

# تشفير البيانات
encrypted_data = security.encrypt("بيانات حساسة")

# التحقق من الصلاحيات
if security.check_permission(user, "admin"):
    # تنفيذ العملية المحمية
    pass
```

---

## 🚀 **أمثلة الاستخدام**

### 🤖 **استخدام خدمات الذكاء الاصطناعي:**
```python
from src.ai_services.nlp_processor import NLPProcessor

# معالجة النصوص
nlp = NLPProcessor()
result = nlp.analyze_text("مرحباً، كيف يمكنني مساعدتك؟")
print(f"المشاعر: {result['sentiment']}")
print(f"الموضوع: {result['topic']}")
```

### 🔄 **أتمتة المهام:**
```python
from src.automation.task_automator import TaskAutomator

# إنشاء مهمة تلقائية
automator = TaskAutomator()
task = automator.create_task(
    name="نسخ احتياطي يومي",
    schedule="0 2 * * *",  # كل يوم في الساعة 2 صباحاً
    action="backup_database"
)
automator.start_task(task)
```

### 📊 **تحليل البيانات:**
```python
from src.data_management.data_analyzer import DataAnalyzer

# تحليل البيانات
analyzer = DataAnalyzer()
insights = analyzer.analyze_dataset("user_behavior.csv")
print(f"الاتجاهات: {insights['trends']}")
print(f"التوصيات: {insights['recommendations']}")
```

---

## 🔧 **استكشاف الأخطاء**

### ❓ **مشاكل شائعة:**

#### **"خطأ في الاتصال بقاعدة البيانات"**
```bash
# فحص حالة قاعدة البيانات
python -c "from src.data_management.database_manager import DatabaseManager; DatabaseManager().test_connection()"

# إعادة تهيئة قاعدة البيانات
python src/data_management/init_database.py
```

#### **"خطأ في تحميل النموذج"**
```bash
# فحص إعدادات الذكاء الاصطناعي
cat config/ai_config.json

# تحديث النماذج
python src/ai_services/model_updater.py
```

### 💡 **نصائح للاستخدام الأمثل:**
1. **📊 راقب الأداء:** استخدم أدوات المراقبة المدمجة
2. **🔐 حافظ على الأمان:** حدث كلمات المرور بانتظام
3. **📝 اقرأ السجلات:** تابع ملفات السجلات للمشاكل
4. **🧪 اختبر التغييرات:** استخدم البيئة المعزولة للاختبار

---

## 📚 **الموارد الإضافية**

### 📖 **التوثيق:**
- `../docs/` - التوثيق الشامل للمشروع
- `../reports_and_analysis/` - التقارير والتحليلات
- `../DEVELOPMENT_ROADMAP.md` - خريطة طريق التطوير

### 🔗 **ملفات مهمة:**
- `../requirements.txt` - متطلبات المشروع الكاملة
- `../scripts/` - سكريبتات مساعدة للإعداد والصيانة
- `../utilities/` - أدوات مساعدة متنوعة

---

<div align="center">

**🏺 نظام أنوبيس - حارس الذكاء الاصطناعي الموثوق**

*Anubis System - Trusted Guardian of Artificial Intelligence*

[![Start](https://img.shields.io/badge/🚀-Start%20Now-success?style=for-the-badge)](main.py)
[![Docker](https://img.shields.io/badge/🐳-Docker%20Ready-blue?style=for-the-badge)](docker-compose.yml)
[![Secure](https://img.shields.io/badge/🔐-Secure-red?style=for-the-badge)](ssl/)

**🏺 بحكمة أنوبيس وحمايته، ابدأ رحلتك في عالم الذكاء الاصطناعي الآمن**

```bash
python quick_start_anubis.py
```

</div>
