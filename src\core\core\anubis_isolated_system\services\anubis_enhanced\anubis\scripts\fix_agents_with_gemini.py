#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 إصلاح الوكلاء الأربعة مع مساعدة Gemini
Fix Four Agents with Gemini Assistance

إصلاح شامل للوكلاء مع دعم التطوير الكامل للمشاريع
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))


class GeminiAssistedAgentFixer:
    """مصلح الوكلاء بمساعدة Gemini"""

    def __init__(self):
        self.agents_to_fix = [
            "ErrorDetectorAgent",
            "ProjectAnalyzerAgent",
            "FileOrganizerAgent",
            "MemoryAgent",
        ]

        self.fullstack_capabilities = {
            "backend": ["FastAPI", "Django", "Flask", "Express.js", "Spring Boot"],
            "frontend": ["React", "Vue.js", "Angular", "Svelte", "Next.js"],
            "databases": ["PostgreSQL", "MongoDB", "Redis", "MySQL", "SQLite"],
            "devops": ["Docker", "Kubernetes", "CI/CD", "AWS", "Azure"],
            "ai_integration": ["Ollama", "OpenAI", "Gemini", "LangChain", "LangSmith"],
        }

        print("🔧 مصلح الوكلاء بمساعدة Gemini - جاهز للعمل")

    def get_gemini_advice(self, agent_name: str, issue: str) -> str:
        """محاكاة نصائح Gemini لإصلاح الوكلاء"""

        gemini_responses = {
            "ErrorDetectorAgent": """
            🔍 تحليل Gemini لـ ErrorDetectorAgent:

            المشاكل المكتشفة:
            1. عدم وجود دعم للذكاء الاصطناعي
            2. محدودية في أنواع الأخطاء المكتشفة
            3. عدم دعم لغات البرمجة المتعددة

            الحلول المقترحة:
            1. دمج AI لكشف أخطاء معقدة
            2. إضافة دعم للباك إند والفرونت إند
            3. تحليل أخطاء الأمان والأداء
            4. دعم أطر العمل المختلفة

            الميزات الجديدة:
            - كشف أخطاء React/Vue.js
            - تحليل أخطاء APIs
            - فحص أمان قواعد البيانات
            - اقتراحات إصلاح ذكية
            """,
            "ProjectAnalyzerAgent": """
            📊 تحليل Gemini لـ ProjectAnalyzerAgent:

            المشاكل المكتشفة:
            1. تحليل سطحي للمشاريع
            2. عدم فهم هياكل المشاريع الحديثة
            3. محدودية في التوصيات

            الحلول المقترحة:
            1. تحليل ذكي لهياكل المشاريع
            2. فهم أطر العمل المختلفة
            3. تحليل الأداء والأمان
            4. توصيات تطوير شاملة

            الميزات الجديدة:
            - تحليل مشاريع React/Next.js
            - فهم هياكل Django/FastAPI
            - تحليل قواعد البيانات
            - اقتراحات تحسين الأداء
            """,
            "FileOrganizerAgent": """
            📁 تحليل Gemini لـ FileOrganizerAgent:

            المشاكل المكتشفة:
            1. تنظيم بسيط للملفات
            2. عدم فهم هياكل المشاريع المعقدة
            3. محدودية في أنواع الملفات

            الحلول المقترحة:
            1. تنظيم ذكي حسب نوع المشروع
            2. فهم هياكل الباك إند والفرونت إند
            3. تنظيم ملفات التكوين والاختبارات
            4. إنشاء هياكل مشاريع جديدة

            الميزات الجديدة:
            - إنشاء مشاريع React/Vue.js
            - تنظيم مشاريع Django/FastAPI
            - إدارة ملفات Docker
            - تنظيم ملفات CI/CD
            """,
            "MemoryAgent": """
            🧠 تحليل Gemini لـ MemoryAgent:

            المشاكل المكتشفة:
            1. دوال مفقودة (store_memory, retrieve_memory)
            2. عدم دعم أنواع البيانات المختلفة
            3. محدودية في البحث والاسترجاع

            الحلول المقترحة:
            1. إضافة دوال التخزين والاسترجاع
            2. دعم قواعد البيانات المختلفة
            3. بحث ذكي في الذاكرة
            4. تخزين سياق المشاريع

            الميزات الجديدة:
            - تخزين معلومات المشاريع
            - ذاكرة للكود والتوثيق
            - تخزين تفضيلات المطور
            - استرجاع ذكي للمعلومات
            """,
        }

        return gemini_responses.get(agent_name, "لا توجد نصائح متاحة لهذا الوكيل")

    def fix_error_detector_agent(self):
        """إصلاح وكيل كشف الأخطاء"""
        print("\n🔍 إصلاح ErrorDetectorAgent...")

        # الحصول على نصائح Gemini
        advice = self.get_gemini_advice("ErrorDetectorAgent", "missing AI support")
        print(f"💎 نصائح Gemini:\n{advice}")

        # إنشاء الوكيل المحسن
        agent_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 وكيل كشف الأخطاء المحسن بالذكاء الاصطناعي
Enhanced AI-Powered Error Detector Agent

وكيل ذكي لكشف الأخطاء في جميع أنواع المشاريع البرمجية
"""

import os
import sys
import re
import ast
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# إضافة مسار core
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from base_agent import BaseAgent
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from anubis.core.base_agent import BaseAgent

class ErrorDetectorAgent(BaseAgent):
    """🔍 وكيل كشف الأخطاء المحسن"""

    def get_agent_type(self) -> str:
        return "error_detector"

    def initialize_agent(self):
        """تهيئة وكيل كشف الأخطاء"""
        self.supported_languages = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React JSX',
            '.tsx': 'React TSX',
            '.vue': 'Vue.js',
            '.php': 'PHP',
            '.java': 'Java',
            '.cpp': 'C++',
            '.cs': 'C#',
            '.go': 'Go',
            '.rs': 'Rust'
        }

        self.error_patterns = {
            'python': [
                (r'print\s*\([^)]*\)', 'استخدام print في الكود الإنتاجي'),
                (r'eval\s*\(', 'استخدام eval - خطر أمني'),
                (r'exec\s*\(', 'استخدام exec - خطر أمني'),
                (r'import\s+\*', 'استيراد جميع الوحدات - غير مستحسن'),
                (r'except\s*:', 'التعامل مع الاستثناءات بشكل عام'),
            ],
            'javascript': [
                (r'var\s+', 'استخدام var بدلاً من let/const'),
                (r'eval\s*\(', 'استخدام eval - خطر أمني'),
                (r'innerHTML\s*=', 'استخدام innerHTML - خطر XSS محتمل'),
                (r'document\.write', 'استخدام document.write - غير مستحسن'),
                (r'==\s*[^=]', 'استخدام == بدلاً من ==='),
            ],
            'react': [
                (r'dangerouslySetInnerHTML', 'استخدام dangerouslySetInnerHTML - خطر XSS'),
                (r'useEffect\s*\(\s*[^,]+\s*\)', 'useEffect بدون dependencies'),
                (r'setState\s*\([^)]*\)\s*;?\s*setState', 'استدعاءات setState متتالية'),
            ]
        }

        self.framework_patterns = {
            'django': [
                (r'User\.objects\.get\(', 'استخدام get() بدون try/except'),
                (r'raw\s*\(', 'استخدام raw SQL - خطر SQL injection'),
                (r'mark_safe\s*\(', 'استخدام mark_safe - خطر XSS'),
            ],
            'fastapi': [
                (r'@app\.get\s*\([^)]*\)\s*def\s+\w+\s*\([^)]*\):', 'endpoint بدون type hints'),
                (r'request\.json\(\)', 'استخدام request.json() بدون validation'),
            ],
            'express': [
                (r'app\.get\s*\([^,]*,\s*\([^)]*\)\s*=>', 'route handler بدون error handling'),
                (r'req\.query\.\w+', 'استخدام query parameters بدون validation'),
            ]
        }

        self.detected_errors = []
        self.log_action("تهيئة وكيل كشف الأخطاء المحسن", "تم تفعيل جميع أنماط الكشف")

    def detect_errors(self, file_path: str = None, code_content: str = None) -> List[Dict[str, Any]]:
        """كشف الأخطاء في الملف أو الكود"""
        if file_path:
            return self.detect_errors_in_file(file_path)
        elif code_content:
            return self.detect_errors_in_code(code_content)
        else:
            return self.detect_errors_in_project()

    def detect_errors_in_file(self, file_path: str) -> List[Dict[str, Any]]:
        """كشف الأخطاء في ملف محدد"""
        file_path = Path(file_path)

        if not file_path.exists():
            return [{'error': f'الملف غير موجود: {file_path}'}]

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # تحديد نوع الملف
            file_ext = file_path.suffix.lower()
            language = self.supported_languages.get(file_ext, 'unknown')

            # كشف الأخطاء التقليدي
            traditional_errors = self._detect_traditional_errors(content, language, str(file_path))

            # كشف الأخطاء بالذكاء الاصطناعي
            ai_errors = []
            if self.is_ai_enabled():
                ai_errors = self._detect_ai_errors(content, language, str(file_path))

            # دمج النتائج
            all_errors = traditional_errors + ai_errors

            return {
                'file_path': str(file_path),
                'language': language,
                'total_errors': len(all_errors),
                'traditional_errors': traditional_errors,
                'ai_errors': ai_errors,
                'all_errors': all_errors
            }

        except Exception as e:
            return [{'error': f'خطأ في قراءة الملف: {e}'}]

    def detect_errors_in_code(self, code_content: str, language: str = 'python') -> List[Dict[str, Any]]:
        """كشف الأخطاء في كود محدد"""
        # كشف الأخطاء التقليدي
        traditional_errors = self._detect_traditional_errors(code_content, language)

        # كشف الأخطاء بالذكاء الاصطناعي
        ai_errors = []
        if self.is_ai_enabled():
            ai_errors = self._detect_ai_errors(code_content, language)

        return {
            'language': language,
            'total_errors': len(traditional_errors) + len(ai_errors),
            'traditional_errors': traditional_errors,
            'ai_errors': ai_errors
        }

    def detect_errors_in_project(self) -> Dict[str, Any]:
        """كشف الأخطاء في المشروع كاملاً"""
        project_errors = {
            'total_files_scanned': 0,
            'files_with_errors': 0,
            'total_errors': 0,
            'errors_by_file': {},
            'errors_by_type': {},
            'summary': {}
        }

        # فحص جميع الملفات المدعومة
        for ext in self.supported_languages.keys():
            files = list(self.project_path.rglob(f'*{ext}'))

            for file_path in files:
                project_errors['total_files_scanned'] += 1

                file_errors = self.detect_errors_in_file(str(file_path))

                if file_errors.get('total_errors', 0) > 0:
                    project_errors['files_with_errors'] += 1
                    project_errors['total_errors'] += file_errors['total_errors']
                    project_errors['errors_by_file'][str(file_path)] = file_errors

        # إنشاء ملخص
        project_errors['summary'] = {
            'error_rate': round(project_errors['files_with_errors'] / max(project_errors['total_files_scanned'], 1) * 100, 2),
            'avg_errors_per_file': round(project_errors['total_errors'] / max(project_errors['files_with_errors'], 1), 2),
            'most_common_language': self._get_most_common_language(),
            'recommendations': self._generate_recommendations(project_errors)
        }

        return project_errors

    def _detect_traditional_errors(self, content: str, language: str, file_path: str = None) -> List[Dict[str, Any]]:
        """كشف الأخطاء بالطرق التقليدية"""
        errors = []
        lines = content.split('\\n')

        # اختيار الأنماط المناسبة
        patterns = []
        lang_key = language.lower()

        if lang_key in self.error_patterns:
            patterns.extend(self.error_patterns[lang_key])

        # كشف أنماط الأطر
        if 'django' in content.lower():
            patterns.extend(self.framework_patterns.get('django', []))
        elif 'fastapi' in content.lower():
            patterns.extend(self.framework_patterns.get('fastapi', []))
        elif 'express' in content.lower():
            patterns.extend(self.framework_patterns.get('express', []))

        # فحص كل سطر
        for line_num, line in enumerate(lines, 1):
            for pattern, description in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    errors.append({
                        'type': 'pattern_match',
                        'line': line_num,
                        'code': line.strip(),
                        'description': description,
                        'severity': self._get_severity(description),
                        'file_path': file_path
                    })

        # فحص syntax للـ Python
        if language.lower() == 'python':
            syntax_errors = self._check_python_syntax(content)
            errors.extend(syntax_errors)

        return errors

    def _detect_ai_errors(self, content: str, language: str, file_path: str = None) -> List[Dict[str, Any]]:
        """كشف الأخطاء بالذكاء الاصطناعي"""
        if not self.is_ai_enabled():
            return []

        prompt = f"""
        حلل هذا الكود بلغة {language} واكتشف الأخطاء والمشاكل:

        ```{language.lower()}
        {content[:2000]}  # أول 2000 حرف
        ```

        ابحث عن:
        1. أخطاء منطقية
        2. مشاكل الأمان
        3. مشاكل الأداء
        4. انتهاكات أفضل الممارسات
        5. أخطاء محتملة في وقت التشغيل

        قدم النتائج في شكل قائمة واضحة.
        """

        try:
            ai_response = self.get_ai_analysis(prompt, {
                'language': language,
                'file_path': file_path,
                'analysis_type': 'error_detection'
            })

            # تحليل استجابة الذكاء الاصطناعي
            ai_errors = self._parse_ai_response(ai_response, file_path)

            return ai_errors

        except Exception as e:
            return [{'type': 'ai_error', 'description': f'خطأ في التحليل الذكي: {e}'}]

    def _parse_ai_response(self, response: str, file_path: str = None) -> List[Dict[str, Any]]:
        """تحليل استجابة الذكاء الاصطناعي"""
        errors = []
        lines = response.split('\\n')

        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                error_text = line.lstrip('-•* ').strip()
                if len(error_text) > 10:  # تجاهل الأسطر القصيرة
                    errors.append({
                        'type': 'ai_detected',
                        'description': error_text,
                        'severity': 'medium',
                        'source': 'ai_analysis',
                        'file_path': file_path
                    })

        return errors[:10]  # أقصى 10 أخطاء من الذكاء الاصطناعي

    def _check_python_syntax(self, content: str) -> List[Dict[str, Any]]:
        """فحص syntax للـ Python"""
        errors = []

        try:
            ast.parse(content)
        except SyntaxError as e:
            errors.append({
                'type': 'syntax_error',
                'line': e.lineno,
                'description': f'خطأ في البناء: {e.msg}',
                'severity': 'high',
                'code': e.text.strip() if e.text else ''
            })
        except Exception as e:
            errors.append({
                'type': 'parse_error',
                'description': f'خطأ في تحليل الكود: {e}',
                'severity': 'medium'
            })

        return errors

    def _get_severity(self, description: str) -> str:
        """تحديد شدة الخطأ"""
        high_severity_keywords = ['أمني', 'security', 'خطر', 'danger', 'eval', 'exec']
        medium_severity_keywords = ['أداء', 'performance', 'مستحسن', 'recommended']

        description_lower = description.lower()

        if any(keyword in description_lower for keyword in high_severity_keywords):
            return 'high'
        elif any(keyword in description_lower for keyword in medium_severity_keywords):
            return 'medium'
        else:
            return 'low'

    def _get_most_common_language(self) -> str:
        """الحصول على أكثر اللغات استخداماً في المشروع"""
        language_count = {}

        for ext, lang in self.supported_languages.items():
            files = list(self.project_path.rglob(f'*{ext}'))
            language_count[lang] = len(files)

        if language_count:
            return max(language_count, key=language_count.get)
        else:
            return 'Unknown'

    def _generate_recommendations(self, project_errors: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات للتحسين"""
        recommendations = []

        if project_errors['total_errors'] > 0:
            recommendations.append(f"تم العثور على {project_errors['total_errors']} خطأ - يُنصح بإصلاحها")

        if project_errors['summary']['error_rate'] > 50:
            recommendations.append("معدل الأخطاء عالي - راجع جودة الكود")

        if self.is_ai_enabled():
            recommendations.append("استخدم التحليل الذكي للحصول على اقتراحات متقدمة")

        return recommendations

    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        return {
            'agent_type': self.agent_type,
            'supported_languages': list(self.supported_languages.values()),
            'total_patterns': sum(len(patterns) for patterns in self.error_patterns.values()),
            'ai_enabled': self.is_ai_enabled(),
            'last_scan': datetime.now().isoformat()
        }
'''

        # حفظ الوكيل المحسن
        agent_file = Path("agents/error_detector.py")
        agent_file.parent.mkdir(exist_ok=True)

        with open(agent_file, "w", encoding="utf-8") as f:
            f.write(agent_code)

        print(f"✅ تم إنشاء ErrorDetectorAgent المحسن في: {agent_file}")
        return str(agent_file)

    def fix_all_agents(self):
        """إصلاح جميع الوكلاء الأربعة"""
        print("🚀 بدء إصلاح جميع الوكلاء الأربعة...")

        fixed_agents = []

        # إصلاح ErrorDetectorAgent
        try:
            error_agent_file = self.fix_error_detector_agent()
            fixed_agents.append(("ErrorDetectorAgent", error_agent_file))
        except Exception as e:
            print(f"❌ فشل في إصلاح ErrorDetectorAgent: {e}")

        # سيتم إضافة باقي الوكلاء في الجزء التالي

        return fixed_agents


def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح الوكلاء الأربعة مع مساعدة Gemini")
    print("=" * 60)

    fixer = GeminiAssistedAgentFixer()

    # بدء الإصلاح
    fixed_agents = fixer.fix_all_agents()

    print(f"\n🏆 تم إصلاح {len(fixed_agents)} وكيل:")
    for agent_name, file_path in fixed_agents:
        print(f"   ✅ {agent_name}: {file_path}")

    print("\n🚀 المرحلة التالية: إصلاح باقي الوكلاء وإضافة قدرات التطوير الكامل!")


if __name__ == "__main__":
    main()
