#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 إعداد مباشر لقاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Direct Database Setup
"""

import json
from pathlib import Path

import mysql.connector
from mysql.connector import Error


def load_config():
    """تحميل إعدادات قاعدة البيانات"""
    config_path = Path(__file__).parent.parent / "configs" / "database_config.json"
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    return config["database"]["mysql"]


def create_tables(connection):
    """إنشاء الجداول مباشرة"""

    tables = [
        # جدول المشاريع
        """
        CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            path TEXT NOT NULL,
            type VARCHAR(100),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_type (type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        # جدول التحليلات
        """
        CREATE TABLE IF NOT EXISTS analyses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            agent_type VARCHAR(100) NOT NULL,
            analysis_data JSON,
            results JSON,
            score DECIMAL(5,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            INDEX idx_project_id (project_id),
            INDEX idx_agent_type (agent_type),
            INDEX idx_score (score),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        # جدول الأخطاء
        """
        CREATE TABLE IF NOT EXISTS errors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            file_path TEXT,
            line_number INT DEFAULT 0,
            error_type VARCHAR(100),
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            INDEX idx_project_id (project_id),
            INDEX idx_error_type (error_type),
            INDEX idx_severity (severity),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        # جدول التقارير
        """
        CREATE TABLE IF NOT EXISTS reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT NOT NULL,
            report_type VARCHAR(100),
            title VARCHAR(255),
            content LONGTEXT,
            file_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            INDEX idx_project_id (project_id),
            INDEX idx_report_type (report_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        # جدول الإضافات
        """
        CREATE TABLE IF NOT EXISTS plugins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            version VARCHAR(50),
            description TEXT,
            config JSON,
            enabled BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_enabled (enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        # جدول الأنشطة
        """
        CREATE TABLE IF NOT EXISTS activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id INT,
            activity_type VARCHAR(100),
            description TEXT,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
            INDEX idx_project_id (project_id),
            INDEX idx_activity_type (activity_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
    ]

    cursor = connection.cursor()

    for i, table_sql in enumerate(tables, 1):
        try:
            cursor.execute(table_sql)
            print(f"✅ تم إنشاء الجدول {i}/6")
        except Error as e:
            print(f"❌ خطأ في إنشاء الجدول {i}: {e}")

    cursor.close()


def insert_sample_data(connection):
    """إدراج بيانات تجريبية"""

    cursor = connection.cursor()

    try:
        # إدراج مشاريع تجريبية
        projects_data = [
            (
                "نظام أنوبيس للمساعدين الذكيين",
                "/path/to/anubis/system",
                "python",
                "نظام ذكي شامل للمساعدة في تطوير البرمجيات",
            ),
            (
                "مشروع تجريبي 1",
                "/path/to/test/project1",
                "javascript",
                "مشروع تجريبي لاختبار النظام",
            ),
            (
                "مشروع تجريبي 2",
                "/path/to/test/project2",
                "java",
                "مشروع آخر لاختبار الميزات",
            ),
        ]

        cursor.executemany(
            "INSERT INTO projects (name, path, type, description) VALUES (%s, %s, %s, %s)",
            projects_data,
        )
        print("✅ تم إدراج المشاريع التجريبية")

        # إدراج تحليلات تجريبية
        analyses_data = [
            (
                1,
                "project_analyzer",
                '{"files_analyzed": 25, "lines_of_code": 2500}',
                '{"overall_score": 92.5, "issues_found": 3}',
                92.5,
            ),
            (
                1,
                "error_detector",
                '{"files_scanned": 25, "patterns_checked": 50}',
                '{"errors_found": 2, "warnings": 5}',
                88.0,
            ),
            (
                2,
                "project_analyzer",
                '{"files_analyzed": 10, "lines_of_code": 800}',
                '{"overall_score": 85.0, "issues_found": 1}',
                85.0,
            ),
        ]

        cursor.executemany(
            "INSERT INTO analyses (project_id, agent_type, analysis_data, results, score) VALUES (%s, %s, %s, %s, %s)",
            analyses_data,
        )
        print("✅ تم إدراج التحليلات التجريبية")

        # إدراج أخطاء تجريبية
        errors_data = [
            (1, "src/main.py", 42, "syntax_error", "high", "خطأ نحوي في السطر 42"),
            (1, "src/utils.py", 15, "logic_error", "medium", "خطأ منطقي محتمل"),
            (2, "app.js", 28, "undefined_variable", "medium", "متغير غير معرف"),
        ]

        cursor.executemany(
            "INSERT INTO errors (project_id, file_path, line_number, error_type, severity, message) VALUES (%s, %s, %s, %s, %s, %s)",
            errors_data,
        )
        print("✅ تم إدراج الأخطاء التجريبية")

        # إدراج إضافات تجريبية
        plugins_data = [
            (
                "Code Quality Checker",
                "1.0.0",
                "فحص جودة الكود",
                '{"max_complexity": 10, "check_style": true}',
                True,
            ),
            (
                "Security Scanner",
                "1.2.0",
                "فحص الأمان",
                '{"scan_dependencies": true, "check_vulnerabilities": true}',
                True,
            ),
        ]

        cursor.executemany(
            "INSERT INTO plugins (name, version, description, config, enabled) VALUES (%s, %s, %s, %s, %s)",
            plugins_data,
        )
        print("✅ تم إدراج الإضافات التجريبية")

        # إدراج أنشطة تجريبية
        activities_data = [
            (
                1,
                "analysis_completed",
                "تم إكمال تحليل المشروع",
                '{"agent": "project_analyzer", "duration": "45s"}',
            ),
            (
                1,
                "error_detected",
                "تم اكتشاف خطأ جديد",
                '{"file": "src/main.py", "line": 42}',
            ),
            (
                2,
                "project_created",
                "تم إنشاء مشروع جديد",
                '{"type": "javascript", "files": 10}',
            ),
        ]

        cursor.executemany(
            "INSERT INTO activities (project_id, activity_type, description, metadata) VALUES (%s, %s, %s, %s)",
            activities_data,
        )
        print("✅ تم إدراج الأنشطة التجريبية")

        connection.commit()

    except Error as e:
        print(f"❌ خطأ في إدراج البيانات التجريبية: {e}")
        connection.rollback()

    cursor.close()


def test_setup(connection):
    """اختبار الإعداد"""
    cursor = connection.cursor(dictionary=True)

    # عرض الجداول
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"\n📊 الجداول الموجودة ({len(tables)}):")

    for table in tables:
        table_name = list(table.values())[0]
        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        count = cursor.fetchone()["count"]
        print(f"  📋 {table_name}: {count} صف")

    # عرض المشاريع
    cursor.execute("SELECT * FROM projects")
    projects = cursor.fetchall()
    print(f"\n📁 المشاريع ({len(projects)}):")
    for project in projects:
        print(f"  - {project['name']} ({project['type']})")

    cursor.close()


def main():
    """الدالة الرئيسية"""
    print("🏺 إعداد مباشر لقاعدة بيانات نظام أنوبيس")
    print("=" * 50)

    # تحميل الإعدادات
    config = load_config()

    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            database=config["database"],
            charset=config.get("charset", "utf8mb4"),
            autocommit=False,
        )

        print(f"✅ تم الاتصال بقاعدة البيانات: {config['database']}")

        # إنشاء الجداول
        print("\n📋 إنشاء الجداول...")
        create_tables(connection)

        # إدراج البيانات التجريبية
        print("\n📝 إدراج البيانات التجريبية...")
        insert_sample_data(connection)

        # اختبار الإعداد
        print("\n🧪 اختبار الإعداد...")
        test_setup(connection)

        connection.close()

        print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("\n📝 الخطوات التالية:")
        print("  1. اختبار الاتصال: python database/mysql_connector.py")
        print("  2. تشغيل نظام أنوبيس: python main.py")

    except Error as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")


if __name__ == "__main__":
    main()
