# 📦 مجلد الأرشيف - نظام أنوبيس
## Archive Directory - Anubis System

**آخر تحديث**: 2025-07-16  
**الحالة**: ✅ منظم ومرتب  
**المحتويات**: ملفات مؤرشفة ومنظمة  

---

## 📜 الوصف

مجلد `archive` يحتوي على الملفات والمجلدات المؤرشفة من نظام أنوبيس. هذا المجلد يحافظ على الملفات القديمة والنسخ الاحتياطية والملفات غير المستخدمة حالياً، مما يساعد في الحفاظ على تنظيم المشروع الرئيسي.

## 📁 هيكل الأرشيف

### **المجلدات الرئيسية:**

#### **📂 cache_files/**
- **الوصف**: ملفات ذاكرة التخزين المؤقت القديمة
- **المحتويات**: ملفات cache منتهية الصلاحية
- **الغرض**: حفظ ملفات التخزين المؤقت للمراجعة

#### **📂 duplicate_reports/**
- **الوصف**: تقارير الملفات المكررة
- **المحتويات**: تقارير عن الملفات المكررة التي تم العثور عليها
- **الغرض**: مراجعة الملفات المكررة المحذوفة

#### **📂 old_databases/**
- **الوصف**: ملفات قواعد البيانات القديمة
- **المحتويات**: نسخ احتياطية من قواعد البيانات السابقة
- **الغرض**: الاحتفاظ بالبيانات التاريخية

#### **📂 old_files/**
- **الوصف**: الملفات والمجلدات القديمة
- **المحتويات**: 
  - مجلدات scripts القديمة المنقولة
  - ملفات تكوين قديمة
  - كود مهجور
- **الغرض**: حفظ الملفات القديمة للمراجعة

#### **📂 temp_files/**
- **الوصف**: ملفات مؤقتة مؤرشفة
- **المحتويات**: ملفات مؤقتة تم إنشاؤها أثناء العمليات
- **الغرض**: حفظ الملفات المؤقتة المهمة

#### **📂 unused_files/**
- **الوصف**: ملفات غير مستخدمة
- **المحتويات**: ملفات لم تعد مستخدمة في النظام
- **الغرض**: حفظ الملفات غير المستخدمة قبل الحذف النهائي

---

## 📊 ملفات التقارير المؤرشفة

### **تقارير التنظيم:**
- **`cleanup_log_20250714_144107.json`**: سجل عملية التنظيف
- **`cleanup_summary_20250714_144107.md`**: ملخص عملية التنظيف
- **`organization_log_20250714_140701.json`**: سجل عملية التنظيم
- **`organization_summary_20250714_140701.md`**: ملخص عملية التنظيم

---

## 🔍 كيفية استخدام الأرشيف

### **1. البحث في الملفات المؤرشفة:**
```bash
# البحث عن ملف معين
find archive/ -name "filename.py" -type f

# البحث عن ملفات بامتداد معين
find archive/ -name "*.json" -type f

# البحث في محتوى الملفات
grep -r "search_term" archive/
```

### **2. استعادة ملف من الأرشيف:**
```bash
# نسخ ملف من الأرشيف
cp archive/old_files/filename.py ./

# نقل ملف من الأرشيف
mv archive/old_files/filename.py ./
```

### **3. مراجعة التقارير:**
```bash
# عرض تقرير التنظيف
cat archive/cleanup_summary_20250714_144107.md

# عرض سجل التنظيم
cat archive/organization_log_20250714_140701.json
```

---

## 📋 إدارة الأرشيف

### **سياسة الاحتفاظ:**
- **الملفات الحديثة**: الاحتفاظ لمدة 6 أشهر
- **التقارير**: الاحتفاظ لمدة سنة واحدة
- **النسخ الاحتياطية**: الاحتفاظ لمدة سنتين
- **الملفات المهمة**: الاحتفاظ دائماً

### **التنظيف الدوري:**
```bash
# حذف الملفات الأقدم من 6 أشهر
find archive/temp_files/ -type f -mtime +180 -delete

# حذف الملفات الفارغة
find archive/ -type f -empty -delete

# حذف المجلدات الفارغة
find archive/ -type d -empty -rmdir
```

---

## 🛠️ أدوات إدارة الأرشيف

### **سكريبت أرشفة تلقائي:**
```python
#!/usr/bin/env python3
"""
سكريبت أرشفة تلقائي
"""
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path

def archive_old_files(source_dir, archive_dir, days_old=30):
    """أرشفة الملفات القديمة"""
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    for file_path in Path(source_dir).rglob("*"):
        if file_path.is_file():
            file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            if file_time < cutoff_date:
                # نقل إلى الأرشيف
                archive_path = Path(archive_dir) / file_path.relative_to(source_dir)
                archive_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(file_path), str(archive_path))
                print(f"تم أرشفة: {file_path}")

# استخدام السكريبت
archive_old_files("temp/", "archive/temp_files/", days_old=30)
```

### **سكريبت تنظيف الأرشيف:**
```python
def cleanup_archive(archive_dir, retention_days=365):
    """تنظيف الأرشيف من الملفات القديمة جداً"""
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    for file_path in Path(archive_dir).rglob("*"):
        if file_path.is_file():
            file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            if file_time < cutoff_date:
                file_path.unlink()
                print(f"تم حذف: {file_path}")

# تنظيف الملفات الأقدم من سنة
cleanup_archive("archive/temp_files/", retention_days=365)
```

---

## 📊 إحصائيات الأرشيف

### **حجم المجلدات:**
```bash
# حساب حجم كل مجلد
du -sh archive/*/

# إجمالي حجم الأرشيف
du -sh archive/

# عدد الملفات في كل مجلد
find archive/ -type f | wc -l
```

### **أقدم وأحدث الملفات:**
```bash
# أقدم ملف
find archive/ -type f -printf '%T+ %p\n' | sort | head -1

# أحدث ملف
find archive/ -type f -printf '%T+ %p\n' | sort | tail -1
```

---

## 🔒 الأمان والنسخ الاحتياطية

### **حماية الأرشيف:**
- **صلاحيات القراءة فقط**: للملفات المؤرشفة
- **نسخ احتياطية منتظمة**: للأرشيف المهم
- **تشفير الملفات الحساسة**: عند الحاجة

### **النسخ الاحتياطي:**
```bash
# إنشاء نسخة احتياطية مضغوطة
tar -czf archive_backup_$(date +%Y%m%d).tar.gz archive/

# نسخ احتياطي إلى موقع خارجي
rsync -av archive/ /backup/location/archive/
```

---

## 📝 سجل التغييرات

### **2025-07-16:**
- ✅ تم تنظيم الأرشيف وإنشاء هيكل واضح
- ✅ نقل المجلدات القديمة من scripts إلى old_files
- ✅ إنشاء README شامل للأرشيف
- ✅ تصنيف الملفات حسب النوع والغرض

### **2025-07-14:**
- ✅ إنشاء تقارير التنظيف والتنظيم
- ✅ أرشفة الملفات المكررة والمؤقتة
- ✅ تنظيف المجلدات الفارغة

---

## 🚀 الاستخدام المستقبلي

### **للمطورين:**
1. **مراجعة الكود القديم**: للتعلم والمقارنة
2. **استعادة الملفات**: عند الحاجة
3. **تحليل التطور**: مراجعة تطور المشروع
4. **استخراج المكونات**: إعادة استخدام أجزاء مفيدة

### **للصيانة:**
1. **تنظيف دوري**: حذف الملفات القديمة جداً
2. **مراقبة الحجم**: تجنب امتلاء القرص
3. **فهرسة المحتوى**: تسهيل البحث
4. **ضغط الملفات**: توفير مساحة التخزين

---

## ⚠️ تحذيرات مهمة

### **قبل الحذف:**
- ✅ تأكد من عدم الحاجة للملف
- ✅ راجع التبعيات والمراجع
- ✅ أنشئ نسخة احتياطية
- ✅ وثق سبب الحذف

### **عند الاستعادة:**
- ✅ تحقق من توافق الإصدار
- ✅ اختبر الملف المستعاد
- ✅ راجع التبعيات
- ✅ حدث التوثيق

---

<div align="center">

**📦 مجلد الأرشيف - نظام أنوبيس**

**حفظ وتنظيم الملفات التاريخية**

[![Archive](https://img.shields.io/badge/Archive-Organized-blue.svg)](README.md)
[![Storage](https://img.shields.io/badge/Storage-Efficient-brightgreen.svg)](README.md)
[![History](https://img.shields.io/badge/History-Preserved-gold.svg)](README.md)

**جزء من نظام أنوبيس المتكامل**

</div>
