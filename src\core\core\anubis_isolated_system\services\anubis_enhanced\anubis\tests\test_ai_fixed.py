#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 اختبار الذكاء الاصطناعي المُصحح
Fixed AI Integration Test

اختبار مُصحح لنظام دمج الذكاء الاصطناعي مع الوكلاء
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "agents"))

try:
    from anubis.core.ai_integration import AIIntegrationManager, OllamaProvider
    from anubis.core.base_agent import BaseAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)


class FixedAITestAgent(BaseAgent):
    """وكيل اختبار مُصحح للذكاء الاصطناعي"""

    def get_agent_type(self) -> str:
        return "fixed_ai_test"

    def initialize_agent(self):
        """تهيئة الوكيل"""
        self.test_results = []
        self.log_action("تهيئة وكيل اختبار الذكاء الاصطناعي المُصحح")

    def run_analysis(self):
        """تشغيل التحليل - مطلوب للفئة الأساسية"""
        return {
            "status": "completed",
            "agent_type": self.agent_type,
            "timestamp": datetime.now().isoformat(),
            "message": "تم تشغيل تحليل وكيل الاختبار",
        }


def test_ollama_direct():
    """اختبار مباشر لـ Ollama"""
    print("🧪 اختبار مباشر لـ Ollama...")

    models_to_test = ["llama3:8b", "mistral:7b", "phi3:mini"]
    results = {}

    for model in models_to_test:
        print(f"\n   🔍 اختبار النموذج: {model}")

        try:
            provider = OllamaProvider(model_name=model)

            # فحص التوفر
            is_available = provider.is_available()
            print(f"   📡 متاح: {'✅ نعم' if is_available else '❌ لا'}")

            if is_available:
                # اختبار بسيط
                test_prompt = "قل مرحبا"
                print(f"   📤 إرسال: {test_prompt}")

                response = provider.generate_response(test_prompt)

                if response and not response.startswith("خطأ"):
                    print(f"   📥 الاستجابة: {response[:100]}...")
                    results[model] = {
                        "available": True,
                        "working": True,
                        "response_length": len(response),
                    }
                else:
                    print(f"   ❌ فشل: {response}")
                    results[model] = {
                        "available": True,
                        "working": False,
                        "error": response,
                    }
            else:
                results[model] = {"available": False, "working": False}

        except Exception as e:
            print(f"   ❌ خطأ: {e}")
            results[model] = {"available": False, "working": False, "error": str(e)}

    return results


def test_ai_manager_fixed():
    """اختبار مُصحح لمدير الذكاء الاصطناعي"""
    print("\n🧠 اختبار مدير الذكاء الاصطناعي المُصحح...")

    try:
        # إنشاء مدير الذكاء الاصطناعي
        ai_manager = AIIntegrationManager()

        # فحص الحالة
        status = ai_manager.get_status()
        print(f"   📊 الحالة: {status}")

        # فحص الموفرين المتاحين
        available_providers = ai_manager.get_available_providers()
        print(f"   🔌 الموفرين المتاحين: {available_providers}")

        # اختبار توليد استجابة مع نموذج محدد
        if available_providers:
            # تحديد نموذج صحيح
            ai_manager.set_active_provider("ollama")

            # تحديث النموذج إلى نموذج يعمل
            if "ollama" in ai_manager.providers:
                ai_manager.providers["ollama"].model_name = "llama3:8b"

            test_prompt = "قل مرحبا بالعربية"
            print(f"   📤 اختبار التوليد: {test_prompt}")

            response = ai_manager.generate_ai_response(test_prompt)
            print(f"   📥 الاستجابة: {response[:150]}...")

            return {
                "manager_working": True,
                "available_providers": available_providers,
                "response_test": not response.startswith("خطأ"),
                "response_length": len(response),
            }
        else:
            print("   ⚠️ لا توجد موفرين متاحين")
            return {
                "manager_working": True,
                "available_providers": [],
                "response_test": False,
            }

    except Exception as e:
        print(f"   ❌ خطأ في مدير الذكاء الاصطناعي: {e}")
        return {"manager_working": False, "error": str(e)}


def test_agent_integration_fixed():
    """اختبار مُصحح لدمج الوكلاء مع الذكاء الاصطناعي"""
    print("\n🤖 اختبار دمج الوكلاء مع الذكاء الاصطناعي (مُصحح)...")

    try:
        # إنشاء وكيل اختبار مُصحح
        agent = FixedAITestAgent(project_path=".", config={"test_mode": True}, verbose=True)

        # فحص حالة الذكاء الاصطناعي في الوكيل
        ai_status = agent.get_ai_status()
        print(f"   📊 حالة الذكاء الاصطناعي في الوكيل: {ai_status['available']}")

        if agent.is_ai_enabled():
            # اختبار التحليل الذكي
            test_context = {
                "project_type": "Python",
                "files_count": 50,
                "main_language": "Python",
            }

            analysis_prompt = "اكتب جملة واحدة عن هذا المشروع"
            print(f"   📤 اختبار التحليل: {analysis_prompt}")

            analysis = agent.get_ai_analysis(analysis_prompt, test_context)
            print(f"   📥 التحليل: {analysis[:150]}...")

            # اختبار الاقتراحات الذكية
            test_data = {"code_quality": 75, "test_coverage": 60, "documentation": 40}

            suggestions = agent.get_smart_suggestions(test_data)
            print(f"   💡 الاقتراحات ({len(suggestions)}):")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"      {i}. {suggestion}")

            return {
                "agent_ai_enabled": True,
                "analysis_working": len(analysis) > 10 and not analysis.startswith("خطأ"),
                "suggestions_count": len(suggestions),
                "integration_successful": True,
            }
        else:
            print("   ⚠️ الذكاء الاصطناعي غير مفعل في الوكيل")
            return {"agent_ai_enabled": False, "integration_successful": False}

    except Exception as e:
        print(f"   ❌ خطأ في اختبار دمج الوكيل: {e}")
        return {
            "agent_ai_enabled": False,
            "integration_successful": False,
            "error": str(e),
        }


def test_simple_use_cases():
    """اختبار حالات استخدام بسيطة"""
    print("\n🎯 اختبار حالات الاستخدام البسيطة...")

    test_cases = [
        {
            "name": "تحية بسيطة",
            "prompt": "قل مرحبا",
        },
        {
            "name": "سؤال بسيط",
            "prompt": "ما هو Python؟",
        },
    ]

    results = {}

    try:
        ai_manager = AIIntegrationManager()

        # تأكد من استخدام نموذج يعمل
        if "ollama" in ai_manager.providers:
            ai_manager.providers["ollama"].model_name = "llama3:8b"

        for test_case in test_cases:
            print(f"\n   🧪 {test_case['name']}")
            print(f"   📤 {test_case['prompt']}")

            response = ai_manager.generate_ai_response(test_case["prompt"])

            if response and not response.startswith("خطأ"):
                results[test_case["name"]] = {
                    "success": True,
                    "response_length": len(response),
                    "response_preview": response[:100],
                }

                print(f"   📥 نجح ({len(response)} حرف): {response[:50]}...")
            else:
                results[test_case["name"]] = {"success": False, "error": response}
                print(f"   ❌ فشل: {response}")

    except Exception as e:
        print(f"   ❌ خطأ في اختبار حالات الاستخدام: {e}")
        results["error"] = str(e)

    return results


def generate_fixed_report(ollama_results, manager_results, agent_results, use_case_results):
    """إنتاج تقرير مُصحح"""

    report = {
        "timestamp": datetime.now().isoformat(),
        "test_type": "fixed_ai_integration_test",
        "test_summary": {
            "ollama_models_tested": len(ollama_results),
            "working_models": len([r for r in ollama_results.values() if r.get("working", False)]),
            "ai_manager_working": manager_results.get("manager_working", False),
            "agent_integration_working": agent_results.get("integration_successful", False),
            "use_cases_tested": len(use_case_results),
            "successful_use_cases": len(
                [r for r in use_case_results.values() if r.get("success", False)]
            ),
        },
        "detailed_results": {
            "ollama_providers": ollama_results,
            "ai_manager": manager_results,
            "agent_integration": agent_results,
            "use_cases": use_case_results,
        },
        "fixes_applied": [
            "إصلاح الفئة الأساسية للوكلاء",
            "تصحيح اسم النموذج في Ollama",
            "إضافة دالة run_analysis المطلوبة",
            "تحسين معالجة الأخطاء",
        ],
    }

    # حفظ التقرير
    report_file = (
        f"ai_integration_fixed_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )

    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n📄 تم حفظ التقرير المُصحح في: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")

    return report


def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الذكاء الاصطناعي المُصحح - نظام أنوبيس")
    print("=" * 60)

    # اختبار مباشر لـ Ollama
    ollama_results = test_ollama_direct()

    # اختبار مدير الذكاء الاصطناعي المُصحح
    manager_results = test_ai_manager_fixed()

    # اختبار دمج الوكلاء المُصحح
    agent_results = test_agent_integration_fixed()

    # اختبار حالات الاستخدام البسيطة
    use_case_results = test_simple_use_cases()

    # إنتاج التقرير
    report = generate_fixed_report(ollama_results, manager_results, agent_results, use_case_results)

    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار المُصحح:")
    print(f"   🤖 نماذج Ollama المختبرة: {report['test_summary']['ollama_models_tested']}")
    print(f"   ✅ النماذج التي تعمل: {report['test_summary']['working_models']}")
    print(
        f"   🧠 مدير الذكاء الاصطناعي: {'✅ يعمل' if report['test_summary']['ai_manager_working'] else '❌ لا يعمل'}"
    )
    print(
        f"   🔗 دمج الوكلاء: {'✅ ناجح' if report['test_summary']['agent_integration_working'] else '❌ فاشل'}"
    )
    print(
        f"   🎯 حالات الاستخدام الناجحة: {report['test_summary']['successful_use_cases']}/{report['test_summary']['use_cases_tested']}"
    )

    print("\n🔧 الإصلاحات المطبقة:")
    for fix in report["fixes_applied"]:
        print(f"   ✅ {fix}")

    print("\n🏺 انتهى اختبار الذكاء الاصطناعي المُصحح!")

    return 0 if report["test_summary"]["working_models"] > 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
