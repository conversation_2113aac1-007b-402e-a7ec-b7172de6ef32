{"timestamp": "2025-07-14T13:43:14.213712", "total_tests": 6, "passed_tests": 6, "success_rate": 100.0, "total_duration": 0.1627671718597412, "test_results": [{"test_name": "اختبار الاتصال", "status": "PASS", "message": "الاتصال بقاعدة البيانات نجح", "duration": 0.10271167755126953, "timestamp": "2025-07-14T13:43:14.153104"}, {"test_name": "اختبار هيكل قاعدة البيانات", "status": "PASS", "message": "جميع الجداول والأعمدة موجودة (6 جداول)", "duration": 0.011360883712768555, "timestamp": "2025-07-14T13:43:14.164827"}, {"test_name": "اختبار سلامة البيانات", "status": "PASS", "message": "البيانات سليمة - المشاريع: 9, التحليلات: 9, الأخطاء: 9, الإضافات: 6, الأنشطة: 9", "duration": 0.009085416793823242, "timestamp": "2025-07-14T13:43:14.174251"}, {"test_name": "اختبار عمليات CRUD", "status": "PASS", "message": "جميع عمليات الإنشاء والقراءة والتحديث والحذف نجحت", "duration": 0.018890857696533203, "timestamp": "2025-07-14T13:43:14.193392"}, {"test_name": "اختبار الأداء", "status": "PASS", "message": "أوقات الاستعلامات: استعلام بسيط: 0.000s, استعلام معقد: 0.002s, استعلام JSON: 0.001s", "duration": 0.00967717170715332, "timestamp": "2025-07-14T13:43:14.203406"}, {"test_name": "اختبار الأمان", "status": "PASS", "message": "الحماية من SQL Injection تعمل بشكل صحيح", "duration": 0.009027957916259766, "timestamp": "2025-07-14T13:43:14.212821"}], "errors_found": [], "database_config": {"host": "localhost", "port": 3306, "database": "anubis_system"}}