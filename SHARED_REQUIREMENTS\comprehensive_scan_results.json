{"scan_date": "2025-07-23T02:55:57.987603", "python_environments": [{"name": "current", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\.venv\\Scripts\\python.exe", "version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "is_venv": true}], "all_packages": {"aiofiles": {"version": "24.1.0", "environments": ["current"]}, "annotated-types": {"version": "0.7.0", "environments": ["current"]}, "anyio": {"version": "4.9.0", "environments": ["current"]}, "certifi": {"version": "2025.7.14", "environments": ["current"]}, "charset-normalizer": {"version": "3.4.2", "environments": ["current"]}, "click": {"version": "8.2.1", "environments": ["current"]}, "colorama": {"version": "0.4.6", "environments": ["current"]}, "fastapi": {"version": "0.116.1", "environments": ["current"]}, "greenlet": {"version": "3.2.3", "environments": ["current"]}, "h11": {"version": "0.16.0", "environments": ["current"]}, "httpcore": {"version": "1.0.9", "environments": ["current"]}, "httpx": {"version": "0.28.1", "environments": ["current"]}, "idna": {"version": "3.10", "environments": ["current"]}, "iniconfig": {"version": "2.1.0", "environments": ["current"]}, "jinja2": {"version": "3.1.6", "environments": ["current"]}, "markupsafe": {"version": "3.0.2", "environments": ["current"]}, "mysql-connector-python": {"version": "9.3.0", "environments": ["current"]}, "packaging": {"version": "25.0", "environments": ["current"]}, "pip": {"version": "25.1.1", "environments": ["current"]}, "pluggy": {"version": "1.6.0", "environments": ["current"]}, "psutil": {"version": "7.0.0", "environments": ["current"]}, "pydantic": {"version": "2.11.7", "environments": ["current"]}, "pydantic_core": {"version": "2.33.2", "environments": ["current"]}, "pygments": {"version": "2.19.2", "environments": ["current"]}, "pytest": {"version": "8.4.1", "environments": ["current"]}, "python-dotenv": {"version": "1.1.1", "environments": ["current"]}, "python-multipart": {"version": "0.0.20", "environments": ["current"]}, "pywin32": {"version": "311", "environments": ["current"]}, "pyyaml": {"version": "6.0.2", "environments": ["current"]}, "requests": {"version": "2.32.4", "environments": ["current"]}, "sniffio": {"version": "1.3.1", "environments": ["current"]}, "sqlalchemy": {"version": "2.0.41", "environments": ["current"]}, "starlette": {"version": "0.47.1", "environments": ["current"]}, "typing_extensions": {"version": "4.14.1", "environments": ["current"]}, "typing-inspection": {"version": "0.4.1", "environments": ["current"]}, "urllib3": {"version": "2.5.0", "environments": ["current"]}, "uvicorn": {"version": "0.35.0", "environments": ["current"]}}, "categorized_packages": {"ai_ml": [], "web_development": ["<PERSON><PERSON><PERSON>", "httpx", "requests", "urllib3"], "data_science": [], "automation": [], "system_tools": ["psutil"], "development_tools": ["pytest"], "other": ["aiofiles", "annotated-types", "anyio", "certifi", "charset-normalizer", "click", "colorama", "greenlet", "h11", "httpcore", "idna", "iniconfig", "jinja2", "markupsafe", "mysql-connector-python", "packaging", "pip", "pluggy", "pydantic", "pydantic_core", "pygments", "python-dotenv", "python-multipart", "pywin32", "pyyaml", "sniffio", "sqlalchemy", "starlette", "typing_extensions", "typing-inspection", "u<PERSON><PERSON>"]}, "statistics": {"total_packages": 37, "total_environments": 1, "categories_count": {"ai_ml": 0, "web_development": 4, "data_science": 0, "automation": 0, "system_tools": 1, "development_tools": 1, "other": 31}}}