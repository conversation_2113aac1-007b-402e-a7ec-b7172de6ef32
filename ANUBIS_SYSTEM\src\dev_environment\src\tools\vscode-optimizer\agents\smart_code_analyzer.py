#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 محلل الكود الذكي المدعوم بالذكاء الاصطناعي
Smart AI-Powered Code Analyzer

وكيل ذكي لتحليل الكود باستخدام نماذج الذكاء الاصطناعي
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# إضافة مسار core
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from base_agent import BaseAgent
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent


class SmartCodeAnalyzer(BaseAgent):
    """🧠 محلل الكود الذكي المدعوم بالذكاء الاصطناعي"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "smart_code_analyzer"
    
    def initialize_agent(self):
        """تهيئة محلل الكود الذكي"""
        self.supported_extensions = {
            '.py': 'Python',
            '.js': 'JavaScript', 
            '.ts': 'TypeScript',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust'
        }
        
        self.analysis_results = []
        self.log_action("تهيئة محلل الكود الذكي", "تم تفعيل جميع الميزات الذكية")
    
    def analyze_file_with_ai(self, file_path: str) -> Dict[str, Any]:
        """تحليل ملف بالذكاء الاصطناعي"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                'status': 'error',
                'message': f'الملف غير موجود: {file_path}'
            }
        
        # قراءة محتوى الملف
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في قراءة الملف: {e}'
            }
        
        # تحديد نوع الملف
        file_ext = file_path.suffix.lower()
        language = self.supported_extensions.get(file_ext, 'Unknown')
        
        # تحليل تقليدي أولاً
        traditional_analysis = self._analyze_traditional(content, language)
        
        # تحليل ذكي إضافي
        ai_analysis = None
        if self.is_ai_enabled():
            ai_analysis = self._analyze_with_ai(content, language, file_path.name)
        
        result = {
            'status': 'success',
            'file_path': str(file_path),
            'file_info': {
                'name': file_path.name,
                'size': len(content),
                'lines': len(content.split('\n')),
                'language': language
            },
            'traditional_analysis': traditional_analysis,
            'ai_analysis': ai_analysis,
            'timestamp': datetime.now().isoformat()
        }
        
        self.analysis_results.append(result)
        return result
    
    def _analyze_traditional(self, content: str, language: str) -> Dict[str, Any]:
        """تحليل تقليدي للكود"""
        lines = content.split('\n')
        
        analysis = {
            'basic_metrics': {
                'total_lines': len(lines),
                'non_empty_lines': len([line for line in lines if line.strip()]),
                'comment_lines': 0,
                'code_lines': 0
            },
            'complexity_indicators': {
                'nested_blocks': 0,
                'function_count': 0,
                'class_count': 0
            },
            'potential_issues': []
        }
        
        # تحليل أساسي حسب اللغة
        if language == 'Python':
            analysis = self._analyze_python_traditional(lines, analysis)
        elif language in ['JavaScript', 'TypeScript']:
            analysis = self._analyze_js_traditional(lines, analysis)
        
        return analysis
    
    def _analyze_python_traditional(self, lines: List[str], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل تقليدي للـ Python"""
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # عد التعليقات
            if stripped.startswith('#'):
                analysis['basic_metrics']['comment_lines'] += 1
            elif stripped and not stripped.startswith('#'):
                analysis['basic_metrics']['code_lines'] += 1
            
            # عد الدوال والفئات
            if stripped.startswith('def '):
                analysis['complexity_indicators']['function_count'] += 1
            elif stripped.startswith('class '):
                analysis['complexity_indicators']['class_count'] += 1
            
            # فحص التعقيد
            indentation = len(line) - len(line.lstrip())
            if indentation > 12:  # أكثر من 3 مستويات
                analysis['complexity_indicators']['nested_blocks'] += 1
            
            # فحص مشاكل محتملة
            if 'eval(' in stripped:
                analysis['potential_issues'].append(f"خطر أمني: استخدام eval في السطر {i+1}")
            
            if len(stripped) > 100:
                analysis['potential_issues'].append(f"سطر طويل جداً: السطر {i+1} ({len(stripped)} حرف)")
        
        return analysis
    
    def _analyze_js_traditional(self, lines: List[str], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل تقليدي للـ JavaScript"""
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # عد التعليقات
            if stripped.startswith('//') or stripped.startswith('/*'):
                analysis['basic_metrics']['comment_lines'] += 1
            elif stripped:
                analysis['basic_metrics']['code_lines'] += 1
            
            # عد الدوال
            if 'function ' in stripped or '=>' in stripped:
                analysis['complexity_indicators']['function_count'] += 1
            
            # عد الفئات
            if stripped.startswith('class '):
                analysis['complexity_indicators']['class_count'] += 1
            
            # فحص مشاكل محتملة
            if 'eval(' in stripped:
                analysis['potential_issues'].append(f"خطر أمني: استخدام eval في السطر {i+1}")
            
            if 'var ' in stripped:
                analysis['potential_issues'].append(f"استخدام var بدلاً من let/const في السطر {i+1}")
        
        return analysis
    
    def _analyze_with_ai(self, content: str, language: str, filename: str) -> Dict[str, Any]:
        """تحليل ذكي بالذكاء الاصطناعي"""
        # تحضير السياق
        context = {
            'language': language,
            'filename': filename,
            'file_size': len(content),
            'lines_count': len(content.split('\n'))
        }
        
        # إنشاء prompt للتحليل
        analysis_prompt = f"""
        حلل هذا الكود بلغة {language} بشكل شامل:
        
        اسم الملف: {filename}
        اللغة: {language}
        عدد الأسطر: {len(content.split('\n'))}
        
        الكود:
        ```{language.lower()}
        {content[:2000]}  # أول 2000 حرف فقط
        ```
        
        قدم تحليلاً يتضمن:
        1. تقييم جودة الكود (1-10)
        2. نقاط القوة
        3. نقاط الضعف والمشاكل
        4. اقتراحات للتحسين
        5. مشاكل الأمان المحتملة
        6. تحسينات الأداء
        
        اجعل التحليل مفصلاً وعملياً.
        """
        
        try:
            ai_response = self.get_ai_analysis(analysis_prompt, context)
            
            # محاولة استخراج تقييم رقمي
            quality_score = self._extract_quality_score(ai_response)
            
            return {
                'ai_response': ai_response,
                'quality_score': quality_score,
                'analysis_length': len(ai_response),
                'ai_provider': 'ollama'
            }
            
        except Exception as e:
            return {
                'error': f'خطأ في التحليل الذكي: {e}',
                'ai_response': None,
                'quality_score': None
            }
    
    def _extract_quality_score(self, ai_response: str) -> int:
        """استخراج تقييم الجودة من استجابة الذكاء الاصطناعي"""
        import re
        
        # البحث عن أرقام من 1-10
        patterns = [
            r'جودة.*?(\d+)/10',
            r'تقييم.*?(\d+)/10', 
            r'نقاط.*?(\d+)/10',
            r'درجة.*?(\d+)/10',
            r'(\d+)\s*/\s*10'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, ai_response, re.IGNORECASE)
            if match:
                score = int(match.group(1))
                if 1 <= score <= 10:
                    return score
        
        return None
    
    def analyze_project_files(self, max_files: int = 10) -> Dict[str, Any]:
        """تحليل ملفات المشروع"""
        code_files = []
        
        # البحث عن ملفات الكود
        for ext in self.supported_extensions.keys():
            files = list(self.project_path.rglob(f'*{ext}'))
            code_files.extend(files[:max_files//len(self.supported_extensions)])
        
        # تحديد العدد الأقصى
        code_files = code_files[:max_files]
        
        results = []
        for file_path in code_files:
            self.log_action("تحليل ملف", str(file_path.name))
            result = self.analyze_file_with_ai(str(file_path))
            results.append(result)
        
        # تلخيص النتائج
        summary = self._create_project_summary(results)
        
        return {
            'status': 'completed',
            'files_analyzed': len(results),
            'results': results,
            'summary': summary,
            'timestamp': datetime.now().isoformat()
        }
    
    def _create_project_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """إنشاء ملخص للمشروع"""
        total_files = len(results)
        total_lines = sum(r['file_info']['lines'] for r in results if r['status'] == 'success')
        
        # حساب متوسط الجودة
        quality_scores = [
            r['ai_analysis']['quality_score'] 
            for r in results 
            if r['status'] == 'success' and r.get('ai_analysis') and r['ai_analysis'].get('quality_score')
        ]
        
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else None
        
        # جمع المشاكل
        all_issues = []
        for result in results:
            if result['status'] == 'success':
                issues = result['traditional_analysis']['potential_issues']
                all_issues.extend(issues)
        
        return {
            'total_files': total_files,
            'total_lines': total_lines,
            'average_quality_score': avg_quality,
            'total_issues_found': len(all_issues),
            'common_issues': list(set(all_issues))[:10],  # أكثر 10 مشاكل شيوعاً
            'languages_detected': list(set(
                r['file_info']['language'] 
                for r in results 
                if r['status'] == 'success'
            ))
        }
    
    def get_smart_recommendations(self) -> List[str]:
        """الحصول على توصيات ذكية للمشروع"""
        if not self.analysis_results:
            return ["قم بتحليل بعض الملفات أولاً للحصول على توصيات"]
        
        # تحضير بيانات للذكاء الاصطناعي
        summary_data = self._create_project_summary(self.analysis_results)
        
        if self.is_ai_enabled():
            recommendations_prompt = f"""
            بناءً على تحليل المشروع التالي، قدم 5-10 توصيات عملية للتحسين:
            
            ملخص المشروع:
            - عدد الملفات: {summary_data['total_files']}
            - إجمالي الأسطر: {summary_data['total_lines']}
            - متوسط الجودة: {summary_data.get('average_quality_score', 'غير محدد')}
            - عدد المشاكل: {summary_data['total_issues_found']}
            - اللغات: {', '.join(summary_data['languages_detected'])}
            
            المشاكل الشائعة:
            {chr(10).join(summary_data['common_issues'][:5])}
            
            قدم توصيات محددة وعملية لتحسين جودة الكود والأداء والأمان.
            """
            
            recommendations_text = self.get_ai_analysis(recommendations_prompt, summary_data)
            return self.get_smart_suggestions({'analysis_summary': summary_data})
        
        # توصيات تقليدية
        return [
            "قم بإضافة المزيد من التعليقات للكود",
            "راجع الأسطر الطويلة وقسمها",
            "تحقق من مشاكل الأمان المكتشفة",
            "حسن تنظيم الكود والمتغيرات"
        ]
