[{"timestamp": "2025-07-14T14:07:01.160750", "action": "إنشاء هيكل الأرشيف", "details": "تم إنشاء 6 مجلدات"}, {"timestamp": "2025-07-14T14:07:01.203824", "action": "حذف __pycache__", "details": "__pycache__"}, {"timestamp": "2025-07-14T14:07:01.207612", "action": "حذف __pycache__", "details": "agents\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.211140", "action": "حذف __pycache__", "details": "core\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.212723", "action": "حذف __pycache__", "details": "database\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.215307", "action": "حذف __pycache__", "details": "plugins\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.217807", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.222372", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.223959", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.228784", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.231151", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\certifi\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.234688", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\dependency_groups\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.242040", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.244736", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.250015", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\idna\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.252719", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\msgpack\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.260086", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.261650", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.266652", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.274165", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.275838", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.286195", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\requests\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.289168", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.325246", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.327924", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.329881", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\tomli_w\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.333328", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\truststore\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.339562", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.343273", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.344971", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.351398", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.353809", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.356387", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.359829", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\resolvers\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.361693", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.363086", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\filters\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.364826", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.367222", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.369246", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\styles\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.371043", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\packaging\\licenses\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.373458", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.380023", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\cli\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.389168", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\commands\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.392412", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\distributions\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.395390", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\index\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.398395", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\locations\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.401073", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.407844", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\models\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.412287", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\network\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.415252", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.419116", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\req\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.420922", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.433692", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\utils\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.437270", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\vcs\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.439374", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.444065", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.449346", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\build\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.451596", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.454441", "action": "حذف __pycache__", "details": ".venv\\Lib\\site-packages\\pip\\_internal\\metadata\\importlib\\__pycache__"}, {"timestamp": "2025-07-14T14:07:01.459335", "action": "أرشفة تقرير مكرر", "details": "all_tests_report_20250714_135032.json"}, {"timestamp": "2025-07-14T14:07:01.460292", "action": "أرشفة تقرير مكرر", "details": "stress_test_report_20250714_135031.json"}, {"timestamp": "2025-07-14T14:07:01.461801", "action": "أرشفة تقرير مكرر", "details": "test_report_20250714_135031.json"}, {"timestamp": "2025-07-14T14:07:01.463062", "action": "أرشفة تقرير مكرر", "details": "simple_validation_report_20250714_135030.json"}, {"timestamp": "2025-07-14T14:07:01.464204", "action": "أرشفة تقرير مكرر", "details": "simple_validation_report_20250714_134926.json"}, {"timestamp": "2025-07-14T14:07:01.465344", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_134653.json"}, {"timestamp": "2025-07-14T14:07:01.466469", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_134549.json"}, {"timestamp": "2025-07-14T14:07:01.467482", "action": "أرشفة تقرير مكرر", "details": "final_validation_report_20250714_134540.json"}, {"timestamp": "2025-07-14T14:07:01.468444", "action": "أرشفة تقرير مكرر", "details": "stress_test_report_20250714_134422.json"}, {"timestamp": "2025-07-14T14:07:01.469405", "action": "أرشفة تقرير مكرر", "details": "test_report_20250714_134314.json"}, {"timestamp": "2025-07-14T14:07:01.471048", "action": "أرشفة التقارير المكررة", "details": "تم أرشفة 10 ملف"}, {"timestamp": "2025-07-14T14:07:01.472307", "action": "أرشفة قاعدة بيانات قديمة", "details": "project_db.db"}, {"timestamp": "2025-07-14T14:07:01.473744", "action": "أرشفة قاعدة بيانات قديمة", "details": "database/anubis.db"}, {"timestamp": "2025-07-14T14:07:01.473897", "action": "أرشفة قواعد البيانات القديمة", "details": "تم أرشفة 2 ملف"}, {"timestamp": "2025-07-14T14:07:01.475733", "action": "نقل ملف اختبار", "details": "test_anubis_system.py -> tests/"}, {"timestamp": "2025-07-14T14:07:01.477047", "action": "نقل ملف اختبار", "details": "ask_anubis.py -> tests/"}, {"timestamp": "2025-07-14T14:07:01.477252", "action": "تنظيم ملفات الاختبار", "details": "تم نقل 2 ملف"}, {"timestamp": "2025-07-14T14:07:01.516127", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": ".venv\\Include"}, {"timestamp": "2025-07-14T14:07:01.516732", "action": "حذ<PERSON> مج<PERSON>د فارغ", "details": "tests\\workspace\\backups"}, {"timestamp": "2025-07-14T14:07:01.517917", "action": "إنشاء .gitignore", "details": "تم إنشاء ملف .gitignore محسن"}, {"timestamp": "2025-07-14T14:07:01.519173", "action": "إنشاء توثيق الهيكل", "details": "تم إنشاء PROJECT_STRUCTURE.md"}]