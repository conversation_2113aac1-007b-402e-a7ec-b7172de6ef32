{"project_info": {"path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgi1i7kge", "name": "tmpgi1i7kge", "type": "custom", "structure": {"project_exists": true, "is_directory": true, "files_count": 3, "directories_count": 3, "python_files": ["src\\main.py", "src\\models.py", "tests\\test_main.py"], "config_files": [], "data_files": []}, "agent_type": "project_analyzer", "analysis_time": "2025-07-14T12:35:43.943823"}, "project_size_analysis": {"total_files": 3, "total_lines": 3, "code_lines": 3, "comment_lines": 0, "blank_lines": 0, "file_types": {".py": 3}, "largest_files": [{"file": "src\\main.py", "lines": 1}, {"file": "src\\models.py", "lines": 1}, {"file": "tests\\test_main.py", "lines": 1}], "directory_structure": {}}, "complexity_analysis": {"cyclomatic_complexity": 0, "cognitive_complexity": 0, "function_complexity": [], "class_complexity": [], "module_complexity": [{"file": "src\\main.py", "complexity": 1}, {"file": "src\\models.py", "complexity": 1}, {"file": "tests\\test_main.py", "complexity": 1}], "complexity_distribution": {"low": 3, "medium": 0, "high": 0}, "average_complexity": 1.0}, "dependency_analysis": {"total_dependencies": 0, "external_dependencies": [], "internal_dependencies": [], "dependency_tree": {}, "outdated_dependencies": [], "security_vulnerabilities": []}, "architecture_analysis": {"project_type": "custom", "directory_structure": {"depth": 1, "directories": ["docs", "src", "tests"], "organization_score": 42.857142857142854}, "design_patterns": [], "architectural_style": "MVC", "modularity_score": 90, "coupling_analysis": {}, "cohesion_analysis": {}}, "performance_analysis": {"potential_bottlenecks": [], "optimization_opportunities": [], "resource_usage_patterns": {}, "scalability_assessment": {}}, "security_analysis": {"security_issues": [], "vulnerability_assessment": {}, "security_score": 100, "recommendations": []}, "maintainability_analysis": {"maintainability_index": 19.999999999999996, "documentation_coverage": 0.0, "test_coverage_estimate": 33.33333333333333, "code_duplication": 0, "technical_debt": []}, "recommendations": [{"category": "architecture", "priority": "high", "title": "تحسين الهيكل المعماري", "description": "فصل الاهتمامات وتطبيق أنماط التصميم", "action": "إعادة تنظيم الكود حسب المسؤوليات"}, {"category": "performance", "priority": "medium", "title": "تحسين الأداء", "description": "تحسين الخوارزميات وتقليل التعقيد", "action": "مراجعة الحلقات والاستعلامات"}, {"category": "security", "priority": "high", "title": "تعزيز الأمان", "description": "إضافة طبقات حماية وتشفير البيانات الحساسة", "action": "مراجعة نقاط الضعف الأمنية"}, {"category": "maintainability", "priority": "medium", "title": "تحسين قابلية الصيانة", "description": "إضافة توثيق واختبارات شاملة", "action": "كتابة docstrings واختبارات وحدة"}], "summary": {"overall_score": 64.8, "project_size": "3 ملف", "code_lines": 3, "complexity_level": "من<PERSON><PERSON>ض", "security_status": "<PERSON>ي<PERSON>", "maintainability_level": "ضعيف", "recommendations_count": 4, "analysis_time": "2025-07-14T12:35:43.987029"}}