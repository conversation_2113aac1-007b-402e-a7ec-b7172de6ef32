#!/bin/bash
# سكريبت إيقاف نظام الأتمتة المعزول

echo "🛑 إيقاف نظام سير العمل والأتمتة المعزول..."

cd workflows_and_automation

# إيقاف جميع الخدمات
echo "📱 إيقاف جميع خدمات الأتمتة..."
docker-compose down

# إزالة الشبكات (اختياري)
echo "🌐 تنظيف الشبكات (اختياري)..."
read -p "هل تريد إزالة الشبكات المعزولة؟ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker network rm anubis-workflows-net anubis-automation-secure-net 2>/dev/null || true
    echo "✅ تم تنظيف الشبكات"
fi

echo "✅ تم إيقاف نظام الأتمتة بنجاح"
