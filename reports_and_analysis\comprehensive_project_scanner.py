#!/usr/bin/env python3
"""
🔍 فاحص المشروع الشامل
Comprehensive Project Scanner

يقوم بفحص جميع مجلدات وملفات المشروع بالتفصيل
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import mimetypes


class ComprehensiveProjectScanner:
    """فاحص المشروع الشامل"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.scan_results = {
            "scan_date": datetime.now().isoformat(),
            "project_root": str(self.project_root.absolute()),
            "directories": {},
            "file_types": {},
            "statistics": {},
            "detailed_analysis": {}
        }
        
        # أنواع الملفات المهمة
        self.important_extensions = {
            '.py': 'Python Source',
            '.md': 'Markdown Documentation',
            '.json': 'JSON Configuration',
            '.txt': 'Text File',
            '.bat': 'Batch Script',
            '.ps1': 'PowerShell Script',
            '.sh': 'Shell Script',
            '.html': 'HTML File',
            '.css': 'CSS Stylesheet',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React JSX',
            '.sql': 'SQL Script',
            '.db': 'Database File',
            '.sqlite': 'SQLite Database',
            '.log': 'Log File',
            '.yml': 'YAML Configuration',
            '.yaml': 'YAML Configuration',
            '.ini': 'INI Configuration',
            '.cfg': 'Configuration File',
            '.toml': 'TOML Configuration'
        }
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """الحصول على معلومات مفصلة عن الملف"""
        try:
            stat = file_path.stat()
            file_info = {
                "name": file_path.name,
                "size": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 3),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "extension": file_path.suffix.lower(),
                "type": self.important_extensions.get(file_path.suffix.lower(), "Unknown"),
                "is_empty": stat.st_size == 0
            }
            
            # محاولة قراءة عدد الأسطر للملفات النصية
            if file_path.suffix.lower() in ['.py', '.md', '.txt', '.json', '.js', '.css', '.html']:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        file_info["lines"] = len(lines)
                        file_info["non_empty_lines"] = len([line for line in lines if line.strip()])
                except:
                    file_info["lines"] = "N/A"
                    file_info["non_empty_lines"] = "N/A"
            
            return file_info
        except Exception as e:
            return {
                "name": file_path.name,
                "error": str(e),
                "type": "Error"
            }
    
    def scan_directory(self, directory: Path, max_depth: int = 5, current_depth: int = 0) -> Dict[str, Any]:
        """فحص مجلد بالتفصيل"""
        if current_depth > max_depth:
            return {"skipped": "Max depth reached"}
        
        dir_info = {
            "path": str(directory.relative_to(self.project_root)),
            "absolute_path": str(directory.absolute()),
            "files": [],
            "subdirectories": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {},
                "python_files": 0,
                "config_files": 0,
                "doc_files": 0,
                "script_files": 0
            }
        }
        
        try:
            items = list(directory.iterdir())
            
            for item in items:
                if item.is_file():
                    file_info = self.get_file_info(item)
                    dir_info["files"].append(file_info)
                    
                    # إحصائيات
                    dir_info["statistics"]["total_files"] += 1
                    if isinstance(file_info.get("size"), int):
                        dir_info["statistics"]["total_size"] += file_info["size"]
                    
                    # تصنيف الملفات
                    ext = file_info.get("extension", "")
                    file_type = file_info.get("type", "Unknown")
                    
                    if ext not in dir_info["statistics"]["file_types"]:
                        dir_info["statistics"]["file_types"][ext] = 0
                    dir_info["statistics"]["file_types"][ext] += 1
                    
                    if ext == '.py':
                        dir_info["statistics"]["python_files"] += 1
                    elif ext in ['.json', '.yml', '.yaml', '.ini', '.cfg', '.toml']:
                        dir_info["statistics"]["config_files"] += 1
                    elif ext in ['.md', '.txt', '.html']:
                        dir_info["statistics"]["doc_files"] += 1
                    elif ext in ['.bat', '.ps1', '.sh']:
                        dir_info["statistics"]["script_files"] += 1
                
                elif item.is_dir() and not item.name.startswith('.') and item.name not in ['__pycache__', '.git', '.venv']:
                    subdir_info = self.scan_directory(item, max_depth, current_depth + 1)
                    dir_info["subdirectories"][item.name] = subdir_info
                    
                    # إضافة إحصائيات المجلدات الفرعية للمجلد الحالي
                    if isinstance(subdir_info, dict) and "statistics" in subdir_info:
                        sub_stats = subdir_info["statistics"]
                        dir_info["statistics"]["total_files"] += sub_stats["total_files"]
                        dir_info["statistics"]["total_size"] += sub_stats["total_size"]
                        dir_info["statistics"]["python_files"] += sub_stats["python_files"]
                        dir_info["statistics"]["config_files"] += sub_stats["config_files"]
                        dir_info["statistics"]["doc_files"] += sub_stats["doc_files"]
                        dir_info["statistics"]["script_files"] += sub_stats["script_files"]
                        
                        # دمج أنواع الملفات
                        for ext, count in sub_stats["file_types"].items():
                            if ext not in dir_info["statistics"]["file_types"]:
                                dir_info["statistics"]["file_types"][ext] = 0
                            dir_info["statistics"]["file_types"][ext] += count
            
            # حساب الحجم الإجمالي بالميجابايت
            dir_info["statistics"]["total_size_mb"] = round(
                dir_info["statistics"]["total_size"] / (1024 * 1024), 3
            )
            
        except PermissionError:
            dir_info["error"] = "Permission denied"
        except Exception as e:
            dir_info["error"] = str(e)
        
        return dir_info
    
    def analyze_project_structure(self):
        """تحليل هيكل المشروع"""
        print("🔍 بدء الفحص الشامل للمشروع...")
        
        # فحص المجلدات الرئيسية
        main_directories = [
            "anubis", "tools", "archive", "n8n", "backup", 
            "temp", "augment-cht", "Universal-AI-Assistants", 
            "workspace", ".kiro"
        ]
        
        for dir_name in main_directories:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                print(f"📁 فحص مجلد: {dir_name}")
                self.scan_results["directories"][dir_name] = self.scan_directory(dir_path)
        
        # فحص الملفات في الجذر
        root_files = []
        for item in self.project_root.iterdir():
            if item.is_file():
                file_info = self.get_file_info(item)
                root_files.append(file_info)
        
        self.scan_results["root_files"] = root_files
        
        # حساب الإحصائيات الإجمالية
        self.calculate_global_statistics()
        
        print("✅ تم إكمال الفحص الشامل!")
    
    def calculate_global_statistics(self):
        """حساب الإحصائيات الإجمالية"""
        stats = {
            "total_directories": 0,
            "total_files": 0,
            "total_size": 0,
            "total_size_mb": 0,
            "file_types": {},
            "python_files": 0,
            "config_files": 0,
            "doc_files": 0,
            "script_files": 0,
            "largest_files": [],
            "largest_directories": []
        }
        
        def count_recursive(dir_info):
            if isinstance(dir_info, dict) and "statistics" in dir_info:
                stats["total_directories"] += 1
                stats["total_files"] += dir_info["statistics"]["total_files"]
                stats["total_size"] += dir_info["statistics"]["total_size"]
                stats["python_files"] += dir_info["statistics"]["python_files"]
                stats["config_files"] += dir_info["statistics"]["config_files"]
                stats["doc_files"] += dir_info["statistics"]["doc_files"]
                stats["script_files"] += dir_info["statistics"]["script_files"]
                
                # دمج أنواع الملفات
                for ext, count in dir_info["statistics"]["file_types"].items():
                    if ext not in stats["file_types"]:
                        stats["file_types"][ext] = 0
                    stats["file_types"][ext] += count
                
                # فحص المجلدات الفرعية
                for subdir in dir_info.get("subdirectories", {}).values():
                    count_recursive(subdir)
        
        # حساب إحصائيات جميع المجلدات
        for dir_info in self.scan_results["directories"].values():
            count_recursive(dir_info)
        
        # إضافة ملفات الجذر
        for file_info in self.scan_results.get("root_files", []):
            stats["total_files"] += 1
            if isinstance(file_info.get("size"), int):
                stats["total_size"] += file_info["size"]
            
            ext = file_info.get("extension", "")
            if ext not in stats["file_types"]:
                stats["file_types"][ext] = 0
            stats["file_types"][ext] += 1
        
        stats["total_size_mb"] = round(stats["total_size"] / (1024 * 1024), 3)
        self.scan_results["statistics"] = stats
    
    def generate_detailed_report(self):
        """إنشاء تقرير مفصل"""
        report_path = Path("anubis/workspace/reports/comprehensive_scan_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير نصي
        text_report_path = report_path.with_suffix('.md')
        self.generate_markdown_report(text_report_path)
        
        print(f"📋 تم حفظ التقرير الشامل في: {report_path}")
        print(f"📋 تم حفظ التقرير النصي في: {text_report_path}")
    
    def generate_markdown_report(self, report_path: Path):
        """إنشاء تقرير Markdown مفصل"""
        stats = self.scan_results["statistics"]
        
        content = f"""# 🔍 تقرير الفحص الشامل للمشروع

**تاريخ الفحص:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 الإحصائيات الإجمالية

- **📁 إجمالي المجلدات:** {stats['total_directories']}
- **📄 إجمالي الملفات:** {stats['total_files']}
- **💾 الحجم الإجمالي:** {stats['total_size_mb']} MB
- **🐍 ملفات Python:** {stats['python_files']}
- **⚙️ ملفات التكوين:** {stats['config_files']}
- **📚 ملفات التوثيق:** {stats['doc_files']}
- **📜 ملفات النصوص:** {stats['script_files']}

## 📁 تفصيل أنواع الملفات

| النوع | العدد | النسبة |
|-------|------|--------|
"""
        
        total_files = stats['total_files']
        for ext, count in sorted(stats['file_types'].items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_files * 100) if total_files > 0 else 0
            file_type = self.important_extensions.get(ext, 'Unknown')
            content += f"| {ext or 'بدون امتداد'} ({file_type}) | {count} | {percentage:.1f}% |\n"
        
        content += "\n## 📂 تفصيل المجلدات الرئيسية\n\n"
        
        for dir_name, dir_info in self.scan_results["directories"].items():
            if isinstance(dir_info, dict) and "statistics" in dir_info:
                stats_info = dir_info["statistics"]
                content += f"### 📁 {dir_name}/\n"
                content += f"- **الملفات:** {stats_info['total_files']}\n"
                content += f"- **الحجم:** {stats_info['total_size_mb']} MB\n"
                content += f"- **ملفات Python:** {stats_info['python_files']}\n"
                content += f"- **ملفات التكوين:** {stats_info['config_files']}\n"
                content += f"- **ملفات التوثيق:** {stats_info['doc_files']}\n\n"
        
        content += "\n---\n🔍 **تم إنشاء التقرير بواسطة فاحص المشروع الشامل**"
        
        report_path.write_text(content, encoding='utf-8')
    
    def run_comprehensive_scan(self):
        """تشغيل الفحص الشامل"""
        self.analyze_project_structure()
        self.generate_detailed_report()
        return self.scan_results


def main():
    """الدالة الرئيسية"""
    scanner = ComprehensiveProjectScanner()
    results = scanner.run_comprehensive_scan()
    
    print("\n🎉 تم إكمال الفحص الشامل!")
    print(f"📊 إجمالي المجلدات: {results['statistics']['total_directories']}")
    print(f"📄 إجمالي الملفات: {results['statistics']['total_files']}")
    print(f"💾 الحجم الإجمالي: {results['statistics']['total_size_mb']} MB")


if __name__ == "__main__":
    main()