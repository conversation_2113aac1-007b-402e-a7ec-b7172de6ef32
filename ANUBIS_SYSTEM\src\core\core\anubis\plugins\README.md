# 🔌 مجلد الإضافات - نظام أنوبيس
## Plugins Directory - Anubis System

**آخر تحديث**: 2025-07-16  
**الحالة**: ✅ نشط ومنظم  
**عدد الإضافات**: 3 إضافات أساسية  

---

## 📜 الوصف

مجلد `plugins` يحتوي على نظام الإضافات المرن لنظام أنوبيس. يسمح هذا النظام بإضافة وظائف جديدة للنظام دون تعديل الكود الأساسي، مما يوفر مرونة وقابلية توسع عالية.

## 📁 محتويات المجلد

### **الملفات الأساسية:**
- **`__init__.py`**: ملف تهيئة المجلد كحزمة Python
- **`base_plugin.py`**: الفئة الأساسية لجميع الإضافات
- **`plugin_manager.py`**: مدير الإضافات الرئيسي
- **`example_plugin.py`**: مثال على إضافة بسيطة

---

## 🔧 كيفية عمل نظام الإضافات

### **1. الفئة الأساسية (BasePlugin):**
```python
from plugins.base_plugin import BasePlugin

class MyPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.name = "My Custom Plugin"
        self.version = "1.0.0"
    
    def execute(self, *args, **kwargs):
        # تنفيذ وظيفة الإضافة
        return "نتيجة الإضافة"
```

### **2. مدير الإضافات (PluginManager):**
```python
from plugins.plugin_manager import PluginManager

# إنشاء مدير الإضافات
manager = PluginManager()

# تحميل جميع الإضافات
manager.load_plugins()

# تشغيل إضافة محددة
result = manager.execute_plugin("my_plugin", data="test")
```

---

## 🚀 كيفية الاستخدام

### **1. إنشاء إضافة جديدة:**
```python
# إنشاء ملف my_new_plugin.py
from plugins.base_plugin import BasePlugin

class MyNewPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.name = "My New Plugin"
        self.description = "وصف الإضافة الجديدة"
        self.version = "1.0.0"
        self.enabled = True
    
    def execute(self, data=None, **kwargs):
        """تنفيذ وظيفة الإضافة"""
        # منطق الإضافة هنا
        return {
            "status": "success",
            "message": "تم تنفيذ الإضافة بنجاح",
            "data": data
        }
    
    def validate_input(self, data):
        """التحقق من صحة المدخلات"""
        return True
```

### **2. تسجيل الإضافة:**
```python
from plugins.plugin_manager import PluginManager
from my_new_plugin import MyNewPlugin

manager = PluginManager()
manager.register_plugin(MyNewPlugin())
```

### **3. استخدام الإضافة:**
```python
# تشغيل الإضافة
result = manager.execute_plugin("My New Plugin", data="test_data")
print(result)
```

---

## 📋 الإضافات المتاحة

### **1. Example Plugin (`example_plugin.py`)**
- **الوصف**: مثال توضيحي لكيفية إنشاء إضافة
- **الوظيفة**: معالجة بيانات بسيطة وإرجاع نتيجة
- **الاستخدام**: للتعلم والاختبار

```python
from plugins.example_plugin import ExamplePlugin

plugin = ExamplePlugin()
result = plugin.execute(data="test")
```

---

## 🔧 ميزات نظام الإضافات

### **المرونة:**
- ✅ **تحميل ديناميكي**: تحميل الإضافات في وقت التشغيل
- ✅ **تفعيل/إلغاء تفعيل**: تحكم في حالة كل إضافة
- ✅ **إدارة التبعيات**: التحقق من المتطلبات
- ✅ **معالجة الأخطاء**: التعامل مع أخطاء الإضافات

### **الأمان:**
- ✅ **التحقق من المدخلات**: فحص البيانات قبل المعالجة
- ✅ **عزل الإضافات**: تشغيل آمن ومعزول
- ✅ **تسجيل العمليات**: تتبع جميع أنشطة الإضافات
- ✅ **إدارة الصلاحيات**: تحكم في وصول الإضافات

### **الأداء:**
- ✅ **تحميل كسول**: تحميل الإضافات عند الحاجة فقط
- ✅ **ذاكرة تخزين مؤقت**: حفظ نتائج الإضافات
- ✅ **تنفيذ متوازي**: دعم التشغيل المتوازي
- ✅ **مراقبة الأداء**: قياس أوقات التنفيذ

---

## 📊 إدارة الإضافات

### **عرض الإضافات المتاحة:**
```python
manager = PluginManager()
plugins = manager.list_plugins()

for plugin in plugins:
    print(f"الاسم: {plugin.name}")
    print(f"الإصدار: {plugin.version}")
    print(f"الحالة: {'مفعل' if plugin.enabled else 'معطل'}")
    print("---")
```

### **تفعيل/إلغاء تفعيل إضافة:**
```python
# تفعيل إضافة
manager.enable_plugin("plugin_name")

# إلغاء تفعيل إضافة
manager.disable_plugin("plugin_name")
```

### **إزالة إضافة:**
```python
manager.unregister_plugin("plugin_name")
```

---

## 🛠️ تطوير إضافات متقدمة

### **إضافة مع إعدادات:**
```python
class AdvancedPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.name = "Advanced Plugin"
        self.config = {
            "timeout": 30,
            "max_retries": 3,
            "debug_mode": False
        }
    
    def configure(self, **settings):
        """تكوين الإضافة"""
        self.config.update(settings)
    
    def execute(self, data=None, **kwargs):
        # استخدام الإعدادات
        timeout = self.config.get("timeout", 30)
        # منطق الإضافة
        return result
```

### **إضافة مع تبعيات:**
```python
class DatabasePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.name = "Database Plugin"
        self.dependencies = ["mysql-connector-python"]
    
    def check_dependencies(self):
        """فحص التبعيات"""
        try:
            import mysql.connector
            return True
        except ImportError:
            return False
    
    def execute(self, query=None, **kwargs):
        if not self.check_dependencies():
            raise Exception("التبعيات المطلوبة غير متوفرة")
        
        # تنفيذ العملية
        return result
```

---

## 📈 أفضل الممارسات

### **عند تطوير الإضافات:**
1. **وراثة من BasePlugin**: استخدم دائماً الفئة الأساسية
2. **التحقق من المدخلات**: فحص البيانات قبل المعالجة
3. **معالجة الأخطاء**: استخدم try/except بشكل مناسب
4. **التوثيق**: اكتب وصف واضح للإضافة
5. **الاختبار**: اختبر الإضافة قبل النشر

### **عند استخدام الإضافات:**
1. **فحص التوفر**: تأكد من وجود الإضافة قبل الاستخدام
2. **معالجة الأخطاء**: تعامل مع فشل الإضافات
3. **مراقبة الأداء**: راقب أوقات تنفيذ الإضافات
4. **التحديث المنتظم**: حدث الإضافات بانتظام

---

## 🔍 استكشاف الأخطاء

### **مشاكل شائعة:**

#### **إضافة لا تُحمل:**
```python
# فحص الأخطاء
try:
    manager.load_plugin("plugin_name")
except Exception as e:
    print(f"خطأ في تحميل الإضافة: {e}")
```

#### **خطأ في التنفيذ:**
```python
# تنفيذ آمن
try:
    result = manager.execute_plugin("plugin_name", data="test")
except Exception as e:
    print(f"خطأ في تنفيذ الإضافة: {e}")
    result = None
```

### **تشخيص المشاكل:**
```python
# فحص حالة الإضافة
plugin_info = manager.get_plugin_info("plugin_name")
print(f"الحالة: {plugin_info['status']}")
print(f"آخر خطأ: {plugin_info['last_error']}")
```

---

## 🚀 الخطوات التالية

### **للمطورين:**
1. **إنشاء إضافات مخصصة** لاحتياجات محددة
2. **تطوير واجهة إدارة** للإضافات
3. **إضافة نظام تحديث** تلقائي للإضافات
4. **تطوير متجر إضافات** لمشاركة الإضافات

### **للمستخدمين:**
1. **استكشاف الإضافات المتاحة**
2. **تجربة الإضافات المختلفة**
3. **تقديم ملاحظات** للمطورين
4. **طلب إضافات جديدة** حسب الحاجة

---

<div align="center">

**🔌 مجلد الإضافات - نظام أنوبيس**

**نظام إضافات مرن وقابل للتوسع**

[![Plugins](https://img.shields.io/badge/Plugins-3%20Active-blue.svg)](README.md)
[![Extensible](https://img.shields.io/badge/System-Extensible-brightgreen.svg)](README.md)
[![Framework](https://img.shields.io/badge/Framework-Flexible-gold.svg)](README.md)

**جزء من نظام أنوبيس المتكامل**

</div>
