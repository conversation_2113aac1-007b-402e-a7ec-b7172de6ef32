#!/usr/bin/env python3
"""
🤖 الفئة الأساسية للوكلاء الأذكياء
Base Agent Class for Universal AI Assistants
"""

import json
import os
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# استيراد نظام الذكاء الاصطناعي
try:
    from ai_integration import ai_manager
except ImportError:
    ai_manager = None


class BaseAgent(ABC):
    """الفئة الأساسية لجميع الوكلاء الأذكياء"""

    def __init__(self, project_path: str, config: Dict[str, Any], verbose: bool = False):
        """
        تهيئة الوكيل الأساسي

        Args:
            project_path: مسار المشروع
            config: إعدادات الوكيل
            verbose: عرض تفاصيل أكثر
        """
        self.project_path = Path(project_path)
        self.config = config
        self.verbose = verbose

        # معلومات الوكيل
        self.agent_name = self.__class__.__name__
        self.agent_type = self.get_agent_type()
        self.version = "1.0.0"

        # مجلدات العمل
        self.workspace_dir = Path("workspace")
        self.logs_dir = self.workspace_dir / "logs"
        self.reports_dir = self.workspace_dir / "reports"

        # إنشاء المجلدات
        self._create_directories()

        # ملف السجل
        self.log_file = self.logs_dir / f"{self.agent_type}_agent.log"

        # حالة الوكيل
        self.is_initialized = False
        self.last_run = None
        self.status = "ready"

        # تهيئة الوكيل
        self._initialize()

        # تهيئة الذكاء الاصطناعي
        self.ai_enabled = ai_manager is not None and self._check_ai_availability()

    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        self.workspace_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)

    def _initialize(self):
        """تهيئة الوكيل"""
        try:
            self.log_action("تهيئة الوكيل", f"النوع: {self.agent_type}")
            self.initialize_agent()
            self.is_initialized = True
            self.status = "initialized"
            self.log_action("تم تهيئة الوكيل بنجاح")
        except Exception as e:
            self.status = "error"
            self.log_action("خطأ في تهيئة الوكيل", str(e))
            raise

    @abstractmethod
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        pass

    @abstractmethod
    def initialize_agent(self):
        """تهيئة الوكيل المخصص"""
        pass

    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل التحليل - تنفيذ افتراضي"""
        return {
            "status": "completed",
            "agent_type": self.agent_type,
            "timestamp": datetime.now().isoformat(),
            "message": "تم تشغيل التحليل الأساسي",
        }

    def log_action(self, action: str, details: str = ""):
        """تسجيل العمليات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {self.agent_name}: {action}"
        if details:
            log_entry += f" - {details}"

        # عرض في وحدة التحكم
        if self.verbose:
            print(f"🤖 {log_entry}")

        # كتابة في ملف السجل
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"⚠️ خطأ في كتابة السجل: {e}")

    def get_status(self) -> Dict[str, Any]:
        """إرجاع حالة الوكيل"""
        return {
            "agent_name": self.agent_name,
            "agent_type": self.agent_type,
            "version": self.version,
            "status": self.status,
            "is_initialized": self.is_initialized,
            "last_run": self.last_run,
            "project_path": str(self.project_path),
            "log_file": str(self.log_file),
        }

    def run(self) -> Dict[str, Any]:
        """تشغيل الوكيل"""
        if not self.is_initialized:
            raise RuntimeError(f"الوكيل {self.agent_name} غير مهيأ")

        try:
            self.status = "running"
            self.log_action("بدء تشغيل الوكيل")

            # تشغيل التحليل
            result = self.run_analysis()

            # تحديث الحالة
            self.last_run = datetime.now().isoformat()
            self.status = "completed"

            # إضافة معلومات الوكيل للنتيجة
            result["agent_info"] = self.get_status()
            result["success"] = True
            result["timestamp"] = self.last_run

            self.log_action("تم تشغيل الوكيل بنجاح")
            return result

        except Exception as e:
            self.status = "error"
            self.log_action("خطأ في تشغيل الوكيل", str(e))

            return {
                "success": False,
                "error": str(e),
                "agent_info": self.get_status(),
                "timestamp": datetime.now().isoformat(),
            }

    def save_report(self, data: Dict[str, Any], filename: str = None) -> str:
        """حفظ تقرير"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.agent_type}_report_{timestamp}.json"

        report_file = self.reports_dir / filename

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            self.log_action("تم حفظ التقرير", str(report_file))
            return str(report_file)

        except Exception as e:
            self.log_action("خطأ في حفظ التقرير", str(e))
            raise

    def check_project_structure(self) -> Dict[str, Any]:
        """فحص هيكل المشروع"""
        structure = {
            "project_exists": self.project_path.exists(),
            "is_directory": self.project_path.is_dir(),
            "files_count": 0,
            "directories_count": 0,
            "python_files": [],
            "config_files": [],
            "data_files": [],
        }

        if structure["project_exists"] and structure["is_directory"]:
            try:
                for item in self.project_path.rglob("*"):
                    if item.is_file():
                        structure["files_count"] += 1

                        # تصنيف الملفات
                        if item.suffix == ".py":
                            structure["python_files"].append(
                                str(item.relative_to(self.project_path))
                            )
                        elif item.suffix in [".json", ".yaml", ".yml", ".ini", ".cfg"]:
                            structure["config_files"].append(
                                str(item.relative_to(self.project_path))
                            )
                        elif item.suffix in [".csv", ".xlsx", ".db", ".sqlite"]:
                            structure["data_files"].append(str(item.relative_to(self.project_path)))

                    elif item.is_dir():
                        structure["directories_count"] += 1

            except Exception as e:
                structure["error"] = str(e)

        return structure

    def detect_project_type(self) -> str:
        """اكتشاف نوع المشروع"""
        # فحص ملفات المشروع
        files = list(self.project_path.glob("*.py"))

        # البحث عن مؤشرات نوع المشروع
        for file in files:
            try:
                content = file.read_text(encoding="utf-8")

                if "streamlit" in content.lower():
                    return "streamlit"
                elif "django" in content.lower():
                    return "django"
                elif "fastapi" in content.lower():
                    return "fastapi"
                elif "flask" in content.lower():
                    return "flask"

            except Exception:
                continue

        # فحص ملفات التكوين
        if (self.project_path / "manage.py").exists():
            return "django"
        elif (self.project_path / "requirements.txt").exists():
            try:
                req_content = (self.project_path / "requirements.txt").read_text()
                if "streamlit" in req_content:
                    return "streamlit"
                elif "fastapi" in req_content:
                    return "fastapi"
                elif "flask" in req_content:
                    return "flask"
            except Exception:
                pass

        return "custom"

    def get_project_info(self) -> Dict[str, Any]:
        """الحصول على معلومات المشروع"""
        return {
            "path": str(self.project_path),
            "name": self.project_path.name,
            "type": self.detect_project_type(),
            "structure": self.check_project_structure(),
            "agent_type": self.agent_type,
            "analysis_time": datetime.now().isoformat(),
        }

    def _check_ai_availability(self) -> bool:
        """فحص توفر الذكاء الاصطناعي"""
        if not ai_manager:
            return False

        available_providers = ai_manager.get_available_providers()
        return len(available_providers) > 0

    def get_ai_analysis(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """الحصول على تحليل ذكي من نموذج الذكاء الاصطناعي"""
        if not hasattr(self, "ai_enabled") or not self.ai_enabled:
            return "الذكاء الاصطناعي غير متاح"

        # إضافة معلومات الوكيل للسياق
        agent_context = {
            "agent_type": self.agent_type,
            "agent_name": self.agent_name,
            "project_path": str(self.project_path),
            "timestamp": datetime.now().isoformat(),
        }

        if context:
            agent_context.update(context)

        try:
            response = ai_manager.generate_ai_response(prompt, agent_context)
            self.log_action("تحليل ذكي", f"تم الحصول على استجابة: {len(response)} حرف")
            return response
        except Exception as e:
            self.log_action("خطأ في التحليل الذكي", str(e))
            return f"خطأ في التحليل الذكي: {e}"

    def get_smart_suggestions(self, data: Dict[str, Any]) -> List[str]:
        """الحصول على اقتراحات ذكية"""
        if not hasattr(self, "ai_enabled") or not self.ai_enabled:
            return ["الذكاء الاصطناعي غير متاح للاقتراحات"]

        prompt = f"بناءً على البيانات التالية، قدم اقتراحات ذكية للتحسين:\n{json.dumps(data, ensure_ascii=False, indent=2)}"

        response = self.get_ai_analysis(prompt)

        # محاولة استخراج الاقتراحات من الاستجابة
        suggestions = []
        lines = response.split("\n")
        for line in lines:
            line = line.strip()
            if line.startswith("-") or line.startswith("•") or line.startswith("*"):
                suggestions.append(line[1:].strip())
            elif line and not line.startswith("#") and len(line) > 10:
                suggestions.append(line)

        return suggestions[:10]  # أقصى 10 اقتراحات

    def is_ai_enabled(self) -> bool:
        """فحص ما إذا كان الذكاء الاصطناعي مفعل"""
        return hasattr(self, "ai_enabled") and self.ai_enabled

    def get_ai_status(self) -> Dict[str, Any]:
        """الحصول على حالة الذكاء الاصطناعي"""
        if not ai_manager:
            return {"available": False, "reason": "نظام الذكاء الاصطناعي غير مثبت"}

        return {
            "available": self.is_ai_enabled(),
            "status": ai_manager.get_status(),
            "providers": ai_manager.get_available_providers(),
        }
