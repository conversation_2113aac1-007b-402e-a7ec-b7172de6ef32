#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 فحص المكتبات المثبتة
Check Installed Packages

فحص شامل للمكتبات المثبتة في البيئة الحالية
"""

import sys
import importlib
from datetime import datetime

def check_package(package_name, import_name=None):
    """فحص وجود مكتبة معينة"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'غير محدد')
        return True, version
    except ImportError:
        return False, None

def main():
    """الدالة الرئيسية لفحص المكتبات"""
    print("🔍 فحص المكتبات المثبتة في نظام أنوبيس")
    print("=" * 60)
    print(f"🐍 Python Version: {sys.version}")
    print(f"📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        # المكتبات الأساسية
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("requests", "requests"),
        ("jinja2", "jinja2"),
        ("aiofiles", "aiofiles"),
        ("httpx", "httpx"),
        
        # قواعد البيانات
        ("mysql-connector-python", "mysql.connector"),
        ("aiosqlite", "aiosqlite"),
        ("sqlalchemy", "sqlalchemy"),
        
        # معالجة البيانات
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        
        # الذكاء الاصطناعي
        ("openai", "openai"),
        ("transformers", "transformers"),
        ("torch", "torch"),
        
        # أدوات النظام
        ("psutil", "psutil"),
        ("pathlib", "pathlib"),
        ("json", "json"),
        ("os", "os"),
        ("sys", "sys"),
        ("datetime", "datetime"),
        ("asyncio", "asyncio"),
        ("logging", "logging"),
        
        # أدوات إضافية
        ("click", "click"),
        ("colorama", "colorama"),
        ("tqdm", "tqdm"),
        ("pytest", "pytest"),
    ]
    
    installed_count = 0
    missing_packages = []
    
    print("\n📦 فحص المكتبات:")
    print("-" * 60)
    
    for package_name, import_name in required_packages:
        is_installed, version = check_package(package_name, import_name)
        
        if is_installed:
            status = "✅"
            installed_count += 1
            version_info = f"v{version}" if version != "غير محدد" else "غير محدد"
            print(f"{status} {package_name:<25} {version_info}")
        else:
            status = "❌"
            missing_packages.append(package_name)
            print(f"{status} {package_name:<25} غير مثبت")
    
    print("-" * 60)
    print(f"📊 الإحصائيات:")
    print(f"   إجمالي المكتبات المفحوصة: {len(required_packages)}")
    print(f"   ✅ مثبت: {installed_count}")
    print(f"   ❌ مفقود: {len(missing_packages)}")
    print(f"   📈 نسبة التثبيت: {(installed_count/len(required_packages))*100:.1f}%")
    
    if missing_packages:
        print(f"\n❌ المكتبات المفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"   pip install {' '.join(missing_packages)}")
    
    # فحص المكتبات الأساسية للنظام
    print(f"\n🏺 فحص متطلبات نظام أنوبيس الأساسية:")
    print("-" * 60)
    
    core_requirements = [
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("sqlalchemy", "sqlalchemy"),
        ("aiosqlite", "aiosqlite"),
    ]
    
    core_missing = []
    for package_name, import_name in core_requirements:
        is_installed, version = check_package(package_name, import_name)
        if not is_installed:
            core_missing.append(package_name)
    
    if not core_missing:
        print("✅ جميع المتطلبات الأساسية مثبتة!")
        print("🚀 يمكن تشغيل نظام أنوبيس")
    else:
        print("❌ بعض المتطلبات الأساسية مفقودة:")
        for package in core_missing:
            print(f"   - {package}")
        print("⚠️ يجب تثبيت المتطلبات الأساسية قبل تشغيل النظام")
    
    # فحص إضافي للمكتبات المتقدمة
    print(f"\n🔬 فحص المكتبات المتقدمة:")
    print("-" * 60)
    
    advanced_packages = [
        ("mysql-connector-python", "mysql.connector"),
        ("redis", "redis"),
        ("prometheus-client", "prometheus_client"),
        ("docker", "docker"),
        ("chromadb", "chromadb"),
    ]
    
    for package_name, import_name in advanced_packages:
        is_installed, version = check_package(package_name, import_name)
        status = "✅" if is_installed else "⚠️"
        note = "متاح" if is_installed else "اختياري"
        print(f"{status} {package_name:<25} {note}")
    
    print("\n" + "=" * 60)
    print("🏺 انتهى فحص المكتبات")
    
    return len(missing_packages) == 0

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ جميع المكتبات الأساسية متاحة!")
    else:
        print("⚠️ بعض المكتبات مفقودة - راجع القائمة أعلاه")
