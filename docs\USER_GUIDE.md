# 📚 دليل المستخدم - نظام أنوبيس
# User Guide - Anubis System

## 🎯 **مقدمة**

مرحباً بك في نظام أنوبيس! هذا الدليل سيساعدك على فهم واستخدام جميع ميزات النظام بفعالية.

## 🚀 **البدء السريع**

### 1. **التثبيت الأول**

```bash
# استنساخ المشروع
git clone https://github.com/your-username/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# تشغيل الإعداد السريع
python scripts/quick_setup.py

# تفعيل البيئة الافتراضية
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac
```

### 2. **التشغيل الأول**

```bash
# الطريقة الأولى: تشغيل مباشر
python main.py

# الطريقة الثانية: استخدام CLI
python src/cli/anubis_cli.py system start

# الطريقة الثالثة: Docker
python src/cli/anubis_cli.py docker up
```

### 3. **الوصول للنظام**

- **الواجهة الرئيسية:** http://localhost:8000
- **توثيق API:** http://localhost:8000/docs
- **N8N (الأتمتة):** http://localhost:5678
- **Grafana (المراقبة):** http://localhost:3000

## 🖥️ **استخدام CLI الموحد**

### **الأوامر الأساسية**

```bash
# عرض المساعدة العامة
python src/cli/anubis_cli.py --help

# عرض إصدار النظام
python src/cli/anubis_cli.py --version
```

### **إدارة النظام**

```bash
# تشغيل النظام
python src/cli/anubis_cli.py system start

# إيقاف النظام
python src/cli/anubis_cli.py system stop

# إعادة تشغيل النظام
python src/cli/anubis_cli.py system restart

# عرض حالة النظام
python src/cli/anubis_cli.py system status

# عرض حالة مفصلة (JSON)
python src/cli/anubis_cli.py system status --format json
```

### **إدارة Docker**

```bash
# تشغيل جميع الخدمات
python src/cli/anubis_cli.py docker up

# تشغيل خدمة محددة
python src/cli/anubis_cli.py docker up --service anubis-core

# إيقاف الخدمات
python src/cli/anubis_cli.py docker down

# عرض سجلات الحاويات
python src/cli/anubis_cli.py docker logs

# عرض الحاويات النشطة
python src/cli/anubis_cli.py docker ps
```

### **أدوات التطوير**

```bash
# فحص جودة الكود
python src/cli/anubis_cli.py dev lint

# تنسيق الكود
python src/cli/anubis_cli.py dev format

# تشغيل الاختبارات
python src/cli/anubis_cli.py dev test

# تشغيل الاختبارات مع تقرير التغطية
python src/cli/anubis_cli.py dev test --coverage
```

### **إدارة قواعد البيانات**

```bash
# تهيئة قاعدة البيانات
python src/cli/anubis_cli.py db init

# تشغيل migrations
python src/cli/anubis_cli.py db migrate

# إنشاء نسخة احتياطية
python src/cli/anubis_cli.py db backup
```

### **أدوات المراقبة**

```bash
# مراقبة النظام في الوقت الفعلي
python src/cli/anubis_cli.py monitor watch

# فحص صحة شامل
python src/cli/anubis_cli.py monitor health

# مراقبة مع فترة تحديث مخصصة
python src/cli/anubis_cli.py monitor watch --interval 10
```

## 🤖 **استخدام خدمات الذكاء الاصطناعي**

### **إعداد مفاتيح API**

1. **إنشاء ملف .env:**
```bash
cp .env.example .env
```

2. **إضافة مفاتيح API:**
```env
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Google Gemini
GOOGLE_API_KEY=your-google-api-key

# Anthropic Claude
ANTHROPIC_API_KEY=your-anthropic-api-key
```

### **استخدام API**

```bash
# إرسال طلب للذكاء الاصطناعي
curl -X POST "http://localhost:8000/api/v1/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "prompt": "اكتب قصة قصيرة عن الذكاء الاصطناعي",
       "model": "gpt-3.5-turbo"
     }'
```

## 🔄 **أتمتة سير العمل مع N8N**

### **الوصول لـ N8N**

1. **تشغيل N8N:**
```bash
python src/cli/anubis_cli.py docker up --service anubis-n8n
```

2. **فتح واجهة N8N:**
   - الرابط: http://localhost:5678
   - المستخدم: admin
   - كلمة المرور: (من ملف .env)

### **إنشاء سير عمل بسيط**

1. **إنشاء Workflow جديد**
2. **إضافة Trigger** (مثل Webhook)
3. **إضافة Action** (مثل HTTP Request)
4. **ربط العقد** وحفظ السير

## 📊 **المراقبة مع Grafana**

### **الوصول لـ Grafana**

1. **تشغيل Grafana:**
```bash
python src/cli/anubis_cli.py docker up --service anubis-grafana
```

2. **فتح لوحة Grafana:**
   - الرابط: http://localhost:3000
   - المستخدم: admin
   - كلمة المرور: (من ملف .env)

### **لوحات المراقبة المتاحة**

- **System Overview:** نظرة عامة على النظام
- **API Performance:** أداء واجهات API
- **Database Metrics:** مقاييس قاعدة البيانات
- **Docker Containers:** حالة الحاويات

## 🛡️ **الأمان والعزل**

### **إعدادات الأمان**

1. **تغيير كلمات المرور الافتراضية**
2. **استخدام HTTPS في الإنتاج**
3. **تفعيل Firewall**
4. **تحديث النظام بانتظام**

### **العزل بـ Docker**

```bash
# تشغيل في وضع العزل الكامل
python src/cli/anubis_cli.py docker up --isolation-mode full

# فحص حالة العزل
python src/cli/anubis_cli.py monitor health
```

## 🔧 **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها**

#### **1. فشل في تشغيل النظام**
```bash
# فحص السجلات
python src/cli/anubis_cli.py docker logs

# فحص حالة النظام
python src/cli/anubis_cli.py system status

# إعادة تشغيل
python src/cli/anubis_cli.py system restart
```

#### **2. مشاكل قاعدة البيانات**
```bash
# فحص اتصال قاعدة البيانات
python src/cli/anubis_cli.py db test-connection

# إعادة تهيئة قاعدة البيانات
python src/cli/anubis_cli.py db reset
```

#### **3. مشاكل Docker**
```bash
# تنظيف الحاويات
docker system prune

# إعادة بناء الصور
python src/cli/anubis_cli.py docker rebuild
```

### **الحصول على المساعدة**

- **السجلات:** `logs/anubis.log`
- **التقارير:** `docs/reports/`
- **الدعم:** [GitHub Issues](https://github.com/your-username/Universal-AI-Assistants/issues)

## 📈 **نصائح للأداء الأمثل**

1. **استخدم SSD** لتحسين أداء قاعدة البيانات
2. **خصص ذاكرة كافية** (4GB على الأقل)
3. **راقب استخدام الموارد** بانتظام
4. **نظف السجلات القديمة** دورياً
5. **حدث النظام** بانتظام

## 🎓 **موارد إضافية**

- **[API Documentation](http://localhost:8000/docs)** - توثيق واجهات API
- **[Developer Guide](DEVELOPER_GUIDE.md)** - دليل المطورين
- **[Architecture Overview](ARCHITECTURE.md)** - نظرة على البنية
- **[FAQ](FAQ.md)** - الأسئلة الشائعة
