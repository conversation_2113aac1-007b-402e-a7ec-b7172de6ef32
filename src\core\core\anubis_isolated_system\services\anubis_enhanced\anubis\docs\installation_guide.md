# 🚀 دليل التثبيت
# Installation Guide

## 📋 متطلبات النظام

### الحد الأدنى للمتطلبات:
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Linux Ubuntu 18.04+
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM
- **التخزين**: 1 GB مساحة فارغة
- **الشبكة**: اتصال إنترنت لتحميل التبعيات

### المتطلبات الموصى بها:
- **Python**: الإصدار 3.10 أو أحدث
- **الذاكرة**: 8 GB RAM أو أكثر
- **التخزين**: 5 GB مساحة فارغة (SSD مفضل)
- **المعالج**: معالج متعدد النوى

## 🔧 طرق التثبيت

### الطريقة 1: التثبيت من GitHub (موصى به)

#### 1. استنساخ المستودع:
```bash
git clone https://github.com/your-repo/Universal-AI-Assistants.git
cd Universal-AI-Assistants
```

#### 2. إنشاء بيئة افتراضية:
```bash
# على Windows
python -m venv venv
venv\Scripts\activate

# على macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

#### 3. تثبيت التبعيات:
```bash
pip install -r requirements.txt
```

#### 4. التحقق من التثبيت:
```bash
python main.py --help
```

### الطريقة 2: التثبيت المباشر

#### 1. تحميل الملفات:
- قم بتحميل أحدث إصدار من [صفحة الإصدارات](https://github.com/your-repo/releases)
- فك ضغط الملف في المجلد المطلوب

#### 2. تثبيت Python:
- تأكد من تثبيت Python 3.8+ من [python.org](https://python.org)

#### 3. تثبيت التبعيات:
```bash
cd Universal-AI-Assistants
pip install -r requirements.txt
```

## ⚙️ إعداد البيئة

### 1. إعداد متغيرات البيئة (اختياري):
```bash
# إضافة مسار النظام إلى PATH
export PATH=$PATH:/path/to/Universal-AI-Assistants

# إعداد مجلد العمل
export UNIVERSAL_AI_WORKSPACE=/path/to/workspace
```

### 2. إعداد قواعد البيانات (اختياري):
```bash
# لـ MySQL
pip install mysql-connector-python

# لـ PostgreSQL  
pip install psycopg2-binary

# SQLite مدمج مع Python
```

### 3. إعداد أدوات إضافية (اختياري):
```bash
# أدوات فحص الكود
pip install flake8 pylint black

# أدوات الاختبار
pip install pytest pytest-cov

# أدوات التوثيق
pip install sphinx
```

## 🧪 اختبار التثبيت

### 1. اختبار أساسي:
```bash
python main.py --version
```

### 2. اختبار شامل:
```bash
python test_system.py
```

### 3. اختبار مع مشروع مثال:
```bash
# إنشاء مشروع مثال
mkdir test_project
cd test_project
echo "print('Hello World')" > main.py

# تحليل المشروع
cd ..
python main.py --project test_project --analyze
```

## 🔍 استكشاف مشاكل التثبيت

### مشكلة: خطأ في تثبيت التبعيات
```bash
# الحل: تحديث pip
python -m pip install --upgrade pip

# إعادة تثبيت التبعيات
pip install -r requirements.txt --force-reinstall
```

### مشكلة: خطأ في استيراد الوحدات
```bash
# الحل: التأكد من تفعيل البيئة الافتراضية
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# التحقق من مسار Python
which python
```

### مشكلة: خطأ في الأذونات
```bash
# على Linux/macOS
chmod +x main.py

# تشغيل بصلاحيات المستخدم
python main.py --project . --analyze
```

### مشكلة: نفاد الذاكرة
```bash
# تقليل حجم التحليل
python main.py --project . --agent memory --verbose
```

## 🐳 التثبيت باستخدام Docker (متقدم)

### 1. إنشاء Dockerfile:
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py", "--help"]
```

### 2. بناء الصورة:
```bash
docker build -t universal-ai-assistants .
```

### 3. تشغيل الحاوية:
```bash
docker run -v $(pwd)/projects:/app/projects universal-ai-assistants \
  python main.py --project /app/projects/my_project --analyze
```

## 📦 التثبيت على أنظمة مختلفة

### Windows:
```powershell
# تثبيت Python من Microsoft Store أو python.org
# فتح PowerShell كمدير
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# استنساخ المشروع
git clone https://github.com/your-repo/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python -m venv venv
venv\Scripts\Activate.ps1

# تثبيت التبعيات
pip install -r requirements.txt
```

### macOS:
```bash
# تثبيت Homebrew (إذا لم يكن مثبت)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت Python
brew install python

# استنساخ المشروع
git clone https://github.com/your-repo/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت التبعيات
pip install -r requirements.txt
```

### Ubuntu/Debian:
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Python و pip
sudo apt install python3 python3-pip python3-venv git -y

# استنساخ المشروع
git clone https://github.com/your-repo/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت التبعيات
pip install -r requirements.txt
```

## ✅ التحقق من نجاح التثبيت

### 1. فحص الإصدار:
```bash
python main.py --version
# يجب أن يظهر: Universal AI Assistants v1.0.0
```

### 2. فحص الوكلاء:
```bash
python -c "from agents import AVAILABLE_AGENTS; print(list(AVAILABLE_AGENTS.keys()))"
# يجب أن يظهر قائمة بالوكلاء المتاحين
```

### 3. فحص التكوين:
```bash
python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); print('✅ التكوين يعمل')"
```

### 4. تشغيل اختبار شامل:
```bash
python quick_start.py
```

## 🔄 التحديث

### تحديث من GitHub:
```bash
cd Universal-AI-Assistants
git pull origin main
pip install -r requirements.txt --upgrade
```

### تحديث التبعيات فقط:
```bash
pip install -r requirements.txt --upgrade
```

## 🗑️ إلغاء التثبيت

### إزالة البيئة الافتراضية:
```bash
deactivate  # إلغاء تفعيل البيئة
rm -rf venv  # حذف مجلد البيئة
```

### إزالة المشروع:
```bash
cd ..
rm -rf Universal-AI-Assistants
```

## 📞 الحصول على المساعدة

إذا واجهت مشاكل في التثبيت:

1. **راجع الأسئلة الشائعة**: [FAQ](faq.md)
2. **ابحث في المشاكل المعروفة**: [GitHub Issues](https://github.com/your-repo/issues)
3. **أنشئ مشكلة جديدة**: مع تفاصيل النظام ورسالة الخطأ
4. **تواصل مع الدعم**: <EMAIL>

---

**نصائح للتثبيت الناجح:**
- ✅ استخدم بيئة افتراضية دائماً
- ✅ تأكد من إصدار Python المطلوب
- ✅ اقرأ رسائل الخطأ بعناية
- ✅ احتفظ بنسخة احتياطية من إعداداتك
