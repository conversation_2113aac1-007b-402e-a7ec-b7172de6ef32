# 🧪 اختبارات نظام المساعدين الذكيين العالمي
# Tests for Universal AI Assistants System

## 📋 نظرة عامة

هذا المجلد يحتوي على جميع اختبارات نظام المساعدين الذكيين العالمي، بما في ذلك اختبارات الوحدة واختبارات التكامل.

## 📁 ملفات الاختبار

### 🧪 الاختبارات الأساسية:
- `test_system.py` - اختبارات النظام الأساسي
- `test_agents.py` - اختبارات تحميل الوكلاء
- `run_all_tests.py` - تشغيل جميع الاختبارات

### 🤖 اختبارات الوكلاء:
- `test_error_detector.py` - اختبارات وكيل كشف الأخطاء
- `test_project_analyzer.py` - اختبارات وكيل تحليل المشاريع

### 🔌 اختبارات الإضافات:
- `test_plugins.py` - اختبارات نظام الإضافات

### 💎 اختبارات مشروع Crestal Diamond:
- `test_jewelry_database.py` - اختبارات قاعدة بيانات المجوهرات
- `test_jewelry_logic.py` - اختبارات منطق ورشة المجوهرات

## 🚀 كيفية تشغيل الاختبارات

### تشغيل جميع الاختبارات:
```bash
cd tests
python run_all_tests.py
```

### تشغيل اختبار محدد:
```bash
cd tests
python test_system.py
python test_error_detector.py
python test_project_analyzer.py
python test_plugins.py
```

### تشغيل الاختبارات من المجلد الرئيسي:
```bash
# من مجلد Universal-AI-Assistants
python tests/run_all_tests.py
python tests/test_system.py
```

### تشغيل الاختبارات باستخدام pytest:
```bash
cd tests
pytest -v
pytest test_error_detector.py -v
pytest --cov=../agents --cov=../plugins
```

## 📊 تقارير الاختبارات

عند تشغيل `run_all_tests.py`، يتم إنشاء تقرير مفصل في:
```
../workspace/reports/test_report_YYYYMMDD_HHMMSS.txt
```

## 🔧 متطلبات الاختبارات

### المتطلبات الأساسية:
- Python 3.8+
- جميع تبعيات المشروع مثبتة

### أدوات الاختبار الإضافية:
```bash
pip install pytest pytest-cov
```

## 📋 قائمة الاختبارات

### ✅ الاختبارات المكتملة:
- [x] اختبارات النظام الأساسي
- [x] اختبارات تحميل الوكلاء
- [x] اختبارات وكيل كشف الأخطاء
- [x] اختبارات وكيل تحليل المشاريع
- [x] اختبارات نظام الإضافات
- [x] اختبارات التكامل

### 🔄 اختبارات مستقبلية:
- [ ] اختبارات الأداء
- [ ] اختبارات الأمان
- [ ] اختبارات واجهة المستخدم
- [ ] اختبارات التحميل

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في الاستيراد:
```
ModuleNotFoundError: No module named 'agents'
```
**الحل**: تأكد من تشغيل الاختبارات من مجلد `tests` أو استخدام المسارات النسبية الصحيحة.

#### خطأ في المسارات:
```
FileNotFoundError: [Errno 2] No such file or directory: 'main.py'
```
**الحل**: تأكد من أن المسارات النسبية صحيحة (`../main.py` بدلاً من `main.py`).

#### فشل في الاختبارات:
```
FAILED test_error_detector.py::TestErrorDetectorAgent::test_syntax_error_detection
```
**الحل**: راجع رسائل الخطأ المفصلة وتأكد من أن جميع التبعيات مثبتة.

## 📈 إحصائيات الاختبارات

### آخر تشغيل ناجح:
- **إجمالي الاختبارات**: 6
- **نجح**: 6
- **فشل**: 0
- **معدل النجاح**: 100%
- **الوقت الإجمالي**: ~3 ثواني

## 🤝 المساهمة في الاختبارات

### إضافة اختبار جديد:
1. أنشئ ملف `test_new_feature.py`
2. اتبع نمط الاختبارات الموجودة
3. أضف الاختبار إلى `run_all_tests.py`
4. تأكد من أن جميع الاختبارات تمر

### معايير الاختبارات:
- ✅ استخدم أسماء وصفية للاختبارات
- ✅ اكتب تعليقات باللغة العربية
- ✅ اختبر الحالات العادية والحدية
- ✅ اختبر معالجة الأخطاء
- ✅ نظف الموارد بعد الاختبار

---

**ملاحظة**: جميع الاختبارات تستخدم مجلدات مؤقتة ولا تؤثر على ملفات المشروع الأصلية.
