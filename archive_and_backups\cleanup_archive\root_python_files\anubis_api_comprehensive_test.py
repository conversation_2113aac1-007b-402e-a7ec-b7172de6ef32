#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 اختبار شامل لـ API أنوبيس المعزول
Anubis Isolated API Comprehensive Test
"""

import requests
import json
import time
from datetime import datetime

class AnubisAPITester:
    """فاحص API أنوبيس الشامل"""
    
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.test_results = {}
        
    def test_endpoint(self, endpoint, description, expected_status=200):
        """اختبار نقطة API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            response_time = (time.time() - start_time) * 1000
            
            success = response.status_code == expected_status
            
            result = {
                "url": url,
                "status_code": response.status_code,
                "response_time_ms": round(response_time, 2),
                "success": success,
                "timestamp": datetime.now().isoformat()
            }
            
            if success:
                try:
                    result["data"] = response.json()
                except:
                    result["data"] = response.text[:200]
                print(f"✅ {description}: {response.status_code} ({response_time:.1f}ms)")
            else:
                result["error"] = response.text[:200]
                print(f"❌ {description}: {response.status_code} ({response_time:.1f}ms)")
            
            self.test_results[endpoint] = result
            return success, result
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {description}: اتصال مرفوض")
            self.test_results[endpoint] = {"error": "connection_refused"}
            return False, None
        except Exception as e:
            print(f"❌ {description}: خطأ - {e}")
            self.test_results[endpoint] = {"error": str(e)}
            return False, None
    
    def test_api_performance(self):
        """اختبار أداء API"""
        print("⚡ اختبار الأداء:")
        print("-" * 30)
        
        # اختبار متعدد للصفحة الرئيسية
        times = []
        for i in range(5):
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}/", timeout=5)
                if response.status_code == 200:
                    times.append((time.time() - start_time) * 1000)
            except:
                pass
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"📊 متوسط وقت الاستجابة: {avg_time:.1f}ms")
            print(f"⚡ أسرع استجابة: {min_time:.1f}ms")
            print(f"🐌 أبطأ استجابة: {max_time:.1f}ms")
            
            if avg_time < 100:
                print("🎉 الأداء ممتاز!")
            elif avg_time < 500:
                print("✅ الأداء جيد")
            else:
                print("⚠️  الأداء يحتاج تحسين")
        else:
            print("❌ فشل اختبار الأداء")
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🏺 اختبار شامل لـ API أنوبيس المعزول")
        print("=" * 60)
        print(f"🕐 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 الخادم: {self.base_url}")
        
        # اختبار النقاط الأساسية
        print(f"\n🔍 اختبار النقاط الأساسية:")
        print("-" * 40)
        
        tests = [
            ("/", "الصفحة الرئيسية"),
            ("/health", "فحص الصحة"),
            ("/info", "معلومات الخدمة"),
            ("/docs", "توثيق API", 200),
            ("/redoc", "ReDoc API", 200),
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            endpoint = test[0]
            description = test[1]
            expected_status = test[2] if len(test) > 2 else 200
            
            success, _ = self.test_endpoint(endpoint, description, expected_status)
            if success:
                passed += 1
        
        # اختبار الأداء
        print(f"\n")
        self.test_api_performance()
        
        # اختبار حالة الخدمة (قد يفشل)
        print(f"\n🔍 اختبارات إضافية:")
        print("-" * 30)
        self.test_endpoint("/status", "حالة الخدمة المفصلة", 200)
        self.test_endpoint("/metrics", "مقاييس الخدمة", 200)
        
        # النتيجة النهائية
        print(f"\n📊 نتائج الاختبار:")
        print("=" * 30)
        print(f"✅ نجح: {passed}/{total}")
        print(f"❌ فشل: {total - passed}/{total}")
        print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 جميع الاختبارات نجحت! API جاهز للإنتاج")
            return True
        elif passed >= total * 0.8:
            print("✅ معظم الاختبارات نجحت! API يعمل بشكل جيد")
            return True
        else:
            print("⚠️  بعض الاختبارات فشلت، يحتاج مراجعة")
            return False
    
    def save_test_report(self):
        """حفظ تقرير الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"anubis_api_test_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "test_results": self.test_results,
                    "metadata": {
                        "base_url": self.base_url,
                        "test_timestamp": timestamp,
                        "tester_version": "1.0.0"
                    }
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ تقرير الاختبار: {report_file}")
            return report_file
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None

def main():
    """الدالة الرئيسية"""
    tester = AnubisAPITester()
    
    # تشغيل الاختبار الشامل
    success = tester.run_comprehensive_test()
    
    # حفظ التقرير
    tester.save_test_report()
    
    return success

if __name__ == "__main__":
    main()
