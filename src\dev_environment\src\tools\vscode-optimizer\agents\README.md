# 🤖 مجلد الوكلاء المحسنين - نظام أنوبيس
## Enhanced Agents Directory - Anubis System

**آخر تحديث**: 2025-07-16
**الحالة**: ✅ مكتمل ومحسن بالتعاون مع Gemini CLI
**عدد الوكلاء النشطين**: 7 وكلاء ذكيين

---

## 📜 نظرة عامة

مجلد `agents` هو القلب النابض لنظام أنوبيس، حيث يحتوي على مجموعة من الوكلاء الأذكياء والمتخصصين الذين يعملون معاً لتحليل، تحسين، وإدارة المشاريع البرمجية بكفاءة عالية. كل وكيل مصمم لأداء مهام محددة، ويتكامل مع الآخرين لتقديم رؤية شاملة وقدرات متقدمة.

## 🎯 الوكلاء المحسنين الأساسيين

### 🔍 EnhancedErrorDetectorAgent
**الملف**: `enhanced_error_detector.py`
**الوظيفة**: كشف الأخطاء المتقدم للباك إند والفرونت إند

**المميزات**:
- دعم 15+ لغة برمجة (Python, JavaScript, TypeScript, React, Vue)
- كشف أخطاء الأمان (XSS, SQL Injection, كلمات المرور المكشوفة)
- تحليل الأداء واقتراحات التحسين
- تكامل مع الذكاء الاصطناعي

**الاستخدام**:
```python
from agents.enhanced_error_detector import EnhancedErrorDetectorAgent

detector = EnhancedErrorDetectorAgent(".", {}, True)
result = detector.scan_single_file("file.py")
```

### 📊 EnhancedProjectAnalyzerAgent
**الملف**: `enhanced_project_analyzer.py`
**الوظيفة**: تحليل شامل للمشاريع وكشف التقنيات

**المميزات**:
- كشف نوع المشروع تلقائياً (React, Vue, Django, FastAPI)
- تحليل التقنيات والأطر المستخدمة
- تقييم جودة المشروع (نقاط 0-100)
- توصيات ذكية للتحسين

**الاستخدام**:
```python
from agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent

analyzer = EnhancedProjectAnalyzerAgent(".", {}, True)
result = analyzer.analyze_project()
```

### 📁 EnhancedFileOrganizerAgent
**الملف**: `enhanced_file_organizer.py`
**الوظيفة**: تنظيم ذكي للملفات وإنشاء هياكل المشاريع

**المميزات**:
- تنظيم تلقائي للملفات (6 فئات)
- إنشاء هياكل مشاريع جاهزة (React, Vue, Django, FastAPI)
- تنظيف المجلدات الفارغة
- نسخ احتياطية للملفات المهمة

**الاستخدام**:
```python
from agents.enhanced_file_organizer import EnhancedFileOrganizerAgent

organizer = EnhancedFileOrganizerAgent(".", {}, True)
result = organizer.organize_files()
project = organizer.create_project_structure("react", "my-app")
```

### 🧠 EnhancedMemoryAgent
**الملف**: `enhanced_memory_agent.py`
**الوظيفة**: إدارة الذاكرة الذكية والبحث المتقدم

**المميزات**:
- تخزين ذكي مع تصنيف وتاريخ
- بحث متقدم في المفاتيح والمحتوى
- إحصائيات شاملة للذاكرة
- إدارة الفئات والتصنيفات

**الاستخدام**:
```python
from agents.enhanced_memory_agent import EnhancedMemoryAgent

memory = EnhancedMemoryAgent(".", {}, True)
memory.store_memory("key", "data", "category")
data = memory.retrieve_memory("key")
```

---

## 🔧 الوكلاء المتخصصين

### 🗄️ DatabaseAgent
**الملف**: `database_agent.py`
**الوظيفة**: إدارة قواعد البيانات المتقدمة

### 🧠 SmartAIAgent
**الملف**: `smart_ai_agent.py`
**الوظيفة**: الذكاء الاصطناعي الشامل على مستوى المشروع

### 📝 SmartCodeAnalyzer
**الملف**: `smart_code_analyzer.py`
**الوظيفة**: تحليل الكود الذكي للملفات الفردية

---

## 📊 إحصائيات المجلد

| النوع | العدد | الحالة |
|-------|-------|--------|
| **الوكلاء المحسنين** | 4 | ✅ نشط |
| **الوكلاء المتخصصين** | 3 | ✅ نشط |
| **الوكلاء المحذوفين** | 5 | 🗑️ محذوف |
| **المجموع النشط** | **7** | **✅ جاهز** |

---

## 🗑️ الوكلاء المحذوفين

تم حذف الوكلاء التالية لأنها قديمة أو مكررة:

- `file_organizer_agent.py` → استبدل بـ `enhanced_file_organizer.py`
- `memory_agent.py` → استبدل بـ `enhanced_memory_agent.py`
- `project_analyzer_agent.py` → استبدل بـ `enhanced_project_analyzer.py`
- `error_detector.py` → استبدل بـ `enhanced_error_detector.py`
- `error_detector_agent.py` → استبدل بـ `enhanced_error_detector.py`

**النسخ الاحتياطية**: متوفرة في `backup/old_agents/`

---

## 🚀 الاستخدام السريع

### استيراد جميع الوكلاء:
```python
from agents import (
    EnhancedErrorDetectorAgent,
    EnhancedProjectAnalyzerAgent,
    EnhancedFileOrganizerAgent,
    EnhancedMemoryAgent,
    DatabaseAgent,
    SmartAIAgent,
    SmartCodeAnalyzer
)
```

### مثال شامل:
```python
# إنشاء الوكلاء
detector = EnhancedErrorDetectorAgent(".", {}, True)
analyzer = EnhancedProjectAnalyzerAgent(".", {}, True)
organizer = EnhancedFileOrganizerAgent(".", {}, True)
memory = EnhancedMemoryAgent(".", {}, True)

# تشغيل التحليل الشامل
errors = detector.scan_entire_project()
analysis = analyzer.analyze_project()
organized = organizer.organize_files()

# حفظ النتائج في الذاكرة
memory.store_memory("last_scan", {
    "errors": errors,
    "analysis": analysis,
    "organized": organized
})
```

---

<div align="center">

**🤖 مجلد الوكلاء المحسنين - نظام أنوبيس**

**منظف ومحسن بالتعاون مع Gemini CLI**

[![Agents](https://img.shields.io/badge/Agents-7%20Active-brightgreen.svg)](README.md)
[![Status](https://img.shields.io/badge/Status-Clean-success.svg)](README.md)
[![Version](https://img.shields.io/badge/Version-2.0-blue.svg)](README.md)

</div>
