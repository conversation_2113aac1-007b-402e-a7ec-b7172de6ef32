{"timestamp": "2025-07-14T13:50:31.867886", "total_duration": 0.6062071323394775, "test_results": [{"test": "insert_projects", "count": 100, "successful": 100, "failed": 0, "duration": 0.2479255199432373, "rate": 403.34694073806946}, {"test": "concurrent_read", "thread_count": 10, "queries_per_thread": 50, "total_queries": 500, "successful_queries": 500, "failed_queries": 0, "duration": 0.27748656272888184, "queries_per_second": 1801.8890539522263, "avg_query_time": 0.002603033542633057}, {"test": "bulk_insert", "batch_size": 1000, "duration": 0.045430660247802734, "rate": 22011.566517974286}], "errors_count": 0, "errors": [], "cleanup": {"deleted_records": 1100}, "database_config": {"host": "localhost", "port": 3306, "database": "anubis_system"}}