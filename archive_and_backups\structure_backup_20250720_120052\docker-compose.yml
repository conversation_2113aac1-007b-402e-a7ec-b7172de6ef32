version: '3.8'

services:
  anubis-core:
    build: .
    container_name: anubis-core
    restart: unless-stopped
    command: python main.py
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - ANUBIS_ENV=production
      - DATABASE_URL=sqlite:///app/data/anubis.db
    ports:
      - "8000:8000"
    
  anubis-mysql:
    image: mysql:8.0
    container_name: anubis-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 2452329511
      MYSQL_DATABASE: anubis_system
      MYSQL_USER: anubis
      MYSQL_PASSWORD: anubis_secure_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

volumes:
  mysql_data:
