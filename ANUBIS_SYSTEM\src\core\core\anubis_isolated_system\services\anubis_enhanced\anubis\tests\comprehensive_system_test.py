#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔬 اختبار شامل لنظام أنوبيس
Comprehensive Anubis System Test

اختبار شامل لجميع الوكلاء مع نماذج الذكاء الاصطناعي
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
sys.path.append(os.path.join(os.path.dirname(__file__), "agents"))


def test_all_agents_with_ai():
    """اختبار جميع الوكلاء مع الذكاء الاصطناعي"""
    print("🔬 اختبار شامل لنظام أنوبيس مع الذكاء الاصطناعي")
    print("=" * 60)

    test_results = {}

    # 1. اختبار ErrorDetectorAgent
    print("\n🔍 اختبار وكيل كشف الأخطاء...")
    try:
        from anubis.agents.error_detector import ErrorDetectorAgent

        error_agent = ErrorDetectorAgent(
            project_path=".", config={"ai_enhanced": True}, verbose=True
        )

        # اختبار كشف أخطاء مع AI
        test_code = """
def broken_function():
    x = 10
    y = 0
    result = x / y  # خطأ: قسمة على صفر
    return result
        """

        if hasattr(error_agent, "detect_errors_with_ai"):
            errors = error_agent.detect_errors_with_ai(test_code)
        else:
            errors = error_agent.detect_errors(test_code)

        test_results["error_detector"] = {
            "status": "success",
            "ai_enabled": error_agent.is_ai_enabled(),
            "errors_found": len(errors) if isinstance(errors, list) else 1,
            "working": True,
        }

        print(f"   ✅ يعمل - AI: {'مفعل' if error_agent.is_ai_enabled() else 'غير مفعل'}")
        print(f"   🔍 أخطاء مكتشفة: {test_results['error_detector']['errors_found']}")

    except Exception as e:
        test_results["error_detector"] = {
            "status": "error",
            "error": str(e),
            "working": False,
        }
        print(f"   ❌ خطأ: {e}")

    # 2. اختبار ProjectAnalyzerAgent
    print("\n📊 اختبار وكيل تحليل المشاريع...")
    try:
        from anubis.agents.project_analyzer import ProjectAnalyzerAgent

        project_agent = ProjectAnalyzerAgent(
            project_path=".", config={"ai_insights": True}, verbose=True
        )

        # تشغيل تحليل المشروع
        analysis = project_agent.run_analysis()

        test_results["project_analyzer"] = {
            "status": "success",
            "ai_enabled": project_agent.is_ai_enabled(),
            "analysis_completed": analysis.get("status") == "completed",
            "working": True,
        }

        print(f"   ✅ يعمل - AI: {'مفعل' if project_agent.is_ai_enabled() else 'غير مفعل'}")
        print(f"   📊 التحليل: {analysis.get('status', 'غير محدد')}")

    except Exception as e:
        test_results["project_analyzer"] = {
            "status": "error",
            "error": str(e),
            "working": False,
        }
        print(f"   ❌ خطأ: {e}")

    # 3. اختبار FileOrganizerAgent
    print("\n📁 اختبار وكيل تنظيم الملفات...")
    try:
        from anubis.agents.file_organizer import FileOrganizerAgent

        organizer_agent = FileOrganizerAgent(
            project_path=".", config={"smart_organization": True}, verbose=True
        )

        # تشغيل تحليل التنظيم
        organization = organizer_agent.run_analysis()

        test_results["file_organizer"] = {
            "status": "success",
            "ai_enabled": organizer_agent.is_ai_enabled(),
            "organization_completed": organization.get("status") == "completed",
            "working": True,
        }

        print(f"   ✅ يعمل - AI: {'مفعل' if organizer_agent.is_ai_enabled() else 'غير مفعل'}")
        print(f"   📁 التنظيم: {organization.get('status', 'غير محدد')}")

    except Exception as e:
        test_results["file_organizer"] = {
            "status": "error",
            "error": str(e),
            "working": False,
        }
        print(f"   ❌ خطأ: {e}")

    # 4. اختبار MemoryAgent
    print("\n🧠 اختبار وكيل الذاكرة...")
    try:
        from anubis.agents.memory_agent import MemoryAgent

        memory_agent = MemoryAgent(
            project_path=".", config={"intelligent_storage": True}, verbose=True
        )

        # اختبار حفظ واسترجاع
        test_data = {"test": "اختبار الذاكرة الذكية"}
        memory_agent.store_memory("test_key", test_data)
        retrieved = memory_agent.retrieve_memory("test_key")

        test_results["memory_agent"] = {
            "status": "success",
            "ai_enabled": memory_agent.is_ai_enabled(),
            "storage_working": retrieved is not None,
            "working": True,
        }

        print(f"   ✅ يعمل - AI: {'مفعل' if memory_agent.is_ai_enabled() else 'غير مفعل'}")
        print(f"   🧠 التخزين: {'يعمل' if retrieved else 'لا يعمل'}")

    except Exception as e:
        test_results["memory_agent"] = {
            "status": "error",
            "error": str(e),
            "working": False,
        }
        print(f"   ❌ خطأ: {e}")

    # 5. اختبار SmartCodeAnalyzer
    print("\n🧠 اختبار محلل الكود الذكي...")
    try:
        from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer

        smart_analyzer = SmartCodeAnalyzer(
            project_path=".", config={"full_ai_analysis": True}, verbose=True
        )

        # تحليل ملف بسيط
        test_file = "quick_ai_test.py"
        if os.path.exists(test_file):
            analysis = smart_analyzer.analyze_file_with_ai(test_file)

            test_results["smart_analyzer"] = {
                "status": "success",
                "ai_enabled": smart_analyzer.is_ai_enabled(),
                "analysis_working": analysis.get("status") == "success",
                "working": True,
            }

            print(f"   ✅ يعمل - AI: {'مفعل' if smart_analyzer.is_ai_enabled() else 'غير مفعل'}")
            print(f"   🧠 التحليل: {analysis.get('status', 'غير محدد')}")
        else:
            test_results["smart_analyzer"] = {
                "status": "warning",
                "message": "لا يوجد ملف للاختبار",
                "working": True,
            }
            print("   ⚠️ لا يوجد ملف للاختبار")

    except Exception as e:
        test_results["smart_analyzer"] = {
            "status": "error",
            "error": str(e),
            "working": False,
        }
        print(f"   ❌ خطأ: {e}")

    return test_results


def test_ai_models_performance():
    """اختبار أداء النماذج المختلفة"""
    print("\n🤖 اختبار أداء النماذج...")

    try:
        from anubis.core.ai_integration import AIIntegrationManager, OllamaProvider

        models_to_test = ["llama3:8b", "mistral:7b", "phi3:mini"]
        model_results = {}

        for model in models_to_test:
            print(f"\n   🔍 اختبار {model}...")

            try:
                provider = OllamaProvider(model_name=model)

                if provider.is_available():
                    # اختبار سرعة الاستجابة
                    start_time = time.time()
                    response = provider.generate_response("قل مرحبا")
                    end_time = time.time()

                    response_time = end_time - start_time

                    model_results[model] = {
                        "available": True,
                        "working": not response.startswith("خطأ"),
                        "response_time": round(response_time, 2),
                        "response_length": len(response),
                        "performance": (
                            "ممتاز"
                            if response_time < 5
                            else "جيد" if response_time < 10 else "بطيء"
                        ),
                    }

                    print(
                        f"      ✅ يعمل - الوقت: {response_time:.2f}ث - الأداء: {model_results[model]['performance']}"
                    )
                else:
                    model_results[model] = {"available": False, "working": False}
                    print(f"      ❌ غير متاح")

            except Exception as e:
                model_results[model] = {
                    "available": False,
                    "working": False,
                    "error": str(e),
                }
                print(f"      ❌ خطأ: {e}")

        return model_results

    except Exception as e:
        print(f"   ❌ خطأ في اختبار النماذج: {e}")
        return {}


def test_agent_ai_collaboration():
    """اختبار تعاون الوكلاء مع الذكاء الاصطناعي"""
    print("\n🤝 اختبار تعاون الوكلاء مع الذكاء الاصطناعي...")

    collaboration_results = {}

    try:
        # اختبار تمرير البيانات بين الوكلاء
        from anubis.agents.smart_code_analyzer import SmartCodeAnalyzer

        analyzer = SmartCodeAnalyzer(
            project_path=".", config={"collaborative_mode": True}, verbose=False
        )

        if analyzer.is_ai_enabled():
            # تحليل مشروع صغير
            project_analysis = analyzer.analyze_project_files(max_files=3)

            # الحصول على توصيات ذكية
            recommendations = analyzer.get_smart_recommendations()

            collaboration_results = {
                "multi_agent_analysis": True,
                "ai_recommendations": len(recommendations) > 0,
                "data_sharing": project_analysis.get("status") == "completed",
                "collaborative_intelligence": True,
            }

            print("   ✅ التعاون بين الوكلاء والذكاء الاصطناعي يعمل")
            print(f"   🧠 التوصيات المولدة: {len(recommendations)}")

        else:
            collaboration_results = {
                "multi_agent_analysis": False,
                "ai_recommendations": False,
                "reason": "الذكاء الاصطناعي غير مفعل",
            }
            print("   ⚠️ الذكاء الاصطناعي غير مفعل للتعاون")

    except Exception as e:
        collaboration_results = {"error": str(e), "working": False}
        print(f"   ❌ خطأ في اختبار التعاون: {e}")

    return collaboration_results


def generate_comprehensive_report(agent_results, model_results, collaboration_results):
    """إنتاج تقرير شامل"""

    # حساب الإحصائيات
    total_agents = len(agent_results)
    working_agents = len([r for r in agent_results.values() if r.get("working", False)])
    ai_enabled_agents = len([r for r in agent_results.values() if r.get("ai_enabled", False)])

    total_models = len(model_results)
    working_models = len([r for r in model_results.values() if r.get("working", False)])

    report = {
        "timestamp": datetime.now().isoformat(),
        "test_type": "comprehensive_system_test",
        "summary": {
            "total_agents_tested": total_agents,
            "working_agents": working_agents,
            "ai_enabled_agents": ai_enabled_agents,
            "total_models_tested": total_models,
            "working_models": working_models,
            "system_health": (
                "ممتاز"
                if working_agents == total_agents and working_models > 0
                else "جيد" if working_agents > total_agents / 2 else "يحتاج تحسين"
            ),
        },
        "detailed_results": {
            "agents": agent_results,
            "models": model_results,
            "collaboration": collaboration_results,
        },
        "recommendations": [],
    }

    # إضافة توصيات
    if working_agents == total_agents:
        report["recommendations"].append("✅ جميع الوكلاء تعمل بشكل ممتاز")
    else:
        report["recommendations"].append(f"⚠️ {total_agents - working_agents} وكلاء تحتاج إصلاح")

    if ai_enabled_agents > 0:
        report["recommendations"].append(f"🧠 {ai_enabled_agents} وكلاء مدعومين بالذكاء الاصطناعي")
    else:
        report["recommendations"].append("❌ لا توجد وكلاء مدعومين بالذكاء الاصطناعي")

    if working_models > 0:
        report["recommendations"].append(f"🤖 {working_models} نماذج ذكاء اصطناعي تعمل")
    else:
        report["recommendations"].append("❌ لا توجد نماذج ذكاء اصطناعي تعمل")

    # حفظ التقرير
    report_file = (
        f"comprehensive_system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )

    try:
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n📄 تم حفظ التقرير الشامل في: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")

    return report


def main():
    """الدالة الرئيسية"""
    print("🏺 اختبار شامل لنظام أنوبيس")
    print("=" * 60)

    # اختبار جميع الوكلاء
    agent_results = test_all_agents_with_ai()

    # اختبار أداء النماذج
    model_results = test_ai_models_performance()

    # اختبار التعاون
    collaboration_results = test_agent_ai_collaboration()

    # إنتاج التقرير الشامل
    report = generate_comprehensive_report(agent_results, model_results, collaboration_results)

    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("🏆 نتائج الاختبار الشامل:")
    print(
        f"   🤖 الوكلاء العاملة: {report['summary']['working_agents']}/{report['summary']['total_agents_tested']}"
    )
    print(
        f"   🧠 الوكلاء الذكية: {report['summary']['ai_enabled_agents']}/{report['summary']['total_agents_tested']}"
    )
    print(
        f"   🔥 النماذج العاملة: {report['summary']['working_models']}/{report['summary']['total_models_tested']}"
    )
    print(f"   📊 صحة النظام: {report['summary']['system_health']}")

    print("\n💡 التوصيات:")
    for recommendation in report["recommendations"]:
        print(f"   {recommendation}")

    print(f"\n🏺 انتهى الاختبار الشامل لنظام أنوبيس!")

    # تحديد كود الخروج
    if report["summary"]["system_health"] == "ممتاز":
        return 0
    elif report["summary"]["system_health"] == "جيد":
        return 1
    else:
        return 2


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
