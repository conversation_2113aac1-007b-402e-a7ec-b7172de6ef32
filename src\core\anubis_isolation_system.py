#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام العزل الشامل لأنوبيس
Anubis Comprehensive Isolation System

نظام متقدم لعزل جميع مكونات المشروع مع:
- عزل الموارد والذاكرة
- عزل الشبكة والأمان  
- عزل البيانات والتخزين
- مراقبة منفصلة لكل مكون

مطور بالتعاون مع Gemini CLI و Ollama
"""

import json
import os
import shutil
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import yaml


class AnubisIsolationManager:
    """مدير العزل الشامل لنظام أنوبيس"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.isolation_config = {
            "isolation_timestamp": datetime.now().isoformat(),
            "components": {},
            "networks": {},
            "volumes": {},
            "monitoring": {},
            "security": {}
        }
        
        # تحديد المكونات المختلفة
        self.components = {
            "anubis_enhanced": {
                "path": "anubis/",
                "type": "python_service",
                "port": 8000,
                "resources": {"cpu": "1.0", "memory": "1G"},
                "dependencies": ["database", "ollama"]
            },
            "universal_ai": {
                "path": "Universal-AI-Assistants/",
                "type": "python_service", 
                "port": 8001,
                "resources": {"cpu": "0.5", "memory": "512M"},
                "dependencies": ["database"]
            },
            "ollama_service": {
                "path": "ollama/",
                "type": "ai_service",
                "port": 11434,
                "resources": {"cpu": "2.0", "memory": "4G", "gpu": True},
                "models": ["llama3:8b", "mistral:7b", "phi3:mini", "gemma3n:e4b"]
            },
            "database_mysql": {
                "path": "database/mysql/",
                "type": "database",
                "port": 3306,
                "resources": {"cpu": "0.5", "memory": "1G"},
                "persistent": True
            },
            "api_gateway": {
                "path": "gateway/",
                "type": "proxy",
                "port": 80,
                "resources": {"cpu": "0.2", "memory": "256M"}
            },
            "monitoring": {
                "path": "monitoring/",
                "type": "monitoring",
                "ports": [9090, 3000],  # Prometheus, Grafana
                "resources": {"cpu": "0.3", "memory": "512M"}
            }
        }
        
    def create_isolation_structure(self):
        """إنشاء هيكل العزل الشامل"""
        print("🏺 بدء إنشاء نظام العزل الشامل لأنوبيس...")
        print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
        print("=" * 60)
        
        # إنشاء مجلد العزل الرئيسي
        isolation_dir = self.project_root / "anubis_isolated_system"
        isolation_dir.mkdir(exist_ok=True)
        
        # إنشاء هيكل المجلدات
        self._create_directory_structure(isolation_dir)
        
        # إنشاء ملفات Docker
        self._create_docker_files(isolation_dir)
        
        # إنشاء ملفات التكوين
        self._create_configuration_files(isolation_dir)
        
        # إنشاء سكريبتات الإدارة
        self._create_management_scripts(isolation_dir)
        
        # إنشاء نظام المراقبة
        self._create_monitoring_system(isolation_dir)
        
        print(f"\n✅ تم إنشاء نظام العزل في: {isolation_dir}")
        return isolation_dir
    
    def _create_directory_structure(self, isolation_dir: Path):
        """إنشاء هيكل المجلدات"""
        print("📁 إنشاء هيكل المجلدات...")
        
        directories = [
            "services/anubis_enhanced",
            "services/universal_ai", 
            "services/ollama",
            "services/database",
            "services/api_gateway",
            "services/monitoring",
            "configs",
            "scripts",
            "volumes/database_data",
            "volumes/ollama_models",
            "volumes/logs",
            "volumes/monitoring_data",
            "networks",
            "security",
            "docs"
        ]
        
        for directory in directories:
            (isolation_dir / directory).mkdir(parents=True, exist_ok=True)
            print(f"  ✅ {directory}")
    
    def _create_docker_files(self, isolation_dir: Path):
        """إنشاء ملفات Docker"""
        print("🐳 إنشاء ملفات Docker...")
        
        # إنشاء docker-compose.yml الرئيسي
        self._create_main_docker_compose(isolation_dir)
        
        # إنشاء Dockerfile لكل خدمة
        self._create_service_dockerfiles(isolation_dir)
        
        # إنشاء ملفات الشبكة (تم دمجها في docker-compose.yml)
    
    def _create_main_docker_compose(self, isolation_dir: Path):
        """إنشاء ملف docker-compose.yml الرئيسي"""
        compose_config = {
            "version": "3.8",
            "services": {
                "anubis_enhanced": {
                    "build": "./services/anubis_enhanced",
                    "container_name": "anubis_enhanced_container",
                    "ports": ["8000:8000"],
                    "environment": [
                        "PYTHONPATH=/app",
                        "DATABASE_URL=mysql://anubis:2452329511@database_mysql:3306/anubis_system",
                        "OLLAMA_URL=http://ollama_service:11434"
                    ],
                    "volumes": [
                        "./volumes/logs:/app/logs",
                        "./configs:/app/configs:ro"
                    ],
                    "networks": ["anubis_network"],
                    "depends_on": ["database_mysql", "ollama_service"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "1.0", "memory": "1G"},
                            "reservations": {"cpus": "0.5", "memory": "512M"}
                        }
                    }
                },
                "universal_ai": {
                    "build": "./services/universal_ai",
                    "container_name": "universal_ai_container", 
                    "ports": ["8001:8001"],
                    "environment": [
                        "PYTHONPATH=/app",
                        "DATABASE_URL=mysql://anubis:2452329511@database_mysql:3306/anubis_system"
                    ],
                    "volumes": [
                        "./volumes/logs:/app/logs",
                        "./configs:/app/configs:ro"
                    ],
                    "networks": ["anubis_network"],
                    "depends_on": ["database_mysql"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "0.5", "memory": "512M"},
                            "reservations": {"cpus": "0.2", "memory": "256M"}
                        }
                    }
                },
                "ollama_service": {
                    "image": "ollama/ollama:latest",
                    "container_name": "ollama_container",
                    "ports": ["11434:11434"],
                    "volumes": [
                        "./volumes/ollama_models:/root/.ollama",
                        "./volumes/logs:/app/logs"
                    ],
                    "networks": ["anubis_network"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "2.0", "memory": "4G"},
                            "reservations": {"cpus": "1.0", "memory": "2G"}
                        }
                    },
                    "environment": [
                        "OLLAMA_MODELS=/root/.ollama"
                    ]
                },
                "database_mysql": {
                    "image": "mysql:8.0",
                    "container_name": "mysql_container",
                    "ports": ["3306:3306"],
                    "environment": [
                        "MYSQL_ROOT_PASSWORD=2452329511",
                        "MYSQL_DATABASE=anubis_system",
                        "MYSQL_USER=anubis",
                        "MYSQL_PASSWORD=2452329511"
                    ],
                    "volumes": [
                        "./volumes/database_data:/var/lib/mysql",
                        "./volumes/logs:/var/log/mysql"
                    ],
                    "networks": ["anubis_network"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "0.5", "memory": "1G"},
                            "reservations": {"cpus": "0.2", "memory": "512M"}
                        }
                    }
                },
                "api_gateway": {
                    "image": "nginx:alpine",
                    "container_name": "nginx_gateway",
                    "ports": ["80:80", "443:443"],
                    "volumes": [
                        "./services/api_gateway/nginx.conf:/etc/nginx/nginx.conf:ro",
                        "./volumes/logs:/var/log/nginx"
                    ],
                    "networks": ["anubis_network"],
                    "depends_on": ["anubis_enhanced", "universal_ai"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "0.2", "memory": "256M"},
                            "reservations": {"cpus": "0.1", "memory": "128M"}
                        }
                    }
                },
                "prometheus": {
                    "image": "prom/prometheus:latest",
                    "container_name": "prometheus_container",
                    "ports": ["9090:9090"],
                    "volumes": [
                        "./services/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro",
                        "./volumes/monitoring_data:/prometheus"
                    ],
                    "networks": ["anubis_network"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "0.3", "memory": "512M"},
                            "reservations": {"cpus": "0.1", "memory": "256M"}
                        }
                    }
                },
                "grafana": {
                    "image": "grafana/grafana:latest",
                    "container_name": "grafana_container",
                    "ports": ["3000:3000"],
                    "environment": [
                        "GF_SECURITY_ADMIN_PASSWORD=anubis2024"
                    ],
                    "volumes": [
                        "./volumes/monitoring_data:/var/lib/grafana"
                    ],
                    "networks": ["anubis_network"],
                    "depends_on": ["prometheus"],
                    "restart": "unless-stopped",
                    "deploy": {
                        "resources": {
                            "limits": {"cpus": "0.3", "memory": "512M"},
                            "reservations": {"cpus": "0.1", "memory": "256M"}
                        }
                    }
                }
            },
            "networks": {
                "anubis_network": {
                    "driver": "bridge",
                    "ipam": {
                        "config": [{"subnet": "**********/16"}]
                    }
                }
            },
            "volumes": {
                "database_data": {"driver": "local"},
                "ollama_models": {"driver": "local"},
                "logs": {"driver": "local"},
                "monitoring_data": {"driver": "local"}
            }
        }
        
        compose_file = isolation_dir / "docker-compose.yml"
        with open(compose_file, 'w', encoding='utf-8') as f:
            yaml.dump(compose_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"  ✅ docker-compose.yml")
    
    def _create_service_dockerfiles(self, isolation_dir: Path):
        """إنشاء Dockerfile لكل خدمة"""
        
        # Dockerfile لخدمة أنوبيس المحسنة
        anubis_dockerfile = """FROM python:3.11-slim

WORKDIR /app

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \\
    gcc \\
    default-libmysqlclient-dev \\
    pkg-config \\
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# إنشاء مستخدم غير جذر
RUN useradd -m -u 1000 anubis && chown -R anubis:anubis /app
USER anubis

# تعريف المنفذ
EXPOSE 8000

# أمر التشغيل
CMD ["python", "main.py"]
"""
        
        anubis_service_dir = isolation_dir / "services/anubis_enhanced"
        with open(anubis_service_dir / "Dockerfile", 'w') as f:
            f.write(anubis_dockerfile)
        
        # Dockerfile لخدمة Universal AI
        universal_dockerfile = """FROM python:3.11-slim

WORKDIR /app

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \\
    gcc \\
    default-libmysqlclient-dev \\
    pkg-config \\
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# إنشاء مستخدم غير جذر
RUN useradd -m -u 1001 universal && chown -R universal:universal /app
USER universal

# تعريف المنفذ
EXPOSE 8001

# أمر التشغيل
CMD ["python", "main.py"]
"""
        
        universal_service_dir = isolation_dir / "services/universal_ai"
        with open(universal_service_dir / "Dockerfile", 'w') as f:
            f.write(universal_dockerfile)
        
        print(f"  ✅ Dockerfiles للخدمات")
    
    def _create_configuration_files(self, isolation_dir: Path):
        """إنشاء ملفات التكوين"""
        print("⚙️ إنشاء ملفات التكوين...")
        
        # تكوين Nginx
        nginx_config = """events {
    worker_connections 1024;
}

http {
    upstream anubis_enhanced {
        server anubis_enhanced_container:8000;
    }
    
    upstream universal_ai {
        server universal_ai_container:8001;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/anubis/ {
            proxy_pass http://anubis_enhanced/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/universal/ {
            proxy_pass http://universal_ai/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /health {
            access_log off;
            return 200 "healthy\\n";
            add_header Content-Type text/plain;
        }
    }
}
"""
        
        nginx_dir = isolation_dir / "services/api_gateway"
        with open(nginx_dir / "nginx.conf", 'w') as f:
            f.write(nginx_config)
        
        # تكوين Prometheus
        prometheus_config = {
            "global": {
                "scrape_interval": "15s"
            },
            "scrape_configs": [
                {
                    "job_name": "anubis_enhanced",
                    "static_configs": [{"targets": ["anubis_enhanced_container:8000"]}]
                },
                {
                    "job_name": "universal_ai", 
                    "static_configs": [{"targets": ["universal_ai_container:8001"]}]
                },
                {
                    "job_name": "ollama",
                    "static_configs": [{"targets": ["ollama_container:11434"]}]
                }
            ]
        }
        
        monitoring_dir = isolation_dir / "services/monitoring"
        with open(monitoring_dir / "prometheus.yml", 'w') as f:
            yaml.dump(prometheus_config, f)
        
        print(f"  ✅ ملفات التكوين")
    
    def _create_management_scripts(self, isolation_dir: Path):
        """إنشاء سكريبتات الإدارة"""
        print("📜 إنشاء سكريبتات الإدارة...")
        
        # سكريبت البدء
        start_script = """#!/bin/bash
echo "🏺 بدء تشغيل نظام أنوبيس المعزول..."
docker-compose up -d
echo "✅ تم بدء جميع الخدمات"
echo "🌐 الخدمات متاحة على:"
echo "  - أنوبيس المحسن: http://localhost/api/anubis/"
echo "  - Universal AI: http://localhost/api/universal/"
echo "  - Grafana: http://localhost:3000 (admin/anubis2024)"
echo "  - Prometheus: http://localhost:9090"
"""
        
        scripts_dir = isolation_dir / "scripts"
        with open(scripts_dir / "start.sh", 'w') as f:
            f.write(start_script)
        
        # سكريبت الإيقاف
        stop_script = """#!/bin/bash
echo "🛑 إيقاف نظام أنوبيس المعزول..."
docker-compose down
echo "✅ تم إيقاف جميع الخدمات"
"""
        
        with open(scripts_dir / "stop.sh", 'w') as f:
            f.write(stop_script)
        
        # سكريبت المراقبة
        monitor_script = """#!/bin/bash
echo "📊 حالة نظام أنوبيس المعزول:"
docker-compose ps
echo ""
echo "📈 استهلاك الموارد:"
docker stats --no-stream
"""
        
        with open(scripts_dir / "monitor.sh", 'w') as f:
            f.write(monitor_script)
        
        # جعل السكريبتات قابلة للتنفيذ
        os.chmod(scripts_dir / "start.sh", 0o755)
        os.chmod(scripts_dir / "stop.sh", 0o755) 
        os.chmod(scripts_dir / "monitor.sh", 0o755)
        
        print(f"  ✅ سكريبتات الإدارة")
    
    def _create_monitoring_system(self, isolation_dir: Path):
        """إنشاء نظام المراقبة"""
        print("📊 إنشاء نظام المراقبة...")
        
        # إنشاء ملف README للمراقبة
        monitoring_readme = """# 📊 نظام مراقبة أنوبيس المعزول

## الخدمات المتاحة

### Prometheus (http://localhost:9090)
- جمع المقاييس من جميع الخدمات
- مراقبة الأداء والموارد
- تنبيهات تلقائية

### Grafana (http://localhost:3000)
- لوحات تحكم تفاعلية
- تصور البيانات
- تقارير مفصلة

## المقاييس المراقبة
- استهلاك CPU والذاكرة
- عدد الطلبات والاستجابات
- أوقات الاستجابة
- حالة قواعد البيانات
- حالة نماذج AI

## التنبيهات
- استهلاك موارد عالي
- فشل في الخدمات
- بطء في الاستجابة
- مشاكل في قاعدة البيانات
"""
        
        docs_dir = isolation_dir / "docs"
        with open(docs_dir / "monitoring.md", 'w', encoding='utf-8') as f:
            f.write(monitoring_readme)
        
        print(f"  ✅ نظام المراقبة")
    
    def copy_source_code(self, isolation_dir: Path):
        """نسخ الكود المصدري إلى الخدمات"""
        print("📋 نسخ الكود المصدري...")
        
        # نسخ كود أنوبيس المحسن
        anubis_source = self.project_root / "anubis"
        anubis_dest = isolation_dir / "services/anubis_enhanced"
        
        if anubis_source.exists():
            shutil.copytree(anubis_source, anubis_dest / "anubis", dirs_exist_ok=True)
            print(f"  ✅ نسخ أنوبيس المحسن")
        
        # نسخ كود Universal AI
        universal_source = self.project_root / "Universal-AI-Assistants"
        universal_dest = isolation_dir / "services/universal_ai"
        
        if universal_source.exists():
            shutil.copytree(universal_source, universal_dest / "universal", dirs_exist_ok=True)
            print(f"  ✅ نسخ Universal AI")
    
    def generate_isolation_report(self, isolation_dir: Path):
        """إنشاء تقرير العزل"""
        report = {
            "isolation_system": "Anubis Comprehensive Isolation",
            "created_at": datetime.now().isoformat(),
            "architecture": "Docker-based Microservices",
            "components": len(self.components),
            "isolation_features": [
                "Resource isolation (CPU, Memory)",
                "Network isolation (Bridge network)",
                "Data isolation (Separate volumes)",
                "Security isolation (Non-root users)",
                "Monitoring isolation (Dedicated services)"
            ],
            "services": list(self.components.keys()),
            "networks": ["anubis_network"],
            "volumes": ["database_data", "ollama_models", "logs", "monitoring_data"],
            "ports": {
                "anubis_enhanced": 8000,
                "universal_ai": 8001,
                "ollama": 11434,
                "mysql": 3306,
                "nginx": 80,
                "prometheus": 9090,
                "grafana": 3000
            },
            "security_measures": [
                "Non-root container users",
                "Resource limits",
                "Network segmentation",
                "Read-only configurations",
                "Encrypted database connections"
            ]
        }
        
        report_file = isolation_dir / "isolation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ تقرير العزل: {report_file}")
        return report


def main():
    """الدالة الرئيسية"""
    print("🏺 نظام العزل الشامل لأنوبيس")
    print("🤖 مطور بالتعاون مع Gemini CLI و Ollama")
    print("🔒 عزل متقدم لجميع المكونات")
    
    # إنشاء مدير العزل
    isolation_manager = AnubisIsolationManager()
    
    # إنشاء نظام العزل
    isolation_dir = isolation_manager.create_isolation_structure()
    
    # نسخ الكود المصدري
    isolation_manager.copy_source_code(isolation_dir)
    
    # إنشاء التقرير
    report = isolation_manager.generate_isolation_report(isolation_dir)
    
    print("\n" + "="*60)
    print("🎉 تم إنشاء نظام العزل الشامل بنجاح!")
    print(f"📁 المجلد: {isolation_dir}")
    print(f"🐳 الخدمات: {len(report['services'])}")
    print(f"🔒 ميزات العزل: {len(report['isolation_features'])}")
    print("\n🚀 للبدء:")
    print(f"  cd {isolation_dir}")
    print("  ./scripts/start.sh")
    print("="*60)


if __name__ == "__main__":
    main()
