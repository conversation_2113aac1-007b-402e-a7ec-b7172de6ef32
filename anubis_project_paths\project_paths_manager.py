#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير مسارات مشروع أنوبيس
Anubis Project Paths Manager
"""

import json
import os
from pathlib import Path
from datetime import datetime

class AnubisProjectPathsManager:
    """مدير مسارات مشروع أنوبيس"""
    
    def __init__(self):
        self.project_root = Path("..").resolve()  # العودة للجذر من مجلد المسارات
        self.paths_config = self.generate_paths_config()
        
    def generate_paths_config(self):
        """إنشاء إعدادات المسارات"""
        
        paths_config = {
            "project_info": {
                "name": "Universal AI Assistants (Anubis)",
                "root_path": str(self.project_root),
                "created_at": datetime.now().isoformat(),
                "version": "1.0.0"
            },
            
            "main_system": {
                "description": "النظام الأساسي",
                "paths": {
                    "main_file": "main.py",
                    "readme": "README.md",
                    "requirements": "requirements.txt",
                    "docker_compose": "docker-compose.yml",
                    "dockerfile": "Dockerfile",
                    "system_directory": "anubis_main_system"
                }
            },
            
            "isolation_system": {
                "description": "النظام المعزول",
                "paths": {
                    "root_directory": "anubis_isolation_system",
                    "api_service": "anubis_isolation_system/api",
                    "worker_service": "anubis_isolation_system/worker", 
                    "monitor_service": "anubis_isolation_system/monitor",
                    "docker_compose": "anubis_isolation_system/docker-compose-isolation.yml",
                    "readme": "anubis_isolation_system/README.md",
                    "launcher": "anubis_isolation_system/anubis_docker_isolation_launcher.py",
                    "manager": "anubis_isolation_system/anubis_isolation_system_manager.py"
                }
            },
            
            "ai_team": {
                "description": "فريق الذكاء الاصطناعي",
                "paths": {
                    "root_directory": "anubis_ai_team",
                    "workflow_manager": "anubis_ai_team/team_workflow_manager.py",
                    "collaboration_system": "anubis_ai_team/anubis_ai_team_collaboration_system.py",
                    "collaboration_helper": "anubis_ai_team/anubis_ai_collaboration_helper.py",
                    "team_config": "anubis_ai_team/anubis_ai_team_collaboration_plan.json",
                    "gemini_helper": "anubis_ai_team/anubis_gemini_cli_helper.py",
                    "readme": "anubis_ai_team/README.md"
                }
            },
            
            "project_paths": {
                "description": "إدارة مسارات المشروع",
                "paths": {
                    "root_directory": "anubis_project_paths",
                    "paths_manager": "anubis_project_paths/project_paths_manager.py",
                    "quick_access": "anubis_project_paths/quick_access_shortcuts.py",
                    "navigation_helper": "anubis_project_paths/project_navigation_helper.py",
                    "readme": "anubis_project_paths/README.md"
                }
            },
            
            "data_directories": {
                "description": "مجلدات البيانات والتخزين",
                "paths": {
                    "configs": "configs",
                    "database": "database", 
                    "data": "data",
                    "logs": "logs",
                    "documentation": "documentation",
                    "reports": "reports",
                    "scripts": "scripts",
                    "utilities": "utilities",
                    "archive_and_backups": "archive_and_backups",
                    "workspace": "workspace"
                }
            },
            
            "specialized_systems": {
                "description": "الأنظمة المتخصصة",
                "paths": {
                    "universal_ai_system": "universal_ai_system",
                    "workflows_and_automation": "workflows_and_automation",
                    "tools_and_utilities": "tools_and_utilities",
                    "reports_and_analysis": "reports_and_analysis",
                    "isolation_systems": "isolation_systems",
                    "isolation_configs": "isolation_configs"
                }
            },
            
            "service_endpoints": {
                "description": "نقاط الخدمات",
                "endpoints": {
                    "main_system": {
                        "base_url": "http://localhost:8000",
                        "health": "http://localhost:8000/health",
                        "docs": "http://localhost:8000/docs"
                    },
                    "isolation_api": {
                        "base_url": "http://localhost:8080", 
                        "health": "http://localhost:8080/health",
                        "docs": "http://localhost:8080/docs",
                        "status": "http://localhost:8080/status"
                    },
                    "isolation_monitor": {
                        "base_url": "http://localhost:9090",
                        "health": "http://localhost:9090/health",
                        "services": "http://localhost:9090/monitor/services",
                        "system": "http://localhost:9090/monitor/system"
                    }
                }
            },
            
            "important_files": {
                "description": "الملفات المهمة",
                "files": {
                    "project_analyzer": "anubis_project_analyzer.py",
                    "system_tester": "anubis_complete_system_test.py",
                    "api_tester": "anubis_api_comprehensive_test.py",
                    "move_isolation": "move_isolation_files.py",
                    "organize_team": "organize_ai_team.py"
                }
            }
        }
        
        return paths_config
    
    def get_absolute_path(self, relative_path):
        """الحصول على المسار المطلق"""
        return str(self.project_root / relative_path)
    
    def check_path_exists(self, relative_path):
        """فحص وجود المسار"""
        full_path = self.project_root / relative_path
        return full_path.exists()
    
    def get_system_paths(self, system_name):
        """الحصول على مسارات نظام محدد"""
        return self.paths_config.get(system_name, {}).get("paths", {})
    
    def get_service_endpoints(self, service_name=None):
        """الحصول على نقاط الخدمات"""
        endpoints = self.paths_config.get("service_endpoints", {}).get("endpoints", {})
        if service_name:
            return endpoints.get(service_name, {})
        return endpoints
    
    def generate_navigation_shortcuts(self):
        """إنشاء اختصارات التنقل"""
        shortcuts = {
            "🏺 النظام الأساسي": {
                "main": self.get_absolute_path("main.py"),
                "system": self.get_absolute_path("anubis_main_system"),
                "config": self.get_absolute_path("configs")
            },
            
            "🔒 النظام المعزول": {
                "root": self.get_absolute_path("anubis_isolation_system"),
                "api": self.get_absolute_path("anubis_isolation_system/api"),
                "worker": self.get_absolute_path("anubis_isolation_system/worker"),
                "monitor": self.get_absolute_path("anubis_isolation_system/monitor"),
                "docker": self.get_absolute_path("anubis_isolation_system/docker-compose-isolation.yml")
            },
            
            "🤖 فريق الذكاء الاصطناعي": {
                "root": self.get_absolute_path("anubis_ai_team"),
                "workflow": self.get_absolute_path("anubis_ai_team/team_workflow_manager.py"),
                "collaboration": self.get_absolute_path("anubis_ai_team/anubis_ai_collaboration_helper.py"),
                "gemini": self.get_absolute_path("anubis_ai_team/anubis_gemini_cli_helper.py")
            },
            
            "📁 مجلدات البيانات": {
                "data": self.get_absolute_path("data"),
                "logs": self.get_absolute_path("logs"),
                "reports": self.get_absolute_path("reports"),
                "workspace": self.get_absolute_path("workspace")
            },
            
            "🌐 نقاط الخدمات": self.get_service_endpoints()
        }
        
        return shortcuts
    
    def save_paths_config(self):
        """حفظ إعدادات المسارات"""
        config_file = "anubis_project_paths_config.json"
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.paths_config, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ إعدادات المسارات: {config_file}")
        return config_file
    
    def save_navigation_shortcuts(self):
        """حفظ اختصارات التنقل"""
        shortcuts = self.generate_navigation_shortcuts()
        shortcuts_file = "anubis_navigation_shortcuts.json"
        
        with open(shortcuts_file, 'w', encoding='utf-8') as f:
            json.dump(shortcuts, f, ensure_ascii=False, indent=2)
        
        print(f"🔗 تم حفظ اختصارات التنقل: {shortcuts_file}")
        return shortcuts_file
    
    def display_project_structure(self):
        """عرض بنية المشروع"""
        print("🏺 بنية مشروع أنوبيس")
        print("=" * 60)
        
        for system_name, system_info in self.paths_config.items():
            if system_name == "project_info":
                continue
                
            print(f"\n📋 {system_info.get('description', system_name)}")
            print("-" * 40)
            
            if system_name == "service_endpoints":
                endpoints = system_info.get("endpoints", {})
                for service, urls in endpoints.items():
                    print(f"🌐 {service}:")
                    for endpoint_name, url in urls.items():
                        print(f"   {endpoint_name}: {url}")
            else:
                paths = system_info.get("paths", {})
                for path_name, path_value in paths.items():
                    exists = "✅" if self.check_path_exists(path_value) else "❌"
                    print(f"   {exists} {path_name}: {path_value}")
    
    def generate_quick_access_script(self):
        """إنشاء سكريبت الوصول السريع"""
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 الوصول السريع لمشروع أنوبيس
Anubis Quick Access
"""

import os
import subprocess
import webbrowser
from pathlib import Path

class AnubisQuickAccess:
    """الوصول السريع لمشروع أنوبيس"""
    
    def __init__(self):
        self.project_root = Path("..").resolve()
    
    def open_main_system(self):
        """فتح النظام الأساسي"""
        main_file = self.project_root / "main.py"
        if main_file.exists():
            os.system(f'code "{main_file}"')
            print("🏺 تم فتح النظام الأساسي")
        else:
            print("❌ النظام الأساسي غير موجود")
    
    def open_isolation_system(self):
        """فتح النظام المعزول"""
        isolation_dir = self.project_root / "anubis_isolation_system"
        if isolation_dir.exists():
            os.system(f'code "{isolation_dir}"')
            print("🔒 تم فتح النظام المعزول")
        else:
            print("❌ النظام المعزول غير موجود")
    
    def open_ai_team(self):
        """فتح فريق الذكاء الاصطناعي"""
        team_dir = self.project_root / "anubis_ai_team"
        if team_dir.exists():
            os.system(f'code "{team_dir}"')
            print("🤖 تم فتح فريق الذكاء الاصطناعي")
        else:
            print("❌ فريق الذكاء الاصطناعي غير موجود")
    
    def open_service_urls(self):
        """فتح روابط الخدمات"""
        urls = [
            "http://localhost:8000",  # النظام الأساسي
            "http://localhost:8080",  # النظام المعزول
            "http://localhost:9090"   # المراقبة
        ]
        
        for url in urls:
            webbrowser.open(url)
            print(f"🌐 تم فتح: {url}")
    
    def run_system_tests(self):
        """تشغيل اختبارات النظام"""
        test_files = [
            "anubis_api_comprehensive_test.py",
            "anubis_complete_system_test.py"
        ]
        
        for test_file in test_files:
            test_path = self.project_root / test_file
            if test_path.exists():
                print(f"🧪 تشغيل: {test_file}")
                subprocess.run(["python", str(test_path)])
    
    def show_menu(self):
        """عرض القائمة"""
        print("🏺 الوصول السريع لمشروع أنوبيس")
        print("=" * 40)
        print("1. فتح النظام الأساسي")
        print("2. فتح النظام المعزول") 
        print("3. فتح فريق الذكاء الاصطناعي")
        print("4. فتح روابط الخدمات")
        print("5. تشغيل اختبارات النظام")
        print("0. خروج")
        
        choice = input("\\nاختر رقم: ")
        
        if choice == "1":
            self.open_main_system()
        elif choice == "2":
            self.open_isolation_system()
        elif choice == "3":
            self.open_ai_team()
        elif choice == "4":
            self.open_service_urls()
        elif choice == "5":
            self.run_system_tests()
        elif choice == "0":
            print("👋 وداعاً!")
            return False
        else:
            print("❌ اختيار غير صحيح")
        
        return True

def main():
    """الدالة الرئيسية"""
    access = AnubisQuickAccess()
    
    while True:
        if not access.show_menu():
            break
        input("\\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
'''
        
        with open("quick_access_shortcuts.py", 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("🚀 تم إنشاء سكريبت الوصول السريع: quick_access_shortcuts.py")
    
    def run_full_setup(self):
        """تشغيل الإعداد الكامل"""
        print("🏺 إعداد مدير مسارات مشروع أنوبيس")
        print("=" * 60)
        
        # عرض بنية المشروع
        self.display_project_structure()
        
        # حفظ الإعدادات
        print(f"\n💾 حفظ الإعدادات...")
        self.save_paths_config()
        self.save_navigation_shortcuts()
        
        # إنشاء سكريبت الوصول السريع
        print(f"\n🚀 إنشاء أدوات الوصول السريع...")
        self.generate_quick_access_script()
        
        print(f"\n✅ تم إعداد مدير المسارات بنجاح!")
        
        return True

def main():
    """الدالة الرئيسية"""
    manager = AnubisProjectPathsManager()
    manager.run_full_setup()

if __name__ == "__main__":
    main()
