# 🏺 Anubis Worker Service - Isolated Container
FROM python:3.11-slim

# معلومات الصورة
LABEL maintainer="Anubis System"
LABEL version="1.0.0"
LABEL description="Anubis Worker Service - Isolated"

# إعداد المستخدم غير المميز
RUN groupadd -r anubis && useradd -r -g anubis anubis

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# إنشاء مجلدات العمل
RUN mkdir -p /app/data /app/logs /app/config /app/src /app/tasks
RUN chown -R anubis:anubis /app

# نسخ متطلبات Python
COPY requirements.txt /app/
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r /app/requirements.txt

# نسخ الكود
COPY . /app/src/
RUN chown -R anubis:anubis /app

# التبديل للمستخدم غير المميز
USER anubis

# مجلد العمل
WORKDIR /app/src

# متغيرات البيئة
ENV CELERY_BROKER_URL=redis://anubis-redis-isolated:6379/0
ENV CELERY_RESULT_BACKEND=redis://anubis-redis-isolated:6379/0

# نقطة الدخول
ENTRYPOINT ["python", "worker_simple.py"]
