#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗂️ منظم الملفات الشامل المحسن
Complete Enhanced File Organizer

تنظيم شامل لجميع الملفات المتبقية في المجلد الرئيسي
"""

import json
import shutil
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List


class CompleteFileOrganizer:
    """🗂️ منظم الملفات الشامل"""

    def __init__(self):
        self.base_path = Path(__file__).parent.absolute()
        self.moved_files = {}
        self.skipped_files = []

        # قواعد تنظيم الملفات المحسنة
        self.organization_rules = {
            # ملفات التوثيق
            "docs": {
                "patterns": ["*.md", "*.rst", "*.txt"],
                "specific_files": [
                    "AI_STATUS_REPORT.md",
                    "FINAL_COMPREHENSIVE_REPORT.md",
                    "FINAL_ORGANIZATION_REPORT.md",
                    "FIXES_COMPLETED_REPORT.md",
                    "LANGSMITH_INTEGRATION_ANALYSIS.md",
                    "PROJECT_STRUCTURE.md",
                ],
                "exclude": ["README.md", "README_NEW.md"],  # نبقيها في الجذر
            },
            # ملفات الاختبار
            "tests": {
                "patterns": ["test_*.py", "*_test.py"],
                "specific_files": [
                    "test_ai_fixed.py",
                    "test_ai_integration.py",
                    "test_enhanced_error_detector.py",
                    "test_fixed_agents.py",
                    "test_smart_analyzer.py",
                ],
            },
            # سكريبتات التشغيل
            "scripts": {
                "patterns": [
                    "*fix*.py",
                    "*integration*.py",
                    "*helper*.py",
                    "*demo*.py",
                ],
                "specific_files": [
                    "fix_agents_with_gemini.py",
                    "gemini_cli_helper.py",
                    "gemini_integration_system.py",
                    "langsmith_integration_demo.py",
                    "check_ollama.py",
                ],
            },
            # ملفات مؤقتة للحذف
            "temp": {
                "patterns": ["test_*.css", "test_*.jsx", "test_*.js"],
                "specific_files": [
                    "test_python_file.py",
                    "test_react_file.jsx",
                    "test_style.css",
                ],
            },
            # ملفات التكوين
            "configs": {
                "patterns": ["requirements*.txt"],
                "specific_files": ["requirements.txt", "requirements_database.txt"],
            },
        }

        print("🗂️ منظم الملفات الشامل المحسن")
        print(f"📁 المسار: {self.base_path}")

    def organize_all_files(self):
        """تنظيم جميع الملفات"""
        print("\n🚀 بدء التنظيم الشامل للملفات...")

        # الحصول على جميع الملفات في المجلد الرئيسي
        root_files = [f for f in self.base_path.iterdir() if f.is_file()]

        print(f"📄 عدد الملفات في الجذر: {len(root_files)}")

        for file_path in root_files:
            # تجاهل هذا الملف نفسه
            if file_path.name == "complete_file_organizer.py":
                continue

            target_folder = self._determine_target_folder(file_path)

            if target_folder:
                self._move_file_safely(file_path, target_folder)
            else:
                self.skipped_files.append(str(file_path))
                print(f"   ⏭️ تم تجاهل: {file_path.name}")

        return self.moved_files

    def _determine_target_folder(self, file_path: Path) -> str:
        """تحديد المجلد المناسب للملف"""
        file_name = file_path.name

        # فحص كل قاعدة تنظيم
        for folder, rules in self.organization_rules.items():
            # فحص الملفات المحددة أولاً
            if "specific_files" in rules:
                if file_name in rules["specific_files"]:
                    return folder

            # فحص الأنماط
            if "patterns" in rules:
                for pattern in rules["patterns"]:
                    if self._matches_pattern(file_name, pattern):
                        # فحص الاستثناءات
                        if "exclude" in rules and file_name in rules["exclude"]:
                            continue
                        return folder

        return None

    def _matches_pattern(self, filename: str, pattern: str) -> bool:
        """فحص تطابق النمط"""
        if pattern.startswith("*") and pattern.endswith("*"):
            return pattern[1:-1] in filename
        elif pattern.startswith("*"):
            return filename.endswith(pattern[1:])
        elif pattern.endswith("*"):
            return filename.startswith(pattern[:-1])
        else:
            return filename == pattern

    def _move_file_safely(self, file_path: Path, target_folder: str):
        """نقل الملف بأمان"""
        target_dir = self.base_path / target_folder
        target_path = target_dir / file_path.name

        try:
            # التأكد من وجود المجلد
            target_dir.mkdir(exist_ok=True)

            # إنشاء نسخة احتياطية إذا كان الملف موجوداً
            if target_path.exists():
                backup_dir = self.base_path / "backup"
                backup_dir.mkdir(exist_ok=True)
                backup_path = (
                    backup_dir
                    / f"{file_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_path.suffix}"
                )
                shutil.copy2(target_path, backup_path)
                print(f"   📦 نسخة احتياطية: {backup_path.name}")

            # نقل الملف
            shutil.move(str(file_path), str(target_path))

            # تسجيل النقل
            if target_folder not in self.moved_files:
                self.moved_files[target_folder] = []
            self.moved_files[target_folder].append(file_path.name)

            print(f"   ➡️ {file_path.name} → {target_folder}/")

        except Exception as e:
            print(f"   ❌ خطأ في نقل {file_path.name}: {e}")
            self.skipped_files.append(str(file_path))

    def clean_empty_directories(self):
        """تنظيف المجلدات الفارغة"""
        print("\n🧹 تنظيف المجلدات الفارغة...")

        cleaned_dirs = []

        for item in self.base_path.iterdir():
            if item.is_dir() and item.name not in [
                "core",
                "agents",
                "tests",
                "scripts",
                "docs",
                "configs",
                "reports",
                "logs",
                "backup",
                "temp",
                "examples",
                "tools",
            ]:
                try:
                    # فحص إذا كان المجلد فارغ أو يحتوي على ملفات غير مهمة فقط
                    contents = list(item.rglob("*"))
                    important_files = [
                        f
                        for f in contents
                        if f.is_file()
                        and not f.name.startswith(".")
                        and f.suffix not in [".pyc", ".log"]
                    ]

                    if len(important_files) == 0:
                        # نقل إلى archive إذا كان يحتوي على ملفات
                        if contents:
                            archive_dir = self.base_path / "archive" / item.name
                            archive_dir.parent.mkdir(exist_ok=True)
                            shutil.move(str(item), str(archive_dir))
                            print(f"   📦 أرشفة: {item.name} → archive/")
                        else:
                            # حذف إذا كان فارغ تماماً
                            shutil.rmtree(item)
                            print(f"   🗑️ حذف مجلد فارغ: {item.name}")

                        cleaned_dirs.append(item.name)

                except Exception as e:
                    print(f"   ⚠️ خطأ في تنظيف {item.name}: {e}")

        return cleaned_dirs

    def clean_cache_files(self):
        """تنظيف ملفات الكاش"""
        print("\n🧹 تنظيف ملفات الكاش...")

        cache_patterns = ["__pycache__", "*.pyc", "*.pyo", ".pytest_cache"]
        cleaned_files = []

        for pattern in cache_patterns:
            if pattern.startswith("__") and pattern.endswith("__"):
                # مجلدات كاش
                for cache_dir in self.base_path.rglob(pattern):
                    if cache_dir.is_dir():
                        try:
                            shutil.rmtree(cache_dir)
                            cleaned_files.append(str(cache_dir))
                            print(f"   🗑️ حذف كاش: {cache_dir}")
                        except Exception as e:
                            print(f"   ⚠️ خطأ في حذف {cache_dir}: {e}")
            else:
                # ملفات كاش
                for cache_file in self.base_path.rglob(pattern):
                    if cache_file.is_file():
                        try:
                            cache_file.unlink()
                            cleaned_files.append(str(cache_file))
                            print(f"   🗑️ حذف: {cache_file.name}")
                        except Exception as e:
                            print(f"   ⚠️ خطأ في حذف {cache_file}: {e}")

        return cleaned_files

    def generate_organization_report(self):
        """إنتاج تقرير التنظيم"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "operation": "complete_file_organization",
            "moved_files": self.moved_files,
            "skipped_files": self.skipped_files,
            "summary": {
                "total_moved": sum(len(files) for files in self.moved_files.values()),
                "total_skipped": len(self.skipped_files),
                "folders_used": list(self.moved_files.keys()),
            },
        }

        # حفظ التقرير
        reports_dir = self.base_path / "reports"
        reports_dir.mkdir(exist_ok=True)

        report_file = (
            reports_dir
            / f'complete_organization_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )

        try:
            with open(report_file, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"\n📄 تم حفظ تقرير التنظيم: {report_file}")
            return str(report_file)

        except Exception as e:
            print(f"\n❌ خطأ في حفظ التقرير: {e}")
            return None

    def run_complete_organization(self):
        """تشغيل التنظيم الشامل"""
        print("🚀 بدء التنظيم الشامل لنظام أنوبيس")
        print("=" * 50)

        # تنظيم الملفات
        moved_files = self.organize_all_files()

        # تنظيف ملفات الكاش
        cache_files = self.clean_cache_files()

        # تنظيف المجلدات الفارغة
        cleaned_dirs = self.clean_empty_directories()

        # إنتاج التقرير
        report_file = self.generate_organization_report()

        # عرض النتائج
        print(f"\n🏆 تم إكمال التنظيم الشامل!")
        print(f"📄 الملفات المنقولة: {sum(len(files) for files in moved_files.values())}")
        print(f"⏭️ الملفات المتجاهلة: {len(self.skipped_files)}")
        print(f"🗑️ ملفات الكاش المحذوفة: {len(cache_files)}")
        print(f"📁 المجلدات المنظفة: {len(cleaned_dirs)}")

        # عرض تفاصيل النقل
        if moved_files:
            print(f"\n📋 تفاصيل النقل:")
            for folder, files in moved_files.items():
                print(f"   📂 {folder}: {len(files)} ملف")

        return {
            "moved_files": moved_files,
            "skipped_files": self.skipped_files,
            "cache_files": cache_files,
            "cleaned_dirs": cleaned_dirs,
            "report_file": report_file,
        }


def main():
    """الدالة الرئيسية"""
    organizer = CompleteFileOrganizer()
    result = organizer.run_complete_organization()

    print(f"\n✅ تم تنظيم المشروع بالكامل!")
    print(f"🏺 نظام أنوبيس منظم ونظيف!")

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
