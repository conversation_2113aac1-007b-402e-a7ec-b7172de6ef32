#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مساعد التنقل في مشروع أنوبيس
Anubis Project Navigation Helper
"""

import os
import json
import webbrowser
import subprocess
from pathlib import Path

class AnubisNavigationHelper:
    """مساعد التنقل في مشروع أنوبيس"""
    
    def __init__(self):
        self.project_root = Path("..").resolve()
        self.shortcuts = self.load_shortcuts()
        
    def load_shortcuts(self):
        """تحميل اختصارات التنقل"""
        shortcuts_file = "anubis_navigation_shortcuts.json"
        if Path(shortcuts_file).exists():
            with open(shortcuts_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def open_in_vscode(self, path):
        """فتح في VS Code"""
        try:
            subprocess.run(['code', str(path)], check=True)
            print(f"📝 تم فتح في VS Code: {path}")
            return True
        except:
            print(f"❌ فشل فتح VS Code: {path}")
            return False
    
    def open_in_explorer(self, path):
        """فتح في مستكشف الملفات"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(path)
            elif os.name == 'posix':  # macOS/Linux
                subprocess.run(['open', path] if os.uname().sysname == 'Darwin' else ['xdg-open', path])
            print(f"📁 تم فتح في المستكشف: {path}")
            return True
        except:
            print(f"❌ فشل فتح المستكشف: {path}")
            return False
    
    def open_url(self, url):
        """فتح رابط في المتصفح"""
        try:
            webbrowser.open(url)
            print(f"🌐 تم فتح الرابط: {url}")
            return True
        except:
            print(f"❌ فشل فتح الرابط: {url}")
            return False
    
    def run_python_file(self, file_path):
        """تشغيل ملف Python"""
        try:
            full_path = self.project_root / file_path
            if full_path.exists():
                subprocess.run(['python', str(full_path)], cwd=str(self.project_root))
                print(f"🐍 تم تشغيل: {file_path}")
                return True
            else:
                print(f"❌ الملف غير موجود: {file_path}")
                return False
        except Exception as e:
            print(f"❌ خطأ في التشغيل: {e}")
            return False
    
    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        print("🏺 مساعد التنقل في مشروع أنوبيس")
        print("=" * 50)
        print("1. 🏺 النظام الأساسي")
        print("2. 🔒 النظام المعزول")
        print("3. 🤖 فريق الذكاء الاصطناعي")
        print("4. 📁 مجلدات البيانات")
        print("5. 🌐 فتح الخدمات")
        print("6. 🧪 تشغيل الاختبارات")
        print("7. 🛠️ أدوات التطوير")
        print("0. خروج")
        
        choice = input("\nاختر رقم: ")
        return choice
    
    def handle_main_system(self):
        """التعامل مع النظام الأساسي"""
        print("\n🏺 النظام الأساسي:")
        print("1. فتح main.py")
        print("2. فتح مجلد النظام")
        print("3. فتح الإعدادات")
        print("4. فتح في المتصفح (Port 8000)")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.open_in_vscode(self.project_root / "main.py")
        elif choice == "2":
            self.open_in_vscode(self.project_root / "anubis_main_system")
        elif choice == "3":
            self.open_in_vscode(self.project_root / "configs")
        elif choice == "4":
            self.open_url("http://localhost:8000")
    
    def handle_isolation_system(self):
        """التعامل مع النظام المعزول"""
        print("\n🔒 النظام المعزول:")
        print("1. فتح مجلد النظام المعزول")
        print("2. فتح خدمة API")
        print("3. فتح خدمة Worker")
        print("4. فتح خدمة Monitor")
        print("5. فتح Docker Compose")
        print("6. فتح API في المتصفح (Port 8080)")
        print("7. فتح Monitor في المتصفح (Port 9090)")
        print("8. تشغيل مدير النظام المعزول")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.open_in_vscode(self.project_root / "anubis_isolation_system")
        elif choice == "2":
            self.open_in_vscode(self.project_root / "anubis_isolation_system/api")
        elif choice == "3":
            self.open_in_vscode(self.project_root / "anubis_isolation_system/worker")
        elif choice == "4":
            self.open_in_vscode(self.project_root / "anubis_isolation_system/monitor")
        elif choice == "5":
            self.open_in_vscode(self.project_root / "anubis_isolation_system/docker-compose-isolation.yml")
        elif choice == "6":
            self.open_url("http://localhost:8080")
        elif choice == "7":
            self.open_url("http://localhost:9090")
        elif choice == "8":
            self.run_python_file("anubis_isolation_system/anubis_isolation_system_manager.py")
    
    def handle_ai_team(self):
        """التعامل مع فريق الذكاء الاصطناعي"""
        print("\n🤖 فريق الذكاء الاصطناعي:")
        print("1. فتح مجلد الفريق")
        print("2. تشغيل مدير سير العمل")
        print("3. تشغيل مساعد التعاون")
        print("4. تشغيل مساعد Gemini CLI")
        print("5. فتح إعدادات الفريق")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.open_in_vscode(self.project_root / "anubis_ai_team")
        elif choice == "2":
            self.run_python_file("anubis_ai_team/team_workflow_manager.py")
        elif choice == "3":
            self.run_python_file("anubis_ai_team/anubis_ai_collaboration_helper.py")
        elif choice == "4":
            self.run_python_file("anubis_ai_team/anubis_gemini_cli_helper.py")
        elif choice == "5":
            self.open_in_vscode(self.project_root / "anubis_ai_team/anubis_ai_team_collaboration_plan.json")
    
    def handle_data_directories(self):
        """التعامل مع مجلدات البيانات"""
        print("\n📁 مجلدات البيانات:")
        print("1. مجلد البيانات (data)")
        print("2. مجلد السجلات (logs)")
        print("3. مجلد التقارير (reports)")
        print("4. مجلد العمل (workspace)")
        print("5. مجلد الإعدادات (configs)")
        print("6. مجلد قاعدة البيانات (database)")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        directories = {
            "1": "data",
            "2": "logs", 
            "3": "reports",
            "4": "workspace",
            "5": "configs",
            "6": "database"
        }
        
        if choice in directories:
            self.open_in_explorer(self.project_root / directories[choice])
    
    def handle_services(self):
        """التعامل مع الخدمات"""
        print("\n🌐 فتح الخدمات:")
        print("1. النظام الأساسي (Port 8000)")
        print("2. API المعزول (Port 8080)")
        print("3. Monitor المعزول (Port 9090)")
        print("4. توثيق API الأساسي")
        print("5. توثيق API المعزول")
        print("6. فتح جميع الخدمات")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.open_url("http://localhost:8000")
        elif choice == "2":
            self.open_url("http://localhost:8080")
        elif choice == "3":
            self.open_url("http://localhost:9090")
        elif choice == "4":
            self.open_url("http://localhost:8000/docs")
        elif choice == "5":
            self.open_url("http://localhost:8080/docs")
        elif choice == "6":
            urls = [
                "http://localhost:8000",
                "http://localhost:8080", 
                "http://localhost:9090"
            ]
            for url in urls:
                self.open_url(url)
    
    def handle_tests(self):
        """التعامل مع الاختبارات"""
        print("\n🧪 تشغيل الاختبارات:")
        print("1. اختبار API شامل")
        print("2. اختبار النظام الكامل")
        print("3. فحص حالة النظام المعزول")
        print("4. تحليل المشروع")
        print("5. تشغيل جميع الاختبارات")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.run_python_file("anubis_api_comprehensive_test.py")
        elif choice == "2":
            self.run_python_file("anubis_complete_system_test.py")
        elif choice == "3":
            self.run_python_file("anubis_isolation_system/anubis_isolation_status_checker.py")
        elif choice == "4":
            self.run_python_file("anubis_project_analyzer.py")
        elif choice == "5":
            tests = [
                "anubis_api_comprehensive_test.py",
                "anubis_complete_system_test.py",
                "anubis_project_analyzer.py"
            ]
            for test in tests:
                print(f"\n🧪 تشغيل: {test}")
                self.run_python_file(test)
    
    def handle_dev_tools(self):
        """التعامل مع أدوات التطوير"""
        print("\n🛠️ أدوات التطوير:")
        print("1. فتح المشروع كاملاً في VS Code")
        print("2. فتح مجلد المسارات")
        print("3. تحديث إعدادات المسارات")
        print("4. عرض بنية المشروع")
        print("0. رجوع")
        
        choice = input("اختر: ")
        
        if choice == "1":
            self.open_in_vscode(self.project_root)
        elif choice == "2":
            self.open_in_vscode(self.project_root / "anubis_project_paths")
        elif choice == "3":
            self.run_python_file("anubis_project_paths/project_paths_manager.py")
        elif choice == "4":
            from project_paths_manager import AnubisProjectPathsManager
            manager = AnubisProjectPathsManager()
            manager.display_project_structure()
    
    def run(self):
        """تشغيل مساعد التنقل"""
        while True:
            choice = self.show_main_menu()
            
            if choice == "1":
                self.handle_main_system()
            elif choice == "2":
                self.handle_isolation_system()
            elif choice == "3":
                self.handle_ai_team()
            elif choice == "4":
                self.handle_data_directories()
            elif choice == "5":
                self.handle_services()
            elif choice == "6":
                self.handle_tests()
            elif choice == "7":
                self.handle_dev_tools()
            elif choice == "0":
                print("👋 وداعاً!")
                break
            else:
                print("❌ اختيار غير صحيح")
            
            input("\nاضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    helper = AnubisNavigationHelper()
    helper.run()

if __name__ == "__main__":
    main()
