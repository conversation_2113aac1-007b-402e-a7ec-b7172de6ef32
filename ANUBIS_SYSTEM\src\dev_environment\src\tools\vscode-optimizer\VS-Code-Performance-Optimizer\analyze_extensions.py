#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل إضافات VS Code المثبتة
"""

import os
import json
import glob

def analyze_extensions():
    """تحليل إضافات VS Code المثبتة"""
    
    print('🔌 تحليل إضافات VS Code المثبتة...')
    print('=' * 60)
    
    # مسار إضافات VS Code
    extensions_path = os.path.expanduser('~/.vscode/extensions')
    if not os.path.exists(extensions_path):
        extensions_path = os.path.expanduser('~/AppData/Roaming/Code/User/extensions')
    
    if not os.path.exists(extensions_path):
        print('❌ لم يتم العثور على مجلد الإضافات')
        return
    
    print(f'📁 مسار الإضافات: {extensions_path}')
    
    # قائمة الإضافات المثبتة
    extensions = []
    for ext_dir in os.listdir(extensions_path):
        ext_path = os.path.join(extensions_path, ext_dir)
        if os.path.isdir(ext_path):
            package_json = os.path.join(ext_path, 'package.json')
            if os.path.exists(package_json):
                try:
                    with open(package_json, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        extensions.append({
                            'name': data.get('displayName', data.get('name', ext_dir)),
                            'id': data.get('name', ext_dir),
                            'version': data.get('version', 'unknown'),
                            'publisher': data.get('publisher', 'unknown'),
                            'description': data.get('description', '')[:100],
                            'folder_size': sum(os.path.getsize(os.path.join(dirpath, filename))
                                             for dirpath, dirnames, filenames in os.walk(ext_path)
                                             for filename in filenames) / (1024*1024)  # MB
                        })
                except:
                    extensions.append({
                        'name': ext_dir,
                        'id': ext_dir,
                        'version': 'unknown',
                        'publisher': 'unknown',
                        'description': '',
                        'folder_size': 0
                    })
    
    print(f'\n📊 إجمالي الإضافات المثبتة: {len(extensions)}')
    
    # ترتيب الإضافات حسب الحجم
    extensions.sort(key=lambda x: x['folder_size'], reverse=True)
    
    print('\n🔍 أكبر الإضافات حجماً (قد تسبب بطء):')
    print('-' * 80)
    print("الاسم                          الحجم (MB)    الإصدار     الناشر")
    print('-' * 80)
    
    for ext in extensions[:15]:  # أكبر 15 إضافة
        name = ext['name'][:29]
        size = f"{ext['folder_size']:.1f}"
        version = ext['version'][:9]
        publisher = ext['publisher'][:19]
        
        # تحديد الإضافات المشبوهة
        if ext['folder_size'] > 50:
            status = '🔴'
        elif ext['folder_size'] > 20:
            status = '🟡'
        else:
            status = '🟢'
        
        print(f"{status} {name:<29} {size:<11} {version:<9} {publisher}")
    
    # الإضافات المعروفة بأنها تسبب بطء
    heavy_extensions = [
        'python', 'pylance', 'jupyter', 'intellicode', 'copilot',
        'eslint', 'prettier', 'live-server', 'auto-rename-tag',
        'bracket-pair-colorizer', 'gitlens', 'docker'
    ]
    
    print('\n⚠️ الإضافات المحتملة لتسبب البطء:')
    found_heavy = []
    for ext in extensions:
        for heavy in heavy_extensions:
            if heavy.lower() in ext['id'].lower():
                found_heavy.append(ext)
                break
    
    if found_heavy:
        for ext in found_heavy:
            print(f"  🔴 {ext['name']} - {ext['folder_size']:.1f} MB")
    else:
        print('  ✅ لم يتم العثور على إضافات معروفة بالبطء')
    
    print('\n💡 توصيات لتحسين الأداء:')
    print('1. عطل الإضافات الكبيرة غير المستخدمة')
    print('2. حدث الإضافات للإصدارات الأحدث')
    print('3. استخدم إضافات أخف بديلة')
    print('4. عطل الإضافات مؤقتاً لاختبار الأداء')
    
    return extensions

if __name__ == "__main__":
    try:
        analyze_extensions()
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")
