#!/bin/bash

# VS Code Process Control Dashboard - Linux/Mac Launcher
# ====================================================

# إعداد الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# الرموز
SUCCESS="✅"
ERROR="❌"
WARNING="⚠️"
INFO="ℹ️"
ROCKET="🚀"
GEAR="⚙️"
DASHBOARD="🎛️"

# دالة طباعة ملونة
print_message() {
    local color=$1
    local symbol=$2
    local message=$3
    echo -e "${color}${symbol} ${message}${NC}"
}

# عرض الترويسة
show_header() {
    clear
    echo -e "${PURPLE}████████████████████████████████████████████████████████████████${NC}"
    echo -e "${PURPLE}█                                                              █${NC}"
    echo -e "${PURPLE}█           ${DASHBOARD} VS Code Process Control Dashboard ${DASHBOARD}          █${NC}"
    echo -e "${PURPLE}█                                                              █${NC}"
    echo -e "${PURPLE}█  واجهة تحكم شاملة لإدارة العمليات والإضافات                █${NC}"
    echo -e "${PURPLE}█                                                              █${NC}"
    echo -e "${PURPLE}████████████████████████████████████████████████████████████████${NC}"
    echo ""
}

# فحص Python
check_python() {
    print_message $CYAN $INFO "فحص تثبيت Python..."
    
    if command -v python3 &> /dev/null; then
        local version=$(python3 --version 2>&1)
        print_message $GREEN $SUCCESS "Python متاح: $version"
        PYTHON_CMD="python3"
        return 0
    elif command -v python &> /dev/null; then
        local version=$(python --version 2>&1)
        if [[ $version == *"Python 3"* ]]; then
            print_message $GREEN $SUCCESS "Python متاح: $version"
            PYTHON_CMD="python"
            return 0
        else
            print_message $RED $ERROR "يتطلب Python 3.6 أو أحدث"
            return 1
        fi
    else
        print_message $RED $ERROR "Python غير مثبت"
        print_message $YELLOW $WARNING "تثبيت Python:"
        echo "  Ubuntu/Debian: sudo apt-get install python3 python3-pip python3-tk"
        echo "  CentOS/RHEL: sudo yum install python3 python3-pip tkinter"
        echo "  macOS: brew install python-tk"
        return 1
    fi
}

# فحص المكتبات المطلوبة
check_packages() {
    print_message $CYAN $INFO "فحص المكتبات المطلوبة..."
    
    local missing_packages=()
    
    # فحص psutil
    if ! $PYTHON_CMD -c "import psutil" &> /dev/null; then
        print_message $RED $ERROR "psutil غير مثبت"
        missing_packages+=("psutil")
    else
        print_message $GREEN $SUCCESS "psutil متاح"
    fi
    
    # فحص tkinter
    if ! $PYTHON_CMD -c "import tkinter" &> /dev/null; then
        print_message $RED $ERROR "tkinter غير متاح"
        print_message $YELLOW $WARNING "تثبيت tkinter:"
        echo "  Ubuntu/Debian: sudo apt-get install python3-tk"
        echo "  CentOS/RHEL: sudo yum install tkinter"
        echo "  macOS: مثبت مع Python عادة"
    else
        print_message $GREEN $SUCCESS "tkinter متاح"
    fi
    
    # تثبيت المكتبات المفقودة
    if [ ${#missing_packages[@]} -gt 0 ]; then
        print_message $YELLOW $WARNING "المكتبات المفقودة: ${missing_packages[*]}"
        read -p "هل تريد تثبيتها الآن؟ (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for package in "${missing_packages[@]}"; do
                print_message $CYAN $GEAR "تثبيت $package..."
                if $PYTHON_CMD -m pip install $package; then
                    print_message $GREEN $SUCCESS "تم تثبيت $package"
                else
                    print_message $RED $ERROR "فشل في تثبيت $package"
                    return 1
                fi
            done
        else
            return 1
        fi
    fi
    
    return 0
}

# فحص VS Code
check_vscode() {
    print_message $CYAN $INFO "فحص VS Code..."
    
    if command -v code &> /dev/null; then
        local version=$(code --version 2>&1 | head -n 1)
        print_message $GREEN $SUCCESS "VS Code متاح: $version"
        return 0
    else
        print_message $YELLOW $WARNING "VS Code غير متاح في PATH"
        print_message $YELLOW $WARNING "بعض المميزات قد لا تعمل"
        print_message $CYAN $INFO "تثبيت VS Code:"
        echo "  Ubuntu/Debian: wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg"
        echo "  macOS: brew install --cask visual-studio-code"
        return 1
    fi
}

# فحص ملفات لوحة التحكم
check_dashboard_files() {
    print_message $CYAN $INFO "فحص ملفات لوحة التحكم..."
    
    local required_files=("process_control_dashboard.py" "dashboard_config.json")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_message $GREEN $SUCCESS "$file موجود"
        else
            print_message $RED $ERROR "$file غير موجود"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_message $RED $ERROR "الملفات المفقودة: ${missing_files[*]}"
        return 1
    fi
    
    return 0
}

# تشغيل لوحة التحكم
run_dashboard() {
    print_message $CYAN $ROCKET "تشغيل لوحة التحكم..."
    echo "================================================================"
    
    # التأكد من الصلاحيات
    if [ ! -x "process_control_dashboard.py" ]; then
        chmod +x process_control_dashboard.py
    fi
    
    # تشغيل التطبيق
    if $PYTHON_CMD process_control_dashboard.py; then
        print_message $GREEN $SUCCESS "تم إغلاق لوحة التحكم بنجاح"
    else
        print_message $RED $ERROR "حدث خطأ في تشغيل لوحة التحكم"
        return 1
    fi
}

# عرض معلومات النظام
show_system_info() {
    print_message $CYAN $INFO "معلومات النظام:"
    echo "  نظام التشغيل: $(uname -s)"
    echo "  الإصدار: $(uname -r)"
    echo "  المعمارية: $(uname -m)"
    echo "  Shell: $SHELL"
    echo ""
}

# الدالة الرئيسية
main() {
    show_header
    
    # عرض معلومات النظام إذا طُلب
    if [[ "$1" == "--verbose" || "$1" == "-v" ]]; then
        show_system_info
    fi
    
    # فحص المتطلبات
    if [[ "$1" != "--skip-checks" ]]; then
        print_message $CYAN $GEAR "فحص المتطلبات..."
        echo ""
        
        # فحص Python
        if ! check_python; then
            print_message $RED $ERROR "فشل في فحص Python"
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        
        # فحص المكتبات
        if ! check_packages; then
            print_message $RED $ERROR "فشل في فحص المكتبات"
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        
        # فحص VS Code (اختياري)
        check_vscode
        
        # فحص ملفات لوحة التحكم
        if ! check_dashboard_files; then
            print_message $RED $ERROR "فشل في فحص ملفات لوحة التحكم"
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        
        echo ""
        print_message $GREEN $SUCCESS "جميع المتطلبات متوفرة!"
        echo ""
    fi
    
    # تشغيل لوحة التحكم
    run_dashboard
    
    if [[ "$1" != "--skip-checks" ]]; then
        read -p "اضغط Enter للخروج..."
    fi
}

# معالجة المعاملات
case "$1" in
    --help|-h)
        echo "استخدام: $0 [OPTIONS]"
        echo ""
        echo "الخيارات:"
        echo "  --help, -h          عرض هذه المساعدة"
        echo "  --verbose, -v       عرض معلومات مفصلة"
        echo "  --skip-checks       تخطي فحص المتطلبات"
        echo "  --install-deps      تثبيت المكتبات تلقائياً"
        echo ""
        echo "أمثلة:"
        echo "  $0                  تشغيل عادي"
        echo "  $0 --verbose        تشغيل مع معلومات مفصلة"
        echo "  $0 --skip-checks    تشغيل سريع بدون فحص"
        exit 0
        ;;
    *)
        # تشغيل البرنامج الرئيسي
        main "$@"
        ;;
esac
