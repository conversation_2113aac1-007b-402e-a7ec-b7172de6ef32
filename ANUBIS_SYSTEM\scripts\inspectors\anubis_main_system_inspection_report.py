#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 فاحص مجلد anubis_main_system الشامل
Anubis Main System Comprehensive Inspector
"""

import os
import json
from pathlib import Path
from datetime import datetime

class AnubisMainSystemInspector:
    def __init__(self):
        self.base_path = Path("anubis_main_system")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "system_name": "Anubis Main System",
            "inspection_type": "comprehensive_analysis",
            "overall_health": "unknown",
            "components": {},
            "issues": [],
            "recommendations": [],
            "next_steps": []
        }
    
    def inspect_core_anubis(self):
        """فحص النواة الأساسية core/anubis"""
        core_path = self.base_path / "core" / "anubis"
        print("🔍 فحص النواة الأساسية core/anubis...")
        
        component_health = {
            "status": "active",
            "files_count": 0,
            "subcomponents": {},
            "issues": [],
            "strengths": []
        }
        
        # فحص الملفات الرئيسية
        key_files = ["main.py", "__init__.py", "README.md"]
        for file in key_files:
            file_path = core_path / file
            if file_path.exists():
                component_health["strengths"].append(f"✅ {file} موجود")
                component_health["files_count"] += 1
            else:
                component_health["issues"].append(f"❌ {file} مفقود")
        
        # فحص المجلدات الفرعية
        subdirs = ["agents", "api", "configs", "core", "database", "docs", "scripts"]
        for subdir in subdirs:
            subdir_path = core_path / subdir
            if subdir_path.exists():
                files_count = len(list(subdir_path.glob("*")))
                component_health["subcomponents"][subdir] = {
                    "exists": True,
                    "files_count": files_count,
                    "status": "✅ نشط" if files_count > 0 else "⚠️ فارغ"
                }
                component_health["strengths"].append(f"✅ {subdir}/ موجود ({files_count} ملف)")
            else:
                component_health["subcomponents"][subdir] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ مجلد {subdir}/ مفقود")
        
        self.report["components"]["core_anubis"] = component_health
        return component_health
    
    def inspect_isolation_systems(self):
        """فحص أنظمة العزل"""
        print("🔍 فحص أنظمة العزل...")
        
        isolation_systems = {
            "basic_isolation": self.base_path / "core" / "anubis_isolated_system",
            "advanced_isolation": self.base_path / "core" / "anubis_advanced_isolation"
        }
        
        component_health = {
            "status": "active",
            "systems": {},
            "docker_configs": 0,
            "issues": [],
            "strengths": []
        }
        
        for system_name, system_path in isolation_systems.items():
            if system_path.exists():
                docker_compose = system_path / "docker-compose.yml"
                has_docker = docker_compose.exists()
                
                dirs_count = len([d for d in system_path.iterdir() if d.is_dir()])
                files_count = len([f for f in system_path.iterdir() if f.is_file()])
                
                component_health["systems"][system_name] = {
                    "exists": True,
                    "has_docker": has_docker,
                    "directories": dirs_count,
                    "files": files_count,
                    "status": "✅ مكتمل" if has_docker else "⚠️ ناقص"
                }
                
                if has_docker:
                    component_health["docker_configs"] += 1
                    component_health["strengths"].append(f"✅ {system_name} مع Docker")
                else:
                    component_health["issues"].append(f"⚠️ {system_name} بدون Docker")
            else:
                component_health["systems"][system_name] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ نظام {system_name} مفقود")
        
        self.report["components"]["isolation_systems"] = component_health
        return component_health
    
    def inspect_main_scripts(self):
        """فحص السكريبتات الرئيسية"""
        print("🔍 فحص السكريبتات الرئيسية...")
        
        component_health = {
            "status": "active",
            "scripts": {},
            "total_scripts": 0,
            "issues": [],
            "strengths": []
        }
        
        # السكريبتات المهمة في المجلد الرئيسي
        main_scripts = [
            "anubis_comprehensive_scanner.py",
            "anubis_agents_comprehensive_scanner.py",
            "anubis_comprehensive_project_scanner.py",
            "anubis_isolation_system.py",
            "anubis_advanced_isolation_system.py",
            "anubis_internal_system_test.py"
        ]
        
        for script in main_scripts:
            script_path = self.base_path / script
            if script_path.exists():
                size = script_path.stat().st_size
                component_health["scripts"][script] = {
                    "exists": True,
                    "size": size,
                    "status": "✅ موجود"
                }
                component_health["total_scripts"] += 1
                component_health["strengths"].append(f"✅ {script} ({size} bytes)")
            else:
                component_health["scripts"][script] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ {script} مفقود")
        
        self.report["components"]["main_scripts"] = component_health
        return component_health
    
    def analyze_configs(self):
        """تحليل ملفات التكوين"""
        print("🔍 تحليل ملفات التكوين...")
        
        configs_path = self.base_path / "core" / "anubis" / "configs"
        component_health = {
            "status": "active",
            "configs": {},
            "total_configs": 0,
            "issues": [],
            "strengths": []
        }
        
        # ملفات التكوين المهمة
        important_configs = [
            "ai_config.json",
            "database_config.json", 
            "default_config.json",
            "langsmith_config.json"
        ]
        
        for config in important_configs:
            config_path = configs_path / config
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    component_health["configs"][config] = {
                        "exists": True,
                        "valid_json": True,
                        "keys_count": len(data) if isinstance(data, dict) else 0,
                        "status": "✅ صحيح"
                    }
                    component_health["total_configs"] += 1
                    component_health["strengths"].append(f"✅ {config} صحيح")
                    
                except json.JSONDecodeError:
                    component_health["configs"][config] = {
                        "exists": True,
                        "valid_json": False,
                        "status": "❌ JSON غير صحيح"
                    }
                    component_health["issues"].append(f"❌ {config} - JSON غير صحيح")
            else:
                component_health["configs"][config] = {
                    "exists": False,
                    "status": "❌ مفقود"
                }
                component_health["issues"].append(f"❌ {config} مفقود")
        
        self.report["components"]["configs"] = component_health
        return component_health
    
    def evaluate_overall_health(self):
        """تقييم الحالة العامة للنظام"""
        print("🏥 تقييم الحالة العامة...")
        
        total_issues = 0
        total_strengths = 0
        
        for component_name, component in self.report["components"].items():
            if "issues" in component:
                total_issues += len(component["issues"])
            if "strengths" in component:
                total_strengths += len(component["strengths"])
        
        # تحديد الحالة العامة
        if total_issues == 0:
            self.report["overall_health"] = "excellent"
            health_emoji = "🟢"
            health_text = "ممتاز"
        elif total_issues <= 3:
            self.report["overall_health"] = "good"
            health_emoji = "🟡"
            health_text = "جيد"
        elif total_issues <= 6:
            self.report["overall_health"] = "fair"
            health_emoji = "🟠"
            health_text = "متوسط"
        else:
            self.report["overall_health"] = "poor"
            health_emoji = "🔴"
            health_text = "يحتاج عمل"
        
        print(f"\n{health_emoji} الحالة العامة: {health_text}")
        print(f"📊 المشاكل: {total_issues}")
        print(f"✅ نقاط القوة: {total_strengths}")
        
        return self.report["overall_health"]
    
    def generate_recommendations(self):
        """إنشاء التوصيات"""
        print("💡 إنشاء التوصيات...")
        
        # تحليل المشاكل وإنشاء توصيات
        all_issues = []
        for component in self.report["components"].values():
            if "issues" in component:
                all_issues.extend(component["issues"])
        
        recommendations = []
        
        if any("مفقود" in issue for issue in all_issues):
            recommendations.append("🔧 إصلاح الملفات المفقودة - أولوية عالية")
        
        if any("JSON غير صحيح" in issue for issue in all_issues):
            recommendations.append("📝 إصلاح ملفات التكوين التالفة")
        
        if any("Docker" in issue for issue in all_issues):
            recommendations.append("🐳 إكمال إعداد أنظمة العزل Docker")
        
        # توصيات عامة
        recommendations.extend([
            "🧪 تشغيل اختبارات شاملة للنظام",
            "📚 تحديث التوثيق حسب الحالة الحالية",
            "🔄 إنشاء نسخ احتياطية للملفات المهمة",
            "⚡ تحسين الأداء والاستقرار"
        ])
        
        self.report["recommendations"] = recommendations
        return recommendations
    
    def generate_next_steps(self):
        """إنشاء الخطوات التالية"""
        next_steps = []
        
        health = self.report["overall_health"]
        
        if health in ["poor", "fair"]:
            next_steps.extend([
                "🚨 إصلاح المشاكل الحرجة فوراً",
                "🔧 استعادة الملفات المفقودة",
                "🧪 اختبار المكونات المصلحة"
            ])
        
        if health in ["good", "excellent"]:
            next_steps.extend([
                "🚀 تشغيل اختبارات شاملة",
                "📈 تحسين الأداء",
                "🌟 إضافة ميزات جديدة"
            ])
        
        # خطوات عامة
        next_steps.extend([
            "📊 مراقبة الأداء والاستقرار",
            "🔄 تحديث دوري للنظام",
            "📚 توثيق التحسينات والتغييرات"
        ])
        
        self.report["next_steps"] = next_steps
        return next_steps
    
    def run_inspection(self):
        """تشغيل الفحص الشامل"""
        print("🏺 بدء فحص مجلد anubis_main_system الشامل")
        print("=" * 60)
        
        # تشغيل جميع عمليات الفحص
        self.inspect_core_anubis()
        self.inspect_isolation_systems()
        self.inspect_main_scripts()
        self.analyze_configs()
        
        # تقييم وتوصيات
        self.evaluate_overall_health()
        self.generate_recommendations()
        self.generate_next_steps()
        
        return self.report
    
    def print_detailed_report(self):
        """طباعة التقرير المفصل"""
        print("\n" + "="*60)
        print("📋 تقرير فحص anubis_main_system المفصل")
        print("="*60)
        
        # الحالة العامة
        health_emojis = {
            "excellent": "🟢",
            "good": "🟡", 
            "fair": "🟠",
            "poor": "🔴"
        }
        
        emoji = health_emojis.get(self.report["overall_health"], "❓")
        print(f"\n{emoji} الحالة العامة: {self.report['overall_health']}")
        
        # تفاصيل المكونات
        print(f"\n📊 تفاصيل المكونات:")
        for name, component in self.report["components"].items():
            print(f"\n🔹 {name}:")
            if "strengths" in component:
                for strength in component["strengths"]:
                    print(f"   {strength}")
            if "issues" in component:
                for issue in component["issues"]:
                    print(f"   {issue}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        for rec in self.report["recommendations"]:
            print(f"   {rec}")
        
        # الخطوات التالية
        print(f"\n🚀 الخطوات التالية:")
        for step in self.report["next_steps"]:
            print(f"   {step}")
        
        print("\n" + "="*60)
        print("🏺 انتهى فحص anubis_main_system")
        print("="*60)
    
    def save_report(self):
        """حفظ التقرير في ملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_main_system_inspection_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ التقرير في: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    inspector = AnubisMainSystemInspector()
    
    # تشغيل الفحص
    report = inspector.run_inspection()
    
    # طباعة التقرير
    inspector.print_detailed_report()
    
    # حفظ التقرير
    inspector.save_report()
    
    return report

if __name__ == "__main__":
    main()
