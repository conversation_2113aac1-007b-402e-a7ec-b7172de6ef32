#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مراقب n8n - نظام أنوبيس
Anubis n8n Monitor
"""

import requests
import time
import webbrowser
import sys
from datetime import datetime

def check_n8n_status(url="http://localhost:5678"):
    """فحص حالة n8n"""
    try:
        response = requests.get(f"{url}/healthz", timeout=5)
        if response.status_code == 200:
            return True
        else:
            return False
    except:
        return False

def wait_for_n8n(url="http://localhost:5678", max_wait=120):
    """انتظار تشغيل n8n"""
    print("🏺 مراقب n8n - نظام أنوبيس")
    print("=" * 40)
    print(f"⏳ انتظار تشغيل n8n على {url}")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        if check_n8n_status(url):
            elapsed = int(time.time() - start_time)
            print(f"✅ n8n جاهز! ({elapsed} ثانية)")
            return True
        
        # عرض نقطة تقدم
        elapsed = int(time.time() - start_time)
        print(f"⏳ انتظار... ({elapsed}s)", end="\r")
        time.sleep(2)
    
    print(f"\n❌ انتهت مهلة الانتظار ({max_wait} ثانية)")
    return False

def open_n8n_browser(url="http://localhost:5678"):
    """فتح n8n في المتصفح"""
    try:
        print(f"🌐 فتح n8n في المتصفح: {url}")
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"❌ خطأ في فتح المتصفح: {e}")
        return False

def show_n8n_info():
    """عرض معلومات n8n"""
    print("\n" + "🏺" + "=" * 50)
    print("📊 معلومات n8n - نظام أنوبيس")
    print("=" * 52)
    
    print("🌐 الروابط:")
    print("   📱 الواجهة الرئيسية: http://localhost:5678")
    print("   🔧 API: http://localhost:5678/api")
    print("   📊 الصحة: http://localhost:5678/healthz")
    print("   📈 المقاييس: http://localhost:5678/metrics")
    
    print("\n🛠️  الميزات المتاحة:")
    print("   ✅ إنشاء سير العمل (Workflows)")
    print("   ✅ ربط التطبيقات والخدمات")
    print("   ✅ أتمتة المهام")
    print("   ✅ معالجة البيانات")
    print("   ✅ إدارة المستخدمين")
    
    print("\n📋 أوامر مفيدة:")
    print("   🔄 إعادة تشغيل: Ctrl+C ثم npx n8n")
    print("   📊 عرض السجلات: في نافذة التشغيل")
    print("   🛑 إيقاف: Ctrl+C")
    
    print("\n💡 نصائح:")
    print("   🔐 قم بإعداد كلمة مرور للأمان")
    print("   💾 احفظ سير العمل بانتظام")
    print("   🔄 استخدم Webhooks للتكامل")
    print("   📚 راجع التوثيق: https://docs.n8n.io")

def monitor_n8n():
    """مراقبة n8n المستمرة"""
    url = "http://localhost:5678"
    
    # انتظار تشغيل n8n
    if not wait_for_n8n(url):
        print("❌ لم يتم تشغيل n8n بنجاح")
        print("💡 تأكد من تشغيل: npx n8n")
        return
    
    # فتح المتصفح
    open_n8n_browser(url)
    
    # عرض المعلومات
    show_n8n_info()
    
    # مراقبة مستمرة
    print(f"\n🔍 مراقبة n8n...")
    print("⚠️  اضغط Ctrl+C للخروج من المراقبة")
    
    try:
        last_status = True
        while True:
            current_status = check_n8n_status(url)
            
            if current_status != last_status:
                timestamp = datetime.now().strftime("%H:%M:%S")
                if current_status:
                    print(f"[{timestamp}] ✅ n8n متصل")
                else:
                    print(f"[{timestamp}] ❌ n8n غير متصل")
                last_status = current_status
            
            time.sleep(10)  # فحص كل 10 ثوان
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف المراقبة")

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--check":
            # فحص سريع فقط
            if check_n8n_status():
                print("✅ n8n يعمل")
            else:
                print("❌ n8n لا يعمل")
            return
        elif sys.argv[1] == "--open":
            # فتح المتصفح فقط
            open_n8n_browser()
            return
    
    # مراقبة كاملة
    monitor_n8n()

if __name__ == "__main__":
    main()
