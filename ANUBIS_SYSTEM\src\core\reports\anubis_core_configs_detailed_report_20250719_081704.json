{"scan_info": {"timestamp": "2025-07-19T08:17:03.811412", "scanner_type": "core_configs_detailed", "version": "1.0"}, "core_analysis": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\core", "files_count": 9, "python_files": {"agent_container.py": {"size_kb": 12.97, "lines_count": 354, "imports": ["json", "logging", "multiprocessing", "os", "signal", "sys", "threading", "time", "traceback", "abc.ABC", "abc.abstractmethod", "contextlib.contextmanager", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "typing.<PERSON>", "typing.Union", "subprocess", "tempfile", "uuid", "shutil"], "classes": [{"name": "AgentContainerError", "methods": [], "docstring": "استثناء مخصص لأخطاء الحاوية"}, {"name": "AgentTimeoutError", "methods": [], "docstring": "استثناء انتهاء مهلة الوكيل"}, {"name": "AgentSecurityError", "methods": [], "docstring": "استثناء أمان الوكيل"}, {"name": "ResourceLimits", "methods": ["__init__"], "docstring": "حدو<PERSON> الموارد للوكلاء"}, {"name": "AgentSandbox", "methods": ["__init__", "__enter__", "__exit__", "check_execution_time", "check_file_operation"], "docstring": "صندوق رمل للوكلاء"}, {"name": "IsolatedAgent", "methods": ["__init__", "run_in_container", "_run_with_monitoring"], "docstring": "وكيل معزول في حاوية"}, {"name": "Agent<PERSON><PERSON><PERSON>", "methods": ["__init__", "register_agent", "run_agent", "run_all_agents", "get_agent_status", "stop_agent", "get_container_stats"], "docstring": "حاوية الوكلاء الرئيسية"}], "functions": [{"name": "create_agent_container", "args_count": 1, "docstring": "إنشاء حاوية وكلاء جديدة"}, {"name": "run_analysis", "args_count": 0, "docstring": ""}], "docstring": "🏺 حاوية الوكلاء المخصصة لنظام أنوبيس\nAnubis Agent Container System\n\nنظام عزل وإدارة الوكلاء الذكيين مع حماية وأمان متقدم\nمطور بالتعاون مع Gemini CLI", "complexity": "high"}, "ai_integration.py": {"size_kb": 12.08, "lines_count": 332, "imports": ["json", "os", "subprocess", "abc.ABC", "abc.abstractmethod", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "typing.Union", "requests"], "classes": [{"name": "AIProvider", "methods": ["generate_response", "is_available"], "docstring": "الفئة الأساسية لموفري الذكاء الاصطناعي"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": ["__init__", "generate_response", "is_available"], "docstring": "موفر نماذج <PERSON> المحلية"}, {"name": "GeminiProvider", "methods": ["__init__", "generate_response", "is_available"], "docstring": "موفر Google Gemini"}, {"name": "OpenAIProvider", "methods": ["__init__", "generate_response", "is_available"], "docstring": "موفر OpenAI"}, {"name": "AIIntegrationManager", "methods": ["__init__", "load_config", "save_config", "initialize_providers", "get_available_providers", "set_active_provider", "generate_ai_response", "get_status"], "docstring": "مدير دمج الذكاء الاصطناعي"}], "functions": [], "docstring": "🧠 نظام دمج الذكاء الاصطناعي مع وكلاء أنوبيس\nAnubis AI Integration System\n\nدعم لنماذج الذكاء الاصطناعي المختلفة مع الوكلاء", "complexity": "medium"}, "assistant_system.py": {"size_kb": 15.07, "lines_count": 389, "imports": ["importlib", "importlib.util", "os", "sys", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger", "database_manager.AnubisDatabaseManager", "base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger", "traceback"], "classes": [{"name": "UniversalAssistantSystem", "methods": ["__init__", "_initialize_system", "_validate_project", "_detect_project_type", "_load_available_agents", "_activate_agents", "analyze_project", "_create_analysis_summary", "run_agent", "organize_files", "health_check", "fix_issues", "get_system_status", "shutdown"], "docstring": "النظام الرئيسي للمساعدين الذكيين العالمي"}], "functions": [], "docstring": "🤖 النظام الرئيسي للمساعدين الذكيين العالمي\nMain Universal AI Assistants System", "complexity": "high"}, "base_agent.py": {"size_kb": 12.61, "lines_count": 342, "imports": ["json", "os", "abc.ABC", "abc.abstractmethod", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "ai_integration.ai_manager"], "classes": [{"name": "BaseAgent", "methods": ["__init__", "_create_directories", "_initialize", "get_agent_type", "initialize_agent", "run_analysis", "log_action", "get_status", "run", "save_report", "check_project_structure", "detect_project_type", "get_project_info", "_check_ai_availability", "get_ai_analysis", "get_smart_suggestions", "is_ai_enabled", "get_ai_status"], "docstring": "الفئة الأساسية لجميع الوكلاء الأذكياء"}], "functions": [], "docstring": "🤖 الفئة الأساسية للوكلاء الأذكياء\nBase Agent Class for Universal AI Assistants", "complexity": "medium"}, "config_manager.py": {"size_kb": 13.38, "lines_count": 336, "imports": ["json", "os", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.Optional"], "classes": [{"name": "ConfigManager", "methods": ["__init__", "create_default_config", "load_config", "save_config", "validate_config", "get_agent_config", "is_agent_enabled", "get_workspace_config", "get_database_config", "update_agent_config", "enable_agent", "disable_agent", "get_project_templates", "create_project_templates", "get_config_summary"], "docstring": "مدير التكوين والإعدادات"}], "functions": [], "docstring": "⚙️ مدير التكوين لنظام المساعدين الذكيين العالمي\nConfiguration Manager for Universal AI Assistants", "complexity": "low"}, "enhanced_integrations.py": {"size_kb": 13.7, "lines_count": 363, "imports": ["json", "time", "logging", "abc.ABC", "abc.abstractmethod", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "typing.Union", "requests", "subprocess", "functools.wraps", "langsmith.Client", "langsmith.Client"], "classes": [{"name": "IntegrationError", "methods": [], "docstring": "استثناء مخصص لأخطاء التكامل"}, {"name": "ServiceUnavailableError", "methods": [], "docstring": "استثناء عندما تكون الخدمة غير متاحة"}, {"name": "HealthChecker", "methods": ["__init__", "check_ollama_health", "check_langsmith_health", "check_gemini_health", "run_comprehensive_health_check", "_calculate_overall_health"], "docstring": "فاحص صحة الخدمات الخارجية"}, {"name": "Enhan<PERSON><PERSON><PERSON><PERSON>", "methods": ["__init__", "generate_response", "is_available"], "docstring": "مو<PERSON><PERSON> <PERSON><PERSON><PERSON> محسن مع معالجة أخطاء متقدمة"}, {"name": "EnhancedLangSmithWrapper", "methods": ["__init__", "_initialize_client", "trace_agent_run"], "docstring": "Lang<PERSON>mith wrapper محسن مع معالجة أخطاء متقدمة"}, {"name": "IntegrationsManager", "methods": ["__init__", "_load_config", "_initialize_providers", "run_startup_health_check", "get_available_provider", "trace_operation"], "docstring": "مدير التكاملات الرئيسي"}], "functions": [{"name": "retry_with_backoff", "args_count": 2, "docstring": "مُزخرف لإعادة المحاولة مع تراجع أسي"}, {"name": "decorator", "args_count": 1, "docstring": ""}, {"name": "wrapper", "args_count": 0, "docstring": ""}], "docstring": "🔗 نظام التكامل المحسن للخدمات الخارجية\nEnhanced External Services Integration System\n\nمطور بالتعاون مع Gemini CLI لتحسين موثوقية التكاملات", "complexity": "high"}, "langsmith_wrapper.py": {"size_kb": 3.86, "lines_count": 121, "imports": ["json", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.Optional", "langsmith.Client", "langsmith.utils.tracing_context"], "classes": [{"name": "AnubisLangSmithWrapper", "methods": ["__init__", "load_config", "trace_agent_operation", "log_model_performance", "get_traces_summary"], "docstring": "🔗 Wrapper لتكامل LangSmith مع نظام أنوبيس"}, {"name": "MockTrace", "methods": ["__init__", "__enter__", "__exit__"], "docstring": "محاكاة Trace لـ LangSmith"}], "functions": [], "docstring": "🔗 LangSmith Wrapper لنظام أنوبيس\nLangSmith Integration Wrapper for Anubis System", "complexity": "low"}, "logger.py": {"size_kb": 9.89, "lines_count": 287, "imports": ["logging", "os", "datetime.datetime", "logging.handlers.RotatingFileHandler", "pathlib.Path", "typing.Optional", "traceback"], "classes": [{"name": "SystemLogger", "methods": ["__init__", "_setup_logger", "info", "warning", "error", "debug", "critical", "_format_message", "log_agent_action", "log_system_event", "log_error_with_traceback", "create_session_log", "get_log_stats"], "docstring": "نظام السجلات المتقدم"}, {"name": "<PERSON><PERSON>ogger", "methods": ["__init__", "_setup_session_logger", "log", "log_agent_result", "end_session", "get_session_summary"], "docstring": "سجل جلسة منفصل"}], "functions": [{"name": "log_info", "args_count": 2, "docstring": "تسجيل معلومة"}, {"name": "log_warning", "args_count": 2, "docstring": "تسجيل تحذير"}, {"name": "log_error", "args_count": 3, "docstring": "تس<PERSON>ي<PERSON> خطأ"}, {"name": "log_agent_action", "args_count": 3, "docstring": "تسجيل عمل وكيل"}], "docstring": "📝 نظام السجلات لنظام المساعدين الذكيين العالمي\nLogging System for Universal AI Assistants", "complexity": "medium"}}, "imports_analysis": {}, "classes_analysis": {}, "functions_analysis": {}, "complexity_score": 200}, "configs_analysis": {"path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\anubis\\configs", "files_count": 9, "json_files": {"ai_config.json": {"size_kb": 2.85, "valid_json": true, "keys_count": 5, "nested_levels": 3, "structure": {"default_provider": "str", "providers": {"ollama": {"enabled": "bool", "model": "str", "host": "str", "port": "int", "description": "str"}, "gemini": {"enabled": "bool", "model": "str", "api_key": "str", "description": "str"}, "openai": {"enabled": "bool", "model": "str", "api_key": "str", "description": "str"}}, "agent_prompts": {"error_detector": {"system_prompt": "str", "analysis_prompt": "str"}, "project_analyzer": {"system_prompt": "str", "analysis_prompt": "str"}, "file_organizer": {"system_prompt": "str", "organization_prompt": "str"}, "database_agent": {"system_prompt": "str", "query_prompt": "str"}, "memory_agent": {"system_prompt": "str", "memory_prompt": "str"}}, "response_settings": {"max_tokens": "int", "temperature": "float", "timeout": "int", "language": "str", "format": "str"}, "features": {"code_analysis": "bool", "project_insights": "bool", "auto_suggestions": "bool", "smart_organization": "bool", "intelligent_search": "bool"}}, "content_summary": {"total_keys": 47, "data_types": {"str": 22, "dict": 12, "bool": 9, "int": 3, "float": 1}, "key_patterns": ["default_provider", "providers", "providers.ollama", "providers.ollama.enabled", "providers.ollama.model", "providers.ollama.host", "providers.ollama.port", "providers.ollama.description", "providers.gemini", "providers.gemini.enabled"]}}, "database_config.json": {"size_kb": 1.45, "valid_json": true, "keys_count": 5, "nested_levels": 3, "structure": {"database": {"type": "str", "description": "str", "sqlite": {"db_path": "str", "description": "str", "auto_backup": "bool", "backup_interval": "str"}, "mysql": {"host": "str", "port": "int", "user": "str", "password": "str", "database": "str"}}, "system": {"name": "str", "version": "str", "description": "str", "database_features": {"project_tracking": "bool", "analysis_history": "bool", "error_logging": "bool", "performance_metrics": "bool", "user_activities": "bool"}}, "performance": {"connection_timeout": "int", "query_timeout": "int", "max_retries": "int", "batch_size": "int", "cache_enabled": "bool"}, "security": {"encrypt_sensitive_data": "bool", "log_queries": "bool", "sanitize_inputs": "bool", "max_connections": "int"}, "maintenance": {"auto_vacuum": "bool", "optimize_tables": "bool", "cleanup_old_data": "bool", "retention_days": "int", "backup_retention_days": "int"}}, "content_summary": {"total_keys": 47, "data_types": {"dict": 8, "str": 14, "bool": 15, "int": 10}, "key_patterns": ["database", "database.type", "database.description", "database.sqlite", "database.sqlite.db_path", "database.sqlite.description", "database.sqlite.auto_backup", "database.sqlite.backup_interval", "database.mysql", "database.mysql.host"]}}, "default_config.json": {"size_kb": 4.07, "valid_json": true, "keys_count": 7, "nested_levels": 6, "structure": {"system": {"name": "str", "version": "str", "language": "str", "encoding": "str", "created_date": "str"}, "project": {"auto_detect_type": "bool", "supported_types": "array[5]", "default_type": "str"}, "agents": {"database_agent": {"enabled": "bool", "database": {"type": "str", "name": "str", "host": "str", "user": "str", "password": "str"}, "auto_backup": "bool", "check_performance": "bool", "check_security": "bool"}, "file_organizer_agent": {"enabled": "bool", "auto_organize": "bool", "backup_before_organize": "bool", "dry_run": "bool", "organization_rules": {"core_files": {"target_dir": "str", "patterns": "array[5]", "description": "str"}, "source_code": {"target_dir": "str", "patterns": "array[1]", "exclude": "array[5]", "description": "str"}, "tests": {"target_dir": "str", "patterns": "array[3]", "extensions": "array[1]", "description": "str"}, "documentation": {"target_dir": "str", "patterns": "array[3]", "exclude": "array[1]", "description": "str"}, "configuration": {"target_dir": "str", "patterns": "array[6]", "description": "str"}}}, "memory_agent": {"enabled": "bool", "auto_save": "bool", "memory_retention_days": "int", "max_memory_size_mb": "int"}, "error_detector_agent": {"enabled": "bool", "check_syntax": "bool", "check_imports": "bool", "check_style": "bool", "auto_fix": "bool"}, "project_analyzer_agent": {"enabled": "bool", "deep_analysis": "bool", "performance_check": "bool", "security_scan": "bool"}}, "workspace": {"base_dir": "str", "subdirs": {"logs": "str", "reports": "str", "backups": "str", "memory": "str", "collaboration": "str"}, "auto_cleanup": "bool", "max_log_size_mb": "int"}, "database": {"default_type": "str", "connection_timeout": "int", "backup_frequency": "str", "security_checks": "bool"}}, "content_summary": {"total_keys": 114, "data_types": {"dict": 25, "str": 42, "bool": 24, "list": 16, "int": 7}, "key_patterns": ["system", "system.name", "system.version", "system.language", "system.encoding", "system.created_date", "project", "project.auto_detect_type", "project.supported_types", "project.default_type"]}}, "langsmith_config.json": {"size_kb": 1.5, "valid_json": true, "keys_count": 3, "nested_levels": 3, "structure": {"langsmith": {"enabled": "bool", "project_name": "str", "tracing_enabled": "bool", "api_url": "str", "environment": "str"}, "agents_integration": {"enhanced_error_detector": {"trace_enabled": "bool", "performance_monitoring": "bool"}, "enhanced_project_analyzer": {"trace_enabled": "bool", "performance_monitoring": "bool"}, "enhanced_file_organizer": {"trace_enabled": "bool", "performance_monitoring": "bool"}, "enhanced_memory_agent": {"trace_enabled": "bool", "performance_monitoring": "bool"}, "smart_ai_agent": {"trace_enabled": "bool", "performance_monitoring": "bool"}}, "models_integration": {"ollama_models": "array[3]", "model_selection": {"auto_select": "bool", "performance_based": "bool", "fallback_model": "str"}, "performance_tracking": {"response_time": "bool", "quality_metrics": "bool", "usage_statistics": "bool"}}}, "content_summary": {"total_keys": 40, "data_types": {"dict": 12, "bool": 19, "str": 7, "list": 2}, "key_patterns": ["langsmith", "langsmith.enabled", "langsmith.project_name", "langsmith.tracing_enabled", "langsmith.api_url", "langsmith.environment", "langsmith.tags", "langsmith.metadata", "langsmith.metadata.system_version", "langsmith.metadata.created_at"]}}, "memory.json": {"size_kb": 0.22, "valid_json": true, "keys_count": 1, "nested_levels": 3, "structure": {"test_project": {"data": {"project_name": "str", "version": "str", "agents_count": "int"}, "timestamp": "str", "category": "str"}}, "content_summary": {"total_keys": 7, "data_types": {"dict": 2, "str": 4, "int": 1}, "key_patterns": ["test_project", "test_project.data", "test_project.data.project_name", "test_project.data.version", "test_project.data.agents_count", "test_project.timestamp", "test_project.category"]}}, "ollama_check_report_20250716_092556.json": {"size_kb": 1.8, "valid_json": true, "keys_count": 6, "nested_levels": 4, "structure": {"timestamp": "str", "ollama_service": {"status": "str", "accessible": "bool"}, "installed_models": {"count": "int", "models": "array[6]"}, "running_models": {"count": "int", "models": "array[0]"}, "test_results": "array[3]"}, "content_summary": {"total_keys": 15, "data_types": {"str": 2, "dict": 4, "bool": 1, "int": 5, "list": 3}, "key_patterns": ["timestamp", "ollama_service", "ollama_service.status", "ollama_service.accessible", "installed_models", "installed_models.count", "installed_models.models", "running_models", "running_models.count", "running_models.models"]}}, "openapi.json": {"size_kb": 530.76, "valid_json": true, "keys_count": 5, "nested_levels": 14, "structure": {"openapi": "str", "info": {"title": "str", "version": "str"}, "paths": {"/api/v1/sessions/{session_id}/dashboard": {"post": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}}, "/api/v1/sessions/{session_id}": {"get": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}, "patch": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}, "delete": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}}, "/api/v1/sessions": {"get": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}, "post": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}, "delete": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}}, "/api/v1/sessions/{session_id}/metadata": {"get": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}}, "/api/v1/sessions/{session_id}/views": {"get": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}, "post": {"tags": "array[1]", "summary": "str", "description": "str", "operationId": "str", "security": "array[3]"}}}, "components": {"schemas": {"AIMessage": {"properties": {"content": {"anyOf": "array[2]", "title": "str"}, "additional_kwargs": {"type": "str", "title": "str"}, "response_metadata": {"type": "str", "title": "str"}, "type": {"type": "str", "const": "str", "title": "str", "default": "str"}, "name": {"anyOf": "array[2]", "title": "str"}}, "additionalProperties": "bool", "type": "str", "required": "array[1]", "title": "str"}, "AIMessageChunk": {"properties": {"content": {"anyOf": "array[2]", "title": "str"}, "additional_kwargs": {"type": "str", "title": "str"}, "response_metadata": {"type": "str", "title": "str"}, "type": {"type": "str", "const": "str", "title": "str", "default": "str"}, "name": {"anyOf": "array[2]", "title": "str"}}, "additionalProperties": "bool", "type": "str", "required": "array[1]", "title": "str"}, "APIFeedbackSource": {"properties": {"type": {"type": "str", "title": "str", "default": "str"}, "metadata": {"anyOf": "array[2]", "title": "str"}}, "type": "str", "title": "str", "description": "str"}, "APIKeyCreateRequest": {"properties": {"description": {"type": "str", "title": "str", "default": "str"}, "read_only": {"type": "str", "title": "str", "default": "bool"}, "expires_at": {"anyOf": "array[2]", "title": "str"}}, "type": "str", "title": "str", "description": "str"}, "APIKeyCreateResponse": {"properties": {"created_at": {"anyOf": "array[2]", "title": "str"}, "id": {"type": "str", "format": "str", "title": "str"}, "short_key": {"type": "str", "title": "str"}, "description": {"type": "str", "title": "str"}, "read_only": {"type": "str", "title": "str", "default": "bool"}}, "type": "str", "required": "array[4]", "title": "str", "description": "str"}}, "securitySchemes": {"API Key": {"type": "str", "in": "str", "name": "str"}, "Tenant ID": {"type": "str", "in": "str", "name": "str"}, "Bearer Auth": {"type": "str", "description": "str", "scheme": "str"}, "Organization ID": {"type": "str", "in": "str", "name": "str"}}}, "definitions": {"alerts.AlertAction": {"type": "str", "required": "array[2]", "properties": {"alert_rule_id": {"type": "str"}, "config": {"type": "str"}, "created_at": {"type": "str"}, "id": {"type": "str"}, "target": {"type": "str", "enum": "array[2]"}}}, "alerts.AlertActionBase": {"type": "str", "required": "array[2]", "properties": {"alert_rule_id": {"type": "str"}, "config": {"type": "str"}, "id": {"type": "str"}, "target": {"type": "str", "enum": "array[2]"}}}, "alerts.AlertRule": {"type": "str", "required": "array[7]", "properties": {"aggregation": {"type": "str", "enum": "array[3]"}, "attribute": {"type": "str", "enum": "array[5]"}, "created_at": {"type": "str"}, "denominator_filter": {"type": "str"}, "description": {"type": "str"}}}, "alerts.AlertRuleBase": {"type": "str", "required": "array[7]", "properties": {"aggregation": {"type": "str", "enum": "array[3]"}, "attribute": {"type": "str", "enum": "array[5]"}, "denominator_filter": {"type": "str"}, "description": {"type": "str"}, "filter": {"type": "str"}}}, "alerts.AlertRuleResponse": {"type": "str", "properties": {"actions": {"type": "str", "items": {"$ref": "str"}}, "rule": {"$ref": "str"}}}}}, "content_summary": {"total_keys": 17804, "data_types": {"str": 7285, "dict": 7733, "list": 2336, "bool": 286, "NoneType": 1, "int": 138, "float": 25}, "key_patterns": ["openapi", "info", "info.title", "info.version", "paths", "paths./api/v1/sessions/{session_id}/dashboard", "paths./api/v1/sessions/{session_id}/dashboard.post", "paths./api/v1/sessions/{session_id}/dashboard.post.tags", "paths./api/v1/sessions/{session_id}/dashboard.post.summary", "paths./api/v1/sessions/{session_id}/dashboard.post.description"]}}, "project_structure.json": {"size_kb": 5.97, "valid_json": true, "keys_count": 10, "nested_levels": 6, "structure": {"project_name": "str", "version": "str", "root_path": "str", "main_directories": {"anubis": {"path": "str", "description": "str", "subdirectories": {"agents": {"path": "str", "description": "str", "files": "array[2]"}, "api": {"path": "str", "description": "str", "files": "array[3]"}, "core": {"path": "str", "description": "str", "files": "array[8]"}, "database": {"path": "str", "description": "str", "subdirectories": {"core": "str", "docs": "str", "setup": "str", "tests": "str"}, "files": "array[4]"}, "configs": {"path": "str", "description": "str", "files": "array[8]"}}, "main_files": "array[5]"}, "tools": {"path": "str", "description": "str", "subdirectories": {"vscode-optimizer": "str", "emergency": "str"}}, "n8n": {"path": "str", "description": "str", "subdirectories": {"credentials": "str", "nodes": "str", "workflows": "str"}}}, "entry_points": {"main_system": "str", "api_server": "str", "quick_client": "str", "tests": "str"}}, "content_summary": {"total_keys": 100, "data_types": {"str": 55, "dict": 24, "list": 15, "bool": 6}, "key_patterns": ["project_name", "version", "root_path", "main_directories", "main_directories.anubis", "main_directories.anubis.path", "main_directories.anubis.description", "main_directories.anubis.subdirectories", "main_directories.anubis.subdirectories.agents", "main_directories.anubis.subdirectories.agents.path"]}}, "system_paths.json": {"size_kb": 0.78, "valid_json": true, "keys_count": 5, "nested_levels": 2, "structure": {"timestamp": "str", "base_path": "str", "project_name": "str", "paths": {"base": "str", "core": "str", "agents": "str", "configs": "str", "tests": "str"}, "active_processes_count": "int"}, "content_summary": {"total_keys": 14, "data_types": {"str": 12, "dict": 1, "int": 1}, "key_patterns": ["timestamp", "base_path", "project_name", "paths", "paths.base", "paths.core", "paths.agents", "paths.configs", "paths.tests", "paths.logs"]}}}, "config_types": {"ai_config.json": "ai_config", "database_config.json": "database_config", "default_config.json": "default_config", "langsmith_config.json": "integration_config", "memory.json": "general_config", "ollama_check_report_20250716_092556.json": "general_config", "openapi.json": "general_config", "project_structure.json": "project_config", "system_paths.json": "system_config"}, "sensitive_data": [{"file": "ai_config.json", "items": ["providers.gemini.api_key", "providers.openai.api_key", "response_settings.max_tokens"]}, {"file": "database_config.json", "items": ["database.mysql.password"]}, {"file": "default_config.json", "items": ["agents.database_agent.database.password"]}, {"file": "openapi.json", "items": ["components.schemas.APIKeyCreateRequest", "components.schemas.APIKeyCreateResponse", "components.schemas.APIKeyCreateResponse.properties.short_key", "components.securitySchemes.API Key"]}, {"file": "project_structure.json", "items": ["main_directories.n8n.subdirectories.credentials"]}], "validation_status": {}}, "dependencies_map": {"core_internal_deps": {"agent_container.py": [], "ai_integration.py": [], "assistant_system.py": ["base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger", "base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger"], "base_agent.py": ["ai_integration.ai_manager"], "config_manager.py": [], "enhanced_integrations.py": [], "langsmith_wrapper.py": [], "logger.py": []}, "core_external_deps": {"agent_container.py": ["json", "logging", "multiprocessing", "os", "signal", "sys", "threading", "time", "traceback", "abc.ABC"], "ai_integration.py": ["json", "os", "subprocess", "abc.ABC", "abc.abstractmethod", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional"], "assistant_system.py": ["importlib", "importlib.util", "os", "sys", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional"], "base_agent.py": ["json", "os", "abc.ABC", "abc.abstractmethod", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List", "typing.Optional"], "config_manager.py": ["json", "os", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.Optional"], "enhanced_integrations.py": ["json", "time", "logging", "abc.ABC", "abc.abstractmethod", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.List"], "langsmith_wrapper.py": ["json", "datetime.datetime", "pathlib.Path", "typing.Any", "typing.Dict", "typing.Optional", "langsmith.Client", "langsmith.utils.tracing_context"], "logger.py": ["logging", "os", "datetime.datetime", "logging.handlers.RotatingFileHandler", "pathlib.Path", "typing.Optional", "traceback"]}, "configs_usage": {"agent_container.py": [], "ai_integration.py": [], "assistant_system.py": [], "base_agent.py": [], "config_manager.py": ["config_manager"], "enhanced_integrations.py": [], "langsmith_wrapper.py": [], "logger.py": []}, "circular_deps": []}, "isolation_recommendations": {"core_isolation": {"strategy": "microservices", "components": [{"file": "agent_container.py", "isolation_type": "container", "resource_requirements": {"memory": "512M", "cpu": "0.5"}, "dependencies": []}, {"file": "ai_integration.py", "isolation_type": "module", "resource_requirements": {"memory": "256M", "cpu": "0.2"}, "dependencies": []}, {"file": "assistant_system.py", "isolation_type": "container", "resource_requirements": {"memory": "512M", "cpu": "0.5"}, "dependencies": ["base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger", "base_agent.BaseAgent", "config_manager.ConfigManager", "logger.SessionLogger", "logger.SystemLogger"]}, {"file": "base_agent.py", "isolation_type": "module", "resource_requirements": {"memory": "256M", "cpu": "0.2"}, "dependencies": ["ai_integration.ai_manager"]}, {"file": "config_manager.py", "isolation_type": "module", "resource_requirements": {"memory": "256M", "cpu": "0.2"}, "dependencies": []}, {"file": "enhanced_integrations.py", "isolation_type": "container", "resource_requirements": {"memory": "512M", "cpu": "0.5"}, "dependencies": []}, {"file": "langsmith_wrapper.py", "isolation_type": "module", "resource_requirements": {"memory": "256M", "cpu": "0.2"}, "dependencies": []}, {"file": "logger.py", "isolation_type": "module", "resource_requirements": {"memory": "256M", "cpu": "0.2"}, "dependencies": []}], "priority": "critical"}, "configs_isolation": {"strategy": "shared_volume", "security_level": "high", "encryption": true}, "deployment_plan": {"phase1": ["configs_setup", "base_infrastructure"], "phase2": ["agent_container.py", "assistant_system.py", "enhanced_integrations.py"], "phase3": ["ai_integration.py", "base_agent.py", "config_manager.py", "langsmith_wrapper.py", "logger.py"]}}}