#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 نظام النسخ الاحتياطية الآمنة
Secure Backup System

نظام متقدم للنسخ الاحتياطية المشفرة مع فريق حورس
Advanced encrypted backup system with Horus team
"""

import os
import json
import shutil
import zipfile
import hashlib
import secrets
from datetime import datetime, timedelta
from pathlib import Path
from cryptography.fernet import Fernet
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('secure_backup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HorusSecureBackupSystem:
    """💾 نظام النسخ الاحتياطية الآمنة مع فريق حورس"""
    
    def __init__(self):
        """تهيئة نظام النسخ الاحتياطية"""
        self.vault_dir = Path(__file__).parent / "vault"
        self.backup_dir = self.vault_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        self.local_backup_dir = self.backup_dir / "local"
        self.local_backup_dir.mkdir(exist_ok=True)
        
        self.cloud_backup_dir = self.backup_dir / "cloud_ready"
        self.cloud_backup_dir.mkdir(exist_ok=True)
        
        self.backup_encryption_key = None
        self.backup_schedule = {
            "daily": True,
            "weekly": True,
            "monthly": True,
            "retention_days": 90
        }
        
        logger.info("💾 تم تهيئة نظام النسخ الاحتياطية الآمنة")
    
    def generate_backup_key(self) -> str:
        """إنشاء مفتاح تشفير النسخ الاحتياطية"""
        backup_key = Fernet.generate_key()
        self.backup_encryption_key = Fernet(backup_key)
        
        # حفظ مفتاح النسخ الاحتياطية بشكل آمن
        key_file = self.backup_dir / "backup_encryption.key"
        with open(key_file, 'wb') as f:
            f.write(backup_key)
        
        os.chmod(key_file, 0o600)
        
        logger.info("🔑 تم إنشاء مفتاح تشفير النسخ الاحتياطية")
        return backup_key.decode()
    
    def load_backup_key(self) -> bool:
        """تحميل مفتاح تشفير النسخ الاحتياطية"""
        try:
            key_file = self.backup_dir / "backup_encryption.key"
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    backup_key = f.read()
                self.backup_encryption_key = Fernet(backup_key)
                logger.info("✅ تم تحميل مفتاح تشفير النسخ الاحتياطية")
                return True
            else:
                self.generate_backup_key()
                return True
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل مفتاح النسخ الاحتياطية: {e}")
            return False
    
    def create_backup_manifest(self, backup_files: list) -> dict:
        """إنشاء فهرس النسخة الاحتياطية"""
        manifest = {
            "backup_id": secrets.token_hex(16),
            "created_at": datetime.now().isoformat(),
            "backup_type": "full",
            "encryption": "AES-256",
            "files": [],
            "total_size": 0,
            "checksum": ""
        }
        
        total_size = 0
        checksums = []
        
        for file_path in backup_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                total_size += file_size
                
                # حساب checksum للملف
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                
                checksums.append(file_hash)
                
                manifest["files"].append({
                    "path": str(file_path),
                    "size": file_size,
                    "checksum": file_hash,
                    "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                })
        
        manifest["total_size"] = total_size
        manifest["checksum"] = hashlib.sha256(''.join(checksums).encode()).hexdigest()
        
        return manifest
    
    def create_encrypted_backup(self, source_dir: str, backup_name: str = None) -> dict:
        """إنشاء نسخة احتياطية مشفرة"""
        if not self.backup_encryption_key:
            self.load_backup_key()
        
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_info = {
            "backup_name": backup_name,
            "started_at": datetime.now().isoformat(),
            "status": "in_progress",
            "source_dir": source_dir,
            "files_backed_up": 0,
            "total_size": 0
        }
        
        try:
            # جمع جميع الملفات للنسخ الاحتياطي
            source_path = Path(source_dir)
            backup_files = []
            
            if source_path.is_file():
                backup_files = [str(source_path)]
            else:
                backup_files = [str(f) for f in source_path.rglob("*") if f.is_file()]
            
            # إنشاء فهرس النسخة الاحتياطية
            manifest = self.create_backup_manifest(backup_files)
            backup_info["backup_id"] = manifest["backup_id"]
            
            # إنشاء أرشيف مضغوط
            zip_file = self.local_backup_dir / f"{backup_name}.zip"
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in backup_files:
                    arcname = os.path.relpath(file_path, source_path.parent)
                    zipf.write(file_path, arcname)
                    backup_info["files_backed_up"] += 1
                
                # إضافة الفهرس
                manifest_json = json.dumps(manifest, indent=2, ensure_ascii=False)
                zipf.writestr("backup_manifest.json", manifest_json)
            
            backup_info["total_size"] = os.path.getsize(zip_file)
            
            # تشفير الأرشيف
            with open(zip_file, 'rb') as f:
                zip_data = f.read()
            
            encrypted_data = self.backup_encryption_key.encrypt(zip_data)
            
            # حفظ النسخة المشفرة
            encrypted_file = self.local_backup_dir / f"{backup_name}.encrypted"
            with open(encrypted_file, 'wb') as f:
                f.write(encrypted_data)
            
            # حذف الملف غير المشفر
            os.remove(zip_file)
            
            backup_info.update({
                "completed_at": datetime.now().isoformat(),
                "status": "completed",
                "encrypted_file": str(encrypted_file),
                "manifest": manifest
            })
            
            logger.info(f"💾 تم إنشاء نسخة احتياطية مشفرة: {backup_name}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            backup_info.update({
                "completed_at": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            })
        
        return backup_info
    
    def restore_from_backup(self, backup_file: str, restore_dir: str) -> dict:
        """استعادة من النسخة الاحتياطية"""
        if not self.backup_encryption_key:
            self.load_backup_key()
        
        restore_info = {
            "started_at": datetime.now().isoformat(),
            "backup_file": backup_file,
            "restore_dir": restore_dir,
            "status": "in_progress"
        }
        
        try:
            # فك تشفير النسخة الاحتياطية
            with open(backup_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.backup_encryption_key.decrypt(encrypted_data)
            
            # إنشاء ملف مؤقت للأرشيف
            temp_zip = self.backup_dir / "temp_restore.zip"
            with open(temp_zip, 'wb') as f:
                f.write(decrypted_data)
            
            # استخراج الملفات
            restore_path = Path(restore_dir)
            restore_path.mkdir(parents=True, exist_ok=True)
            
            with zipfile.ZipFile(temp_zip, 'r') as zipf:
                zipf.extractall(restore_path)
                
                # قراءة الفهرس
                if "backup_manifest.json" in zipf.namelist():
                    manifest_data = zipf.read("backup_manifest.json")
                    manifest = json.loads(manifest_data.decode())
                    restore_info["manifest"] = manifest
            
            # حذف الملف المؤقت
            os.remove(temp_zip)
            
            restore_info.update({
                "completed_at": datetime.now().isoformat(),
                "status": "completed"
            })
            
            logger.info(f"✅ تم استعادة النسخة الاحتياطية إلى: {restore_dir}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            restore_info.update({
                "completed_at": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            })
        
        return restore_info
    
    def cleanup_old_backups(self) -> dict:
        """تنظيف النسخ الاحتياطية القديمة"""
        cleanup_info = {
            "started_at": datetime.now().isoformat(),
            "deleted_backups": [],
            "total_deleted": 0,
            "space_freed": 0
        }
        
        try:
            retention_date = datetime.now() - timedelta(days=self.backup_schedule["retention_days"])
            
            for backup_file in self.local_backup_dir.glob("*.encrypted"):
                file_date = datetime.fromtimestamp(backup_file.stat().st_mtime)
                
                if file_date < retention_date:
                    file_size = backup_file.stat().st_size
                    backup_file.unlink()
                    
                    cleanup_info["deleted_backups"].append({
                        "file": str(backup_file),
                        "date": file_date.isoformat(),
                        "size": file_size
                    })
                    cleanup_info["total_deleted"] += 1
                    cleanup_info["space_freed"] += file_size
            
            cleanup_info["completed_at"] = datetime.now().isoformat()
            
            logger.info(f"🧹 تم حذف {cleanup_info['total_deleted']} نسخة احتياطية قديمة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف النسخ الاحتياطية: {e}")
            cleanup_info["error"] = str(e)
        
        return cleanup_info
    
    def setup_backup_scheduler(self) -> str:
        """إعداد جدولة النسخ الاحتياطية"""
        scheduler_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📅 جدولة النسخ الاحتياطية التلقائية
Automatic Backup Scheduler
"""

import schedule
import time
import json
from datetime import datetime
from pathlib import Path

def daily_backup():
    """نسخة احتياطية يومية"""
    print(f"📅 نسخة احتياطية يومية - {datetime.now()}")
    
    # هنا يمكن استدعاء نظام النسخ الاحتياطية
    backup_log = {
        "timestamp": datetime.now().isoformat(),
        "type": "daily",
        "status": "completed"
    }
    
    log_file = Path(__file__).parent / "backup_schedule.log"
    with open(log_file, 'a') as f:
        f.write(json.dumps(backup_log) + "\\n")

def weekly_backup():
    """نسخة احتياطية أسبوعية"""
    print(f"📅 نسخة احتياطية أسبوعية - {datetime.now()}")
    
    backup_log = {
        "timestamp": datetime.now().isoformat(),
        "type": "weekly",
        "status": "completed"
    }
    
    log_file = Path(__file__).parent / "backup_schedule.log"
    with open(log_file, 'a') as f:
        f.write(json.dumps(backup_log) + "\\n")

def monthly_cleanup():
    """تنظيف شهري للنسخ القديمة"""
    print(f"🧹 تنظيف شهري - {datetime.now()}")
    
    cleanup_log = {
        "timestamp": datetime.now().isoformat(),
        "type": "monthly_cleanup",
        "status": "completed"
    }
    
    log_file = Path(__file__).parent / "backup_schedule.log"
    with open(log_file, 'a') as f:
        f.write(json.dumps(cleanup_log) + "\\n")

# جدولة المهام
schedule.every().day.at("02:00").do(daily_backup)
schedule.every().sunday.at("03:00").do(weekly_backup)
schedule.every().month.do(monthly_cleanup)

print("📅 تم تشغيل جدولة النسخ الاحتياطية")
print("📅 نسخة يومية: 02:00 صباحاً")
print("📅 نسخة أسبوعية: الأحد 03:00 صباحاً")
print("🧹 تنظيف شهري: أول كل شهر")

# تشغيل الجدولة
while True:
    schedule.run_pending()
    time.sleep(3600)  # فحص كل ساعة
'''
        
        scheduler_file = self.backup_dir / "backup_scheduler.py"
        with open(scheduler_file, 'w', encoding='utf-8') as f:
            f.write(scheduler_script)
        
        os.chmod(scheduler_file, 0o755)
        
        logger.info(f"📅 تم إعداد جدولة النسخ الاحتياطية: {scheduler_file}")
        return str(scheduler_file)
    
    def start_backup_system(self) -> dict:
        """بدء نظام النسخ الاحتياطية"""
        logger.info("🚀 بدء نظام النسخ الاحتياطية الآمنة")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "actions_completed": [],
            "files_created": [],
            "backups_created": []
        }
        
        try:
            # تحميل مفتاح التشفير
            if self.load_backup_key():
                results["actions_completed"].append("🔑 تم تحميل مفتاح تشفير النسخ الاحتياطية")
            
            # إنشاء نسخة احتياطية للمفاتيح الآمنة
            secure_dir = self.vault_dir / "secure"
            if secure_dir.exists():
                backup_info = self.create_encrypted_backup(str(secure_dir), "keys_backup")
                if backup_info["status"] == "completed":
                    results["backups_created"].append(backup_info)
                    results["actions_completed"].append("💾 تم إنشاء نسخة احتياطية للمفاتيح الآمنة")
            
            # إنشاء نسخة احتياطية لملفات التدوير
            rotation_dir = self.vault_dir / "rotation"
            if rotation_dir.exists():
                backup_info = self.create_encrypted_backup(str(rotation_dir), "rotation_backup")
                if backup_info["status"] == "completed":
                    results["backups_created"].append(backup_info)
                    results["actions_completed"].append("💾 تم إنشاء نسخة احتياطية لملفات التدوير")
            
            # تنظيف النسخ القديمة
            cleanup_info = self.cleanup_old_backups()
            if cleanup_info.get("total_deleted", 0) > 0:
                results["actions_completed"].append(f"🧹 تم حذف {cleanup_info['total_deleted']} نسخة قديمة")
            
            # إعداد الجدولة
            scheduler_file = self.setup_backup_scheduler()
            results["files_created"].append(scheduler_file)
            results["actions_completed"].append("📅 تم إعداد جدولة النسخ الاحتياطية")
            
            # حفظ تقرير النسخ الاحتياطية
            report = {
                "backup_system_status": results,
                "backup_schedule": self.backup_schedule,
                "total_backups": len(results["backups_created"])
            }
            
            report_file = self.backup_dir / f"backup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            results["files_created"].append(str(report_file))
            results["actions_completed"].append("📊 تم إنشاء تقرير النسخ الاحتياطية")
            
            logger.info("✅ تم بدء نظام النسخ الاحتياطية بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء نظام النسخ الاحتياطية: {e}")
            results["status"] = "error"
            results["error"] = str(e)
        
        return results

def main():
    """الدالة الرئيسية"""
    print("💾 نظام النسخ الاحتياطية الآمنة")
    print("=" * 50)
    
    backup_system = HorusSecureBackupSystem()
    results = backup_system.start_backup_system()
    
    print("\n✅ تم بدء نظام النسخ الاحتياطية!")
    print("\n✅ الإجراءات المكتملة:")
    for action in results.get("actions_completed", []):
        print(f"   {action}")
    
    print("\n📁 الملفات المنشأة:")
    for file_path in results.get("files_created", []):
        print(f"   📄 {file_path}")
    
    print(f"\n💾 النسخ الاحتياطية المنشأة: {len(results.get('backups_created', []))}")

if __name__ == "__main__":
    main()
