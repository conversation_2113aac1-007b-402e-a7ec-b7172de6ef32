#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير سير عمل فريق الذكاء الاصطناعي - أنوبيس
Anubis AI Team Workflow Manager
"""

import json
import subprocess
import time
from datetime import datetime
from pathlib import Path

class AnubisTeamWorkflowManager:
    """مدير سير عمل فريق الذكاء الاصطناعي"""
    
    def __init__(self):
        self.team_config = self.load_team_config()
        self.workflow_history = []
        
    def load_team_config(self):
        """تحميل إعدادات الفريق"""
        config_file = Path("anubis_ai_team_collaboration_plan.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def create_task_workflow(self, task_type, task_description, priority="medium"):
        """إنشاء سير عمل للمهمة"""
        
        # تحديد النماذج المناسبة حسب نوع المهمة
        workflow_templates = {
            "analysis": {
                "primary": "phi3:mini",
                "support": ["mistral:7b"],
                "coordinator": "gemini_cli",
                "phases": ["quick_analysis", "detailed_review", "recommendations"]
            },
            "development": {
                "primary": "mistral:7b", 
                "support": ["phi3:mini", "strikegpt-r1-zero-8b"],
                "coordinator": "gemini_cli",
                "phases": ["code_analysis", "development", "testing", "optimization"]
            },
            "planning": {
                "primary": "llama3:8b",
                "support": ["gemini_cli"],
                "coordinator": "gemini_cli", 
                "phases": ["strategic_analysis", "planning", "resource_allocation", "timeline"]
            },
            "innovation": {
                "primary": "strikegpt-r1-zero-8b",
                "support": ["llama3:8b", "Qwen2.5-VL-7B"],
                "coordinator": "gemini_cli",
                "phases": ["creative_thinking", "innovation", "feasibility", "implementation"]
            },
            "review": {
                "primary": "gemini_cli",
                "support": ["llama3:8b", "mistral:7b"],
                "coordinator": "gemini_cli",
                "phases": ["comprehensive_review", "quality_assurance", "final_approval"]
            }
        }
        
        template = workflow_templates.get(task_type, workflow_templates["analysis"])
        
        workflow = {
            "task_id": f"anubis_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "task_type": task_type,
            "task_description": task_description,
            "priority": priority,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "workflow": template,
            "execution_log": [],
            "results": {}
        }
        
        return workflow
    
    def execute_ollama_phase(self, workflow, model_name, phase_description):
        """تنفيذ مرحلة مع نموذج Ollama"""
        
        print(f"🤖 تنفيذ مرحلة مع {model_name}: {phase_description}")
        
        # إنشاء prompt مخصص
        prompt = self.create_phase_prompt(workflow, model_name, phase_description)
        
        try:
            # تشغيل النموذج
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'run', model_name, prompt
            ], capture_output=True, text=True, timeout=120)
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                phase_result = {
                    "model": model_name,
                    "phase": phase_description,
                    "success": True,
                    "response": response,
                    "execution_time": round(execution_time, 2),
                    "timestamp": datetime.now().isoformat()
                }
                
                print(f"✅ {model_name} أكمل المرحلة في {execution_time:.1f} ثانية")
                
            else:
                phase_result = {
                    "model": model_name,
                    "phase": phase_description,
                    "success": False,
                    "error": result.stderr,
                    "execution_time": round(execution_time, 2),
                    "timestamp": datetime.now().isoformat()
                }
                
                print(f"❌ {model_name} فشل في المرحلة: {result.stderr}")
            
            # إضافة النتيجة لسجل التنفيذ
            workflow["execution_log"].append(phase_result)
            
            return phase_result
            
        except subprocess.TimeoutExpired:
            print(f"⏰ انتهت مهلة {model_name}")
            phase_result = {
                "model": model_name,
                "phase": phase_description,
                "success": False,
                "error": "timeout",
                "timestamp": datetime.now().isoformat()
            }
            workflow["execution_log"].append(phase_result)
            return phase_result
            
        except Exception as e:
            print(f"❌ خطأ في {model_name}: {e}")
            phase_result = {
                "model": model_name,
                "phase": phase_description,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            workflow["execution_log"].append(phase_result)
            return phase_result
    
    def create_phase_prompt(self, workflow, model_name, phase_description):
        """إنشاء prompt للمرحلة"""
        
        role_map = {
            "phi3:mini": "محلل سريع ومباشر",
            "mistral:7b": "مطور خبير ومتخصص",
            "llama3:8b": "مستشار استراتيجي وحكيم",
            "strikegpt-r1-zero-8b": "مبدع ومبتكر",
            "Qwen2.5-VL-7B": "محلل بصري متخصص"
        }
        
        role = role_map.get(model_name, "مساعد ذكي")
        
        prompt = f"""🏺 مشروع أنوبيس - Universal AI Assistants

🤖 دورك: {role}
📋 المهمة: {workflow['task_description']}
🎯 المرحلة الحالية: {phase_description}
⚡ الأولوية: {workflow['priority']}

📊 سياق المشروع:
- نظام مساعدين ذكيين متقدم
- النظام الأساسي: Port 8000
- النظام المعزول: Port 8080  
- التقنيات: Python, FastAPI, Docker, PostgreSQL, Redis

🔍 المطلوب في هذه المرحلة:
{phase_description}

⚡ توجيهات مهمة:
- كن مباشراً وعملياً
- قدم حلول قابلة للتنفيذ
- اذكر أي مخاطر أو تحديات
- اقترح خطوات واضحة

🎯 اجعل إجابتك مناسبة لدورك كـ{role}

الرجاء تقديم تحليلك وتوصياتك:"""
        
        return prompt
    
    def create_gemini_coordination_request(self, workflow):
        """إنشاء طلب تنسيق لـ Gemini CLI"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"gemini_coordination_request_{workflow['task_id']}.md"
        
        # جمع نتائج النماذج
        successful_results = [log for log in workflow["execution_log"] if log["success"]]
        failed_results = [log for log in workflow["execution_log"] if not log["success"]]
        
        content = f"""# 🏺 طلب تنسيق Gemini CLI - مشروع أنوبيس

## 📋 معلومات المهمة:
- **ID:** {workflow['task_id']}
- **النوع:** {workflow['task_type']}
- **الوصف:** {workflow['task_description']}
- **الأولوية:** {workflow['priority']}
- **تاريخ الإنشاء:** {workflow['created_at']}

## 🤖 نتائج فريق النماذج المحلية:

### ✅ النماذج التي نجحت ({len(successful_results)}):
"""
        
        for result in successful_results:
            content += f"""
#### 📋 {result['model']} - {result['phase']}
**وقت التنفيذ:** {result['execution_time']} ثانية

```
{result['response'][:800]}{'...' if len(result['response']) > 800 else ''}
```

---
"""
        
        if failed_results:
            content += f"""
### ❌ النماذج التي فشلت ({len(failed_results)}):
"""
            for result in failed_results:
                content += f"""
- **{result['model']}:** {result.get('error', 'خطأ غير محدد')}
"""
        
        content += f"""

## 🎯 المطلوب من Gemini CLI:

### 1. 📊 تحليل النتائج:
- مراجعة جميع المخرجات من النماذج المحلية
- تحديد أفضل الاقتراحات والحلول
- دمج الآراء المختلفة في رؤية موحدة

### 2. 🎯 التوجيه الاستراتيجي:
- وضع خطة عمل نهائية واضحة
- تحديد الأولويات والخطوات التالية
- اقتراح أفضل الممارسات

### 3. 🛡️ ضمان الجودة:
- مراجعة الحلول المقترحة
- تحديد المخاطر والتحديات
- اقتراح تحسينات وبدائل

### 4. 📋 خطة التنفيذ:
- خطوات واضحة ومفصلة
- جدول زمني مقترح
- متطلبات الموارد

## 🏺 هدف المشروع:
تطوير نظام أنوبيس للمساعدين الذكيين ليكون أكثر فعالية وأماناً وقابلية للتوسع.

---

**🌟 شكراً لتنسيقكم وإشرافكم على فريق الذكاء الاصطناعي!**

*تم إنشاؤه في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 تم إنشاء طلب التنسيق: {filename}")
        return filename
    
    def execute_full_workflow(self, task_type, task_description, priority="medium"):
        """تنفيذ سير العمل الكامل"""
        
        print("🏺 بدء تنفيذ سير العمل الكامل")
        print("=" * 60)
        
        # إنشاء سير العمل
        workflow = self.create_task_workflow(task_type, task_description, priority)
        print(f"📋 تم إنشاء المهمة: {workflow['task_id']}")
        print(f"🎯 النوع: {task_type}")
        print(f"📝 الوصف: {task_description}")
        
        # تنفيذ المراحل مع النماذج المحلية
        workflow_template = workflow["workflow"]
        
        # المرحلة الأساسية مع النموذج الرئيسي
        primary_model = workflow_template["primary"]
        print(f"\n🎯 المرحلة الرئيسية مع {primary_model}")
        self.execute_ollama_phase(workflow, primary_model, f"التحليل الرئيسي للمهمة")
        
        # المراحل الداعمة
        for support_model in workflow_template["support"]:
            if support_model != "gemini_cli":  # تجاهل Gemini CLI في هذه المرحلة
                print(f"\n🤝 مرحلة داعمة مع {support_model}")
                self.execute_ollama_phase(workflow, support_model, f"مراجعة ودعم من منظور {support_model}")
                time.sleep(1)  # انتظار قصير بين النماذج
        
        # إنشاء طلب التنسيق لـ Gemini
        print(f"\n🌟 إنشاء طلب التنسيق لـ Gemini CLI...")
        gemini_file = self.create_gemini_coordination_request(workflow)
        
        # حفظ سير العمل
        workflow_file = f"workflow_{workflow['task_id']}.json"
        with open(workflow_file, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, ensure_ascii=False, indent=2)
        
        # إضافة لتاريخ سير العمل
        self.workflow_history.append(workflow)
        
        # عرض الملخص
        successful_phases = len([log for log in workflow["execution_log"] if log["success"]])
        total_phases = len(workflow["execution_log"])
        
        print(f"\n📊 ملخص التنفيذ:")
        print(f"✅ مراحل نجحت: {successful_phases}/{total_phases}")
        print(f"📄 ملف سير العمل: {workflow_file}")
        print(f"🌟 طلب Gemini: {gemini_file}")
        
        print(f"\n🎯 الخطوات التالية:")
        print(f"1. راجع الملف: {gemini_file}")
        print(f"2. انسخ المحتوى إلى Gemini CLI")
        print(f"3. احصل على التوجيه النهائي")
        print(f"4. نفذ الحل المدمج")
        
        return workflow

def main():
    """الدالة الرئيسية"""
    manager = AnubisTeamWorkflowManager()
    
    print("🏺 مدير سير عمل فريق الذكاء الاصطناعي")
    print("=" * 50)
    
    # مثال على تنفيذ سير عمل
    workflow = manager.execute_full_workflow(
        task_type="development",
        task_description="تحسين نظام العزل وإضافة ميزة مراقبة الأداء في الوقت الفعلي",
        priority="high"
    )
    
    return workflow

if __name__ == "__main__":
    main()
